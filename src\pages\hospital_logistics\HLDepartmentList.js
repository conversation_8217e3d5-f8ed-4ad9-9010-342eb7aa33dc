import React,{Component} from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, TextInput, Clipboard, Linking,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class HLDepartmentList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            searchKeyWord:"",
            hospitalId:null,
            hospitalName:null
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { hospitalId, hospitalName} = route.params;
            if (hospitalId) {
                console.log("=============hospitalId" + hospitalId + "");
                this.setState({
                    hospitalId:hospitalId,
                })
                this.loadDepartmentList(hospitalId);
            }
            else{
                this.loadDepartmentList();
            }
            if (hospitalName) {
                console.log("=============hospitalName" + hospitalName + "");
                this.setState({
                    hospitalName:hospitalName,
                })
            }
        }
    }

    loadDepartmentList=(hospitalId)=>{
        let url= "/biz/hl/department/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "hospitalId":hospitalId?hospitalId:this.state.hospitalId,
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadDepartmentListCallBack);
    }
    _loadDepartmentListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteDepartment=(departmentId)=>{
        console.log("=======delete=departmentId", departmentId);
        let url= "/biz/hl/department/delete";
        let requestParams={'departmentId':departmentId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
           console.log("==========不刷新=====");
           return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/hl/department/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "hospitalId":this.state.hospitalId,
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        console.log("123")
        this.setState({
            refreshing:true
        })
        this.loadDepartmentList();
    }


    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLDepartmentAdd", 
                {
                    // 传递回调函数
                    hospitalId:this.state.hospitalId,
                    hospitalName:this.state.hospitalName,
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }
    callBackFunction=()=>{
        let url= "/biz/hl/department/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "hospitalId":this.state.hospitalId,
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.departmentId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>科室名称：{item.departmentName}</Text>
                        {
                            item.logisticsFlag=="Y" ?
                            <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                               后勤科室
                            </Text> : <View/>
                        }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>所属院区：{item.hospitalName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>科室位置：{item.departmentLocation}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>额定床位：{item.departmentRatedBed}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>额外床位：{item.departmentExtraBed}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>关联ID号：{item.departmentCode?item.departmentCode:"无"}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>后勤科室：{item.logisticsFlag=="Y"?"是":"否"}</Text>
                </View> */}
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>排序：{item.departmentSort}</Text>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    <TouchableOpacity onPress={()=>this.props.navigation.navigate("HLDepartmentStoreKeeperRelList",
                    {
                        departmentId:item.departmentId,
                        departmentName:item.departmentName,
                        // 传递回调函数
                       refresh: this.callBackFunction 
                    })}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:78,marginLeft:0,backgroundColor:'#FFB800',flexDirection:'row'}
                        ]}>
                            <Image style={{width:18, height:18,marginRight:2}} source={require('../../assets/icon/iconfont/staff.png')}></Image>
                            <Text style={[CommonStyle.itemBottomEditBtnTextStyle]}>库管员</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        this.props.navigation.navigate("HLDoctorList", {
                            "departmentId": item.departmentId,
                            "hospitalId":item.hospitalId,
                            "departmentName":item.departmentName,
                        })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{backgroundColor:'#FFB800'},{width:70,marginLeft:0,flexDirection:"row"}]}>
                            <Image  style={{width:25, height:25,marginRight:1}} source={require('../../assets/icon/iconfont/doc.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>医生</Text>
                        </View>
                    </TouchableOpacity>                   
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该科室吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteDepartment(item.departmentId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:70,marginLeft:0,flexDirection:'row'}]}>
                            <Image  style={{width:20, height:20,marginRight:3}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>this.props.navigation.navigate("HLDepartmentAdd",
                    {
                        departmentId:item.departmentId,
                        departmentName:item.departmentName,
                        //tenantId:item.tenantId,
                        // 传递回调函数
                       refresh: this.callBackFunction 
                    })}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,marginLeft:0,flexDirection:'row'}]}>
                            <Image  style={{width:20, height:20,marginRight:3}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                    
                </View>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='科室设置'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                    />
                </View>                    
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});