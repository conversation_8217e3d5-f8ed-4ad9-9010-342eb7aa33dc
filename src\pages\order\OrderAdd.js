import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, Dimensions, Image, Modal,TouchableOpacity, KeyboardAvoidingView} from 'react-native';

//import { TouchableOpacity } from 'react-native-gesture-handler';
import { WToast } from 'react-native-smart-tip'

// import TopScreen from '../../component/TopScreen';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
// 引入公共样式
// import CommonStyle from '../../assets/css/CommonStyle';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class OrderAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            // customerDataSource:[],
            // selectCustomer:[],
            operate: "",
            // contractDataSource: [],
            selectContract: [],

            brickTypeDataSource: [],
            selectBrirck: [],

            orderId: '',
            orderName: '',

            contractId: '',
            selContractId: '',
            contractName: '',
            selContractName: '',
            contractList:[],
            _contractDataSource:[],
            searchKeyWord:"",

            brickTypeId: '',
            brickTypeName: '',

            customerId: '',
            // customerName:'',
            // orderContact:'',
            // orderContactTel:'',
            brickAmount: '',
            orderWeight: '',
            contractAmont: '',
            display: 'Y',
            positionId: null,
            outsourcingId: null,
            modal:false,
        }
    }


    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        let loadUrl;
        loadTypeUrl = "/biz/order/position/list";
        loadRequest = { 'currentPage': 1, 'pageSize': 1000 };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadOrderPosition);

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { orderId } = route.params;
            if (orderId) {
                console.log("========Edit==orderId:", orderId);
                this.setState({
                    operate: "编辑",
                    orderId: orderId
                })
                loadTypeUrl = "/biz/order/get";
                loadRequest = { 'orderId': orderId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditOrderDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增",
                })
            }

        }
        // 加载砖型
        loadTypeUrl = "/biz/brick/series/type/effBrickTreeCatalog";
        loadRequest = { 'currentPage': 1, 'pageSize': 10000 };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadBrickTypeData);

        // 加载客户
        // loadTypeUrl= "/biz/tenant/customer/list";
        // loadRequest={'currentPage':1,'pageSize':100};
        // httpPost(loadTypeUrl, loadRequest, this.callBackLoadCustomerData);

        // 加载合同
        this.loadContractList();
        // loadUrl = "/biz/contract/list";
        // loadRequest = {
        //     "currentPage": 1,
        //     "pageSize": 1000,
        //     "contractState": '0AA',
        //     "qryContent": "contract",
        //     "searchKeyWord":this.state.searchKeyWord
        // };
        // httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
        
    }

    // 加载合同
    loadContractList=()=>{
        let loadUrl = "/biz/contract/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
            "contractState": '0AA',
            "qryContent": "contract"
        };
        httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
    }
    callBackLoadOrderPosition = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                OrderPositionDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditOrderDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                orderName: response.data.orderName,
                orderContact: response.data.orderContact,
                orderContactTel: response.data.orderContactTel,
                // contractId: response.data.contractId,
                selContractId: response.data.contractId,
                //contractName: response.data.contractName,
                selContractName: response.data.contractName,
                // selectContract: [response.data.contractName],
                brickTypeId: response.data.brickTypeId,
                brickTypeName: response.data.brickTypeName,
                brickAmount: response.data.brickAmount,
                orderWeight: response.data.orderWeight,
                contractAmont: response.data.contractAmont,
                customerId: response.data.customerId,
                customerName: response.data.customerName,
                display: response.data.display,
                positionId: response.data.positionId,
                outsourcingId: response.data.outsourcingId,
                selPositionId: response.data.positionId,
            })
            // this.loadContractList(response.data.customerId);
        }
    }

    callBackLoadBrickTypeData = (response) => {
        if (response.code == 200 && response.data && response.data) {
            this.setState({
                brickTypeDataSource: response.data
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // callBackLoadCustomerData=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         this.setState({
    //             customerDataSource:response.data.dataList
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    searchContract = () => {
        var _contractDataSource = copyArr(this.state.contractList);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _contractDataSource = _contractDataSource.filter(item => item.contractName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _contractDataSource: _contractDataSource,
        })
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                    {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部中间
    renderTitleItem() {
        return (
            <TouchableOpacity onPress={() => { }}>
                <View>
                    <Text style={{ fontWeight: '600' }}>新增订单</Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.navigate("OrderList") }}>
            //     <Text style={CommonStyle.headRightText}>订单管理</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>新增订单</Text>
                    {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
                </TouchableOpacity>
            </View>
        )
    }
    // 渲染砖型底部滚动数据
    openBrickTypeSelect() {
        if (!this.state.brickTypeDataSource || this.state.brickTypeDataSource.length < 1) {
            WToast.show({ data: "请先添加砖型" });
            return
        }
        this.refs.SelectBrickType.showBrickType(this.state.selectBrirck, this.state.brickTypeDataSource)
    }
    // // 渲染客户底部滚动数据
    // openCustomerSelect(){
    //     if (!this.state.customerDataSource || this.state.customerDataSource.length < 1) {
    //         WToast.show({data:"请先添加客户"});
    //         return
    //     }
    //     this.setState({
    //         contractDataSource:[],
    //     })
    //     this.refs.SelectCustomer.showCustomer(this.state.selectCustomer, this.state.customerDataSource)
    // }

    // 渲染客户合同底部滚动数据
    // openContractSelect() {
    //     // if (!this.state.customerId) {
    //     //     WToast.show({data:"请先选择客户"});
    //     //     return
    //     // }
    //     if (!this.state.contractDataSource || this.state.contractDataSource.length < 1) {
    //         WToast.show({ data: "没有生效状态的合同，请确认" });
    //         return
    //     }
    //     console.log("==========合同数据源：", this.state.contractDataSource);
    //     this.refs.SelectContract.showContract(this.state.selectContract, this.state.contractDataSource)
    // }


    callBackBrickTypeValue(value) {
        console.log("==========砖型选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectBrirck: value
        })
        // 取选定的砖型ID
        if (value.length == 2) {
            // 加载砖型
            let loadTypeUrl = "/biz/brick/series/type/getBrickByName";
            let loadRequest = {
                "brickTypeName": value[1],
                "seriesName": value[0],
            };
            httpPost(loadTypeUrl, loadRequest, this._callBackLoadBrickTypeData);
        }
        else {
            console.log("======选择砖型返回数据不合法", value)
        }
    }

    _callBackLoadBrickTypeData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                brickTypeName: response.data.brickTypeName,
                brickTypeId: response.data.brickTypeId,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
            this.setState({
                brickTypeName: '',
                brickTypeId: '',
            })
        }
    }

    // callBackCustomerValue(value){
    //     console.log("==========客户选择结果：", value)
    //     if (!value) {
    //         return;
    //     }
    //     this.setState({
    //         selectCustomer:value
    //     })
    //     var customerName = value.toString();
    //     let loadUrl= "/biz/tenant/customer/getCustomerByName";
    //     let loadRequest={
    //         "customerName":customerName
    //     };
    //     httpPost(loadUrl, loadRequest, this.callBackLoadCustomerDetailData);
    // }

    // callBackLoadCustomerDetailData=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             customerName:response.data.customerName,
    //             customerId:response.data.customerId,
    //             orderContact:response.data.customerConcat,
    //             orderContactTel:response.data.customerTel,
    //         });

    //         this.loadContractList(response.data.customerId)
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // loadContractList=()=> {
    //     let loadUrl= "/biz/contract/list";
    //     let loadRequest={
    //         "currentPage": 1,
    //         "pageSize": 1000,
    //         "contractState":'0AA',
    //     };
    //     httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
    // }

    loadContractListCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var data = response.data.dataList;
            this.setState({
                contractList: data,
            })
        }
        else {
            WToast.show({ data: response.message })
        }
    }
    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    // callBackContractValue(value) {
    //     console.log("==========合同选择结果：", value)
    //     if (!value) {
    //         return;
    //     }
    //     this.setState({
    //         selectContract: value
    //     })
    //     var contractName = value.toString();
    //     let loadUrl = "/biz/contract/getContractByName";
    //     let loadRequest = {
    //         "contractName": contractName
    //     };
    //     httpPost(loadUrl, loadRequest, (response) => {
    //         if (response.code == 200 && response.data) {
    //             this.setState({
    //                 contractName: response.data.contractName,
    //                 contractId: response.data.contractId,
    //                 customerId: response.data.partyA,
    //             })
    //         }
    //         else if (response.code == 401) {
    //             WToast.show({ data: response.message });
    //             this.props.navigation.navigate("LoginView");
    //         }
    //         else {
    //             WToast.show({ data: response.message });
    //         }
    //     });
    // }

    saveOrder = () => {
        console.log("=======saveOrder");
        let toastOpts;
        if (!this.state.orderName) {
            toastOpts = getFailToastOpts("请输入订单名称");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.customerId) {
        //     toastOpts = getFailToastOpts("请选择客户");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.orderContact) {
        //     toastOpts = getFailToastOpts("请输入联系人");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.orderContactTel) {
        //     toastOpts = getFailToastOpts("请输入联系电话");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.selContractId) {
            toastOpts = getFailToastOpts("请选择合同");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.brickTypeId) {
            toastOpts = getFailToastOpts("请选择砖型");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/order/add";
        if (this.state.orderId) {
            console.log("=========Edit===orderId", this.state.orderId)
            url = "/biz/order/modify";
        }
        let requestParams = {
            'orderId': this.state.orderId,
            'orderName': this.state.orderName,
            'customerId': this.state.customerId,
            'orderContact': this.state.orderContact,
            'orderContactTel': this.state.orderContactTel,
            'brickTypeId': this.state.brickTypeId,
            'brickAmount': this.state.brickAmount,
            'orderWeight': this.state.orderWeight,
            'contractAmont': this.state.contractAmont,
            'contractId': this.state.selContractId,
            'display': this.state.display,
            'positionId': this.state.selPositionId,
            'outsourcingId': this.state.outsourcingId,
        };
        httpPost(url, requestParams, this.saveOrder_call_back);
    }

    // 保存回调函数
    saveOrder_call_back = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack();
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    renderOrderPositionRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selPositionId: item.positionId
                })
            }}>
                <View key={item.positionId} style={[item.positionId === this.state.selPositionId ? 
                    // CommonStyle.selectedBlockItemViewStyle 
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    : 
                    // CommonStyle.blockItemViewStyle
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginLeft:16,
                        // marginRight: 8,
                        marginTop: 8,
                        marginBottom: 8,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                    ]}>
                    <Text style={[item.positionId === this.state.selPositionId ? 
                        // CommonStyle.selectedBlockItemTextStyle16 
                        {
                            color: '#1E6EFA'
                        }
                        : 
                        // CommonStyle.blockItemTextStyle16
                        {
                            color: '#404956'
                        }
                        ,
                        {
                            fontSize: 16, textAlign : 'center'
                        }
                        ]}>
                        {item.positionName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    renderContractRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selContractId: item.contractId,
                    selContractName:item.contractName,
                    customerId:item.customerId
                })
            }}>
                <View key={item.contractId} style={[item.contractId === this.state.selContractId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.contractId === this.state.selContractId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.contractName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '订单'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.lineHeadBorderStyle} />

                <ScrollView style={[CommonStyle.formContentViewStyle]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>
                                订单名称
                            </Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ orderName: text })}
                        >
                            {this.state.orderName}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>所属合同</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[{flexWrap:'wrap'}]}>
                            <TouchableOpacity onPress={() => {
                                if (this.state.contractList && this.state.contractList.length > 0) {
                                    this.setState({
                                        _contractDataSource: copyArr(this.state.contractList),
                                    })
                                }
                                this.setState({
                                    modal: true,
                                })

                                if (!this.state.selContractId && this.state.contractList && this.state.contractList.length > 0) {
                                    this.setState({
                                        selContractId: this.state.contractList[0].contractId,
                                        selContractName: this.state.contractList[0].contractName,
                                    })
                                }
                            }}>
                                <View style={[
                                
                                {       
                                    backgroundColor: '#FFFFFF', 
                                    borderColor: '#1E6EFA', 
                                    borderWidth: 1        ,          
                                    marginRight: 8,
                                    marginTop: 8,
                                    marginBottom: 4,
                                    borderRadius: 4,
                                    justifyContent: 'center',
                                    alignContent: 'center',
                                    height: 36,
                                    //width: (screenWidth - 54)/3,
                                    borderRadius: 4,
                                    
                                }
                                ]}>
                                {
                                    this.state.selContractId && this.state.selContractName ?
                                    <Text style={{color: '#1E6EFA', fontSize: 13, textAlign : 'center',paddingLeft:5,paddingRight:5}}>{this.state.selContractName}</Text>
                                    :
                                    <Text style={{fontSize: 13, textAlign : 'center', color: '#1E6EFA',paddingLeft:5,paddingRight:5}}>选择合同</Text>
                                }
                                </View>
                            </TouchableOpacity>
                        </View>
                        <Modal
                            animationType={'slide'}
                            transparent={true}
                            onRequestClose={() => console.log('onRequestClose...')}
                            visible={this.state.modal}>
                            <View style={CommonStyle.fullScreenKeepOut}>
                                <View style={CommonStyle.modalContentViewStyle}>
                                    <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.searchContract();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                    </View>
                                    <ScrollView style={{}}>
                                        <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                            {
                                                (this.state._contractDataSource && this.state._contractDataSource.length > 0)
                                                    ?
                                                    this.state._contractDataSource.map((item, index) => {
                                                        if (index < 1000) {
                                                            return this.renderContractRow(item)
                                                        }
                                                    })
                                                    : <EmptyRowViewComponent />
                                            }
                                        </View>
                                    </ScrollView>
                                    <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                        <TouchableOpacity onPress={() => {
                                            this.setState({
                                                modal: false,
                                            })
                                        }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                        </TouchableOpacity>
                                        <TouchableOpacity onPress={() => {
                                            if (!this.state.selContractId) {
                                                let toastOpts = getFailToastOpts("您还没有选择合同");
                                                WToast.show(toastOpts);
                                                return;
                                            }
                                            this.setState({
                                                modal: false,
                                            })
                                        }}>
                                            <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                                <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                                <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>
                        </Modal>
                    </View>

                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>砖型信息</Text>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>砖型</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={() => this.openBrickTypeSelect()}>
                            <View style={[styles.inputTextStyleTextStyle,{borderWidth:0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.brickTypeName ? "请选择" : this.state.brickTypeName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>数量</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ brickAmount: text })}
                        >
                            {this.state.brickAmount}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>重量</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ orderWeight: text })}
                        >
                            {this.state.orderWeight}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>订单金额</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ contractAmont: text })}
                        >
                            {this.state.contractAmont}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View>
                        {
                            (this.state.OrderPositionDataSource && this.state.OrderPositionDataSource.length > 0)
                                ?
                                <View>
                                    <View style={styles.inputRowStyle}>
                                        <View style={styles.leftLabView}>
                                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                            <Text style={styles.leftLabNameTextStyle}>部位</Text>
                                        </View>
                                    </View>
                                    <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row' }}>
                                        {
                                            (this.state.OrderPositionDataSource && this.state.OrderPositionDataSource.length > 0)
                                                ?
                                                this.state.OrderPositionDataSource.map((item, index) => {
                                                    return this.renderOrderPositionRow(item)
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </View>
                                : <View />
                        }
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    {/* <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>部位</Text>
                    </View>
                </View>
                <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                    {
                        (this.state.OrderPositionDataSource && this.state.OrderPositionDataSource.length > 0) 
                        ? 
                        this.state.OrderPositionDataSource.map((item, index)=>{
                            return this.renderOrderPositionRow(item)
                        })
                        : <EmptyRowViewComponent/> 
                    }
                </View> */}
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>是否显示</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[styles.selectViewItem, (this.state.display === 'Y') ? { backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1, borderRadius: 4} : {backgroundColor: '#F2F5FC',borderRadius: 4}]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    display: "Y",
                                })
                            }}>
                                <View style={{                        
                                                // marginRight: 8,
                                                marginTop: 4,
                                                marginBottom: 4,
                                                borderRadius: 4,
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                                // textAlignVertical:'center',
                                                height: 36,
                                                width: (screenWidth - 54)/3,
                                                borderRadius: 4
                                            }}>
                                    <Text style={[(this.state.display === 'Y') ? { color: '#1E6EFA', fontSize: 16, textAlign : 'center' } : {  color: '#404956', fontSize: 16, textAlign : 'center' }]}>显示</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={{width:15}}></View>
                        <View style={[styles.selectViewItem, (this.state.display === 'N') ? { backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1, borderRadius: 4} : {backgroundColor: '#F2F5FC' ,borderRadius: 4}]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    display: "N",
                                })
                            }}>
                                <View style={{
                                                // marginRight: 8,
                                                marginTop: 4,
                                                marginBottom: 4,
                                                borderRadius: 4,
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                                // textAlignVertical:'center',
                                                height: 36,
                                                width: (screenWidth - 54)/3,
                                                borderRadius: 4
                                }}>
                                    <Text style={[(this.state.display === 'N') ? { color: '#1E6EFA', fontSize: 16, textAlign : 'center'  } : {   color: '#404956', fontSize: 16, textAlign : 'center'  }]}>不显示</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    {/* <View style={{height:ifIphoneXContentViewHeight()-418-86-178, backgroundColor:'#F2F5FC'}}> */}
                    <View style={{height:ifIphoneXContentViewHeight()-418-86-176, backgroundColor:'#FFFFFF'}}>
                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{ marginTop:0}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveOrder.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect
                        ref={'SelectBrickType'}
                        callBackBrickTypeValue={this.callBackBrickTypeValue.bind(this)}
                    />
                    {/* <BottomScrollSelect
                        ref={'SelectContract'}
                        callBackContractValue={this.callBackContractValue.bind(this)}
                    /> */}
                </ScrollView>
            </KeyboardAvoidingView>
           
        );

    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     backgroundColor:'#FFFFFF',
    //     height:screenHeight - 140,
    // },
    selectViewItem: {
        width: 100, justifyContent: 'center', alignItems: 'center'
    },
    selectTextItem: {
        fontSize: 18,
        fontWeight: 'bold',
        // marginRight:26,
        // justifyContent: 'center',
        // alignContent: 'center',
        // textAlignVertical:'center'
        // marginRight: 8,
        marginTop: 4,
        marginBottom: 4,
        borderRadius: 4,
        justifyContent: 'center',
        alignContent: 'center',
        // textAlignVertical:'center',
        height: 36,
        width: (screenWidth - 54)/3,
        borderRadius: 4
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },

    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:5,
        marginRight:5,
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        // borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputTextStyleTextStyle: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    }
})
module.exports = OrderAdd;