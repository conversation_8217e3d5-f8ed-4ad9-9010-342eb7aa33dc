import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  Linking,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {WToast} from 'react-native-smart-tip';
import ClassHeadScreen from '../../../component/ClassHeadScreen';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import EmptyListComponent from '../../../component/EmptyListComponent';
import ProgressBar from '../../../component/ProgressBar';
import {ifIphoneXContentViewDynamicHeight} from '../../../utils/ScreenUtil';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;

const commonStyles = {
  color: 'rgba(255, 255, 255, 1)',
  fontSize: 12,
  width: 55,
  height: 19,
  paddingLeft: 9,
  paddingTop: 2,
};

const taskStateBgColor = {
  '0AA': '#0000ff',
  '0BB': '#FF5D5D',
  '0CC': '#26C761',
  default: 'rgba(0,10,32,0.45)',
};

const taskStateText = {
  '0AA': '实习中',
  '0BB': '已超期',
  '0CC': '已完成',
  default: '未开始',
};

const taskTitleBgColor = {
  0: '#FB7B04',
  1: '#1084FD',
  2: '#1E85A3',
  3: '#FBB100',
  4: '#BF181E',
  5: '#1B9342',
};

export default class MyCourseList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      seltaskState: null,

      searchKeyWord: '',

      showCourseTypeSearchItemBlock: false,
      courseTypeDataSource: [],
      selcourseTypeId: null,
      selcourseTypeName: null,

      showCourseLevelSearchItemBlock: false,
      courseLevelDataSource: null,
      selcourseLevelId: null,
      selcourseLevelName: null,

      selStaffId: null,

      qryNowTime: null,
      //待展示开始学习按钮的courseID
      courseIdData: [],
      flag: true,
      documentTypeList: ['TD', 'TV'],

      userLevelIdList: [],

      warnModal: false,
      modalItem: {},
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  initqryNowTime = () => {
    // 当前时间
    var currentDate = new Date();
    // currentDate.setMonth(currentDate.getMonth() - 1);
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _qryNowTime =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      qryNowTime: _qryNowTime,
    });
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    // 实习类型
    this.initqryNowTime();
    let loadCourseTypeUrl = '/biz/course/type/list';
    let loadCourseTypeRequest = {qryAll: 'Y', currentPage: 1, pageSize: 1000};
    httpPost(loadCourseTypeUrl, loadCourseTypeRequest, (response) => {
      if (response.code == 200 && response.data.dataList) {
        var courseTypeData = response.data.dataList;
        courseTypeData.unshift({courseTypeId: 0, courseTypeName: '全部'});
        this.setState({
          courseTypeDataSource: courseTypeData,
        });
        // courseTypeData
        // console.log("==========实习类型数据源：", this.state.courseTypeDataSource);
      }
    });

    //当前登录人的职级列表
    let url = '/biz/course/level/user/list';
    let loadRequest = {
      //"currentPage": this.state.currentPage,
      // "pageSize": this.state.pageSize,
      userId: constants.loginUser.userId,
    };
    var userLevelIdList = [];
    httpPost(url, loadRequest, (response) => {
      if (response.code == 200 && response.data && response.data.dataList) {
        var courseLevel;
        //console.log("当前为",response.data.dataList)
        for (var index = 0; index < response.data.dataList.length; index++) {
          courseLevel = response.data.dataList[index];
          if (courseLevel) {
            userLevelIdList = userLevelIdList.concat(courseLevel.courseLevelId);
          }
        }
        this.setState({
          userLevelIdList: userLevelIdList,
        });
        console.log('=========userLevelIdList:', userLevelIdList);
      } else if (response.code == 401) {
        WToast.show({data: response.message});
        this.props.navigation.navigate('LoginView');
      }
    });

    //所属职级
    let loadCourseLevelUrl = '/biz/course/level/list';
    let loadCourseLevelRequest = {qryAll: 'Y', currentPage: 1, pageSize: 1000};
    httpPost(loadCourseLevelUrl, loadCourseLevelRequest, (response) => {
      if (response.code == 200 && response.data.dataList) {
        var courseLevelData = response.data.dataList;
        courseLevelData.unshift({courseLevelId: 0, courseLevelName: '全部'});
        this.setState({
          courseLevelDataSource: response.data.dataList,
        });
        // console.log("==========所属职级数据源：", this.state.courseLevelDataSource);
      }
    });
    this.loadMyCourseList();
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/course/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      courseLevelId: this.state.selcourseLevelId,
      courseTypeId: this.state.selcourseTypeId,
      userId: constants.loginUser.userId,
      documentTypeList: this.state.documentTypeList,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/course/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      courseLevelId: this.state.selcourseLevelId,
      courseTypeId: this.state.selcourseTypeId,
      documentTypeList: this.state.documentTypeList,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };

  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    if (this.state.refreshing) {
      WToast.show({data: 'loading...'});
      return;
    }
    this.setState({refreshing: true}, () => {
      console.log('refreshing 已更新:', this.state.refreshing);
      // 在这里执行后续操作
      this.loadMyCourseList();
    });
  };

  loadMyCourseList = () => {
    let url = '/biz/course/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      courseLevelId: this.state.selcourseLevelId,
      courseTypeId: this.state.selcourseTypeId,
      documentTypeList: this.state.documentTypeList,
    };
    httpPost(url, loadRequest, this.loadMyCourseCallBack);
  };

  loadMyCourseCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      var dataAll = [...dataOld, ...dataNew];
      //console.log('我的实习列表', JSON.stringify(dataAll, null, 6));
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 22, height: 22}}
          source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          Alert.alert('确认', '您确定要导出PDF文件吗？', [
            {
              text: '取消',
              onPress: () => {
                WToast.show({data: '点击了取消'});
              },
            },
            {
              text: '确定',
              onPress: () => {
                WToast.show({data: '点击了确定'});
                this.exportPdfFile();
              },
            },
          ]);
        }}
        style={[{marginRight: 10}]}>
        <Image
          style={{width: 23, height: 23}}
          source={require('../../../assets/icon/iconfont/newExport.png')}></Image>
      </TouchableOpacity>
    );
  }

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    console.log(
      'constants.loginUser',
      JSON.stringify(constants.loginUser, null, 6),
    );
    let url = '/biz/generate/pdf/course';
    let requestParams = {
      currentPage: 1,
      pageSize: 1000,
      checkOutUserId: constants.loginUser.userId,
      courseTypeId:
        this.state.selcourseTypeId == 0 ? null : this.state.selcourseTypeId,
      courseLevelId:
        this.state.selcourseLevelId == 0 ? null : this.state.selcourseLevelId,
      userId: constants.loginUser.userId,
      tenantId: constants.loginUser.tenantId,
      taskState:
        this.state.selCourseStateCode === 'all'
          ? null
          : this.state.selCourseStateCode,
      scene: 'D',
      documentTypeList: this.state.documentTypeList,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  addCourseTask = (taskItem, index) => {
    console.log('=======addCourseTask=taskItem', taskItem);
    // console.log("@_addCourseTask_@", JSON.stringify(this.state.dataSource, null, 6));
    var _dataSource = copyArr(this.state.dataSource);
    if (_dataSource && Object.keys(_dataSource).length > 0) {
      _dataSource = _dataSource.filter(
        (item) => item.courseId == taskItem.courseId,
      );
    }
    console.log('数据' + _dataSource[0].courseDuration);

    var dateTime = new Date();
    console.log('dateTime1=====' + dateTime);
    dateTime = dateTime.setHours(dateTime.getHours() + 8);
    dateTime = new Date(dateTime);
    dateTime = dateTime.setDate(
      dateTime.getDate() + _dataSource[0].courseDuration,
    );
    dateTime = new Date(dateTime);
    console.log('dateTime3=====' + dateTime);
    let Y = dateTime.getFullYear() + '-';
    let M =
      (dateTime.getMonth() + 1 < 10
        ? '0' + (dateTime.getMonth() + 1)
        : dateTime.getMonth() + 1) + '-';
    let D =
      dateTime.getDate() < 10 ? '0' + dateTime.getDate() : dateTime.getDate();
    let date = Y + M + D;
    // console.log("dateTime4=====", date);
    let requestUrl = '/biz/course/task/add';
    let requestParams = {
      courseId: taskItem.courseId,
      planCompletionTime: date,
      checkOutUserId: constants.loginUser.userId,
    };
    httpPost(requestUrl, requestParams, this.addCourseTaskCallBack);
  };
  // 开始学习按操作的回调
  addCourseTaskCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '该实习任务已开始'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      if (response.message == '重复检验失败') {
        response.message = '任务已开始，无需重复点击';
        WToast.show({data: response.message});
      }
    }
  };

  renderRow = (item, index) => {
    const state = item.taskState || 'default';
    const indexOfBgColor = (parseInt(index) + 1) % 6;
    const studyDays = item.syCourseTaskDTO
      ? item.syCourseTaskDTO.taskState == '0CC'
        ? item.courseDuration
        : Math.abs(
            (
              (Date.now() -
                Date.parse(item.syCourseTaskDTO.gmtCreated.replace(' ', 'T'))) /
              (1000 * 3600 * 24)
            ).toFixed(0),
          )
      : 0;
    let marginStyle = {};

    if (index === 0) {
      // 第一个 item
      marginStyle = {marginTop: 15, marginBottom: 7.5};
    } else if (index === this.state.totalRecord - 1) {
      // 最后一个 item
      marginStyle = {marginTop: 7.5, marginBottom: 15};
    } else {
      // 中间的 item
      marginStyle = {marginTop: 7.5, marginBottom: 7.5};
    }

    return (
      <TouchableOpacity
        onPress={() => {
          if (this.state.userLevelIdList?.includes(item.courseLevelId)) {
            this.props.navigation.navigate('MyCourseDetail', {
              courseTaskId: item.courseTaskId,
              courseId: item.courseId,
              taskNum: item.taskNum,
              courseIndex: index,
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          } else {
            this.setState({
              warnModal: true,
              modalItem: item,
            });
            return;
          }
        }}>
        <View
          key={item.courseId}
          style={[
            {
              flexDirection: 'row',
              height: 129,
              backgroundColor: '#ffffffff',
              marginLeft: 15,
              marginRight: 15,
              borderRadius: 11,
            },
            marginStyle,
          ]}>
          <View
            style={{
              width: 130 + 28,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <View>
              {item.coursePhoto ? (
                <View>
                  <Image
                    source={{
                      uri: constants.image_addr + '/' + item.coursePhoto,
                    }}
                    style={{width: 130, height: 93, borderRadius: 10}}></Image>
                </View>
              ) : (
                <View>
                  <View
                    style={{
                      width: 130,
                      height: 93,
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 8,
                      borderRadius: 10,
                      backgroundColor: taskTitleBgColor[indexOfBgColor],
                    }}>
                    <Text
                      style={{
                        fontSize: 18,
                        color: 'white',
                        fontWeight: 'bold',
                      }}>
                      {item.courseName}
                    </Text>
                  </View>
                </View>
              )}
              <View
                style={{
                  position: 'absolute',
                  zIndex: 10,
                  borderTopLeftRadius: 10,
                  borderBottomRightRadius: 10,
                  backgroundColor: taskStateBgColor[state],
                }}>
                <Text style={commonStyles}>{taskStateText[state]}</Text>
              </View>
            </View>
          </View>

          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 10,
            }}>
            <View style={{height: 93, width: '100%'}}>
              {/* 自定义组件 */}
              <ClassHeadScreen
                redTitle={item.courseLevelName}
                blackTitle={' 任务' + item.courseSort + '：' + item.courseName}
              />

              <View style={{height: 20, flexDirection: 'row'}}>
                <View
                  style={{
                    //外边距
                    borderRadius: 10,
                    backgroundColor: 'rgba(27,188,130,0.2)',
                    height: 20,
                    paddingLeft: 10,
                    paddingRight: 10,
                    marginTop: 8,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Text style={{fontSize: 12, color: '#1BBC82'}}>
                    {item.courseTypeName}
                  </Text>
                </View>
              </View>

              {/* 实习进度栏 */}
              <View
                style={{
                  height: 12,
                  marginTop: 15,
                }}>
                <ProgressBar
                  fillColor={'rgba(30, 110, 250, 1)'}
                  height={3}
                  progress={
                    studyDays / item.courseDuration >= 1
                      ? 1
                      : studyDays / item.courseDuration
                  }
                />
              </View>
              <View style={{height: 17, flexDirection: 'row'}}>
                <Text style={[styles.courseDaysTextStyle]}>
                  {'已实习(天）：'}
                </Text>
                {item.syCourseTaskDTO ? (
                  item.syCourseTaskDTO.taskState == '0AA' ? (
                    <Text style={[styles.courseDaysTextStyle]}>
                      {studyDays}
                    </Text>
                  ) : item.syCourseTaskDTO.taskState == '0BB' ? (
                    <Text style={{color: 'red'}}>{'超期'}</Text>
                  ) : (
                    <Text style={[styles.courseDaysTextStyle]}>
                      {item.courseDuration}
                    </Text>
                  )
                ) : (
                  <Text style={[styles.courseDaysTextStyle]}>0</Text>
                )}
                <Text style={[styles.courseDaysTextStyle]}>
                  {'/' + item.courseDuration}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 实习类型
  renderCourseTypeRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selcourseTypeId: item.courseTypeId,
            selcourseTypeName: item.courseTypeName,
          });

          item.courseLevelId;
        }}>
        <View
          key={'department_' + item.courseTypeId}
          style={[
            item.courseTypeId === this.state.selcourseTypeId
              ? CommonStyle.choseToSearchItemsSelectedViewColor
              : CommonStyle.choseToSearchItemsViewColor,
            CommonStyle.choseToSearchItemsViewSize,
          ]}>
          <Text
            style={[
              item.courseTypeId === this.state.selcourseTypeId
                ? CommonStyle.choseToSearchItemsSelectedTextStyle
                : CommonStyle.choseToSearchItemsTextStyle,
            ]}>
            {item.courseTypeName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  //所属职级
  renderCourseLevelRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selcourseLevelId: item.courseLevelId,
            selcourseLevelName: item.courseLevelName,
          });
        }}>
        <View
          key={'department_' + item.courseLevelId}
          style={[
            item.courseLevelId === this.state.selcourseLevelId
              ? CommonStyle.choseToSearchItemsSelectedViewColor
              : CommonStyle.choseToSearchItemsViewColor,
            CommonStyle.choseToSearchItemsViewSize,
          ]}>
          <Text
            style={[
              item.courseLevelId === this.state.selcourseLevelId
                ? CommonStyle.choseToSearchItemsSelectedTextStyle
                : CommonStyle.choseToSearchItemsTextStyle,
            ]}>
            {item.courseLevelName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  searchByKeyWord = () => {
    let loadUrl = '/biz/course/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      courseLevelId: this.state.selcourseLevelId,
      courseTypeId: this.state.selcourseTypeId,
      documentTypeList: this.state.documentTypeList,
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  // 显示搜索项目
  showCourseTypeSearchItemSelect() {
    if (this.state.flag == false) {
      return;
    }
    if (
      !this.state.courseTypeDataSource ||
      this.state.courseTypeDataSource.length < 1
    ) {
      WToast.show({data: '请先添加实习类型'});
      return;
    }
    this.setState({
      showCourseTypeSearchItemBlock: true,
      flag: false,
    });
  }
  showCourseLevelSearchItemSelect() {
    if (this.state.flag == false) {
      return;
    }
    if (
      !this.state.courseLevelDataSource ||
      this.state.courseLevelDataSource.length < 1
    ) {
      WToast.show({data: '请先添加实习所属职级'});
      return;
    }
    this.setState({
      showCourseLevelSearchItemBlock: true,
      flag: false,
    });
  }

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="我的实习"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.headViewStyle,
            {borderLeftWidth: 0, borderRightWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View style={[styles.topSelectViewStyle]}>
            <View style={{flexWrap: 'wrap', flexDirection: 'row'}}>
              <TouchableOpacity
                onPress={() => this.showCourseTypeSearchItemSelect()}>
                {this.state.showCourseTypeSearchItemBlock ? (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                      {this.state.selcourseTypeId &&
                      this.state.selcourseTypeName
                        ? this.state.selcourseTypeName
                        : '实习类型'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchClosedIconSize]}
                      source={require('../../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                  </View>
                ) : (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                      {this.state.selcourseTypeId &&
                      this.state.selcourseTypeName
                        ? this.state.selcourseTypeName
                        : '实习类型'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchOpenedIconSize]}
                      source={require('../../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                  </View>
                )}
              </TouchableOpacity>
            </View>

            <View style={{flexWrap: 'wrap', flexDirection: 'row'}}>
              <TouchableOpacity
                onPress={() => this.showCourseLevelSearchItemSelect()}>
                {this.state.showCourseLevelSearchItemBlock ? (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                      {this.state.selcourseLevelId &&
                      this.state.selcourseLevelName
                        ? this.state.selcourseLevelName
                        : '所属职级'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchClosedIconSize]}
                      source={require('../../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                  </View>
                ) : (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                      {this.state.selcourseLevelId &&
                      this.state.selcourseLevelName
                        ? this.state.selcourseLevelName
                        : '所属职级'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchOpenedIconSize]}
                      source={require('../../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                  </View>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View>
          {this.state.showCourseTypeSearchItemBlock ? (
            <View
              style={[
                CommonStyle.choseToSearchBigBoxViewStyle,
                {
                  height: ifIphoneXContentViewDynamicHeight(
                    this.state.topBlockLayoutHeight,
                  ),
                },
              ]}>
              <View style={CommonStyle.heightLimited}>
                <ScrollView>
                  <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                    <View
                      style={[
                        {backgroundColor: 'rgba(255,255,255,1)'},
                        CommonStyle.choseToSearchItemsViewSize,
                      ]}>
                      <Text
                        style={[
                          CommonStyle.blockItemTextStyle16,
                          {fontWeight: 'bold'},
                        ]}>
                        实习类型：
                      </Text>
                    </View>
                    {this.state.courseTypeDataSource &&
                    this.state.courseTypeDataSource.length > 0
                      ? this.state.courseTypeDataSource.map((item, index) => {
                          return this.renderCourseTypeRow(item);
                        })
                      : null}
                  </View>
                </ScrollView>
              </View>
              <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showCourseTypeSearchItemBlock: false,
                      flag: true,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]}>
                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    let loadUrl = '/biz/course/list';
                    let loadRequest = {
                      currentPage: 1,
                      pageSize: this.state.pageSize,
                      userId: constants.loginUser.userId,
                      courseLevelId: this.state.selcourseLevelId,
                      courseTypeId: this.state.selcourseTypeId,
                      documentTypeList: this.state.documentTypeList,
                    };
                    console.log(
                      '选择的实习类型=====' + this.state.selcourseTypeId,
                    );
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    this.setState({
                      showCourseTypeSearchItemBlock: false,
                      flag: true,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>
                      确定搜索
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}
          {this.state.showCourseLevelSearchItemBlock ? (
            <View
              style={[
                CommonStyle.choseToSearchBigBoxViewStyle,
                {
                  height: ifIphoneXContentViewDynamicHeight(
                    this.state.topBlockLayoutHeight,
                  ),
                },
              ]}>
              <View style={CommonStyle.heightLimited}>
                <ScrollView>
                  <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                    <View
                      style={[
                        {backgroundColor: 'rgba(255,255,255,1)'},
                        CommonStyle.choseToSearchItemsViewSize,
                      ]}>
                      <Text
                        style={[
                          CommonStyle.blockItemTextStyle16,
                          {fontWeight: 'bold'},
                        ]}>
                        所属职级：
                      </Text>
                    </View>
                    {this.state.courseLevelDataSource &&
                    this.state.courseLevelDataSource.length > 0
                      ? this.state.courseLevelDataSource.map((item, index) => {
                          return this.renderCourseLevelRow(item);
                        })
                      : null}
                  </View>
                </ScrollView>
              </View>
              <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showCourseLevelSearchItemBlock: false,
                      flag: true,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]}>
                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    let loadUrl = '/biz/course/list';
                    let loadRequest = {
                      currentPage: 1,
                      pageSize: this.state.pageSize,
                      courseTypeId: this.state.selcourseTypeId,
                      userId: constants.loginUser.userId,
                      courseLevelId: this.state.selcourseLevelId,
                      documentTypeList: this.state.documentTypeList,
                    };
                    console.log(
                      '选择的职级=====' + this.state.selcourseLevelId,
                    );
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    this.setState({
                      showCourseLevelSearchItemBlock: false,
                      flag: true,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>
                      确定搜索
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}

          <View
            style={[
              CommonStyle.contentNewViewStyle,
              {
                height: ifIphoneXContentViewDynamicHeight(
                  this.state.topBlockLayoutHeight,
                ),
              },
            ]}>
            <FlatList
              data={this.state.dataSource}
              renderItem={({item, index}) => this.renderRow(item, index)}
              ListEmptyComponent={this.emptyComponent}
              // 自定义下拉刷新
              refreshControl={
                <RefreshControl
                  tintColor="#FF0000"
                  title="loading"
                  colors={['#FF0000', '#00FF00', '#0000FF']}
                  progressBackgroundColor="#FFFF00"
                  refreshing={this.state.refreshing}
                  onRefresh={() => {
                    this._loadFreshData();
                  }}
                />
              }
              // 底部加载
              ListFooterComponent={() => this.flatListFooterComponent()}
              onEndReached={() => this._loadNextData()}
              // onEndReachedThreshold={0.2}
            />
          </View>

          {/* 警告弹窗 */}
          <Modal
            animationType="fade"
            transparent={true}
            visible={this.state.warnModal}
            //  onShow={this.onShow.bind(this)}
            onRequestClose={() => console.log('onRequestClose...')}>
            <View
              style={[
                CommonStyle.fullScreenKeepOut,
                {backgroundColor: 'rgba(0,0,0,0.64)'},
              ]}>
              <View
                style={{
                  width: 272,
                  height: 156,
                  bottom: screenHeight / 2 - 80,
                  position: 'absolute',
                  backgroundColor: '#FFFFFF',
                  borderRadius: 10,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                {/* <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认删除该日报?</Text>
              </View> */}
                <View
                  style={{
                    //marginTop: 10,
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: 100,
                    width: 250,
                  }}>
                  <Text style={{fontSize: 16, color: 'rgba(0,10,32,0.65)'}}>
                    {this.state.modalItem.noRightTip
                      ? this.state.modalItem.noRightTip
                      : '您没有权限开始此实习!'}
                  </Text>
                </View>

                <View
                  style={{
                    width: 272,
                    height: 40,
                    alignItems: 'flex-end',
                    justifyContent: 'flex-end',
                    marginTop: 10,
                    borderTopWidth: 1,
                    borderColor: '#DFE3E8',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      this.setState({
                        warnModal: false,
                      });
                      WToast.show({data: '点击了确认'});
                    }}>
                    <View
                      style={{
                        width: 105,
                        height: 40,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          fontSize: 17,
                          fontWeight: '400',
                          color: '#1E6EFA',
                        }}>
                        确认
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  titleViewStyle: {
    flexDirection: 'row',
    marginLeft: 12,
    marginRight: 16,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    fontSize: 14,
    lineHeight: 24,
    textAlign: 'left',
    textAlignVertical: 'top',
    color: 'rgba(0, 10, 32, 0.65)',
  },
  itemContentTextStyle: {
    marginLeft: 12,
    marginRight: 16,
    marginTop: 3,
    lineHeight: 24,
  },
  lineViewStyle: {
    // height:1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 5,
    // marginBottom: 6,
    borderBottomWidth: 1,
    borderColor: '#E8E9EC',
  },
  courseDaysTextStyle: {
    color: '#838383',
    fontSize: 13,
  },
  topSelectViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
});
