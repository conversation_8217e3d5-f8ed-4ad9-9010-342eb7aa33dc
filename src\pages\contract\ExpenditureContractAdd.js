import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Modal,
    FlatList,RefreshControl,Image,ScrollView,TextInput,KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import { uploadMultiImageLibrary } from '../../utils/UploadImageUtils';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../utils/CameraRollUtils';

import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class ExpenditureContractAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            contractId: "",
            contractName: "",
            contractCode: "",
            partyA: "",
            partyB: constants.loginUser.tenantName,
            contractAmount: "",
            contractWeight: "",
            partyAContact: "",
            partyATel: "",
            partyBContact: "",
            partyBTel: "",
            signingTime: "",
            remark: "",
            operate: "",
            selectSigningTime: [],
            supplierDataSource: [],
            selectSupplier: [],
            selectDeliveryDate: [],
            deliveryDate: "",
            actualWeight: "",
            actualAmount: "",
            enterpriseDataSource:[],
            selectEnterprise:[],
            enterpriseName:"",
            enterpriseId:"",
            supplierName:"",
            parentContractName:"",
            selParentContractName:"",
            parentContractId:"",
            selParentContractId:"",
            searchKeyWord:"",
            selectParentContract:[],
            parentContractDataSource:[],
            _parentContractDataSource:[],
            compressFileList:[],
            pictureIndex:0,
            isShowImage: false,     //  显示弹窗组件  
            urls:[],
            modal:false
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 加载公司
        this.loadEnterpriseList();

        // 加载客户
        loadTypeUrl = "/biz/supplier/list";
        loadRequest = { 'currentPage': 1, 'pageSize': 1000 };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadSupplierData);

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { contractId } = route.params;
            if (contractId) {
                console.log("========Edit==contractId:", contractId);
                this.setState({
                    contractId: contractId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/contract/get";
                loadRequest = { 'contractId': contractId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditContractDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectSigningTime: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    signingTime: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                })
                var dateString = this.state.signingTime + ' 00:00:01';
                dateString = dateString.substring(0, 19);
                dateString = dateString.replace(/-/g, '/');
                var dateStringTimestamp = new Date(dateString).getTime();
                // 根据毫秒数构建 Date 对象
                var SevenDaysLast = new Date(dateStringTimestamp);
                //获取当前时间的毫秒数
                var nowMilliSeconds = currentDate.getTime();
                // 用获取毫秒数 加上30天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
                SevenDaysLast.setTime(nowMilliSeconds + (30 * 86400000));
                //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
                //格式化月，如果小于9，前面补0  
                var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
                //格式化日，如果小于9，前面补0  
                var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
                this.setState({
                    selectDeliveryDate: [SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
                    deliveryDate: SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
                })
            }
        }
        // 加载合同
        // this.loadContractList();
        loadTypeUrl = "/biz/contract/list";
        loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
            "contractState": '0AA',
            "qryContent": "contract"
        };
        httpPost(loadTypeUrl, loadRequest, this.loadContractListCallBack);
    }

    loadContractListCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                parentContractDataSource: response.data.dataList,
            })
        }
        else {
            WToast.show({ data: response.message })
        }
    }

    loadEnterpriseList=()=>{
        let url= "/biz/enterprise/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            
        };
        httpPost(url, loadRequest, this.loadEnterpriseListCallBack);
    }

    loadEnterpriseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            this.setState({
                enterpriseDataSource:data
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    callBackLoadSupplierData = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                supplierDataSource: response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditContractDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectSigningTime = response.data.signingTime.split("-");
            var selectDeliveryDate = [];
            if (response.data.deliveryDate) {
                selectDeliveryDate = response.data.deliveryDate.split("-");
            }
            console.log("===========" + response.data.contractWeight)
            this.setState({
                contractId: response.data.contractId,
                contractName: response.data.contractName,
                contractCode: response.data.contractCode,
                partyA: response.data.partyA,
                partyB: response.data.partyB,
                enterpriseName:response.data.partyAEnterpriseName ,
                selectEnterprise:[response.data.partyAEnterpriseName],
                supplierName:response.data.supplierName,
                selectSupplier:[response.data.supplierName],
                signingTime: response.data.signingTime,
                contractAmount: response.data.contractAmount,
                selectSigningTime: selectSigningTime,
                selParentContractId:response.data.parentContractId,
                selParentContractName:response.data.parentContractName,
                selectParentContract:[response.data.parentContractName],
                compressFileList:response.data.compressFileList,
            })
            var urls = [];
            if(response.data.compressFileList && response.data.compressFileList.length > 0){
                for(var i=0;i<response.data.compressFileList.length;i++){
                    var url = {
                        url:constants.image_addr + '/' +  response.data.compressFileList[i].compressFile
                    } 
                    urls=urls.concat(url)
                    console.log(url)
                }
            }
            this.setState({
                urls:urls
            })
        }
    }
    searchContract = () => {
        var _parentContractDataSource = copyArr(this.state.parentContractDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _parentContractDataSource = _parentContractDataSource.filter(item => item.contractName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _parentContractDataSource: _parentContractDataSource,
        })
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
            //     <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>  
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
            
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("ExpenditureContractList"
            //     // , 
            //     // {
            //     //     // 传递回调函数
            //     //     refresh: this.callBackFunction 
            //     // }
            //     )
            // }}>
            //     <Text style={CommonStyle.headRightText}>外协合同</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

            }}>
            {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>外协合同</Text>
            {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
                </TouchableOpacity>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    // 渲染客户底部滚动数据
    openSupplierSelect() {
        if (!this.state.supplierDataSource || this.state.supplierDataSource.length < 1) {
            WToast.show({ data: "请先添加供应商" });
            return
        }
        this.refs.SelectSupplier.showSupplier(this.state.selectSupplier, this.state.supplierDataSource)
    }
    callBackSupplierValue(value) {
        console.log("==========供应商选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectSupplier: value
        })
        var supplierName = value.toString();
        let loadUrl = "/biz/supplier/getSupplierByName";
        let loadRequest = {
            "supplierName": supplierName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadSupplierDetailData);
    }
    callBackLoadSupplierDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                supplierName:response.data.supplierName,
                partyB:response.data.supplierId
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    openSigningTime() {
        this.refs.SelectSigningTime.showDate(this.state.selectSigningTime)
    }
    callBackSelectSigningTimeValue(value) {
        console.log("==========签订时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectSigningTime: value
        })
        if (this.state.selectSigningTime && this.state.selectSigningTime.length) {
            var signingTime = "";
            var vartime;
            for (var index = 0; index < this.state.selectSigningTime.length; index++) {
                vartime = this.state.selectSigningTime[index];
                if (index === 0) {
                    signingTime += vartime;
                }
                else {
                    signingTime += "-" + vartime;
                }
            }
            this.setState({
                signingTime: signingTime
            })
        }
        var dateString = this.state.signingTime + ' 00:00:01';
        dateString = dateString.substring(0, 19);
        dateString = dateString.replace(/-/g, '/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var SevenDaysLast = new Date(dateStringTimestamp);
        // 用获取毫秒数 加上30天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        SevenDaysLast.setTime(dateStringTimestamp + (30 * 86400000));
        //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
        //格式化月，如果小于9，前面补0  
        var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0  
        var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
        this.setState({
            selectDeliveryDate: [SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
            deliveryDate: SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
        })
        if (this.state.selectDeliveryDate && this.state.selectDeliveryDate.length) {
            var deliveryDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectDeliveryDate.length; index++) {
                vartime = this.state.selectDeliveryDate[index];
                if (index === 0) {
                    deliveryDate += vartime;
                }
                else {
                    deliveryDate += "-" + vartime;
                }
            }
            this.setState({
                deliveryDate: deliveryDate
            })
        }
    }
    
    openDeliveryDate() {
        this.refs.SelectDeliveryDate.showDate(this.state.selectDeliveryDate)
    }

    callBackSelectDeliveryDateValue(value) {
        console.log("==========交付时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectDeliveryDate: value
        })
        if (this.state.selectDeliveryDate && this.state.selectDeliveryDate.length) {
            var deliveryDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectDeliveryDate.length; index++) {
                vartime = this.state.selectDeliveryDate[index];
                if (index === 0) {
                    deliveryDate += vartime;
                }
                else {
                    deliveryDate += "-" + vartime;
                }
            }
            this.setState({
                deliveryDate: deliveryDate
            })
        }
    }

    // 渲染公司底部滚动数据
    openEnterpriseSelect() {
        if (!this.state.enterpriseDataSource || this.state.enterpriseDataSource.length < 1) {
            WToast.show({ data: "请先添加公司" });
            return
        }
        this.refs.SelectEnterprise.showEnterprise(this.state.selectEnterprise, this.state.enterpriseDataSource)
    }

    callBackEnterpriseValue(value) {
        console.log("==========公司选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectEnterprise: value
        })
        var enterpriseName = value.toString();
        // this.setState({
        //     enterpriseName:enterpriseName
        // })
        let loadUrl = "/biz/enterprise/getEnterpriseByName";
        let loadRequest = {
            "enterpriseName": enterpriseName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadEnterpriseDetailData);
    }

    callBackLoadEnterpriseDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                enterpriseName:response.data.enterpriseName,
                partyA: response.data.enterpriseId,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // openContractSelect() {
    //     // if (!this.state.customerId) {
    //     //     WToast.show({data:"请先选择客户"});
    //     //     return
    //     // }
    //     if (!this.state.parentContractDataSource || this.state.parentContractDataSource.length < 1) {
    //         WToast.show({ data: "没有生效状态的合同，请确认" });
    //         return
    //     }
    //     console.log("==========合同数据源：", this.state.parentContractDataSource);
    //     this.refs.SelectContract.showContract(this.state.selectParentContract, this.state.parentContractDataSource)
    // }

    // callBackContractValue(value) {
    //     console.log("==========合同选择结果：", value)
    //     if (!value) {
    //         return;
    //     }
    //     this.setState({
    //         selectParentContract: value
    //     })
    //     var contractName = value.toString();
    //     let loadUrl = "/biz/contract/getContractByName";
    //     let loadRequest = {
    //         "contractName": contractName
    //     };
    //     httpPost(loadUrl, loadRequest, (response) => {
    //         if (response.code == 200 && response.data) {
    //             this.setState({
    //                 parentContractName: response.data.contractName,
    //                 parentContractId: response.data.contractId,
    //             })
    //         }
    //         else if (response.code == 401) {
    //             WToast.show({ data: response.message });
    //             this.props.navigation.navigate("LoginView");
    //         }
    //         else {
    //             WToast.show({ data: response.message });
    //         }
    //     });
    // }

    saveExpenditureContract = () => {
        console.log("=======saveExpenditureContract");
        let toastOpts;
        if (!this.state.contractName) {
            toastOpts = getFailToastOpts("请输入合同名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selParentContractId) {
            toastOpts = getFailToastOpts("请选择总包合同");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.partyA) {
            //子公司列表
            toastOpts = getFailToastOpts("请选择甲方");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.partyB) {
            //供应商列表
            toastOpts = getFailToastOpts("请输入乙方");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.contractCode) {
        //     toastOpts = getFailToastOpts("总包合同");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.contractCode) {
        //     toastOpts = getFailToastOpts("请输入合同金额");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url = "/biz/contract/add";
        if (this.state.contractId) {
            console.log("=========Edit===contractId", this.state.contractId)
            url = "/biz/contract/modify";
        }
        let requestParams = {
            contractId: this.state.contractId,
            contractName: this.state.contractName,
            contractCode: this.state.contractCode,
            contractAmount: this.state.contractAmount,
            signingTime: this.state.signingTime,
            partyA: this.state.partyA,
            partyB: this.state.partyB,
            contractType:"E",
            parentContractId:this.state.selParentContractId,
            compressFileList:this.state.compressFileList,
        };
        httpPost(url, requestParams, this.saveExpenditureContractCallBack);
    }

    // 保存回调函数
    saveExpenditureContractCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    //这一块有问题
    renderContractItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    selParentContractId:item.contractId,
                    selParentContractName:item.contractName,
                })
            }}>
                <View key={item.contractId} style={item.contractId===this.state.selParentContractId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.contractId===this.state.selParentContractId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.contractName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '合同'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.lineHeadBorderStyle} />
                {/* <View style={CommonStyle.contentViewStyle}>
                </View> */}

                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>合同名称</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ contractName: text })}
                        >
                            {this.state.contractName}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>合同编号</Text>
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ contractCode: text })}
                        >
                            {this.state.contractCode}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>总包合同</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        
                        <View style={[{flexWrap:'wrap'}]}>
                        <TouchableOpacity onPress={()=>{
                           if (this.state.parentContractDataSource && this.state.parentContractDataSource.length > 0) {
                                this.setState({
                                    _parentContractDataSource: copyArr(this.state.parentContractDataSource),
                                })
                            }
                            this.setState({ 
                                modal:true,
                            })

                            if (!this.state.selParentContractId && this.state.parentContractDataSource && this.state.parentContractDataSource.length > 0) {
                                this.setState({
                                    selParentContractId:this.state.parentContractDataSource[0].contractId,
                                    selParentContractName:this.state.parentContractDataSource[0].contractName,
                                })
                            }
                        }}>
                            <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, { flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                {
                                    this.state.selParentContractId && this.state.selParentContractName ?
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>{this.state.selParentContractName}</Text>
                                    :
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>选择总包合同名称</Text>
                                }
                                </View>
                        </TouchableOpacity>
                    </View>

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput 
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({searchKeyWord:text})}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={()=>{
                                        this.searchContract();
                                        }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{flexDirection:'row', flexWrap:'wrap', overflow:'scroll'}}>
                                    {
                                        (this.state._parentContractDataSource && this.state._parentContractDataSource.length > 0) 
                                        ? 
                                        this.state._parentContractDataSource.map((item, index)=>{
                                            if (index < 1000) {
                                                return this.renderContractItem(item)
                                            }
                                        })
                                        : <EmptyRowViewComponent/> 
                                    }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                    <TouchableOpacity onPress={() => { 
                                        this.setState({
                                            modal:false,
                                            searchKeyWord:"",                         
                                            // selParentContractName: null,
                                            // selParentContractId: null,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        // if (!this.state.selOrderId) {
                                        //     let toastOpts = getFailToastOpts("您还没有选择砖型");
                                        //     WToast.show(toastOpts);
                                        //     return;
                                        // }
                                        this.setState({
                                            modal:false,
                                            searchKeyWord:"",
                                            parentContractId: this.state.selParentContractId,
                                            parentContractName: this.state.selParentContractName,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>甲方</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={() => this.openEnterpriseSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5), borderWidth:0 }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.enterpriseName ? "请选择" : this.state.enterpriseName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>乙方</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={() => this.openSupplierSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5), borderWidth:0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.supplierName ? "请选择" : this.state.supplierName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>总包合同</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入总包合同'}
                            onChangeText={(text) => this.setState({ contractAmount: text })}
                        >
                            {this.state.contractAmount}
                        </TextInput>
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>合同金额</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ contractAmount: text })}
                        >
                            {this.state.contractAmount}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>签订日期</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openSigningTime()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.signingTime ? "请选择签订日期" : this.state.signingTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>附件</Text>
                    </View>
                    <View>
                    {
                        this.state.compressFileList && this.state.compressFileList.length > 0 ?
                        (
                            <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                {
                                    this.state.compressFileList.map((item,index) =>{
                                        return(
                                            <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex'}]}>
                                            <TouchableOpacity
                                                style={{position:'absolute',left:110,top:-10,zIndex:1000}}
                                                onPress={() => {
                                                    console.log("========deletePhoto")
                                                    var urls = this.state.urls;
                                                    var compressFileList = this.state.compressFileList;

                                                    urls.splice(index,1);
                                                    compressFileList.splice(index,1);
                                                    console.log(urls)
                                                    console.log(this.state.compressFileList)

                                                    this.setState({
                                                        urls:urls,
                                                        compressFileList:compressFileList
                                                    })
                                                }}
                                            >
                                                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/deleteRed.png')}></Image>
                                                        
                                            </TouchableOpacity>
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    isShowImage:true,
                                                    pictureIndex:index
                                                })
                                            }}>
                                                <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />                                                    
                                            </TouchableOpacity>
                                            <Modal visible={this.state.isShowImage} transparent={true}>
                                                <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                 onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls}
                                                 onSave={() => {
                                                            saveImage( this.state.urls[this.state.pictureIndex].url)
                                                         }} />
                                            </Modal>
                                        </View>
                                        )
                                    })
                                }
                                <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                    <TouchableOpacity onPress={() => {
                                        uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                            console.log("========imageUploadResponse", imageUploadResponse)
                                            if (imageUploadResponse.code === 200) {
                                                WToast.show({ data: "上传成功" });
                                                let compressFileList = imageUploadResponse.data
                                                this.setState({
                                                    compressFileList: this.state.compressFileList.concat(compressFileList)
                                                })
                                                var urls = this.state.urls;
                                                if(compressFileList && compressFileList.length > 0){
                                                    for(var i=0;i<compressFileList.length;i++){
                                                        var url = {
                                                            url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                        } 
                                                        urls=urls.concat(url)
                                                        console.log(url)
                                                    }
                                                }
                                                this.setState({
                                                    urls:urls
                                                })
                                            }
                                            else {
                                                WToast.show({ data: imageUploadResponse.message });
                                            }
                                        });

                                    }}>
                                        <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                            <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        )
                        :
                        <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                        <TouchableOpacity onPress={() => {
                            uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                console.log("========imageUploadResponse", imageUploadResponse)
                                if (imageUploadResponse.code === 200) {
                                    WToast.show({ data: "上传成功" });
                                    let compressFileList = imageUploadResponse.data
                                    this.setState({
                                        compressFileList: compressFileList
                                    })
                                    var urls = [];
                                    if(compressFileList && compressFileList.length > 0){
                                        for(var i=0;i<compressFileList.length;i++){
                                            var url = {
                                                url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                            } 
                                            urls=urls.concat(url)
                                            console.log(url)
                                        }
                                    }
                                    this.setState({
                                        urls:urls
                                    })
                                }
                                else {
                                    WToast.show({ data: imageUploadResponse.message });
                                }
                            });
                        }}>
                            <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                            </View>
                        </TouchableOpacity>
                        </View>
                    }
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    {/* <View style={styles.inputRowStyle}>
                    <TouchableOpacity onPress={()=> {
                            if (this.state.orderId) {
                                WToast.show({data:"编辑只能编辑客户、合同、订单名称及砖型以外的信息"})
                            }
                            else{
                                this.openSupplierSelect()
                            }
                        }}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#AOAOAO',fontSize:15}}>
                                    {!this.state.supplierName ? "请选择供应商": this.state.supplierName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}

                    <View style={[CommonStyle.blockAddCancelSaveStyle]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveExpenditureContract.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <BottomScrollSelect
                        ref={'SelectSigningTime'}
                        callBackDateValue={this.callBackSelectSigningTimeValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectDeliveryDate'}
                        callBackDateValue={this.callBackSelectDeliveryDateValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectEnterprise'}
                        callBackEnterpriseValue={this.callBackEnterpriseValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectSupplier'}
                        callBackSupplierValue={this.callBackSupplierValue.bind(this)}
                    />
                    {/* <BottomScrollSelect
                        ref={'SelectContract'}
                        callBackContractValue={this.callBackContractValue.bind(this)}
                    /> */}
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}

const styles = StyleSheet.create({
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:5,
        marginRight:5,
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        // borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },

})