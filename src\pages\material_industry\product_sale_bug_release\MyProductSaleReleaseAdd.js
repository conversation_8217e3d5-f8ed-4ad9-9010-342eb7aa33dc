import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Modal,
    FlatList,RefreshControl,TextInput,ScrollView,Image,KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
var CommonStyle = require('../../../assets/css/CommonStyle');
import { uploadMultiImageLibrary } from '../../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class MyProductSaleReleaseAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            releaseId: "",
            productName: "",
            productModel: "",
            productMaterial: "",
            productWeight: "",
            productionAddr: "",
            productExplain: "",
            productState:"",
            spotFlag:"",
            contactPerson: "",
            contactTel: "",
            operate:"",
            compressFileList:[],
            urls:[],
            pictureIndex:0,
            isShowImage: false,
            topBlockLayoutHeight:0,
            productTypeList:[
                {
                    "typeId":0,
                    "typeName":"耐火制品",
                    "typeCode":"P"
                },
                {
                    "typeId":1,
                    "typeName":"耐火原料",
                    "typeCode":"M"
                }
            ],
            productType:"P",
            productStateName:"",
            productStateList:[
                {
                    "stateId":0,
                    "stateName":"是",
                    "stateCode":"U"
                },
                {
                    "stateId":1,
                    "stateName":"否",
                    "stateCode":"D"
                }
            ],
            spotFlagStateName:"",
            spotFlagStateList:[
                {
                    "spotStateId":0,
                    "spotStateName":"是",
                    "spotStateCode":"Y"
                },
                {
                    "spotStateId":1,
                    "spotStateName":"否",
                    "spotStateCode":"N"
                }
            ],
            selectIsUpProduct: [],
            selectIsSpotFlag: [],
            expDate: "",
            selectExpDate: [],
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { releaseId } = route.params;
            if (releaseId) {
                console.log("=============releaseId" + releaseId + "");
                this.setState({
                    releaseId:releaseId,
                    operate:"编辑"
                })
                let loadTypeUrl= "/biz/product/release/get";
                let loadRequest={'releaseId':releaseId};
                httpPost(loadTypeUrl, loadRequest, this.loadMyAskBugReleaseCallBack);
            }
            else {
                this.setState({
                    operate:"发布",
                    productState:"U",
                    spotFlag:"Y",
                    productStateName:"是",
                    spotFlagStateName:"是",
                    selectIsUpProduct:["是"],
                    selectIsSpotFlag:["是"]
                })
                const d = new Date();
                //获取当前后三个月的日期
                const SY=d.getFullYear();
                const SM=d.getMonth()+1;
                const SD=d.getDate();
                //结果
                const mydate =new Date(SY,SM+3,SD);
                const yyyy = mydate.getFullYear();
                let MM = ("0" + mydate.getMonth()).slice(-2);
                let dd = ("0" + mydate.getDate()).slice(-2);;
                //如果月份是2月，就进行闰年和非闰年处理，防止出现类似2月30的情况
                if (MM==2 && dd>28){
                if (yyyy%4==0){
                    dd = 29;
                }else {
                    dd = 28;
                }
                }
                console.log(yyyy,'-',MM,'-',dd);
                this.setState({
                    selectExpDate: [yyyy,MM,dd],
                    expDate:`${yyyy}-${MM}-${dd}`
                })
            }
        }
    }
    loadMyAskBugReleaseCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                productName:response.data.productName,
                productModel:response.data.productModel,
                productMaterial:response.data.productMaterial,
                productWeight:response.data.productWeight,
                productPrice:response.data.productPrice,
                productionAddr:response.data.productionAddr,
                productExplain:response.data.productExplain,
                productState:response.data.productState,
                contactPerson:response.data.contactPerson,
                contactTel:response.data.contactTel,
                compressFileList:response.data.compressFileList,
                productType:response.data.productType,
                spotFlag:response.data.spotFlag,
                selectIsUpProduct:response.data.productState == "U" ? ["是"] : ["否"],
                productStateName:response.data.productState == "U" ? "是" : "否",
                selectIsSpotFlag:response.data.spotFlag == "Y" ? ["是"] : ["否"],
                spotFlagStateName:response.data.spotFlag == "Y" ? "是" : "否"
            })
            var expDate = response.data.expDate?response.data.expDate.split(" "):null;
            if(null != expDate){
                this.setState({
                    expDate:expDate[0],
                    selectExpDate:expDate[0].split("-")
                })
            }
            console.log("expDate==",expDate)

            var urls = [];
            if(response.data.compressFileList && response.data.compressFileList.length > 0){
                for(var i=0;i<response.data.compressFileList.length;i++){
                    var url = {
                        url:constants.image_addr + '/' +  response.data.compressFileList[i].compressFile
                    } 
                    urls=urls.concat(url)
                    console.log(url)
                }
            }
            this.setState({
                urls:urls
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("MyProductSaleRelease")
            }}>
                <Text style={CommonStyle.headRightText}>我的产品</Text>
            </TouchableOpacity>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveMyAskBugRelease =()=> {
        console.log("=======saveMyAskBugRelease");
        let toastOpts;
        if (!this.state.productName) {
            toastOpts = getFailToastOpts("请填写产品名称");
            WToast.show(toastOpts)
            return;
        }
        if(this.state.productType == "P"){
            if (!this.state.productModel) {
                toastOpts = getFailToastOpts("请填写型号");
                WToast.show(toastOpts)
                return;
            }
            if (!this.state.productMaterial) {
                toastOpts = getFailToastOpts("请填写材质");
                WToast.show(toastOpts)
                return;
            }
        }
        
        if (!this.state.productWeight) {
            toastOpts = getFailToastOpts("请填写数量");
            WToast.show(toastOpts)
            return;
        }
        // if(this.state.productType == "P"){
        //     if (!this.state.productPrice) {
        //         toastOpts = getFailToastOpts("请填写价格");
        //         WToast.show(toastOpts)
        //         return;
        //     }
        // }
        if (!this.state.productionAddr) {
            toastOpts = getFailToastOpts("请填写产地");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.compressFileList || this.state.compressFileList.length < 1) {
            toastOpts = getFailToastOpts("请上传图片");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contactPerson) {
            toastOpts = getFailToastOpts("请填写联系人");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contactTel) {
            toastOpts = getFailToastOpts("请填写联系电话");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.spotFlag) {
            toastOpts = getFailToastOpts("请选择是否现货");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/product/release/add";
        if (this.state.releaseId) {
            console.log("=========Edit===releaseId", this.state.releaseId)
            url= "/biz/product/release/modify";
        }
        let requestParams={
            releaseId:this.state.releaseId,
            productName: this.state.productName,
            productModel: this.state.productType == "P" ? this.state.productModel : null,
            productMaterial:this.state.productType == "P" ? this.state.productMaterial : null,
            productWeight:this.state.productWeight,
            productPrice:this.state.productPrice,
            productionAddr:this.state.productionAddr,
            productExplain:this.state.productExplain,
            contactPerson:this.state.contactPerson,
            contactTel:this.state.contactTel,
            releaseType:"R",
            userId:constants.loginUser.userId,
            compressFileList:this.state.compressFileList,
            productState:this.state.productState,
            productType:this.state.productType,
            spotFlag:this.state.spotFlag,
            auditState:"1",
            expDate:this.state.expDate + " 23:59:59" 
        };
        // console.log("============spotFlag", this.state.spotFlag)
        httpPost(url, requestParams, this.saveMyAskBugReleaseCallBack);
    }

    // 保存回调函数
    saveMyAskBugReleaseCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    productTypeRow=(item, index)=>{
        return (
            <View key={item.typeId} >
                <TouchableOpacity onPress={()=>{
                    var typeCode = item.typeCode;
                    this.setState({
                        productType:typeCode
                    })
                }}>
                   <View key={item.typeCode} style={[item.typeCode===this.state.productType? [CommonStyle.selectedBlockItemViewStyle,
                        {backgroundColor:'#FFF',borderBottomWidth:2,borderBottomColor:'#FC783D',borderRadius:0,paddingBottom:0,marginBottom:0}] : CommonStyle.blockItemViewStyle,{paddingLeft:8,backgroundColor:'#FFF',paddingBottom:0,marginBottom:0.8}]}>
                        <Text style={[item.typeCode===this.state.productType? styles.selectedBlockItemTextStyle : styles.blockItemTextStyle]}>
                            {item.typeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    openIsUpProductSelect() {
        if (!this.state.productStateList || this.state.productStateList.length < 1) {
            WToast.show({ data: "没有数据，请确认" });
            return
        }
        console.log("==========数据源：", this.state.productStateList);
        this.refs.SelectIsUpProduct.showIsUpProduct(this.state.selectIsUpProduct, this.state.productStateList)
    }

    callBackIsUpProductValue(value) {
        console.log("==========是否上架选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectIsUpProduct: value,
            productStateName:value.toString(),
            productState:value.toString()=="是"?"U":"D"
        })
    }
    openIsSpotFlagSelect() {
        if (!this.state.spotFlagStateList || this.state.spotFlagStateList.length < 1) {
            WToast.show({ data: "没有数据，请确认" });
            return
        }
        console.log("==========数据源：", this.state.spotFlagStateList);
        this.refs.SelectIsSpotFlag.showIsSpotFlag(this.state.selectIsSpotFlag, this.state.spotFlagStateList)
    }

    callBackIsSpotFlagValue(value) {
        console.log("==========是否现货选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectIsSpotFlag: value,
            spotFlagStateName:value.toString(),
            spotFlag:value.toString()=="是"?"Y":"N"
        })
    }

    openExpDate() {
        this.refs.SelectExpDate.showDate(this.state.selectExpDate)
    }
    callBackSelectExpDateValue(value) {
        console.log("==========失效时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectExpDate: value
        })
        if (this.state.selectExpDate && this.state.selectExpDate.length) {
            var expDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectExpDate.length; index++) {
                vartime = this.state.selectExpDate[index];
                if (index === 0) {
                    expDate += vartime;
                }
                else {
                    expDate += "-" + vartime;
                }
            }
            this.setState({
                expDate: expDate
            })
            console.log("==========expDate", (expDate + " 23:59:59" ))
        }
        
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle,{backgroundColor:'#F5F5F5'}]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '产品'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[{marginTop:0,backgroundColor:'#FFF'}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop: 5,marginBottom:5, index: 1000, flexWrap: 'wrap', flexDirection: 'row' ,borderBottomColor:'#33333333',borderBottomWidth:1,alignItems:'flex-end'}}>
                        {
                            (this.state.productTypeList && this.state.productTypeList.length > 0)
                                ?
                                this.state.productTypeList.map((item, index) => {
                                    return this.productTypeRow(item)
                                })
                                : <View />
                        }
                    </View>
                </View>
                <ScrollView style={[CommonStyle.formContentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>产品名称</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入产品名称'}
                            onChangeText={(text) => this.setState({ productName: text })}
                        >
                            {this.state.productName}
                        </TextInput>
                    </View>
                    
                        {
                            this.state.productType == "P"
                            ?
                            <View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                        <Text style={styles.leftLabNameTextStyle}>型号</Text>
                                    </View>
                                    <TextInput
                                        //keyboardType='text'
                                        style={styles.inputRightText}
                                        placeholder={'请输入型号'}
                                        onChangeText={(text) => this.setState({ productModel: text })}
                                    >
                                        {this.state.productModel}
                                    </TextInput>
                                </View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                        <Text style={styles.leftLabNameTextStyle}>材质</Text>
                                    </View>
                                    <TextInput
                                        //keyboardType='text'
                                        style={styles.inputRightText}
                                        placeholder={'请输入材质'}
                                        onChangeText={(text) => this.setState({ productMaterial: text })}
                                    >
                                        {this.state.productMaterial}
                                    </TextInput>
                                </View>
                            </View>
                            :null
                        }
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>数量(吨)</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入数量'}
                            onChangeText={(text) => this.setState({ productWeight: text })}
                        >
                            {this.state.productWeight}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            {/* {
                                this.state.productType == "P"?
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                :
                                <Text style={{marginLeft:15}}></Text>
                            } */}
                            <Text style={{marginLeft:15}}></Text>
                            <Text style={styles.leftLabNameTextStyle}>价格(元)</Text>
                        </View>
                        <TextInput
                            // keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入价格'}
                            onChangeText={(text) => this.setState({ productPrice: text })}
                        >
                            {this.state.productPrice}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>产地</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入产地'}
                            onChangeText={(text) => this.setState({ productionAddr: text })}
                        >
                            {this.state.productionAddr}
                        </TextInput>
                    </View>

                    <View style={[{flexDirection: 'row',marginTop: 10,borderBottomColor:'#33333315',borderBottomWidth:0.8}]}>
                        <View style={[styles.leftLabView,{alignItems:'flex-start'}]}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>图片</Text>
                        </View>
                        <View style={{marginTop:10,width: screenWidth - (leftLabWidth + 5),}}>
                            {
                                this.state.compressFileList && this.state.compressFileList.length > 0 ?
                                (
                                    <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                        {
                                            this.state.compressFileList.map((item,index) =>{
                                                return(
                                                    <View style={[{ width: 85,height:85,marginLeft:20,marginBottom:10,display:'flex'}]}>
                                                    <TouchableOpacity
                                                        style={{position:'absolute',left:75,top:-10,zIndex:1000}}
                                                        onPress={() => {
                                                            console.log("========deletePhoto")
                                                            var urls = this.state.urls;
                                                            var compressFileList = this.state.compressFileList;

                                                            urls.splice(index,1);
                                                            compressFileList.splice(index,1);
                                                            console.log(urls)
                                                            console.log(this.state.compressFileList)

                                                            this.setState({
                                                                urls:urls,
                                                                compressFileList:compressFileList
                                                            })
                                                        }}
                                                    >
                                                        <Image style={{ width: 22, height: 22}} source={require('../../../assets/icon/iconfont/deleteRed.png')}></Image>
                                                    </TouchableOpacity>
                                                    <TouchableOpacity onPress={() => {
                                                        this.setState({
                                                            isShowImage:true,
                                                            pictureIndex:index
                                                        })
                                                        // uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                                        //     console.log("========imageUploadResponse", imageUploadResponse)
                                                        //     if (imageUploadResponse.code === 200) {
                                                        //         WToast.show({ data: "上传成功" });
                                                        //         let compressFileList = imageUploadResponse.data
                                                        //         this.setState({
                                                        //             compressFileList: compressFileList
                                                        //         })
                                                        //     }
                                                        //     else {
                                                        //         WToast.show({ data: imageUploadResponse.message });
                                                        //     }
                                                        // });
        
                                                    }}>
                                                        <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 85, width:85 }} />                                                    
                                                                
                                                    </TouchableOpacity>
                                                    <Modal visible={this.state.isShowImage} transparent={true}>
                                                        <ImageViewer onClick={()=>{this.setState({isShowImage:false})}}  index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                         onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} 
                                                         onSave={() => {
                                                            saveImage( this.state.urls[this.state.pictureIndex].url)
                                                         }}/>
                                                    </Modal>
                                                </View>
                                                )
                                                
                                            })
                                        }
                                    <View style={[{ width: 85,height:85,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:0.5,borderStyle:'solid',borderRadius:5}]}>
                                            <TouchableOpacity onPress={() => {
                                                uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                                    console.log("========imageUploadResponse", imageUploadResponse)
                                                    if (imageUploadResponse.code === 200) {
                                                        WToast.show({ data: "上传成功" });
                                                        let compressFileList = imageUploadResponse.data
                                                        this.setState({
                                                            compressFileList: this.state.compressFileList.concat(compressFileList)
                                                        })
                                                        var urls = this.state.urls;
                                                        if(compressFileList && compressFileList.length > 0){
                                                            for(var i=0;i<compressFileList.length;i++){
                                                                var url = {
                                                                    url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                                } 
                                                                urls=urls.concat(url)
                                                                console.log(url)
                                                            }
                                                        }
                                                        this.setState({
                                                            urls:urls
                                                        })
                                                    }
                                                    else {
                                                        WToast.show({ data: imageUploadResponse.message });
                                                    }
                                                });

                                            }}>
                                                <View style={{width:120,height:150,display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center'}}>
                                                    {/* <Image source ={require('../../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image> */}
                                                    <Text style={{fontSize:30,color:"#a0a0a0"}}>+</Text>
                                                    <Text style={{fontSize:14,color:"#a0a0a0"}}>上传图片</Text>
                                                </View>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                    
                                )
                                :
                                <View style={[{ width: 85,height:85,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:0.5,borderStyle:'solid',borderRadius:5}]}>
                                <TouchableOpacity onPress={() => {
                                        uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                            console.log("========imageUploadResponse", imageUploadResponse)
                                            if (imageUploadResponse.code === 200) {
                                                WToast.show({ data: "上传成功" });
                                                let compressFileList = imageUploadResponse.data
                                                this.setState({
                                                    compressFileList: compressFileList
                                                })
                                                var urls = [];
                                                if(compressFileList && compressFileList.length > 0){
                                                    for(var i=0;i<compressFileList.length;i++){
                                                        var url = {
                                                            url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                        } 
                                                        urls=urls.concat(url)
                                                        console.log(url)
                                                    }
                                                }
                                                this.setState({
                                                    urls:urls
                                                })
                                            }
                                            else {
                                                WToast.show({ data: imageUploadResponse.message });
                                            }
                                        });

                                    }}>
                                        <View style={{width:120,height:150,display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center'}}>
                                            {/* <Image source ={require('../../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image> */}
                                            <Text style={{fontSize:30,color:"#a0a0a0"}}>+</Text>
                                            <Text style={{fontSize:14,color:"#a0a0a0"}}>上传图片</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            }
                            
                        </View>
                    </View>
                        
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabNameTextStyle,{marginLeft:15}]}>说明</Text>
                        </View>
                        <TextInput 
                            style={[styles.inputRightText,{height:100}]}
                            multiline={true}
                            textAlignVertical="top"
                            placeholder={'请输入产品相关说明（最多100个字符）'}
                            onChangeText={(text) => this.setState({productExplain:text})}
                        >
                            {this.state.productExplain}
                        </TextInput>
                    </View>
                     <View style={[styles.inputRowStyle,{marginTop:10}]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入联系人'}
                            onChangeText={(text) => this.setState({ contactPerson: text })}
                        >
                            {this.state.contactPerson}
                        </TextInput>
                    </View> 
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入联系电话'}
                            onChangeText={(text) => this.setState({ contactTel: text })}
                        >
                            {this.state.contactTel}
                        </TextInput>
                    </View> 
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>是否上架</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openIsUpProductSelect()}>
                            <View style={[styles.inputRightText,{marginTop:10}]}>
                                <Text style={{ color: '#000', fontSize: 15 }}>
                                    {!this.state.productStateName ? "请选择是否上架" : this.state.productStateName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View> 
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>是否现货</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openIsSpotFlagSelect()}>
                            <View style={[styles.inputRightText,{marginTop:10}]}>
                                <Text style={{ color: '#000', fontSize: 15 }}>
                                    {!this.state.spotFlagStateName ? "请选择是否现货" : this.state.spotFlagStateName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={[styles.leftLabNameTextStyle,{}]}>失效时间</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openExpDate()}>
                            <View style={[styles.inputRightText,{marginTop:10}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.expDate ? "请选择失效时间" : this.state.expDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={[CommonStyle.btnRowStyle,{flexDirection:'column',alignItems:'center'}]}>
                        <TouchableOpacity onPress={this.saveMyAskBugRelease.bind(this)}>
                            <View style={styles.btnRowRightSaveBtnView}>
                                {/* <Image style={{width:25, height:25,marginRight:15}} source={require('../../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={styles.btnRowLeftCancelBtnView} >
                                {/* <Image style={{width:25, height:25,marginRight:15}} source={require('../../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect
                        ref={'SelectIsUpProduct'}
                        callBackIsUpProductValue={this.callBackIsUpProductValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectIsSpotFlag'}
                        callBackIsSpotFlagValue={this.callBackIsSpotFlagValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectExpDate'}
                        callBackDateValue={this.callBackSelectExpDateValue.bind(this)}
                    />
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        borderBottomColor:'#33333315',
        borderBottomWidth:0.8,
    },

    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        color:'#333333',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    btnRowRightSaveBtnView:{
        backgroundColor:'#559ff3',
        alignItems:'center',
        // alignContent:'center',
        justifyContent:'center',
        borderRadius:6,
        flexDirection:'column',
        width:screenWidth - 40,
        height:45,
        marginTop:15
    },
    btnRowLeftCancelBtnView:{
        backgroundColor:'#FFFFFF',
        alignItems:'center',
        justifyContent:'center',
        borderWidth:1,
        borderColor:'#A0A0A0',
        borderRadius:6,
        flexDirection:'column',
        width:screenWidth - 40,
        height:45,
        // marginLeft:35,
        marginTop:15
    },
    
    selectedBlockItemTextStyle: {
        fontSize:19,
        color:'#FC783D',
        marginBottom:5
    },
    blockItemTextStyle: {
        fontSize:17,
        color:'#33333399',
        marginBottom:5
    },
   
});