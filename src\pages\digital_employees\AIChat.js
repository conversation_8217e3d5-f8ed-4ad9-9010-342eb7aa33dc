import React, {Component} from 'react';
import {
  Dimensions,
  Image,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  View,
} from 'react-native';
import {WebView} from 'react-native-webview';
import CommonHeadScreen from '../../component/CommonHeadScreen';
const {
  ifIphoneXBodyViewHeight,
  ifIphoneXContentViewDynamicHeight,
} = require('../../utils/ScreenUtil');
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class AIChat extends Component {
  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
      </TouchableOpacity>
    );
  }

  // // 头部右侧
  renderRightItem() {
    return <View></View>;
  }

  render() {
    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta name="viewport" 
            content="width=device-width,
            initial-scale=1.0,
            maximum-scale=1.0,
            minimum-scale=1.0,
            user-scalable=no,
            shrink-to-fit=no">
        <style>
            /* 自定义类样式 */
            .custom-chat-frame {
                border: 2px solid #e0e0e0;
                border-radius: 12px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                background: #ffffff;
                overflow: hidden;
            }

            /* 响应式调整 */
            @media (max-width: 768px) {
                .custom-chat-frame {
                    border-radius: 0;
                    border-left: none;
                    border-right: none;
                }
            }

            /* 加载状态样式 */
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255,255,255,0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 999;
            }
        </style>
    </head>
    <body style="margin:0; padding:0;">
        <div class="loading-overlay" id="loading">加载中...</div>
        <iframe 
            id="myIframe" 
            src="https://maxkb.njjzgk.com/ui/chat/f20bd29356e1c404"
            class="custom-chat-frame"
            frameborder="0"
            allow="microphone"
            onload="document.getElementById('loading').style.display='none'"
            style="width:100%; height:100vh; border:none; overflow:hidden;">
        </iframe>
        <script>
            window.onload = function() {
                const iframe = document.getElementById('myIframe');
                const innerDoc = iframe.contentDocument || iframe.contentWindow.document;

                // 确保 iframe 已经加载
                iframe.onload = function() {
                    // 创建一个新的样式元素
                    const style = innerDoc.createElement('style');
                    style.textContent = \`
                        .el-dropdown {
                            display: none;
                        }
                    \`;
                    innerDoc.head.appendChild(style);
                };
            };
        </script>
    </body>
    </html>
`;

    return (
      <View style={[{backgroundColor: '#FFFFFF', height: screenHeight}]}>
        <CommonHeadScreen
          title={'极致学社智能助手'}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <KeyboardAvoidingView
          style={{height: ifIphoneXBodyViewHeight()}}
          behavior={Platform.OS == 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS == 'ios' ? 0 : 25}>
          <WebView
            source={{html: htmlContent}}
            javaScriptEnabled={true}
            style={{flex: 1}}
            allowsFullscreenVideo={false}
            mixedContentMode="compatibility"
          />
        </KeyboardAvoidingView>
      </View>
    );
  }
}
