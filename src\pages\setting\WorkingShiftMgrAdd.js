import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class WorkingShiftMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            operateTenantId:"",
            tenantName:"",
            operate:"",
            shiftId:"",
            shiftName:"",
            shiftSort:0,
            productionLineDataSource:[],
            selProductionLineId:"",
            shiftTypeDataSource:[],
            selShiftType:""
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadProductionLineList();
        
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { shiftId ,productionLineId, shiftType} = route.params;
            if (shiftId) {
                this.setState({
                    operate:"编辑",
                    shiftId:shiftId,
                    selProductionLineId:productionLineId,
                    selShiftType:shiftType
                })               
                console.log("====shiftType===="+shiftType);
                loadTypeUrl= "/biz/working/shift/get";
                loadRequest={'shiftId':shiftId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditWorkingShiftDataCallBack);
       
            }
            else {
                this.setState({
                    operate:"新增",
                })
            }
        }
        this.loadShiftTypeList();
    }

    loadProductionLineList=()=>{
        // 生产车间列表
        let url= "/biz/production/line/list";
        let loadRequest={'currentPage':1, 'pageSize':1000};
        httpPost(url, loadRequest, this.callBackLoadProductionLine);
    }

    callBackLoadProductionLine=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            let productionLineDataSource = response.data.dataList;
            let selProductionLineId = response.data.dataList[0].productionLineId;
            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                selProductionLineId = constants.loginUser.spUserExtDTO.productionLineId;
            }

            if(this.state.selProductionLineId) {
                selProductionLineId = this.state.selProductionLineId;
            }

            this.setState({
                productionLineDataSource:productionLineDataSource,
                selProductionLineId:selProductionLineId,
            })
            // console.log("111"+(3%3==0 ? 1: 0))
            // console.log(Math.floor(this.state.productionLineDataSource.length/3+1-Math.floor(this.state.productionLineDataSource%3==0 ? 1:0)))
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadShiftTypeList=()=>{
        let url= "/biz/working/shift/type/list";
        let loadRequest={'currentPage':1, 'pageSize':1000};
        httpPost(url, loadRequest, this.callBackShiftTypeList);
    }

    callBackShiftTypeList=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                shiftTypeDataSource:response.data,
                selShiftType:this.state.shiftId?this.state.selShiftType:response.data[0].shiftType
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditWorkingShiftDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {

            this.setState({
                shiftName:response.data.shiftName,
                shiftSort:response.data.shiftSort
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("WorkingShiftMgrList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>班次管理</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>班次管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveContractCollectMoneyPoint =()=> {
        console.log("=======saveWorkingShift");
        let toastOpts;
        if (!this.state.shiftName) {
            toastOpts = getFailToastOpts("请填写班次名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selProductionLineId) {
            toastOpts = getFailToastOpts("请选择生产车间");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selShiftType) {
            toastOpts = getFailToastOpts("请填写所属部门");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/working/shift/add";
        if (this.state.shiftId) {
            console.log("=========Edit===shiftId", this.state.shiftId)
            url= "/biz/working/shift/modify";
        }
        let requestParams={
            shiftId:this.state.shiftId,
            shiftName: this.state.shiftName,
            shiftSort:this.state.shiftSort,
            productionLineId:this.state.selProductionLineId,
            shiftType:this.state.selShiftType
        };
        httpPost(url, requestParams, this.saveWorkingShiftCallBack);
    }

    // 保存回调函数
    saveWorkingShiftCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 车间单项渲染
    renderProductionLineRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.shiftId) {
                    return;
                }
                this.setState({
                    selProductionLineId:item.productionLineId,
                }) 
                console.log("=======变化的id" + this.state.selProductionLineId);
            }}>
                
                
                <View key={item.productionLineId} style={[item.productionLineId===this.state.selProductionLineId? 
                    // CommonStyle.selectedBlockItemViewStyle 
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    : 
                    // CommonStyle.blockItemViewStyle
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        // marginLeft:8,
                        // marginRight: 4,
                        marginHorizontal:10,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 60)/3,
                        borderRadius: 4
                    }
                    ,
                    this.state.shiftId ? 
                    CommonStyle.disableViewStyle 
                    :
                     ''
                    ] }>
                    <Text style={[item.productionLineId===this.state.selProductionLineId? 
                    // CommonStyle.selectedBlockItemTextStyle16 
                    {
                        color: '#1E6EFA'
                    } 
                    :
                    // CommonStyle.blockItemTextStyle16
                    {
                        color: '#404956'
                    }
                    ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                    {item.productionLineName}
                    </Text>
                </View>
            </TouchableOpacity>

        )
    }

    renderShiftTypeRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.shiftId) {
                    return;
                }
                this.setState({
                    selShiftType:item.shiftType,
                }) 
                console.log("=======变化的id" + item.shiftType);
            }}>
                <View key={item.shiftId} style={[item.shiftType===this.state.selShiftType? 
                    // CommonStyle.selectedBlockItemViewStyle 
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    : 
                    // CommonStyle.blockItemViewStyle
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        // marginRight: 8,
                        marginHorizontal:8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                    ,
                    this.state.shiftId? 
                    CommonStyle.disableViewStyle 
                    :
                     ''
                    ] }>
                    <Text style={[item.shiftType===this.state.selShiftType? 
                        // CommonStyle.selectedBlockItemTextStyle16 
                        {
                            color: '#1E6EFA'
                        } 
                        : 
                        // CommonStyle.blockItemTextStyle16
                        {
                            color: '#404956'
                        }
                        ,
                        {
                            fontSize: 16, textAlign : 'center'
                        }
                        ]}>
                        {item.shiftTypeName}
                    </Text>
                </View>
            </TouchableOpacity>

        )
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + "班次"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>班次名称</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({shiftName:text})}
                        >
                            {this.state.shiftName}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    {
                        this.state.productionLineDataSource.length == 1 ?
                        <View></View>
                        :
                        <View>
                            <View style={[styles.inputRowStyle]}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>生产车间</Text>
                                </View>
                            </View>
                            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                            <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                                {
                                    (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0) 
                                    ? 
                                    this.state.productionLineDataSource.map((item, index)=>{
                                        return this.renderProductionLineRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                        </View>
                        
                    }
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />


                        <View>
                            <View style={[styles.inputRowStyle]}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>所属部门</Text>
                                </View>
                            </View>
                            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                            <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                                {
                                    (this.state.shiftTypeDataSource && this.state.shiftTypeDataSource.length > 0) 
                                    ? 
                                    this.state.shiftTypeDataSource.map((item, index)=>{
                                        return this.renderShiftTypeRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                        </View>
                        <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({shiftSort:text})}
                        >
                            {this.state.shiftSort}


                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0}} />
                    <View style={{height:ifIphoneXContentViewHeight()-245-Math.ceil(this.state.shiftTypeDataSource.length/3)*40-Math.ceil(this.state.productionLineDataSource.length/3)*54-66, backgroundColor:'#F2F5FC'}}>
                        {/* <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                        >
                        </TextInput> */}
                    </View>
                    
                    <View style={[CommonStyle.blockAddCancelSaveStyle, { marginTop: 0 ,marginBottom: 20}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveContractCollectMoneyPoint.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    {/* <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveContractCollectMoneyPoint.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#FFFFFF',
        // borderWidth:1,
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        // paddingTop:5,
        // paddingBottom:5,
        marginTop:4,
        marginBottom:4,
        marginLeft:15, 
        // borderTopWidth:1,
        // borderTopColor:'#F1F1F1',
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:0,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'#E63633',
        marginLeft:4,
        marginRight:3
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:4,
        marginRight:3,
    },
})