import React,{Component} from 'react';
import {
    Alert,
    View, 
    ScrollView, Image,Text,TextInput,Modal, StyleSheet, FlatList,TouchableOpacity, Dimensions
} from 'react-native';

import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class HLMedicineStorageOutDetailAdd extends Component {

    constructor(props) {
        super(props);
        this.state ={
            detailId:'',
            selMedicineId:"",
            selMedicineName:"",
            medicineDataSource:[],
            _medicineDataSource:[],
            searchKeyWord:"",
            medicineSpec:"",
            salePrice:"",
            unitName:"",
            totalAmount:"",
            everyTimeAmount:"",
            frequency:"",
            medicineUsage:"",
            moneyAmount:"",
            storageOutId:'',
            modal:false,
            storageOutDetailList:[],
            inventoryAmount:"",
            hospitalId:"",
            pharmacyId:""
        }
    }

    UNSAFE_componentWillMount(){
        console.log('=aaaa=UNSAFE_componentWillMount==');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageOutId , hospitalId , pharmacyId, hlMedicineStorageOutDetailDTOList} = route.params;
            if (storageOutId) {
                console.log("=========storageOutId:", storageOutId);
                this.setState({
                    storageOutId:storageOutId
                })
                // this.loadOrderData(customerId);
            }
            if (hospitalId) {
                console.log("=========hospitalId:", hospitalId);
                this.setState({
                    hospitalId:hospitalId
                })
            }
            if (pharmacyId) {
                console.log("=========pharmacyId:", pharmacyId);
                this.setState({
                    pharmacyId:pharmacyId
                })
            }
            if (hlMedicineStorageOutDetailDTOList) {
                console.log("=========hlMedicineStorageOutDetailDTOList:", hlMedicineStorageOutDetailDTOList);
                this.setState({
                    storageOutDetailList:hlMedicineStorageOutDetailDTOList
                })
            }

        }
        this.loadMedicineData();
    }

        //加载物资数据
    loadMedicineData=()=>{
        let loadUrl= "/biz/hl/medicine/list";
        let loadRequest={'currentPage':1,'pageSize':1000};
        httpPost(loadUrl, loadRequest, this.loadMedicineDataCallBack);
    }
        
    loadMedicineDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                medicineDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    //加载药品
    loadMedicine = () => {
        var _medicineDataSource = copyArr(this.state.medicineDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _medicineDataSource = _medicineDataSource.filter(item => item.medicineName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _medicineDataSource: _medicineDataSource,
        })
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh(this.state.storageOutDetailList)
                }
                this.props.navigation.navigate("HLMedicineStorageOutAdd") 

 
            }}>
                <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                {/* <Text style={CommonStyle.headRightText}>完成</Text> */}
            </TouchableOpacity>
        )
    }

    // 渲染订单底部滚动数据
    // openOrderSelect(){
    //     this.refs.SelectOrder.showOrder(this.state.selectOrder, this.state.orderDataSource)
    // }

    renderMedicineItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.detailId) {
                    return;
                }
                this.setState({
                    selMedicineId:item.medicineId,
                    selMedicineName:item.medicineName,
                    medicineSpec:item.medicineSpec,
                    salePrice:item.salePrice,
                    unitName:item.unitName
                })
                
            }}>
                <View key={item.medicineId} style={item.medicineId===this.state.selMedicineId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.medicineId===this.state.selMedicineId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.medicineName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    
    render(){
        return(
            <View>
                <CommonHeadScreen title='新增明细'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}> 
                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>药品</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[(!this.state.medicineDataSource || this.state.medicineDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={()=>{
                            if (this.state.medicineDataSource && this.state.medicineDataSource.length > 0) {
                                this.setState({
                                    _medicineDataSource: copyArr(this.state.medicineDataSource),
                                })
                            }
                            if (!this.state.medicineDataSource || this.state.medicineDataSource.length === 0) {
                                
                                let errorMsg = '暂无药品';
                                Alert.alert('确认', errorMsg, [
                                    {
                                        text: "确定", onPress: () => {
                                            WToast.show({ data: '点击了确定' });
                                        }
                                    }
                                ]);
                                return;
                            }

                            this.setState({
                                modal:true,
                                searchKeyWord: ""
                            })

                            if (!this.state.selMedicineId && this.state.medicineDataSource && this.state.medicineDataSource.length > 0) {
                                this.setState({
                                    selMedicineId:this.state.medicineDataSource[0].medicineId,
                                    selMedicineName:this.state.medicineDataSource[0].medicineName,
                                    medicineSpec:this.state.medicineDataSource[0].medicineSpec,
                                    salePrice:this.state.medicineDataSource[0].salePrice,
                                    unitName:this.state.medicineDataSource[0].unitName
                                })
                            }
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}]}>
                                <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                                    {this.state.selMedicineId && this.state.selMedicineName ? (this.state.selMedicineName) : "选择药品"}
                                </Text>
                            </View>     
                            </TouchableOpacity>
                        </View>
                    </View>
                    <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.modal}>
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={CommonStyle.modalContentViewStyle}>
                            <View style={CommonStyle.rowLabView}>
                                <View style={styles.leftLabViewImage}>
                                    <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                    <TextInput
                                        style={[styles.searchInputText]}
                                        returnKeyType="search"
                                        returnKeyLabel="搜索"
                                        onSubmitEditing={e => {
                                            this.loadMedicine();
                                    }}        
                                        placeholder={'药品名称'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                </View>
                                {/* <TouchableOpacity onPress={()=>{
                                    this.loadMedicine();
                                    }}>
                                    <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                        <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                    </View>
                                </TouchableOpacity> */}
                            </View>
                            <ScrollView style={{}}>
                                <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                    {
                                        (this.state._medicineDataSource && this.state._medicineDataSource.length > 0)
                                            ?
                                            this.state._medicineDataSource.map((item, index) => {
                                                if (index < 1000) {
                                                    return this.renderMedicineItem(item)
                                                }
                                            })
                                            : <EmptyRowViewComponent />
                                    }
                                </View>
                            </ScrollView>
                            <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                <TouchableOpacity onPress={() => { 
                                    this.setState({
                                        modal:false,
                                    }) 
                                }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                    <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => {
                                    if (!this.state.selMedicineId) {
                                        let toastOpts = getFailToastOpts("您还没有选择药品");
                                        WToast.show(toastOpts);
                                        return;
                                    }
                                        let loadUrl= "/biz/hl/medicine/inventory/list";
                                        let loadRequest={
                                            'currentPage':1,
                                            'pageSize':100,
                                            "medicineId":this.state.selMedicineId,
                                            "pharmacyId":this.state.pharmacyId,
                                            "hospitalId":this.state.hospitalId,
                                        };
                                        httpPost(loadUrl, loadRequest, (response)=>{
                                            if (response.code == 200 && response.data) {
                                                if(response.data.dataList && response.data.dataList.length === 1){
                                                    var amount = 0;
                                                    if(this.state.storageOutDetailList && this.state.storageOutDetailList.length > 0) {
                                                       var list =  this.state.storageOutDetailList.filter(item => {return item.medicineId === this.state.selMedicineId})
                                                       console.log("list===",list) 
                                                       list.forEach(item =>{
                                                            amount += item.totalAmount*1
                                                        })
                                                    }
                                                    this.setState({
                                                        inventoryAmount:response.data.dataList[0].storageInAmountDisplay - response.data.dataList[0].storageOutAmountDisplay - amount
                                                    })
                                                }
                                                else {
                                                    this.setState({
                                                        inventoryAmount:0
                                                    })
                                                }
                                            }
                                            else if (response.code == 401) {
                                                WToast.show({data:response.message});
                                                this.props.navigation.navigate("LoginView");
                                            }
                                        });
                                    // }
                                    this.setState({
                                        modal: false,
                                    })
                                }}>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                    <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                        <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>库存数量</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <View style={[CommonStyle.inputTextStyleTextStyle,, { width: screenWidth - (leftLabWidth + 30) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                            {!this.state.inventoryAmount ? (this.state.inventoryAmount===0?0:"请先选择药品") : this.state.inventoryAmount}
                        </Text>
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>规格</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <View style={[CommonStyle.inputTextStyleTextStyle,, { width: screenWidth - (leftLabWidth + 30) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                            {!this.state.medicineSpec ? "请先选择药品" : this.state.medicineSpec}
                        </Text>
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>单位</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 30) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                            {!this.state.unitName ? "请先选择药品" : this.state.unitName}
                        </Text>
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>零售价格</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <View style={[CommonStyle.inputTextStyleTextStyle,, { width: screenWidth - (leftLabWidth + 30) }]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                            {!this.state.salePrice ? "请先选择药品" : this.state.salePrice}
                        </Text>
                    </View>
                </View>
                
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库总量</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText]}
                            placeholder={'请输入总量'}
                            onChangeText={(text) => {
                                this.setState({ totalAmount: text })
                                if(this.state.salePrice){
                                    this.setState({ moneyAmount: (text * this.state.salePrice *1).toFixed(2) })
                                }
                            }}
                        >
                            {this.state.totalAmount}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>用药频率</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={[styles.inputRightText]}
                            placeholder={'如：1天3次'}
                            onChangeText={(text) => this.setState({ frequency: text })}
                        >
                            {this.state.frequency}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>每次用量</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={[styles.inputRightText]}
                            placeholder={'请输入每次用量'}
                            onChangeText={(text) => this.setState({ everyTimeAmount: text })}
                        >
                            {this.state.everyTimeAmount}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>用法</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={[styles.inputRightText]}
                            placeholder={'请输入用法'}
                            onChangeText={(text) => this.setState({ medicineUsage: text })}
                        >
                            {this.state.medicineUsage}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>金额</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            editable={false}
                            //keyboardType='text'
                            style={[styles.inputRightText]}
                            placeholder={'请选择药品并输入总量'}
                            onChangeText={(text) => this.setState({ moneyAmount: text })}
                        >
                            {this.state.moneyAmount}
                        </TextInput>
                    </View>
                    <View style={styles.btnRowView}>
                     <TouchableOpacity onPress={()=>{
                        if (!this.state.selMedicineId) {
                            WToast.show({data:"请选择药品"});
                            return;
                        }
                        if (!this.state.totalAmount || this.state.totalAmount === "0") {
                            WToast.show({data:"请输入总量"});
                            return;
                        }
                        if (this.state.totalAmount > this.state.inventoryAmount) {
                            WToast.show({data:"库存数量不足，请重新输入总量"});
                            return;
                        }
                        if (!this.state.everyTimeAmount) {
                            WToast.show({data:"请输入每次用量"});
                            return;
                        }
                        if (!this.state.frequency) {
                            WToast.show({data:"请输入频率"});
                            return;
                        }
                        var storageOutDetailDTO = {
                            _index:this.state.storageOutDetailList.length,
                            medicineId: this.state.selMedicineId,
                            medicineName: this.state.selMedicineName,
                            salePrice: this.state.salePrice,
                            unitName: this.state.unitName,
                            medicineSpec:this.state.medicineSpec,
                            totalAmount:this.state.totalAmount,
                            everyTimeAmount: this.state.everyTimeAmount,
                            frequency: this.state.frequency,
                            medicineUsage: this.state.medicineUsage,
                            moneyAmount: this.state.moneyAmount,
                        }
                        var _hlStorageOutDetailDTOList = this.state.storageOutDetailList;
                        _hlStorageOutDetailDTOList = _hlStorageOutDetailDTOList.concat(storageOutDetailDTO);
                        this.setState({
                            storageOutDetailList:_hlStorageOutDetailDTOList
                        })
                        this.setState({
                            selMedicineId:"",
                            selMedicineName:"",
                            inventoryAmount:"",
                            medicineSpec:"",
                            unitName:"",
                            salePrice:"",
                            totalAmount:"",
                            everyTimeAmount:"",
                            frequency:"",
                            medicineUsage:"",
                            moneyAmount:"",
                            
                        })
                     }}>
                         <View style={{marginRight:screenWidth/8}}>
                         {/* <View style={[styles.btnAddView]}> */}
                             {/* <Text style={styles.btnAddText}>新增</Text> */}
                             <Image style={{ width:25, height:25,justifyContent:'center'}} source={require('../../assets/icon/iconfont/add1.png')}></Image>
                        </View>
                     </TouchableOpacity>
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View>
                    <FlatList 
                    data={this.state.storageOutDetailList}
                    renderItem={({item}) => 
                    <View key={item._index} style={styles.titleViewStyle}>
                        <View style={{ marginTop:10}}>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                名称：{item.medicineName}
                            </Text>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                规格：{item.medicineSpec}
                            </Text>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                单位：{item.unitName}
                            </Text>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                零售价格：{item.salePrice}
                            </Text>
                        </View>
                        <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10,marginTop:10}]}>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>出库总量：{item.totalAmount}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>用药频率：{item.frequency}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>每次用量：{item.everyTimeAmount}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>用法：{item.medicineUsage?item.medicineUsage:"无"}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>金额：{item.moneyAmount}</Text>
                            </View>
                        </View>
                        <TouchableOpacity
                            style={{marginLeft:-145,zIndex:1000,marginTop:130}}
                            onPress={() => {
                                console.log("========deleteStorageInDetailDTO")
                                var urls = this.state.storageOutDetailList;
                                urls.splice(item._index,1);                   
                                console.log(urls)
                                this.setState({
                                    storageOutDetailList:urls,
                                })
                            }}
                        >
                            <View style={[styles.btnDeleteView]}>
                                <Text style={styles.btnDeleteText}>-  删除</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    }
                    />
                </View>
                </ScrollView>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    searchInputText: {
        width: screenWidth -100,
        // borderColor: '#000000',
        // borderBottomWidth: 1,
        // marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        // marginLeft: 0,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabViewImage: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        borderWidth:1,
        borderColor:"#E4E4E4",
        borderRadius:5,
        marginTop:5,
        // marginRight:5
    },   
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', width:100, alignItems:'center', alignContent:'flex-end', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:25, borderColor:'#999999', 
        borderWidth:1,paddingLeft:10, paddingRight:10, marginRight:15,
         justifyContent:'center',borderRadius:3,backgroundColor:'rgba(255,0,0,0)'
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})