import React, {Component} from 'react';
import {
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class RoleUserAdd extends Component {
  constructor() {
    super();
    this.state = {
      userId: '',
      userName: '',
      userCode: '',
      roleId: '',
      salePersonName: '',
      salePersonTel: '',
      enterpriseName: '',
      operate: '',
      isContainM: false,
    };
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');

    var menuTypes = constants.loginUser.tenantExtAttrJSON.menuTypes;
    console.log('===menuTypes:', menuTypes);
    var menuTypeList = menuTypes.split(',');
    console.log('===menuTypeList:', menuTypeList);
    this.setState({
      isContainM: menuTypeList.includes('M'),
    });
    let loadTypeUrl;
    let loadRequest;
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {userId} = route.params;
      if (userId) {
        console.log('========Edit==userId:', userId);
        this.setState({
          userId: userId,
          operate: '编辑',
        });
        loadTypeUrl = '/biz/role/get_role_user';
        loadRequest = {userId: userId};
        httpPost(loadTypeUrl, loadRequest, this.loadEditRoleUserDataCallBack);
      } else {
        this.setState({
          operate: '添加',
        });
      }
      const {roleId} = route.params;
      if (roleId) {
        console.log('========Edit==user==roleId:', roleId);
        this.setState({
          roleId: roleId,
        });
      }
    }
  }
  loadEditRoleUserDataCallBack = (response) => {
    if (response.code == 200 && response.data) {
      this.setState({
        userName: response.data.userName,
        userCode: response.data.userCode,
        salePersonName: response.data.salePersonName,
        salePersonTel: response.data.salePersonTel,
        enterpriseName: response.data.enterpriseName,
      });
    }
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/back.png')}></Image>
      </TouchableOpacity>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('RoleUserList');
        }}>
        <Text style={CommonStyle.headRightText}>员工管理</Text>
      </TouchableOpacity>
    );
  }

  emptyComponent() {
    return <EmptyRowViewComponent />;
  }

  saveRoleUser = () => {
    console.log('=======saveRoleUser');
    let toastOpts;
    if (!this.state.userName) {
      toastOpts = getFailToastOpts('请输入员工姓名');
      WToast.show(toastOpts);
      return;
    }
    if (!this.state.userCode) {
      toastOpts = getFailToastOpts('请输入员工手机号');
      WToast.show(toastOpts);
      return;
    }
    let url = '/biz/role/role_user_add';
    if (this.state.userId) {
      console.log('=========Edit===userId', this.state.userId);
      url = '/biz/role/role_user_modify';
    }
    let requestParams = {
      userId: this.state.userId,
      userName: this.state.userName,
      userCode: this.state.userCode,
      salePersonName: this.state.salePersonName,
      salePersonTel: this.state.salePersonTel,
      enterpriseName: this.state.enterpriseName,
      roleId: this.state.roleId,
    };
    httpPost(url, requestParams, this.saveRoleUserCallBack);
  };

  // 保存回调函数
  saveRoleUserCallBack = (response) => {
    let toastOpts;
    switch (response.code) {
      case 200:
        if (this.props.route.params.refresh) {
          this.props.route.params.refresh();
        }
        toastOpts = getSuccessToastOpts('保存完成');
        WToast.show(toastOpts);
        this.props.navigation.goBack();
        break;
      default:
        toastOpts = getFailToastOpts(response.message);
        WToast.show({data: response.message});
    }
  };
  render() {
    return (
      <View>
        <CommonHeadScreen
          title={this.state.operate + '员工'}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <ScrollView style={CommonStyle.contentViewStyle}>
          <View style={styles.inputRowStyle}>
            <View style={styles.leftLabView}>
              <Text style={styles.leftLabNameTextStyle}>员工姓名</Text>
              <Text style={styles.leftLabRedTextStyle}>*</Text>
            </View>
            <TextInput
              style={styles.inputRightText}
              placeholder={'请输入员工姓名'}
              onChangeText={(text) => this.setState({userName: text})}>
              {this.state.userName}
            </TextInput>
          </View>
          <View style={styles.inputRowStyle}>
            <View style={styles.leftLabView}>
              <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
              <Text style={styles.leftLabRedTextStyle}>*</Text>
            </View>
            <TextInput
              keyboardType="numeric"
              style={styles.inputRightText}
              placeholder={'请输入联系电话'}
              onChangeText={(text) => this.setState({userCode: text})}>
              {this.state.userCode}
            </TextInput>
          </View>
          <View style={CommonStyle.addItemSplitRowView}>
            <Text style={CommonStyle.addItemSplitRowText}>接口人信息</Text>
          </View>

          <View style={styles.inputRowStyle}>
            <View style={styles.leftLabView}>
              <Text style={styles.leftLabNameTextStyle}>接口人</Text>
              {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
            </View>
            <TextInput
              style={styles.inputRightText}
              placeholder={'请输入接口人'}
              onChangeText={(text) => this.setState({salePersonName: text})}>
              {this.state.salePersonName}
            </TextInput>
          </View>
          <View style={styles.inputRowStyle}>
            <View style={styles.leftLabView}>
              <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
              {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
            </View>
            <TextInput
              keyboardType="numeric"
              style={styles.inputRightText}
              placeholder={'请输入联系电话'}
              onChangeText={(text) => this.setState({salePersonTel: text})}>
              {this.state.salePersonTel}
            </TextInput>
          </View>

          {this.state.isContainM ? (
            <View style={styles.inputRowStyle}>
              <View style={styles.leftLabView}>
                <Text style={styles.leftLabNameTextStyle}>企业名称</Text>
                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
              </View>
              <TextInput
                style={styles.inputRightText}
                placeholder={'请输入企业名称'}
                onChangeText={(text) => this.setState({enterpriseName: text})}>
                {this.state.enterpriseName}
              </TextInput>
            </View>
          ) : null}

          <View style={CommonStyle.btnRowStyle}>
            <TouchableOpacity
              onPress={() => {
                this.props.navigation.goBack();
              }}>
              <View
                style={[
                  CommonStyle.btnRowLeftCancelBtnView,
                  {
                    flexDirection: 'row',
                    width: 130,
                    height: 40,
                    marginLeft: 35,
                    marginTop: 15,
                  },
                ]}>
                <Image
                  style={{width: 25, height: 25, marginRight: 15}}
                  source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity onPress={this.saveRoleUser.bind(this)}>
              <View
                style={[
                  CommonStyle.btnRowRightSaveBtnView,
                  {flexDirection: 'row'},
                ]}>
                <Image
                  style={{width: 25, height: 25, marginRight: 15}}
                  source={require('../../assets/icon/iconfont/save.png')}></Image>
                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
              </View>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    );
  }
}

let styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 140,
  //     backgroundColor:'#FFFFFF'
  // },
  itemViewStyle: {
    margin: 10,
    padding: 15,
    borderRadius: 2,
    backgroundColor: '#FFFFFF',
  },
  selectedItemViewStyle: {
    margin: 10,
    padding: 15,
    borderRadius: 2,
    backgroundColor: '#CB4139',
  },
  itemTextStyle: {
    color: '#000000',
  },
  selectedItemTextStyle: {
    color: '#FFFFFF',
  },
  inputRowStyle: {
    height: 45,
    flexDirection: 'row',
    marginTop: 10,
    // flex: 1,
    // justifyContent: 'space-between',
    // alignContent:'center'
    // backgroundColor:'#000FFF',
    // width:screenWidth,
    // alignContent:'space-between',
    // justifyContent:'center'
  },

  rowLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
    // alignContent:'flex-start',
    // justifyContent:'center',
    // backgroundColor:'yellow',
  },
  leftLabView: {
    width: leftLabWidth,
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
    // alignContent:'flex-start',
    // justifyContent:'center',
    // backgroundColor:'yellow',
  },
  leftLabNameTextStyle: {
    fontSize: 18,
    // color:'red',
    // borderColor:'#000',
    // borderWidth:1,
    // justifyContent:'center',
    // alignContent:'center',
    // backgroundColor:'yellow',
  },
  leftLabRedTextStyle: {
    color: 'red',
    marginLeft: 5,
    marginRight: 5,
  },
  inputRightText: {
    width: screenWidth - (leftLabWidth + 5),
    borderRadius: 5,
    borderColor: '#F1F1F1',
    borderWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 15,
    paddingLeft: 10,
    paddingRight: 10,
  },
});
