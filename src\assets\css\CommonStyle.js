'use strict';

var React = require('react-native');
const {
  fullScreenIfIphoneXContentViewHeight,
  ifIphoneXContentViewHeight,
  ifIphoneXBodyViewHeight,
  isIphoneX,
  ifIphoneXHeaderHeight,
} = require('../../utils/ScreenUtil');
var {StyleSheet, Dimensions, Platform} = React;
import {isHaveSpiritIsland} from '../../utils/ScreenUtil';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 70;

module.exports = StyleSheet.create({
  // 带滚动页面的调试
  fullScreenContentViewStyle: {
    // height:Platform.OS === 'ios' ? screenHeight - 110 : screenHeight - 140,
    backgroundColor: '#F5F5F5',
    height: fullScreenIfIphoneXContentViewHeight(),
  },
  // 带滚动页面的调试
  contentViewStyle: {
    // height:Platform.OS === 'ios' ? screenHeight - 110 : screenHeight - 140,
    backgroundColor: '#FFFFFF',
    height: ifIphoneXContentViewHeight(),
  },
  // 带滚动页面的调试
  contentNewViewStyle: {
    // height:Platform.OS === 'ios' ? screenHeight - 110 : screenHeight - 140,
    backgroundColor: 'rgba(240, 240, 240, 1)',
    height: ifIphoneXContentViewHeight(),
  },
  // 表单页面整体内容样式
  formContentViewStyle: {
    // height:Platform.OS === 'ios' ? screenHeight - 110 : screenHeight - 140,
    backgroundColor: '#FFFFFF',
    height: ifIphoneXContentViewHeight() + ifIphoneXHeaderHeight(),
    marginBottom: 10,
  },
  // 带滚动页面的调试
  onlyContainHeaderContentViewStyle: {
    // height:Platform.OS === 'ios' ? screenHeight - 110 : screenHeight - 140,
    backgroundColor: '#FFFFFF',
    height: ifIphoneXBodyViewHeight(),
  },

  // modal全屏遮挡
  fullScreenKeepOut: {
    height: screenHeight,
    width: screenWidth,
    backgroundColor: 'rgba(169,169,169,0.95)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // modal全屏遮挡 -- 内容样式
  modalContentViewStyle: {
    height: ifIphoneXContentViewHeight(),
    width: screenWidth - 30,
    backgroundColor: '#FFFFFF',
    padding: 10,
    borderRadius: 5,
  },
  modalSearchInputText: {
    width: screenWidth - 200,
    borderColor: '#000000',
    borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 18,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
  },
  modalSearchBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 1,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    borderRadius: 4,
  },
  modalSearchBtnTextStyle: {
    color: '#000000',
    fontSize: 16,
  },

  // 主体文字大小
  bodyViewStyle: {
    padding: 5,
  },
  // 主体文字大小
  bodyTextStyle: {
    fontSize: 16,
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  fontSize18: {
    fontSize: 18,
  },
  boldTextStyle: {
    fontWeight: 'bold',
  },
  alignCenterStyle: {
    alignSelf: 'center',
    alignItems: 'center',
    alignContent: 'center',
  },

  // 隐藏-无用
  hiddenViewStyle: {
    width: 0,
    height: 0,
    display: 'none',
  },
  // 置灰，不可用
  disableViewStyle: {
    opacity: 0.3,
  },
  // 页面顶部悬浮按钮容器
  rightAbsoluteButtonContainer: {
    position: 'absolute',
    width: 110,
    right: 15,
    top: isHaveSpiritIsland(screenHeight) ? 100 : isIphoneX() ? 75 : 50,
    borderRadius: 50,
    opacity: 0.85,
    zIndex: 100,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  // 单个按钮容器
  rightAbsoluteButtonView: {
    marginTop: 10,
    height: 32,
    width: 110,
    opacity: 1,
    borderRadius: 8,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(242, 245, 252, 1)',
  },
  // 含图标和文字的容器
  rightAbsoluteButtonBoxView: {
    width: 70,
    backgroundColor: 'rgba(242, 245, 252, 1)',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  // 悬浮按钮图标样式
  rightAbsoluteButtonIconView: {
    width: 20,
    height: 20,
    marginRight: 5,
    tintColor: 'rgba(0,10,32,0.85)',
  },
  // 悬浮按钮图标样式
  rightAbsoluteButtonTextView: {
    color: 'rgba(0,10,32,0.85)',
    fontSize: 14,
  },
  rightTop50FloatingBlockView: {
    width: 50,
    height: 50,
    zIndex: 100,
    backgroundColor: 'green',
    position: 'absolute',
    right: 15,
    top: isIphoneX() ? 75 : 50,
    borderRadius: 50,
    opacity: 0.6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rightTop50FloatingBlockText: {
    color: '#FFF',
  },
  rightTop50SecondFloatingBlockView: {
    width: 50,
    height: 50,
    zIndex: 100,
    backgroundColor: 'green',
    position: 'absolute',
    right: 15,
    top: isIphoneX() ? 127 : 102,
    borderRadius: 50,
    opacity: 0.6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rightTop50ThirdFloatingBlockView: {
    width: 50,
    height: 50,
    zIndex: 100,
    backgroundColor: 'green',
    position: 'absolute',
    right: 15,
    top: isIphoneX() ? 180 : 155,
    borderRadius: 50,
    opacity: 0.6,
    alignItems: 'center',
    justifyContent: 'center',
  },

  // 列表项按钮样式
  itemBottomBtnStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  // 置闲
  itemBottomFreeBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 0,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: 'green',
    borderRadius: 4,
  },
  itemBottomFreeBtnTextStyle: {
    color: '#FFFFFF',
    fontSize: 16,
  },

  // 故障
  itemBottomBreakdownBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 1,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: 'yellow',
    borderRadius: 4,
  },
  itemBottomBreakdownBtnTextStyle: {
    color: '#000000',
    fontSize: 16,
  },
  // 查看详情
  itemBottomDetailBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 0,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: 'green',
    borderRadius: 6,
  },
  itemBottomDetailBtnTextStyle: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  itemBottomDeleteBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 1,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    borderRadius: 6,
  },
  itemBottomDeleteGreyBtnViewStyle: {
    fontSize: 16,
    width: 80,
    height: 28,
    flexDirection: 'row',
    justifyContent: 'center',
    borderWidth: 0.85,
    alignItems: 'center',
    margin: 10,
    marginRight: 0,
    borderRadius: 6,
    borderColor: 'rgba(145, 147, 152, 1)',
    // flexWrap: 'wrap'
  },
  itemBottomDeleteBtnTextStyle: {
    color: '#000000',
    fontSize: 16,
  },
  itemBottomEditBtnViewStyle: {
    fontSize: 222,
    width: 100,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: '#CB4139',
    borderRadius: 4,
  },
  itemBottomEditBlueBtnViewStyle: {
    width: 80,
    height: 28,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    marginRight: 0,
    backgroundColor: '#1E6EFA',
    borderRadius: 6,
  },
  itemAgreeBtnViewStyle: {
    fontSize: 222,
    width: 100,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: '#ffffff',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#3ab240',
  },
  itemRejectBtnViewStyle: {
    fontSize: 222,
    width: 100,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: '#ffffff',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#CB4139',
  },

  itemBottomEditBtnTextStyle: {
    color: '#F0F0F0',
    fontSize: 16,
  },

  naicaiIndexHeadLeftText: {
    color: '#000000',
    fontSize: 16,
  },
  // 导航左侧字体样式
  headLeftText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  // 导航右侧字体样式
  // headRightText:{
  //     color:'#FFFFFF',
  //     fontSize:16,
  // },
  headRightText: {
    // color:'rgb(180,180,180)',
    color: '#33333375',
    fontSize: 15,
  },

  // 一行放两个按钮【取消、保存】
  btnRowStyle: {
    flexDirection: 'row',
    margin: 10,
    justifyContent: 'space-between',
  },
  btnRowLeftCancelBtnView: {
    backgroundColor: '#FFFFFF',
    // justifyContent:'flex-end',
    // alignContent:'flex-end',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#A0A0A0',
    borderRadius: 50,
    flexDirection: 'row',
    width: 170,
    height: 45,
    marginLeft: 5,
    marginTop: 15,
  },
  btnRowLeftCancelBtnText: {
    fontSize: 18,
    color: '#000A20',
  },
  btnRowRightSaveBtnView: {
    backgroundColor: '#1E6EFA',
    alignItems: 'center',
    // alignContent:'center',
    justifyContent: 'center',
    borderRadius: 50,
    flexDirection: 'row',
    width: 170,
    height: 45,
    marginRight: 5,
    marginTop: 15,
  },
  image: {
    width: 25,
    height: 25,
    marginRight: 15,
  },
  btnRowRightSaveBtnText: {
    fontSize: 18,
    color: '#FFFFFF',
  },
  //Add分割线样式
  viewAddLineStyle: {
    borderBottomWidth: 1,
    borderBottomColor: '#F1F1F1',
    width: '100%',
    marginTop: 0,
    marginLeft: 15,
    marginTop: 10,
  },
  //AddList的左右顶部页面样式
  viewAddLeftViewStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 70,
  },
  btnAddLeftBtn: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  btnAddLeftBtnView: {
    width: 22,
    height: 22,
    marginVertical: 2,
    tintColor: '#3C6CDE',
  },
  btnAddLeftBtnText: {
    color: '#3C6CDE',
    fontWeight: 'bold',
  },
  btnAddRightBtnText: {
    color: '#FFFFFF',
  },
  viewAddRightViewStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 70,
  },
  viewListLeftViewStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 70,
  },
  btnListLeftBtn: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  btnListLeftBtnImage: {
    width: 22,
    height: 22,
    marginVertical: 2,
    tintColor: '#3C6CDE',
  },
  btnListLeftBtnText: {
    color: '#3C6CDE',
    fontWeight: 'bold',
  },
  viewListRightViewStyle: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    width: 70,
  },
  btnListRightBtnImage: {
    width: 22,
    height: 22,
    marginVertical: 2,
  },
  //2个按钮的编辑删除通用样式
  blockTwoEditDelStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    flexWrap: 'wrap',
  },
  btnTwoEditBtnView: {
    width: 64,
    height: 28,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    borderRadius: 6,
    // fontSize: 16,
    // width:80,
    // borderWidth: 1,
    // marginRight:0,
    // borderColor: '#1E6EFA',
    backgroundColor: '#1E6EFA',
    // width:80,
  },
  btnTwoEditBtnImage: {
    width: 17,
    height: 17,
    marginRight: 3,
  },
  btnTwoEditBtnText: {
    color: '#F0F0F0',
    fontSize: 14,
    lineHeight: 20,
  },
  btnTwoDeleteBtnView: {
    fontSize: 16,
    // width: 100,
    height: 28,
    borderWidth: 1,
    borderColor: 'rgba(145,147,152,1)',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    borderRadius: 6,
    borderWidth: 0.85,
    width: 64,
    marginRight: -2,
    flexDirection: 'row',
  },
  btnTwoDeleteBtnImage: {
    width: 17,
    height: 17,
    marginRight: 3,
    tintColor: 'rgba(145, 147, 152, 1)',
  },
  btnTwoDeleteBtnText: {
    fontSize: 14,
    lineHeight: 20,
    color: 'rgba(145, 147, 152, 1)',
  },

  //大于2个按钮的隐藏编辑删除的通用样式
  // modal编辑删除全屏遮挡
  btnHiddenEditDeleteStyle: {
    position: 'absolute',
    right: 13,
    top: 0,
  },
  btnHiddenEditDeleteView: {
    width: 35,
    height: 35,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnHiddenEditDeleteText: {
    width: 28,
    height: 28,
  },
  fullScreenKeepOutEditDelete: {
    height: screenHeight,
    width: screenWidth,
    // backgroundColor: 'rgba(169,169,169,0.95)',
    backgroundColor: 'rgba(0,0,0,0.64)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  blockEditDeleteChose: {
    width: 291,
    bottom: screenHeight / 2 - 80,
    position: 'absolute',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
  },
  btnEditDeleteBtnView: {
    width: 145,
    height: 50,
    paddingLeft: 30,
    marginTop: 5,
  },
  btnEditDeleteBtnText: {
    color: 'rgba(0, 10, 32, 0.85)',
    fontSize: 18,
    lineHeight: 52,
  },
  btnBorderCancelBtnView: {
    width: 291,
    height: 50,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    marginTop: 10,
    borderTopWidth: 1,
    borderColor: '#DFE3E8',
  },
  btnCancelBtnView: {
    width: 105,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnCancelBtnText: {
    fontSize: 17,
    fontFamily: 'PingFangSC',
    fontWeight: '400',
    color: '#1E6EFA',
  },
  //删除之后的确认删除通用样式
  blockConfirmDeleteView: {
    width: 292,
    height: 156,
    bottom: screenHeight / 2 - 80,
    position: 'absolute',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
  },
  WarnConfirmDeleteView: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  WarnConfirmDeleteText: {
    fontSize: 18,
  },
  WarnDeleteView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 24,
  },
  WarnDeleteText: {
    fontSize: 14,
    color: 'rgba(0,10,32,0.64)',
  },
  blockCancelDeleteView: {
    flexDirection: 'row',
    width: 292,
    height: 56,
    marginTop: 15,
    borderTopWidth: 1,
    borderColor: '#DFE3E8',
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnInsideCancelBtnView: {
    width: 146,
    height: 56,
    alignItems: 'center',
    justifyContent: 'center',
    borderRightWidth: 1,
    borderColor: '#DFE3E8',
  },
  btnInsideCancelBtnText: {
    fontSize: 17,
    fontFamily: 'PingFangSC',
    fontWeight: '400',
    color: '#000A20',
  },
  btnInsideDeleteBtnView: {
    width: 146,
    height: 56,
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnInsideDeleteBtnText: {
    fontSize: 17,
    fontFamily: 'PingFangSC',
    fontWeight: '400',
    color: '#1E6EFA',
  },

  //add界面的确定取消按钮通用样式
  blockAddCancelSaveStyle: {
    flexDirection: 'row',
    marginBottom: 10,
    marginTop: 10,
    justifyContent: 'space-between',
    width: screenWidth,
    backgroundColor: 'rgba(255, 255, 255, 1)',
    height: 66,
  },
  btnAddCancelBtnView: {
    backgroundColor: '#FFFFFF',
    // justifyContent:'flex-end',
    // alignContent:'flex-end',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#A0A0A0',
    borderRadius: 4,
    flexDirection: 'row',
    width: 170,
    height: 45,
    marginLeft: 5,
    marginTop: 15,
    marginLeft: 20,
    // marginTop:8,
    width: (screenWidth - 56) / 2,
  },
  btnAddSaveBtnView: {
    backgroundColor: '#255BDA',
    // backgroundColor: '#1E6EFA',
    alignItems: 'center',
    // alignContent:'center',
    justifyContent: 'center',
    // borderWidth: 1,
    borderRadius: 4,
    flexDirection: 'row',
    width: 170,
    height: 45,
    marginRight: 5,
    marginTop: 15,
    marginRight: 20,
    // marginTop:8,
    width: (screenWidth - 56) / 2,
  },

  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginLeft:10,
    marginLeft: 40,
    marginRight: 10,
    marginBottom: 2,
    marginTop: 2,
  },
  titleTextStyle: {
    width: 220,
    height: 24,
    // fontFamily: 'PingFangSC',
    fontWeight: '400',
    fontSize: 14,
    // color: 'rgba(0,10,32,0.65)',
    lineHeight: 24,
    textAlign: 'left',
    fontStyle: 'normal',
  },
  // 解决由于高度影响到了换行
  newTitleViewStyle: {
    flexDirection: 'row',
    //justifyContent:'space-between',
    // marginLeft:10,
    marginLeft: 40,
    marginRight: 0,
    marginBottom: 2,
    marginTop: 2,
  },
  newTitleTextStyle: {
    width: 220,
    // fontFamily: 'PingFangSC',
    fontWeight: '400',
    fontSize: 14,
    // color: 'rgba(0,10,32,0.65)',
    lineHeight: 24,
    textAlign: 'left',
    fontStyle: 'normal',
  },

  // List里的RenderRow中列表
  titleViewStyleSpecial: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginLeft:10,
    marginLeft: 40,
    marginRight: 10,
    marginBottom: 10,
    marginTop: 8,
  },
  titleTextStyleSpecial: {
    width: 400,
    //height: 24,
    // fontFamily: 'PingFangSC',
    fontWeight: 'bold',
    fontSize: 20,
    color: '#404956',
    //lineHeight: 24,
    textAlign: 'left',
    fontStyle: 'normal',
  },
  //List里的跟着renderRow的滑动头部分割线
  lineListHeadRenderRowStyle: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 10,
    borderBottomColor: '#F4F7F9',
  },
  //add里头部与renderrow的分割线
  lineHeadBorderStyle: {
    borderBottomWidth: 1,
    borderBottomColor: '#F1F1F1',
    width: '100%',
    marginTop: -2,
  },
  //add里的输入框下边线
  lineBorderBottomStyle: {
    borderBottomWidth: 1,
    borderBottomColor: '#F1F1F1',
    width: '100%',
    // marginTop: 4,
    marginLeft: 16,
  },

  // 分隔样式，垂直居中靠左对齐，占整行
  addItemSplitRowView: {
    width: screenWidth,
    height: 50,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: '#F6F9FA',
    marginTop: 10,
  },
  addItemSplitRowViewDetail: {
    width: screenWidth,
    height: 30,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    // backgroundColor: '#F6F9FA',
    marginTop: 10,
  },
  addItemSplitRowTextDetail: {
    fontWeight: 'bold',
    color: 'rgba(0, 10, 32, 0.85)',
    fontSize: 16,
    marginLeft: 8,
    lineHeight: 20,
  },
  addItemSplitRowText: {
    color: '#999999',
    fontSize: 20,
    paddingLeft: 10,
  },

  blockSplitViewStyle: {
    backgroundColor: '#F6F9FA',
    height: 20,
  },

  rowSplitViewStyle: {
    borderBottomWidth: 1,
    borderBottomColor: '#D9D9D9',
    height: 0,
    marginTop: 5,
  },
  rowAddSegmentLineStyle: {
    borderBottomWidth: 1,
    borderBottomColor: '#F1F1F1',
    width: '100%',
    marginTop: 4,
    marginLeft: 15,
    // marginBottom:10,
  },

  // 分段器样式
  blockItemViewStyle: {
    margin: 5,
    paddingTop: 5,
    paddingBottom: 5,
    paddingLeft: 10,
    paddingRight: 10,
    // height:35,
    justifyContent: 'center',
    borderRadius: 2,
    backgroundColor: '#FFFFFF',
  },
  selectedBlockItemViewStyle: {
    margin: 5,
    paddingTop: 5,
    paddingBottom: 5,
    paddingLeft: 10,
    paddingRight: 10,
    // height:35,
    justifyContent: 'center',
    borderRadius: 2,
    backgroundColor: '#1E6EFA',
  },
  blockItemTextStyle: {
    color: '#000000',
    fontSize: 18,
  },
  selectedBlockItemTextStyle: {
    color: '#FFFFFF',
    fontSize: 18,
  },
  blockItemTextStyle16: {
    color: '#000000',
    fontSize: 15,
  },
  selectedBlockItemTextStyle16: {
    color: '#FFFFFF',
    fontSize: 15,
  },

  // 添加页面-占整行
  rowLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  rowLabTextStyle: {
    fontSize: 18,
  },
  rowLabLeftView: {
    width: leftLabWidth,
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  rowLabRedTextStyle: {
    color: 'red',
    marginLeft: 5,
    marginRight: 5,
  },
  rowLabWhiteTextStyle: {
    color: '#FFFFFF',
    marginLeft: 5,
    marginRight: 5,
  },
  rowRightTextInput: {
    width: screenWidth - (leftLabWidth + 15),
    borderRadius: 5,
    borderColor: '#F1F1F1',
    borderWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 15,
    height: 45,
    paddingLeft: 10,
    paddingRight: 10,
  },
  // 假装是输入框的Text样式
  inputTextStyleTextStyle: {
    width: screenWidth - (leftLabWidth + 5),
    borderRadius: 5,
    borderColor: '#F1F1F1',
    borderWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 15,
    paddingLeft: 10,
    paddingRight: 10,
    height: 45,
    justifyContent: 'center',
  },
  // 假装是输入框的Text样式--不指定宽度
  inputTextStyleTextStyleNoWidth: {
    borderRadius: 5,
    borderColor: '#F1F1F1',
    borderWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 15,
    paddingLeft: 10,
    paddingRight: 10,
    height: 45,
    justifyContent: 'center',
  },
  // 输入框占整行
  inputRowText: {
    width: screenWidth - 10,
    borderRadius: 5,
    borderColor: '#F1F1F1',
    borderWidth: 1,
    marginRight: 5,
    marginLeft: 5,
    color: '#A0A0A0',
    fontSize: 15,
    paddingLeft: 10,
    paddingRight: 10,
  },

  // 搜索框
  // 假装是输入框的Text样式
  inputTextStyleViewStyle: {
    borderRadius: 5,
    borderColor: '#F1F1F1',
    borderWidth: 1,
    marginRight: 5,
    paddingLeft: 10,
    paddingRight: 10,
    height: 45,
    justifyContent: 'center',
  },
  // 重置
  resetBtnViewStyle: {
    fontSize: 16,
    height: 30,
    borderWidth: 1,
    borderColor: '#A4A4A4',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    paddingLeft: 10,
    paddingRight: 10,
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
  },
  resetBtntextStyle: {
    color: '#A0A0A0',
    fontSize: 16,
  },
  searchInputText: {
    width: screenWidth / 1.5,
    borderColor: '#000000',
    borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 18,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
  },

  selectViewItem: {
    width: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectTextItem: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  //renderRow最外层样式
  innerViewStyle: {
    borderColor: 'rgba(242, 245, 252, 1)',
    borderBottomWidth: 8,
    borderTopWidth: 8,
  },
  //列表页面头部操作区最外层样式
  headViewStyle: {
    borderColor: '#ffffff',
    borderWidth: 8,
    backgroundColor: '#ffffff',
  },

  // Tab分类样式
  tabItemViewStyle: {
    margin: 5,
    width: 65,
    borderRadius: 0,
    paddingTop: 2,
    paddingBottom: 0,
    paddingLeft: 2,
    paddingRight: 2,
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
  },
  selectedtabItemTextStyle: {
    color: 'rgba(0, 10, 2, 0.8)',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  tabItemTextStyle: {
    color: 'rgba(0, 10, 2, 0.45)',
    fontSize: 14,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  //搜索框和导出按钮同一行时两者的样式布局
  searchBoxAndExport: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 12,
    height: 34,
    paddingRight: 16,
  },
  //同一行有导出按钮的搜索时间框的样式
  searchTimeBoxWithExport: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    margin: 0,
    marginTop: 0,
    marginLeft: 0,
    width: screenWidth / 1.4,
    backgroundColor: '#F2F5FC',
    // backgroundColor: '#4169E1',
    paddingLeft: 5,
    borderWidth: 0,
    borderColor: '#F2F5FC',
    height: 34,
    borderRadius: 15,
  },
  //选项搜索样式
  heightLimited: {
    maxHeight: screenWidth - 68,
  },
  choseToSearchViewStyle: {
    margin: 5,
    paddingTop: 5,
    paddingBottom: 5,
    paddingLeft: 10,
    paddingRight: 10,
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    alignItems: 'center',
    flexDirection: 'row',
  },
  choseToSearchClosedTextStyle: {
    color: '#666666',
    paddingRight: 10,
    fontSize: 16,
  },
  choseToSearchOpenedTextStyle: {
    color: 'rgba(30,110,250,1)',
    paddingRight: 10,
    fontSize: 16,
  },
  choseToSearchClosedIconSize: {
    width: 10,
    height: 10,
  },
  choseToSearchOpenedIconSize: {
    width: 10,
    height: 10,
  },
  choseToSearchBigBoxViewStyle: {
    position: 'absolute',
    backgroundColor: 'rgba(169,169,169,0.5)',
    width: screenWidth,
    zIndex: 101,
    right: 0,
    left: 0,
    // height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)
  },
  choseToSearchSmallBoxViewStyle: {
    //内边距
    paddingTop: 13,
    paddingBottom: 7,
    paddingLeft: 10,
    paddingRight: 10,
    width: screenWidth,
    flexWrap: 'wrap',
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,1)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,10,32,0.15)',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,10,32,0.15)',
  },
  choseToSearchItemsViewSize: {
    //外边距
    marginLeft: 6,
    marginRight: 6,
    marginTop: 8,
    marginBottom: 0,
    //内边距
    paddingTop: 5,
    paddingBottom: 5,
    paddingLeft: 15,
    paddingRight: 15,
    borderRadius: 4,
    justifyContent: 'center',
    height: 30,
    borderRadius: 4,
  },
  choseToSearchItemsSelectedViewColor: {
    backgroundColor: 'rgba(255, 255, 255, 0.10)',
    borderWidth: 1,
    borderColor: 'rgba(30, 110, 250, 1)',
  },
  choseToSearchItemsViewColor: {
    backgroundColor: 'rgba(246,246,246,1)',
  },
  choseToSearchItemsSelectedTextStyle: {
    color: 'rgba(30,110,250,1)',
    fontSize: 16,
  },
  choseToSearchItemsTextStyle: {
    color: 'rgba(0,10,32,0.45)',
    fontSize: 16,
  },
  choseToSearchBtnRowStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    // borderWidth: 9,
    borderColor: '#ffffff',
    backgroundColor: '#ffffff',
    height: 68,
    flexDirection: 'row',
  },
  choseToSearchBtnCanleViewStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    width: screenWidth / 2 - 60,
    height: 40,
    marginRight: 20,
    backgroundColor: 'rgba(0,10,32,0.10)',
    borderRadius: 20,
  },
  choseToSearchBtnOKViewStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    width: screenWidth / 2 - 60,
    height: 40,
    // marginRight: 20,
    backgroundColor: 'rgba(30,110,250,1)',
    borderRadius: 20,
  },

  singleSearchBox: {
    width: '100%',
    // borderColor: '#F2F5FC',
    backgroundColor: '#FFFFFF',
    // backgroundColor: '#1E90FF',
    height: 34,
    flexDirection: 'row',
    paddingLeft: 12,
    paddingRight: 16,
  },
  searchBoxWithoutOthers: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 0,
    marginTop: 0,
    marginLeft: 0,
    width: '100%',
    backgroundColor: '#F2F5FC',
    // backgroundColor: '#4169E1',
    paddingLeft: 5,
    borderWidth: 0,
    borderColor: '#F2F5FC',
    height: 34,
    borderRadius: 15,
  },
  itemBottomStudyGreyBtnViewStyle: {
    width: 92,
    backgroundColor: '#1BBC82',
    fontSize: 16,
    height: 28,
    flexDirection: 'row',
    justifyContent: 'center',
    borderWidth: 0.85,
    borderColor: '#1BBC82',
    alignItems: 'center',
    margin: 10,
    marginRight: 0,
    borderRadius: 6,
  },
  itemBottomProgressGreyBtnViewStyle: {
    width: 64,
    backgroundColor: '#1BBC82',
    fontSize: 16,
    height: 28,
    flexDirection: 'row',
    justifyContent: 'center',
    borderWidth: 0.85,
    borderColor: '#1BBC82',
    alignItems: 'center',
    margin: 10,
    marginRight: 0,
    borderRadius: 6,
  },
  //耐材通用样式
  naicaiTabViewStyle: {
    height: 49,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  naiCaiTabItemTextStyle: {
    color: '#2B333F',
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 49,
    textAlign: 'center',
  },
  selectedNaiCaiTabItemTextStyle: {
    color: '#255BDA',
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 49,
    textAlign: 'center',
    borderColor: '#255BDA',
    borderBottomWidth: 2,
    paddingLeft: 5,
    paddingRight: 5,
  },
  exportModelUpViewStyle: {
    width: 291,
    height: 156,
    bottom: screenHeight / 2 - 80,
    position: 'absolute',
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
  },
  exportModelDownViewStyle: {
    flexDirection: 'row',
    width: 291,
    height: 56,
    marginTop: 15,
    borderTopWidth: 1,
    borderColor: '#DFE3E8',
    alignItems: 'center',
    justifyContent: 'center',
  },
  exportConfirmViewStyle: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  exportPromptViewStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 24,
  },
  exportRefuseViewStyle: {
    width: 145,
    height: 56,
    alignItems: 'center',
    justifyContent: 'center',
  },
  exportOpenViewStyle: {
    width: 145,
    height: 56,
    alignItems: 'center',
    justifyContent: 'center',
    borderLeftWidth: 1,
    borderColor: '#DFE3E8',
  },
  exportConfirmTextStyle: {
    fontSize: 18,
  },
  exportPromptTextStyle: {
    fontSize: 14,
    color: 'rgba(0,10,32,0.65)',
  },
  exportRefuseTextStyle: {
    fontSize: 17,
    fontWeight: '400',
    color: '#000A20',
  },
  exportOpenTextStyle: {
    fontSize: 17,
    fontWeight: '400',
    color: '#1E6EFA',
  },
  blockSelectionViewStyle: {
    margin: 5,
    paddingTop: 5,
    paddingBottom: 5,
    paddingLeft: 10,
    paddingRight: 10,
    // height:35,
    justifyContent: 'center',
    borderRadius: 2,
    backgroundColor: '#FFFFFF',
    padding: 10,
    flexDirection: 'row',
  },
  blockSelectionTextStyle: {
    color: '#000000',
    fontSize: 15,
    fontWeight: 'bold',
    paddingRight: 10,
    color: 'rgba(0, 10, 2, 0.45)',
  },
  selectedBlockSelectionTextStyle: {
    color: '#000000',
    fontSize: 15,
    fontWeight: 'bold',
    color: 'rgba(30,110,250,1)',
    paddingRight: 10,
  },
  searchBoxExternalViewStyle: {
    backgroundColor: '#ffffff',
    height: 50,
    marginTop: 8,
    marginBottom: 8,
  },
  searchBoxInternalViewStyle: {
    height: 40,
    width: screenWidth - 40,
    marginTop: 5,
    marginLeft: 20,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#F2F5FC',
    backgroundColor: '#F2F5FC',
    borderRadius: 5,
  },
  searchBoxImageViewStyle: {
    height: 40,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 15,
  },
  dropDownSelectionViewStyle: {
    fontSize: 16,
    width: 100,
    borderWidth: 0,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: 'green',
    borderRadius: 6,
    marginRight: 0,
    flexDirection: 'row',
    top: screenHeight / 7.5,
    height: 35,
    zIndex: 100,
    position: 'absolute',
    right: 15,
    opacity: 0.6,
  },
  dropDownSelectionTextStyle: {
    color: '#FFF',
    fontSize: 16,
  },
  timeDropDownSelectionViewStyle: {
    zIndex: 100,
    backgroundColor: 'green',
    position: 'absolute',
    right: 15,
    borderRadius: 50,
    opacity: 0.6,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 3,
    width: null,
    height: 40,
    top: screenHeight / 15,
    marginTop: 10,
    paddingLeft: 15,
    paddingRight: 15,
  },
  timeDropDownSelectionTextStyle: {
    color: '#FFF',
  },
});
