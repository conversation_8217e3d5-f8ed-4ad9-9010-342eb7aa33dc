import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class ExpenditureContractReport extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      searchKeyWord: '',
    };
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {tenantId} = route.params;
      if (tenantId) {
        console.log('=============tenantId' + tenantId + '');
      }
    }
    this.loadOutsourcingProcessList();
  }

  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/out/sourcing/progress/findTrack';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchOutsourcingTenantName: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataAll = [...dataNew];
      this.setState({
        // dataSource:list,
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadOutsourcingProcessList();
  };

  loadOutsourcingProcessList = () => {
    let url = '/biz/out/sourcing/progress/findTrack';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      searchOutsourcingTenantName: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this.loadOutsourcingProcessListCallBack);
  };

  loadOutsourcingProcessListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        // dataSource:list,
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
      console.log(dataAll);
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  renderRow = (item, index) => {
    return (
      <View style={[styles.innerViewStyle]}>
        {index == 0 ? (
          <View style={CommonStyle.lineListHeadRenderRowStyle}></View>
        ) : (
          <View></View>
        )}
        <View style={[CommonStyle.titleViewStyleSpecial]}>
          {/* <View style={[styles.titleViewStyle,{height:25, backgroundColor:'rgba(255,0,0,0)',borderRadius:5, justifyContent:'center',alignItems:'center'}]}> */}
          <Text style={[CommonStyle.titleTextStyleSpecial]}>
            {/* <Text style={[styles.titleTextStyle,{fontWeight:'bold',fontSize:18,color:'#237a28'}]}> */}
            {item.outsourcingAbbreviation
              ? item.outsourcingAbbreviation
              : item.outsourcingName}
          </Text>
        </View>
        <View style={[styles.titleViewStyle]}>
          <Text style={[styles.titleTextStyle, {fontWeight: 'bold'}]}>
            项目名称
          </Text>
          <Text style={[styles.titleTextStyle, {fontWeight: 'bold'}]}>
            {' '}
            最近更新时间
          </Text>
        </View>
        <View style={[styles.titleViewStyle]}>
          <Text style={[styles.titleTextStyle]}>成型</Text>
          <Text style={[styles.titleTextStyle]}>
            {item.semiNewestTime ? item.semiNewestTime : '暂无进展'}
          </Text>
        </View>
        <View style={[styles.titleViewStyle]}>
          <Text style={[styles.titleTextStyle]}>入窑</Text>
          <Text style={[styles.titleTextStyle]}>
            {item.encastageNewestTime ? item.encastageNewestTime : '暂无进展'}
          </Text>
        </View>
        <View style={[styles.titleViewStyle, {marginBottom: 10}]}>
          <Text style={[styles.titleTextStyle]}>成品</Text>
          <Text style={[styles.titleTextStyle]}>
            {item.goodsNewestTime ? item.goodsNewestTime : '暂无进展'}
          </Text>
        </View>
      </View>
    );
  };

  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
      //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      // </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[{flexDirection: 'row', alignItems: 'center'}]}>
          {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
          {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={{color: '#3C6CDE', fontWeight: 'bold'}}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity onPress={() => {}}>
          {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
          <Text style={{color: '#FFFFFF'}}>外协报表</Text>
          {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
        </TouchableOpacity>
      </View>
    );
  }

  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };

  searchByKeyWord = () => {
    let url = '/biz/out/sourcing/progress/findTrack';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchOutsourcingTenantName: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="外协报表"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.headViewStyle,
            {width: screenWidth, borderWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={[
              CommonStyle.headViewStyle,
              {borderLeftWidth: 0, borderRightWidth: 0},
            ]}
            onLayout={this.topBlockLayout.bind(this)}>
            <View style={CommonStyle.singleSearchBox}>
              <View style={CommonStyle.searchBoxWithoutOthers}>
                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                <Image
                  style={{width: 16, height: 16, marginLeft: 7}}
                  source={require('../../assets/icon/iconfont/search.png')}></Image>
                <TextInput
                  style={{
                    color: 'rgba(rgba(0, 10, 32, 0.45))',
                    fontSize: 14,
                    marginLeft: 5,
                    paddingTop: 0,
                    paddingBottom: 0,
                    paddingRight: 0,
                    paddingLeft: 0,
                    width: '100%',
                  }}
                  returnKeyType="search"
                  returnKeyLabel="搜索"
                  onSubmitEditing={(e) => {
                    this.searchByKeyWord();
                  }}
                  placeholder={'外协名称'}
                  onChangeText={(text) => this.setState({searchKeyWord: text})}>
                  {this.state.searchKeyWord}
                </TextInput>
              </View>
            </View>
          </View>
        </View>
        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> */}
          <FlatList
            data={this.state.dataSource}
            ItemSeparatorComponent={this.space}
            ListEmptyComponent={this.emptyComponent}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.outsourcingTenantId}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
          {/* </ScrollView> */}
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
    marginTop: 5,
  },
  btnRowLeftCancelBtnView: {
    flexDirection: 'row',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#a1a1a1',
    borderRadius: 5,
    height: 40,
  },
  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
  },
  searchInputText: {
    width: screenWidth - 100,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },

  innerViewStyle: {
    // marginLeft:15,
    marginTop: 10,
    // borderColor:"#F4F4F4",
    // borderWidth:14,
    // borderColor:"#F4F4F4",
    // borderWidth:8,
  },
  innerViewStyleSearch: {
    // marginTop:10,
    // borderColor:"#F4F4F4",
    // borderWidth:14,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 40,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 20,
    marginTop: 15,
    fontSize: 16,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginTop: 10,
  },
  itemContentLeftChildViewStyle: {
    flexDirection: 'column',
    // alignContent:'flex-start',
    // justifyContent:'flex-start',
    // alignItems:'flex-start',
    width: screenWidth - 180,
    marginLeft: 20,
  },
  itemContentRightChildViewStyle: {
    flexDirection: 'column',
    // alignContent:'flex-start',
    // justifyContent:'flex-start',
    // alignItems:'flex-start',
    width: 120,
    marginLeft: -20,
  },
});
