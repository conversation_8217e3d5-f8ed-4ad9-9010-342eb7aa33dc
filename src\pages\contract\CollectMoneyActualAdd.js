import React, { Component } from 'react';
import {
    View, ScrollView, Text, TextInput, StyleSheet
    , FlatList, TouchableOpacity, Dimensions, Image, KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 160;

export default class CollectMoneyPointAdd extends Component {
    constructor() {
        super()
        this.state = {
            pointId: "",
            selectActualDate: [],
            selectInvoiceDate:[],
            // 节点数据集合
            pointIdDataSource: [],
            actualId: 0,
            selPointId: 0,
            actualProportion: "",
            actualAmount: "",
            actualDate: "",
            operate: "",
            contractAmount: "",
            contractId: "",
            actualProportionSum: "",
            dif: 0,
            actualInvoiceAmount: "",
            isInvoice: [
                {
                    "invoiceId": 0,
                    "invoice": "Y",
                    "describe": "是"
                },
                {
                    "invoiceId": 1,
                    "invoice": "N",
                    "describe": "否"
                }
            ],
            isReceipt: [
                {
                    "receiptId": 0,
                    "receipt": "Y",
                    "describe": "是"
                },
                {
                    "receiptId": 1,
                    "receipt": "N",
                    "describe": "否"
                }
            ],
            invoiceTypeList: [
                {
                    invoiceTypeId: 0,
                    invoiceType: "P",
                    describe: "普通发票"
                },
                {
                    invoiceTypeId: 1,
                    invoiceType: "V",
                    describe: "增值税专用发票"
                }
            ],
            selInvoice: "",
            selInvoiceType: "",
            selReceipt: "",
            invoiceDate: "",
            invoiceNo: "",
            rate: "",
            paymentType:"",         //支付方式
            paymentTypeDataSource:[],
            lastInput:"P"
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        if (constants.loginUser.tenantId == 66) {
            this.setState({
                selInvoice: "Y",
                selReceipt: "Y"
            })
        }
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { actualId, contractId, contractAmount, dataSource } = route.params;

            if (contractAmount) {
                console.log("合同金额目前是=====" + contractAmount);
                this.setState({
                    contractAmount: contractAmount
                })
            }
            else {
                let toastOpts;
                toastOpts = getFailToastOpts('请先填写合同金额');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
            }

            if (contractId) {
                console.log("=========现在的合同Id是" + contractId);
                this.setState({
                    contractId: contractId
                })
            }

            if (actualId) {
                console.log("=========现在的实际收款节点Id是" + actualId);
                this.setState({
                    actualId: actualId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/contract/collect/money/actual/get";
                loadRequest = { 'actualId': actualId };
                httpPost(loadTypeUrl, loadRequest, (response) => {
                    if (response.code == 200 && response.data) {
                        var selectActualDate = response.data.actualDate.split("-");
                        var selectInvoiceDate = [];
                        if (response.data.invoiceDate) {
                            selectInvoiceDate = response.data.invoiceDate.split("-");
                        }
                        else{
                            var currentDate = new Date();
                            var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                            var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                            selectInvoiceDate = [currentDate.getFullYear(), currentDateMonth, currentDateDay];
                        }
                        this.setState({
                            actualId: response.data.actualId,
                            pointId: response.data.pointId,
                            selectActualDate: selectActualDate,
                            actualProportion: response.data.actualProportion ? response.data.actualProportion : parseFloat((response.data.actualAmount / contractAmount * 100).toFixed(2)),
                            actualAmount: response.data.actualAmount ? response.data.actualAmount : parseFloat((response.data.actualProportion * contractAmount / 100).toFixed(2)),
                            actualDate: response.data.actualDate,
                            selPointId: response.data.pointId,
                            dif: response.data.actualProportion ? response.data.actualProportion : parseFloat((response.data.actualAmount / contractAmount * 100).toFixed(2)),
                            pointName: response.data.pointName,
                            contractId: response.data.contractId,
                            actualInvoiceAmount: response.data.actualInvoiceAmount,
                            selInvoice: response.data.invoice,
                            selInvoiceType: response.data.invoiceType,
                            selectInvoiceDate: selectInvoiceDate,
                            invoiceDate: response.data.invoiceDate,
                            invoiceNo: response.data.invoiceNo,
                            rate: response.data.rate,
                            selReceipt: response.data.receipt,
                            paymentType:response.data.paymentType,
                            lastInput:response.data.actualProportion ? 'P' : 'A'
                        })
                    }
                });
            }
            else {

                this.setState({
                    operate: "新增",
                })
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectActualDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    actualDate: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                    selectInvoiceDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                })
            }
        }
    

        let paymentTypeDataSource = [
            {
                stateCode: 1,
                stateName:'现金',
            },
            {
                stateCode: 2,
                stateName:'银行转账',
            },
            {
                stateCode: 3,
                stateName:'纸质承兑',
            },
            {
                stateCode: 4,
                stateName:'电子承兑',
            },
        ]
        this.setState({
            paymentTypeDataSource:paymentTypeDataSource,

        })

        let url = "/biz/contract/collect/money/point/list";
        let loadRequest1 = {
            "currentPage": 1,
            "pageSize": 15
        };
        httpPost(url, loadRequest1, this.loadContractMoneyPointListCallBack);
    }

    loadContractMoneyPointListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            // var dataNew = response.data.dataList;
            // var dataOld = this.state.pointIdDataSource;
            // // dataOld.unshift(dataNew);
            // var dataAll = [...dataOld,...dataNew];
            this.setState({
                pointIdDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditActualDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectActualDate = response.data.actualDate.split("-");

            var selectInvoiceDate = [];
            if (response.data.invoiceDate) {
                selectInvoiceDate = response.data.invoiceDate.split("-");
            }
            this.setState({

                actualId: response.data.actualId,
                pointId: response.data.pointId,
                selectActualDate: selectActualDate,
                actualProportion: response.data.actualProportion,
                actualAmount: response.data.actualAmount,
                actualDate: response.data.actualDate,
                selPointId: response.data.pointId,
                dif: response.data.actualProportion,
                pointName: response.data.pointName,
                contractId: response.data.contractId,
                actualInvoiceAmount: response.data.actualInvoiceAmount,
                selInvoice: response.data.invoice,
                selInvoiceType: response.data.invoiceType,
                selectInvoiceDate: selectInvoiceDate,
                invoiceDate: response.data.invoiceDate,
                invoiceNo: response.data.invoiceNo,
                rate: response.data.rate,
                selReceipt: response.data.receipt,
            })
        }
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    // 收款节点渲染
    renderPointRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selPointId: item.pointId
                })
            }}>
                <View key={item.pointId} style={item.pointId === this.state.selPointId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.pointId === this.state.selPointId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.pointName}
                    </Text>
                </View>

            </TouchableOpacity>
        )
    }

    // 是否开票渲染
    renderInvoiceRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selInvoice: item.invoice
                })
            }}>
                <View key={item.invoiceId} style={item.invoice === this.state.selInvoice ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.invoice === this.state.selInvoice ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.describe}
                    </Text>
                </View>

            </TouchableOpacity>
        )
    }

    // 开票类型渲染
    renderInvoiceTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selInvoiceType: item.invoiceType
                })
            }}>
                <View key={item.invoiceTypeId} style={item.invoiceType === this.state.selInvoiceType ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.invoiceType === this.state.selInvoiceType ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.describe}
                    </Text>
                </View>

            </TouchableOpacity>
        )
    }

    // 是否收款渲染
    renderReceiptRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selReceipt: item.receipt
                })
            }}>
                <View key={item.receiptId} style={item.receipt === this.state.selReceipt ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.receipt === this.state.selReceipt ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.describe}
                    </Text>
                </View>

            </TouchableOpacity>
        )
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("CollectMoneyActualList")
            }}>
                <Text style={CommonStyle.headRightText}>收款管理</Text>
            </TouchableOpacity>
        )
    }

    openActualDate() {
        this.refs.selectActualDate.showDate(this.state.selectActualDate)
    }
    openInvoiceDate() {
        this.refs.selectInvoiceDate.showDate(this.state.selectInvoiceDate)
    }
    callBackSelectActualDateValue(value) {
        console.log("==========实际收款时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectActualDate: value
        })
        if (this.state.selectActualDate && this.state.selectActualDate.length) {
            var actualDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectActualDate.length; index++) {
                vartime = this.state.selectActualDate[index];
                if (index === 0) {
                    actualDate += vartime;
                }
                else {
                    actualDate += "-" + vartime;
                }
            }
            this.setState({
                actualDate: actualDate
            })
        }
        
        var dateString = this.state.actualDate + ' 00:00:01';
        dateString = dateString.substring(0, 19);
        dateString = dateString.replace(/-/g, '/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var OneDaysLast = new Date(dateStringTimestamp);
        // 用获取毫秒数 加上30天的毫秒数 赋值给OneDaysLast对象（一天有86400000毫秒）
        OneDaysLast.setTime(dateStringTimestamp + (1 * 86400000));
        //格式化月，如果小于9，前面补0
        var OneDaysLastOfMonth = ("0" + (OneDaysLast.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0
        var OneDaysLastOfDay = ("0" + OneDaysLast.getDate()).slice(-2);
        this.setState({
            selectInvoiceDate: [OneDaysLast.getFullYear(), OneDaysLastOfMonth, OneDaysLastOfDay],
            invoiceDate: OneDaysLast.getFullYear() + "-" + OneDaysLastOfMonth + "-" + OneDaysLastOfDay
        })
        if (this.state.selectInvoiceDate && this.state.selectInvoiceDate.length) {
            var invoiceDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectInvoiceDate.length; index++) {
                vartime = this.state.selectInvoiceDate[index];
                if (index === 0) {
                    invoiceDate += vartime;
                }
                else {
                    invoiceDate += "-" + vartime;
                }
            }
            this.setState({
                invoiceDate: invoiceDate
            })
        }
    }

    callBackSelectInvoiceDateValue(value) {
        console.log("==========实际开票时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectInvoiceDate: value
        })
        if (this.state.selectInvoiceDate && this.state.selectInvoiceDate.length) {
            var invoiceDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectInvoiceDate.length; index++) {
                vartime = this.state.selectInvoiceDate[index];
                if (index === 0) {
                    invoiceDate += vartime;
                }
                else {
                    invoiceDate += "-" + vartime;
                }
            }
            this.setState({
                invoiceDate: invoiceDate
            })
        }
    }

    getProportionSum = () => {

        console.log("=======getProportionSum");
        let toastOpts;
        if (!this.state.selPointId) {
            toastOpts = getFailToastOpts("请选择收款节点");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.actualProportion) {
        //     toastOpts = getFailToastOpts("请输入实际收款比例");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.actualAmount) {
        //     toastOpts = getFailToastOpts("请输入实际收款金额");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.actualDate) {
            toastOpts = getFailToastOpts("请选择实际收款日期");
            WToast.show(toastOpts)
            return;
        }

        let url0 = "/biz/contract/collect/money/actual/sum";
        let params = {
            contractId: this.state.contractId
        }
        httpPost(url0, params, this.getProportionSumCallBack);

    }

    // 获取比列总和的回调函数
    getProportionSumCallBack = (response) => {
        if (response.code == 200) {
            console.log(response)
            var sum;
            sum = response.data
            // this.setState({
            //     planProportionSum:sum
            // })
            console.log("比例综合为：" + sum);
            this.saveCollectMoneyActual(sum);
        }
    }


    saveCollectMoneyActual = (sum) => {

        var proportionSum;
        let url = "/biz/contract/collect/money/actual/add";
        proportionSum = 100 - sum;
        console.log(sum);
        // console.log(proportionSum + this.state.diff);


        if (this.state.actualId) {
            console.log("=========Edit===actualId", this.state.actualId)
            proportionSum = proportionSum + this.state.dif
            url = "/biz/contract/collect/money/actual/modify";
        }



        let requestParams = {
            actualId: this.state.actualId,
            pointId: this.state.selPointId,
            actualProportion: this.state.lastInput == 'P' ? this.state.actualProportion : null,
            actualAmount: this.state.lastInput == 'A' ? this.state.actualAmount : null,
            actualDate: this.state.actualDate,
            contractId: this.state.contractId,
            actualInvoiceAmount: this.state.actualInvoiceAmount,
            invoice: this.state.selInvoice,
            invoiceType: this.state.selInvoiceType,
            receipt: this.state.selReceipt,
            invoiceDate: this.state.invoiceDate,
            invoiceNo: this.state.invoiceNo,
            rate: this.state.rate,
            paymentType:this.state.paymentType,
        };

        console.log("===requestParams",requestParams);
        // 保留2位小数
        proportionSum = proportionSum.toFixed(2)
        if (proportionSum >= this.state.actualProportion) {
            httpPost(url, requestParams, this.saveCollectActualPointCallBack);
        } else {
            console.log("==proportionSum:", proportionSum, "==actualProportion", this.state.actualProportion)
            let toastOpts;
            toastOpts = getFailToastOpts("比例超过100，请重新输入比例");
            WToast.show(toastOpts)
        }
    }


    saveCollectActualPointCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }


    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }

    // 配置付款方式单项渲染
    renderPaymentTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() =>{
                this.setState({
                    paymentType: item.stateCode
                })
                console.log("======paymentTypeChoose==", this.state.paymentType,",",item.stateName,",",item.stateCode)
            }}>
                <View key={item.stateCode} style={item.stateCode === this.state.paymentType ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.stateCode === this.state.paymentType ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.stateName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={CommonStyle.rowLabView}>
                        <Text style={CommonStyle.rowLabTextStyle}>收款节点</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.pointIdDataSource && this.state.pointIdDataSource.length > 0)
                                ?
                                this.state.pointIdDataSource.map((item, index) => {
                                    return this.renderPointRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>实际收款比例</Text>
                            {/*<Text style={styles.leftLabRedTextStyle}>*</Text>*/}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 60) }]}
                            placeholder={'请输入实际收款比例'}
                            onChangeText={(text) => {
                                this.setState({
                                    actualProportion: text,
                                    actualAmount: this.state.contractAmount ? parseFloat((text * this.state.contractAmount / 100).toFixed(2)) : null,
                                    lastInput:"P"
                                })
                            }}
                        >
                            {this.state.actualProportion}

                        </TextInput>
                        <Text style={styles.percent}>%</Text>
                    </View>

                    {
                        (constants.loginUser.tenantId == 66) ?
                            <View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>是否开票</Text>
                                    </View>
                                    <View style={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                                        {
                                            (this.state.isInvoice && this.state.isInvoice.length > 0)
                                                ?
                                                this.state.isInvoice.map((item, index) => {
                                                    return this.renderInvoiceRow(item)
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </View>
                                {
                                    this.state.selInvoice === 'Y' ?
                                        <View style={styles.inputRowStyle}>
                                            <View style={styles.leftLabView}>
                                                <Text style={styles.leftLabNameTextStyle}>开票类型</Text>
                                            </View>
                                            <View style={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                                                {
                                                    (this.state.invoiceTypeList && this.state.invoiceTypeList.length > 0)
                                                        ?
                                                        this.state.invoiceTypeList.map((item, index) => {
                                                            return this.renderInvoiceTypeRow(item)
                                                        })
                                                        : <EmptyRowViewComponent />
                                                }
                                            </View>
                                        </View>
                                        : <View />
                                }
                            </View>
                            :
                            <View />
                    }
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>{constants.loginUser.tenantId === 66 ? "实际开票金额(含税)" : "实际开票金额"}</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60)}]}
                            placeholder={'请输入实际开票金额'}
                            onChangeText={(text) => this.setState({ actualInvoiceAmount: text })}
                        >
                            {this.state.actualInvoiceAmount}
                        </TextInput>
                    </View>
                    {
                        (constants.loginUser.tenantId == 66) ?
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>税率</Text>
                                    {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                </View>
                                <TextInput
                                    keyboardType='numeric'
                                    style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 60) }]}
                                    placeholder={'请输入税率'}
                                    onChangeText={(text) => this.setState({ rate: text })}
                                >
                                    {this.state.rate}

                                </TextInput>
                                <Text style={styles.percent}>%</Text>
                            </View>
                            :
                            <View />
                    }
                    {
                        (constants.loginUser.tenantId == 66) ?
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={CommonStyle.rowLabTextStyle}>是否收款</Text>
                                </View>
                                <View style={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                                    {
                                        (this.state.isReceipt && this.state.isReceipt.length > 0)
                                            ?
                                            this.state.isReceipt.map((item, index) => {
                                                return this.renderReceiptRow(item)
                                            })
                                            : <EmptyRowViewComponent />
                                    }
                                </View>
                            </View>
                            :
                            <View />
                    }
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>开票日期</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={() => this.openInvoiceDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { marginLeft: 5, width: screenWidth - (leftLabWidth + 60) }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.invoiceDate ? "请选择开票日期" : this.state.invoiceDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>发票号</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60)}]}
                            placeholder={'请输入发票号'}
                            onChangeText={(text) => this.setState({ invoiceNo: text })}
                        >
                            {this.state.invoiceNo}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>实际收款金额</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60)}]}
                            placeholder={'请输入实际收款金额'}
                            onChangeText={(text) => {
                                this.setState({
                                    actualAmount: text,
                                    actualProportion: this.state.contractAmount ? parseFloat((text * 100 / this.state.contractAmount).toFixed(2)) : null,
                                    lastInput:"A"
                                })
                            }}
                        >
                            {this.state.actualAmount}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>实际收款日期</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openActualDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { marginLeft: 5, width: screenWidth - (leftLabWidth + 60) }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.actualDate ? "请选择实际收款日期" : this.state.actualDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>付款方式</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.paymentTypeDataSource && this.state.paymentTypeDataSource.length > 0) 
                            ? 
                            this.state.paymentTypeDataSource.map((item, index)=>{
                                return this.renderPaymentTypeRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>

                    

                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => this.getProportionSum()}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row' }]}>
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                </ScrollView>

                <BottomScrollSelect
                    ref={'selectActualDate'}
                    callBackDateValue={this.callBackSelectActualDateValue.bind(this)}
                />
                    <BottomScrollSelect
                        ref={'selectInvoiceDate'}
                        callBackDateValue={this.callBackSelectInvoiceDateValue.bind(this)}
                    />                
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({

    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },

    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,

        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 6,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 40),
        marginLeft: 5,
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 8,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },

    inputTextStyleTextStyle: {
        left: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    percent: {
        fontSize: 20,
        marginTop: 6

    }

})

// module.exports = CollectMoneyActualAdd
