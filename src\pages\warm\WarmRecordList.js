import React,{Component} from 'react';
import {View, Text, StyleSheet, Image, FlatList,Dimensions, ScrollView,TouchableOpacity} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
// import TopScreen from '../../component/TopScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CommonHeadScreen from '../../component/CommonHeadScreen';

var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
var cols = 5;
var cellWH = screenWidth / cols;
var vMargin = (screenWidth - cellWH * cols) / (cols + 1);
class WarmRecordList extends Component{
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[]
        }
    }
    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadWarmRecordList();
    }

    loadWarmRecordList=()=>{
        let url= "/biz/warm/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 200
        };
        httpPost(url, loadRequest, this.callBackLoadWarmRecordList);
    }

    callBackLoadWarmRecordList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                dataSource:response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    renderAreaWarm=(areaWarmItem)=>{
        console.log("========areaWarmItem:", areaWarmItem);
        return(
            <View key={areaWarmItem.warmAreaId} style={styles.warmAreaItemStyle}>
                <View style={styles.warmAreaNameItemStyle}>
                    <Text style={styles.warmAreaNameTextItemStyle}>{areaWarmItem.warmAreaName}</Text>
                </View>
                <View style={styles.warmAreaValueItemStyle}>
                    <Text style={styles.warmAreaNameTextItemStyle}>{areaWarmItem.warmValue}</Text>
                </View>
            </View>
        )
    }

    // 行子项的渲染
    renderItemRow=(warmEaraRecordList)=>{  
        console.log("========warmEaraRecordList:", warmEaraRecordList);        
        return (
            <FlatList 
            numColumns = {6}
            data={warmEaraRecordList}
            renderItem={({item}) => this.renderAreaWarm(item)}
        //     renderItem={({item}) => <View>
        //         <View>
        // <Text>{item.warmAreaName}</Text>
        //         </View>
        //         <View>
        // <Text>{item.warmValue}</Text>
        //         </View>
        //     </View>}
            />
        );
    }
    // 行的渲染
    renderRow=(item)=>{
        return (
            <View key={item.warmRecordId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.kilnCarName}</Text>
                    <Text style={styles.titleTextStyle}>{item.gmtCreated}</Text>
                </View>
                {this.renderItemRow(item.warmEaraRecordList)}
            </View>
        )
    }
    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0',marginBottom:10,marginTop:10}}/>)
    }

     // 头部左侧
     renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("AddWarmRecord")
                }}>
                <Text style={CommonStyle.headRightText}>新增温度</Text>
            </TouchableOpacity>
        )
    }
    render(){
        return(
            <View>
                {/* <TopScreen title='隧道窑温度管理' do="warm_record_add"/> */}
                <CommonHeadScreen title='温度管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                <FlatList 
                    data={this.state.dataSource}
                    ItemSeparatorComponent={this.space}
                    ListEmptyComponent={EmptyListComponent}
                    renderItem={({item}) => this.renderRow(item)}
                    />
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10
    },
    titleTextStyle:{
        fontSize:23
    },
    warmAreaItemStyle:{
        width:cellWH,
        marginLeft:vMargin,
        alignItems:'center'
    },
    warmAreaNameItemStyle:{
        padding:10
    },
    warmAreaNameTextItemStyle:{
        color:'#666666',
        fontSize:15
    },
    warmAreaValueItemStyle:{
        backgroundColor:'#EFF0F1',
        marginBottom:5,
        flexDirection:'row',
        justifyContent:'center',
        alignItems:'center',
        padding:10,
        width:70,
        height:30,
        borderRadius:3
    }
})
module.exports = WarmRecordList;