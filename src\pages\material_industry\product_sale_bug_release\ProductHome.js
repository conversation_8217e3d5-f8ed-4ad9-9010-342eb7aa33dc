import React,{Component} from 'react';
import {View, Text, StyleSheet, Image, FlatList,RefreshControl,TextInput,Linking
    ,Dimensions, ScrollView, TouchableOpacity, Alert,Animated,Modal,CameraRoll,Clipboard} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';

// 公共组件及样式
import EmptyListComponent from '../../../component/EmptyListComponent';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
import EmptyRowViewComponent from '../../../component/EmptyRowViewComponent';
import ProductEmptyRowViewComponent from '../../../component/ProductEmptyRowViewComponent';

var CommonStyle = require('../../../assets/css/CommonStyle');

import Swiper from 'react-native-swiper';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../../utils/CameraRollUtils';
// import StickyHeader from 'react-native-StickyHeader';

import {WToast} from 'react-native-smart-tip';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
var cols = 3;
var cellWH = 100;
var vMargin = (screenWidth - cellWH * cols) / (cols + 1);
var hMargin = 10;

class ProductHome extends Component{


    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight: 0,
            crTopBlockLayoutHeight:0,
            searchKeyWord:"",
            swiperDataSource:[],
            refractoryProductDataSource:[],
            productAskForPurchaseDataSource:[],
            materialAskForPurchaseDataSource:[],
            refractoryMaterialDataSource:[],
            isShowImage:false,
            urls:""
        }
        
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
        }

        this.loadswiperDataSource();

        this.loadRefractoryProduct();
        this.loadProductAskForPurchase();
        this.loadRefractoryMaterial();
        this.loadMaterialAskForPurchase();

    }

    loadRefractoryProduct = ()=>{
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 6,
            "releaseType": "R",
            "productType":"P",
            "productState":"U",
        };
        httpPost(url, loadRequest, this.loadRefractoryProductCallBack);
    }

    loadRefractoryProductCallBack = (response)=>{

        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                refractoryProductDataSource:[...response.data.dataList],
            })
            console.log("refractoryProductDataSource=",[...response.data.dataList])

        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        
    }



    loadProductAskForPurchase = ()=>{

        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 6,
            "releaseType": "P",
            "productType":"P",
        };
        httpPost(url, loadRequest, this.loadProductAskForPurchaseCallBack);

    }

    loadProductAskForPurchaseCallBack = (response)=>{

        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                productAskForPurchaseDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        
    }

    loadRefractoryMaterial = ()=>{
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 6,
            "releaseType": "R",
            "productType":"M",
            "productState":"U",
        };
        httpPost(url, loadRequest, this.loadRefractoryMaterialCallBack);
    }

    loadRefractoryMaterialCallBack = (response)=>{

        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                refractoryMaterialDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadMaterialAskForPurchase = ()=>{

        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 6,
            "releaseType": "P",
            "productType":"M",
        };
        httpPost(url, loadRequest, this.loadMaterialAskForPurchaseCallBack);

    }

    loadMaterialAskForPurchaseCallBack = (response)=>{

        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                materialAskForPurchaseDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }



    loadswiperDataSource = ()=>{
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10,
        };
        httpPost(url, loadRequest, this.loadswiperDataSourceCallBack);
    }

    loadswiperDataSourceCallBack = (response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                swiperDataSource:[...response.data.dataList],
            })
            
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }


    logout=()=>{
        console.log("===logout");
        let url = "/biz/user/logout?a=123&b=234"
        httpGet(url, this.logout_call_back);
    }

    logout_call_back=(response)=>{
        console.log("=====logout_call_back:", response);
        this.props.navigation.navigate('LoginView');
    }




    _pressJump(item) {
        const { navigation } = this.props;
        if(navigation && item.component != null) {
            navigation.navigate(item.component, {
                // 测试参数
                itemId: 1000000, 
                code:item.code,
                title: item.title
            })
        }
    }


    refractoryProductRow=(item, index) =>{
        return(
            <View key={item.releaseId} style={styles.innerViewStyle}>
                <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                    <Text style={styles.titleTextStyle}>{item.productName}</Text>
                    <Text style={[styles.greyTextStyle,{marginLeft:30}]}>型号：{item.productModel}</Text>
                </View>

                <View style={{position:'absolute',right:-10,top:10}}>
                    <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/>
                    <Text style={{position:'absolute',right:3,color:"#f0f0f0"}}>现货</Text>
                </View>

                <View style={{flexDirection:'row',display:'flex'}}>
                    <View >
                        {
                            item.compressFileList && item.compressFileList.length > 0 ?
                            <View>
                                <TouchableOpacity onPress={() => {
                                    var urls = [];
                                    var url = {
                                        url:constants.image_addr + '/' +  item.compressFileList[0].compressFile
                                    }
                                    urls=urls.concat(url) 
                                    console.log(url)

                                    this.setState({
                                        urls:urls
                                    })
                                    this.setState({
                                        isShowImage:true,
                                    })

                                }}>
                                    <Image source={{ uri: (constants.image_addr + '/' + item.compressFileList[0].compressFile) }} style={{ height: 70, width:85,marginTop:10 }} />                                                    
                                </TouchableOpacity>
                                <Modal visible={this.state.isShowImage} transparent={true}>
                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} 
                                    enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}  
                                    onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} 
                                    onSave={()=>{
                                        saveImage( this.state.urls[0].url)
                                    }}/>
                                </Modal>
                            </View>
                            :
                            <View style={{display:'flex',justifyContent:'center',alignItems:'center'}}>
                                <Image  style={{height: 70, width:85,marginTop:10}} source={require('../../../assets/icon/iconfont/emptyPicture.png')}></Image>
                            </View>
                        }
                    </View>

                    <View style={{flexDirection:'column',marginLeft:20,marginTop:5}}>
                        <View>
                            <Text style={styles.blackTextStyle}>材质：{item.productMaterial}</Text>
                        </View>
                        <View>
                            <Text style={styles.blackTextStyle}>重量：{item.productWeight}</Text>
                        </View>
                        <View style={{flexDirection:'row'}}>
                            <Text style={styles.blackTextStyle}>价格：
                                <Text style={styles.redTextStyle}>{parseFloat(item.productPrice)}</Text>
                                <Text style={styles.greyTextStyle}> 元/吨</Text>
                            </Text>
                        </View>
                        <View>
                            <Text style={styles.blackTextStyle}>产地：{item.productionAddr}</Text>
                        </View>
                    </View>

                    <View style={{position:'absolute',right:10,alignItems:'center',top:30}}>
                        <TouchableOpacity  onPress={() => {
                            let phone = item.salePersonTel;
                            const url = `tel:${phone}`;
                            Linking.canOpenURL(url)
                            .then(supported => {
                                if (!supported) {
                                return Alert.alert('提示', `您的设备不支持该功能，请手动拨打 ${phone}`, [
                                    { text: '确定' }
                                ]);
                                }
                                return Linking.openURL(url);
                            })
                            .catch(err => Toast.info(`出错了：${err}`, 1.5));
                        }}>
                            <Image  style={{width:40, height:40}} source={require('../../../assets/icon/iconfont/tel.png')}></Image>
                        </TouchableOpacity>
                    </View>
                
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>说明：{item.productExplain?item.productExplain:"无"}</Text>
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>发布日期：{item.releaseDate}</Text>
                </View>

            </View>
        )
        
    }


    productAskForPurchaseRow=(item, index) =>{
        return(
            <View key={item.releaseId} style={styles.innerViewStyle}>
                <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                    <Text style={styles.titleTextStyle}>{item.productName}</Text>
                </View>

                {/* <View style={{position:'absolute',right:-10,top:10}}>
                    <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/>
                    <Text style={{position:'absolute',right:3,color:"#f0f0f0"}}>现货</Text>
                </View> */}

                <View style={{flexDirection:'row',display:'flex'}}>

                    <View style={{flexDirection:'column',marginTop:10}}>
                        <View style={{flexDirection:'row'}}>
                            <Text style={styles.blackTextStyle}>型号：{item.productModel}</Text>
                            <Text style={[styles.blackTextStyle,{marginLeft:30}]}>重量：{item.productWeight}吨</Text>
                        </View>
                        <View>
                            <Text style={styles.blackTextStyle}>材质：{item.productMaterial}</Text>
                        </View>
                        <View>
                            <Text style={styles.blackTextStyle}>期望产地：{item.productionAddr}</Text>
                        </View>
                    </View>

                    <View style={{position:'absolute',right:10,alignItems:'center',top:30}}>
                        <TouchableOpacity  onPress={() => {
                            let phone = item.salePersonTel;
                            const url = `tel:${phone}`;
                            Linking.canOpenURL(url)
                            .then(supported => {
                                if (!supported) {
                                return Alert.alert('提示', `您的设备不支持该功能，请手动拨打 ${phone}`, [
                                    { text: '确定' }
                                ]);
                                }
                                return Linking.openURL(url);
                            })
                            .catch(err => Toast.info(`出错了：${err}`, 1.5));
                        }}>
                            <Image  style={{width:40, height:40}} source={require('../../../assets/icon/iconfont/tel.png')}></Image>
                        </TouchableOpacity>
                    </View>
                
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>说明：{item.productExplain?item.productExplain:"无"}</Text>
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>发布日期：{item.releaseDate}</Text>
                </View>

            </View>
        )
        
    }

    refractoryMaterialRow=(item, index) =>{
        return(
            <View key={item.releaseId} style={styles.innerViewStyle}>
                <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                    <Text style={styles.titleTextStyle}>{item.productName}</Text>
                </View>

                <View style={{position:'absolute',right:-10,top:10}}>
                    <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/>
                    <Text style={{position:'absolute',right:3,color:"#f0f0f0"}}>现货</Text>
                </View>

                <View style={{flexDirection:'row',display:'flex'}}>
                    <View >
                        {
                            item.compressFileList && item.compressFileList.length > 0 ?
                            <View>
                                <TouchableOpacity onPress={() => {
                                    var urls = [];
                                    var url = {
                                        url:constants.image_addr + '/' +  item.compressFileList[0].compressFile
                                    }
                                    urls=urls.concat(url) 
                                    console.log(url)

                                    this.setState({
                                        urls:urls
                                    })
                                    this.setState({
                                        isShowImage:true,
                                    })

                                }}>
                                    <Image source={{ uri: (constants.image_addr + '/' + item.compressFileList[0].compressFile) }} style={{ height: 70, width:85,marginTop:10 }} />                                                    
                                </TouchableOpacity>
                                <Modal visible={this.state.isShowImage} transparent={true}>
                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} 
                                    enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}  
                                    onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} 
                                    onSave={()=>{
                                        saveImage( this.state.urls[0].url)
                                    }}/>
                                </Modal>
                            </View>
                            :
                            <View style={{display:'flex',justifyContent:'center',alignItems:'center'}}>
                                <Image  style={{height: 70, width:85,marginTop:10}} source={require('../../../assets/icon/iconfont/emptyPicture.png')}></Image>
                            </View>
                        }
                    </View>

                    <View style={{flexDirection:'column',marginLeft:20,marginTop:5}}>
                        <View>
                            <Text style={styles.blackTextStyle}>重量：{item.productWeight}</Text>
                        </View>
                        
                        {
                            item.productPrice ?
                            <View style={{flexDirection:'row'}}>
                                <Text style={styles.blackTextStyle}>价格：
                                    <Text style={styles.redTextStyle}>{parseFloat(item.productPrice)}</Text>
                                    <Text style={styles.greyTextStyle}> 元/吨</Text>
                                </Text>
                            </View> :
                            <View style={{flexDirection:'row'}}>
                                <Text style={styles.blackTextStyle}>价格：
                                    <Text style={styles.redTextStyle}>面议</Text>
                                </Text>
                            </View>
                        }

                      
                        <View>
                            <Text style={styles.blackTextStyle}>产地：{item.productionAddr}</Text>
                        </View>
                    </View>

                    <View style={{position:'absolute',right:10,alignItems:'center',top:30}}>
                        <TouchableOpacity  onPress={() => {
                            let phone = item.salePersonTel;
                            const url = `tel:${phone}`;
                            Linking.canOpenURL(url)
                            .then(supported => {
                                if (!supported) {
                                return Alert.alert('提示', `您的设备不支持该功能，请手动拨打 ${phone}`, [
                                    { text: '确定' }
                                ]);
                                }
                                return Linking.openURL(url);
                            })
                            .catch(err => Toast.info(`出错了：${err}`, 1.5));
                        }}>
                            <Image  style={{width:40, height:40}} source={require('../../../assets/icon/iconfont/tel.png')}></Image>
                        </TouchableOpacity>
                    </View>
                
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>说明：{item.productExplain?item.productExplain:"无"}</Text>
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>发布日期：{item.releaseDate}</Text>
                </View>

            </View>
        )
        
    }


    materialAskForPurchaseRow=(item, index) =>{
        return(
            <View key={item.releaseId} style={styles.innerViewStyle}>
                <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                    <Text style={styles.titleTextStyle}>{item.productName}</Text>
                </View>
{/* 
                <View style={{position:'absolute',right:-10,top:10}}>
                    <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/>
                    <Text style={{position:'absolute',right:3,color:"#f0f0f0"}}>现货</Text>
                </View> */}

                <View style={{flexDirection:'row',display:'flex'}}>

                    <View style={{flexDirection:'column',marginTop:10}}>
                        <View>
                            <Text style={styles.blackTextStyle}>重量：{item.productWeight}吨</Text>
                        </View>
                        <View>
                            <Text style={styles.blackTextStyle}>期望产地：{item.productionAddr}</Text>
                        </View>
                    </View>

                    <View style={{position:'absolute',right:10,alignItems:'center',top:15}}>
                        <TouchableOpacity  onPress={() => {
                            let phone = item.salePersonTel;
                            const url = `tel:${phone}`;
                            Linking.canOpenURL(url)
                            .then(supported => {
                                if (!supported) {
                                return Alert.alert('提示', `您的设备不支持该功能，请手动拨打 ${phone}`, [
                                    { text: '确定' }
                                ]);
                                }
                                return Linking.openURL(url);
                            })
                            .catch(err => Toast.info(`出错了：${err}`, 1.5));
                        }}>
                            <Image  style={{width:40, height:40}} source={require('../../../assets/icon/iconfont/tel.png')}></Image>
                        </TouchableOpacity>
                    </View>
                
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>说明：{item.productExplain?item.productExplain:"无"}</Text>
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>发布日期：{item.releaseDate}</Text>
                </View>

            </View>
        )
        
    }



    render(){
        return(
            <View style={{backgroundColor:'#F5F5F5'}}>
                

                <ScrollView
                // style={{display:'flex'}}
                //  showsVerticalScrollIndicator={false}
                //  style={{zIndex:1000}}
                //  stickyHeaderIndices={[2]}
                 
                >
                
                <View style={{display:'flex'}} >
                    <View style={[CommonStyle.contentViewStyle,{height:200,width:screenWidth,backgroundColor:'#FFFFFF',justifyContent:'center'}]}>
                    {
                        this.state.swiperDataSource && this.state.swiperDataSource.length > 0
                        ?
                            <Swiper
                                ref="swiper"    
                                key={this.state.swiperDataSource.length}
                                height={300} 
                                // horizontal={false} 
                                autoplay={true} //自动轮播
                                autoplayTimeout={3.5} //每隔2秒切换
                            >
                                {
                                    this.state.swiperDataSource && this.state.swiperDataSource.length > 0?
                                        (
                                        this.state.swiperDataSource.map((item, index) => {
                                            return (
                                                <View style={{width:screenWidth - 20,marginLeft:10,marginTop:5}}>
                                                    <Image  style={[{height:180 ,borderRadius:10}]} source={{uri:item.advertisingImage}} />                                
                                                </View>
                                            
                                            );
                                        })
                                        )
                                    :
                                    <View></View>
                                }
                                
                                
                            </Swiper>
                        
                        :
                        <Swiper style={[styles.wrapper,{justifyContent:'center',alignItems:'center'}]}
                            ref="swiper"
                            height={300} 
                            // horizontal={false} 
                            autoplay={false} //自动轮播
                            // autoplayTimeout={3.5} //每隔2秒切换
                            // data={this.state.swiperDataSource}
                            // renderItem={({ item, index }) => this.renderPictureRow(item, index)}
                        >
                            <View></View>
                            
                            
                        </Swiper>

                    }
                    
                    </View>
                </View>
                
                <View style={{marginTop:10,backgroundColor:'#F9FDFF',paddingLeft:10}}>
                    <TouchableOpacity onPress={() => {this.props.navigation.navigate("Product", { productType:'P' })}} style={{height:40,display:'flex',flexDirection:'row',alignItems:'center'}}>
                        <Image  style={[{height:40,width:screenWidth/1.2}]} source={require('../../../assets/image/refractoryProduct.jpg')} />    
                        <Image style={{width : 12 ,height:12,marginLeft:30}} source={require('../../../assets/icon/iconfont/arrowRight.png')}></Image>
                    </TouchableOpacity>
                </View>

                <View style={{ index: 1000,backgroundColor:'#FFF'}}>
                    {
                        (this.state.refractoryProductDataSource && this.state.refractoryProductDataSource.length > 0)
                            ?
                            this.state.refractoryProductDataSource.map((item, index) => {
                                return this.refractoryProductRow(item)
                            })
                            : <View/>
                    }
                </View>
                
                <View style={{marginTop:10,backgroundColor:'#F9FDFF',paddingLeft:10}}>
                    <TouchableOpacity onPress={() => {this.props.navigation.navigate("AskForPurchase", { productType:'P' })}} style={{height:40,display:'flex',flexDirection:'row',alignItems:'center'}}>
                        <Image  style={[{height:40,width:screenWidth/1.2}]} source={require('../../../assets/image/productAskForPurchase.jpg')} />    
                        <Image style={{width : 12 ,height:12,marginLeft:30}} source={require('../../../assets/icon/iconfont/arrowRight.png')}></Image>
                    </TouchableOpacity>
                </View>

                <View style={{ index: 1000,backgroundColor:'#FFF'}}>
                    {
                        (this.state.productAskForPurchaseDataSource && this.state.productAskForPurchaseDataSource.length > 0)
                            ?
                            this.state.productAskForPurchaseDataSource.map((item, index) => {
                                return this.productAskForPurchaseRow(item)
                            })
                            : <View/>
                    }
                </View>

                <View style={{marginTop:10,backgroundColor:'#F9FDFF',paddingLeft:10}}>
                    <TouchableOpacity onPress={() => {this.props.navigation.navigate("Product", { productType:'M' })}} style={{height:40,display:'flex',flexDirection:'row',alignItems:'center'}}>
                        <Image  style={[{height:40,width:screenWidth/1.2}]} source={require('../../../assets/image/refractoryMaterial.jpg')} />    
                        <Image style={{width : 12 ,height:12,marginLeft:30}} source={require('../../../assets/icon/iconfont/arrowRight.png')}></Image>
                    </TouchableOpacity>
                </View>

                <View style={{ index: 1000,backgroundColor:'#FFF'}}>
                    {
                        (this.state.refractoryMaterialDataSource && this.state.refractoryMaterialDataSource.length > 0)
                            ?
                            this.state.refractoryMaterialDataSource.map((item, index) => {
                                return this.refractoryMaterialRow(item)
                            })
                            : <View/>
                    }
                </View>

                <View style={{marginTop:10,backgroundColor:'#F9FDFF',paddingLeft:10}}>
                    <TouchableOpacity onPress={() => {this.props.navigation.navigate("AskForPurchase", { productType:'M' })}} style={{height:40,display:'flex',flexDirection:'row',alignItems:'center'}}>
                        <Image  style={[{height:40,width:screenWidth/1.2}]} source={require('../../../assets/image/materialAskForPurchase.jpg')} />    
                        <Image style={{width : 12 ,height:12,marginLeft:30}} source={require('../../../assets/icon/iconfont/arrowRight.png')}></Image>
                    </TouchableOpacity>
                </View>

                <View style={{ index: 1000,backgroundColor:'#FFF'}}>
                    {
                        (this.state.materialAskForPurchaseDataSource && this.state.materialAskForPurchaseDataSource.length > 0)
                            ?
                            this.state.materialAskForPurchaseDataSource.map((item, index) => {
                                return this.materialAskForPurchaseRow(item)
                            })
                            : <ProductEmptyRowViewComponent/>
                    }
                </View>
                

                </ScrollView>


                <View style={CommonStyle.contentViewStyle}>
                    
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({

    innerViewStyle:{
        width:screenWidth - 20,
        marginLeft:10,
        paddingLeft:10,
        paddingTop:10,
        marginTop:10,
        backgroundColor:'#FFF',
        borderBottomColor:'#33333333',
        borderBottomWidth:0.8,
        paddingBottom:10
    },
    crInnerViewStyle:{
        marginTop:20,
        backgroundColor:'#FFF',
        width:screenWidth - 40,
        borderRadius:20
    },
    innerOrderViewStyle:{
        width:cellWH, 
        height:cellWH, 
        marginLeft:vMargin,
        marginTop:hMargin,
        alignItems:'center', 
        justifyContent:'center',
        backgroundColor:'#FFFFFF'
    },
     titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 18,
        color:'#333333',
        backgroundColor:'#FFF',
        fontWeight:'bold',
        marginLeft:0,
    },
    greyTextStyle: {
        fontSize:14,
        color:'#33333399',
        backgroundColor:'#FFF',
    },
    blackTextStyle: {
        fontSize:15,
        color:'#333333',
    },
    redTextStyle: {
        fontSize:16,
        color:'#E41F00',
        fontWeight:'bold',
    },

    pictureTextStyle: {
        fontSize: 16,
        color:'#AAA'
    },

    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyRowLeftView:{
        width:screenWidth/2-40, 
        flexDirection:'row'
    },
    bodyRowRightView:{
        flexDirection:'row', 
        alignItems:'flex-start',
        paddingLeft:10,
        marginRight:5, 
        justifyContent:'flex-start',
        alignContent:'flex-start'
    },
    searchInputText: {
        width: screenWidth / 1.5,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    inputRowStyle: {
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft:15
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginTop: 15,
        fontSize: 16
    },
    classViewStyle:{
        backgroundColor:'#FFFFFF',
        height:0,
        alignItems:'flex-start',
        justifyContent:'center',
        // borderBottomWidth:1,
        // borderBottomColor:'#E0E0E0'
        position:'absolute',
        top:0
    },
    classTextStyle:{
        color:'#000',
        fontSize:16,
        fontWeight:'bold',
        marginLeft:15
    },
    innerViewImageStyle:{
        width:cellWH-50,
        height:cellWH-50
    },
    wrapper: {
        justifyContent:'center'
    },
    slide: {
        // flex:1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#9DD6EB',
        width:350,
        height:200,
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
    },
    itemContentRightChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
    },
    enterpriseLogoStyle:{
        borderRadius:10,
        width:50,
        height:50,
        marginTop:15
    }

})
module.exports = ProductHome;