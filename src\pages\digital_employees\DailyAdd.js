import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, Alert, TouchableOpacity, Dimensions, Image, KeyboardAvoidingView } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class DailyAdd extends Component {
    constructor() {
        super()
        this.state = {
            operate: "",
            dailyId: "",
            dailyDate: "",
            finishedWork: "",
            unfinishedWork: "",
            dailyState: "",
            requiresCoordinationWork: "",
            workPlan: "",
            selectedDailyDate: [],
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { dailyId } = route.params;
            if (dailyId) {
                console.log("========Edit==dailyId:", dailyId);
                this.setState({
                    dailyId: dailyId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/daily/get";
                loadRequest = { 'dailyId': dailyId };
                httpPost(loadTypeUrl, loadRequest, this.loadDailyDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectedDailyDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    dailyDate: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
    }
    loadDailyDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectedDailyDate = response.data.dailyDate.split("-");
            this.setState({
                dailyId: response.data.dailyId,
                dailyDate: response.data.dailyDate,
                finishedWork: response.data.finishedWork,
                unfinishedWork: response.data.unfinishedWork,
                requiresCoordinationWork: response.data.requiresCoordinationWork,
                workPlan: response.data.workPlan,
                selectedDailyDate: selectedDailyDate,
                dailyState: response.data.dailyState
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    holdDaily = () => {
        console.log("======= holdDaily");
        let toastOpts;
        let url = "/biz/daily/add";
        if (this.state.dailyId) {
            console.log("=========Edit===dailyId", this.state.dailyId)
            url = "/biz/daily/modify";
        }
        let requestParams = {
            dailyId: this.state.dailyId,
            dailyDate: this.state.dailyDate,
            finishedWork: this.state.finishedWork,
            unfinishedWork: this.state.unfinishedWork,
            requiresCoordinationWork: this.state.requiresCoordinationWork,
            workPlan: this.state.workPlan,
            dailyState: "0BB"
        };
        httpPost(url, requestParams, this.holdDailyCallBack);
    }

    // 暂存回调函数
    holdDailyCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    openDailyDate() {
        this.refs.SelectDailyDate.showDate(this.state.selectedDailyDate)
    }

    callBackSelectSelectDailyDateValue(value) {
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedDailyDate: value
        })
        if (value && value.length) {
            var dailyDate = "";
            var vartime;
            for (var index = 0; index < value.length; index++) {
                vartime = value[index];
                if (index === 0) {
                    dailyDate += vartime;
                }
                else {
                    dailyDate += "-" + vartime;
                }
            }
            this.setState({
                dailyDate: dailyDate
            })
        }
    }

    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '日报'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.formContentViewStyle]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>提交日期</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openDailyDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, {borderWidth: 0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.dailyDate ? "请选择预计完成时间" : this.state.dailyDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>完成的工作</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle, { height: 300 }]}>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText, { height: 300, borderWidth: 0 }]}
                            placeholder={'请输入完成的工作'}
                            onChangeText={(text) => this.setState({ finishedWork: text })}
                        >
                            {this.state.finishedWork}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>工作计划</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle, { height: 300 }]}>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText, { height: 300, borderWidth: 0 }]}
                            placeholder={'请输入工作计划'}
                            onChangeText={(text) => this.setState({ workPlan: text })}
                        >
                            {this.state.workPlan}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>未完成工作</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle, { height: 100 }]}>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText, { height: 100, borderWidth: 0 }]}
                            placeholder={'请输入未完成工作'}
                            onChangeText={(text) => this.setState({ unfinishedWork: text })}
                        >
                            {this.state.unfinishedWork}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>需协调工作</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle, { height: 100 }]}>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText, { height: 100, borderWidth: 0 }]}
                            placeholder={'请输入需协调工作'}
                            onChangeText={(text) => this.setState({ requiresCoordinationWork: text })}
                        >
                            {this.state.requiresCoordinationWork}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    {
                        this.state.dailyState != "0AA" ?
                            <View style={styles.btnRowView}>
                                <TouchableOpacity onPress={this.holdDaily.bind(this)}>
                                    <View style={styles.holdbtnView}>
                                        <Text style={styles.holdBtnText}>暂存</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            :
                            <View></View>
                    }
                    <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0, marginTop: 6}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginLeft: 20, width: (screenWidth - 56)/2}]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => {
                            let toastOpts;
                            if (!this.state.finishedWork) {
                                toastOpts = requiredReminder("完成的工作为必填项!");
                                WToast.show(toastOpts)
                                return;
                            }
                            if (!this.state.workPlan) {
                                toastOpts = requiredReminder("工作计划为必填项!");
                                WToast.show(toastOpts)
                                return;
                            }
                            Alert.alert('确认', '提交后不可修改，您确定要提交吗？', [
                                {
                                    text: "取消", onPress: () => {
                                        WToast.show({ data: '点击了取消' });
                                    }
                                },
                                {
                                    text: "确定", onPress: () => {
                                        let url = "/biz/daily/add";
                                        if (this.state.dailyId) {
                                            console.log("=========Edit===dailyId", this.state.dailyId)
                                            url = "/biz/daily/modify";
                                        }
                                        let requestParams = {
                                            dailyId: this.state.dailyId,
                                            dailyDate: this.state.dailyDate,
                                            finishedWork: this.state.finishedWork,
                                            unfinishedWork: this.state.unfinishedWork,
                                            requiresCoordinationWork: this.state.requiresCoordinationWork,
                                            workPlan: this.state.workPlan,
                                            dailyState: "0AA"
                                        };
                                        console.log("=========url:", url)
                                        console.log("=========requestParams:", requestParams)
                                        httpPost(url, requestParams, (response) => {
                                            let toastOpts;
                                            switch (response.code) {
                                                case 200:
                                                    if (this.props.route.params.refresh) {
                                                        this.props.route.params.refresh()
                                                    }
                                                    toastOpts = getSuccessToastOpts('保存完成');
                                                    WToast.show(toastOpts)
                                                    this.props.navigation.goBack()
                                                    break;
                                                default:
                                                    toastOpts = getFailToastOpts(response.message);
                                                    WToast.show({ data: response.message })
                                            }
                                        });
                                    }
                                }
                            ]);
                        }}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView, {marginRight: 20, width: (screenWidth - 56)/2}]}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>提交</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                <BottomScrollSelect
                    ref={'SelectDailyDate'}
                    callBackDateValue={this.callBackSelectSelectDailyDateValue.bind(this)}
                />
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: '#FD6645',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },
    holdbtnView: {
        fontSize: 16, width: 60, height: 30,
        borderWidth: 1,
        borderColor: 'rgba(250, 250, 250, 1)',
        backgroundColor: 'rgba(30, 110, 250, 1)',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 5,
        borderRadius: 4,
        flexDirection: 'row'
    },
    holdBtnText: {
        color: 'rgba(250, 250, 250, 1)', fontSize: 16
    },
    inputLineViewStyle: {
        height:1,
        marginLeft: 13,
        marginRight: 13,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
})