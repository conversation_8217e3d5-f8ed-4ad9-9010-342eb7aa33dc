import React,{Component} from 'react';
import {
    View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../utils/CameraRollUtils';

export default class HLStorageOutAudit extends Component {
    constructor(props) {
        super(props);
        this.state = {
            supplierName:"",
            storageOutDate:"",
            storageOutTypeName:"",            
            operator:"",
            carrier:"",
            recipient:"",
            bookKeeper:"",
            receiptRemark:"",
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,

            storageOutId:"",
            selReplyId:1,
            replyDataSource:[
                {
                    replyId:1,
                    replyType:"同意",
                    replyName:"Y"
                },
                {
                    replyId:2,
                    replyType:"驳回",
                    replyName:"N"
                }
            ],
            selReplyName:"Y",
            recordId:"",
            nodeId:"",
            auditUserId:"",
            auditOpinion:"",
            applyAuditId:"",
            auditTypeCode:"",
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId,recordId,applyAuditId } = route.params;
            if (recordId) {
                let url= "/biz/audit/node/record/get";
                let loadRequest={
                    "recordId": recordId,
                };
                httpPost(url, loadRequest, this.loadAuditRecordCallBack);
            }
        }
    }

    loadAuditRecordCallBack=(response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                departmentName:response.data.storageOutDTOList[0].departmentName,
                storageOutDate:response.data.storageOutDTOList[0].storageOutDate,
                storageOutTypeName:response.data.storageOutDTOList[0].storageOutTypeName,
                bookKeeper:response.data.storageOutDTOList[0].bookKeeper,
                bookKeepingDate:response.data.storageOutDTOList[0].bookKeepingDate,
                operator:response.data.storageOutDTOList[0].operator,
                carrier:response.data.storageOutDTOList[0].carrier,
                recipient:response.data.storageOutDTOList[0].recipient,
                receiptRemark:response.data.storageOutDTOList[0].receiptRemark,
                recordId:response.data.recordId,
                nodeId:response.data.nodeId,
                auditUserId:response.data.auditUserId,
                storageOutId:response.data.storageOutDTOList[0].storageOutId,
                auditTypeCode:response.data.auditTypeCode,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    saveAudit = () => {
        console.log("=======saveAuditConfig");
        let toastOpts;

        let url = "/biz/audit/node/record/storageOutAuditAdd";
        let requestParams = {
            
            "storageOutId":this.state.storageOutId,

            "recordId":this.state.recordId,
            "nodeId":this.state.nodeId,
            "auditUserId":this.state.auditUserId,
            "auditTypeCode":this.state.auditTypeCode,


            "auditResult":this.state.selReplyName,
            "auditOpinion":this.state.auditOpinion,
        };
        httpPost(url, requestParams, this.saveAuditCallBack);
    }

    // 保存回调函数
    saveAuditCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    //sex列表展示
    renderReplyRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selReplyId:item.replyId,
                        selReplyName:item.replyName,
                    })
                }}>
                <View key={item.replyId} style={[item.replyId===this.state.selReplyId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.replyId===this.state.selReplyId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.replyType}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='审核'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库科室</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入出库科室'}
                            onChangeText={(text) => this.setState({departmentName:text})}
                        >
                            {this.state.departmentName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库类型</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入出库类型'}
                            onChangeText={(text) => this.setState({storageOutTypeName:text})}
                        >
                            {this.state.storageOutTypeName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库日期</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入出库日期'}
                            onChangeText={(text) => this.setState({storageOutDate:text})}
                        >
                            {this.state.storageOutDate}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>经办人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入经办人'}
                            onChangeText={(text) => this.setState({operator:text})}
                        >
                            {this.state.operator}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>运送人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入运送人'}
                            onChangeText={(text) => this.setState({carrier:text})}
                        >
                            {this.state.carrier}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>领用人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入领用人'}
                            onChangeText={(text) => this.setState({recipient:text})}
                        >
                            {this.state.recipient}
                        </TextInput>
                    </View>

                    {
                        this.state.bookKeeper ? 
                        <View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>记账人</Text>
                                    {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                </View>
                                <TextInput
                                    editable={false} 
                                    keyboardType='numeric'
                                    style={styles.inputRightText}
                                    placeholder={'请输入记账人'}
                                    onChangeText={(text) => this.setState({bookKeeper:text})}
                                >
                                    {this.state.bookKeeper}
                                </TextInput>
                            </View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>记账日期</Text>
                                    {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                </View>
                                <TextInput 
                                    editable={false} 
                                    keyboardType='numeric'
                                    style={styles.inputRightText}
                                    placeholder={'请输入记账日期'}
                                    onChangeText={(text) => this.setState({bookKeepingDate:text})}
                                >
                                    {this.state.bookKeepingDate}
                                </TextInput>
                            </View>
                        </View>
                        
                    : <View/>
                    }
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>单据备注</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:80}]}>
                        <TextInput 
                            editable={false} 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:80}]}
                            placeholder={'请输入单据备注'}
                            onChangeText={(text) => this.setState({receiptRemark:text})}
                        >
                            {this.state.receiptRemark?this.state.receiptRemark:"无"}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={[styles.rowLabView,{marginRight:30}]}>
                            <Text style={styles.leftLabNameTextStyle}>审核批复</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row',marginLeft:0}}>
                            {
                                (this.state.replyDataSource && this.state.replyDataSource.length > 0) 
                                ? 
                                this.state.replyDataSource.map((item, index)=>{
                                    return this.renderReplyRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>审核意见</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:150}]}
                            placeholder={'请输入审核意见'}
                            onChangeText={(text) => this.setState({auditOpinion:text})}
                        >
                            {this.state.auditOpinion}
                        </TextInput>
                    </View>



                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveAudit.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:18
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputTextStyleTextStyle:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        height:45,
        justifyContent:'center'
    }
});