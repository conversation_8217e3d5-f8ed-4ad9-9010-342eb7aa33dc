import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,KeyboardAvoidingView,TouchableOpacity,Image,Dimensions,Modal} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = (screenWidth - 60) / 2;

export default class VerifyInternalStandardAdd extends Component {
    constructor(){
        super()
        this.state = {
            standardType:"I",
            operate:"",
            standardId:'',
            ordersDataSource:[],
            selBrickTypeId:0,
            selOrderId:0,
            selOrderName:"",
            modal:false,
            searchKeyWord:null,
            spVerifyItemDTOList:[]
        }
    }

    UNSAFE_componentWillMount(){
        console.log('==VerifyStandardAdd==componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { standardId } = route.params;
            if (standardId) {
                console.log("========Edit==standardId:", standardId);
                this.setState({
                    operate:"编辑",
                    standardId:standardId
                })
            }
            else {
                this.setState({
                    operate:"新增",
                })
            }
        }
        this.loadInitData();
    }
    
    componentWillUnmount(){
        console.log('==VerifyStandardAdd==componentWillUnmount');
    }

    loadVerifyStandardDataCallBack=(response)=>{
        console.log("=========loadVerifyStandardDataCallBack===response:", response);
        if (response.code == 200 && response.data) {
            this.setState({
                standardId:response.data.standardId,
                selBrickTypeId:response.data.brickTypeId,
                selOrderId:response.data.orderId,
                selOrderName:response.data.orderName,
                spVerifyItemDTOList:response.data.spVerifyItemDTOList,
            })
        }
    }

    loadInitData=()=>{
        // 加载排产状态的订单，显示砖型
        let loadUrl= "/biz/order/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':1000,
            "display":"Y",
            "qryContent":"order",
            "searchKeyWord":this.state.searchKeyWord,
            "excludeOrderStateList":[
                "A","K"
            ]
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadOrder);
    }

    // 订单回调加载
    callBackLoadOrder=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                WToast.show({data:"没有中途状态订单"});
                return;
            }
            this.setState({
                ordersDataSource:response.data.dataList,
                // selBrickTypeId:response.data.dataList[0] ? response.data.dataList[0].brickTypeId : 0,
                // selOrderId:response.data.dataList[0] ? response.data.dataList[0].orderId : 0,
            })
            let loadUrl;
            let loadRequest;
            const { route, navigation } = this.props;
            if (route && route.params) {
                const { standardId } = route.params;
                if (standardId) {
                    console.log("========Edit==standardId:", standardId);
                    this.setState({
                        operate:"编辑",
                        standardId:standardId
                    })
                    loadUrl= "/biz/verify/standard/get";
                    loadRequest={'standardId':standardId};
                    httpPost(loadUrl, loadRequest, this.loadVerifyStandardDataCallBack);
                }
                else {
                    this.setState({
                        operate:"新增",
                    })
                    loadUrl= "/biz/verify/item/template/list";
                    loadRequest={
                        "standardType": this.state.standardType,
                        "sortWay":"asc",
                        "currentPage": 1,
                        "pageSize": 100000
                    };
                    httpPost(loadUrl, loadRequest, this.loadVerifyStandardTemplateItemCallBack);
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    
    loadVerifyStandardTemplateItemCallBack=(response)=>{
        console.log("=========loadVerifyStandardTemplateItemCallBack===response:", response);
        if (response.code == 200 && response.data && response.data.dataList) {
            var templateItemNameList = response.data.dataList;
            // 遍历取自检要求的值
            var _spVerifyItemDTOList = [];
            var _spVerifyItemDTO;
            templateItemNameList.forEach((item)=>{
                _spVerifyItemDTO = {
                    "itemName":item.templateItemName,
                    "itemRefValue":item.templateItemDefaultValue,
                }
                _spVerifyItemDTOList.push(_spVerifyItemDTO);
            });
            this.setState({
                spVerifyItemDTOList:_spVerifyItemDTOList,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("VerifyInternalStandardList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>标准管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.standardId) {
                    return;
                }
                    this.setState({
                        selBrickTypeId:item.brickTypeId,
                        selOrderId:item.orderId,
                        selOrderName:item.orderName
                    })
                }}>
                <View key={item.orderId} style={[item.orderId===this.state.selOrderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle, this.state.standardId ? CommonStyle.disableViewStyle : ''] }>
                    <Text style={item.orderId===this.state.selOrderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    saveVerifyStandard =()=> {
        console.log("=======saveVerifyStandard");
        let toastOpts;
        if (!this.state.selBrickTypeId || this.state.selBrickTypeId === 0) {
            toastOpts = getFailToastOpts("请选择要入库的砖型");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selOrderId || this.state.selOrderId === 0) {
            toastOpts = getFailToastOpts("请选择要入库的砖型.");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/verify/standard/add";
        if (this.state.standardId) {
            console.log("=========Edit===standardId", this.state.standardId)
            url= "/biz/verify/standard/modify";
        }
        let requestParams={
            "standardType": this.state.standardType,
            "standardId":this.state.standardId,
            "brickTypeId":this.state.selBrickTypeId,
            "orderId": this.state.selOrderId,
            "spVerifyItemDTOList":this.state.spVerifyItemDTOList,
        };
        httpPost(url, requestParams, this.saveVerifyStandardCallBack);
    }
    
    // 保存回调函数
    saveVerifyStandardCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 自检要求
    renderVerifyStandardItemRow=(item, index)=>{
        return (
            <View style={styles.inputRowStyle}>
                <View style={styles.leftLabView}>
                    {
                        (item.sourceBy == 'custome') ?
                        <TextInput 
                            style={[styles.inputLeftText,{width:screenWidth - (leftLabWidth + 80)}]}
                            placeholder={'请输入项目'}
                            onChangeText={(text) => {
                                item.itemName = text
                            }}
                        >
                            {item.itemName}
                        </TextInput>
                        :
                        <Text style={[styles.inputLeftText,{width:screenWidth - (leftLabWidth + 80), borderWidth:0,}]}>
                            {item.itemName}
                        </Text>
                    }
                </View>
                <TextInput 
                    style={[styles.inputRightText,{width:screenWidth - (leftLabWidth + 60)}]}
                    placeholder={'请输入参考值'}
                    onChangeText={(text) => {
                        item.itemRefValue = text
                    }}
                >
                    {item.itemRefValue}
                </TextInput>
                <View style={{position: 'absolute', right: -50, width: 50, height: 50, justifyContent:'center', }}>
                    <TouchableOpacity onPress={()=>{
                        var _spVerifyItemDTOList = this.state.spVerifyItemDTOList;
                        _spVerifyItemDTOList.splice(index, 1);
                        this.setState({
                            spVerifyItemDTOList:_spVerifyItemDTOList
                        })
                    }}>
                    <View style={{width: 35,height: 35, borderRadius:35, marginLeft:5, alignItems:'center', justifyContent:'center' , backgroundColor:'red'}}>
                        <Text style={{color:'#FFFFFF', fontWeight:'bold', fontSize:20 }}>X</Text>
                    </View>
                    </TouchableOpacity>
                </View>
                
            </View>
        )
    }


    render(){
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '标准'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    
                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>订单名称</Text>
                    </View>

                    <View style={[{flexWrap:'wrap',marginLeft:15}, this.state.standardId? CommonStyle.disableViewStyle : null]}>
                        <TouchableOpacity onPress={()=>{
                            if(this.state.standardId) {
                                return;
                            }
                            this.setState({ 
                                modal:true,
                            })

                            if (!this.state.selOrderId && this.state.ordersDataSource && this.state.ordersDataSource.length > 0) {
                                this.setState({
                                    selOrderId:this.state.ordersDataSource[0].orderId,
                                    selOrderName:this.state.ordersDataSource[0].orderName,
                                    selBrickTypeId:this.state.ordersDataSource[0].brickTypeId,
                                })
                            }
                        }}>
                            <View style={[this.state.selOrderId && this.state.selOrderName ?
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    :
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        paddingLeft:6,
                                        paddingRight:6,
                                        // width: (screenWidth - 54)/2,
                                        borderRadius: 4,
                                    }
                                ]}>
                                    <Text style={[this.state.selOrderId && this.state.selOrderName ?
                                        {
                                            color: '#1E6EFA'
                                        }
                                        :
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>
                                        {this.state.selOrderId && this.state.selOrderName ? ('选择订单：'+ this.state.selOrderName) : '选择订单'}
                                    </Text>
                                </View>
                        </TouchableOpacity>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput 
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({searchKeyWord:text})}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={()=>{
                                        this.loadInitData();
                                        }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{flexDirection:'row', flexWrap:'wrap', overflow:'scroll'}}>
                                    {
                                        (this.state.ordersDataSource && this.state.ordersDataSource.length > 0) 
                                        ? 
                                        this.state.ordersDataSource.map((item, index)=>{
                                            if (index < 1000) {
                                                return this.renderRow(item)
                                            }
                                        })
                                        : <EmptyRowViewComponent/> 
                                    }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                    <TouchableOpacity onPress={() => { 
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selOrderId) {
                                            let toastOpts = getFailToastOpts("您还没有选择订单");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            
                        </View>
                        <View>

                        </View>
                    </Modal>
                    
                    {/* <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.ordersDataSource && this.state.ordersDataSource.length > 0) 
                            ? 
                            this.state.ordersDataSource.map((item, index)=>{
                                return this.renderRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View> */}
                    
                    <View style={[styles.rowLabView,{backgroundColor:'#F5F5F5', justifyContent:'center', paddingLeft:0}]}>
                        <Text style={[styles.leftLabNameTextStyle,{fontWeight:'bold'}]}>自检要求</Text>
                    </View>

                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.spVerifyItemDTOList && this.state.spVerifyItemDTOList.length > 0) 
                            ? 
                            this.state.spVerifyItemDTOList.map((item, index)=>{
                                return this.renderVerifyStandardItemRow(item, index)
                            })
                            : <EmptyRowViewComponent title="请新增自检要求"/> 
                        }
                    </View>

                    <View style={styles.btnRowView}>
                        <TouchableOpacity onPress={()=>{
                            var _spVerifyItemDTOList = this.state.spVerifyItemDTOList;
                            var spVerifyItemDTO = {
                                "itemName": "",
                                "itemRefValue": "",
                                "sourceBy":"custome"
                            };
                            _spVerifyItemDTOList.push(spVerifyItemDTO);
                            this.setState({
                                spVerifyItemDTOList:_spVerifyItemDTOList
                            })
                            console.log("=======_spVerifyItemDTOList:", _spVerifyItemDTOList);

                        }}>
                            <View style={[styles.btnAddView,{marginBottom:5,backgroundColor: '#F2F5FC'}]}>
                                <Text style={[styles.btnAddText,{color: '#404956'}]}>+新增标准</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnAddCancelBtnView} >
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveVerifyStandard.bind(this)}>
                            <View style={CommonStyle.btnAddSaveBtnView}>
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#1E6EFA', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'#E63633',
        marginLeft:4,
        marginRight:3
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:4,
        marginRight:3,
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#FFFFFF',
        // borderWidth:1,
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
    },
})