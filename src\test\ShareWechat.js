import React from 'react'
import {
    Text,
    View,
    ScrollView,
    TouchableOpacity,
    Dimensions,
    Image,
    Alert,
    StyleSheet,
} from 'react-native';
import * as WeChat from 'react-native-wechat-lib';
// WeChat.registerApp(constants.wechatShareAppId, constants.wechatUniversalLink);
import { WToast } from 'react-native-smart-tip';

import '../utils/Global';

import OButton from '../component/OButton';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class ShareWechat extends React.Component {

    constructor(props) {
        super(props);
        this.state = {
            errorMessage: null,
        }
    }
    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        console.log('wechatShareAppId', constants.wechatShareAppId);
        console.log('wechatUniversalLink', constants.wechatUniversalLink);
    }

    render() {
        return (
            <ScrollView style={{ width: screenWidth, height: screenHeight, overflow: 'scroll', flexDirection: 'column', backgroundColor: '#fff000' }}>
                <View style={{ alignItems: 'center', justifyContent: 'center', }}>
                    <View style={{ flexDirection: 'row', marginTop: 50 }}>
                        <View >
                            <View>
                                <TouchableOpacity
                                    activeOpacity={.8}
                                    onPress={() => {
                                        console.log("======登录")
                                        httpPost("/biz/user/login", { "userCode": "test", "userPwd": "1234567" }, (resp) => {
                                            console.log("======resp", resp)
                                            if (resp.code === 200) {
                                                WToast.show({ data: "登录成功" });
                                            }
                                            else {
                                                WToast.show({ data: resp.message });
                                            }
                                        })
                                    }}
                                >
                                    <View style={{ width: screenWidth * 0.35, height: 50, alignItems: 'center', justifyContent: 'center', backgroundColor: 'red', borderRadius: 30 }}>
                                        <Text style={{ color: '#000000' }}>
                                            登录
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>

                        <View>
                            <View>
                                <TouchableOpacity
                                    activeOpacity={.8}
                                    onPress={() => {
                                        httpGet("/biz/user/logout", (resp) => {
                                            console.log("======resp", resp)
                                            if (resp.code === 200) {
                                                WToast.show({ data: "登出" });
                                            }
                                            else {
                                                WToast.show({ data: resp.message });
                                            }
                                        })
                                    }}
                                >
                                    <View style={{ width: screenWidth * 0.35, height: 50, marginLeft: 10, alignItems: 'center', justifyContent: 'center', backgroundColor: 'red', borderRadius: 30 }}>
                                        <Text style={{ color: '#000000' }}>
                                            登出
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    <View>
                        <Text>
                            ErrorMessage:{this.state.errorMessage}
                        </Text>
                    </View>

                    <View style={{ margin: 20 }}>
                        <Text style={styles.welcome}>
                            微信分享
                        </Text>
                        <OButton text='是否安装了微信'
                            onPress={() => {
                                WeChat.isWXAppInstalled()
                                    .then((isInstalled) => {
                                        if (isInstalled) {
                                            WToast.show({ data: "安装了" });
                                        }
                                        else {
                                            WToast.show({ data: "没安装" });
                                        }
                                    })
                                    .catch((err) => {
                                        WToast.show({ data: "==err:" + JSON.stringify(err) });
                                    });
                            }}
                        />
                        <OButton text='检查支持情况'
                            onPress={() => {
                                WeChat.isWXAppSupportApi()
                                    .then((isInstalled) => {
                                        if (isInstalled) {
                                            WToast.show({ data: "支持" });
                                        }
                                        else {
                                            WToast.show({ data: "不支持" });
                                        }
                                    })
                                    .catch((err) => {
                                        WToast.show({ data: "==err:" + JSON.stringify(err) });
                                    });
                            }}
                        />
                        <OButton text='获取 API 版本号'
                            onPress={() => {
                                WeChat.getApiVersion()
                                    .then((apiVersion) => {
                                        WToast.show({ data: "版本号：" + apiVersion });
                                    })
                                    .catch((err) => {
                                        WToast.show({ data: "==err:" + JSON.stringify(err) });
                                    });
                            }}
                        />
                        <OButton text='打开微信'
                            onPress={() => {
                                WeChat.openWXApp()
                                    .then((isOpenWxApp) => {
                                        if (isOpenWxApp) {
                                            WToast.show({ data: "打开了" });
                                        }
                                        else {
                                            WToast.show({ data: "没打开" });
                                        }
                                    })
                                    .catch((err) => {
                                        WToast.show({ data: "==err:" + err });
                                    });
                            }}
                        />
                        <OButton text='分享文本'
                            onPress={() => {
                                WeChat.shareText({ text: '测试微信好友分享的文本内容-极致学社', scene: 0 })
                                    .then((respJSON) => {
                                        WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                    })
                                    .catch((err) => {
                                        WToast.show({ data: "respJSON==err" + err });
                                    });
                            }}
                        />
                        <OButton text='微信好友分享链接'
                            onPress={() => {
                                WeChat.shareWebpage({
                                    title: '微信好友测试的链接-极致学社',
                                    description: '分享的标题内容-极致学社',
                                    thumbImage: 'https://image.njjzgk.com/image_a/10/2021-12/rn_image_picker_lib_temp_e2913d29-903a-450b-ac9a-7bf51effe959_060917917_small.jpg',
                                    webpageUrl: 'https://jzxs.njjzgk.com/index.html',
                                    scene: 0
                                })
                                    .then((respJSON) => {
                                        WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                    })
                                    .catch((error) => {
                                        WToast.show({ data: error });
                                        Alert.alert(error.message);
                                    });
                            }}
                        />
                        <OButton text='微信朋友圈分享的文本'
                            onPress={() => {
                                WeChat.shareText({ text: '测试微信好友分享的文本内容-极致学社', scene: 1 })
                                    .then((respJSON) => {
                                        WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                    })
                                    .catch((error) => {
                                        WToast.show({ data: error });
                                        Alert.alert(error.message);
                                    });
                            }}
                        />
                        <OButton text='微信朋友圈分享的链接'
                            onPress={() => {
                                WeChat.shareWebpage({
                                    title: '分享的标题-极致学社',
                                    description: '分享的标题内容-极致学社',
                                    thumbImage: 'https://image.njjzgk.com/image_a/10/2021-12/rn_image_picker_lib_temp_e2913d29-903a-450b-ac9a-7bf51effe959_060917917_small.jpg',
                                    webpageUrl: 'https://jzxs.njjzgk.com/index.html',
                                    scene: 1
                                })
                                    .then((respJSON) => {
                                        WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                    })
                                    .catch((error) => {
                                        WToast.show({ data: error });
                                        Alert.alert(error.message);
                                    });
                            }}
                        />

                        <OButton text='微信支付'
                            onPress={() => {
                                WeChat.isWXAppInstalled()
                                    .then((isInstalled) => {
                                        if (isInstalled) {
                                            WeChat.pay({
                                                partnerId: 'xxxxxx',  // 商家向财付通申请的商家id
                                                prepayId: 'xxxxxx',   // 预支付订单
                                                nonceStr: 'xxxxxx',   // 随机串，防重发
                                                timeStamp: 'xxxxxxx',  // 时间戳，防重发.
                                                package: 'Sign=WXPay',    // 商家根据财付通文档填写的数据和签名
                                                sign: 'xxxxxxxxx'       // 商家根据微信开放平台文档对数据做的签名
                                            }).then((requestJson) => {
                                                //支付成功回调                                           
                                                if (requestJson.errCode == "0") {
                                                    //回调成功处理
                                                }
                                            }).catch((err) => {
                                                Alert.alert('支付失败')
                                            })
                                        } else {
                                            Alert.alert('请安装微信');
                                        }
                                    });

                            }}
                        />
                    </View>

                </View>
            </ScrollView>
        )
    }
}

const styles = StyleSheet.create({
    welcome: {
        backgroundColor: '#F5F5F5',
        padding: 10,
    },
});
