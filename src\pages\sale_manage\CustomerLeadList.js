import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    Image,TextInput,FlatList,RefreshControl,Modal,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import BottomScrollSelect from '../../component/BottomScrollSelect';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class CustomerLeadList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            initGmtCreated: null,
            selectGmtCreated:null,
            searchKeyWord: "",
            topBlockLayoutHeight: 0,
            year:null,
            selFlagChooseCode:'all',
            display:"N" ,
            flagChooseDataSource:[],
            modal:false,
            detailDTO:{},
            gmtCreated:null,
            detailModal:false
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // const { route, navigation } = this.props;
        // if (route && route.params) {
        //     const { tenantId } = route.params;
        //     if (tenantId) {
        //         console.log("=============tenantId" + tenantId + "");
        //     }
        // }
        let flagChooseDataSource = [
            {
                chooseCode:'all',
                chooseName:'全部',
            },
            {
                chooseCode:'Y',
                chooseName:'已审核',
            },
            {
                chooseCode:'N',
                chooseName:'未审核',
            },
        ]
        this.setState({
            flagChooseDataSource:flagChooseDataSource,
        })
        var _gmtCreated = this.initGmtCreated();
        this.loadCustomerLeadList(_gmtCreated);
    }

    initGmtCreated=()=>{
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 3);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectGmtCreated: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            gmtCreated: _gmtCreated,
            initGmtCreated: _gmtCreated,
        })
        return _gmtCreated;
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/customer/lead/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditFlag": this.state.selFlagChooseCode === 'all' ? null : this.state.selFlagChooseCode,
            "searchKeyWord": this.state.searchKeyWord,
            "gmtCreated":this.state.gmtCreated
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.gmtCreated === this.state.initGmtCreated) {
            return;
        }
        var _gmtCreated = this.initGmtCreated();
        this.setState({
            gmtCreated: _gmtCreated,
        })
        this.setState({
            currentPage: 1
        })
        let url = "/biz/customer/lead/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditFlag": this.state.selFlagChooseCode === 'all' ? null : this.state.selFlagChooseCode,
            "searchKeyWord": this.state.searchKeyWord,
            "gmtCreated": _gmtCreated,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadCustomerLeadList();
    }

    loadCustomerLeadList = (_gmtCreated) => {
        let url = "/biz/customer/lead/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "auditFlag": this.state.selFlagChooseCode === 'all' ? null : this.state.selFlagChooseCode,
            "searchKeyWord": this.state.searchKeyWord,
            "gmtCreated": _gmtCreated ? _gmtCreated : this.state.gmtCreated
        };
        httpPost(url, loadRequest, this.loadCustomerLeadListCallBack);
    }

    loadCustomerLeadListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/customer/lead/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditFlag": this.state.selFlagChooseCode === 'all' ? null : this.state.selFlagChooseCode,
            "searchKeyWord": this.state.searchKeyWord,
            "gmtCreated":this.state.gmtCreated
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    deleteCustomerLead = (leadId) => {
        console.log("=======delete=customerLeadId", leadId);
        let url = "/biz/customer/lead/delete";
        let requestParams = { 'leadId': leadId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    // modifyContractState = (contractId, contractState) => {
    //     console.log("=======delete=contractId", contractId);
    //     let url = "/biz/contract/modify";
    //     let requestParams = { 'contractId': contractId, 'contractState': contractState };
    //     httpPost(url, requestParams, this.modifyContractStateCallBack);
    // }

    // // 修改状态操作的回调操作
    // modifyContractStateCallBack = (response) => {
    //     if (response.code == 200 && response.data) {
    //         WToast.show({ data: "状态修改完成" });
    //         this._loadFreshData();
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    //     else {
    //         WToast.show({ data: response.message });
    //     }
    // }
    
    renderRow = (item, index) => {
        return (
            <View key={item.leadId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle]}>客户名称：{item.customerName}</Text>
                    {
                        item.auditFlag==='N'?
                        <Text style={{color:"#bfbfbf"}}>未审核</Text>
                        :
                        <View>
                        {
                            item.auditResult==="放弃" ?
                            <Text style={{color:"#CB4139"}}>{item.auditResult}</Text>
                            :
                            <Text style={{color:"#F2C16D"}}>{item.auditFlag==='N'?"未审核":item.auditResult}</Text>
                        }
                        </View>
                    }
                    {/* {
                        item.claimUserId ? 
                        // (
                        //     item.claimUserId == constants.loginUser.userId ? 
                        //     <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>认领</Text>
                        //     :
                        //     <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>已被认领</Text>
                        // )
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>已被认领</Text>
                        :
                        <Text></Text>
                    } */}
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系人：{item.contactPerson}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>职务：{item.jobTitle}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>联系电话：{item.contactTel}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>审核结果：{item.auditFlag==='N'?"未审核":item.auditResult}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>提交人：{item.userName}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>提交时间：{item.gmtCreated}</Text>
                </View>
                {
                    item.claimUserId ?
                    <View>
                        <View style={[styles.titleViewStyle]}>
                            <Text style={styles.titleTextStyle}>认领人：{item.claimUserName}</Text>
                        </View>
                        <View style={[styles.titleViewStyle]}>
                            <Text style={styles.titleTextStyle}>认领时间：{item.claimTime}</Text>
                        </View>
                    </View>
                    :
                    <View/>
                }
                
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>  
                        {
                            item.auditFlag!="N" || item.claimUserId ?
                            <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                <TouchableOpacity onPress={()=>{
                                    let loadUrl = "/biz/customer/lead/detail";
                                    let loadRequest = {
                                        "leadId": item.leadId,
                                    };
                                    httpPost(loadUrl, loadRequest, (response)=>{
                                        if (response.code == 200 && response.data) {
                                            this.setState({
                                                detailDTO: response.data,
                                                detailModal:true
            
                                            })
                                        }
                                
                                    });
                                }}>
                                    <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {backgroundColor:"#3ab240", width: 75 ,flexDirection:"row"}]}>
                                    <Image  style={{width:25, height:25,marginRight:3}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            :
                            <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                
                                {
                                    item.userFlag == 'Y' ?
                                    <View style={{flexDirection:'row'}}>
                                        <TouchableOpacity onPress={()=>{
                                            Alert.alert('确认','您确定要删除该线索吗？',[
                                                {
                                                    text:"取消", onPress:()=>{
                                                    WToast.show({data:'点击了取消'});
                                                    // this在这里可用，传到方法里还有问题
                                                    // this.props.navigation.goBack();
                                                    }
                                                },
                                                {
                                                    text:"确定", onPress:()=>{
                                                        WToast.show({data:'点击了确定'});
                                                        this.deleteCustomerLead(item.leadId)
                                                    }
                                                }
                                            ]);
                                        }}>
                                            <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:80,flexDirection:'row'}]}>
                                                <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                                                <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                            </View>
                                        </TouchableOpacity>
                                        <TouchableOpacity onPress={()=>{
                                                this.props.navigation.navigate("CustomerLeadAdd", 
                                                {
                                                    // 传递参数
                                                    leadId:item.leadId,
                                                    // 传递回调函数
                                                    refresh: this.callBackFunction 
                                                })
                                            }}>
                                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:'row'}]}>
                                                <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    : <View/>
                                }
                                
                                <TouchableOpacity onPress={()=>{
                                    let loadUrl = "/biz/customer/lead/detail";
                                    let loadRequest = {
                                        "leadId": item.leadId,
                                    };
                                    httpPost(loadUrl, loadRequest, (response)=>{
                                        if (response.code == 200 && response.data) {
                                            this.setState({
                                                detailDTO: response.data,
                                                detailModal:true
            
                                            })
                                        }
                                
                                    });
                                }}>
                                    <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {backgroundColor:"#3ab240", width:75 ,flexDirection:"row"}]}>
                                        <Image  style={{width:25, height:25,marginRight:3}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        }
                    </View>
                    
            </View>
        )
    }
    
    emptyComponent() {
        return <EmptyListComponent />
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("CustomerLeadAdd",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    flagChooseStateRow=(item, index)=>{
        return (
            <View key={item.chooseCode} >
                <TouchableOpacity onPress={()=>{
                    var selFlagChooseCode = item.chooseCode;
                    this.setState({
                        selFlagChooseCode:selFlagChooseCode
                    })

                    let loadUrl= "/biz/customer/lead/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "auditFlag": item.chooseCode === 'all' ? null : item.chooseCode,
                        "searchKeyWord": this.state.searchKeyWord,
                        "gmtCreated":this.state.gmtCreated
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.chooseCode} style={[item.chooseCode===this.state.selFlagChooseCode? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle,{paddingLeft:8,paddingRight:8}]}>
                        <Text style={[item.chooseCode===this.state.selFlagChooseCode? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                            {item.chooseName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }
    openGmtCreated(){
        this.refs.SelectGmtCreated.showDate(this.state.selectGmtCreated)
    }

    callBackSelectGmtCreatedValue(value){
        console.log("==========时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectGmtCreated:value
        })
        if (this.state.selectGmtCreated && this.state.selectGmtCreated.length) {
            var _gmtCreated = "";
            var vartime;
            for(var index=0;index<this.state.selectGmtCreated.length;index++) {
                vartime = this.state.selectGmtCreated[index];
                if (index===0) {
                    _gmtCreated += vartime;
                }
                else if (index < 3){
                    _gmtCreated += "-" + vartime;
                }
                else if (index===3){
                    _gmtCreated += " " + vartime;
                }
                else {
                    _gmtCreated += ":" + vartime;
                }
            }
            this.setState({
                currentPage: 1,
                gmtCreated:_gmtCreated
            })
            let url= "/biz/customer/lead/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "auditFlag": this.state.selFlagChooseCode === 'all' ? null : this.state.selFlagChooseCode,
                "searchKeyWord": this.state.searchKeyWord,
                "gmtCreated": _gmtCreated,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='线索管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.rightTop50FloatingBlockView,this.state.gmtCreated 
                    ? {borderRadius:3, width:null,height: 40,top:screenHeight/45, marginTop: 50,paddingLeft:15, paddingRight:15, opacity:0.5} : {}]}>
                    <TouchableOpacity onPress={()=>this.openGmtCreated()}>
                        <Text style={CommonStyle.rightTop50FloatingBlockText}>
                        {!this.state.gmtCreated ? "时间" : this.state.gmtCreated}
                        </Text>
                    </TouchableOpacity>
                </View>
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>

                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.flagChooseDataSource && this.state.flagChooseDataSource.length > 0)
                                ?
                                this.state.flagChooseDataSource.map((item, index) => {
                                    return this.flagChooseStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                    
                    <View style={{}}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                                <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'客户名称/提交人'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                            {/* <TouchableOpacity onPress={() => {
                                this.searchByKeyWord();
                            }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 70 }]}>
                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>查询</Text>
                                </View>
                            </TouchableOpacity> */}
                        </View>
                        {/* <View style={{ height: 5, backgroundColor: '#FFFFFF' }}></View> */}
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                        onEndReachedThreshold={1}
                    />
                </View>
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.detailModal}
                    >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle,{height:40, backgroundColor:'#3ab240',borderRadius:5, justifyContent:'center',alignItems:'center',marginTop:10}]}>
                                <Text style={[styles.titleTextStyle,{fontSize:18,fontWeight:'bold',color:"#ffffff"}]}>线索详情</Text>
                            </View>
                            <ScrollView>
                                {
                                    this.state.detailDTO.customerLeadDTOList && this.state.detailDTO.customerLeadDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>线索</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>客户名称：{this.state.detailDTO.customerName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.customerLeadDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职务：{this.state.detailDTO.customerLeadDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.customerLeadDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>客户简介：{this.state.detailDTO.customerLeadDTOList[0].customerSummary?this.state.detailDTO.customerLeadDTOList[0].customerSummary:"无"}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>备注说明：{this.state.detailDTO.customerLeadDTOList[0].remark?this.state.detailDTO.customerLeadDTOList[0].remark:"无"}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.customerLeadDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.customerLeadDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                {
                                    this.state.detailDTO.auditLeadOperateDTOList && this.state.detailDTO.auditLeadOperateDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>审核</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.auditLeadOperateDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职务：{this.state.detailDTO.auditLeadOperateDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.auditLeadOperateDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>审核结果：{this.state.detailDTO.auditLeadOperateDTOList[0].operateResultName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>审核意见：{this.state.detailDTO.auditLeadOperateDTOList[0].operateOpinion}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.auditLeadOperateDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.auditLeadOperateDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                {
                                    this.state.detailDTO.telLeadOperateDTOList && this.state.detailDTO.telLeadOperateDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>电邀</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.telLeadOperateDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职务：{this.state.detailDTO.telLeadOperateDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.telLeadOperateDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>电邀结果：{this.state.detailDTO.telLeadOperateDTOList[0].operateResultName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>电邀意见：{this.state.detailDTO.telLeadOperateDTOList[0].operateOpinion}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.telLeadOperateDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.telLeadOperateDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                {
                                    this.state.detailDTO.visitLeadOperateDTOList && this.state.detailDTO.visitLeadOperateDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>拜访</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.visitLeadOperateDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职务：{this.state.detailDTO.visitLeadOperateDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.visitLeadOperateDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>拜访结果：{this.state.detailDTO.visitLeadOperateDTOList[0].operateResultName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>拜访意见：{this.state.detailDTO.visitLeadOperateDTOList[0].operateOpinion}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.visitLeadOperateDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.visitLeadOperateDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                
                                {
                                    this.state.detailDTO.slSaleOpportunityDTOList && this.state.detailDTO.slSaleOpportunityDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>商机</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>商机名称：{this.state.detailDTO.slSaleOpportunityDTOList[0].opportunityName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.slSaleOpportunityDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.slSaleOpportunityDTOList[0].gmtCreated}</Text>
                                        </View>
                                        {
                                            this.state.detailDTO.slSaleOpportunityDTOList[0].slSaleOpportunityTrackDTOList && this.state.detailDTO.slSaleOpportunityDTOList[0].slSaleOpportunityTrackDTOList.length > 0
                                            ?
                                            <View>
                                                {
                                                    this.state.detailDTO.slSaleOpportunityDTOList[0].slSaleOpportunityTrackDTOList.map((trackItem,index)=>{
                                                        return(
                                                            <View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0,fontWeight:'bold'}]}>进展{index + 1}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>联系人：{trackItem.contactPerson}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>职务：{trackItem.contactPerson}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>联系电话：{trackItem.contactTel}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>进展说明：{trackItem.trackExplain}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>提交人：{trackItem.userName}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>提交时间：{trackItem.gmtCreated}</Text>
                                                                </View>
                                                            </View>
                                                        )
                                                    })
                                                }
                                                
                                            </View>
                                            :
                                            <View>
                                            </View>
                                        }
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }

                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    
                                    this.setState({
                                        detailModal:false
                                    })
                                }}>
                                <View style={[styles.btnRowLeftCancelBtnView]} >
                                {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                    <Text  style={[styles.titleTextStyle,{ fontWeight: 'bold',fontSize:18,color:'#a1a1a1'}]}>返       回</Text>
                                </View>
                                </TouchableOpacity>  
                            </View>
                        </View>
                    </View>
                </Modal>
                <BottomScrollSelect 
                    ref={'SelectGmtCreated'} 
                    callBackDateValue={this.callBackSelectGmtCreatedValue.bind(this)}
                />
            </View>
        )
    }
}
        
const styles = StyleSheet.create({
     // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle: {
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        // width:screenWidth - 180,
        marginLeft:20
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    btnRowLeftCancelBtnView:{
        flexDirection:'row',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
        alignItems:'center',
        justifyContent:'center',
        borderWidth:1,
        borderColor:'#a1a1a1',
        borderRadius:5,
        height:40,
    },
});