import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, ImageBackground,
    FlatList, RefreshControl, Image,Modal,KeyboardAvoidingView,TextInput,ScrollView,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
export default class MyBacklogDailyList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            standardType: "E",
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            auditModal:false,
            auditScore:null,
            auditOpinion:null,
            dailyItem: {},
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        this.loadDailyList();
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/daily/audit/task/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "my_backlog": "Y",
            "dailyState": "0AA"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/daily/audit/task/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "my_backlog": "Y",
            "dailyState": "0AA"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadDailyList();
    }

    loadDailyList = () => {
        let url = "/biz/daily/audit/task/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "my_backlog": "Y",
            "dailyState": "0AA"
        };
        httpPost(url, loadRequest, this.loadDailyListCallBack);
    }

    loadDailyListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    auditDaily =()=> {
        console.log("=======auditDaily");
        let toastOpts;
        if (!this.state.auditScore && this.state.auditScore !==0) {
            toastOpts = getFailToastOpts("请输入审核得分");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/daily/audit";
        let requestParams={
            dailyId: this.state.dailyItem.dailyId,
            auditScore: this.state.auditScore,
            auditOpinion: this.state.auditOpinion,
        };
        httpPost(url, requestParams, this.saveAuditCallBack);
    }
    
    // 保存回调函数
    saveAuditCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('审核完成');
                WToast.show(toastOpts);
                this.callBackFunction();
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    renderRow = (item, index) => {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("DailyDetail", {
                    // 传递参数
                    dailyId: item.dailyId,
                    userName: item.syDailyDTO.userName,
                    my_backlog: "Y",
                    // 传递回调函数
                    refresh: this.callBackFunction
                })
            }}>

            <View key={item.taskId} style={[CommonStyle.innerViewStyle]}>
                {/* 日报顶部信息 */}
                <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
                    {
                        item.syDailyDTO.userPhoto ?
                            <Image source={{uri: (constants.image_addr + '/' + item.syDailyDTO.userPhoto)}} style={{ height: 48, width: 48, borderRadius: 50}} />
                            :
                            <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                    {
                                        item.syDailyDTO.userName.length <= 2 ? 
                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {item.syDailyDTO.userName}
                                        </Text>
                                        :
                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {item.syDailyDTO.userName.slice(-2)}
                                        </Text>
                                    }
                                </View>
                            </ImageBackground>
                    }
                    
                    <View style={{marginLeft:11, flexDirection: 'column'}}>
                        <View style={{flexDirection: 'row', marginTop: 4 }}>
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={{ fontSize: 16 }}>{item.syDailyDTO.userName}的日报</Text>
                            </View>
                            <View style={{ width: 52, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#FF8C28' }}>
                                <Text style={{fontSize: 13, color: '#FFFFFF' }}>待审核</Text>
                            </View>
                        </View>

                        <View style={{flexDirection: 'row'}}>
                        <Image style={{ height: 13 , width: 12, marginTop: 5, marginLeft: 1, marginRight: 5}} source={require('../../assets/icon/iconfont/clock.png')}></Image> 
                            <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                                <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)' }]}>{item.syDailyDTO.dailyDate} 提交</Text>
                            </View>
                        </View>
                    </View>
                </View>

                {/* 分隔线 */}
                <View style={styles.lineViewStyle}/>

                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>完成的工作</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.itemContentStyle}>{item.syDailyDTO.finishedWork}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>工作计划</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.itemContentStyle}>{item.syDailyDTO.workPlan}</Text>
                </View>
                {
                    item.syDailyDTO.unfinishedWork ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>未完成工作</Text>
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.itemContentStyle}>{item.syDailyDTO.unfinishedWork}</Text>
                            </View>
                        </View>
                        :
                        <View></View>
                }
                {
                    item.syDailyDTO.requiresCoordinationWork ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>需协调工作</Text>
                            </View>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.itemContentStyle}>{item.syDailyDTO.requiresCoordinationWork}</Text>
                            </View>
                        </View>
                        :
                        <View></View>
                }
                
                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                    <TouchableOpacity onPress={() => {
                        // 触发-快捷审核弹窗modal
                        this.setState({
                            auditModal:true,
                            dailyItem: item
                        })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 65,backgroundColor: "#1E6EFA", flexDirection: "row", marginRight: 16, borderRadius: 3 }
                            , item.auditScore ? CommonStyle.disableViewStyle : ""]}>
                            <Image style={{ width: 16, height: 18, marginRight: 2, marginRight: 3 }} source={require('../../assets/icon/iconfont/audit2.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>审核</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                
            </View>
            </TouchableOpacity>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='我的待办'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                
                {/* 审核快捷操作Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.auditModal}
                 //  onShow={this.onShow.bind(this)}
                    onRequestClose={() =>console.log('onRequestClose...')}
                >
                    <KeyboardAvoidingView
                        behavior={Platform.OS == "ios" ? "padding" : "height"}
                        style={[{backgroundColor: 'rgba(0,0,0,0.64)' ,flex:1}]}
                    >
                        <View style={{flex:1}}>
                            <ScrollView style={{ width: screenWidth, height: 360, bottom: 0, position: 'absolute', backgroundColor: '#FFFFFF', borderTopLeftRadius: 10,borderTopRightRadius: 10}}>
                                    <View style={{ height: 50, justifyContent: 'center', alignItems: 'center' }}>
                                        <Text style={{ fontSize: 18 }}>确认审核</Text>
                                    </View>
                                    <View style={{flexDirection:'row', height:50, borderBottomWidth: 0.8, borderColor:'#E8E9EC'}}>
                                        <View style={styles.leftLabView}>
                                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                                <Text style={styles.leftLabNameTextStyle}>审核得分</Text>
                                        </View>
                                        <TextInput 
                                            keyboardType='numeric'
                                            style={styles.inputRightText}
                                            placeholder={'请输入审核得分'}
                                            onChangeText={(text) => this.setState({ auditScore: text})}
                                        />
                                    </View>
                                    
                                    <View style={{flexDirection:'row',marginTop:6}}>
                                        <View style={{ width:leftLabWidth,height:30,flexDirection:'row',alignItems:'center',paddingLeft:10,}}>
                                            <Text style={{fontSize:18,marginLeft:5,}}>审核意见</Text>
                                        </View>
                                    </View>
                                    <View style={{flexDirection:'row',height:120,borderBottomWidth: 0.8,borderColor:'#E8E9EC'}}>
                                        <TextInput 
                                            multiline={true}
                                            textAlignVertical="top"
                                            style={[CommonStyle.inputRowText, {height:100, borderWidth: 0}]}
                                            placeholder={'请输入审核意见'}
                                            onChangeText={(text) => this.setState({auditOpinion:text})}
                                        />
                                    </View>

                                    <View style={[CommonStyle.btnRowStyle, {width:screenWidth, marginLeft: 0, marginTop: 6}]}>
                                        <TouchableOpacity 
                                            onPress={() => { 
                                                this.setState({
                                                    auditModal: false,
                                                    auditScore: null
                                                })
                                        }}>
                                            <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginLeft: 20, width: (screenWidth - 56)/2}]} >
                                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                                            </View>
                                        </TouchableOpacity>
                                        <TouchableOpacity onPress={() => {
                                            if (!this.state.auditScore && this.state.auditScore !==0) {
                                                let toastOpts = getFailToastOpts("请输入审核得分");
                                                WToast.show(toastOpts)
                                                return;
                                            }
                                            this.setState({
                                                auditModal: false,
                                            })
                                            this.auditDaily();
                                        }}>
                                            <View style={[CommonStyle.btnRowRightSaveBtnView, {marginRight: 20, width: (screenWidth - 56)/2}]}>
                                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确认</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                            </ScrollView>
                            </View>
                    </KeyboardAvoidingView>
                </Modal>

                <View style={CommonStyle.contentViewStyle}>
                    <FlatList
                        data={this.state.dataSource}
                        keyExtractor={(item) => item.taskId}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 14,
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentTextStyle: {
        marginLeft: 12,
        marginRight: 16,
        marginTop: 3,
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 14,
        marginRight: 16,
        marginTop: 5,
        alignItems:'center'
    },
    titleTextStyle: {
        fontSize: 16,
        lineHeight: 24
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        marginBottom: 6,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    leftLabNameTextStyle:{
        fontSize:18,
        marginLeft:5,
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        // color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
});