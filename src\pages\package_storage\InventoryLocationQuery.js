import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Clipboard,Linking,Image,ScrollView,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class InventoryLocationQuery extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:6,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            locationDataSource:[],
            selLocationName:"",
            selLocationId:null,
            showLocationBlock:false,
            selLocationAreaName:"",
            selLocationAreaId:null,
            locationAreaDataSource:[],
            showLocationAreaBlock:false,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadInventoryList();
        this.loadLocationAreaList();
        // this.loadLocationListByAreaId();
    }

    loadLocationAreaList=()=>{
        // 加载库区列表
        let url= "/biz/storage/location/area/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':1000,
        };
        httpPost(url, loadRequest, this.callBackLoadStorageLocationArea);
    }

    // 库区回调加载
    callBackLoadStorageLocationArea=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库区");
                WToast.show(toastOpts);
                return;
            }
            this.setState({
                locationAreaDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadLocationListByAreaId=(locationAreaId)=>{
        // 加载库位
        var loadTypeUrl= "/biz/storage/location/list";
        var loadRequest={'currentPage':1,'pageSize':10000,'locationAreaId':locationAreaId};
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    locationDataSource:response.data.dataList
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/inventory/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "locationId":this.state.selLocationId ? this.state.selLocationId : null,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        
        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.selLocationId == null) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            selLocationName:"",
            selLocationId:null,
            showLocationBlock:false,
        })
        this.setState({
            currentPage:1
        })
        let url= "/biz/inventory/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "locationId":null,
            "locationAreaId":null
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            // var dataAll = [...dataNew];
            this.setState({
                dataSource:response.data.dataList,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadInventoryList();
    }

    loadInventoryList=()=>{
        let url= "/biz/inventory/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "locationId":this.state.selLocationId ? this.state.selLocationId : null,
        };
        httpPost(url, loadRequest, this.loadInventoryListCallBack);
    }

    loadInventoryListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            // var dataAll = dataOld.concat(dataNew.filter(v => !dataOld.includes(v)))
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.inventoryId} style={styles.innerViewStyle}>
                {
                    index == 0 ?
                        <View style={{ width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF', borderBottomWidth: 10, borderBottomColor: '#F4F7F9' }}>
                        </View>
                        :
                        <View></View>
                }
                <View style={CommonStyle.titleViewStyleSpecial}>
                    {/* <Text style={styles.titleTextStyle}>砖型：{item.seriesName}-{item.brickTypeName}</Text> */}
                    <Text style={CommonStyle.titleTextStyleSpecial}>砖型：{item.seriesName}-{item.brickTypeName}</Text>

                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>尺寸：{item.standardSize}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>单重(Kg)：{item.standardWeight}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>库区：{item.locationAreaName}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>库位：{item.locationName}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>入库总数：{item.storageInAmount}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>出库总数：{item.storageOutAmount}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>现有库存：{item.storageInAmount - item.storageOutAmount}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>库存总重(Kg)：{((item.storageInAmount - item.storageOutAmount) * item.standardWeight).toFixed(2)}</Text>
                </View>
                <View style={[CommonStyle.titleViewStyle,{marginBottom:10}]}>
                    <Text style={CommonStyle.titleTextStyle}>更新时间：{item.gmtModified == null ? item.gmtCreated : item.gmtModified}</Text>
                </View>
                {/* <View style={[CommonStyle.itemBottomBtnStyle]}>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("InventoryAdjust", 
                            {
                                // 传递参数
                                brickTypeId:item.brickTypeId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle
                        ]}>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>库存调整</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0',marginHorizontal:16,}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                }}  >
                    {/* <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
                </TouchableOpacity>
            </View>
        )
    }

    // 库区选择
    locationAreaSelect(){
        if (!this.state.locationAreaDataSource || this.state.locationAreaDataSource.length < 1) {
            WToast.show({data:"请先添加库区"});
            return
        }
        this.setState({
            showLocationAreaBlock:true,
        })
    }

    // 库位选择
    locationSelect(){
        if (!this.state.selLocationAreaId) {
            WToast.show({data:"请先选择库区"});
            return
        }
        if (!this.state.locationDataSource || this.state.locationDataSource.length < 1) {
            WToast.show({data:"请先添加库位"});
            return
        }
        this.setState({
            showLocationBlock:true,
        })
    }


    exportPdfFile=()=> {
        console.log("=======exportPdfFile");
        let url= "/biz/generate/pdf/inventory_location";
        let requestParams={
            "currentPage": 1,
            "pageSize": 1000,
            "locationAreaId":this.state.selLocationAreaId ? this.state.selLocationAreaId : null,
            "locationId":this.state.selLocationId ? this.state.selLocationId : null,
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data); 
                WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
                Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                    {
                        text:"不打开", onPress:()=>{
                        WToast.show({data:'点击了不打开'});
                        }
                    },
                    {
                        text:"打开", onPress:()=>{
                            WToast.show({data:'点击了打开'});
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }

    // 库区
    renderLocationAreaRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selLocationAreaId:item.locationAreaId,
                selLocationAreaName:item.locationAreaName,
            }) }}>
                <View key={item.locationAreaId} style={[item.locationAreaId===this.state.selLocationAreaId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle, {padding:10, margin:5, }] }>
                    <Text style={[item.locationAreaId===this.state.selLocationAreaId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                        {item.locationAreaName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 库位
    renderLocationRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selLocationId:item.locationId,
                selLocationName:item.locationName,
            }) }}>
                <View key={item.locationId} style={[item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle, {padding:10, margin:5, }] }>
                    <Text style={[item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                        {item.locationName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='清点库存'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.rightTop50FloatingBlockView, 
                    {
                        // borderRadius: 3, width: null, paddingLeft: 15, paddingRight: 15, opacity: 0.6,height:40,marginTop:10 
                        // marginRight:1,
                        marginTop:9,
                        height: 32,
                        width: 110,
                        opacity: 0.6,
                        right:0,
                        borderRadius: 8,
                        backgroundColor: "rgba(242, 245, 252, 1)"
                    } 
                    ]}>
                    <TouchableOpacity onPress={() => this.locationAreaSelect()}>
                        <Text style={[{
                            color: 'rgba(0,10,32,0.85)', 
                            fontSize: 14,
                        }]}>
                            {!this.state.selLocationAreaName ? "库区": this.state.selLocationAreaName}
                        </Text>
                    </TouchableOpacity>
                </View>
                <View style={[CommonStyle.itemBottomDetailBtnViewStyle, 
                {
                    // width: 100,
                    marginRight:0, 
                    backgroundColor: 'green', 
                    flexDirection: "row", 
                    top:85,
                    // height: 35,
                    zIndex: 100,
                    position: 'absolute', 
                    right: 0, 
                    // opacity: 0.6, 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    height: 32,
                    width: 110,
                    opacity: 0.6,
                    borderRadius: 8,
                    backgroundColor: "rgba(242, 245, 252, 1)"
                    

                }]}>
                    <TouchableOpacity onPress={()=>this.locationSelect()}>
                        <Text style={[
                            CommonStyle.rightTop50FloatingBlockText,
                            { color: 'rgba(0,10,32,0.85)', fontSize: 14 }
                            ]}>
                        {!this.state.selLocationName ? "库位": this.state.selLocationName}
                        </Text>
                    </TouchableOpacity>
                </View>

                <View style={[CommonStyle.itemBottomDetailBtnViewStyle, 
                    { 
                        width: 80 ,
                        // backgroundColor:"#F2C16D",
                        flexDirection:"row",
                        top:120,
                        zIndex:100,
                        position: 'absolute',
                        right: 5,
                        opacity:0.6,
                        alignItems:'center',
                        justifyContent:'center',
                        height: 32,
                        // width: 110,
                        // opacity: 0.6,
                        borderRadius: 8,
                        backgroundColor: "rgba(242, 245, 252, 1)"
                    }]}>
                    <TouchableOpacity onPress={() => {
                        Alert.alert('确认', '您确定要导出PDF文件吗？', [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.exportPdfFile()
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, 
                            {
                                width: 70 ,
                                // backgroundColor:"#F2C16D",
                                backgroundColor: "rgba(242, 245, 252, 1)",
                                flexDirection:"row"
                            }
                            ]}>
                        <Image  style={{
                            width:20, 
                            height:20,
                            marginRight:5,
                            tintColor: 'rgba(0,10,32,0.85)',
                        }} source={require('../../assets/icon/iconfont/output.png')}></Image>
                            <Text style={[CommonStyle.itemBottomDetailBtnTextStyle,
                            {
                                color: 'rgba(0,10,32,0.85)',
                                fontSize: 14,
                            }]}>导出</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                {/* <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 70 ,backgroundColor:"#F2C16D",flexDirection:"row"
                ,top:screenHeight/5.5,zIndex:100,position: 'absolute',right: 15,opacity:0.6,alignItems:'center',justifyContent:'center'}]}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要导出PDF文件吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.exportPdfFile()
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 70, backgroundColor: "#F2C16D", flexDirection: "row" }]}>
                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/output.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>导出</Text>
                        </View>                    
                    </TouchableOpacity>
                </View> */}

                <View style={this.state.showLocationAreaBlock ? {
                    position: 'absolute',
                    backgroundColor:'rgba(169,169,169,0.95)',
                    width:screenWidth,
                    zIndex:101,
                    padding:10,
                    right: 0,
                    left:0,
                    top: 50,
                    } : {height:0, width:0}}>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.locationAreaDataSource && this.state.locationAreaDataSource.length > 0) 
                                ? 
                                this.state.locationAreaDataSource.map((item, index)=>{
                                    return this.renderLocationAreaRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                    </View>
                    <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                        <TouchableOpacity onPress={() => { 
                            this.setState({
                                showLocationAreaBlock:false,
                                selLocationAreaId:"",
                                selLocationAreaName:""
                            }) 
                        }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20},{flexDirection:'row'}]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => { 
                            let url= "/biz/inventory/list";
                            let loadRequest={
                                "currentPage": 1,
                                "pageSize": this.state.pageSize,
                                "locationAreaId":this.state.selLocationAreaId ? this.state.selLocationAreaId : null,
                            };
                            httpPost(url, loadRequest, this._loadFreshDataCallBack);
                            this.setState({
                                showLocationAreaBlock:false,
                                selLocationId:"",
                                selLocationName:""
                            }) 
                            this.loadLocationListByAreaId(this.state.selLocationAreaId);
                        }}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20},{flexDirection:'row'}]}>
                                <Image style={{width:30, height:30,marginRight:15}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>

                <View style={this.state.showLocationBlock ? {
                    position: 'absolute',
                    backgroundColor:'rgba(169,169,169,0.95)',
                    width:screenWidth,
                    zIndex:101,
                    padding:10,
                    right: 0,
                    left:0,
                    top: 50,
                    } : {height:0, width:0}}>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.locationDataSource && this.state.locationDataSource.length > 0) 
                                ? 
                                this.state.locationDataSource.map((item, index)=>{
                                    return this.renderLocationRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                    </View>
                    <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                        <TouchableOpacity onPress={() => { 
                            this.setState({
                                showLocationBlock:false,
                                selLocationId:"",
                                selLocationName:""
                            }) 
                        }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20},{flexDirection:'row'}]} >
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => { 
                            let url= "/biz/inventory/list";
                            let loadRequest={
                                "currentPage": 1,
                                "pageSize": this.state.pageSize,
                                "locationAreaId":this.state.selLocationAreaId ? this.state.selLocationAreaId : null,
                                "locationId":this.state.selLocationId ? this.state.selLocationId : null,
                            };
                            httpPost(url, loadRequest, this._loadFreshDataCallBack);
                            this.setState({
                                showLocationBlock:false,
                            }) 
                        }}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20},{flexDirection:'row'}]}>
                                <Image style={{width:30, height:30,marginRight:15}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
                
                
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        ItemSeparatorComponent={this.space}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        // borderColor:"#F4F4F4",
        // borderWidth:8,
        // marginLeft:15,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:3,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    // itemContentViewStyle:{
    //     flexDirection:'row',
    //     justifyContent:'space-between',
    //     marginLeft:25
    // },
    itemContentChildViewStyle:{
        justifyContent:'space-between',
        flexDirection:'row',
    },
});