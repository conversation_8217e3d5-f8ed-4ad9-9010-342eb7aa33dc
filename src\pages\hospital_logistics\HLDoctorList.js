import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl, TextInput, Clipboard, Linking,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class HLDoctorList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            searchKeyWord:"",
            departmentId:"",
            departmentName:"",
            hospitalId:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { departmentId, departmentName,hospitalId} = route.params;
            if (departmentId) {
                console.log("=============departmentId" + departmentId + "");
                this.setState({
                    departmentId:departmentId,
                })
                this.loadDoctorList(departmentId);
            }
            else{
                this.loadDoctorList();
            }
            if (departmentName) {
                console.log("=============departmentName" + departmentName + "");
                this.setState({
                    departmentName:departmentName,
                })
            }
            console.log("++++++++++" , hospitalId)
            if (hospitalId) {
                console.log("=============hospitalId" + hospitalId + "");
                this.setState({
                    hospitalId:hospitalId,
                })
            }
        }
    }

    loadDoctorList=(departmentId)=>{
        let url= "/biz/hl/doctor/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "departmentId":departmentId?departmentId:this.state.departmentId,
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadDoctorListCallBack);
    }

    _loadDoctorListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteDoctor=(doctorId)=>{
        console.log("=======delete=doctorId", doctorId);
        let url= "/biz/hl/doctor/delete";
        let requestParams={'doctorId':doctorId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
           console.log("==========不刷新=====");
           return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/hl/doctor/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId":this.state.departmentId,

            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        console.log("123")
        this.setState({
            refreshing:true
        })
        this.loadDoctorList();
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLDoctorAdd", 
                {
                    // 传递回调函数
                    departmentId:this.state.departmentId,
                    hospitalId:this.state.hospitalId,
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    callBackFunction=()=>{
        let url= "/biz/hl/doctor/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId":this.state.departmentId,
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.doctorId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>医生姓名：{item.doctorName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>性别：{item.gender == 'M'?"男":"女"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>工号：{item.jobNumber}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系电话：{item.contactTel}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>关联ID号：{item.doctorCode?item.doctorCode:"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>所属科室：{item.departmentName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>排序：{item.doctorSort}</Text>
                </View>
                <View style={CommonStyle.itemBottomBtnStyle}>                
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该医生吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteDoctor(item.doctorId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:80,flexDirection:'row'}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>this.props.navigation.navigate("HLDoctorAdd",
                    {
                        doctorId:item.doctorId,
                        doctorName:item.doctorName,
                       refresh: this.callBackFunction 
                    })}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:'row'}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                    
                </View>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='医生设置'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                    />
                </View>                    
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});