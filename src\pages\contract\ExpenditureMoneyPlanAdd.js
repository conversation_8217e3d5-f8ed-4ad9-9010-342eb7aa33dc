import React, { Component } from 'react';
import {
    View, ScrollView, Text, TextInput, StyleSheet, FlatList,
    TouchableOpacity, Dimensions, Image, KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 160;

export default class ExpenditureMoneyPlanAdd extends Component {
    constructor() {
        super()
        this.state = {
            operate: "",
            planId: "",
            // 节点编号
            pointId: "",
            // 节点名称
            pointName: "",
            // 计划收款比例
            planProportion: "",
            // 计划收款金额
            planAmount: "",
            // 计划收款日期
            planDate: "",
            selectCollectMoneyPlanDate: [],
            // 收款节点数据集合
            pointDateSource: [],
            // 勾选的收款节点
            selPointId: "",
            // 合同编号
            contractId: "",
            contractAmount: "",
            planProportionSum: "",
            planInvoiceAmount: "",
            invoiceTypeList: [],
            // pointSort:0,
            // invoiceTypeDataSource: [],
            invoiceTypeChooseDataSource: [],
            selInvoiceType: "P",
            diff: 0
        }
    }


    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        let invoiceTypeChooseDataSource = [
            {
                invoiceTypeId: "1",
                invoiceType: 'P',
                invoiceTypeName: "普通发票",
            },
            {
                invoiceTypeId: "2",
                invoiceType: "V",
                invoiceTypeName: "增值税专用发票",
            }
        ]
        this.setState({
            invoiceTypeChooseDataSource: invoiceTypeChooseDataSource,
        })
        // this.loadInvoicetypeList();
        const { route, navigation } = this.props;
        if (route && route.params) {
            console.log(route.params);
            const { planId, contractId, contractAmount, dataSource } = route.params;
            if (contractId) {
                this.setState({
                    contractId: contractId
                })
            }
            if (contractAmount) {
                this.setState({
                    contractAmount: contractAmount
                })
            }
            if (planId) {
                this.setState({
                    planId: planId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/contract/collect/money/plan/get";
                loadRequest = {
                    'planId': planId,
                };
                httpPost(loadTypeUrl, loadRequest, this.loadEditContractCollectMoneyPlanDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增",
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectCollectMoneyPlanDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                })
                if (constants.loginUser.tenantId != 66) {
                    this.setState({
                        planDate: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                    })
                }
            }

        }
        this.loadContractMoneyPointList();
    }


    loadEditContractCollectMoneyPlanDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            console.log(response)
            var planDate;
            if (response.data.planDate) {
                planDate = response.data.planDate.split("-");
            }
            this.setState({
                planId: response.data.planId,
                selPointId: response.data.pointId,
                pintId: response.data.pointId,
                pointName: response.data.pointName,
                planProportion: response.data.planProportion,
                planAmount: response.data.planAmount,
                contractId: response.data.contractId,
                planDate: response.data.planDate,
                selectCollectMoneyPlanDate: planDate,
                diff: response.data.planProportion,
                planInvoiceAmount: response.data.planInvoiceAmount,
                selinvoiceType: response.data.invoiceType
            })
            console.log(this.state.selectCollectMoneyPlanDate)
        }
    }

    // 获取合同收款节点列表
    loadContractMoneyPointList = () => {
        console.log("获取节点列表");
        let url = "/biz/contract/collect/money/point/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 30,
            "pointType": 'E'
        };
        httpPost(url, loadRequest, this.loadContractMoneyPointListCallBack);
    }

    loadContractMoneyPointListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log(response.data)
            var dataNew = response.data.dataList;
            var dataOld = this.state.pointDateSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                pointDateSource: dataAll,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 收款节点单项渲染
    renderPlanPointRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selPointId: item.pointId
                })
            }}>
                <View key={item.pointId} style={item.pointId === this.state.selPointId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.pointId === this.state.selPointId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.pointName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 获取发票类型列表
    //  loadInvoiceTypeList=()=>{
    //     console.log("获取合同类型列表");
    //     let url= "/biz/contract/collect/money/plan/list";
    //     let loadRequest={
    //         "currentPage": 1,
    //         "pageSize": 30,
    //         "invoiceType": this.state.selInvoiceType === 'P' ?  null  : this.state.selInvoiceType,
    //     };
    //     httpPost(url, loadRequest, this.loadInvoiceTypeListCallBack);
    // }

    // loadInvoiceTypeListCallBack=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         console.log(response.data)
    //         var dataNew = response.data.dataList;
    //         var dataOld = this.state.invoiceTypeDataSource;
    //         // dataOld.unshift(dataNew);
    //         var dataAll = [...dataOld,...dataNew];
    //         this.setState({
    //             invoiceTypeDataSource:dataAll,
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }
    // 发票类型单项渲染
    renderInvoiceTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selInvoiceType: item.invoiceType
                })
            }}>
                <View key={item.invoiceTypeId} style={item.invoiceType === this.state.selInvoiceType ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.invoiceType === this.state.selInvoiceType ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.invoiceTypeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("ExpenditureMoneyPlanList")
            }}>
                <Text style={CommonStyle.headRightText}>付款计划</Text>
            </TouchableOpacity>
        )
    }

    getProportionSum = () => {
        console.log("=======getProportionSum");
        let toastOpts;
        if (!this.state.selPointId) {
            toastOpts = getFailToastOpts("请选择付款节点");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.planProportion) {
            toastOpts = getFailToastOpts("请输入计划付款比例");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.planAmount) {
        //     toastOpts = getFailToastOpts("请输入计划收款金额");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (constants.loginUser.tenantId != 66) {
            if (!this.state.planDate) {
                toastOpts = getFailToastOpts("请选择计划付款日期");
                WToast.show(toastOpts)
                return;
            }
        }
        // if (!this.state.pointSort) {
        //     toastOpts = getFailToastOpts("请输入排序");
        //     WToast.show(toastOpts)
        //     return;
        // }

        let url0 = "/biz/contract/collect/money/plan/sum";
        let params = {
            contractId: this.state.contractId
        }
        httpPost(url0, params, this.getProportionSumCallBack);

    }

    // 获取比列总和的回调函数
    getProportionSumCallBack = (response) => {
        if (response.code == 200) {
            console.log(response)
            var sum;
            sum = response.data
            // this.setState({
            //     planProportionSum:sum
            // })
            console.log("比例综合为：" + sum);
            this.saveCollectMoneyPlan(sum);
        }
    }

    saveCollectMoneyPlan = (sum) => {
        console.log("=======saveCollectMoneyPlan");
        var proportionSum;
        let url = "/biz/contract/collect/money/plan/add";
        proportionSum = 100 - sum;
        console.log(sum);
        console.log(proportionSum + this.state.diff);
        if (this.state.planId) {
            console.log("=========Edit===planId", this.state.planId)
            proportionSum = proportionSum + this.state.diff
            url = "/biz/contract/collect/money/plan/modify";
        }
        let requestParams = {
            planId: this.state.planId,
            pointId: this.state.selPointId,
            planProportion: this.state.planProportion,
            planAmount: this.state.planAmount,
            planDate: this.state.planDate,
            contractId: this.state.contractId,
            planInvoiceAmount: this.state.planInvoiceAmount,
            invoiceType: this.state.selInvoiceType
        };
        if (proportionSum >= this.state.planProportion) {
            httpPost(url, requestParams, this.saveContractCallBack);
        } else {
            let toastOpts;
            toastOpts = getFailToastOpts("比例超过100，请重新输入比例");
            WToast.show(toastOpts)
        }
    }

    // 保存回调函数
    saveContractCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack();
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    openCollectMoneyPlanDate() {
        this.refs.SelectCollectMoneyPlanDate.showDate(this.state.selectCollectMoneyPlanDate)
    }

    callBackSelectSelectCollectMoneyPlanDate(value) {
        console.log("==========计划收款时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectCollectMoneyPlanDate: value
        })
        if (value && value.length) {
            var planDate = "";
            var vartime;
            for (var index = 0; index < value.length; index++) {
                vartime = value[index];
                if (index === 0) {
                    planDate += vartime;
                }
                else {
                    planDate += "-" + vartime;
                }
            }
            this.setState({
                planDate: planDate
            })
        }
    }

    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>付款节点</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.pointDateSource && this.state.pointDateSource.length > 0)
                                ?
                                this.state.pointDateSource.map((item, index) => {
                                    return this.renderPlanPointRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>发票类型</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{ flexWrap: 'wrap', flexDirection: 'row' }}>
                            {
                                (this.state.invoiceTypeChooseDataSource && this.state.invoiceTypeChooseDataSource.length > 0)
                                    ?
                                    this.state.invoiceTypeChooseDataSource.map((item, index) => {
                                        return this.renderInvoiceTypeRow(item)
                                    })
                                    : <EmptyRowViewComponent />
                            }
                        </View>
                    </View>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>计划付款比例</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 60) }]}
                            placeholder={'请输入计划付款比例'}
                            onChangeText={(text) => this.setState({ planProportion: text, planAmount: text * this.state.contractAmount / 100 })}
                        >
                            {this.state.planProportion}
                        </TextInput>
                        <Text style={styles.percent}>%</Text>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>计划付款金额</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 60) }]}
                            placeholder={'请输入计划付款金额'}
                            onChangeText={(text) => this.setState({ planAmount: text })}
                        >
                            {this.state.planAmount}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>计划付款日期</Text>
                            {
                                (constants.loginUser.tenantId != 66) ?
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    :
                                    <View />
                            }
                        </View>
                        <TouchableOpacity onPress={() => this.openCollectMoneyPlanDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { marginLeft: 5, width: screenWidth - (leftLabWidth + 60) }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.planDate ? "请选择计划付款日期" : this.state.planDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    {/* <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入排序'}
                                onChangeText={(text) => this.setState({ pointSort: text })}
                            >
                                {this.state.pointSort}
                            </TextInput>
                        </View> */}
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{ marginTop: 0}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => this.getProportionSum()}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                <BottomScrollSelect
                    ref={'SelectCollectMoneyPlanDate'}
                    callBackDateValue={this.callBackSelectSelectCollectMoneyPlanDate.bind(this)}
                />
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 10),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginLeft: 5,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    rightLabView: {
        width: 20,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    percent: {
        fontSize: 20,
        marginTop: 6
    },
    inputTextStyleTextStyle: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    }
})