import React,{Component} from 'react';
import {
    Alert,
    View, 
    ScrollView, 
    Text, 
    TextInput, 
    StyleSheet, 
    FlatList ,
    TouchableOpacity,
    Dimensions,
    Image,
    Modal,
    KeyboardAvoidingView
} from 'react-native';

import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class OutsourcingStorageOutAddSelDetail extends Component {

    constructor(props) {
        super(props);
        this.state ={
            brickTypeDataSource:[],
            selectBrirck:[],
            storageLocationDataSource:[],
            selLocationId:"",
            selLocationName:"",
            orderDataSource:[],
            checkOutDetailOrderList:[],
            selOrderId:'',
            selOrderName:'',
            selBrickTypeId:'',
            selBrickTypeName:'',
            selOutAmount:'',
            selOutWeight:'',
            selPackageAmount:"",
            packageStyleEnumDataSource:[],
            selPackageStyle:"",
            selPackageStyleName:"",
            amountPerPackage:"",
            selLocationAreaId:0,
            selLocationAreaName:"",
            storageLocationAreaDataSource:[],
            outsourcingTenantDataSource:[],
            selectOutsourcingTenant:[],
            outsourcingTenantId:"",
            outsourcingTenantName:"",
            modal: false,
            searchKeyWord: null,
            _orderDataSource:[]
        }
    }

    UNSAFE_componentWillMount(){
        console.log('=aaaa=UNSAFE_componentWillMount==');
        this.loadOutsourcingTenantList();
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { customerId } = route.params;
            if (customerId) {
                console.log("=========customerId:", customerId);
                this.setState({
                    customerId:customerId
                })
                // this.loadOrderData(customerId);
            }
        }

        var _packageStyleEnumDataSource = [{
            "code":"T",
            "name":"托盘"
        },{
            "code":"D",
            "name":"吨包"
        },{
            "code":"N",
            "name":"无"
        }];
        this.setState({
            packageStyleEnumDataSource:_packageStyleEnumDataSource,
            selPackageStyle:_packageStyleEnumDataSource[0].code,
            selPackageStyleName:_packageStyleEnumDataSource[0].name,
        })

        // 加载砖型
        // loadTypeUrl= "/biz/brick/series/type/effBrickTreeCatalog";
        // loadRequest={'currentPage':1,'pageSize':10000};
        // httpPost(loadTypeUrl, loadRequest, this.callBackLoadBrickTypeData);

        // // 加载库位列表
        // loadTypeUrl= "/biz/storage/location/list";
        // loadRequest={'currentPage':1,'pageSize':1000};
        // httpPost(loadTypeUrl, loadRequest, this.callBackLoadStorageLocation);
    }

    // callBackLoadBrickTypeData=(response)=>{
    //     if (response.code == 200 && response.data && response.data) {
    //         this.setState({
    //             brickTypeDataSource:response.data
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    loadOutsourcingTenantList=()=>{
        let loadTypeUrl = "/biz/tenant/outsourcing/list";
        let loadRequest = { 'currentPage': 1, 'pageSize': 1000 };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadOutSourcing);
    }

    callBackLoadOutSourcing = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            let outSourceTenantList = response.data.dataList;
            this.setState({
                outsourcingTenantDataSource: outSourceTenantList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadLocationAreaList=(outsourcingTenantId)=>{
        let url= "/biz/storage/location/area/list";
        let loadRequest={'currentPage':1,'pageSize':1000,"operateTenantId":outsourcingTenantId};
        httpPost(url, loadRequest, this.callBackLoadStorageLocationArea);
    }

    // 库区回调加载
    callBackLoadStorageLocationArea=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库区");
                WToast.show(toastOpts);
                this.setState({
                    storageLocationAreaDataSource:[],
                    selLocationAreaId:"",
                    selLocationAreaName:"",
                    storageLocationDataSource:[]
                })
                return;
            }
            this.setState({
                storageLocationAreaDataSource:response.data.dataList,
                selLocationAreaId:response.data.dataList[0].locationAreaId,
                selLocationAreaName:response.data.dataList[0].locationAreaName,
                storageLocationDataSource:[]
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // // 库位回调加载
    // callBackLoadStorageLocation=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         if (response.data.dataList.length <= 0) {
    //             let toastOpts = getFailToastOpts("请联系管理员添加库位");
    //             WToast.show(toastOpts);
    //             return;
    //         }
    //         this.setState({
    //             storageLocationDataSource:response.data.dataList,
    //         })
    //         if (this.state.storageInId && this.state.selLocationId != 0) {
    //             this.setState({
    //                 selLocationId:this.state.selLocationId,
    //                 selLocationName:this.state.selBrickTypeName,
    //             })
    //         }
    //         else if (response.data.dataList.length > 0) {
    //             this.setState({
    //                 selLocationId:response.data.dataList[0].locationId,
    //                 selLocationName:response.data.dataList[0].locationName,
    //             })
    //         }
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // 加载订单
    loadOrderData=(outsourcingTenantId)=>{
        let loadUrl= "/biz/order/list";
        let loadRequest={
            "currentPage":1,
            "pageSize":1000,
            "display":"Y",
            "excludeOrderStateListOther":[
                "A","K"
            ], 
            "qryContent":"order",
            "outsourcingTenantId":outsourcingTenantId
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadOrderData);
    }

    callBackLoadOrderData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                orderDataSource:response.data.dataList,
                _orderDataSource:response.data.dataList,
                selOrderId:"",
                selOrderName:""
            })
            if (response.data.dataList.length <= 0) {
                WToast.show({data:"该外协厂没有要出库的订单，请重新选择"});
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh(this.state.checkOutDetailOrderList)
                }
                this.props.navigation.navigate("OutsourcingStorageOutAdd") 
            }}>
                <Text style={CommonStyle.headRightText}>完成</Text>
            </TouchableOpacity>
        )
    }

    // // 渲染订单底部滚动数据
    // openOrderSelect(){
    //     if (!this.state.orderDataSource || this.state.orderDataSource.length <= 0) {
    //         let toastOpts = getFailToastOpts("没有要出库的订单");
    //         WToast.show(toastOpts)
    //         return;
    //     }
    //     this.refs.SelectOrder.showOrder(this.state.selectOrder, this.state.orderDataSource)
    // }

    // callBackOrderValue(value){
    //     console.log("==========订单选择结果：", value)
    //     if (!value) {
    //         return;
    //     }
    //     this.setState({
    //         selectOrder:value
    //     })

    //     // 取选定的订单ID
    //     var orderName = value.toString();
    //     let loadUrl= "/biz/order/getOrderByName";
    //     let loadRequest={"orderName":orderName};
    //     console.log("==========loadDetailRequest", loadRequest)
    //     httpPost(loadUrl, loadRequest, this.loadOrderDetailData);

    // }

    // loadOrderDetailData=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             selOrderId:response.data.orderId,
    //             selOrderName:response.data.orderName,
    //             selBrickTypeId:response.data.brickTypeId,
    //             selBrickTypeName:response.data.brickTypeName,
    //             selOutAmount:'',
    //             selOutWeight:''
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // 渲染砖型底部滚动数据
    openOutsourcingTenantSelect(){
        if (!this.state.outsourcingTenantDataSource || this.state.outsourcingTenantDataSource.length < 1) {
            WToast.show({data:"请先添加外协仓库"});
            return
        }
        this.refs.SelectOutsourcingTenant.showOutsourcingTenant(this.state.selectOutsourcingTenant, this.state.outsourcingTenantDataSource)
    }

    callBackOutsourcingTenantValue(value){
        console.log("==========外协仓库选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectOutsourcingTenant:value,
            outsourcingTenantName:value.toString()
        })
        var outsourcingTenantName = value.toString();
        let loadUrl= "/biz/tenant/getTenantByName";
        let loadRequest={
            "tenantName":outsourcingTenantName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadTenantDetailData);
    }

    callBackLoadTenantDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                outsourcingTenantName:response.data.tenantAbbreviation?response.data.tenantAbbreviation:response.data.tenantName,
                outsourcingTenantId:response.data.tenantId,
            })
            this.loadLocationAreaList(response.data.tenantId);
            this.loadOrderData(response.data.tenantId);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 渲染砖型底部滚动数据
    openBrickTypeSelect(){
        if (!this.state.brickTypeDataSource || this.state.brickTypeDataSource.length < 1) {
            WToast.show({data:"请先添加砖型"});
            return
        }
        this.refs.SelectBrickType.showBrickType(this.state.selectBrirck, this.state.brickTypeDataSource)
    }

    callBackBrickTypeValue(value){
        console.log("==========砖型选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectBrirck:value
        })
        // 取选定的砖型ID
        if (value.length == 2) {
            // 加载砖型
            let loadTypeUrl= "/biz/brick/series/type/getBrickByName";
            let loadRequest={
                "brickTypeName":value[1],
                "seriesName":value[0],
            };
            httpPost(loadTypeUrl, loadRequest, this._callBackLoadBrickTypeData);
        }
        else{
            console.log("======选择砖型返回数据不合法", value)
        }
    }

    _callBackLoadBrickTypeData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                selBrickTypeName:response.data.brickTypeName,
                selBrickTypeId:response.data.brickTypeId,
            })

            let url= "/biz/inventory/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": 10000,
                "brickTypeId":response.data.brickTypeId,
                "locationAreaId":this.state.selLocationAreaId
            };
            httpPost(url, loadRequest, this.loadInventoryListCallBack);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
            this.setState({
                selBrickTypeName:'',
                selBrickTypeId:'',
            })
        }
    }
    loadInventoryListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var storageLocationDataSource = response.data.dataList;
            if (this.state.checkOutDetailOrderList) {
                // 减去已经暂存出库数据
                let storageLocationKey;
                let checkOutKey;
                // 数据库中查到的型号（砖型）在不同库位的库存
                storageLocationDataSource.forEach((locationStorageDetail)=>{
                    // 库位与型号组成唯一标识
                    storageLocationKey = locationStorageDetail.locationId + "_" + this.state.selBrickTypeId;
                    // 已选择的所有型号（砖型）在不同库位的出库临时数据
                    this.state.checkOutDetailOrderList.forEach((checkOutDetail)=>{
                        checkOutKey = checkOutDetail.locationId + "_" + checkOutDetail.brickTypeId;
                        if (storageLocationKey === checkOutKey) {
                            locationStorageDetail.storageOutAmount = parseFloat(locationStorageDetail.storageOutAmount) + parseFloat(checkOutDetail.outAmount);
                        }
                    })
                })
            }
            if(storageLocationDataSource && storageLocationDataSource.length > 0) {
                
            }
            else {
                storageLocationDataSource = [{"locationId":0,"locationName":"库存","storageInAmount":0,"storageOutAmount":0}];
            }
            console.log("storageLocationDataSource===",storageLocationDataSource)

            this.setState({
                storageLocationDataSource:storageLocationDataSource,
            })
        }
    }

    // 库区
    renderLocationAreaRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    selLocationAreaId:item.locationAreaId,
                    selLocationAreaName:item.locationAreaName
                }) 
                let url= "/biz/inventory/list";
                let loadRequest={
                    "currentPage": 1,
                    "pageSize": 10000,
                    "brickTypeId":this.state.selBrickTypeId,
                    "locationAreaId":item.locationAreaId
                };
                httpPost(url, loadRequest, this.loadInventoryListCallBack);
            }}>
                <View key={item.locationAreaId} style={item.locationAreaId===this.state.selLocationAreaId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.locationAreaId===this.state.selLocationAreaId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationAreaName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 库位
    renderLocationRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selLocationId:item.locationId,
                selLocationName:item.locationName,
                selBrickTypeLocationCurrentInventory:item.storageInAmount - item.storageOutAmount
            }) }}>
                <View key={item.locationId} style={item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationName} [{double2StringFormat(item.storageInAmount - item.storageOutAmount)}]
                        {/* {item.locationName} [{item.storageInAmount - item.storageOutAmount}] */}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

        // 订单项
        renderOrderItem = (item) => {
            return (
                <TouchableOpacity onPress={() => {
                    this.setState({
                        selBrickTypeId: item.brickTypeId,
                        selBrickTypeName: item.brickTypeName,
                        selOrderId: item.orderId,
                        selOrderName: item.orderName,
                    })
                }}>
                    <View key={item.orderId} style={item.orderId === this.state.selOrderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                        <Text style={item.orderId === this.state.selOrderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                            {item.orderName}
                        </Text>
                    </View>
                </TouchableOpacity>
            )
        }

    
    // 运输方式
    renderPackageStyleRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selPackageStyle:item.code,
                selPackageStyleName:item.name
            }) }}>
                <View key={item.code} style={[item.code === this.state.selPackageStyle ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.code === this.state.selPackageStyle ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.name}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 搜索订单
    searchOrder = () => {
        var _orderDataSource = copyArr(this.state.orderDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _orderDataSource = _orderDataSource.filter(item => item.orderName.indexOf(this.state.searchKeyWord) > 0);
        }
        this.setState({
            _orderDataSource: _orderDataSource,
        })
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title='新增出库砖型'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>订单名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>{
                            if (this.state.customerId) {
                                // 只有选择了客户过后才会加载订单
                                this.openOrderSelect()
                            }
                            else {
                                WToast.show({data:"请先选择客户"});
                                return;
                            }
                        }}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.selOrderName ? "请选择订单" : this.state.selOrderName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>外协仓库</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openOutsourcingTenantSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth*0.99-leftLabWidth}, {width: screenWidth-(leftLabWidth +50) }]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.outsourcingTenantName ? "请选择外协仓库" : this.state.outsourcingTenantName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                订单名称
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        {/* <TouchableOpacity onPress={()=>{
                        if (this.state.orderId) {
                            WToast.show({data:"编辑只能编辑客户、合同、订单名称及砖型以外的信息"})
                        }
                        else {
                            this.openOrderSelect()
                        }
                    }}>
                        <View style={CommonStyle.inputTextStyleTextStyle}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.orderName ? "请选择订单" : this.state.orderName}
                            </Text>
                        </View>
                    </TouchableOpacity> */}
                        <View style={[(!this.state.orderDataSource || this.state.orderDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (!this.state.orderDataSource || this.state.orderDataSource.length === 0) {
                                    let errorMsg = '没有需要出库的外协订单';
                                    if (!this.state.outsourcingTenantName || this.state.outsourcingTenantName.length === 0) {
                                        errorMsg = "请先选择外协仓库";
                                    }
                                    Alert.alert('确认', errorMsg, [
                                        {
                                            text: "确定", onPress: () => {
                                                WToast.show({ data: '点击了确定' });
                                            }
                                        }
                                    ]);
                                    return;
                                }
                                if (!this.state._orderDataSource || this.state._orderDataSource.length === 0) {
                                    this.setState({
                                        _orderDataSource: copyArr(this.state.orderDataSource),
                                    })
                                }
                                this.setState({
                                    modal: true,
                                })

                                if (!this.state.selOrderId && this.state.orderDataSource && this.state.orderDataSource.length > 0) {
                                    this.setState({
                                        selBrickTypeId: this.state.orderDataSource[0].brickTypeId,
                                        selBrickTypeName: this.state.orderDataSource[0].brickTypeName,
                                        selOrderId: this.state.orderDataSource[0].orderId,
                                        selOrderName: this.state.orderDataSource[0].orderName,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, { flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                    <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.selOrderId && this.state.selOrderName ? (this.state.selOrderName) : "选择外协订单"}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                    <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                </View> */}
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.searchOrder();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._orderDataSource && this.state._orderDataSource.length > 0)
                                                ?
                                                this.state._orderDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderOrderItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selOrderId) {
                                            let toastOpts = getFailToastOpts("您还没有选择砖型");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            modal: false,
                                        })
                                        if(this.state.storageLocationAreaDataSource && this.state.storageLocationAreaDataSource.length > 0) {
                                            
                                        }
                                        else {
                                            let toastOpts = getFailToastOpts("请联系管理员添加库区");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        let url= "/biz/inventory/list";
                                        let loadRequest={
                                            "currentPage": 1,
                                            "pageSize": 10000,
                                            "brickTypeId":this.state.selBrickTypeId,
                                            "locationAreaId":this.state.selLocationAreaId
                                        };
                                        httpPost(url, loadRequest, this.loadInventoryListCallBack);

                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>

                        </View>
                    </Modal>


                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>件数</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入件数'}
                            onChangeText={
                                (text) => {
                                    this.setState({selPackageAmount:text})
                                    if(this.state.amountPerPackage){
                                        this.setState({
                                            selOutAmount:text*this.state.amountPerPackage
                                        })
                                    }
                                }
                            }
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                            {this.state.selPackageAmount}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>块/件</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入块/件'}
                            onChangeText={
                                (text) =>{
                                    this.setState({amountPerPackage:text})
                                    if(this.state.selPackageAmount){
                                        this.setState({
                                            selOutAmount:text*this.state.selPackageAmount
                                        })
                                    }
                                    else{
                                        WToast.show({data:"请输入件数"});
                                    }
                                }
                                }
                                style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                                {this.state.amountPerPackage}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>数量</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入数量'}
                            onChangeText={(text) => this.setState({selOutAmount:text})}
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                            {this.state.selOutAmount}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>库区</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationAreaDataSource && this.state.storageLocationAreaDataSource.length > 0) 
                            ? 
                            this.state.storageLocationAreaDataSource.map((item, index)=>{
                                return this.renderLocationAreaRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>库位</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationDataSource && this.state.storageLocationDataSource.length > 0) 
                            ? 
                            this.state.storageLocationDataSource.map((item, index)=>{
                                return this.renderLocationRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>重量(吨)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入重量'}
                            onChangeText={(text) => this.setState({selOutWeight:text})}
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                            {this.state.selOutWeight}
                        </TextInput>
                    </View>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>包装形式</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <View style={{ width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15 }}>
                        {
                            (this.state.packageStyleEnumDataSource && this.state.packageStyleEnumDataSource.length > 0) 
                            ? 
                            this.state.packageStyleEnumDataSource.map((item, index)=>{
                                return this.renderPackageStyleRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={styles.btnRowView}>
                     <TouchableOpacity onPress={()=>{
                         if (!this.state.outsourcingTenantId) {
                            WToast.show({data:"请选择外协仓库"});
                            return;
                        }
                        if (!this.state.selOrderId) {
                            WToast.show({data:"请选择外协订单"});
                            return;
                        }
                        // if (!this.state.selBrickTypeId) {
                        //     WToast.show({data:"请选择砖型"});
                        //     return;
                        // }
                        if (!this.state.selPackageAmount || this.state.selPackageAmount === "0") {
                            WToast.show({data:"请输入件数"});
                            return;
                        }
                        // if (!this.state.amountPerPackage || this.state.amountPerPackage === "0") {
                        //     WToast.show({data:"请输入块/件"});
                        //     return;
                        // }

                        if (!this.state.selOutAmount || this.state.selOutAmount === "0") {
                            WToast.show({data:"请输入数量"});
                            return;
                        }
                        if (!this.state.selLocationAreaId || this.state.selLocationAreaId === "0") {
                            WToast.show({data:"请选择库区"});
                            return;
                        }
                        if (!this.state.selLocationId || this.state.selLocationId === "0") {
                            WToast.show({data:"请选择库位"});
                            return;
                        }
                        if (!this.state.selOutWeight || this.state.selOutWeight === "0") {
                            WToast.show({data:"请输入重量"});
                            return;
                        }
                        if (this.state.selOutAmount  > this.state.selBrickTypeLocationCurrentInventory) {
                            WToast.show({data:"出库数量不能大于当前库位的库存数量[" + this.state.selBrickTypeLocationCurrentInventory + "]"});
                            return;
                        }
                        var storageOutDetailDTO = {
                            "_index":this.state.checkOutDetailOrderList.length,
                            "outsourcingTenantId":this.state.outsourcingTenantId,
                            "outsourcingTenantName":this.state.outsourcingTenantName,
                            "brickTypeId": this.state.selBrickTypeId,
                            "brickTypeName": this.state.selBrickTypeName,
                            "locationId":this.state.selLocationId,
                            "locationName":this.state.selLocationName,
                            "locationAreaId":this.state.selLocationAreaId,
                            "locationAreaName":this.state.selLocationAreaName,
                            "orderId": this.state.selOrderId,
                            "orderName": this.state.selOrderName,
                            "outAmount": this.state.selOutAmount,
                            "packageAmount":this.state.selPackageAmount,
                            "outWeight": this.state.selOutWeight,
                            "packageStyle":this.state.selPackageStyle,
                            "packageStyleName":this.state.selPackageStyleName,
                        }
                        var _checkOutDetailOrderList = this.state.checkOutDetailOrderList;
                        _checkOutDetailOrderList = _checkOutDetailOrderList.concat(storageOutDetailDTO);
                        this.setState({
                            checkOutDetailOrderList:_checkOutDetailOrderList
                        })
                        this.setState({
                            outsourcingTenantId:"",
                            outsourcingTenantName:"",
                            selBrickTypeId:"",
                            selBrickTypeName:"",
                            selLocationId:"",
                            selLocationName:"",
                            selLocationAreaId:this.state.storageLocationAreaDataSource[0].locationAreaId,
                            selLocationAreaName:this.state.storageLocationAreaDataSource[0].locationAreaName,
                            selOrderId:"",
                            selOutAmount:"",
                            selOutWeight:"",
                            selOrderName:"",
                            selBrickTypeLocationCurrentInventory:0,
                            storageLocationDataSource:[],
                            selPackageStyle:this.state.packageStyleEnumDataSource[0].code,
                            selPackageStyleName:this.state.packageStyleEnumDataSource[0].name,
                        })
                     }}>
                         <View style={[styles.btnAddView]}>
                             <Text style={styles.btnAddText}>新增</Text>
                        </View>
                     </TouchableOpacity>
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View>
                    <FlatList 
                    data={this.state.checkOutDetailOrderList}
                    renderItem={({item}) => 
                    <View key={item._index} style={styles.titleViewStyle}>
                        <View style={{ }}>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                外协仓库：{item.outsourcingTenantName}
                            </Text>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                订单名称：{item.orderName}
                            </Text>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                砖型：{item.brickTypeName}
                            </Text>
                        </View>
                        <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10}]}>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>件数：{item.packageAmount ? item.packageAmount : "无"}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>出库数量：{item.outAmount}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>库区：{item.locationAreaName}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>库位：{item.locationName}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>重量：{item.outWeight}吨</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>包装形式：{item.packageStyleName}</Text>
                            </View>
                        </View>
                    </View>}
                    />
                    </View>
                </ScrollView>
                {/* <BottomScrollSelect
                    ref={'SelectOrder'}
                    callBackOrderValue={this.callBackOrderValue.bind(this)}
                /> */}
                <BottomScrollSelect 
                    ref={'SelectBrickType'} 
                    callBackBrickTypeValue={this.callBackBrickTypeValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectOutsourcingTenant'} 
                    callBackOutsourcingTenantValue={this.callBackOutsourcingTenantValue.bind(this)}
                />
            </KeyboardAvoidingView>
        )
    }
}

const styles = StyleSheet.create({

    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', width:100, alignItems:'center', alignContent:'flex-end', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})