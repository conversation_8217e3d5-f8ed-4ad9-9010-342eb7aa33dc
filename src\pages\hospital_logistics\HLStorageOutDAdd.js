import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class HLStorageOutDAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            storageOutId:"",
            recipient:"",
            storageOutDate:"",
            operator:"",
            selectStorageOutDate: [],
            receiptRemark:"",
            storageOutUsage:"",
            //出库明细列表
            hlStorageOutDetailDTOList:[],
            usageDataSource:[
                {
                    usageId:1,
                    usageType:"消耗",
                    usageName:"A"
                },
                {
                    usageId:2,
                    usageType:"归还",
                    usageName:"B"
                },
                {
                    usageId:3,
                    usageType:"报废",
                    usageName:"C"
                },
                {
                    usageId:4,
                    usageType:"借出",
                    usageName:"D"
                }
            ],
            selUsageId:1,
            departmentId:"",

        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageOutId ,departmentId } = route.params;
            if (storageOutId) {
                console.log("=============storageOutId" + storageOutId + "");
                this.setState({
                    storageOutId:storageOutId,
                    operate:"编辑",
                })
                let loadTypeUrl = "/biz/hl/storage/out/get";
                let loadRequest = { 'storageOutId': storageOutId };
                httpPost(loadTypeUrl, loadRequest, this.loadStorageOutDDCallBack);
            }
            else{
                this.setState({
                    operate:"新增",
                    operator:constants.loginUser.userName,
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectStorageOutDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    storageOutDate: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                })
            }

            if (departmentId) {
                console.log("=============departmentId" + departmentId + "");
                this.setState({
                    departmentId:departmentId,
                    
                })
            }
        }
    }

    loadStorageOutDDCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectStorageOutDate = response.data.storageOutDate.split("-");
            if(response.data.storageOutUsage == 'A'){
                this.setState({
                    selUsageId:1
                })
            }
            if(response.data.storageOutUsage == 'B'){
                this.setState({
                    selUsageId:2
                })
            }
            if(response.data.storageOutUsage == 'C'){
                this.setState({
                    selUsageId:3
                })
            }
            if(response.data.storageOutUsage == 'D'){
                this.setState({
                    selUsageId:4
                })
            }
            this.setState({
                storageOutId:response.data.storageOutId,
                operator: response.data.operator,
                receiptRemark: response.data.receiptRemark,
                storageOutUsage: response.data.storageOutUsage,
                recipient: response.data.recipient,
                storageOutDate:response.data.storageOutDate,
                selectStorageOutDate:selectStorageOutDate
            })
            if (response.data.hlStorageOutDetailDTOList && response.data.hlStorageOutDetailDTOList.length > 0) {
                this.setState({
                    // 出库详细
                    hlStorageOutDetailDTOList:response.data.hlStorageOutDetailDTOList,
                })
            }
            // console.log("--------明细"+ hlStorageOutDetailDTOList)
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("HLStorageOutDList")
            }}>
                <Text style={CommonStyle.headRightText}>科室出库</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }
    saveStorageOutDD = () => {
        console.log("=======saveStorageOutDD");
        let toastOpts;
        if (!this.state.recipient) {
            toastOpts = getFailToastOpts("请填写领用人");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.storageOutDate) {
            toastOpts = getFailToastOpts("请选择出库日期");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.hlStorageOutDetailDTOList || this.state.hlStorageOutDetailDTOList.length < 1) {
            toastOpts = getFailToastOpts("至少新增一条明细");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selUsageId) {
            toastOpts = getFailToastOpts("请选择用途");
            WToast.show(toastOpts)
            return;
        }

        let url = "/biz/hl/storage/out/add";
        if (this.state.storageOutId) {
            console.log("=========Edit===storageOutId", this.state.storageOutId)
            url = "/biz/hl/storage/out/modify";
        }

        let requestParams = {
            storageOutId: this.state.storageOutId,
            operator: this.state.operator,
            receiptRemark: this.state.receiptRemark,
            recipient: this.state.recipient,
            storageOutDate: this.state.storageOutDate,
            // receiptRemark: this.state.receiptRemark,
            storageOutUsage: this.state.usageDataSource[this.state.selUsageId-1].usageName,
            hlStorageOutDetailDTOList:this.state.hlStorageOutDetailDTOList,
            storageOutFlag:"D",
            departmentId:this.state.departmentId ? this.state.departmentId : null,
        };
        httpPost(url, requestParams, this.saveStorageOutDDCallBack);
    }

    // 保存回调函数
    saveStorageOutDDCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    openStorageOutDate() {
        this.refs.SelectStorageOutDate.showDate(this.state.selectStorageOutDate)
    }
    callBackSelectStorageOutDateValue(value) {
        console.log("==========c出库日期选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectStorageOutDate: value
        })
        if (value && value.length) {
            var storageOutDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    storageOutDate += vartime;
                }
                else{
                    storageOutDate += "-" + vartime;
                }
            }
            this.setState({
                storageOutDate: storageOutDate
            })
        }
        var dateString = this.state.signingTime + ' 00:00:01';
        dateString = dateString.substring(0, 19);
        dateString = dateString.replace(/-/g, '/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var SevenDaysLast = new Date(dateStringTimestamp);
        // 用获取毫秒数 加上30天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        SevenDaysLast.setTime(dateStringTimestamp + (30 * 86400000));
        //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
        //格式化月，如果小于9，前面补0  
        var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0  
        var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
        this.setState({
            selectDeliveryDate: [SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
            deliveryDate: SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
        })
        if (this.state.selectDeliveryDate && this.state.selectDeliveryDate.length) {
            var deliveryDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectDeliveryDate.length; index++) {
                vartime = this.state.selectDeliveryDate[index];
                if (index === 0) {
                    deliveryDate += vartime;
                }
                else {
                    deliveryDate += "-" + vartime;
                }
            }
            this.setState({
                deliveryDate: deliveryDate
            })
        }
    }

    _loadFreshHlStorageOutDetailDTOList=(_hlStorageOutDetailDTOList)=>{
        if (_hlStorageOutDetailDTOList && _hlStorageOutDetailDTOList.length > 0) {
            console.log("=========回退数据：", _hlStorageOutDetailDTOList);
            this.setState({
                hlStorageOutDetailDTOList:_hlStorageOutDetailDTOList,
            })
        }
        else {
            console.log("=========回退不成功");
        }
    }

    //用途列表展示
    renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selUsageId:item.usageId,
                    })
                }}>
                <View key={item.usageId} style={[item.usageId===this.state.selUsageId ? [CommonStyle.selectedBlockItemViewStyle,{margin:0}] : [CommonStyle.blockItemViewStyle,,{margin:0}]] }>
                    <Text style={item.usageId===this.state.selUsageId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.usageType}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    render(){
        return(
            <ScrollView style={[CommonStyle.contentViewStyle]}>
                <CommonHeadScreen title={this.state.operate + '出库'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>领用人</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput
                        //keyboardType='text'
                        style={[styles.inputRightText]}
                        placeholder={'请输入领用人'}
                        onChangeText={(text) => this.setState({ recipient: text })}
                    >
                        {this.state.recipient}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>出库日期</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TouchableOpacity onPress={() => this.openStorageOutDate()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 30)}]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.storageOutDate ? "请选择出库日期" : this.state.storageOutDate}
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>用途</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.usageDataSource && this.state.usageDataSource.length > 0) 
                            ? 
                            this.state.usageDataSource.map((item, index)=>{
                                return this.renderRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>经办人</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput
                        //keyboardType='text'
                        style={[styles.inputRightText]}
                        placeholder={'请输入经办人'}
                        onChangeText={(text) => this.setState({ operator: text })}
                    >
                        {this.state.operator}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>备注说明</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <TextInput
                        //keyboardType='text'
                        style={[styles.inputRightText]}
                        placeholder={'请输入备注说明'}
                        onChangeText={(text) => this.setState({ receiptRemark: text })}
                    >
                        {this.state.receiptRemark}
                    </TextInput>
                </View>
                <View style={CommonStyle.rowSplitViewStyle}></View>
                <View style={styles.btnRowView}>
                    {/* <TouchableOpacity onPress={()=>{
                        //  WToast.show({data:"功能未开通"});
                        if (this.state.selCustomerName) {
                            this.props.navigation.navigate("HLStorageOutAddDetail",
                            {
                                customerId:this.state.customerId,
                                // 传递回调函数
                                refresh: this._loadFreshSpStorageOutDetailDTOList 
                            })
                        }
                        else {
                            WToast.show({data:"请先选择需货单位"});
                            return;
                        }
                     }}>
                         <View style={[styles.btnAddView]}>
                             <Text style={styles.btnAddText}>+ 出库明细</Text>
                         </View>
                    </TouchableOpacity> */}
                    <TouchableOpacity onPress={()=>{

                        if (!this.state.recipient) {
                            let toastOpts = getFailToastOpts("请您先填入领用人");
                            WToast.show(toastOpts);
                            return;
                        }
                        // if (this.state.storageOutId) {
                            this.props.navigation.navigate("HLStorageOutAddDetail", 
                        {
                            storageOutFlag:"D",
                            // 传递参数
                            storageOutId:this.state.storageOutId,
                            departmentId:this.state.departmentId,
                            hlStorageOutDetailDTOList:this.state.hlStorageOutDetailDTOList,
                            // 传递回调函数
                            refresh: this._loadFreshHlStorageOutDetailDTOList 
                        })
                    // }
                    }}>
                         <View style={[styles.btnAddView]}>
                             <Text style={styles.btnAddText}>+ 出库明细</Text>
                         </View>
                    </TouchableOpacity>
                </View>
                <View>
                    <FlatList 
                    data={this.state.hlStorageOutDetailDTOList}
                    renderItem={({item}) => 
                    <View key={item._index} style={styles.titleViewStyle}>
                        <View style={{ marginTop:10}}>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                名称：{item.materialName}
                            </Text>
                        </View>
                        <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10,marginTop:10}]}>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>规格型号：{item.materialModal}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>单位：{item.unitName}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>数量：{item.materialAmount}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>价格：{item.materialPrice}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>金额：{item.materialTotalPrice}</Text>
                            </View>
                        </View>
                        <TouchableOpacity
                            style={{marginLeft:-145,zIndex:1000,marginTop:130}}
                            onPress={() => {
                                console.log("========deleteStorageInDetailDTO")
                                var urls = this.state.hlStorageOutDetailDTOList;
                                urls.splice(item._index,1);                   
                                console.log(urls)
                                this.setState({
                                    hlStorageOutDetailDTOList:urls,
                                })
                            }}
                        >
                            <View style={styles.btnDeleteView}>
                                <Text style={styles.btnDeleteText}>-删除</Text>
                            </View>
                        </TouchableOpacity>

                    </View>
                    }
                    />
                </View>
                <View style={CommonStyle.btnRowStyle}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveStorageOutDD.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row', width: 130, height: 40, marginRight: 35, marginTop: 15 }]}>
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                <BottomScrollSelect
                    ref={'SelectStorageOutDate'}
                    callBackDateValue={this.callBackSelectStorageOutDateValue.bind(this)}
                />
            </ScrollView>
        )
    }
}
const styles = StyleSheet.create({
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        // marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})