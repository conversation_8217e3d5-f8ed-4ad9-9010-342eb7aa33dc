import moment from 'moment';
import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  ImageBackground,
  Linking,
  Modal,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {WToast} from 'react-native-smart-tip';
import * as WeChat from 'react-native-wechat-lib';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import MessageInputModal from '../../component/MessageInputModal';
import '../../utils/Global';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;

var screenHeight = Dimensions.get('window').height;
var currentDate = moment(new Date()).format('YYYY-MM-DD');
export default class DailyList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      standardType: 'E',
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      qryStartTime: null,
      selectedQryStartDate: [],
      currentTime: '',
      dailyItem: {},
      userPhotoUrl: constants.image_addr + '/' + constants.loginUser.userPhoto,
      userPhoto: '',
      shareModal: false,
      deleteModal: false,
      exportPdfModal: false,
      moreModal: false,
      messageModal: false,
      messageFkId: '',
      parentMessageId: '',
      messageContent: '',
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  initGmtCreated = () => {
    // 当前时间
    var currentDate = new Date();
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var currentHour = ('0' + (currentDate.getHours() + 8)).slice(-2);
    var currentMinute = ('0' + currentDate.getMinutes()).slice(-2);
    var currentSecond = ('0' + currentDate.getSeconds()).slice(-2);
    var _gmtCreated =
      currentDate.getFullYear() +
      '-' +
      currentDateMonth +
      '-' +
      currentDateDay +
      ' ' +
      currentHour +
      ':' +
      currentMinute +
      ':' +
      currentSecond;
    return _gmtCreated;
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    var currentTime = this.initGmtCreated();
    this.setState({
      currentTime: currentTime,
    });
    // 当前时间
    var currentDate = new Date();
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    this.setState({
      selectedQryStartDate: [
        currentDate.getFullYear(),
        currentDateMonth,
        currentDateDay,
      ],
      // qryStartTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
    });
    httpPost(
      '/biz/portal/user/get',
      {userId: constants.loginUser.userId},
      (response) => {
        if (response.code === 200) {
          let userPhoto = response.data.userPhoto;
          this.setState({
            userPhotoUrl: constants.image_addr + '/' + userPhoto,
            userPhoto: userPhoto,
          });
        }
      },
    );
    this.loadDailyList();
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/daily/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      qryStartTime: this.state.qryStartTime,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/daily/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      qryStartTime: this.state.qryStartTime,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
      if (this.state.currentPage - 1 >= this.state.totalPage) {
        WToast.show({data: '已经是最后一页了，我们也是有底线的'});
        return;
      }
      if (this.state.refreshing) {
        WToast.show({data: 'loading...'});
        return;
      }
      this.setState({ refreshing: true }, () => {
          console.log('refreshing 已更新:', this.state.refreshing);
          // 在这里执行后续操作
          this.loadDailyList();
      });
    };

  loadDailyList = () => {
    let url = '/biz/daily/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      qryStartTime: this.state.qryStartTime,
    };
    httpPost(url, loadRequest, this.loadDailyListCallBack);
  };

  loadDailyListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
      console.log('dataList', response.data.dataList);
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteDaily = (dailyId) => {
    console.log('=======delete=dailyId', dailyId);
    let url = '/biz/daily/delete';
    let requestParams = {dailyId: dailyId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  // 保存留言
  saveDailyMessage = () => {
    console.log('=======saveDailyMessage');
    let url = '/biz/portal/message/board/add';
    let requestParams = {
      messageContent: this.state.messageContent,
      messageFkId: this.state.dailyItem.dailyId,
      parentMessageId: this.state.parentMessageId,
      messageFkType: 'D',
    };
    httpPost(url, requestParams, this.saveDailyMessageCallBack);
  };

  // 保存留言的回调函数
  saveDailyMessageCallBack = (response) => {
    this.setState({
      messageContent: '',
    });
    let toastOpts;
    switch (response.code) {
      case 200:
        WToast.show({data: '留言发送成功'});
        this.callBackFunction();
        break;
      default:
        toastOpts = getFailToastOpts(response.message);
        WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('DailyDetail', {
            // 传递参数
            dailyId: item.dailyId,
            userName: item.userName,
            // 传递回调函数
            refresh: this.callBackFunction,
          });
        }}>
        <View key={item.dailyId} style={[CommonStyle.innerViewStyle]}>
          {/* 日报顶部信息 */}
<View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11, position: 'relative', alignItems: 'center'}}>
    {
        this.state.userPhoto ?
            <Image source={{ uri: this.state.userPhotoUrl }} style={{ height: 48, width: 48, borderRadius: 50}} />
            :
            <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                    {
                        item.userName.length <= 2 ? 
                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                            {item.userName}
                        </Text>
                        :
                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                            {item.userName.slice(-2)}
                        </Text>
                    }
                </View>
            </ImageBackground>
    }
    
    <View style={{marginLeft:11, flexDirection: 'column', justifyContent: 'center'}}>
        <View style={{flexDirection: 'row', marginTop: 4, alignItems: 'center',height:24}}>
            <Text style={{
                fontFamily: 'PingFangSC-Bold',
                fontSize: 16,
                fontWeight: 'normal',
                lineHeight: 23.4,
                letterSpacing: 0,
                color: '#333333',
            }}>{item.userName}的日报</Text>
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Image style={{ height: 13 , width: 12, marginTop: 5, marginLeft: 1, marginRight: 5}} source={require('../../assets/icon/iconfont/clock.png')}></Image> 
            <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)' }]}>{item.dailyDate} 提交</Text>
            </View>
        </View>
    </View>

    {/* 审核状态badge，绝对定位右上角 */}
    {
        item.auditScore !== null || item.auditScore == 0 ?
            <View style={[styles.auditPendingBadge, { backgroundColor: '#3AC9A0' }]}>
                <Text style={styles.auditPendingText}>已完成</Text>
            </View>
            :
            item.dailyState === "0BB" ?
                <View style={[styles.auditPendingBadge, { backgroundColor: '#FF635F' }]}>
                    <Text style={styles.auditPendingText}>未完成</Text>
                </View>
                :
                <View style={[styles.auditPendingBadge, { backgroundColor: '#FBAE3C' }]}>
                    <Text style={styles.auditPendingText}>待审核</Text>
                </View>
    }

    {/* 更多按钮 */}
    <View style={{ position:'absolute', right: 20, top:27 }}>
        <TouchableOpacity onPress={() => {
            this.setState({
                moreModal: true,
                dailyItem: item
            })
        }}>
            <View style={{width: 28, height: 28, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}}>
                <Image style={{ width: 17, height: 24, resizeMode: 'contain' }} source={require('../../assets/icon/iconfont/more1.png')} />
            </View>
        </TouchableOpacity>
    </View>
</View>

          {/* 分隔线 */}
          <View style={styles.lineViewStyle} />

          <View style={[styles.titleViewStyle]}>
            <Text style={styles.titleTextStyle}>完成的工作</Text>
          </View>
          <View style={styles.itemContentTextStyle}>
            <Text style={styles.itemContentStyle}>{item.finishedWork}</Text>
          </View>
          <View style={[styles.titleViewStyle]}>
            <Text style={styles.titleTextStyle}>工作计划</Text>
          </View>
          <View style={[styles.itemContentTextStyle, {marginBottom: 5}]}>
            <Text style={styles.itemContentStyle}>{item.workPlan}</Text>
          </View>
          {item.unfinishedWork ? (
            <View>
              <View style={[styles.titleViewStyle]}>
                <Text style={styles.titleTextStyle}>未完成工作</Text>
              </View>
              <View style={styles.itemContentTextStyle}>
                <Text style={styles.itemContentStyle}>
                  {item.unfinishedWork}
                </Text>
              </View>
            </View>
          ) : (
            <View></View>
          )}
          {item.requiresCoordinationWork ? (
            <View>
              <View style={[styles.titleViewStyle]}>
                <Text style={styles.titleTextStyle}>需协调工作</Text>
              </View>
              <View style={styles.itemContentTextStyle}>
                <Text style={styles.itemContentStyle}>
                  {item.requiresCoordinationWork}
                </Text>
              </View>
            </View>
          ) : (
            <View></View>
          )}
          {item.dailyState === '0BB' ? (
            <View></View>
          ) : (
            <View style={[styles.titleViewStyle]}>
              <Text style={styles.titleTextStyle}>审核人：</Text>
              <Text style={styles.itemContentStyle}>{item.auditOperator}</Text>
            </View>
          )}
          {/* {
                    item.auditScore ?
                        <View>
                            {
                                (item.auditOpinion !== null && item.auditOpinion !== "无") ?
                                    <View style={[styles.titleViewStyle, { marginTop: 5 }]}>
                                        <Text style={styles.titleTextStyle}>审核意见：</Text>
                                        <Text style={styles.itemContentStyle}>{item.auditOpinion}</Text>
                                    </View>
                                    :
                                    <View></View>
                            }
                            <View style={[styles.titleViewStyle, { marginTop: 5 }]}>
                                <Text style={styles.titleTextStyle}>审核时间：</Text>
                                <Text style={styles.itemContentStyle}>{item.auditTime}</Text>

                            </View>
                        </View> :
                        <View></View>
                }
                {
                    item.dailyState === "0BB" ?
                        <View></View>
                        :
                        (
                            item.gmtModified == null ?
                                <View style={[styles.titleViewStyle, { marginTop: 5 }]}>
                                    <Text style={styles.titleTextStyle}>创建时间：</Text>
                                    <Text style={styles.itemContentStyle}>{item.gmtCreated}</Text>
                                </View>
                                :
                                <View style={[styles.titleViewStyle, { marginTop: 5 }]}>
                                    <Text style={styles.titleTextStyle}>创建时间：</Text>
                                    <Text style={styles.itemContentStyle}>{item.gmtModified}</Text>
                                </View>
                        )
                } */}

          <View
            style={[
              CommonStyle.itemBottomBtnStyle,
              {flexWrap: 'wrap', marginLeft: 12, marginRight: 16},
            ]}>
            {item.dailyState === '0AA' ? (
              <View
                style={[CommonStyle.itemBottomBtnStyle, {flexWrap: 'wrap'}]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      shareModal: true,
                      dailyItem: item,
                    });
                  }}>
                  <View
                    style={[
                      {
                        width: 78,
                        height: 28,
                        flexDirection: 'row',
                        alignItems: 'center',
                        margin: 10,
                        marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                      },
                    ]}>
                    <Image
                      style={{
                        width: 18,
                        height: 18,
                        marginRight: 8,
                        marginLeft: 12,
                      }}
                      source={require('../../assets/icon/iconfont/share1.png')}></Image>
                    <Text
                      style={styles.itemContentStyle}>
                      分享
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      messageModal: true,
                      dailyItem: item,
                    });
                    // this.props.navigation.navigate("DailyMessageList",
                    //     {
                    //         // 传递参数
                    //         messageFkId: item.dailyId,
                    //         // 传递回调函数
                    //         refresh: this.callBackFunction
                    //     })
                  }}>
                  <View
                    style={[
                      {
                        width: 78,
                        height: 28,
                        flexDirection: 'row',
                        alignItems: 'center',
                        margin: 10,
                        marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                      },
                    ]}>
                    {/* <Image style={{ width: 24, height: 24, marginRight: 6, marginLeft: 5 }} source={require('../../assets/icon/iconfont/newMessageBlack.png')}></Image>
                                        <Text style={[{ color: 'rgba(83, 106, 247, 1)', fontSize: 14 }]}>{item.messageNum}</Text> */}
                    <Image
                      style={{
                        width: 20,
                        height: 20,
                        marginRight: 8,
                        marginLeft: 12,
                      }}
                      source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                    <Text
                      style={styles.itemContentStyle}>
                      留言
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            ) : (
              <View></View>
            )}
          </View>

          {/* 留言 */}
          {item.messageList && item.messageList.length > 0 ? (
            <View
              style={{
                backgroundColor: 'rgba(242, 245, 252, 0.5)',
                borderRadius: 10,
                width: screenWidth - 24,
                marginLeft: 12,
                marginRight: 12,
                paddingTop: 5,
                marginBottom: 5,
              }}>
              {item.messageList.slice(0, 3).map((item, index) => {
                return (
                  <View
                    key={item.messageId}
                    style={{
                      flexDirection: 'row',
                      marginLeft: 10,
                      marginTop: 10,
                      marginBottom: 10,
                    }}>
                    {item.operatorPhoto ? (
                      <Image
                        source={{
                          uri: constants.image_addr + '/' + item.operatorPhoto,
                        }}
                        style={{height: 36, width: 36, borderRadius: 50}}
                      />
                    ) : (
                      <ImageBackground
                        source={require('../../assets/icon/iconfont/profilePicture.png')}
                        style={{height: 36, width: 36}}>
                        <View
                          style={{
                            height: 36,
                            width: 36,
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          {item.operatorName <= 2 ? (
                            <Text
                              style={{
                                color: '#FFFFFF',
                                fontSize: 13,
                                fontWeight: 'normal',
                                textAlign: 'center',
                                lineHeight: 20,
                              }}>
                              {item.operatorName}
                            </Text>
                          ) : (
                            <Text
                              style={{
                                color: '#FFFFFF',
                                fontSize: 13,
                                fontWeight: 'normal',
                                textAlign: 'center',
                                lineHeight: 20,
                              }}>
                              {item.operatorName.slice(-2)}
                            </Text>
                          )}
                        </View>
                      </ImageBackground>
                    )}

                    <View
                      style={{
                        flexDirection: 'column',
                        marginLeft: 10,
                        flex: 1,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'flex-start',
                          alignItems: 'center',
                          paddingTop: 4,
                        }}>
                        <View style={{flexDirection: 'row'}}>
                          <Text style={{fontSize: 16}}>
                            {item.operatorName}
                          </Text>
                        </View>
                        <View style={{flexDirection: 'row', marginLeft: 6}}>
                          <Text
                            style={[
                              {fontSize: 12, color: 'rgba(0,10,32,0.45)'},
                            ]}>
                            {item.gmtCreated.slice(0, 16)}
                          </Text>
                        </View>
                      </View>

                      {item.parentMessageId ? (
                        <View
                          style={[
                            {
                              flexDirection: 'column',
                              justifyContent: 'flex-start',
                            },
                          ]}>
                          <View
                            style={[
                              {
                                flexDirection: 'row',
                                justifyContent: 'flex-start',
                                alignItems: 'flex-start',
                                marginLeft: 9,
                                marginTop: 11,
                              },
                            ]}>
                            <Text
                              style={[
                                styles.itemContentStyle,
                                {color: 'rgba(0,10,32,0.45)'},
                              ]}>
                              {'回复 ' +
                                item.parentUserName +
                                ': ' +
                                item.parentMessageContent}
                            </Text>
                          </View>
                          <View
                            style={[
                              {
                                flexDirection: 'row',
                                justifyContent: 'flex-start',
                                alignItems: 'flex-start',
                                marginTop: 8,
                              },
                            ]}>
                            <Text style={styles.itemContentStyle}>
                              {item.messageContent}
                            </Text>
                          </View>
                        </View>
                      ) : (
                        <View
                          style={[
                            {
                              flexDirection: 'row',
                              justifyContent: 'flex-start',
                              alignItems: 'flex-start',
                              marginTop: 10,
                            },
                          ]}>
                          <Text style={styles.itemContentStyle}>
                            {item.messageContent}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>
                );
              })}
            </View>
          ) : (
            <View />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('DailyAdd', {
            // 传递回调函数
            refresh: this.callBackFunction,
          });
        }}>
        <Image
          style={{width: 27, height: 27}}
          source={require('../../assets/icon/iconfont/addBlack.png')}></Image>
      </TouchableOpacity>
    );
  }
  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  openQryStartDate() {
    this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate);
  }

  callBackSelectQryStartDateValue(value) {
    console.log('==========提交时间选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectedQryStartDate: value,
    });
    if (value && value.length) {
      var qryStartTime = '';
      var vartime;
      for (var index = 0; index < value.length; index++) {
        vartime = value[index];
        if (index === 0) {
          qryStartTime += vartime;
        } else {
          qryStartTime += '-' + vartime;
        }
      }
      this.setState({
        qryStartTime: qryStartTime,
      });

      let loadUrl = '/biz/daily/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        userId: constants.loginUser.userId,
        qryStartTime: qryStartTime,
      };
      httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }
  }

  resetQry() {
    this.setState({
      qryStartTime: null,
    });
    let loadUrl = '/biz/daily/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      qryStartTime: null,
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  }

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/daily';
    let requestParams = {
      currentPage: 1,
      pageSize: 1000,
      userId: constants.loginUser.userId,
      qryStartTime: this.state.qryStartTime,
      dailyState: '0AA',
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Linking.openURL(response.data);
        // Alert.alert('确认', '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?', [
        //     {
        //         text: "不打开", onPress: () => {
        //             WToast.show({ data: '点击了不打开' });
        //         }
        //     },
        //     {
        //         text: "打开", onPress: () => {
        //             WToast.show({ data: '点击了打开' });
        //             // 直接打开外网链接
        //             Linking.openURL(response.data)
        //         }
        //     }
        // ]);
      }
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="我的日报"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.headViewStyle,
            {borderLeftWidth: 0, borderRightWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          {/*搜索框和导出*/}
          <View style={[CommonStyle.searchBoxAndExport, {}]}>
            <View style={CommonStyle.searchTimeBoxWithExport}>
              <TouchableOpacity onPress={() => this.openQryStartDate()}>
                <View
                  style={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    width: screenWidth / 1.9,
                    borderRadius: 80,
                  }}>
                  <Image
                    style={{width: 16, height: 16, marginLeft: 7}}
                    source={require('../../assets/icon/iconfont/search.png')}></Image>
                  <Text
                    style={{
                      fontFamily: 'PingFangSC-Medium',
                      fontSize: 15,
                      fontWeight: 'normal',
                      lineHeight: 28.08,
                      letterSpacing: 0,
                      color: '#CDCDCD',
                      fontVariationSettings: '"opsz" auto',
                      marginLeft: 15,
                    }}>
                    {!this.state.qryStartTime
                      ? '提交日期'
                      : this.state.qryStartTime}
                  </Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => this.resetQry()}>
                <View
                  style={[
                    CommonStyle.resetBtnViewStyle,
                    {
                      width: 10,
                      borderWidth: 0,
                      backgroundColor: 'rgba(0,0,0,0)',
                      borderRadius: 20,
                    },
                  ]}>
                  <Image
                    style={{width: 16, height: 16}}
                    source={require('../../assets/icon/iconfont/replace.png')}></Image>
                </View>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              onPress={() => {
                // 触发-导出弹窗Modal
                this.setState({
                  exportPdfModal: true,
                });
                // Alert.alert('确认', '您确定要将查询的日报导出为PDF文件吗？', [
                //     {
                //         text: "取消", onPress: () => {
                //             WToast.show({ data: '点击了取消' });
                //         }
                //     },
                //     {
                //         text: "确定", onPress: () => {
                //             WToast.show({ data: '点击了确定' });
                //             this.exportPdfFile()
                //         }
                //     }
                // ]);
              }}>
              <View
                style={[
                  CommonStyle.itemBottomDetailBtnViewStyle,
                  {
                    margin: 0,
                    alignItems: 'center',
                    width: 64,
                    backgroundColor: '#1E6EFA',
                    height: 32,
                    borderRadius: 20,
                  },
                ]}>
                {/* <View style={[ CommonStyle.itemBottomDetailBtnViewStyle,{ width: 64, height: 32, backgroundColor: "#1E6EFA", flexDirection: "row", borderRadius: 20,alignItems: 'center' }]}> */}
                {/* <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/output.png')}></Image> */}
                <Text
                  style={[
                    CommonStyle.itemBottomDetailBtnTextStyle,
                    {fontSize: 14},
                  ]}>
                  导出
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* 分享弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.shareModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: screenWidth,
                height: 200,
                bottom: 0,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderTopLeftRadius: 10,
                borderTopRightRadius: 10,
              }}>
              <View
                style={{
                  height: 120,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  index: 1000,
                  borderTopLeftRadius: 10,
                  borderTopRightRadius: 10,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    console.log('当前时间=====', this.state.currentTime);
                    console.log('日报=====', this.state.dailyItem);
                    var dailyDate = this.state.dailyItem.dailyDate.split('-');
                    var date = dailyDate[0] + dailyDate[1] + dailyDate[2];
                    console.log(
                      '标题=====',
                      '学习总结和计划' + constants.loginUser.userName + date,
                    );
                    // 分享微信好友
                    WeChat.shareWebpage({
                      title:
                        '学习总结和计划' + constants.loginUser.userName + date,
                      description:
                        '学习总结和计划' + constants.loginUser.userName + date,
                      thumbImageUrl:
                        'http://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/images/9801640945242_.pic.jpg',
                      webpageUrl:
                        'https://jzxs.njjzgk.com/html/share/dailyShare.html?dailyId=' +
                        this.state.dailyItem.dailyId,
                      scene: 0,
                    })
                      .then((respJSON) => {
                        WToast.show({
                          data: 'respJSON' + JSON.stringify(respJSON),
                        });
                      })
                      .catch((error) => {
                        WToast.show({data: error});
                        Alert.alert(error.message);
                      });
                    // 查询分享记录
                    let url = '/biz/portal/share/record/list';
                    let requestParams = {
                      shareAddr:
                        'https://jzxs.njjzgk.com/html/share/dailyShare.html?dailyId=' +
                        this.state.dailyItem.dailyId,
                      userId: constants.loginUser.userId,
                      shareScen: '0',
                    };
                    httpPost(url, requestParams, (response) => {
                      if (response.code == 200 && response.data) {
                        if (
                          response.data.dataList &&
                          response.data.dataList.length > 0
                        ) {
                        } else {
                          url = '/biz/portal/share/record/add';
                          requestParams = {
                            shareAddr:
                              'https://jzxs.njjzgk.com/html/share/dailyShare.html?dailyId=' +
                              this.state.dailyItem.dailyId,
                            userId: constants.loginUser.userId,
                            shareScen: '0',
                          };
                          httpPost(url, requestParams, (response) => {
                            if (response.code == 200 && response.data) {
                              console.log('分享记录添加成功');
                            } else if (response.code == 401) {
                              WToast.show({data: response.message});
                              this.props.navigation.navigate('LoginView');
                            } else {
                              WToast.show({data: response.message});
                            }
                          });

                          let loadUrl = '/biz/daily/addDailySharePoint';
                          let loadRequest = {
                            dailyId: this.state.dailyItem.dailyId,
                            scen: '0',
                          };
                          httpPost(loadUrl, loadRequest, (response) => {
                            if (response.code == 200 && response.data) {
                              console.log('打卡奖励积分添加成功');
                            } else if (response.code == 401) {
                              WToast.show({data: response.message});
                              this.props.navigation.navigate('LoginView');
                            } else {
                              WToast.show({data: response.message});
                            }
                          });
                        }
                      } else if (response.code == 401) {
                        WToast.show({data: response.message});
                        this.props.navigation.navigate('LoginView');
                      }
                    });
                  }}>
                  <View
                    style={[
                      {
                        width: 79,
                        height: 91,
                        flexDirection: 'column',
                        marginTop: 20,
                        marginRight: 30,
                        justifyContent: 'center',
                        alignItems: 'center',
                      },
                    ]}>
                    <Image
                      style={{width: 50, height: 50}}
                      source={require('../../assets/icon/iconfont/WeChat3.png')}></Image>
                    <Text
                      style={[
                        CommonStyle.itemBottomEditBtnTextStyle,
                        {marginTop: 8, color: 'rgba(0, 10, 32, 0.65)'},
                      ]}>
                      微信
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    console.log('当前时间=====', this.state.currentTime);
                    var dailyDate = this.state.dailyItem.dailyDate.split('-');
                    var date = dailyDate[0] + dailyDate[1] + dailyDate[2];
                    console.log(
                      '标题=====',
                      '学习总结和计划' + constants.loginUser.userName + date,
                    );
                    // 分享朋友圈
                    WeChat.shareWebpage({
                      title:
                        '学习总结和计划' + constants.loginUser.userName + date,
                      description:
                        '学习总结和计划' + constants.loginUser.userName + date,
                      thumbImageUrl:
                        'http://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/images/9801640945242_.pic.jpg',
                      webpageUrl:
                        'https://jzxs.njjzgk.com/html/share/dailyShare.html?dailyId=' +
                        this.state.dailyItem.dailyId,
                      scene: 1,
                    }).catch((error) => {
                      Alert.alert(error.message);
                    });

                    // 查询分享记录
                    let url = '/biz/portal/share/record/list';
                    let requestParams = {
                      shareAddr:
                        'https://jzxs.njjzgk.com/html/share/dailyShare.html?dailyId=' +
                        this.state.dailyItem.dailyId,
                      userId: constants.loginUser.userId,
                      shareScen: '1',
                    };
                    httpPost(url, requestParams, (response) => {
                      if (response.code == 200 && response.data) {
                        if (
                          response.data.dataList &&
                          response.data.dataList.length > 0
                        ) {
                        } else {
                          url = '/biz/portal/share/record/add';
                          requestParams = {
                            shareAddr:
                              'https://jzxs.njjzgk.com/html/share/dailyShare.html?dailyId=' +
                              this.state.dailyItem.dailyId,
                            userId: constants.loginUser.userId,
                            shareScen: '1',
                          };
                          httpPost(url, requestParams, (response) => {
                            if (response.code == 200 && response.data) {
                              console.log('分享记录添加成功');
                            } else if (response.code == 401) {
                              WToast.show({data: response.message});
                              this.props.navigation.navigate('LoginView');
                            } else {
                              WToast.show({data: response.message});
                            }
                          });

                          let loadUrl = '/biz/daily/addDailySharePoint';
                          let loadRequest = {
                            dailyId: this.state.dailyItem.dailyId,
                            scen: '1',
                          };
                          httpPost(loadUrl, loadRequest, (response) => {
                            if (response.code == 200 && response.data) {
                              console.log('打卡奖励积分添加成功');
                            } else if (response.code == 401) {
                              WToast.show({data: response.message});
                              this.props.navigation.navigate('LoginView');
                            } else {
                              WToast.show({data: response.message});
                            }
                          });
                        }
                      } else if (response.code == 401) {
                        WToast.show({data: response.message});
                        this.props.navigation.navigate('LoginView');
                      }
                    });
                  }}>
                  <View
                    style={[
                      {
                        width: 79,
                        height: 91,
                        flexDirection: 'column',
                        marginTop: 20,
                        marginLeft: 30,
                        justifyContent: 'center',
                        alignItems: 'center',
                      },
                    ]}>
                    <Image
                      style={{width: 50, height: 50}}
                      source={require('../../assets/icon/iconfont/WeChatFriendsCircle2.png')}></Image>
                    <Text
                      style={[
                        CommonStyle.itemBottomEditBtnTextStyle,
                        {marginTop: 8, color: 'rgba(0, 10, 32, 0.65)'},
                      ]}>
                      朋友圈
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: screenWidth,
                  height: 61,
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderTopWidth: 1,
                  borderTopColor: '#cccccc',
                  backgroundColor: '#FFFFFF',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      shareModal: false,
                    });
                  }}>
                  <View
                    style={{justifyContent: 'center', alignItems: 'center'}}>
                    <Text style={[{fontSize: 18, color: '#404956'}]}>取消</Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 更多操作弹窗Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.moreModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    if (
                      this.state.dailyItem.dailyState != '0BB' ||
                      this.state.dailyItem.auditScore ||
                      dateDiffHours(
                        this.state.currentTime,
                        this.state.dailyItem.gmtCreated,
                      ) > constants.loginUser.editDeleteTimeLimit
                    ) {
                      WToast.show({data: '该日报不可编辑'});
                      return;
                    }
                    this.setState({
                      moreModal: false,
                    });
                    this.props.navigation.navigate('DailyAdd', {
                      // 传递参数
                      dailyId: this.state.dailyItem.dailyId,
                      // 传递回调函数
                      refresh: this.callBackFunction,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                      this.state.dailyItem.dailyState != '0BB' ||
                      this.state.dailyItem.auditScore ||
                      dateDiffHours(
                        this.state.currentTime,
                        this.state.dailyItem.dailyDate,
                      ) > constants.loginUser.editDeleteTimeLimit
                        ? CommonStyle.disableViewStyle
                        : '',
                    ]}>
                    {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                    <Text
                      style={{
                        color: 'rgba(0, 10, 32, 0.85)',
                        fontSize: 18,
                        lineHeight: 52,
                      }}>
                      编辑
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              <View>
                <TouchableOpacity
                  onPress={() => {
                    console.log(
                      'dailyItem=================',
                      this.state.dailyItem,
                    );
                    if (
                      this.state.dailyItem.dailyState != '0BB' &&
                      this.state.dailyItem.auditScore
                    ) {
                      WToast.show({data: '日报已审核不可删除'});
                      return;
                    }
                    if (
                      this.state.dailyItem.dailyState != '0BB' &&
                      dateDiffHours(
                        this.state.currentTime,
                        this.state.dailyItem.gmtCreated,
                      ) > constants.loginUser.editDeleteTimeLimit
                    ) {
                      WToast.show({data: '日报已超出删除时限'});
                      return;
                    }
                    // 删除弹窗Modal
                    this.setState({
                      moreModal: false,
                      deleteModal: true,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                      this.state.dailyItem.auditScore ||
                      (this.state.dailyItem.dailyState != '0BB' &&
                        dateDiffHours(
                          this.state.currentTime,
                          this.state.dailyItem.dailyDate,
                        )) > constants.loginUser.editDeleteTimeLimit
                        ? CommonStyle.disableViewStyle
                        : '',
                    ]}>
                    {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                    <Text
                      style={[
                        {
                          color: 'rgba(0, 10, 32, 0.85)',
                          fontSize: 18,
                          lineHeight: 52,
                        },
                      ]}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: 291,
                  height: 50,
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      moreModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 105,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 删除弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.deleteModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 292,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认删除该日报?</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  删除后数据不可恢复，请谨慎操作
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 292,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了确定'});
                    this.deleteDaily(this.state.dailyItem.dailyId);
                  }}>
                  <View
                    style={[
                      {
                        width: 146,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 导出pdf弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.exportPdfModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认导出日报？</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  导出地址已复制到粘贴板，使用浏览器打开
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 291,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      exportPdfModal: false,
                    });
                    WToast.show({data: '点击了不打开'});
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      不打开
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    WToast.show({data: '点击了打开'});
                    this.setState({
                      exportPdfModal: false,
                    });
                    this.exportPdfFile();
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderLeftWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      打开
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 留言输入框弹窗 */}
        {/* 留言输入框弹窗 */}
        <MessageInputModal
          visible={this.state.messageModal}
          onClose={() =>
            this.setState({
              messageModal: false,
              messageContent: '',
            })
          }
          onSend={() => {
            if (!this.state.messageContent) {
              return;
            }
            this.setState({messageModal: false});
            this.saveDailyMessage();
          }}
          messageContent={this.state.messageContent}
          onChangeMessageContent={(text) =>
            this.setState({messageContent: text})
          }
        />

        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          <FlatList
            data={this.state.dataSource}
            keyExtractor={(item) => item.dailyId}
            renderItem={({item, index}) => this.renderRow(item, index)}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>

        <BottomScrollSelect
          ref={'SelectQryStartDate'}
          callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    // marginTop:10,
    borderLeftColor: '#FFFFFF',
    borderRightColor: '#FFFFFF',
    borderTopColor: '#F4F4F4',
    borderBottomColor: '#F4F4F4',
    borderWidth: 4,
  },
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#F2F5FC',
    backgroundColor: '#FFFFFF',
    borderRadius: 50,
    marginTop: 5,
  },
  itemContentTextStyle: {
    marginLeft: 14,
    marginRight: 16,
    marginTop: 3,
  },
  titleViewStyle: {
    flexDirection: 'row',
    marginLeft: 14,
    marginRight: 16,
    marginTop: 5,
    alignItems: 'center',
  },
  titleTextStyle: {
    fontFamily: 'PingFangSC-Bold',
    fontSize: 16,
    fontWeight: 'normal',
    lineHeight: 23.4,
    letterSpacing: 0,
  },
  itemContentStyle: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: 14,
    fontWeight: 'normal',
    lineHeight: 23.4,
    letterSpacing: 0,
    textAlign: 'left',
    textAlignVertical: 'top',
    color: 'rgba(0, 10, 32, 0.65)',
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  lineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    marginBottom: 6,
    borderBottomWidth: 0.5,
    borderColor: '#E8E9EC',
  },
  auditPendingBadge: {
    position: 'absolute',
    top: 5,
    right: 20,
    width: 50,
    height: 20,
    borderRadius: 90,
    
    justifyContent: 'center',
    alignItems: 'center',
    },
  auditPendingText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: 12,
    fontWeight: 'normal',
    lineHeight: 20,
    color: '#FFFFFF',
    textAlign: 'center'
    }
});
