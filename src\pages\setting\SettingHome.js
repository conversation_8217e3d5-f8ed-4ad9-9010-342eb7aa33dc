import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Image, FlatList, Dimensions, ScrollView,
    TouchableOpacity, ImageBackground, StatusBar
} from 'react-native';
import CommonHeadScreen from '../../component/CommonHeadScreen';
var CommonStyle = require('../../assets/css/CommonStyle');
// var Dimensions = require('Dimensions');\
import { ifIphoneXHeaderHeight } from '../../utils/ScreenUtil';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
var cols = 4;
var cellWH = 100;
var vMargin = (screenWidth - cellWH * cols) / (cols + 1);
var hMargin = 25;
const leftLabWidth = 130;
class SettingHome extends Component {
    constructor(props) {
        super(props);
        console.log("======Home=Props:", props.navigationContext);
        this.state = {
            "settingMenuDataSource": [
                {
                    "code": "user_info",
                    "icon": require('../../assets/icon/workbench/workbench_user_info.png'),
                    "title": "个人信息",
                    "component": "Profile"
                },
                {
                    "code": "pwd_reset",
                    "icon": require('../../assets/icon/workbench/workbench_modify_pwd.png'),
                    "title": "修改密码",
                    "component": "ResetPwd"
                },
                {
                    "code": "help_center",
                    "icon": require('../../assets/icon/workbench/help_center.png'),
                    "title": "帮助中心",
                    "component": "HelpCenterList"
                },
                {
                    "code": "suggestion_feedback",
                    "icon": require('../../assets/icon/workbench/suggestion_feedback.png'),
                    "title": "意见反馈",
                    "component": "SuggestionFeedbackAdd"
                },
                // {
                //     "code":"message_remind",
                //     "icon": require('../../assets/icon/workbench/message_icon1.png'),
                //     "title": "我的消息",
                //     "component":"MessageRemind"
                // },
                {
                    "code": "logout",
                    "icon": require('../../assets/icon/workbench/workbench_logout.png'),
                    "title": "退出"
                }
                // {
                //     "code":"message_remind",
                //     "icon": require('../../assets/icon/workbench/message_icon_no_read_tip.png'),
                //     "title": "消息未读",
                //     "component":"MessageRemind"
                // }
            ],
            userPhotoUrl: constants.image_addr + '/' + constants.loginUser.userPhoto,
            userPhoto: constants.loginUser.userPhoto,
            myBacklogDailyTotalRecord: 0,
            promotionPlanTotalRecord: 0,
            dailyTotalRecord: 0,
            HarvestTotalRecord: 0,
            menuTypeFlagDR: false
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        console.log("=====tenantExtAttrJSON==", constants.tenantExtAttrJSON);
        if(null != constants.tenantExtAttrJSON ){
            if(constants.tenantExtAttrJSON.menuTypes.indexOf('D') > 0 || constants.tenantExtAttrJSON.menuTypes.indexOf('R') > 0){
                if(constants.tenantExtAttrJSON.menuTypes.indexOf('M') <= 0 && constants.tenantExtAttrJSON.menuTypes.indexOf('H') <= 0 && constants.tenantExtAttrJSON.menuTypes.indexOf('S') <= 0){
                    this.setState({
                        menuTypeFlagDR: true,
                    })
                    this.loadMyBacklogDailyData();
                    this.loadDailyData();
                    this.loadPromotionPlanData();
                    this.loadHarvestData();
                    console.log("==================D,R==================")
                }
            }
        }
        this.changeMessageRemindIcon();
    }

    loadMyBacklogDailyData = () => {
        let url = "/biz/daily/audit/task/list";
        let loadRequest = {
            "userId": constants.loginUser.userId,
            "my_backlog": "Y",
            "dailyState": "0AA"
        };
        httpPost(url, loadRequest, this.loadMyBacklogDailyDataCallBack);
    }

    loadMyBacklogDailyDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                myBacklogDailyTotalRecord: response.data.totalRecord
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadDailyData = () => {
        let url = "/biz/daily/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 15,
            "userId": constants.loginUser.userId
        };
        httpPost(url, loadRequest, this.loadDailyDataCallBack);
    }

    loadDailyDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                dailyTotalRecord: response.data.totalRecord
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadPromotionPlanData = () => {
        let url = "/biz/promotion/plan/list";
        let loadRequest = {
            "userId": constants.loginUser.userId
        };
        httpPost(url, loadRequest, this.loadPromotionPlanDataCallBack);
    }

    loadPromotionPlanDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                promotionPlanTotalRecord: response.data.totalRecord
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadHarvestData = () => {
        let url = "/biz/harvest/list";
        let loadRequest = {
            "userId": constants.loginUser.userId
        };
        httpPost(url, loadRequest, this.loadHarvestDataCallBack);
    }

    loadHarvestDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                HarvestTotalRecord: response.data.totalRecord
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    changeMessageRemindIcon = () => {
        // console.log("123")
        var settingMenuDataSource = this.state.settingMenuDataSource;
        if (!constants.noReadMessageCount || constants.noReadMessageCount == 0) {
            settingMenuDataSource.map(item => {
                if (item.code == "message_remind") {
                    item.icon = require('../../assets/icon/workbench/message_icon1.png')
                }
            })
        }
        else {
            settingMenuDataSource.map(item => {
                if (item.code == "message_remind") {
                    item.icon = require('../../assets/icon/workbench/message_icon1.png')

                }
            })
        }
        this.setState({
            settingMenuDataSource: settingMenuDataSource
        })
    }


    // loadMessageRemindList=()=>{
    //     let url= "/biz/portal/message/remind/list";
    //     let loadRequest={
    //         "currentPage": 1,
    //         "pageSize": 1000,
    //         "messageFlag":'0',
    //         "messageToUserId":constants.loginUser.userId
    //     };
    //     httpPost(url, loadRequest, this.loadMessageRemindListCallBack);
    // }

    // loadMessageRemindListCallBack=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             totalRecord:response.data.totalRecord
    //         })
    //         var settingMenuDataSource = this.state.settingMenuDataSource;
    //         if(response.data.totalRecord > 0) {
    //             settingMenuDataSource.map(item => {
    //                 if(item.code == "message_remind"){
    //                     item.icon = require('../../assets/icon/workbench/message_icon_no_read_tip.png')
    //                 }
    //             })
    //         }
    //         else{
    //             settingMenuDataSource.map(item => {
    //                 if(item.code == "message_remind"){
    //                     item.icon = require('../../assets/icon/workbench/message_icon.png')
    //                 }
    //             })
    //         }
    //         this.setState({
    //             settingMenuDataSource:settingMenuDataSource
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // 头部左侧
    renderLeftItem() {
        return (
            <View></View>
        )
    }
    // 头部中间
    renderTitleItem() {
        return (
            <Text>首页</Text>
        )
    }

    logout = () => {
        console.log("===logout");
        let url = "/biz/user/logout?a=123&b=234"
        httpGet(url, this.logout_call_back);
    }

    logout_call_back = (response) => {
        console.log("=====logout_call_back:", response);
        this.props.navigation.navigate('MyCenterOrLogin');
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    // 点击跳转
    _pressJump(item) {
        const { navigation } = this.props;
        if (navigation && item.component != null) {
            navigation.navigate(item.component, {
                // 测试参数
                itemId: 1000000,
                code: item.code,
                title: item.title,
                isPersonalCenter: item.code == "message_remind" ? "Y" : null,
                refresh: item.code == "message_remind" ? this.changeMessageRemindIcon : null
            })
        }
    }

    renderRow = (item) => {
        if ("hidden" === item.visibility) {
            return;
        }
        // 登出
        if ("logout" === item.code) {
            return (
                <TouchableOpacity onPress={this.logout.bind(this)}>
                    <View key={item.code} style={styles.innerViewStyle}>
                        <Image style={styles.innerViewImageStyle} source={item.icon}></Image>
                        <Text style={CommonStyle.bodyTextStyle}>{item.title}</Text>
                    </View>
                </TouchableOpacity>
            )
        }
        return (
            <TouchableOpacity onPress={this._pressJump.bind(this, item)} >
                <View key={item.code} style={styles.innerViewStyle}>
                    <Image style={styles.innerViewImageStyle} source={item.icon}></Image>
                    <Text style={CommonStyle.bodyTextStyle}>{item.title}</Text>
                </View>
            </TouchableOpacity>

        )
    }

    render() {
        return (
            <View>
                {/* <CommonHeadScreen title='个人中心'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()} /> */}

                <ImageBackground source={require('../../assets/image/personalCenterBackground2.png')} style={{ width: screenWidth, height: 165+ifIphoneXHeaderHeight() }}>
                    <StatusBar
                        // animated={true} //指定状态栏的变化是否应以动画形式呈现。目前支持这几种样式：backgroundColor, barStyle和hidden  
                        hidden={false}  //是否隐藏状态栏。
                        backgroundColor='transparent' //状态栏的背景色  
                        //translucent={true} //指定状态栏是否透明。设置为true时，应用会在状态栏之下绘制（即所谓“沉浸式”——被状态栏遮住一部分）。常和带有半透明背景色的状态栏搭配使用。  
                        barStyle={'dark-content'} // enum('default', 'light-content', 'dark-content')   
                    >
                    </StatusBar>

                    {/* 解决主体内容和状态栏重合的问题 */}
                    {/* <View style={{height: StatusBar.currentHeight}}></View> */}

                    <View style={{width: screenWidth, height: ifIphoneXHeaderHeight()}}>
                        <View style={{
                            paddingRight: 10, paddingLeft: 10, paddingBottom: 12, marginTop: 0,
                            height: ifIphoneXHeaderHeight(),
                            flexDirection: 'row',//横向排
                            justifyContent: 'space-between',//主轴对齐方式
                            alignItems: 'flex-end',//次轴对齐方式（上下居中）
                            // borderBottomWidth: this.props.borderBottomWidth || 1,//是否有下边框
                            borderColor: '#ccc',
                        }}>
                            <View>
                                {this.renderLeftItem()}
                            </View>
                            <View>
                                <Text style={styles.headCenterTitleText}>个人中心</Text>
                            </View>
                            <View>
                                {this.renderRightItem()}
                            </View>
                        </View>
                    </View>

                    {/* <View style={[styles.inputRowStyle, { flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 16, height: 60, borderWidth: 1, marginTop: 18}]}>
                        <TouchableOpacity onPress={() => {
                            this.props.navigation.navigate("Profile", 
                            {
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                            <View style={[{ width: screenWidth - (leftLabWidth + 25), paddingTop: 5, position: 'relative' }]}>
                                {
                                    this.state.userPhoto ?
                                    <Image source={{ uri: this.state.userPhotoUrl }} style={{ height: 48, width: 48, borderRadius: 50 }} />
                                    :
                                    <Image style={{ width: 48, height: 51 }} source={require('../../assets/icon/iconfont/newAddHead.png')}></Image>
                                }
                                
                            </View>

                            <View style={{ flexDirection: "column", }}>
                                    <View style={{ justifyContent: "space-between", width: screenWidth - 15, flexDirection: 'row', position: 'absolute', paddingTop: 4,backgroundColor:"rgba(0, 10, 32, 0.45)" }}>
                                            <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 22, color: "#FFFFFF"}}>{constants.loginUser.userName}</Text>
                                    </View>
                                    <View style={[{flexDirection: 'row', justifyContent: 'flex-start', width: 30, flexDirection: 'row', paddingLeft: 65, position: 'absolute', paddingTop: 35, backgroundColor: "#020202"}]}>
                                        <Text style={{fontSize: 14, color: "#FFFFFF"}}>15879777899</Text>
                                    </View>
                                </View>
                        </TouchableOpacity>
                    </View> */}

                    <View>
                        <TouchableOpacity onPress={() => {
                            this.props.navigation.navigate("Profile",
                                {
                                    // 传递回调函数
                                    refresh: this.callBackFunction
                                })
                        }}>
                            <View style={[{ flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 16, height: 60, marginTop: 18 }]}>
                                <View style={[{ width: 60, paddingTop: 5, position: 'relative' }]}>
                                    {
                                        this.state.userPhoto ?
                                            <Image source={{ uri: this.state.userPhotoUrl }} style={{ height: 60, width: 60, borderRadius: 50 }} />
                                            :
                                            <Image style={{ width: 60, height: 60 }} source={require('../../assets/icon/iconfont/newAddHead.png')}></Image>
                                    }
                                </View>

                                <View style={{ justifyContent: "space-between", flexDirection: 'column', position: 'absolute', marginLeft: 60, paddingTop: 4 }}>
                                    <View style={{ flexDirection: 'row', paddingLeft: 10 }}>
                                        <Text style={{fontSize: 22, color: "#FFFFFF" }}>{constants.loginUser.userName}</Text>
                                    </View>
                                    
                                    <View style={{ flexDirection: 'row', marginLeft: 5, marginTop: 5,  }}>
                                        <View style={{  backgroundColor: 'rgba(0, 10, 32, 0.2)',paddingLeft: 9, paddingTop: 3, paddingBottom: 3, paddingRight: 12, borderRadius: 12 }}>
                                            <Text style={{fontSize: 14, color: "#FFFFFF" }}>账号：{constants.loginUser.userCode}</Text>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        </TouchableOpacity>
                    </View>

                    {this.state.menuTypeFlagDR ?
                        <View style={{
                            flexDirection: 'row', justifyContent: 'space-around', flexWrap: 'wrap', width: screenWidth-32,
                            backgroundColor: "#FFFFFF", marginTop: 20, marginLeft: 16, marginRight: 16, borderRadius: 12
                        }}>
                            <View style={{marginTop: 20, marginLeft:20}}>
                                <TouchableOpacity onPress={()=>{
                                    this.props.navigation.navigate("MyBacklogDailyList", 
                                    {
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                                    <View style={ { height: 60,flexDirection: "column"}}>
                                        <Text style={{color: '#111111',fontSize: 18}}>{this.state.myBacklogDailyTotalRecord}</Text>
                                        <Text style={{color: 'rgba(0, 10, 32, 0.45)',fontSize: 14,marginTop: 5}}>我的待办</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {/* 分隔线 */}
                            <View style={styles.columnLineViewStyle}></View>
                            
                            <View style={{marginTop: 20}}>
                                <TouchableOpacity onPress={()=>{
                                    this.props.navigation.navigate("DailyList", 
                                    {
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                                    <View style={ { height: 60, flexDirection: "column" }}>
                                        <Text style={{color: '#111111',fontSize: 18}}>{this.state.dailyTotalRecord}</Text>
                                        <Text style={{color: 'rgba(0, 10, 32, 0.45)',fontSize: 14,marginTop: 5}}>我的日报</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {/* 分隔线 */}
                            <View style={styles.columnLineViewStyle}></View>
                            
                            <View style={{marginTop: 20}}>
                                <TouchableOpacity onPress={()=>{
                                    this.props.navigation.navigate("PromotionPlanList", 
                                    {
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                                    <View style={ { height: 60, flexDirection: "column" }}>
                                        <Text style={{color: '#111111',fontSize: 18}}>{this.state.promotionPlanTotalRecord}</Text>
                                        <Text style={{color: 'rgba(0, 10, 32, 0.45)',fontSize: 14,marginTop: 5}}>我的任务</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {/* 分隔线 */}
                            <View style={styles.columnLineViewStyle}></View>

                            <View style={{marginTop: 20, marginRight: 14}}>
                                <TouchableOpacity onPress={()=>{
                                    this.props.navigation.navigate("HarvestMgrList", 
                                    {
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                                    <View style={ { height: 60, flexDirection: "column" }}>
                                        <Text style={{color: '#111111',fontSize: 18}}>{this.state.HarvestTotalRecord}</Text>
                                        <Text style={{color: 'rgba(0, 10, 32, 0.45)',fontSize: 14,marginTop: 5}}>我的成果</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                        :
                        <View />
                    }
                </ImageBackground>


                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    {/* <View style={{
                        flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'center',
                        width: screenWidth, marginTop: 2, backgroundColor: '#FFF'
                    }}>
                        <Image style={[{ height: 180, borderRadius: 10, width: screenWidth / 0.9 }]} source={require('../../assets/image/personalCenterBackground.jpg')} />
                    </View> */}


                    <View style={[{ width: screenWidth, justifyContent: 'flex-start', }]}>
                        <FlatList
                            numColumns={4}
                            data={this.state.settingMenuDataSource}
                            renderItem={({ item }) => this.renderRow(item)}
                        />
                    </View>
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140
    // },
    classViewStyle: {
        height: 50,
        alignItems: 'flex-start',
        justifyContent: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0'
    },
    classTextStyle: {
        color: '#000',
        fontSize: 20,
        marginLeft: 15
    },
    innerViewStyle: {
        width: cellWH,
        height: cellWH,
        marginLeft: vMargin,
        marginTop: hMargin,
        alignItems: 'center',
        justifyContent: 'center'
    },
    innerViewImageStyle: {
        width: cellWH - 50,
        height: cellWH - 50
    },
    headCenterTitleText:{
        fontSize:20,
        color:'#ffffff',
        fontWeight:'600',
     },
    columnLineViewStyle:{
        width:1,
        marginTop:17,
        marginBottom:17,
        flexDirection:'row',
        justifyContent:'center',
        borderRightWidth:1,
        borderColor:'rgba(117, 117, 117, 0.10)'
    },
});
module.exports = SettingHome;