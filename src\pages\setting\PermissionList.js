import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class PermissionList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            selPermissionCode:""
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadDomainList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/domain/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "tableName":"SP_PERMISSION",
            "columnName":"PERMISSION_CODE"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/domain/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "tableName":"SP_PERMISSION",
            "columnName":"PERMISSION_CODE"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadDomainList();
    }

    loadDomainList=()=>{
        let url= "/biz/domain/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "tableName":"SP_PERMISSION",
            "columnName":"PERMISSION_CODE"
        };
        httpPost(url, loadRequest, this.loadDomainListCallBack);
    }

    loadDomainListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow=(item, index)=>{
        return (
            <TouchableOpacity onPress={()=>{
                    this.setState({
                        selPermissionCode:item.value
                    })
                    this.props.navigation.navigate("PermissionUserList", 
                    {
                        // 传递参数
                        permissionCode:item.value,
                        permissionName:item.comments,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })
                }}>
                <View key={item.value} style={[styles.innerViewStyle,
                    this.state.selPermissionCode == item.value ? { backgroundColor:'rgba(255,0,0,0.4)',borderRadius:20,hight:80} : {}]}>
                        {
                            index == 0 ?
                                <View style={{ width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF', borderBottomWidth: 10, borderBottomColor: '#F4F7F9' }}>
                                </View>
                                :
                                <View></View>
                        }
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>编号：{index + 1}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>权限名称：{item.comments}</Text>
                    </View>
                    <View style={{width: 40, height: 40, marginBottom:10,
                        backgroundColor: 'rgba(255,0,0,0.0)', 
                        position:'absolute', 
                        alignItems:'center',
                        justifyContent:'center',
                        right: 5,bottom:0
                        }}>
                        <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/enter4.png')}></Image>
                    </View>
                </View>
            </TouchableOpacity>
        )
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='权限管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <View style={CommonStyle.contentViewStyle}>
                            <FlatList 
                                data={this.state.dataSource}
                                renderItem={({item,index}) => this.renderRow(item, index)}
                                ListEmptyComponent={this.emptyComponent}
                                ItemSeparatorComponent={this.space}
                                // 自定义下拉刷新
                                refreshControl={
                                    <RefreshControl
                                    tintColor="#FF0000"
                                    title="loading"
                                    colors={['#FF0000', '#00FF00', '#0000FF']}
                                    progressBackgroundColor="#FFFF00"
                                    refreshing={this.state.refreshing}
                                    onRefresh={()=>{
                                        this._loadFreshData()
                                    }}
                                    />
                                }
                                // 底部加载
                                ListFooterComponent={()=>this.flatListFooterComponent()}
                                onEndReached={()=>this._loadNextData()}
                                onEndReachedThreshold={0.2}
                                />
                    </View>
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        marginLeft:20,
        marginRight:20,
        // borderWidth: 8
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },

});