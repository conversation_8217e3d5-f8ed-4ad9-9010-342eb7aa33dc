import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class SuggestionFeedbackList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            standardType:"E",
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            qryStartTime:null,
            selectedQryStartDate:[],
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectedQryStartDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            // qryStartTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })
        this.loadHarvestList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/suggest/feedback/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/suggest/feedback/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadHarvestList();
    }

    loadHarvestList=()=>{
        let url= "/biz/suggest/feedback/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this.loadHarvestListCallBack);
    }

    loadHarvestListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteHarvest =(feedbackId)=> {
        console.log("=======delete=feedbackId", feedbackId);
        let url= "/biz/suggest/feedback/delete";
        let requestParams={'feedbackId':feedbackId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.feedbackId} style={styles.innerViewStyle}>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>反馈：</Text>
                    {
                        item.reply ? 
                        null
                        :
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>未回复</Text>
                    }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.feedbackContent}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>提交时间：{item.gmtCreated}</Text>
                </View>
                {
                item.reply ? <View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>回复：{item.reply}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>回复时间：{item.gmtModified}</Text>
                    </View>
                </View> : 
                <View></View>
                }
                
                
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteHarvest(item.feedbackId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle
                        ]}>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    {
                        item.reply ? 
                        null
                        :
                        
                        <TouchableOpacity onPress={()=>{
                            this.props.navigation.push("SuggestionFeedbackAdd", 
                            {
                                // 传递参数
                                feedbackId: item.feedbackId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                            }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle]}>
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                            </View>
                        </TouchableOpacity>
                    }
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("SuggestionFeedbackAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>新增反馈</Text>
            </TouchableOpacity>
        )
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    openQryStartDate(){
        this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate)
    }
    
    callBackSelectQryStartDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryStartDate:value
        })
        if (value && value.length) {
            var qryStartTime = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    qryStartTime += vartime;
                }
                else{
                    qryStartTime += "-" + vartime;
                }
            }
            this.setState({
                qryStartTime:qryStartTime
            })

            let loadUrl= "/biz/suggest/feedback/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "userId": constants.loginUser.userId,
                "qryStartTime": qryStartTime,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }
    }

    resetQry(){
        this.setState({
            qryStartTime:null,
        })
        let loadUrl= "/biz/suggest/feedback/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "userId": constants.loginUser.userId,
            "qryStartTime": null,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }


    render(){
        return(
            <View>
                <CommonHeadScreen title='意见反馈'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                {/* <View style={CommonStyle.rightTop50FloatingBlockView}>
                    <Text style={CommonStyle.rightTop50FloatingBlockText}>{this.state.dataSource.length}</Text>
                </View> */}
                <View style={[styles.innerViewStyle,{justifyContent:"space-between",alignItems:'center',marginTop:0, index:1000, flexWrap:'wrap', flexDirection:'row'}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={[CommonStyle.inputTextStyleViewStyle,{marginLeft:-5}]}>
                        <Text style={CommonStyle.blockItemTextStyle16}>提交日期:</Text>
                    </View>
                    <TouchableOpacity onPress={()=>this.openQryStartDate()}>
                    <View style={{alignItems:'center',flexDirection: 'row',height:35,width:screenWidth/1.9,marginLeft:-90,backgroundColor:"#FFFFFF"}}>
                        <Image  style={{width:25, height:25,marginLeft:10}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            <Text style={{color:'#A0A0A0', fontSize:16,marginLeft:5}}>
                                    {!this.state.qryStartTime ? "提交日期" : this.state.qryStartTime}
                            </Text>
                    </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>this.resetQry()}>
                            <View style={[CommonStyle.resetBtnViewStyle, {width:20,padding:0,borderWidth:0,backgroundColor:'rgba(0,0,0,0)',marginLeft:-115}]}>
                                <Image  style={{width:20, height:20}} source={require('../../assets/icon/iconfont/replace.png')}></Image>
                            </View>
                    </TouchableOpacity>
                    </View>
                    {/* <TouchableOpacity onPress={()=>this.resetQry()}>
                        <View style={[CommonStyle.resetBtnViewStyle]}>
                            <Text style={CommonStyle.resetBtntextStyle}>重置</Text>
                        </View>
                    </TouchableOpacity> */}
                
                
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
                <BottomScrollSelect 
                    ref={'SelectQryStartDate'} 
                    callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
        
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});