import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class QueryMyScore extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      courseLevelDataSource: [],
      topBlockLayoutHeight: 0,
      // 选中的职级信息
      selCourseLevelId: null,
      selCourseLevelName: null,
      // 选中的实习Id
      selCourseId: null,
      // 实习类型数据
      courseTypeDataSource: [],
      // 选中的实习类型
      selectedCourseType: [],
      // 选中的课实习类型信息
      selCourseTypeId: null,
      selCourseTypeName: null,
      // 控制职级搜索栏的显示
      showSearchItemBlock: false,
      searchKeyWord: null,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    let loadTypeUrl;
    let loadRequest;

    // 获取职级列表
    loadTypeUrl = '/biz/course/level/list';
    loadRequest = {
      currentPage: 1,
      pageSize: 1000,
    };
    httpPost(loadTypeUrl, loadRequest, (response) => {
      if (response.code == 200 && response.data && response.data.dataList) {
        let dataSource = response.data.dataList;
        dataSource.unshift({courseLevelId: 0, courseLevelName: '全部'});
        this.setState({
          courseLevelDataSource: dataSource,
        });
      }
    });
    // 获取实习类型列表
    this.loadCourseTypeList();
    // 获取实习分数列表
    this.loadCourseList();
  }

  loadCourseTypeList = () => {
    let url = '/biz/course/type/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: 1000,
    };
    httpPost(url, loadRequest, this.loadCourseTypeListCallBack);
  };
  loadCourseTypeListCallBack = (response) => {
    if (response.code == 200 && response.data.dataList) {
      let dataSource = response.data.dataList;
      dataSource.unshift({courseTypeId: 0, courseTypeName: '全部'});
      this.setState({
        courseTypeDataSource: dataSource,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/score/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      courseLevelId:
        this.state.selCourseLevelId === 0 ? null : this.state.selCourseLevelId,
      courseTypeId: this.state.selCourseTypeId,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/score/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      courseLevelId:
        this.state.selCourseLevelId === 0 ? null : this.state.selCourseLevelId,
      courseTypeId: this.state.selCourseTypeId,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadCourseList();
  };

  loadCourseList = () => {
    let url = '/biz/score/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      courseLevelId:
        this.state.selCourseLevelId === 0 ? null : this.state.selCourseLevelId,
      courseTypeId: this.state.selCourseTypeId,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this.loadCourseListCallBack);
  };

  loadCourseListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  renderRow = (item, index) => {
    return (
      // <TouchableOpacity onPress={()=>{
      //     this.setState({
      //         selCourseId:item.courseId
      //     })
      // }}>
      <View
        key={item.courseId}
        style={[{paddingBottom: 10, marginLeft: 10, marginRight: 10}]}>
        {/* <View key={item.courseId} style={styles.innerViewStyle}> */}
        <View style={[styles.titleViewStyle]}>
          <Text style={styles.titleTextStyle}>实习名称：</Text>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[styles.itemContentStyle]}>
            {item.courseSort
              ? '任务' + parseInt(item.courseSort) + ' ' + item.courseName
              : item.courseName}
          </Text>
        </View>
        <View style={[styles.titleViewStyle]}>
          <Text style={styles.titleTextStyle}>实习类型：</Text>
          <Text style={styles.itemContentStyle}>
            {item.courseTypeName ? item.courseTypeName : '暂无信息'}
          </Text>
        </View>
        <View style={[styles.titleViewStyle]}>
          <Text style={styles.titleTextStyle}>所属职级：</Text>
          <Text style={styles.itemContentStyle}>
            {item.courseLevelName ? item.courseLevelName : '暂无信息'}
          </Text>
        </View>
        <View style={[styles.titleViewStyle]}>
          <Text style={styles.titleTextStyle}>成绩：</Text>
          <Text style={styles.itemContentStyle}>{item.score}</Text>
        </View>
        {item.score == null ? (
          <View></View>
        ) : (
          <View style={styles.titleViewStyle}>
            <Text style={styles.titleTextStyle}>录入时间：</Text>
            {item.gmtModified == null ? (
              <Text style={styles.itemContentStyle}>{item.gmtCreated}</Text>
            ) : (
              <Text style={styles.itemContentStyle}>{item.gmtModified}</Text>
            )}
          </View>
        )}
        <View style={styles.lineViewStyle} />
      </View>
      // </TouchableOpacity>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return <View></View>;
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  // 渲染职级类型底部滚动数据
  openCourseTypeSelect() {
    if (
      !this.state.courseTypeDataSource ||
      this.state.courseTypeDataSource.length < 1
    ) {
      WToast.show({data: '请先添加实习类型'});
      return;
    }
    this.refs.SelectCourseType.showCourseTypeId(
      this.state.selectedCourseType,
      this.state.courseTypeDataSource,
    );
  }

  callBackCourseTypeValue(value) {
    console.log('==========实习类型选择的结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectedCourseType: value,
    });

    var courseTypeName = value.toString();
    if (courseTypeName === '全部') {
      this.setState({
        selCourseTypeName: '全部',
        selCourseTypeId: 1,
      });
      let url = '/biz/score/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        userId: constants.loginUser.userId,
        courseLevelId:
          this.state.selCourseLevelId === 0
            ? null
            : this.state.selCourseLevelId,
        courseTypeId: null,
        searchKeyWord: this.state.searchKeyWord,
      };
      httpPost(url, loadRequest, this._loadFreshDataCallBack);
    } else {
      var courseTypeName = value.toString();
      let loadUrl = '/biz/course/type/getCourseTypeByName';
      let loadRequest = {
        courseTypeName: courseTypeName,
      };
      httpPost(loadUrl, loadRequest, this.callBackLoadCourseTypeData);
    }
  }

  callBackLoadCourseTypeData = (response) => {
    if (response.code == 200 && response.data) {
      this.setState({
        selCourseTypeId: response.data.courseTypeId,
        selCourseTypeName: response.data.courseTypeName,
      });
      let url = '/biz/score/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        userId: constants.loginUser.userId,
        courseLevelId:
          this.state.selCourseLevelId === 0
            ? null
            : this.state.selCourseLevelId,
        courseTypeId: response.data.courseTypeId,
        searchKeyWord: this.state.searchKeyWord,
      };
      httpPost(url, loadRequest, this._loadFreshDataCallBack);
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  renderCourseLevelRow = (item, index) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selCourseLevelId: item.courseLevelId,
            selCourseLevelName: item.courseLevelName,
          });
          console.log('item.courseLevelId' + item.courseLevelId);
        }}>
        <View
          key={'courseLevel_' + item.courseLevelId}
          style={[
            item.courseLevelId === this.state.selCourseLevelId
              ? CommonStyle.choseToSearchItemsSelectedViewColor
              : CommonStyle.choseToSearchItemsViewColor,
            CommonStyle.choseToSearchItemsViewSize,
          ]}>
          <Text
            style={[
              item.courseLevelId === this.state.selCourseLevelId
                ? CommonStyle.choseToSearchItemsSelectedTextStyle
                : CommonStyle.choseToSearchItemsTextStyle,
            ]}>
            {item.courseLevelName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  // 显示搜索项目
  showSearchItemSelect() {
    if (
      !this.state.courseLevelDataSource ||
      this.state.courseLevelDataSource.length < 1
    ) {
      WToast.show({data: '请先添加职级'});
      return;
    }
    this.setState({
      showSearchItemBlock: true,
    });
  }

  searchByKeyWord = () => {
    let loadUrl = '/biz/score/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      userId: constants.loginUser.userId,
      courseLevelId:
        this.state.selCourseLevelId === 0 ? null : this.state.selCourseLevelId,
      courseTypeId: this.state.selCourseTypeId,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="我的成绩"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        {/* <View
          style={[
            CommonStyle.rightTop50FloatingBlockView,
            {
              height: 32,
              width: 110,
              opacity: 0.6,
              borderRadius: 8,
              backgroundColor: 'rgba(242, 245, 252, 1)',
            },
          ]}>
          <TouchableOpacity onPress={() => this.openCourseTypeSelect()}>
            <Text style={{color: 'rgba(0,10,32,0.85)', fontSize: 14}}>
              {!this.state.selCourseTypeId
                ? '实习类型'
                : this.state.selCourseTypeName}
            </Text>
          </TouchableOpacity>
        </View> */}
        <View
          style={[
            CommonStyle.headViewStyle,
            {borderLeftWidth: 0, borderRightWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'flex-start',
              flexWrap: 'wrap',
              flexDirection: 'row',
            }}>
            {/* <View style={{flexWrap: 'wrap', flexDirection: 'row'}}>
              <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                {this.state.showSearchItemBlock ? (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                      {this.state.selCourseLevelId &&
                      this.state.selCourseLevelName
                        ? '职级：' + this.state.selCourseLevelName
                        : '选择职级'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchClosedIconSize]}
                      source={require('../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                  </View>
                ) : (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                      {this.state.selCourseLevelId &&
                      this.state.selCourseLevelName
                        ? '职级：' + this.state.selCourseLevelName
                        : '选择职级'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchOpenedIconSize]}
                      source={require('../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                  </View>
                )}
              </TouchableOpacity>
            </View> */}
            <View
              style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                backgroundColor: 'rgb(255, 255, 255)',
                alignItems: 'center',
                marginLeft: 10,
              }}>
              <View style={{flexWrap: 'wrap', flexDirection: 'row'}}>
                <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                  {this.state.showSearchItemBlock ? (
                    <View style={[CommonStyle.choseToSearchViewStyle]}>
                      <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                        {this.state.selCourseLevelId &&
                        this.state.selCourseLevelName
                          ? '职级：' + this.state.selCourseLevelName
                          : '选择职级'}
                      </Text>
                      <Image
                        style={[CommonStyle.choseToSearchClosedIconSize]}
                        source={require('../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                    </View>
                  ) : (
                    <View style={[CommonStyle.choseToSearchViewStyle]}>
                      <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                        {this.state.selCourseLevelId &&
                        this.state.selCourseLevelName
                          ? '职级：' + this.state.selCourseLevelName
                          : '选择职级'}
                      </Text>
                      <Image
                        style={[CommonStyle.choseToSearchOpenedIconSize]}
                        source={require('../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
              <View
                style={[
                  {
                    height: 32,
                    opacity: 0.6,
                    borderRadius: 8,
                    backgroundColor: 'rgba(242, 245, 252, 1)',
                    justifyContent: 'center',
                    alignItems: 'center',
                    paddingLeft: 10,
                    paddingRight: 10,
                    marginLeft: screenWidth / 3,
                  },
                ]}>
                <TouchableOpacity onPress={() => this.openCourseTypeSelect()}>
                  <Text style={{color: 'rgba(0,10,32,0.85)', fontSize: 14}}>
                    {!this.state.selCourseTypeId
                      ? '实习类型'
                      : this.state.selCourseTypeName}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            <View style={CommonStyle.singleSearchBox}>
              <View style={CommonStyle.searchBoxWithoutOthers}>
                <Image
                  style={{width: 16, height: 16, marginLeft: 7}}
                  source={require('../../assets/icon/iconfont/search.png')}></Image>
                <TextInput
                  style={{
                    color: 'rgba(rgba(0, 10, 32, 0.45))',
                    fontSize: 14,
                    marginLeft: 15,
                    paddingTop: 0,
                    paddingBottom: 0,
                    paddingRight: 0,
                    paddingLeft: 0,
                  }}
                  returnKeyType="search"
                  returnKeyLabel="搜索"
                  onSubmitEditing={(e) => {
                    this.searchByKeyWord();
                  }}
                  placeholder={'搜索实习名称/实习内容'}
                  onChangeText={(text) => this.setState({searchKeyWord: text})}>
                  {this.state.searchKeyWord}
                </TextInput>
              </View>
            </View>
          </View>
        </View>
        <View>
          {this.state.showSearchItemBlock ? (
            <View
              style={[
                CommonStyle.choseToSearchBigBoxViewStyle,
                {
                  height: ifIphoneXContentViewDynamicHeight(
                    this.state.topBlockLayoutHeight,
                  ),
                },
              ]}>
              <View style={CommonStyle.heightLimited}>
                <ScrollView>
                  <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                    <View
                      style={[
                        {backgroundColor: 'rgba(255,255,255,1)'},
                        CommonStyle.choseToSearchItemsViewSize,
                      ]}>
                      <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                        职级：
                      </Text>
                    </View>
                    {this.state.courseLevelDataSource &&
                    this.state.courseLevelDataSource.length > 0
                      ? this.state.courseLevelDataSource.map((item, index) => {
                          return this.renderCourseLevelRow(item);
                        })
                      : null}
                  </View>
                </ScrollView>
              </View>

              <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]}>
                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    let loadUrl = '/biz/score/list';
                    let loadRequest = {
                      currentPage: 1,
                      pageSize: this.state.pageSize,
                      userId: constants.loginUser.userId,
                      courseLevelId:
                        this.state.selCourseLevelId === 0
                          ? null
                          : this.state.selCourseLevelId,
                      courseTypeId: this.state.selCourseTypeId,
                      searchKeyWord: this.state.searchKeyWord,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    this.setState({
                      showSearchItemBlock: false,
                      // selCourseTypeId:null,
                      // selCourseTypeName:null
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>
                      确定搜索
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}

          <View
            style={[
              CommonStyle.contentViewStyle,
              {
                height: ifIphoneXContentViewDynamicHeight(
                  this.state.topBlockLayoutHeight,
                ),
              },
            ]}>
            <FlatList
              data={this.state.dataSource}
              renderItem={({item, index}) => this.renderRow(item, index)}
              ListEmptyComponent={this.emptyComponent}
              // 自定义下拉刷新
              refreshControl={
                <RefreshControl
                  tintColor="#FF0000"
                  title="loading"
                  colors={['#FF0000', '#00FF00', '#0000FF']}
                  progressBackgroundColor="#FFFF00"
                  refreshing={this.state.refreshing}
                  onRefresh={() => {
                    this._loadFreshData();
                  }}
                />
              }
              // 底部加载
              ListFooterComponent={() => this.flatListFooterComponent()}
              onEndReached={() => this._loadNextData()}
            />
          </View>
        </View>
        <BottomScrollSelect
          ref={'SelectCourseType'}
          callBackCourseTypeIdValue={this.callBackCourseTypeValue.bind(this)}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
    marginTop: 5,
  },

  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
  },
  searchInputText: {
    width: screenWidth - 100,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },
  innerViewStyle: {
    marginTop: 10,
    borderColor: '#F4F4F4',
    borderWidth: 14,
  },
  titleViewStyle: {
    flexDirection: 'row',
    marginLeft: 12,
    marginRight: 16,
    alignItems: 'center',
    marginTop: 10,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    fontSize: 14,
    color: 'rgba(0, 10, 32, 0.65)',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
  lineViewStyle: {
    // height:1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    // marginBottom: 6,
    borderBottomWidth: 1,
    borderColor: '#E8E9EC',
  },
});
