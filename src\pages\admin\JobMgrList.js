import React, {Component} from 'react';
import {
  Alert,
  Dimensions,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyJobComponent from '../../component/EmptyJobComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
const {ifIphoneXContentViewHeight} = require('../../utils/ScreenUtil');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class JobMgrList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      departmentId: null,
      editModal: false,
      newJobId: null,
      newJobName: '',
      newJobAmount: null,
      deletedJobId: null,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {departmentId} = route.params;
      if (departmentId) {
        this.setState({
          departmentId: departmentId,
        });
        this.loadJobList(departmentId);
      }
    }
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/job/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      departmentId: this.state.departmentId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/job/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      departmentId: this.state.departmentId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    if (this.state.refreshing) {
      WToast.show({data: 'loading...'});
      return;
    }
    this.setState({ refreshing: true }, () => {
        console.log('refreshing 已更新:', this.state.refreshing);
        // 在这里执行后续操作
        this.loadJobList();
    });
  };

  loadJobList = (departmentId) => {
    let url = '/biz/job/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      departmentId: departmentId ? departmentId : this.state.departmentId,
    };
    httpPost(url, loadRequest, this.loadJobListCallBack);
  };

  loadJobListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteJob = (jobId) => {
    console.log('=======delete=jobId', jobId);
    let url = '/biz/job/delete';
    let requestParams = {jobId: jobId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View
        style={{width: screenWidth, backgroundColor: 'rgba(255, 255, 255, 1)'}}>
        <View key={item.jobId} style={{marginTop: 10, marginLeft: 20}}>
          <View
            style={{
              flexDirection: 'row',
              // justifyContent: 'space-between',
              marginLeft: 6,
              marginRight: 10,
              marginBottom: 5,
              marginTop: 5,
            }}>
            <Text style={{fontWeight: '600', fontSize: 20}}>
              {item.jobName}
            </Text>
            {item.saleFlag === 'Y' ? (
              <View
                style={{
                  marginLeft: 3,
                  marginTop: 5,
                  height: 20,
                  width: 38,
                  borderRadius: 2,
                  flexDirection: 'column',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    height: 18,
                    width: 34,
                    color: '#FFFFFF',
                    textAlign: 'center',
                    fontSize: 12,
                    backgroundColor: 'rgba(230, 54, 51, 0.80)',
                    borderRadius: 2,
                  }}>
                  销售
                </Text>
              </View>
            ) : (
              <View />
            )}
            <View
              style={[
                styles.titleViewStyle,
                {
                  position: 'absolute',
                  right: 0,
                  top: 0,
                  flexDirection: 'column',
                },
              ]}>
              <TouchableOpacity
                onPress={() => {
                  this.setState({
                    editModal: true,
                    newJobId: item.jobId,
                    newJobName: item.jobName,
                    newJobAmount: item.jobAmount,
                    deletedJobId: item.jobId,
                  });
                }}>
                <View style={[{width: 35, height: 35}]}>
                  <Image
                    style={{width: 28, height: 28}}
                    source={require('../../assets/icon/iconfont/more.png')}></Image>
                </View>
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.titleViewStyle}>
            <Text style={styles.titleTextStyle}>
              职位个数：{item.jobAmount}
            </Text>
          </View>
          <View style={styles.titleViewStyle}>
            <Text style={styles.titleTextStyle}>
              职位权重：{item.powerLevel}
            </Text>
          </View>
          <View style={styles.titleViewStyle}>
            <Text style={styles.titleTextStyle}>
              更新时间：{item.gmtModified ? item.gmtModified : item.gmtCreated}
            </Text>
          </View>
          <View style={styles.lineViewStyle} />
          <View style={[CommonStyle.itemBottomBtnStyle, {flexWrap: 'wrap'}]}>
            {/* <TouchableOpacity onPress={() => {
                        this.props.navigation.navigate("JobStaffMgrList",
                            {
                                // 传递参数
                                jobId: item.jobId,
                                jobName: item.jobName,
                                jobAmount: item.jobAmount,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle, { width: 70, backgroundColor: '#FFB800', flexDirection: 'row' }
                        ]}>
                            <Image style={{ width: 18, height: 18 }} source={require('../../assets/icon/iconfont/staff.png')}></Image>
                            <Text style={[CommonStyle.itemBottomEditBtnTextStyle, { marginLeft: 3 }]}>人员</Text>
                        </View>
                    </TouchableOpacity> */}
            {/* <TouchableOpacity onPress={() => {
                        // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                        //     return;
                        // }
                        Alert.alert('确认', '您确定要删除该职位吗？', [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                    // this在这里可用，传到方法里还有问题
                                    // this.props.navigation.goBack();
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteJob(item.jobId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 75, flexDirection: "row" }]}>
                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity> */}
            {/* <TouchableOpacity onPress={() => {
                        // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                        //     return;
                        // }
                        this.props.navigation.navigate("JobMgrAdd",
                            {
                                // 传递参数
                                jobId: item.jobId,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle, { width: 75, flexDirection: "row" }]}>
                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity> */}
          </View>
        </View>
      </View>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyJobComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      //     <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      // </TouchableOpacity>
      <View style={CommonStyle.viewListLeftViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[CommonStyle.btnListLeftBtn]}>
          <Image
            style={CommonStyle.btnListLeftBtnImage}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={CommonStyle.btnListLeftBtnText}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      // <TouchableOpacity onPress={() => {
      //     this.props.navigation.navigate("JobMgrAdd",
      //         {
      //             departmentId: this.state.departmentId,
      //             // 传递回调函数
      //             refresh: this.callBackFunction
      //         })
      // }}>
      //     <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>

      // </TouchableOpacity>
      <View style={CommonStyle.viewListRightViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('JobMgrAdd', {
              departmentId: this.state.departmentId,
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Image
            style={CommonStyle.btnListRightBtnImage}
            source={require('../../assets/icon/iconfont/add.png')}></Image>
        </TouchableOpacity>
      </View>
    );
  }

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="职位管理"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={{
            backgroundColor: 'rgba(242, 245, 252, 1)',
            height: ifIphoneXContentViewHeight() + 20,
          }}>
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            // ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
        {/* 更多操作弹窗Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.editModal}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    this.props.navigation.navigate('JobStaffMgrList', {
                      // 传递参数
                      jobId: this.state.newJobId,
                      jobName: this.state.newJobName,
                      jobAmount: this.state.newJobAmount,
                      // 传递回调函数
                      refresh: this.callBackFunction,
                    });
                    this.setState({
                      editModal: false,
                    });
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 50,
                      paddingLeft: 30,
                      marginTop: 5,
                    }}>
                    {/* <Image style={{ width: 18, height: 18 }} source={require('../../assets/icon/iconfont/staff.png')}></Image> */}
                    <Text
                      style={{
                        color: 'rgba(0, 10, 32, 0.85)',
                        fontSize: 18,
                        lineHeight: 52,
                      }}>
                      人员管理
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    this.props.navigation.navigate('JobMgrAdd', {
                      // 传递参数
                      jobId: this.state.newJobId,
                      // 传递回调函数
                      refresh: this.callBackFunction,
                    });
                    this.setState({
                      editModal: false,
                    });
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 50,
                      paddingLeft: 30,
                      marginTop: 5,
                    }}>
                    <Text
                      style={{
                        color: 'rgba(0, 10, 32, 0.85)',
                        fontSize: 18,
                        lineHeight: 52,
                      }}>
                      编辑
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              <View>
                <TouchableOpacity
                  onPress={() => {
                    Alert.alert('确认', '您确定要删除该职位吗？', [
                      {
                        text: '取消',
                        onPress: () => {
                          WToast.show({data: '点击了取消'});
                          // this在这里可用，传到方法里还有问题
                          // this.props.navigation.goBack();
                        },
                      },
                      {
                        text: '确定',
                        onPress: () => {
                          WToast.show({data: '点击了确定'});
                          this.deleteJob(this.state.deletedJobId);
                        },
                      },
                    ]);
                    this.setState({
                      editModal: false,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                    ]}>
                    <Text
                      style={[
                        {
                          color: 'rgba(0, 10, 32, 0.85)',
                          fontSize: 18,
                          lineHeight: 52,
                        },
                      ]}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: 291,
                  height: 50,
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      editModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 105,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    marginTop: 10,
    marginLeft: 20,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: 'rgba(0,10,32,0.65)',
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
  lineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    borderBottomWidth: 1,
    borderColor: '#E8E9EC',
  },
});
