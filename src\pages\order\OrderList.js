import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  Linking,
  Modal,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class OrderList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      searchKeyWord: '',
      topBlockLayoutHeight: 0,
      moreModal: false,
      deleteModal: false,
      exportPdfModal: false,
      dailyItem: {},
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    this.loadOrderList();
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/order/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/order/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    if (this.state.refreshing) {
        WToast.show({data: 'loading...'});
        return;
    }
    this.setState({ refreshing: true }, () => {
        console.log('refreshing 已更新:', this.state.refreshing);
        // 在这里执行后续操作
        this.loadOrderList();
    });
  };

  loadOrderList = () => {
    let url = '/biz/order/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this.loadOrderListCallBack);
  };

  loadOrderListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteOrder = (orderId) => {
    console.log('=======delete=orderId', orderId);
    let requestUrl = '/biz/order/delete';
    let requestParams = {orderId: orderId};
    httpPost(requestUrl, requestParams, this.callBackDeleteOrder);
  };
  callBackDeleteOrder = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '成功删除'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  searchByKeyWord = () => {
    let toastOpts;
    if (!this.state.searchKeyWord) {
      toastOpts = getFailToastOpts('请输入客户、合同或订单名称');
      WToast.show(toastOpts);
      return;
    }
    let loadUrl = '/biz/order/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  renderRow = (orderItem, index) => {
    return (
      <View key={orderItem.orderId} style={[styles.innerViewStyle]}>
        {index == 0 ? (
          <View style={CommonStyle.lineListHeadRenderRowStyle}></View>
        ) : (
          <View></View>
        )}
        <View style={{position: 'absolute', right: 0, top: 0, marginRight: 15}}>
          <TouchableOpacity
            onPress={() => {
              this.setState({
                moreModal: true,
                dailyItem: orderItem,
              });
            }}>
            <View
              style={[
                {
                  width: 35,
                  height: 35,
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <Image
                style={{width: 28, height: 28}}
                source={require('../../assets/icon/iconfont/more.png')}></Image>
            </View>
          </TouchableOpacity>
        </View>
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{orderItem.orderName}</Text>
                </View> */}
        <View style={CommonStyle.titleViewStyleSpecial}>
          <Text
            style={[
              CommonStyle.titleTextStyleSpecial,
              orderItem.outsourcingTenantId
                ? {width: screenWidth - 140}
                : {width: screenWidth - 100},
            ]}>
            {orderItem.orderName}
          </Text>
          {/* <Text style={[CommonStyle.titleTextStyleSpecial, orderItem.outsourcingTenantId ? {width:screenWidth - 140} : null]}>名  称：
                        {orderItem.orderName}
                    </Text> */}
          {orderItem.outsourcingTenantId ? (
            <Text
              style={{
                paddingTop: 3,
                paddingBottom: 3,
                paddingLeft: 5,
                paddingRight: 5,
                height: 23,
                borderRadius: 12,
                backgroundColor: 'rgba(255,0,0,0.4)',
                color: '#FFFFFF',
              }}>
              {orderItem.outsourcingTenantId === constants.loginUser.tenantId
                ? '协助'
                : orderItem.outsourcingTenantName}
            </Text>
          ) : null}
        </View>
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>订单号：{orderItem.orderCode}</Text>
                </View> */}
        {/* <View style={styles.titleViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={styles.titleTextStyle}>所属合同：{orderItem.contractName}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={styles.titleTextStyle}>订单时间：{orderItem.gmtCreated}</Text>
                    </View>
                </View> */}
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>所属合同：{orderItem.contractName}</Text>
                </View> */}
        <View style={[CommonStyle.titleViewStyle]}>
          <Text style={CommonStyle.titleTextStyle}>
            客 户：{orderItem.customerName}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            公 司：
            {orderItem.enterpriseAbbreviation
              ? orderItem.enterpriseAbbreviation
              : orderItem.enterpriseName
              ? orderItem.enterpriseName
              : '暂未选择'}
          </Text>
        </View>
        {/* <View style={styles.titleViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={styles.titleTextStyle}>砖型：{orderItem.brickTypeName}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={styles.titleTextStyle}>订单金额：{orderItem.contractAmountUnit}{orderItem.contractAmont}</Text>
                    </View>
                </View> */}
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>砖型：{orderItem.seriesName}-{orderItem.brickTypeName}</Text>
                </View> */}
        {/* <View style={styles.titleViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={styles.titleTextStyle}>联系人：{orderItem.orderContact}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={styles.titleTextStyle}>联系电话：{orderItem.orderContactTel}</Text>
                    </View>
                </View> */}
        {/* <View style={styles.titleViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={styles.titleTextStyle}>数量：{orderItem.brickAmount}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={styles.titleTextStyle}>重量：{orderItem.orderWeight}</Text>
                    </View>
                </View> */}
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            数 量：{orderItem.brickAmount ? orderItem.brickAmount : '无'}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            重 量：{orderItem.orderWeight ? orderItem.orderWeight : '无'}
          </Text>
          {orderItem.display === 'Y' ? (
            <Text
              style={{
                paddingTop: 3,
                paddingBottom: 3,
                paddingLeft: 5,
                paddingRight: 5,
                marginRight: 15,
                height: 23,
                borderRadius: 12,
                backgroundColor: 'rgba(255,184,0,0.4)',
                color: '#FFFFFF',
              }}>
              显示
            </Text>
          ) : (
            <Text
              style={{
                paddingTop: 3,
                paddingBottom: 3,
                paddingLeft: 5,
                paddingRight: 5,
                marginRight: 15,
                height: 23,
                borderRadius: 12,
                backgroundColor: 'rgba(134,134,134,0.4)',
                color: '#FFFFFF',
              }}>
              不显示
            </Text>
          )}
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            金 额：{orderItem.contractAmountUnit}
            {orderItem.contractAmont}
          </Text>
        </View>
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>是否显示：{orderItem.display === 'Y' ? "显示" : "不显示"}</Text>
                </View> */}
        {/* {
                    orderItem.outsourcingTenantId ? 
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>{orderItem.outsourcingTenantId === constants.loginUser.tenantId ? ("协助单位：" + orderItem.tenantName) : ("生产单位：" + orderItem.outsourcingTenantName) }</Text>
                    </View>
                    :
                    null
                } */}
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            状 态：{orderItem.orderStateName}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            创建时间：{orderItem.gmtCreated}
          </Text>
        </View>

        <View
          style={[
            CommonStyle.itemBottomBtnStyle,
            {flexWrap: 'wrap', marginRight: 15},
          ]}>
          <TouchableOpacity
            onPress={() => {
              let netWork =
                constants.service_addr +
                '/html/order/track.html?orderCode=' +
                orderItem.orderCode;
              Clipboard.setString(netWork);
              WToast.show({
                data:
                  '访问网址:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
                  netWork,
              });
              Alert.alert(
                '确认',
                '访问网址已复制到粘贴板，使用浏览器打开:\n' + netWork + ' ',
                [
                  {
                    text: '不打开',
                    onPress: () => {
                      WToast.show({data: '点击了不打开'});
                    },
                  },
                  {
                    text: '打开',
                    onPress: () => {
                      WToast.show({data: '点击了打开'});
                      // 直接打开外网链接
                      Linking.openURL(netWork);
                    },
                  },
                ],
              );
            }}>
            <View
              style={[
                CommonStyle.itemBottomDetailBtnViewStyle,
                {width: 65, flexDirection: 'row'},
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 2}}
                source={require('../../assets/icon/iconfont/share.png')}></Image>
              <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>分享</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              this.props.navigation.navigate('OrderStateTracking', {
                orderId: orderItem.orderId,
                _orderItem: orderItem,
              });
            }}>
            <View
              style={[
                CommonStyle.itemBottomDetailBtnViewStyle,
                {backgroundColor: '#3ab240', width: 65, flexDirection: 'row'},
              ]}>
              <Image
                style={{width: 25, height: 25, marginRight: 2}}
                source={require('../../assets/icon/iconfont/detail1.png')}></Image>
              <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>进度</Text>
            </View>
          </TouchableOpacity>

          {/* {
                        // 没有外协单位或者是外协单位但不是自己的订单可以显示
                        (!orderItem.outsourcingTenantId || (orderItem.outsourcingTenantId && orderItem.outsourcingTenantId != constants.loginUser.tenantId)) 
                        ?
                            <TouchableOpacity onPress={()=>{
                                if (dateDiffHours(constants.nowDateTime, orderItem.gmtCreated) > constants.editDeleteTimeLimit) {
                                    return;
                                }
                                Alert.alert('确认','您确定要删除该订单吗？',[
                                    {
                                        text:"取消", onPress:()=>{
                                        WToast.show({data:'点击了取消'});
                                        // this在这里可用，传到方法里还有问题
                                        // this.props.navigation.goBack();
                                        }
                                    },
                                    {
                                        text:"确定", onPress:()=>{
                                            WToast.show({data:'点击了确定'});
                                            this.deleteOrder(orderItem.orderId)
                                        }
                                    }
                                ]);
                                }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:70,flexDirection:"row"}
                                ,dateDiffHours(constants.nowDateTime, orderItem.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""]}>
                                    <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除{}</Text>
                                </View>
                            </TouchableOpacity>
                        
                        :null
                    } */}
          {/*                     
                    {
                        (orderItem.outsourcingTenantId == null || (orderItem.outsourcingTenantId && orderItem.outsourcingTenantId != constants.loginUser.tenantId)) 
                        ?   
                            <TouchableOpacity onPress={()=>{
                                if (dateDiffHours(constants.nowDateTime, orderItem.gmtCreated) > constants.editDeleteTimeLimit) {
                                    return;
                                }
                                    this.props.navigation.navigate("OrderAdd", 
                                    {
                                        orderId:orderItem.orderId,
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                                <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,flexDirection:"row"}
                                ,dateDiffHours(constants.nowDateTime, orderItem.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                                ]}>
                                    <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                    <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                </View>
                            </TouchableOpacity>
                        
                        :null
                    } */}
        </View>
      </View>
    );
  };
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      // // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      // </TouchableOpacity> */}
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[{flexDirection: 'row', alignItems: 'center'}]}>
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={{color: '#3C6CDE', fontWeight: 'bold'}}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      // <TouchableOpacity onPress={() => {
      //     this.props.navigation.navigate("OrderAdd",
      //     {
      //         // 传递回调函数
      //         refresh: this.callBackFunction
      //     })
      // }}>
      //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
      // </TouchableOpacity>
      <View
        style={{flexDirection: 'row-reverse', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('OrderAdd', {
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Image
            style={{width: 22, height: 22, marginVertical: 2}}
            source={require('../../assets/icon/iconfont/add.png')}></Image>
        </TouchableOpacity>
      </View>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="订单管理"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.headViewStyle,
            {borderLeftWidth: 0, borderRightWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View style={CommonStyle.singleSearchBox}>
            <View style={CommonStyle.searchBoxWithoutOthers}>
              {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
              <Image
                style={{width: 16, height: 16, marginLeft: 7}}
                source={require('../../assets/icon/iconfont/search.png')}></Image>
              <TextInput
                style={{
                  color: 'rgba(rgba(0, 10, 32, 0.45))',
                  fontSize: 14,
                  marginLeft: 5,
                  paddingTop: 0,
                  paddingBottom: 0,
                  paddingRight: 0,
                  paddingLeft: 0,
                  width: '100%',
                }}
                returnKeyType="search"
                returnKeyLabel="搜索"
                onSubmitEditing={(e) => {
                  this.searchByKeyWord();
                }}
                placeholder={'客户/合同/订单'}
                onChangeText={(text) => this.setState({searchKeyWord: text})}>
                {this.state.searchKeyWord}
              </TextInput>
            </View>
          </View>
        </View>
        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> */}
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.orderId}
            ListEmptyComponent={this.emptyComponent}
            ItemSeparatorComponent={this.space}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
          {/* </ScrollView> */}
        </View>
        {/* 更多操作弹窗Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.moreModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View>
                {this.state.dailyItem.outsourcingTenantId == null ||
                (this.state.dailyItem.outsourcingTenantId &&
                  this.state.dailyItem.outsourcingTenantId !=
                    constants.loginUser.tenantId) ? (
                  <TouchableOpacity
                    onPress={() => {
                      // if (this.state.dailyItem.dailyState != "0BB" || this.state.dailyItem.auditScore || dateDiffHours(this.state.currentTime, this.state.dailyItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                      //     WToast.show({ data: '该日报不可编辑' });
                      //     return;
                      // }
                      if (
                        dateDiffHours(
                          constants.nowDateTime,
                          this.state.dailyItem.gmtCreated,
                        ) > constants.editDeleteTimeLimit
                      ) {
                        return;
                      }
                      this.setState({
                        moreModal: false,
                      });
                      this.props.navigation.navigate('OrderAdd', {
                        // 传递参数
                        orderId: this.state.dailyItem.orderId,
                        // 传递回调函数
                        refresh: this.callBackFunction,
                      });
                    }}>
                    <View
                      style={[
                        {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                        dateDiffHours(
                          constants.nowDateTime,
                          this.state.dailyItem.gmtCreated,
                        ) > constants.editDeleteTimeLimit
                          ? CommonStyle.disableViewStyle
                          : '',
                      ]}>
                      {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                      <Text
                        style={{
                          color: 'rgba(0, 10, 32, 0.85)',
                          fontSize: 18,
                          lineHeight: 52,
                        }}>
                        编辑
                      </Text>
                    </View>
                  </TouchableOpacity>
                ) : null}
              </View>

              <View>
                {
                  // 没有外协单位或者是外协单位但不是自己的订单可以显示
                  !this.state.dailyItem.outsourcingTenantId ||
                  (this.state.dailyItem.outsourcingTenantId &&
                    this.state.dailyItem.outsourcingTenantId !=
                      constants.loginUser.tenantId) ? (
                    <TouchableOpacity
                      onPress={() => {
                        if (
                          dateDiffHours(
                            constants.nowDateTime,
                            this.state.dailyItem.gmtCreated,
                          ) > constants.editDeleteTimeLimit
                        ) {
                          return;
                        }
                        console.log(
                          'dailyItem=================',
                          this.state.dailyItem,
                        );

                        // if (this.state.dailyItem.dailyState != "0BB" && this.state.dailyItem.auditScore) {
                        //     WToast.show({ data: '日报已审核不可删除' });
                        //     return;
                        // }
                        // if (this.state.dailyItem.dailyState != "0BB" && dateDiffHours(this.state.currentTime, this.state.dailyItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                        //     WToast.show({ data: '日报已超出删除时限' });
                        //     return;
                        // }
                        // 删除弹窗Modal
                        this.setState({
                          moreModal: false,
                          deleteModal: true,
                        });
                      }}>
                      <View
                        style={[
                          {
                            width: 145,
                            height: 50,
                            paddingLeft: 30,
                            marginTop: 5,
                          },
                          dateDiffHours(
                            constants.nowDateTime,
                            this.state.dailyItem.gmtCreated,
                          ) > constants.editDeleteTimeLimit
                            ? CommonStyle.disableViewStyle
                            : '',
                        ]}>
                        {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                        <Text
                          style={[
                            {
                              color: 'rgba(0, 10, 32, 0.85)',
                              fontSize: 18,
                              lineHeight: 52,
                            },
                          ]}>
                          删除
                        </Text>
                      </View>
                    </TouchableOpacity>
                  ) : null
                }
              </View>
              <View
                style={{
                  width: 291,
                  height: 50,
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      moreModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 105,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 删除弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.deleteModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 292,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认删除该日报?</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  删除后数据不可恢复，请谨慎操作
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 292,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了确定'});
                    this.deleteOrder(this.state.dailyItem.orderId);
                  }}>
                  <View
                    style={[
                      {
                        width: 146,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
  },

  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
    paddingLeft: 0,
  },
  searchInputText: {
    width: screenWidth - 100,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    marginTop: 4,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },
  innerViewStyle: {
    // marginLeft:15,
    marginTop: 10,
    // borderColor:"#F4F4F4",
    // borderWidth:8,
  },
  innerViewStyleSearch: {
    // marginTop:10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginLeft:10,
    marginRight: 10,
    marginBottom: 5,
    // marginTop:5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
});
