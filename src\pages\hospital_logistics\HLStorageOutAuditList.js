import React,{Component} from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, TextInput, Clipboard, Linking,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class HLStorageOutAuditList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            storageOutStateSource:[],
            selStorageOutApplyStateCode:"all",
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        let storageOutStateSource = [
            {
                stateCode:'all',
                stateName:'全部',
            },
            {
                stateCode:'1',
                stateName:'待审核',
            },
            {
                stateCode:'2',
                stateName:'已审核',
            },
        ]
        this.setState({
            storageOutStateSource:storageOutStateSource,
        })


        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
            this.loadStorageOutAuditList();
        }
    }

    callBackFunction=()=>{
        let url= "/biz/audit/node/record/storageOutAuditList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditUserId":constants.loginUser.userId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/audit/node/record/storageOutAuditList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditUserId":constants.loginUser.userId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }


    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadStorageOutAuditList();
    }

    loadStorageOutAuditList=()=>{
        let url= "/biz/audit/node/record/storageOutAuditList";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "auditUserId":constants.loginUser.userId,
        };
        httpPost(url, loadRequest, this.loadStorageOutAuditListCallBack);
    }


    loadStorageOutAuditListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            console.log(dataNew)
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    storageOutApplyStateRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    let stateCode=item.stateCode
                    this.setState({
                        selStorageOutApplyStateCode:stateCode,
                    })
                    console.log("======selStorageOutApplyStateCode==", this.state.selStorageOutApplyStateCode,",",item.stateName,",",item.stateCode)
                    let url= "/biz/audit/node/record/storageOutAuditList";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "excludeAuditResult":item.stateCode === '2' ? "I" : null,
                        "auditResult":item.stateCode === '1' ? "I" : null,
                        "auditUserId":constants.loginUser.userId,
                    };
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View 
                        key={item.stateCode} 
                        style={[item.stateCode===this.state.selStorageOutApplyStateCode? [CommonStyle.selectedBlockItemViewStyle,{borderBottomWidth:2,borderBottomColor:"#CB4139"}] : [CommonStyle.blockItemViewStyle,{}],{paddingLeft:8,paddingRight:8}]}
                        >
                        <Text style={[item.stateCode===this.state.selStorageOutApplyStateCode? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    } 

    renderRow=(item, index)=>{
        return (
            <View>
            {
                item.storageOutDTOList && item.storageOutDTOList.length > 0?
                <View key={item.recordId} style={styles.innerViewStyle}>

                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>出库科室：{item.storageOutDTOList[0].departmentName}</Text>
                            {
                            // item.storageOutDTOList[0].storageOutFlag==="M" ? 
                            <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,184,0,0.6)', color:'#FFFFFF'}}>
                                物资出库
                            </Text>
                            // :
                            // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                            //     科室出库
                            // </Text>
                            }
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>出库类型：{item.storageOutDTOList[0].storageOutTypeName}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>出库日期：{item.storageOutDTOList[0].storageOutDate}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>单据备注：{item.storageOutDTOList[0].receiptRemark ? item.storageOutDTOList[0].receiptRemark : "暂无信息"}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>经办人：{item.storageOutDTOList[0].operator}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>运送人：{item.storageOutDTOList[0].carrier}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>领用人：{item.storageOutDTOList[0].recipient}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>记账人：{item.storageOutDTOList[0].bookKeeper?item.storageOutDTOList[0].bookKeeper : "暂无"}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>记账日期：{item.storageOutDTOList[0].bookKeepingDate}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>提交时间：{item.storageOutDTOList[0].gmtModified ? item.storageOutDTOList[0].gmtModified : item.storageOutDTOList[0].gmtCreated}</Text>
                        </View>
                                
                        <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                        <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("HLStorageOutListDetail", 
                                {
                                    // 传递参数
                                    storageOutId:item.storageOutDTOList[0].storageOutId,
                                    // 传递回调函数
                                    refresh: this.callBackFunction 
                                })
                            }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {backgroundColor:"#3ab240",marginLeft:0 ,width: 70 ,flexDirection:"row"}]}>
                            <Image  style={{width:25, height:25,marginRight:1}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>明细</Text>
                            </View>
                        </TouchableOpacity>
                            {
                                item.auditResult === "I" ?
                                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                    <TouchableOpacity onPress={()=>{
                                            this.props.navigation.navigate("HLStorageOutAudit", 
                                            {
                                                // 传递参数
                                                recordId: item.recordId,
                                                applyAuditId:item.storageOutDTOList[0].storageOutId,
                                                // 传递回调函数
                                                refresh: this.callBackFunction 
                                            })
                                        }}>
                                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:75,flexDirection:"row"}
                                        ]}>
                                            <Image  style={{width:18, height:18,marginRight:5}} source={require('../../assets/icon/iconfont/examine.png')}></Image>
                                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>审核</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={()=>{
                                        this.props.navigation.navigate("HLStorageOutAuditDetail", 
                                        {
                                            // 传递参数
                                            auditItemId: item.auditItemId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                        }}>
                                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 75 ,flexDirection:"row"}]}>
                                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View> 
                                
                            :

                            <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("HLStorageOutAuditDetail", 
                                {
                                    // 传递参数
                                    auditItemId: item.auditItemId,
                                    // 传递回调函数
                                    refresh: this.callBackFunction 
                                })
                                }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 75 ,flexDirection:"row"}]}>
                                    <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                </View>
                            </TouchableOpacity>
                            }
                            
                            
                        </View>
                        </View>
                        :
                    <View />
                    }

                </View>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }
    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='出库审核'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />


                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.storageOutStateSource && this.state.storageOutStateSource.length > 0)
                                ?
                                this.state.storageOutStateSource.map((item, index) => {
                                    return this.storageOutApplyStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                </View>

                <View style={[CommonStyle.contentViewStyle,{ height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>

            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});