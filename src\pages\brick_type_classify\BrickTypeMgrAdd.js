import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class BrickTypeMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            brickTypeId:"",
            brickTypeName:"",
            seriesId:"",
            standardSize:"",
            standardWeight:"",
            brickTypeSort:0,
            statisticalFlag:'N',
            operate:"",
            longTermProduction:"N"
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { brickTypeId,seriesId } = route.params;
            if (brickTypeId) {
                console.log("========Edit==brickTypeId:", brickTypeId);
                this.setState({
                    brickTypeId:brickTypeId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/brick/series/type/get";
                loadRequest={'brickTypeId':brickTypeId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditBrickTypeDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
            if (seriesId) {
                this.setState({
                    seriesId:seriesId,
                })
            }
        }
    }
    loadEditBrickTypeDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                brickTypeId:response.data.brickTypeId,
                seriesId:response.data.seriesId,
                brickTypeName:response.data.brickTypeName,
                standardSize:response.data.standardSize,
                standardWeight:response.data.standardWeight,
                brickTypeSort:response.data.brickTypeSort,
                statisticalFlag:response.data.statisticalFlag,
                longTermProduction:response.data.longTermProduction
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("BrickTypeMgrList")
            }}>
                <Text style={CommonStyle.headRightText}>型号管理</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveBrickType =()=> {
        console.log("=======saveBrickType");
        let toastOpts;
        if (!this.state.brickTypeName) {
            toastOpts = getFailToastOpts("请输入砖型名称");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/brick/series/type/add";
        if (this.state.brickTypeId) {
            console.log("=========Edit===brickTypeId", this.state.brickTypeId, "==seriesId:", this.state.seriesId)
            url= "/biz/brick/series/type/modify";
        }
        let requestParams={
            "brickTypeId":this.state.brickTypeId,
            "brickTypeName":this.state.brickTypeName,
            "seriesId":this.state.seriesId,
            "standardSize":this.state.standardSize,
            "standardWeight":this.state.standardWeight,
            "brickTypeSort":this.state.brickTypeSort,
            "statisticalFlag":this.state.statisticalFlag,
            "longTermProduction":this.state.longTermProduction
        };
        httpPost(url, requestParams, this.saveBrickTypeCallBack);
    }
    
    // 保存回调函数
    saveBrickTypeCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '型号'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>产品型号</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入产品型号'}
                            onChangeText={(text) => this.setState({brickTypeName:text})}
                        >
                            {this.state.brickTypeName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>尺寸</Text>
                            <Text style={styles.leftLabRedTextStyle}></Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入尺寸'}
                            onChangeText={(text) => this.setState({standardSize:text})}
                        >
                            {this.state.standardSize}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>单重(KG)</Text>
                            <Text style={styles.leftLabRedTextStyle}></Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入单重'}
                            onChangeText={(text) => this.setState({standardWeight:text})}
                        >
                            {this.state.standardWeight}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}></Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({brickTypeSort:text})}
                        >
                            {this.state.brickTypeSort}
                        </TextInput>
                    </View>
                    {/* <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>是否统计</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[styles.selectViewItem, (this.state.statisticalFlag === 'Y') ? { backgroundColor: 'red' } : ""]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    statisticalFlag: "Y",
                                })
                            }}>
                                <View style={styles.selectViewItem}>
                                    <Text style={[styles.selectTextItem, (this.state.statisticalFlag === 'Y') ? { color: '#FFF' } : { color: '#000' }]}>统计</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={[styles.selectViewItem, (this.state.statisticalFlag === 'N') ? { backgroundColor: 'red' } : {}]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    statisticalFlag: "N",
                                })
                            }}>
                                <View style={styles.selectViewItem}>
                                    <Text style={[styles.selectTextItem, (this.state.statisticalFlag === 'N') ? { color: '#FFF' } : { color: '#000' }]}>不统计</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View> */}
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>是否长期生产</Text>
                        </View>
                        <View style={[CommonStyle.selectViewItem, (this.state.longTermProduction === 'Y') ? { backgroundColor: 'red' } : ""]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    longTermProduction: "Y",
                                })
                            }}>
                                <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.longTermProduction === 'Y') ? { color: '#FFF' } : { color: '#000' }]}>是</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={[CommonStyle.selectViewItem, (this.state.longTermProduction === 'N') ? { backgroundColor: 'red' } : {}]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    longTermProduction: "N",
                                })
                            }}>
                                <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.longTermProduction === 'N') ? { color: '#FFF' } : { color: '#000' }]}>否</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveBrickType.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },

    selectViewItem: {
        width: 100, justifyContent: 'center', alignItems: 'center'
    },
    selectTextItem: {
        fontSize: 18,
        fontWeight: 'bold'
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})