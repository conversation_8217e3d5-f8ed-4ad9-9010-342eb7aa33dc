import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,
    FlatList,RefreshControl,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class BrickTypeMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            seriesId:"",
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:10,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            searchKeyWord:""
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { seriesId } = route.params;
            if (seriesId) {
                console.log("========BrickType==seriesId:", seriesId);
                this.setState({
                    seriesId:seriesId
                })
                this.loadBrickTypeList(seriesId);
            }
        }
    }

    // 回调函数
    callBackFunction=(seriesId)=>{
        let url= "/biz/brick/series/type/list";
        let loadRequest={
            "seriesId":seriesId ? seriesId : this.state.seriesId,
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "qryAllInfo":"Y"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=(seriesId)=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/brick/series/type/list";
        let loadRequest={
            "seriesId":seriesId ? seriesId : this.state.seriesId,
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "qryAllInfo":"Y"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadBrickTypeList();
    }

    loadBrickTypeList=(seriesId)=>{
        let url= "/biz/brick/series/type/list";
        let loadRequest={
            "seriesId":seriesId ? seriesId : this.state.seriesId,
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "qryAllInfo":"Y"
        };
        httpPost(url, loadRequest, this.loadBrickTypeListCallBack);
    }

    loadBrickTypeListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    updateBrickTypeState =(brickTypeId, BrickTypeState)=> {
        console.log("=======delete=brickTypeId", brickTypeId);
        let url= "/biz/brick/series/type/modify";
        let requestParams={'brickTypeId':brickTypeId,"BrickTypeState":BrickTypeState};
        httpPost(url, requestParams, this.updateBrickTypeStateCallBack);
    }

    // 更新状态的回调操作
    updateBrickTypeStateCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"操作成功"});
            this._loadFreshData();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    deleteBrickType =(brickTypeId)=> {
        console.log("=======delete=brickTypeId", brickTypeId);
        let url= "/biz/brick/series/type/delete";
        let requestParams={'brickTypeId':brickTypeId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    displayBrickType=(item)=>{
        console.log("=======displayBrickType=brickTypeId", item.brickTypeId);
        let requestUrl = "/biz/brick/series/type/modify";
        let requestParams = {
            'brickTypeId': item.brickTypeId,
            'display': item.display === 'Y' ? 'N' : 'Y'
        };
        httpPost(requestUrl, requestParams, (response) => {
            if (response.code == 200) {
                // 更新页面上订单状态
                item.display = (item.display === 'Y' ? 'N' : 'Y');
                WToast.show({ data: (item.display === 'Y' ? '显示' : '隐藏') + "设置完成" });
                let brickTypeDataSource = this.state.dataSource;
                // JS 数组遍历
                brickTypeDataSource.forEach((obj) => {
                    if (obj.brickTypeId === item.brickTypeId) {
                        obj.display = item.display;
                    }
                })
                this.setState({
                    dataSource: brickTypeDataSource,
                })
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.brickTypeId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,{width:screenWidth - 110}]}>产品型号：{item.brickTypeName}</Text>
                    {
                        item.display === 'Y' ? 
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,
                            height:23, borderRadius:12, backgroundColor:'rgba(255,184,0,0.4)', color:'#FFFFFF'}}>
                            显示</Text>
                            :
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,
                            height:23, borderRadius:12, backgroundColor:'rgba(134,134,134,0.4)', color:'#FFFFFF'}}>
                            不显示</Text>
                    }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>尺寸：{item.standardSize ? item.standardSize : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>单重(KG)：{item.standardWeight ? item.standardWeight : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>是否长期生产：{item.longTermProduction === 'Y' ? '是' : '否'}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>排序：{item.brickTypeSort}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>最近更新时间：{item.gmtModified ? item.gmtModified: item.gmtCreated }</Text>
                </View>
                 */}
                <View style={CommonStyle.itemBottomBtnStyle}>
                    <TouchableOpacity onPress={() => {
                        let message = '您确定要' + (item.display === 'Y' ? '隐藏' : '显示') + '该型号吗？';
                        Alert.alert('确认', message, [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.displayBrickType(item)
                                }
                            }
                        ]);
                    }}>
                        <View style={[item.display === 'Y' ? CommonStyle.itemBottomDeleteBtnViewStyle : [CommonStyle.itemBottomDetailBtnViewStyle, { backgroundColor: "#FFB800" }] 
                            , { width: 70, flexDirection: "row", marginLeft: 0 }
                        ]}>
                            {
                                item.display === 'Y' ?
                                <Image style={{ width: 25, height: 30, marginRight: 2 }} source={require('../../assets/icon/iconfont/hide.png')}></Image>
                                :
                                    <Image style={{ width: 25, height: 30, marginRight: 2 }} source={require('../../assets/icon/iconfont/show.png')}></Image>
                            }
                            <Text style={item.display === 'Y' ? CommonStyle.itemBottomDeleteBtnTextStyle :CommonStyle.itemBottomDetailBtnTextStyle}>
                                {item.display === 'Y' ? '隐藏' : '显示'}
                            </Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                        //     return;
                        // }
                        Alert.alert('确认','您确定要删除该砖型吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteBrickType(item.brickTypeId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:70,flexDirection:"row",marginLeft:0}]}>
                            <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                            //     return;
                            // }
                            this.props.navigation.navigate("BrickTypeMgrAdd", 
                            {
                                // 传递参数
                                brickTypeId:item.brickTypeId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,flexDirection:"row",marginLeft:0}]}>
                        <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("BrickTypeMgrAdd", 
                {
                    // 传递参数
                    seriesId:this.state.seriesId,
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    searchByKeyWord = () => {
        let url= "/biz/brick/series/type/list";
        let loadRequest={
            "seriesId":this.state.seriesId,
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "qryAllInfo":"Y"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='型号管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{}}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                                <Image  style={{width:30, height:30,marginBottom:5}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'产品型号'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>

                </View>
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});