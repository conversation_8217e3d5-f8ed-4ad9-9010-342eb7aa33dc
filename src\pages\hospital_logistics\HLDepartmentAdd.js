import React,{Component} from 'react';
import {
    Alert,Modal,
    View, 
    ScrollView, 
    Text, 
    TextInput, 
    StyleSheet, 
    FlatList ,
    TouchableOpacity,
    Dimensions,
    Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

const leftLabWidth = 130;
export default class HLDepartmentAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            operate:"",

            departmentId:"",
            departmentName:"",

            hospitalId:"",
            hospitalDataSource:[],
            selectHospital:[],
            hospitalName:"",

            departmentLocation:"",
            departmentRatedBed:"",
            departmentExtraBed:"",
            departmentCode:"",

            logisticsFlag:"",
            logisticsFlagDataSource:[],
            departmentSort:0,
            tenantId:""
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadHospitalList()
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { departmentId, hospitalId, hospitalName} = route.params;
            if (departmentId) {
                console.log("========Edit==departmentId:", departmentId);
                this.setState({
                    departmentId:departmentId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/hl/department/get";
                loadRequest={'departmentId':departmentId};
                httpPost(loadTypeUrl, loadRequest, this._loadDepartmentCallBack);
            }
            else{
                this.setState({
                    operate:"新增",
                    //当出于新增页面时设置默认值
                    logisticsFlag:"Y"
                })
            }

            if (hospitalId) {
                console.log("========hospitalId:", hospitalId);
                this.setState({
                    hospitalId:hospitalId,
                })
            }
            if (hospitalName) {
                console.log("========hospitalName:", hospitalName);
                this.setState({
                    hospitalName:hospitalName,
                    selectHospital:[hospitalName]
                })
            }
            
        }
        let logisticsFlagDataSource = [
            {
                stateCode:"Y",
                stateName:'是',
            },
            {
                stateCode:"N",
                stateName:'否',
            },
        ]
        this.setState({
            logisticsFlagDataSource:logisticsFlagDataSource
        })
        
    }
    _loadDepartmentCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            console.log(response.data);
            this.setState({
                departmentId:response.data.departmentId,
                departmentName:response.data.departmentName,
                hospitalId:response.data.hospitalId,
                hospitalName:response.data.hospitalName,
                selectHospital:[response.data.hospitalName],
                departmentLocation:response.data.departmentLocation,
                departmentRatedBed:response.data.departmentRatedBed,
                departmentExtraBed:response.data.departmentExtraBed,
                departmentCode:response.data.departmentCode,
                logisticsFlag:response.data.logisticsFlag,
                departmentSort:response.data.departmentSort
            })
        }
    }
    loadHospitalList=()=>{
        let url= "/biz/hl/hospital/list";
        let data={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, data, this.callBackLoadHospitalList);
    }
    callBackLoadHospitalList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log("======load==hospital==", response.data.dataList);
            this.setState({
                hospitalDataSource:response.data.dataList,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        //当出于新增页面时设置院区默认值
        if(this.state.operate=="新增")
        {
            this.setState({
                hospitalId:response.data.dataList[0].hospitalId
            })
        }
    }
    saveDepartment=()=>{
        console.log("=======save====");
        let toastOpts;
        if (!this.state.departmentName) {
            toastOpts = getFailToastOpts("请输入科室名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.hospitalId) {
            toastOpts = getFailToastOpts("请选择院区");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.departmentLocation) {
            toastOpts = getFailToastOpts("请输入科室位置");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.departmentRatedBed) {
            toastOpts = getFailToastOpts("请输入额定床位");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.departmentExtraBed) {
            toastOpts = getFailToastOpts("请输入额外床位");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.departmentCode) {
        //     toastOpts = getFailToastOpts("请输入外系统关联标识");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.departmentSort) {
        //     toastOpts = getFailToastOpts("请输入排序");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.logisticsFlag) {
            toastOpts = getFailToastOpts("请选择是否为后勤科室");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/hl/department/add";
        if (this.state.departmentId) {
            console.log("=========Edit===departmentId", this.state.departmentId)
            url= "/biz/hl/department/modify";
        }
        let requestParams={
            "departmentId":this.state.departmentId,
            "departmentName":this.state.departmentName,
            "hospitalId":this.state.hospitalId,
            "hospitalName":this.state.hospitalName,
            "departmentLocation":this.state.departmentLocation,
            "departmentRatedBed":this.state.departmentRatedBed,
            "departmentExtraBed":this.state.departmentExtraBed,
            "departmentCode":this.state.departmentCode,
            "logisticsFlag":this.state.logisticsFlag,
            "departmentSort":this.state.departmentSort,
        };
        httpPost(url, requestParams, this._saveDepartmenCallBack);
    }
    _saveDepartmenCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    LogisticsFlagStateRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    let stateCode=item.stateCode;
                    this.setState({
                        logisticsFlag:stateCode
                    })
                }}>
                    <View 
                        key={item.stateCode} 
                        style={[item.stateCode===this.state.logisticsFlag? [CommonStyle.selectedBlockItemViewStyle,{borderBottomWidth:2,borderBottomColor:"#CB4139"}] : [CommonStyle.blockItemViewStyle,{}],{paddingLeft:8,paddingRight:8}]}
                        >
                        <Text style={[item.stateCode===this.state.logisticsFlag? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    } 
    callBackHospitalValue(value){
        if (!value) {
            return;
        }
        this.setState({
            selectHospital:value
        })
        let hospitalDataSource=this.state.hospitalDataSource;
        for(let index=0;index<hospitalDataSource.length;index++)
        {
            if(this.state.selectHospital==hospitalDataSource[index].hospitalName)
            this.setState({
                hospitalId:hospitalDataSource[index].hospitalId,
                hospitalName:hospitalDataSource[index].hospitalName
            })
        }
        console.log("==========院区选择结果：", value,this.state.hospitalId)
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}></Text>
            </TouchableOpacity>
        )
    }
    openHospitalSelect=()=>{
        if (!this.state.hospitalDataSource || this.state.hospitalDataSource.length < 1) {
            WToast.show({data:"没有创建院区，请添加对应的院区"});
            return
        }
        console.log("==========院区数据源：", this.state.hospitalDataSource);
        this.refs.SelectHospital.showHospital(this.state.selectHospital, this.state.hospitalDataSource)
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate +'科室'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <ScrollView style={CommonStyle.contentViewStyle}> 

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                科室名称
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                style={styles.inputRightText}
                                placeholder={'请输入科室名称'}
                                onChangeText={(text) => this.setState({departmentName:text})}
                            >
                                {this.state.departmentName}
                            </TextInput>
                        </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>所属院区</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TouchableOpacity onPress={()=>this.openHospitalSelect()}>
                                <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 30)}]}>
                                    <Text style={{color:'#A0A0A0', fontSize:15}}>
                                        {!this.state.hospitalName ? "请选择所属院区" : this.state.hospitalName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                    科室位置
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                style={styles.inputRightText}
                                placeholder={'请输入科室位置'}
                                onChangeText={(text) => this.setState({departmentLocation:text})}
                            >
                                {this.state.departmentLocation}
                            </TextInput>
                        </View>   

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                    额定床位
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入额定床位'}
                                onChangeText={(text) => this.setState({departmentRatedBed:text})}
                            >
                                {this.state.departmentRatedBed}
                            </TextInput>
                        </View>    

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                    额外床位
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入额外床位'}
                                onChangeText={(text) => this.setState({departmentExtraBed:text})}
                            >
                                {this.state.departmentExtraBed}
                            </TextInput>
                        </View>
                                        
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                关联标识
                                </Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                            <TextInput 
                                style={styles.inputRightText}
                                placeholder={'请输入外系统关联标识'}
                                onChangeText={(text) => this.setState({departmentCode:text})}
                            >
                                {this.state.departmentCode}
                            </TextInput>
                        </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                后勤科室
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                            {
                            (this.state.logisticsFlagDataSource && this.state.logisticsFlagDataSource.length > 0)
                                ?
                                this.state.logisticsFlagDataSource.map((item, index) => {
                                    return this.LogisticsFlagStateRow(item)
                                })
                                : <View />
                            }
                            </View>
                        </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                排序
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入排序'}
                                onChangeText={(text) => this.setState({departmentSort:text})}
                            >
                                {this.state.departmentSort}
                            </TextInput>
                        </View>
                        <View style={CommonStyle.btnRowStyle}>
                            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                                <View style={[CommonStyle.btnRowLeftCancelBtnView]} >
                                <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                    <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={this.saveDepartment.bind(this)}>
                                <View style={[CommonStyle.btnRowRightSaveBtnView]}>
                                <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                    <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </ScrollView>
                    <BottomScrollSelect 
                        ref={'SelectHospital'} 
                        callBackHospitalValue={this.callBackHospitalValue.bind(this)}
                    />  
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
         alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        alignItems:'center'
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
});