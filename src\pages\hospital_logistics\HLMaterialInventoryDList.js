import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,
    FlatList,RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class HLMaterialInventoryDList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            selMaterialId:"",
            departmentId:null,
            departmentName:"",
            topBlockLayoutHeight:0,
            showSearchItemBlock:false,
            selDepartmentId:"",
            selDepartmentName:"",
            departmentDataSource:[]
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadDepartmentList();
        this.loadKeepRel();

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }

            this.loadMaterialListing();
        }
    }

    loadKeepRel=()=>{
        let url= "/biz/hl/department/store/keeper/rel/keepRel";
        let loadRequest={
            "userId":constants.loginUser.userId,
        };
        httpPost(url, loadRequest, this.loadKeepRelCallBack);
    }

    loadKeepRelCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                departmentId:response.data.departmentId,
            })
            this.loadDepartmentName(response.data.departmentId)
        }
    }

    loadDepartmentName=(departmentId)=>{
        let loadTypeUrl= "/biz/hl/department/get";
        let loadRequest={'departmentId':departmentId};
        httpPost(loadTypeUrl, loadRequest, this._loadDepartmentCallBack);
    }

    _loadDepartmentCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            console.log(response.data);
            this.setState({
                departmentName:response.data.departmentName,
            })
        }
    }

    loadDepartmentList=()=>{
        let url= "/biz/hl/department/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    departmentDataSource: response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    loadMaterialListing=()=>{
        let loadUrl= "/biz/hl/material/listing/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
        };
        httpPost(loadUrl, loadRequest, this.loadMaterialListingCallBack);
    }

    loadMaterialListingCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadMaterialListing();
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let loadUrl= "/biz/hl/material/listing/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 部门
    renderDepartmentRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                departmentId:item.departmentId,
                departmentName:item.departmentName,
                selMaterialId:""
            }) }}>
                <View key={"department_" + item.departmentId} style={[item.departmentId===this.state.departmentId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle, {padding:10, margin:5, }] }>
                    <Text style={[item.departmentId===this.state.departmentId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                        {item.departmentName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    renderRow=(item, index)=>{
        return (
            <TouchableOpacity onPress={()=>{
                if(!this.state.departmentId){
                    WToast.show({data:"请先选择科室"});
                    return
                }
                this.setState({
                    selMaterialId:item.materialId
                })
                this.props.navigation.navigate("HlMaterialInventoryDetailList", 
                {
                    // 传递参数
                    materialId:item.materialId,
                    materialName:item.materialName,
                    unitName:item.unitName,
                    materialPrice:item.materialPrice,
                    materialModal:item.materialModal,
                    storageOutFlag:"D",
                    departmentId:this.state.departmentId,
                    departmentName:this.state.departmentName,
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <View key={item.materialId} style={[styles.innerViewStyle,
                this.state.selMaterialId == item.materialId ? { backgroundColor:'rgba(255,0,0,0.4)',borderRadius:20,hight:80} : {}]}>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>编号：{index + 1}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>物资名称：{item.materialName}</Text>
                    </View>
                    <View style={{width: 40, height: 40,marginBottom:10,
                        backgroundColor: 'rgba(255,0,0,0.0)', 
                        position:'absolute', 
                        alignItems:'center',
                        justifyContent:'center',
                        right: 20,bottom:0
                        }}>
                        <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/enter4.png')}></Image>
                    </View>
                </View>
            </TouchableOpacity>
        )
    }

    // 显示搜索项目
    showSearchItemSelect(){
        if (!this.state.departmentDataSource || this.state.departmentDataSource.length < 1) {
            WToast.show({data:"请先添加科室"});
            return
        }
        this.setState({
            showSearchItemBlock:true,
        })
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='科室库存'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                {
                this.state.showSearchItemBlock ?
                <View style={{
                    position: 'absolute',
                    backgroundColor:'rgba(169,169,169,0.95)',
                    width:screenWidth,
                    zIndex:101,
                    padding:10,
                    right: 0,
                    left:0,
                    top: 50,
                    }}>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)'}]}>
                            <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>科室：</Text>
                        </View>
                        {
                            (this.state.departmentDataSource && this.state.departmentDataSource.length > 0) 
                            ? 
                            this.state.departmentDataSource.map((item, index)=>{
                                return this.renderDepartmentRow(item)
                            })
                            : null 
                        }
                    </View>
                    <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                        <TouchableOpacity onPress={() => { this.setState({
                            showSearchItemBlock:false,
                        }) }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => { 
                            this.setState({
                                showSearchItemBlock:false,
                            }) 
                        }}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>搜索</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
                :
                null
                }
                <View style={[styles.innerViewStyle,{marginTop:0, index:1000,}]} onLayout={this.topBlockLayout.bind(this)}>

                        <View style={{width:'100%',flexWrap:'wrap', flexDirection:'row'}}>
                            <TouchableOpacity onPress={()=>this.showSearchItemSelect()}>
                                <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}]}>
                                    <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                                        {this.state.departmentId && this.state.departmentName ? (this.state.departmentName) : "选择科室"}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                            
                        </View>

                    
                    
                </View>
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});