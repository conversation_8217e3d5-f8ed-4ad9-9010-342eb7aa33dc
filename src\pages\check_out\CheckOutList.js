import React,{Component} from 'react';
import {View, Text, StyleSheet, Image, FlatList,Dimensions, ScrollView, TouchableOpacity} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
// import TopScreen from '../../component/TopScreen';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';

var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
class CheckOutList extends Component{
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[]
        }
        console.log("============CheckOutList=props=", props)
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadCheckOutList();
    }

    loadCheckOutList=()=>{
        let url= "/biz/check/out/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 200
        };
        httpPost(url, loadRequest, this.callBackloadCheckOutList);
    }

    callBackloadCheckOutList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                dataSource:response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // componentDidMount(){
    //     if (checkOutJSONData.code == 200 && checkOutJSONData.data && checkOutJSONData.data.dataList) {
    //         this.setState({
    //             dataSource:checkOutJSONData.data.dataList
    //         })
    //     }
    // }
    renderRow=(checkOutItem)=>{
        console.log("=======checkOutItem:", checkOutItem);
        return (
            <View key={checkOutItem.checkOutId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{checkOutItem.customerName}</Text>
                    <Text style={styles.titleTextStyle}>{checkOutItem.gmtCreated}</Text>
                </View>
                <FlatList 
                data={checkOutItem.spCheckOutDetailDTOList}
                // renderItem={({item}) => this.renderAreaWarm(item)}
                renderItem={({item}) => 
                <View style={styles.bodyViewStyle}>
                    <Text style={styles.bodyTextStyle}>砖型：{item.brickTypeName}</Text>
                    <Text style={styles.bodyTextStyle}>出库数量：{item.orderCheckOutAmount}</Text>
                </View>
                }/>
                
                <View style={styles.bodyViewStyle}>
                    <Text style={styles.bodyTextStyle}>领料人：{checkOutItem.consigneeName}</Text>
                    <Text style={styles.bodyTextStyle}>货车车号：{checkOutItem.licensePlate}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={styles.bodyTextStyle}>驾驶员：{checkOutItem.driverName}</Text>
                    <Text style={styles.bodyTextStyle}>联系电话：{checkOutItem.driverTel}</Text>
                </View>
            </View>
        )
    }
    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                // this.props.navigation.goBack()
                this.props.navigation.navigate("CheckOutAdd", 
                {
                    // 传递回调函数
                    refresh: this.loadCheckOutList 
                });
            }}>
                 <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }
    render(){
        console.log("====this.state.dataSource:", this.state.dataSource);
        return(
            <View>
                {/* <TopScreen title='出库砖料出库记录管理' do="check_out_add"/> */}
                <CommonHeadScreen title='出库管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                <FlatList 
                    data={this.state.dataSource}
                    ItemSeparatorComponent={this.space}
                    ListEmptyComponent={this.emptyComponent}
                    renderItem={({item}) => this.renderRow(item)}
                    />
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:23
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyTextStyle:{
        fontSize:16
    }
})
module.exports = CheckOutList;