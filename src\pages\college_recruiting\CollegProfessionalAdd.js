import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class CollegProfessionalAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            professionalId: "",
            professionalName: "",
            professionalAbbreviation: "",
            professionalIntroduce: "",
            professionalSort:0,
            operate:"",
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { professionalId } = route.params;
            if (professionalId) {
                console.log("=============professionalId" + professionalId + "");
                this.setState({
                    professionalId:professionalId,
                    operate:"编辑"
                })
                let loadTypeUrl= "/biz/colleg/professional/get";
                let loadRequest={'professionalId':professionalId};
                httpPost(loadTypeUrl, loadRequest, this.loadCollegProfessionalCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    loadCollegProfessionalCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                professionalName:response.data.professionalName,
                professionalAbbreviation:response.data.professionalAbbreviation,
                professionalIntroduce:response.data.professionalIntroduce,
                professionalSort:response.data.professionalSort
            })
        }
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("CollegProfessionalList", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>专业管理</Text>
            </TouchableOpacity>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveCollegProfessional =()=> {
        console.log("=======saveCollegProfessional");
        let toastOpts;
        if (!this.state.professionalName) {
            toastOpts = getFailToastOpts("请填写专业名称");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/colleg/professional/add";
        if (this.state.professionalId) {
            console.log("=========Edit===professionalId", this.state.professionalId)
            url= "/biz/colleg/professional/modify";
        }
        let requestParams={
            professionalId:this.state.professionalId,
            professionalName: this.state.professionalName,
            professionalAbbreviation: this.state.professionalAbbreviation,
            // professionalIntroduce:this.state.professionalIntroduce,
            professionalSort:this.state.professionalSort
        };
        httpPost(url, requestParams, this.saveCollegProfessionalCallBack);
    }

    // 保存回调函数
    saveCollegProfessionalCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '专业'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>专业名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入专业名称'}
                            onChangeText={(text) => this.setState({ professionalName: text })}
                        >
                            {this.state.professionalName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>专业简称</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入专业简称'}
                            onChangeText={(text) => this.setState({ professionalAbbreviation: text })}
                        >
                            {this.state.professionalAbbreviation}
                        </TextInput>
                    </View>
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>专业介绍</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入专业介绍'}
                            onChangeText={(text) => this.setState({ professionalIntroduce: text })}
                        >
                            {this.state.professionalIntroduce}
                        </TextInput>
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({ professionalSort: text })}
                        >
                            {this.state.professionalSort}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveCollegProfessional.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({

    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }

});