import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,ScrollView,
    FlatList,TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class CollegClassGradesAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            classId: "",
            className: "",
            classAbbreviation:"",
            professionalId:"",
            professionalName: "",
            education: "",
            educationName:"",
            professionalDataSource:[],
            classSort:0,
            educationDataSource:[],
            selectEducation:[],
            selectProfessional:[],
            roleDataSource:[],
            selectRole:[],
            roleId:"",
            roleName:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 加载角色列表
        this.loadRoleList();
        // 加载专业列表
        this.loadProfessionalList();
        // 加载学历列表
        this.loadEducationList();
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { classId } = route.params;
            if (classId) {
                console.log("=============classId" + classId + "");
                this.setState({
                    classId:classId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/college/class/grades/get";
                loadRequest={'classId':classId};
                httpPost(loadTypeUrl, loadRequest, this.loadClassDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }

    loadClassDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                className:response.data.className,
                roleName:response.data.roleName,
                selectRole:[response.data.roleName],
                classAbbreviation:response.data.classAbbreviation,
                professionalId:response.data.professionalId,
                professionalName:response.data.professionalName,
                selectProfessional:[response.data.professionalName],
                education:response.data.education,
                educationName:response.data.educationName,
                selectEducation:[response.data.educationName],
                classSort: response.data.classSort,
            })
        }
    }

    loadRoleList=()=>{
        let url= "/biz/role/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            "excludeRoleIdList":[constants.loginUser.roleId]
        };
        httpPost(url, loadRequest, this.loadRoleListCallBack);
    }

    loadRoleListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                roleDataSource:response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadProfessionalList=()=>{
        let loadTypeUrl= "/biz/colleg/professional/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000
        };

        httpPost(loadTypeUrl, loadRequest, this.loadProfessionalListCallBack);

    }

    loadProfessionalListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                professionalDataSource:response.data.dataList
            })
        }
    }

    loadEducationList=()=>{
        let loadTypeUrl= "/biz/college/class/grades/educationList";
        let loadRequest={};
        httpPost(loadTypeUrl, loadRequest, this.loadEducationListCallBack);
    }

    loadEducationListCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                educationDataSource:response.data
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/*<Text style={CommonStyle.headLeftText}>返回</Text>*/}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
     
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("CollegClassGradesList", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>班级管理</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    openRoleSelect=()=>{
        if (!this.state.roleDataSource || this.state.roleDataSource.length < 1) {
            WToast.show({data:"没有创建角色，请添加企业对应的角色"});
            return
        }
        console.log("==========角色数据源：", this.state.roleDataSource);
        this.refs.SelectRole.showRole(this.state.selectRole, this.state.roleDataSource)

    }

    callBackRoleValue(value){
        console.log("==========角色选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectRole:value
        })
        var roleName = value.toString();
        let loadUrl= "/biz/role/getRoleByName";
        let loadRequest={
            "roleName":roleName
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    roleName:response.data.roleName,
                    roleId:response.data.roleId,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({data:response.message});
            }
        });
    }

    openEducationSelect(){
        if (!this.state.educationDataSource || this.state.educationDataSource.length < 1) {
            WToast.show({data:"请先添加学历"});
            return
        }
        this.refs.SelectEducation.showEducation(this.state.selectEducation, this.state.educationDataSource)
    }
    callBackEducationValue(value){
        console.log("==========学历选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectEducation:value
        })
        var educationName= value.toString();
        let loadUrl= "/biz/college/class/grades/educationByName";
        let loadRequest={
            "educationName":educationName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadEducationDetailData);
    }

    callBackLoadEducationDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                educationName:response.data.educationName,
                education:response.data.education,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
   
    openProfessionalSelect(){
        if (!this.state.professionalDataSource || this.state.professionalDataSource.length < 1) {
            WToast.show({data:"请先添加专业"});
            return
        }
        this.refs.SelectProfessional.showMajor(this.state.selectProfessional, this.state.professionalDataSource)
    }
    callBackProfessionValue(value){
        console.log("==========专业选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectProfessional:value
        })
        var professionalName = value.toString();
        let loadUrl= "/biz/colleg/professional/getProfessionalByName";
        let loadRequest={
            "professionalName":professionalName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadProfessionalDetailData);
    }
    callBackLoadProfessionalDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                professionalName:response.data.professionalName,
                professionalId:response.data.professionalId,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    saveClass =()=> {
        console.log("=======saveClass");
        let toastOpts;
        if (!this.state.className) {
            toastOpts = getFailToastOpts("请输入班级名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.roleName) {
            toastOpts = getFailToastOpts("请选择所属角色");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.professionalId) {
            toastOpts = getFailToastOpts("请选择所属专业");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.education) {
            toastOpts = getFailToastOpts("请选择学历");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/college/class/grades/add";
        if (this.state.classId) {
            console.log("=========Edit===classId", this.state.classId)
            url= "/biz/college/class/grades/modify";
        }
        let requestParams={
            classId: this.state.classId,
            className: this.state.className,
            classAbbrevation: this.state.classAbbrevation,
            professionalId: this.state.professionalId,
            education: this.state.education,
            classSort: this.state.classSort,
            roleId: this.state.roleId
        };
        httpPost(url, requestParams, this.saveClassCallBack);
    }

    // 保存回调函数
    saveClassCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

   

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '班级'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>班级名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入班级名称'}
                            onChangeText={(text) => this.setState({className:text})}
                        >
                            {this.state.className}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>所属角色</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openRoleSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 5)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.roleName ? "请选择角色" : this.state.roleName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>专业</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openProfessionalSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 5)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.professionalName ? "请选择专业" : this.state.professionalName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>学历</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openEducationSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 5)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.educationName ? "请选择学历" : this.state.educationName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({ classSort: text })}
                        >
                            {this.state.classSort}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView]} >
                                <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveClass.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView]}>
                                <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect 
                        ref={'SelectRole'} 
                        callBackRoleValue={this.callBackRoleValue.bind(this)}
                    />
                    <BottomScrollSelect 
                            ref={'SelectEducation'} 
                            callBackEducationValue={this.callBackEducationValue.bind(this)}
                        />
                    <BottomScrollSelect 
                            ref={'SelectProfessional'} 
                            callBackMajorValue={this.callBackProfessionValue.bind(this)}
                        />
             </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({ itemViewStyle:{
    margin:10,  
    padding:15, 
    borderRadius:2,
    backgroundColor:'#FFFFFF'
},
selectedItemViewStyle:{
    margin:10,  
    padding:15, 
    borderRadius:2,
    backgroundColor:"#CB4139"
},
itemTextStyle:{
    color:'#000000'
},
selectedItemTextStyle:{
    color:'#FFFFFF'
},
inputRowStyle:{
    height:45,
    flexDirection:'row',
    marginTop:10,
    // flex: 1,
    // justifyContent: 'space-between',
    // alignContent:'center'
    // backgroundColor:'#000FFF',
    // width:screenWidth,
    // alignContent:'space-between',
    // justifyContent:'center'
},

rowLabView:{
    height:45,
    flexDirection:'row',
    alignItems:'center',
    paddingLeft:10,
    // alignContent:'flex-start',
    // justifyContent:'center',
    // backgroundColor:'yellow',
},
leftLabView:{
    width:leftLabWidth,
    height:45,
    flexDirection:'row',
    alignItems:'center',
    paddingLeft:10,
},
leftLabNameTextStyle:{
    fontSize:18
},
leftLabRedTextStyle:{
    color:'red',
    marginLeft:5,
    marginRight:5
},
inputRightText:{
    width:screenWidth - (leftLabWidth + 5),
    borderRadius:5,
    borderColor:'#F1F1F1',
    borderWidth:1,
    marginRight:5,
    color:'#A0A0A0',
    fontSize:15,
    paddingLeft:10,
    paddingRight:10
}

});