import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class PortalTrackingList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            trackFkId: null,
            trackType: null,
            completionState: null,
            listTitleName: null,
            operate: null,
            flag: null
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { trackFkId, completionState, trackType, listTitleName, operate, flag } = route.params;
            // console.log("=====route.params======" + route.params)
            console.log("@_route.params_@", JSON.stringify(route.params, null, 6));
            if (trackFkId) {
                this.setState({
                    trackFkId: trackFkId
                })
            }
            if (completionState) {
                this.setState({
                    completionState: completionState
                })
            }
            if (trackType) {
                this.setState({
                    trackType: trackType
                })
            }
            if (listTitleName) {
                this.setState({
                    listTitleName: listTitleName
                })
            }

            if (operate) {
                this.setState({
                    operate: operate
                })
            }
            if (flag) {
                this.setState({
                    flag: flag
                })
            }

            this.loadTractList(trackFkId, trackType);
        }


    }

    // 回调函数
    callBackFunction = (contractId) => {
        let url = "/biz/track/detail/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "trackFkId": this.state.trackFkId,
            "trackType": this.state.trackType,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/track/detail/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "trackFkId": this.state.trackFkId,
            "trackType": this.state.trackType,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadTractList();
    }

    loadTractList = (trackFkId, trackType) => {
        let url = "/biz/track/detail/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "trackFkId": trackFkId ? trackFkId : this.state.trackFkId,
            "trackType": trackType ? trackType : this.state.trackType,
        };
        httpPost(url, loadRequest, this.loadTractListCallBack);
    }

    loadTractListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteTrack = (trackId) => {
        console.log("=======delete=trackId", trackId);
        let url = "/biz/track/detail/delete";
        let requestParams = { 'trackId': trackId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }


    renderRow = (item, index) => {
        return (
            <View key={item.trackId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.trackRemark}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>提交人：{item.operator}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>提交时间：{item.gmtCreated}</Text>
                </View>
                {
                    this.state.operate === 'query' ||this.state.flag === 'false'?
                        null
                        :
                        <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }, this.state.completionState === 'C' ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (this.state.completionState === 'C') {
                                    return;
                                }
                                Alert.alert('确认', '您确定要删除该条记录吗？', [
                                    {
                                        text: "取消", onPress: () => {
                                            WToast.show({ data: '点击了取消' });
                                            // this在这里可用，传到方法里还有问题
                                            // this.props.navigation.goBack();
                                        }
                                    },
                                    {
                                        text: "确定", onPress: () => {
                                            WToast.show({ data: '点击了确定' });
                                            this.deleteTrack(item.trackId)
                                        }
                                    }
                                ]);
                            }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 80, flexDirection: "row" }
                                ]}>
                                    <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                </View>
                            </TouchableOpacity>
                            <View>
                                {
                                    item.trackState === "0BB" ?
                                        <TouchableOpacity onPress={() => {
                                            this.props.navigation.navigate("PortalTrackingAdd",
                                                {
                                                    // 传递参数
                                                    trackId: item.trackId,
                                                    // 传递回调函数
                                                    refresh: this.callBackFunction
                                                })
                                        }}>
                                            <View style={[CommonStyle.itemBottomEditBtnViewStyle, { width: 80, flexDirection: "row" }
                                            ]}>
                                                <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                            </View>
                                        </TouchableOpacity>
                                        :
                                        null
                                }
                            </View>
                        </View>
                }
            </View>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        console.log("=====flag======" + this.state.flag)
        if (this.state.operate === 'query') {
            return (
                <View />
            )
        }
        if (this.state.flag === "false") {
            return (
                <View />
            )
        }
        else {
            return (
                this.state.completionState === 'C' ?
                    <View />
                    :
                    <TouchableOpacity onPress={() => {
                        // if(this.state.completionState === 'C'){
                        //     return ;
                        //  }
                        if (!this.state.trackFkId || !this.state.trackType) {
                            WToast.show({ dataAll: "数据错误，缺少参数-trackType/trackFkId" })
                            return;
                        }
                        this.props.navigation.navigate("PortalTrackingAdd",
                            {
                                trackType: this.state.trackType,
                                trackFkId: this.state.trackFkId,
                                listTitleName: this.state.listTitleName,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }}>
                        <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
                        {/* <Text style={CommonStyle.headRightText}>新增进展</Text> */}
                    </TouchableOpacity>
            )
        }
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={this.state.listTitleName}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});