import React,{ Component } from 'react';
import {View, Text, StyleSheet,Dimensions} from 'react-native';
var CommonStyle = require('../assets/css/CommonStyle');
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
class EmptyRowViewComponent extends Component {
    render(){
        return(
            <View style={[{height:60, width:screenWidth,alignItems: 'center', justifyContent: 'center', justifyContent: 'center',}]}>
                <Text style={styles.contentTextStyle}>
                    {this.props.title ? this.props.title : 'NO DATA'}
                </Text>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:60,
    //     width:screenWidth,
    //     alignItems: 'center',
    //     justifyContent: 'center',
    //     justifyContent: 'center',
    // },
    contentTextStyle:{
        color:'#A0A0A0',
        fontSize:25
    }
})
module.exports = EmptyRowViewComponent;