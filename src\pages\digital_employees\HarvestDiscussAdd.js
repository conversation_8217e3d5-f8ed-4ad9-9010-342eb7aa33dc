import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput,Image, StyleSheet,FlatList,TouchableOpacity,Dimensions,KeyboardAvoidingView} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class HarvestDiscussAdd extends Component {
    constructor(){
        super()
        this.state = {
            harvestId: "",
            parentDiscussId:"",
            discussContent: "",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { harvestId, parentDiscussId } = route.params;
            if (harvestId) {
                console.log("========harvestId:", harvestId);
                this.setState({
                    harvestId:harvestId,
                })
            }
            if (parentDiscussId) {
                console.log("========parentDiscussId:", parentDiscussId);
                this.setState({
                    parentDiscussId:parentDiscussId,
                })
            }
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    saveHarvestDiscuss =()=> {
        console.log("=======saveHarvestDiscuss");
        let toastOpts;
        if (!this.state.discussContent) {
            toastOpts = getFailToastOpts("请输入留言内容");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/harvest/discuss/add";
        let requestParams={
            discussContent:this.state.discussContent,
            harvestId: this.state.harvestId,
            parentDiscussId: this.state.parentDiscussId
        };
        httpPost(url, requestParams, this.saveHarvestDiscussCallBack);
    }
    
    // 保存回调函数
    saveHarvestDiscussCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={'留言'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <KeyboardAvoidingView behavior="position" keyboardVerticalOffset = {100} >
                        <View style={[styles.inputRowStyle]}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>留言内容</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle,{height:250}]}>
                            <TextInput 
                                multiline={true}
                                textAlignVertical="top"
                                style={[CommonStyle.inputRowText,{height:240}]}
                                placeholder={'请输入留言内容'}
                                onChangeText={(text) => this.setState({discussContent:text})}
                            >
                                {this.state.discussContent}
                            </TextInput>
                        </View>
                        
                        <View style={CommonStyle.btnRowStyle}>
                            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                                <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                    <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={this.saveHarvestDiscuss.bind(this)}>
                                <View style={CommonStyle.btnRowRightSaveBtnView}>
                                    <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </KeyboardAvoidingView>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    selectViewItem:{
        width:100, justifyContent:'center', alignItems:'center'
    },
    selectTextItem:{
        fontSize:18,
        fontWeight:'bold'
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})