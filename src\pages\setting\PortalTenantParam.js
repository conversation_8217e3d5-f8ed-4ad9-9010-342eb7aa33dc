import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image, TextInput,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import EmptyPortalTenantComponent from '../../component/EmptyPortalTenantComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ScrollView } from 'react-native-gesture-handler';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;

import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

const leftLabWidth = 130;
export default class PortalTenantParam extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            pictureIndex:0,
            isShowImage:false,
            image:"",
            bannerConfigList:["RESUME_SHARE_WEBSITE_BANNER", "ENTERPRISE_SHARE_WEBSITE_BANNER", "MEMBER_SHARE_WEBSITE_BANNER"],
            moreModal:false,
            deleteModal:false,
            editModal:false,
            portalTenantParamDataSource: [
                {
                    bizModuleCode: 'D',
                    paramName:'日报分享',
                },
                {
                    bizModuleCode: 'R',
                    paramName:'人才库',
                },
                {
                    bizModuleCode: 'E',
                    paramName:'企业库',
                },
                {
                    bizModuleCode: 'V',
                    paramName:'私域共享平台',
                },
                {
                    bizModuleCode: 'J',
                    paramName:'经川财经',
                }
            ],
            portalConfigItemParamDataSource: []
        }
    }

    // // 回调函数
    // callBackFunction=(scene)=>{
    //     let url= "/biz/tenant/config/list";
    //     let loadRequest={
    //         "currentPage": 1,
    //         "pageSize": this.state.pageSize
    //     };
    //     httpPost(url, loadRequest, this._loadFreshDataCallBack);
    // }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // this.loadPortalTenantConfigList();
        this.loadTenantBizModuleCode();
        
    }

    // 加载租户配置大类
    loadTenantBizModuleCode = ()=>{
        let url= "/biz/tenant/config/getBizModuleCodeList";
        let loadRequest={
            "paramCode": "CONFIG_ITEM_SETTING",
            "tenantId": constants.loginUser.tenantId,
        };
        httpPost(url, loadRequest, this.loadTenantBizModuleCodeCallBack);
    }

    loadTenantBizModuleCodeCallBack = (response) => {
        if (response.code == 200 && response.data && response.data) {
            console.log("response.data===========", response.data)
            var dataNew = response.data;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // loadPortalTenantConfigList=()=>{
    //     let url= "/biz/tenant/config/list";
    //     let loadRequest={
    //         "currentPage": this.state.currentPage,
    //         "pageSize": this.state.pageSize
    //     };
    //     httpPost(url, loadRequest, this.loadPortalTenantConfigListCallBack);
    // }

    // loadPortalTenantConfigListCallBack=(response)=>{
    //     // let paramValue = response.data.paramValue;
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         var dataNew = response.data.dataList;
    //         var dataOld = this.state.dataSource;
    //         // dataOld.unshift(dataNew);
    //         var dataAll = [...dataOld,...dataNew];
    //         this.setState({
    //             dataSource:dataAll,
    //             currentPage:response.data.currentPage + 1,
    //             totalPage:response.data.totalPage,
    //             totalRecord:response.data.totalRecord,
    //             refreshing:false,
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // // 下拉触顶刷新到第一页
    // _loadFreshData=()=>{
    //     if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
    //         console.log("==========不刷新=====");
    //         return;
    //     }
    //     this.setState({
    //         currentPage:1
    //     })
    //     let url= "/biz/tenant/config/list";
    //     let loadRequest={
    //         "currentPage": 1,
    //         "pageSize": this.state.pageSize
    //     };
    //     httpPost(url, loadRequest, this._loadFreshDataCallBack);
    // }

    // _loadFreshDataCallBack=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         var dataNew = response.data.dataList;
    //         // dataOld.unshift(dataNew);
    //         var dataAll = [...dataNew];
    //         this.setState({
    //             dataSource:dataAll,
    //             currentPage:response.data.currentPage + 1,
    //             totalPage:response.data.totalPage,
    //             totalRecord:response.data.totalRecord,
    //             refreshing:false
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // flatListFooterComponent=()=>{
    //     return(
    //         <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
    //     )
    // }
    // // 上拉触底加载下一页
    // _loadNextData=()=>{
    //     if ((this.state.currentPage-1) >= this.state.totalPage) {
    //         WToast.show({data:"已经是最后一页了，我们也是有底线的"});
    //         return;
    //     }
    //     this.setState({
    //         refreshing:true
    //     })
    //     this.loadPortalTenantConfigList();
    // }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("PortalTenantParamAdd", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            
            // </TouchableOpacity>
        )
    }


    // deletePortalTenantParam =(paramCode)=> {
    //     console.log("=======delete=paramCode", paramCode);
    //     let url= "/biz/tenant/config/delete";
    //     let requestParams={'paramCode':paramCode};
    //     httpDelete(url, requestParams, this.deleteCallBack);
    // }

    // // 删除操作的回调操作
    // deleteCallBack=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         WToast.show({data:"删除完成"});
    //         this.callBackFunction();
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    //     else {
    //         WToast.show({data:response.message});
    //     }
    // }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyPortalTenantComponent/>
    }

    renderRow=(item, index)=>{
        return (
            <View style={{backgroundColor:'rgba(255, 255, 255, 1)'}} key={index}>
                <TouchableOpacity 
                    onPress={() => {
                        this.props.navigation.navigate("PortalTenantParamItem",
                            {
                                // 传递参数
                                bizModuleCode: item.bizModuleCode,
                                paramName: item.paramName,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                        
                    }}>
                <View style={[styles.titleViewStyle, { marginTop: 10 }]}>
                    <Text style={styles.titleTextStyle}>{item.paramName}</Text>
                </View>

                    <View style={{
                        width: 40, height: 40,
                        backgroundColor: 'rgba(255,0,0,0.0)',
                        // backgroundColor:"red",
                        position: 'absolute',
                        alignItems: 'center',
                        justifyContent: 'center',
                        right: 20,
                    }}>
                        <Image style={{ width: 18, height: 18 }} source={require('../../assets/icon/iconfont/rightArrow1.png')}></Image>
                    </View>
                </TouchableOpacity>
                <View style={styles.lineViewStyle}/> 
            </View>
    
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='系统配置'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.contentViewStyle]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}                        
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    container:{
        flex: 1,
        justifyContent: 'center', // 水平居中
        //alignItems: 'center', // 水平居中
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    lineViewStyle:{
        // height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 2,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 18
    },
    innerRenderViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        backgroundColor: '#FFFFFF',
        borderWidth: 8,
        paddingBottom: 8,
        paddingTop: 8,
        borderRadius: 20
    },
    lineViewStyle:{
        // height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },
});