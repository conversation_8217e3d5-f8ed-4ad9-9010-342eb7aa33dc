import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,
    FlatList,RefreshControl,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';

import BottomScrollSelect from '../../component/BottomScrollSelect';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class OutsourcingTenantList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            // refreshing: false,
            pageSize:100,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            tenantId:"",
            operateTenantId:""  ,
            selTenantIdList:[],
            oldselTenantIdList:[],      
        }
    }
    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("========Edit==tenantId:", tenantId);
                this.setState({
                    operateTenantId:tenantId,
                })
                this.loadOutsourcingTenantList(tenantId);
            }
        }       
    }

    // 回调函数
    // callBackFunction=(tenantId)=>{
    //     let url= "/biz/tenant/outsourcing/list";                
    //     let loadRequest={
    //         "currentPage": 1,
    //         "pageSize": this.state.pageSize,
    //         "operateTenantId":tenantId ? tenantId : this.state.tenantId
    //     };
    //     httpPost(url, loadRequest, this._loadFreshDataCallBack);
    // }

    // 下拉触顶刷新到第一页
    // _loadFreshData=(tenantId)=>{
    //     if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
    //         console.log("==========不刷新=====");
    //         return;
    //     }
    //     this.setState({
    //         currentPage:1
    //     })
    //     let url= "/biz/tenant/outsourcing/list";          
    //     let loadRequest={
    //         "currentPage": 1,
    //         "pageSize": this.state.pageSize,
    //         "operateTenantId":tenantId?tenantId:this.state.tenantId
    //     };
    //     httpPost(url, loadRequest, this._loadFreshDataCallBack);
    // }

    // _loadFreshDataCallBack=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         var dataNew = response.data.dataList;
    //         // dataOld.unshift(dataNew);
    //         var dataAll = [...dataNew];
    //         this.setState({
    //             dataSource:dataAll,
    //             currentPage:response.data.currentPage + 1,
    //             totalPage:response.data.totalPage,
    //             totalRecord:response.data.totalRecord,
    //             refreshing:false
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // flatListFooterComponent=()=>{
    //     return(
    //         <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
    //     )
    // }

    // 上拉触底加载下一页
    // _loadNextData=()=>{
    //     if ((this.state.currentPage-1) >= this.state.totalPage) {
    //         WToast.show({data:"已经是最后一页了，我们也是有底线的"});
    //         return;
    //     }
    //     this.setState({
    //         refreshing:true
    //     })
    //     this.loadOutsourcingTenantList();
    // }

    copyArr=(varArray)=>{
        let res = []
        for (let i = 0; i < varArray.length; i++) {
         res.push(varArray[i])
        }
        return res
    }

    loadOutsourcingTenantList=(tenantId)=>{
        let loadTypeUrl;
        let loadRequest;
        loadTypeUrl = "/biz/tenant/list";
        loadRequest={
            "currentPage":1,
            "pageSize":100,
            "operateTenantId":tenantId
        }
        httpPost(loadTypeUrl, loadRequest, this.loadOutsourcingTenantListCallBack);
    }

    loadOutsourcingTenantListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            console.log(dataAll)
            let tenantDTOList = dataAll;
            this.setState({
                dataSource:tenantDTOList
            })
            var tenantDTO;

            var selTenantIdList = [];
            for(var index = 0; index < tenantDTOList.length; index ++) {
                tenantDTO = tenantDTOList[index];
                console.log("=========init=tenantDTO:", tenantDTO);
                if (tenantDTO && tenantDTO.selected === "Y") {
                    selTenantIdList = selTenantIdList.concat(tenantDTO.tenantId)
                }
            }
            this.setState({
                selTenantIdList:selTenantIdList,
                oldselTenantIdList:this.copyArr(selTenantIdList),
            })
            console.log("=========init=selTenantIdList:", selTenantIdList);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    saveOutsourcing = () => {
        console.log("=======saveOutsourcing");
        let toastOpts;
        if (!this.state.selTenantIdList) {
            toastOpts = getFailToastOpts("请选择外协单位");
            WToast.show(toastOpts)
            return;
        }

        let url = "/biz/tenant/outsourcing/add";
        let requestParams = {
            operateTenantId:this.state.operateTenantId?this.state.operateTenantId:constants.loginUser.tenantId,
            tenantIdList:this.state.selTenantIdList,
            oldTenantIdList:this.state.oldselTenantIdList
        };
        httpPost(url, requestParams, this.saveOutsourcing_call_back);

    }

    // 保存回调函数
    saveOutsourcing_call_back = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }


    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.saveOutsourcing();
            }}>
                <Image  style={{width:28, height:28,marginRight:5}} source={require('../../assets/icon/iconfont/save.png')}></Image>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='外协管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.95, justifyContent:'flex-start'}]}>
                        {this.state.dataSource.map((item, key)=>{
                            return(
                                <TouchableOpacity onPress={()=>{
                                    var selTenantIdList = this.state.selTenantIdList;
                                    if (item.selected && item.selected == "Y") {
                                        item.selected = "N";
                                        arrayRemoveItem(selTenantIdList, item.tenantId);
                                    }
                                    else {
                                        item.selected = "Y";
                                        selTenantIdList = selTenantIdList.concat(item.tenantId)
                                    }
                                    this.setState({
                                        selTenantIdList:selTenantIdList,
                                    })
                                    WToast.show({data:'点击了' + item.tenantName});
                                    console.log("======selTenantIdList:", selTenantIdList)
                                    console.log("======oldselTenantIdList:", this.state.oldselTenantIdList)
                                }}>
                                    <View style={[{margin:10, borderRadius:4, padding:10, height:40, backgroundColor:'#F5F5F5'}, (item.selected && item.selected === 'Y') ? {backgroundColor:'red'} : ""]}>
                                        <Text style={[styles.titleTextStyle, (item.selected && item.selected === 'Y') ? {color:'#FFFFFF'} : {color:'#000000'}]}>{item.tenantAbbreviation?item.tenantAbbreviation:item.tenantName}</Text>
                                    </View>
                                </TouchableOpacity>
                                
                            )
                        })}
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveOutsourcing.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
});