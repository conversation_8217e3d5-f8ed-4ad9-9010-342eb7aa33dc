import React,{Component} from 'react';
import {View, Text, StyleSheet, Image, FlatList,Dimensions, ScrollView, TouchableOpacity} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';

import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';

// 引入公共样式
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
class UnLoadedKilnList extends Component{
    constructor(props) {
        super(props);
        this.state = {
            dataSource: []
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadUnLoadedKilnList();
    }

    loadUnLoadedKilnList=()=>{
        let url= "/biz/unloaded/kiln/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 200
        };
        httpPost(url, loadRequest, this.callBackLoadUnLoadKilnList);
    }

    callBackLoadUnLoadKilnList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                dataSource:response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    cancelUnloadKiln=(unloadedKilnId)=> {
        WToast.show({data:"数据已经录入,不能取消卸窑"});
        // let url= "/biz/encastage/record/delete";
        // let requestParams={'unloadedKilnId':unloadedKilnId};
        // httpDelete(url, requestParams, this.cancelUnloadKilnCallBack);
    }

    // 删除操作的回调操作
    cancelUnloadKilnCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.loadUnLoadedKilnList();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        console.log("========LIST=>ADD");
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("UnLoadedKilnAdd", 
                {
                    // 传递回调函数
                    refresh: this.loadUnLoadedKilnList 
                })
            }}>
                <Text style={CommonStyle.headRightText}>新增卸窑</Text>
            </TouchableOpacity>
        )
    }

    renderItemRow=(itemChildList)=>{                
        return (
            <FlatList 
            data={itemChildList}
            // renderItem={({item}) => this.renderChildItem(item)}
            renderItem={({item}) => 
            <View key={item.detailId} style={styles.itemContentChildViewStyle}>
                <View style={styles.itemContentChildCol1ViewStyle}>
                    <Text style={styles.itemContentChildTextStyle}>砖型：{item.brickTypeName}</Text>
                </View>
                <View style={styles.itemContentChildCol2ViewStyle}>
                    <Text style={styles.itemContentChildTextStyle}>块数：{item.amount}</Text>
                </View>
                <View style={styles.itemContentChildCol2ViewStyle}>
                    <Text style={styles.itemContentChildTextStyle}>废品数：{item.wasteAmount}</Text>
                </View>
            </View>}
            />
        );
    }
    renderRow=(item)=>{
        return (
            <View key={item.unloadedKilnId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>窑车-{item.kilnCarName}</Text>
                    <Text style={styles.titleTextStyle}>{item.gmtCreated}</Text>
                </View>
                <View style={styles.itemContentStyle}>
                    <View style={{width:100, height:100, borderRadius:100, backgroundColor:'#DFDFFC', flexDirection:'column', alignItems:'center',justifyContent:'center'}}>
                        <Text style={{color:'#666666',fontSize:34}}>{item.kilnCarName}</Text>
                    </View>
                    <View style={styles.itemContentViewStyle}>
                    {this.renderItemRow(item.spUnloadedKilnDetailDTOList)}
                    </View>
                </View>
                {/* <View style={styles.itemBottomBtnStyle}>
                    <TouchableOpacity onPress={()=>{
                        this.cancelUnloadKiln(item.unloadedKilnId)
                    }}>
                        <View style={styles.itemBottomEditBtnViewStyle}>
                            <Text style={styles.itemBottomEditBtnTextStyle}>取消卸窑</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}
            </View>
        )
    }
    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='卸窑管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                <FlatList 
                    data={this.state.dataSource}
                    ItemSeparatorComponent={this.space}
                    renderItem={({item}) => this.renderRow(item)}
                    ListEmptyComponent={this.emptyComponent}
                    />
                </ScrollView>
               
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFF'
    // },
    innerViewStyle:{
        marginTop:10
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10
    },
    titleTextStyle:{
        fontSize:23
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center',
        marginLeft:15,
        paddingTop:5
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'row'
    },
    itemContentChildCol1ViewStyle:{
        marginLeft:20,
        marginTop:15
    },
    itemContentChildCol2ViewStyle:{
        marginLeft:40,
        marginTop:15
    },
    itemContentChildTextStyle:{
        fontSize:15
    },
    itemBottomBtnStyle:{
        flexDirection:'row',
        justifyContent:'flex-end'
    },
    itemBottomDeleteBtnViewStyle:{
        fontSize:16,
        width:100,
        height:30,
        borderWidth:1,
        borderColor:'#A0A0A0',
        justifyContent:'center',
        alignItems:'center',
        margin:10,
        borderRadius:4
    },
    itemBottomEditBtnViewStyle:{
        fontSize:16,
        width:100,
        height:30,
        justifyContent:'center',
        alignItems:'center',
        margin:10,
        backgroundColor:"#CB4139",
        borderRadius:4
    },
    itemBottomEditBtnTextStyle:{
        color:'#F0F0F0'
    }
});
module.exports = UnLoadedKilnList;
