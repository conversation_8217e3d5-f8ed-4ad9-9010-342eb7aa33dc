import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,Image, Modal,
    Platform
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../utils/CameraRollUtils';
const { ifIphoneXContentViewHeight, ifIphoneXBodyViewHeight, isIphoneX, fullScreenIfIphoneXContentViewHeight } = require('../../utils/ScreenUtil');
var CommonStyle = require('../../assets/css/CommonStyle');


var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class MemberManagementDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            personalHonor:"",
            collegeEvaluation:"",
            selExtList:[],
            selStaffId:null,
            staffId:"",
            staffName: "",
            staffTel: "",
            nationality: "",
            className: "",
            nativePlace: "",
            address: "",
            email: "",
            electronicPhotos:"",
            professionalName:  "",
            graduateInstitutions:"",
            userPhotoUrl:"",
            userId:"",
            mainProducts:"",
            enterpriseDemand:"",
            className:  "",
            selectedClass: [],
            classDataSource:[],
            portraitName:"",
            staffPosition: "",
            resumeDisplay: "",
            searchKeyWordThree: "",
            staffIdList: [],
            currentIndex: 0,
            businessCardModal: false,
            businessCard: "",
            showBusinessCard: false,
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 获取积分情况

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { staffId, staffName, searchKeyWordThree } = route.params;
            if(staffName) {
                this.setState({
                    staffName : staffName
                })
            }
            if(searchKeyWordThree) {
                this.setState({
                    searchKeyWordThree : searchKeyWordThree
                })
            }
            if(staffId) {
                console.log("====传入的员工id是" + staffId);
                this.setState({
                    staffId : staffId
                })
                this.loadMemberInterViewList(staffId);
            }
            this.loadMemberManagementList(searchKeyWordThree);
        }
    }


    loadMemberInterViewList=(staffId) =>{
        let url = "/biz/cr/member/get";
        let loadRequest = {
           "staffId" : staffId,
           "staffType": "M",
           "sign":true,
        };
        httpPost(url, loadRequest, this._loadMemberInterViewListcallback);

    }

    _loadMemberInterViewListcallback=(response) =>{
        if (response.code == 200 && response.data) {
            this.setState({
            staffId: response.data.staffId,
            staffName: response.data.staffName,
            staffTel: response.data.staffTel,
            nationality: response.data.nationality,
            className: response.data.className,
            nativePlace: response.data.nativePlace,
            address: response.data.address,
            userPhotoUrl:constants.image_addr + '/' +  response.data.electronicPhotos,
            electronicPhotos:response.data.electronicPhotos,
            professionalName:  response.data.professionalName,
            personalHonor: response.data.personalHonor,
            collegeEvaluation: response.data.collegeEvaluation,
            selExtList:response.data.crStaffExtDTOList,
            graduateInstitutions:response.data.graduateInstitutions,
            mainProducts:response.data.mainProducts,
            enterpriseDemand:response.data.enterpriseDemand,
            userId:response.data.userId,
            className: response.data.className,
            selectedClass: [response.data.className],
            portraitName:response.data.portraitName,
            staffPosition:response.data.staffPosition,
            resumeDisplay:response.data.resumeDisplay,
            })

            console.log("现在的照片",constants.image_addr + '/' +  response.data.electronicPhotos)
        }
    }

    loadMemberManagementList = (searchKeyWordThree) => {
        let url = "/biz/cr/member/management/list";
        let data = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "staffType": "M",
            "staffState": "0AA",
            "sign": true,
            "searchKeyWordThree": searchKeyWordThree ? searchKeyWordThree : this.state.searchKeyWordThree,
        };
        httpPost(url, data, this.callBackLoadMemberManagementList);
    }

    callBackLoadMemberManagementList = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld, ...dataNew];
            // 获取会员标识列表
            var staffList = []
            dataNew.forEach(element => {
                staffList.push(element.staffId);
            });
            var currentIndex = staffList.indexOf(parseInt(this.state.staffId));
            console.log("currentIndex==========", currentIndex)
            console.log("staffList==========", staffList)
            this.setState({
                currentIndex: currentIndex,
                staffIdList: staffList,
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 展示上一个会员的信息
    previousMember = () => {
        var currentIndex = this.state.staffIdList.indexOf(parseInt(this.state.staffId));
        if (currentIndex === 0) {
            console.log("已经到第一个了")
            WToast.show({ data: '已经到第一个了' });
            return;
        }
        this.loadMemberInterViewList(this.state.staffIdList[currentIndex - 1]);
        this.setState({
            currentIndex: currentIndex - 1,
            staffId : this.state.staffIdList[currentIndex - 1]
        })
    }

    // 展示下一个会员的信息
    nextMember = () => {
        var currentIndex = this.state.staffIdList.indexOf(parseInt(this.state.staffId));
        if (currentIndex === (this.state.staffIdList.length - 1)) {
            console.log("已经到最后一个了")
            WToast.show({ data: '已经到最后一个了' });
            return;
        }
        this.loadMemberInterViewList(this.state.staffIdList[currentIndex + 1]);
        this.setState({
            currentIndex: currentIndex + 1,
            staffId : this.state.staffIdList[currentIndex + 1]
        })
    }

    staffDisplaySetting = () => {
        console.log("=======staffDisplaySetting=staffId", this.state.staffId);
        let requestUrl = "/biz/cr/member/update_member_display";
        let requestParams = {
            'staffId': this.state.staffId,
            'resumeDisplay': this.state.resumeDisplay === 'Y' ? 'N' : 'Y',
            "staffType": "M",
        };
        httpPost(requestUrl, requestParams, (response) => {
            if (response.code == 200) {
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                // 更新页面上显示状态
                this.setState({
                    resumeDisplay: this.state.resumeDisplay === 'Y' ? 'N' : 'Y'
                })
                WToast.show({ data: (this.state.resumeDisplay === 'Y' ? '显示' : '隐藏') + "会员完成" });
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }

    exportPdfFile = () => {
        console.log("=======exportPdfFile");
        let url = "/biz/generate/pdf/business_card";
        let requestParams = {
            "staffId": this.state.staffId,
            "staffType": "M"
        };
        httpPost(url, requestParams, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    businessCard: response.data,
                    businessCardModal: true,
                })
            }
        });
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    // 所属行业
    renderPortraitNameList = (value) => {
        return (
            <View key={value} style={[{backgroundColor: '#ECEEF2', height: 25, marginRight: 10, paddingLeft: 7, paddingTop: 2, paddingRight: 10,
                paddingBottom: 3, borderRadius: 2, justifyContent: 'center',alignContent: 'center'}
            ]}>
                <Text style={[{fontSize: 14, fontWeight: "400", color: 'rgba(0, 10, 32, 0.85)', lineHeight: 20, textAlign : 'center'}]}>
                    {value}
                </Text>
            </View>
        )
    }

    render(){
        return(
            <View style={{backgroundColor:'#FFFFFF',height:screenHeight}}>
                <CommonHeadScreen title= "会员详情"
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{height:ifIphoneXBodyViewHeight(),backgroundColor:'#FFFFFF' }}>
                <ScrollView style={[{height:ifIphoneXBodyViewHeight() - 64 }]}>
                    <View style={[styles.topBox]}> 
                        <View style={{ position: 'absolute', right: 3, top: 3 }}>
                            {
                                // this.state.resumeDisplay === "Y" ?
                                //     <View>
                                //         {/* <View style={[{ width: 0, height: 0, borderTopWidth: 40, borderTopColor: '#FF8C2860', borderLeftWidth: 50, borderLeftColor: 'transparent' }]}>
                                //         </View>
                                //         <View style={{ position: 'absolute', right: 2, top: 0 }}>
                                //             <Text style={{ fontSize: 12, color: '#FFFFFF',marginTop:1,marginLeft:1 }}>显示</Text>
                                //         </View> */}
                                //     </View>
                                //     :
                                //     <View>
                                //         <View style={[{ width: 0, height: 0, borderTopWidth: 40, borderTopColor: 'rgba(173,172,173,0.4)', borderLeftWidth: 58, borderLeftColor: 'transparent' }]}>
                                //         </View>
                                //         <View style={{ position: 'absolute', right: 2, top: 0 }}>
                                //             <Text style={{ fontSize: 12, color: '#FFFFFF',marginTop:1,marginLeft:1 }}>已隐藏</Text>
                                //         </View>
                                //     </View>
                            }
                        </View>

                        <View style={{width: screenWidth - 64, flexDirection: 'row'}}>
                            {
                                this.state.electronicPhotos ?
                                    <Image source={{ uri: constants.image_addr + '/' +  this.state.electronicPhotos }} style={{ height: 100, width: 80}} />
                                    :
                                    <Image style={{ height: 100, width: 80}} source={require('../../assets/icon/iconfont/head.png')}></Image>
                            }
                            <View style={{marginLeft:16, flexDirection: 'column'}}>
                                <View style={{flexDirection: 'row', alignItems: 'flex-start', width: screenWidth - 180, flexWrap: 'wrap'}}>
                                    <View style={{ flexDirection: 'row', marginRight: 16, flexWrap: 'wrap',marginTop:5 }}>
                                        <Text style={{ fontSize: 20, fontWeight: 'bold', color: 'rgba(0, 0, 0, 0.86)', lineHeight: 24 }}>{this.state.staffName}</Text>
                                        {
                                            this.state.resumeDisplay === "Y" ?
                                                <View></View>
                                                :
                                                <View>
                                                    <View style={{ width: 48, height: 20, marginLeft: 7, borderRadius: 2, marginTop:1,flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'rgba(173,172,173,0.4)' }}>
                                                        <Text style={{fontSize: 13, color: '#FFFFFF' }}>已隐藏</Text>
                                                    </View>
                                                </View>
                                        }
                                    </View>
                                </View>
                                
                                <View style={{flexDirection: 'row', width: screenWidth - 150, marginTop: 2, alignItems: 'center', flexWrap: "wrap",marginTop:5}}>
                                    <Text style={{ fontSize: 16, color: 'rgba(0, 10, 32, 0.85)', lineHeight: 20 }}>{this.state.graduateInstitutions ? this.state.graduateInstitutions : "暂无信息"}</Text>
                                </View>
                                <View style={{flexDirection: 'row', marginTop: 2, flexWrap: 'wrap',marginTop:5}}>
                                    <Text style={{ fontSize: 16, color: 'rgba(0,10,32,0.65)', lineHeight: 20 }}>{this.state.staffPosition}</Text>
                                </View>

                                <View style={{flexDirection: 'row', width: screenWidth - 160, alignItems: 'center',marginTop:5}}>
                                    <View style={{flexDirection: 'row', marginTop: 2, width: (screenWidth - 160)/2, flexWrap: "wrap"}}>
                                        <Text style={{ fontSize: 16, color: 'rgba(0, 10, 32, 0.85)', lineHeight: 24 }}>{this.state.address ? this.state.address : "暂无信息"}</Text>
                                    </View>

                                    <View style={{marginLeft:6,flexDirection: 'row', marginTop: 2,  width: (screenWidth - 160)/2}}>
                                        <Text style={{ fontSize: 16, color: 'rgba(0, 10, 32, 0.85)', lineHeight: 24 }}>{this.state.staffTel}</Text>
                                    </View>
                                </View>
                                    
                                <View style={{flexDirection: 'row', width: screenWidth - 145, flexWrap: "wrap",marginTop:5}}>
                                    {this.state.portraitName ?
                                        this.state.portraitName.split('  ').slice(0, -1).map((word, index) => (
                                            <View key={index} style={[{backgroundColor: '#ECEEF2', marginRight: 8, marginTop: 5, paddingLeft: 6, paddingTop: 3, paddingRight: 6,
                                                paddingBottom: 3, borderRadius: 2, justifyContent: 'center',alignContent: 'center'}
                                            ]}>
                                                <Text style={{ fontSize: 14, color: 'rgba(0, 10, 32, 0.65)', lineHeight: 20}}>{word}</Text>
                                            </View>
                                        ))
                                        :null
                                    }
                                </View>
                            </View>
                        </View>
                    </View>

                    <View style={[CommonStyle.addItemSplitRowViewDetail, {marginLeft: 18}]}>
                        <Image source={require('../../assets/image/business.png')} style={styles.titleImageStyle} />
                        <Text style={[CommonStyle.addItemSplitRowTextDetail]}>业务简介</Text>
                    </View>

                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>{this.state.collegeEvaluation ? this.state.collegeEvaluation :  "暂无信息"}</Text>
                    </View>

                    <View style={styles.lineViewStyle}/>

                    <View style={[CommonStyle.addItemSplitRowViewDetail, {marginLeft: 18}]}>
                        <Image source={require('../../assets/image/resourcesProviding.png')} style={styles.titleImageStyle} />
                        <Text style={[CommonStyle.addItemSplitRowTextDetail]}>我能提供的资源</Text>
                    </View>

                    <View style={[styles.titleViewStyle, {marginBottom: 30}]}>
                        <Text style={styles.titleTextStyle}>{this.state.personalHonor ? this.state.personalHonor :  "暂无信息"}</Text>
                    </View>


                    <View style={styles.lineViewStyle}/>

                    <View style={[CommonStyle.addItemSplitRowViewDetail, {marginLeft: 18}]}>
                        <Image source={require('../../assets/image/resource.png')} style={styles.titleImageStyle} />
                        <Text style={[CommonStyle.addItemSplitRowTextDetail]}>期望对接的资源</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>{this.state.enterpriseDemand ? this.state.enterpriseDemand :  "暂无信息"}</Text>
                    </View>
                    
                </ScrollView>

                <View style={[styles.buttonContainer]}>
                    <TouchableOpacity onPress={() => {this.previousMember()}}>
                        {
                            this.state.currentIndex === 0 ?
                                <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                                    <View style={[styles.imgBox]}>
                                        <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/previousDisabled.png')} />
                                    </View>
                                    <Text style={[{fontSize: 16, color: '#999999'}]}>上一个</Text>
                                </View>
                                :
                                <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                                    <View style={[styles.imgBox]}>
                                        <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/previous.png')} />
                                    </View>
                                    <Text style={[{fontSize: 16, color: '#1D80FF'}]}>上一个</Text>
                                </View>
                        }
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        let message = '您确认要在会员系统网页中' + (this.state.resumeDisplay === 'Y' ? '隐藏' : '显示') + '该会员吗？';
                        Alert.alert('确认', message, [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.staffDisplaySetting()
                                }
                            }
                        ]);
                    }}>
                        <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                            <View style={[styles.imgBox]}>
                                {this.state.resumeDisplay === 'Y' ?
                                    <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/password_hide.png')}></Image>
                                    :
                                    <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/show.png')}></Image>
                                }
                            </View>
                            {this.state.resumeDisplay === 'Y' ?
                                <Text style={[{ fontSize: 16, color: 'rgba(0, 10, 32, 0.65)' }]}>设为隐藏</Text>
                                :
                                <Text style={[{fontSize: 16, color: '#FF8C28'}]}>设为显示</Text>
                            }
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {this.exportPdfFile()}}>
                        <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                            <View style={[styles.imgBox]}>
                                <Image style={{ height: 20, width: 20}} source={require('../../assets/icon/iconfont/outputGreen.png')} />
                            </View>
                            <Text style={[{fontSize: 16, color: '#1BBC82'}]}>生成名片</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {this.nextMember()}}>
                        {
                            this.state.currentIndex === (this.state.staffIdList.length -1) ?
                                <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                                    <View style={[styles.imgBox]}>
                                        <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/nextDisabled.png')} />
                                    </View>
                                    <Text style={[{fontSize: 16, color: '#999999'}]}>下一个</Text>
                                </View>
                                :
                                <View style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: screenWidth / 4 }}>
                                    <View style={[styles.imgBox]}>
                                        <Image style={{ height: 24, width: 24}} source={require('../../assets/icon/iconfont/next.png')} />
                                    </View>
                                    <Text style={[{fontSize: 16, color: '#1D80FF'}]}>下一个</Text>
                                </View>
                        }
                    </TouchableOpacity>
                    
                </View>
                </View>

                {/* 生成名片弹窗Modal */}
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.businessCardModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle, { width: screenWidth, padding: 0, height: ifIphoneXBodyViewHeight() - 60 }]}>
                            <ScrollView>
                                <View styles={{ width: screenWidth }}>
                                    {
                                        this.state.businessCard
                                            ?
                                            <View>
                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        showBusinessCard: true,
                                                    })
                                                    console.log("个人名片地址", this.state.businessCard);
                                                }}

                                                    onLongPress={() => {

                                                        Alert.alert('确认', '您确定要保存该名片吗？', [
                                                            {
                                                                text: "取消", onPress: () => {
                                                                    WToast.show({ data: '点击了取消' });
                                                                    // this在这里可用，传到方法里还有问题
                                                                    // this.props.navigation.goBack();
                                                                }
                                                            },
                                                            {
                                                                text: "确定", onPress: () => {
                                                                    WToast.show({ data: '点击了确定' });
                                                                    var BusinessCard = this.state.businessCard;
                                                                    saveImage(BusinessCard);
                                                                }
                                                            }
                                                        ]);


                                                    }}
                                                >
                                                    <Image source={{ uri: this.state.businessCard }} style={{ width: screenWidth, height: ifIphoneXContentViewHeight(), justifyContent: 'center', alignItems: 'center' }} />
                                                    <TouchableOpacity style={{ position: 'absolute', justifyContent: 'center', width: screenWidth, alignItems: "center", bottom: 50 }}
                                                        onPress={() => {
                                                            this.setState({
                                                                businessCardModal: false,
                                                            })
                                                        }}
                                                    >
                                                        <Image style={{ width: 50, height: 50, justifyContent: 'center' }} source={require('../../assets/icon/iconfont/cancel2.png')}></Image>
                                                    </TouchableOpacity>

                                                </TouchableOpacity>
                                                <Modal visible={this.state.showBusinessCard} transparent={true}>
                                                    <ImageViewer enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                        saveToLocalByLongPress={true}
                                                        onClick={() => { // 图片单击事件
                                                            this.setState({
                                                                showBusinessCard: false,
                                                            })
                                                        }}
                                                        imageUrls={[{ url: this.state.businessCard }]}
                                                        onSave={() => {
                                                            var BusinessCard = this.state.businessCard;
                                                            saveImage(BusinessCard);
                                                        }}
                                                    />
                                                </Modal>
                                            </View>

                                            : <EmptyListComponent />
                                    }
                                </View>
                            </ScrollView>
                        </View>
                    </View>
                </Modal>
            </View>
        )
    }

}
const styles = StyleSheet.create({
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    titleImageStyle:{
        marginLeft:5, 
        width: 20, 
        height: 21,
    },
    line: {
        borderTopWidth: 0.5,
        borderTopColor: '#e1dede',
    },

    topBox: {
        flexDirection: 'row',
        margin: 16,
        padding: 16,
        borderColor: "#F4F4F4",
        elevation: 3,
    },

    innerViewStyleList:{
        // marginTop: 10,
        borderColor: "rgba(255, 255, 255, 1)",
        // borderColor: "#FFFFFF",
        // borderWidth: 2
    },

    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth + 30,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:16,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', width:100, alignItems:'center', alignContent:'flex-end', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },
    titleTextStyle:{
        fontSize: 16, 
        color: '#404956', 
        lineHeight: 24,
    },
    titleViewStyle:{
        flexDirection: 'row',
        marginLeft: 18,
        marginRight: 14,
        marginTop: 5
    },
    // innerViewStyleList:{
    //     // marginTop: 10,
    //     borderColor: "#F4F4F4",
    //     borderWidth: 2
    // },
    innerViewStyle:{
        marginTop:0,
    },
    photos:{
        width:150,
        height:200,
        // borderRadius:50,
        borderWidth:0,
        // marginTop:80,
        // marginBottom:30
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },
    buttonContainer: {
        // position: 'absolute',
        // flex: 1,
        height: 64,
        width: screenWidth,
        backgroundColor: 'rgba(255, 255, 255, 1)',
        // bottom: 0,
        paddingBottom: 8,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
       // borderTopLeftRadius: 32,
        //borderTopRightRadius: 32
    },
    imgBox: {
        width: 32,
        height: 32,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
    }
});