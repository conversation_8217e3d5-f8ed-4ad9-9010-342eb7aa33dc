import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Modal,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import * as WeChat from 'react-native-wechat-lib';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;

export default class LinkShareList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            shareModal:false,
            totalPage:1,
            totalRecord:1
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }

        }
    }

    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    // 回调函数
    callBackFunction=(contractId)=>{
        let url= "/biz/contract/collect/money/actual/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        console.log(loadRequest)
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=(contractId)=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/contract/collect/money/actual/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            // "contractId": contractId ? contractId : this.state.contractId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    _loadNextData=()=>{
      if ((this.state.currentPage-1) >= this.state.totalPage) {
          WToast.show({data:"已经是最后一页了，我们也是有底线的"});
          return;
      }
      this.setState({
          refreshing:true
      })
    }

    // 列表底部组件
    flatListFooterComponent=()=>{
      return(
          <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
      )
    }

    renderRow=(item, index)=>{
        return(
            <View></View>
        )
      }
  
      space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
      }
  
      emptyComponent() {
          return <EmptyListComponent/>
      }
  
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
       
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='链接分享'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>公益课堂在线报名</Text>
                        <TouchableOpacity onPress={()=>{
                                this.setState({
                                    shareModal:true,
                                })
                            }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,flexDirection:"row",marginLeft:0,borderColor:'#3ab240',borderWidth:1,backgroundColor:'rgba(0,0,0,0)' }]}>
                                    <Image  style={{width:20, height:20,marginRight:3}} source={require('../../assets/icon/iconfont/shareGreen.png')}></Image>
                                <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{color:"#3ab240"}]}>分享</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.shareModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() =>console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut,{backgroundColor: 'rgba(0,0,0,0.5)'}]}>
                        <View style={{width:screenWidth,height:250,bottom:0,position:'absolute',backgroundColor:'#f0f0f0'}}>
                            <View style={{height:55,justifyContent:'center',alignItems:'center'}}>
                                <Text style={{fontSize:18}}>
                                    选择分享方式
                                </Text>
                            </View>
                            <View style={{height:105,flexDirection:'row',justifyContent:'center',index:1000}}>
                                     {/* 分享微信好友 */}
                                <TouchableOpacity 
                                    onPress={() => {
                                        console.log("标题=====",'“数智人才”公益课堂')
                                        WeChat.shareWebpage({
                                            title: '“数智人才”公益课堂',
                                            description: '真学真干真创业，挑战年薪百万',
                                            thumbImageUrl: 'https://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/logo/jiangsu_digital_economy_ederation_logo.jpg',
                                            webpageUrl: 'https://jzxs.njjzgk.com/html/szrc/index.html?tenantId=' + constants.loginUser.tenantId,
                                            scene: 0
                                        })
                                        .then((respJSON) => {
                                            WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                        })
                                        .catch((error) => {
                                            WToast.show({ data: error });
                                            Alert.alert(error.message);
                                        });
                                    }}>
                                    <View style={[{width:100,flexDirection:"column",margin:5,justifyContent:'center',alignItems:'center'}]}>
                                        <Image  style={{width:40, height:40,marginRight:2}} source={require('../../assets/icon/iconfont/WeChat.png')}></Image>
                                        <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{color:'#000000'}]}>微信好友</Text>
                                    </View>
                                </TouchableOpacity>
                                {/* 微信朋友圈 */}
                                <TouchableOpacity 
                                    onPress={() => {
                                        console.log("标题=====",'“数智人才”公益课堂')
                                        WeChat.shareWebpage({
                                            title: '“数智人才”公益课堂',
                                            description: '真学真干真创业，挑战年薪百万',
                                            thumbImageUrl: 'https://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/logo/jiangsu_digital_economy_ederation_logo.jpg',
                                            webpageUrl: 'https://jzxs.njjzgk.com/html/szrc/index.html?tenantId=' + constants.loginUser.tenantId,
                                            scene: 1
                                        })
                                        .then((respJSON) => {
                                            WToast.show({ data: "respJSON" + JSON.stringify(respJSON) });
                                        })
                                        .catch((error) => {
                                            WToast.show({ data: error });
                                            Alert.alert(error.message);
                                        });
                                    }}>
                                    <View style={[{width:100,flexDirection:"column",margin:5,justifyContent:'center',alignItems:'center'}]}>
                                        <Image  style={{width:40, height:40,marginRight:2}} source={require('../../assets/icon/iconfont/FriendsCircle.png')}></Image>
                                        <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{color:'#000000'}]}>微信朋友圈</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{height:50,justifyContent:'center',alignItems:'center',borderTopWidth:1,borderTopColor:'#cccccc'}}>
                                <TouchableOpacity onPress={()=>{
                                    this.setState({
                                        shareModal:false
                                    })
                                }}>
                                    <View style={{width:screenWidth,justifyContent:'center',alignItems:'center'}}>
                                        <Text style={[{fontSize:18}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            

                        </View>
                    </View>
                </Modal>

            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    }
});
