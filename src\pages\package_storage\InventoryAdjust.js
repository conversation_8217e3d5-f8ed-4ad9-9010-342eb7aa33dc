import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class InventoryAdjust extends Component {
    constructor(){
        super()
        this.state = {
            brickTypeId:"",
            brickTypeName:"",
            checkInAmount:"",
            checkOutAmount:"",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { brickTypeId } = route.params;
            if (brickTypeId) {
                console.log("========Edit==brickTypeId:", brickTypeId);
                this.setState({
                    brickTypeId:brickTypeId
                })
                loadTypeUrl= "/biz/brick/series/type/get";
                loadRequest={'brickTypeId':brickTypeId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditBrickTypeDataCallBack);
            }
        }
    }
    loadEditBrickTypeDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                brickTypeId:response.data.brickTypeId,
                brickTypeName:response.data.brickTypeName,
                checkInAmount:response.data.checkInAmount,
                checkOutAmount:response.data.checkOutAmount,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("InventoryQuery")
            }}>
                <Text style={CommonStyle.headRightText}>成品库存查询</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveBrickType =()=> {
        console.log("=======saveBrickType");
        let toastOpts;
        if (!this.state.checkInAmount) {
            toastOpts = getFailToastOpts("请输入入库总数");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.checkOutAmount) {
            toastOpts = getFailToastOpts("请输入出库总数");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/brick/series/type/add";
        if (this.state.brickTypeId) {
            console.log("=========Edit===brickTypeId", this.state.brickTypeId)
            url= "/biz/brick/series/type/modify";
        }
        let requestParams={
            "brickTypeId":this.state.brickTypeId,
            "checkInAmount":this.state.checkInAmount,
            "checkOutAmount":this.state.checkOutAmount,
        };
        httpPost(url, requestParams, this.saveBrickTypeCallBack);
    }
    
    // 保存回调函数
    saveBrickTypeCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    render(){
        return (
            <View>
                <CommonHeadScreen title='库存调整'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>砖型名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入砖型名称'}
                            onChangeText={(text) => this.setState({brickTypeName:text})}
                        >
                            {this.state.brickTypeName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>入库总数</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入入库总数'}
                            onChangeText={(text) => this.setState({checkInAmount:text})}
                        >
                            {this.state.checkInAmount}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库总数</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入出库总数'}
                            onChangeText={(text) => this.setState({checkOutAmount:text})}
                        >
                            {this.state.checkOutAmount}
                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveBrickType.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})