import React,{Component} from 'react';
import {
    Alert,Modal,
    View, 
    ScrollView, 
    Text, 
    TextInput, 
    StyleSheet, 
    FlatList ,
    TouchableOpacity,
    Dimensions,
    Image,
    KeyboardAvoidingView
} from 'react-native';

import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class InventoryAllotAdd extends Component {

    constructor(props) {
        super(props);
        this.state ={
            allotId:"",
            selectBrirck:[],
            storageLocationDataSource:[],
            selLocationId:"",
            selLocationName:"",
            selLocationNewId:"",
            selLocationNewName:"",
            allotInLocationIdNewDataSource:[],
            allotDetailDTOList:[],
            // selOrderId:'',
            // selOrderName:'',

            //产品分类
            selBrickClassId:"",
            selBrickClassName:"",
            classifyDataSource:[],
            _classifyDataSource:[],
            //产品
            selSeriesId:"",
            selSeriesName:"",
            seriesDataSource:[],
            _seriesDataSource:[],
            //型号
            selBrickTypeId:'',
            selBrickTypeName:'',
            brickTypeDataSource:[],
            _brickTypeDataSource:[],
            allotType:"",


            selOutAmount:'',
            selOutWeight:'',
            selLocationAreaId:0,
            selLocationAreaName:"",
            selLocationNewAreaId:0,
            selLocationNewAreaName:"",
            storageLocationAreaDataSource:[],

            pageSize:15,
            currentPage:1,
            searchKeyWord: null,
            classModal:false,
            seriesModal:false,
            brickTypeModal:false,
            allotInLocationModal:false,
        }
    }

    UNSAFE_componentWillMount(){
        console.log('=aaaa=UNSAFE_componentWillMount==');
        this.loadLocationAreaList();
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { allotId, allotDetailDTOList } = route.params;
            if (allotId) {
                console.log("=========allotId:", allotId);
                this.setState({
                    allotId:allotId
                })
                // this.loadOrderData(customerId);
            }
            if (allotDetailDTOList) {
                this.setState({
                    allotDetailDTOList:allotDetailDTOList
                })
            }

        }

        // 加载产品分类
        let url= "/biz/brick/class/list";
        let loadRequest={
            "currentPage":1,
            "pageSize":10000
        };
        httpPost(url, loadRequest, this.loadBrickClassIfyListCallBack);
    }

    loadBrickClassIfyListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                classifyDataSource: response.data.dataList,
            })
            // this.loadBrickSeriesList();
            // console.log("===-=设置之后的id===" + this.state.brickClassId); 

        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadBrickSeriesList=()=>{
        let url= "/biz/brick/class/series/list";
        let loadRequest={
            "currentPage":1,
            "pageSize":1000,
            "brickClassId":this.state.selBrickClassId,
        };
        httpPost(url, loadRequest, this.loadBrickSeriesListCallBack);
    }

    loadBrickSeriesListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                seriesDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadBrickTypeListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                brickTypeDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    callBackLoadBrickTypeData=(response)=>{
        if (response.code == 200 && response.data && response.data) {
            this.setState({
                brickTypeDataSource:response.data
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadLocationAreaList=()=>{
        let url= "/biz/storage/location/area/list";
        let loadRequest={'currentPage':1,'pageSize':1000};
        httpPost(url, loadRequest, this.callBackLoadStorageLocationArea);
    }

    // 库区回调加载
    callBackLoadStorageLocationArea=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库区");
                WToast.show(toastOpts);
                return;
            }
            this.setState({
                storageLocationAreaDataSource:response.data.dataList,
                selLocationAreaId:response.data.dataList[0].locationAreaId,
                selLocationAreaName:response.data.dataList[0].locationAreaName
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 搜索分类
    searchClassiFy = () => {
        var _classifyDataSource = copyArr(this.state.classifyDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            console.log(this.state.searchKeyWord)
            _classifyDataSource = _classifyDataSource.filter(item => item.brickClassName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _classifyDataSource: _classifyDataSource,
        })
        
    }
    
    // 搜索产品
    searchSeries = () => {
        var _seriesDataSource = copyArr(this.state.seriesDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _seriesDataSource = _seriesDataSource.filter(item => item.seriesName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _seriesDataSource: _seriesDataSource,
        })
    }
    
    // 搜索型号
    searchType = () => {
        var _classifyDataSource = copyArr(this.state.classifyDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _classifyDataSource = _classifyDataSource.filter(item => item.brickClassName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _classifyDataSource: _classifyDataSource,
        })
    }
    
    // 分类项
    renderClassiFyItem = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.checkId) {
                    return;
                }
                this.setState({
                    selBrickClassId: item.brickClassId,
                    selBrickClassName: item.brickClassName,
                    selSeriesId:"",
                    selSeriesName:"",
                    selBrickTypeId:"",
                    selBrickTypeName:"",
                    _seriesDataSource:[],
                    _brickTypeDataSource:[]
                })
                let url= "/biz/brick/class/series/list";
                let loadRequest={
                    "currentPage":1,
                    "pageSize":1000,
                    "brickClassId":item.brickClassId,
                };

                httpPost(url, loadRequest, this.loadBrickSeriesListCallBack);
            }}>
                <View key={item.brickClassId} style={item.brickClassId === this.state.selBrickClassId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.brickClassId === this.state.selBrickClassId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.brickClassName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    
    // 产品项
    renderSeriesItem = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.checkId) {
                    return;
                }
                this.setState({
                    selSeriesId: item.seriesId,
                    selSeriesName: item.seriesName,
                    selBrickTypeId:"",
                    selBrickTypeName:"",
                    _brickTypeDataSource:[]
                })
                let url= "/biz/brick/series/type/list";
                let loadRequest={
                    "currentPage":1,
                    "pageSize":1000,
                    "seriesId":item.seriesId,
                };

                httpPost(url, loadRequest, this.loadBrickTypeListCallBack);
            }}>
                <View key={item.seriesId} style={item.seriesId === this.state.selSeriesId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.seriesId === this.state.selSeriesId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.seriesName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 型号项
    renderTypeItem = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.checkId) {
                    return;
                }
                this.setState({
                    selBrickTypeId: item.brickTypeId,
                    selBrickTypeName: item.brickTypeName,
                })
            }}>
                <View key={item.brickTypeId} style={item.brickTypeId === this.state.selBrickTypeId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.brickTypeId === this.state.selBrickTypeId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.brickTypeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    _callBackLoadBrickTypeData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                selBrickTypeName:response.data.brickTypeName,
                selBrickTypeId:response.data.brickTypeId,
            })

            let url= "/biz/inventory/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": 10000,
                "brickTypeId":response.data.brickTypeId,
                "locationAreaId":this.state.selLocationAreaId
            };
            httpPost(url, loadRequest, this.loadInventoryListCallBack);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
            this.setState({
                selBrickTypeName:'',
                selBrickTypeId:'',
            })
        }
    }
    loadInventoryListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var storageLocationDataSource = response.data.dataList;
            if (this.state.allotDetailDTOList) {
                // 减去已经暂存出库数据
                let storageLocationKey;
                let checkOutKey;
                // 数据库中查到的型号（砖型）在不同库位的库存
                storageLocationDataSource.forEach((locationStorageDetail)=>{
                    // 库位与型号组成唯一标识
                    storageLocationKey = locationStorageDetail.locationId + "_" + this.state.selBrickTypeId;
                    // 已选择的所有型号（砖型）在不同库位的出库临时数据
                    this.state.allotDetailDTOList.forEach((checkOutDetail)=>{
                        checkOutKey = checkOutDetail.locationId + "_" + checkOutDetail.brickTypeId;
                        if (storageLocationKey === checkOutKey) {
                            locationStorageDetail.storageOutAmount = parseFloat(locationStorageDetail.storageOutAmount) + parseFloat(checkOutDetail.outAmount);
                        }
                    })
                })
            }
            if(storageLocationDataSource && storageLocationDataSource.length > 0) {
                
            }
            else {
                storageLocationDataSource = [{"locationId":0,"locationName":"库存","storageInAmount":0,"storageOutAmount":0}];
            }

            this.setState({
                storageLocationDataSource:storageLocationDataSource,
            })
        }
    }

    saveAllotDetailData=()=>{
        if (!this.state.allotDetailDTOList || this.state.allotDetailDTOList.length === 0) {
            WToast.show({data:"没有可暂存的数据，请先选择"});
            return;
        }
        let requestUrl= "/biz/inventory/location/allot/add";
        let requestParams={
            "allotState":"0AA",
            "allotType":"I",
            "allotDetailDTOList":this.state.allotDetailDTOList,
        };

        if (this.state.allotId) {
            requestUrl= "/biz/inventory/location/allot/modify";
            requestParams={
                "allotState":"0AA",
                "allotType":"I",
                "allotId": this.state.allotId,
                "allotDetailDTOList":this.state.allotDetailDTOList,
            };
        }
        httpPost(requestUrl, requestParams, (response)=>{
            let toastOpts;
            switch (response.code) {
                case 200:
                    if (this.props.route.params.refresh) {
                        this.props.route.params.refresh()
                    }
                    toastOpts = getSuccessToastOpts('保存');
                    this.state.allotId = response.data.allotId
                    WToast.show(toastOpts)
                    break;
                default:
                    toastOpts = getFailToastOpts(response.message);
                    WToast.show({data:response.message})
            }
        });
    }

    // 库区
    renderLocationAreaRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    selLocationAreaId:item.locationAreaId,
                    selLocationAreaName:item.locationAreaName
                }) 
                let url= "/biz/inventory/list";
                let loadRequest={
                    "currentPage": 1,
                    "pageSize": 10000,
                    "brickTypeId":this.state.selBrickTypeId,
                    "locationAreaId":item.locationAreaId
                };
                httpPost(url, loadRequest, this.loadInventoryListCallBack);
            }}>
                <View key={item.locationAreaId} style={item.locationAreaId===this.state.selLocationAreaId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.locationAreaId===this.state.selLocationAreaId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationAreaName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 库位
    renderLocationRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selLocationId:item.locationId,
                selLocationName:item.locationName,
                selBrickTypeLocationCurrentInventory:item.storageInAmount - item.storageOutAmount
            }) }}>
                <View key={item.locationId} style={item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationName} [{double2StringFormat(item.storageInAmount - item.storageOutAmount)}]
                        {/* {item.locationName} [{item.storageInAmount - item.storageOutAmount}] */}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 新库区
    renderLocationNewAreaRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    selLocationNewAreaId:item.locationAreaId,
                    selLocationNewAreaName:item.locationAreaName
                })
                let url= "/biz/storage/location/list";
                let loadRequest={
                    "currentPage": 1,
                    "pageSize": 10000,
                    "locationAreaId":item.locationAreaId
                };
                httpPost(url, loadRequest, (response)=>{
                    if (response.code == 200 && response.data && response.data.dataList) {
                        this.setState({
                            allotInLocationIdNewDataSource:response.data.dataList
                        })
                    }
                });
            }}>
                <View key={item.locationAreaId} style={item.locationAreaId===this.state.selLocationNewAreaId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.locationAreaId===this.state.selLocationNewAreaId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationAreaName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 新库位
    renderLocationNewRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selLocationNewId:item.locationId,
                selLocationNewName:item.locationName
            }) }}>
                <View key={item.locationId} style={item.locationId===this.state.selLocationNewId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.locationId===this.state.selLocationNewId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title='新增库存盘点'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.formContentViewStyle]}>

                <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>类型</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[CommonStyle.selectViewItem, (this.state.allotType === 'reduce') ? { backgroundColor: 'red' } : ""]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    allotType: "reduce",
                                })
                            }}>
                                <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.allotType === 'reduce') ? { color: '#FFF' } : { color: '#000' }]}>调减（↓）</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={[CommonStyle.selectViewItem, (this.state.allotType === 'add') ? { backgroundColor: 'red' } : {}]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    allotType: "add",
                                })
                            }}>
                                <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.allotType === 'add') ? { color: '#FFF' } : { color: '#000' }]}>调增（↑）</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>产品分类</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[(!this.state.classifyDataSource || this.state.classifyDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (!this.state._classifyDataSource || this.state._classifyDataSource.length === 0) {
                                    this.setState({
                                        _classifyDataSource: copyArr(this.state.classifyDataSource),
                                    })
                                }
                                this.setState({
                                    classModal: true,
                                })

                                if (!this.state.selBrickClassId && this.state.classifyDataSource && this.state.classifyDataSource.length > 0) {
                                    this.setState({
                                        selBrickClassId: this.state.classifyDataSource[0].brickClassId,
                                        selBrickClassName: this.state.classifyDataSource[0].brickClassName,
                                        selSeriesId:"",
                                        selSeriesName:"",
                                        selBrickTypeId:"",
                                        selBrickTypeName:""
                                    })
                                }
                                if(this.state.classifyDataSource && this.state.classifyDataSource.length > 0){
                                    let url= "/biz/brick/class/series/list";
                                    let loadRequest={
                                        "currentPage":1,
                                        "pageSize":1000,
                                        "brickClassId":this.state.selBrickClassId?this.state.selBrickClassId:this.state.classifyDataSource[0].brickClassId,
                                    };
    
                                    httpPost(url, loadRequest, this.loadBrickSeriesListCallBack);
    
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, { height:40,flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                    <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.selBrickClassId && this.state.selBrickClassName ? (this.state.selBrickClassName) : "选择分类"}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>产品名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[(!this.state.seriesDataSource || this.state.seriesDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if(!this.state.selBrickClassId){
                                    let toastOpts = getFailToastOpts("请选择产品分类");
                                    WToast.show(toastOpts);
                                    return;
                                }
                                if (!this.state._seriesDataSource || this.state._seriesDataSource.length === 0) {
                                    this.setState({
                                        _seriesDataSource: copyArr(this.state.seriesDataSource),
                                    })
                                }
                                this.setState({
                                    seriesModal: true,
                                })

                                if (!this.state.selSeriesId && this.state.seriesDataSource && this.state.seriesDataSource.length > 0) {
                                    this.setState({
                                        selSeriesId: this.state.seriesDataSource[0].seriesId,
                                        selSeriesName: this.state.seriesDataSource[0].seriesName,
                                        selBrickTypeId: "",
                                        selBrickTypeName: "",
                                    })
                                }
                                if(this.state.seriesDataSource && this.state.seriesDataSource.length > 0){
                                    let url= "/biz/brick/series/type/list";
                                    let loadRequest={
                                        "currentPage":1,
                                        "pageSize":1000,
                                        "seriesId":this.state.selSeriesId?this.state.selSeriesId:this.state.seriesDataSource[0].seriesId,
                                    };
                                    httpPost(url, loadRequest, this.loadBrickTypeListCallBack);
                                }
                                
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, { height:40,flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                    <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.selSeriesId && this.state.selSeriesName ? (this.state.selSeriesName) : "选择产品"}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>产品型号</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[(!this.state.brickTypeDataSource || this.state.brickTypeDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if(!this.state.selSeriesId){
                                    let toastOpts = getFailToastOpts("请选择产品名称");
                                    WToast.show(toastOpts);
                                    return;
                                }
                                if (!this.state._brickTypeDataSource || this.state._brickTypeDataSource.length === 0) {
                                    this.setState({
                                        _brickTypeDataSource: copyArr(this.state.brickTypeDataSource),
                                    })
                                }
                                this.setState({
                                    brickTypeModal: true,
                                })

                                if (!this.state.selBrickTypeId && this.state.brickTypeDataSource && this.state.brickTypeDataSource.length > 0) {
                                    this.setState({
                                        selBrickTypeId: this.state.brickTypeDataSource[0].brickTypeId,
                                        selBrickTypeName: this.state.brickTypeDataSource[0].brickTypeName,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, { height:40,flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                    <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.selBrickTypeId && this.state.selBrickTypeName ? (this.state.selBrickTypeName) : "选择型号"}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.classModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.searchClassiFy();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._classifyDataSource && this.state._classifyDataSource.length > 0)
                                                ?
                                                this.state._classifyDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderClassiFyItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            classModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selBrickClassId) {
                                            let toastOpts = getFailToastOpts("您还没有选择产品分类");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            classModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.seriesModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.searchSeries();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._seriesDataSource && this.state._seriesDataSource.length > 0)
                                                ?
                                                this.state._seriesDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderSeriesItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            seriesModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selSeriesId) {
                                            let toastOpts = getFailToastOpts("您还没有选择产品");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            seriesModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.brickTypeModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.searchType();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._brickTypeDataSource && this.state._brickTypeDataSource.length > 0)
                                                ?
                                                this.state._brickTypeDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderTypeItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            brickTypeModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selBrickTypeId) {
                                            let toastOpts = getFailToastOpts("您还没有选择型号");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        let url= "/biz/inventory/list";
                                        let loadRequest={
                                            "currentPage": 1,
                                            "pageSize": 10000,
                                            "brickTypeId":this.state.selBrickTypeId,
                                            "locationAreaId":this.state.selLocationAreaId
                                        };
                                        httpPost(url, loadRequest, this.loadInventoryListCallBack);
                                        this.setState({
                                            brickTypeModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    {/* 下一步选新库位 */}
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.allotInLocationModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>

                                        <View style={styles.inputRowStyle}>
                                            <View style={styles.leftLabView}>
                                                <Text style={styles.leftLabNameTextStyle}>选择新库区</Text>
                                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                            </View>
                                        </View>
                                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                                            {
                                                (this.state.storageLocationAreaDataSource && this.state.storageLocationAreaDataSource.length > 0) 
                                                ? 
                                                this.state.storageLocationAreaDataSource.map((item, index)=>{
                                                    return this.renderLocationNewAreaRow(item)
                                                })
                                                : <EmptyRowViewComponent/> 
                                            }
                                        </View>
                        
                                        <View style={styles.inputRowStyle}>
                                            <View style={styles.leftLabView}>
                                                <Text style={styles.leftLabNameTextStyle}>选择新库位</Text>
                                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                            </View>
                                        </View>
                                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                                            {
                                                (this.state.allotInLocationIdNewDataSource && this.state.allotInLocationIdNewDataSource.length > 0) 
                                                ? 
                                                this.state.allotInLocationIdNewDataSource.map((item, index)=>{
                                                    return this.renderLocationNewRow(item)
                                                })
                                                : <EmptyRowViewComponent/> 
                                            }
                                        </View>
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            allotInLocationModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selLocationNewId) {
                                            let toastOpts = getFailToastOpts("您还没有选择新的库位");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        let requestUrl= "/biz/inventory/location/allot/confirm_finish";
                                        let requestParams={
                                            "allotId": this.state.allotId,
                                            "allotInLocationId": this.state.selLocationNewId
                                        };

                                        httpPost(requestUrl, requestParams, (response)=>{
                                            let toastOpts;
                                            switch (response.code) {
                                                case 200:
                                                    if (this.props.route.params.refresh) {
                                                        this.props.route.params.refresh()
                                                    }
                                                    toastOpts = getSuccessToastOpts('完成');
                                                    WToast.show(toastOpts);
                                                    this.props.navigation.navigate("InventoryLocationAllotList")
                                                    break;
                                                default:
                                                    toastOpts = getFailToastOpts(response.message);
                                                    WToast.show({data:response.message})
                                            }
                                        });

                                        this.setState({
                                            allotInLocationModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确认</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>选择原库区</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationAreaDataSource && this.state.storageLocationAreaDataSource.length > 0) 
                            ? 
                            this.state.storageLocationAreaDataSource.map((item, index)=>{
                                return this.renderLocationAreaRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>选择原库位</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationDataSource && this.state.storageLocationDataSource.length > 0) 
                            ? 
                            this.state.storageLocationDataSource.map((item, index)=>{
                                return this.renderLocationRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>数量</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入数量'}
                            onChangeText={(text) => this.setState({selOutAmount:text})}
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                            {this.state.selOutAmount}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>重量(吨)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入重量'}
                            onChangeText={(text) => this.setState({selOutWeight:text})}
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                            {this.state.selOutWeight}
                        </TextInput>
                    </View>
                    <View style={[styles.btnRowView, this.state.allotId ? CommonStyle.hiddenViewStyle : null]}>
                     <TouchableOpacity onPress={()=>{
                        console.log("=======this.state.allotType", this.state.allotType, this.state.allotType === '');
                        if (this.state.allotType === '') {
                            WToast.show({data:"请选择类型"});
                            return;
                        }
                        if (!this.state.selBrickTypeId) {
                            WToast.show({data:"请选择产品"});
                            return;
                        }

                        if (!this.state.selLocationAreaId || this.state.selLocationAreaId === "0") {
                            WToast.show({data:"请选择库区"});
                            return;
                        }
                        if (!this.state.selLocationId || this.state.selLocationId === "0") {
                            WToast.show({data:"请选择库位"});
                            return;
                        }
                        if (!this.state.selOutAmount || this.state.selOutAmount === "0") {
                            WToast.show({data:"请输入数量"});
                            return;
                        }
                        if (!this.state.selOutWeight || this.state.selOutWeight === "0") {
                            WToast.show({data:"请输入重量"});
                            return;
                        }
                        if (this.state.allotType === 'reduce' && this.state.selOutAmount  > this.state.selBrickTypeLocationCurrentInventory) {
                            WToast.show({data:"出库数量不能大于当前库位的库存数量[" + this.state.selBrickTypeLocationCurrentInventory + "]"});
                            return;
                        }
                        var storageOutDetailDTO = {
                            "_index":this.state.allotDetailDTOList.length,
                            "brickTypeId": this.state.selBrickTypeId,
                            "brickTypeName": this.state.selBrickTypeName,
                            "seriesId":this.state.selSeriesId,
                            "seriesName":this.state.selSeriesName,
                            "allotOutLocationId":this.state.selLocationId,
                            "allotOutLocationName":this.state.selLocationName,
                            "locationAreaId":this.state.selLocationAreaId,
                            "locationAreaName":this.state.selLocationAreaName,
                            "allotAmount": this.state.selOutAmount,
                            "allotWeight": this.state.selOutWeight,
                        }
                        if (this.state.allotType === 'add') {
                            storageOutDetailDTO = {
                                "_index":this.state.allotDetailDTOList.length,
                                "brickTypeId": this.state.selBrickTypeId,
                                "brickTypeName": this.state.selBrickTypeName,
                                "seriesId":this.state.selSeriesId,
                                "seriesName":this.state.selSeriesName,
                                "allotInLocationId":this.state.selLocationId,
                                "allotInLocationName":this.state.selLocationName,
                                "locationAreaId":this.state.selLocationAreaId,
                                "locationAreaName":this.state.selLocationAreaName,
                                "allotAmount": this.state.selOutAmount,
                                "allotWeight": this.state.selOutWeight,
                            }
                        }
                        var _allotDetailDTOList = this.state.allotDetailDTOList;
                        _allotDetailDTOList = _allotDetailDTOList.concat(storageOutDetailDTO);
                        this.setState({
                            allotDetailDTOList:_allotDetailDTOList
                        })
                        this.setState({
                            selBrickClassId:"",
                            selBrickClassName:"",
                            selSeriesId:"",
                            selSeriesName:"",
                            seriesDataSource:[],
                            _seriesDataSource:[],
                            selBrickTypeId:"",
                            selBrickTypeName:"",
                            brickTypeDataSource:[],
                            _brickTypeDataSource:[],
                            allotType:"",
                            selLocationId:"",
                            selLocationName:"",
                            selLocationAreaId:this.state.storageLocationAreaDataSource[0].locationAreaId,
                            selLocationAreaName:"",
                            // selOrderId:"",
                            selPackageAmount:"",
                            selOutAmount:"",
                            selOutWeight:"",
                            // selOrderName:"",
                            selBrickTypeLocationCurrentInventory:0,
                            storageLocationDataSource:[],
                        })
                     }}>
                         <View style={{marginRight:screenWidth/8}}>
                         {/* <View style={[styles.btnAddView]}> */}
                             {/* <Text style={styles.btnAddText}>新增</Text> */}
                             <Image style={{ width:25, height:25,justifyContent:'center'}} source={require('../../assets/icon/iconfont/add1.png')}></Image>
                        </View>
                     </TouchableOpacity>
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View>
                    <FlatList 
                    data={this.state.allotDetailDTOList}
                    renderItem={({item}) => 
                    <View key={item._index} style={styles.titleViewStyle}>
                        <View style={{ }}>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                产品：{item.seriesName}-{item.brickTypeName}
                            </Text>
                        </View>
                        <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10}]}>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>调拨数量：{item.allotWeight}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>类型：{item.allotOutLocationId != null ? "调减（↓）" : "调增（↑）"}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>库位：{item.allotOutLocationName != null ? item.allotOutLocationName : item.allotInLocationName}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>重量：{item.allotWeight}吨</Text>
                            </View>
                        </View>
                    </View>}
                    />
                    </View>

                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{flexDirection:'row',width:130,height:40,marginLeft:35,marginTop:15}]} >
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                            console.log("=========this.state.allotDetailDTOList", this.state.allotDetailDTOList);
                            if (!this.state.allotDetailDTOList || this.state.allotDetailDTOList.length === 0) {
                                WToast.show({data:"请先添加要调整的数据"});
                                return;
                            }
                            Alert.alert('确认','您确定要提交吗？',[
                                {
                                    text:"取消", onPress:()=>{
                                    WToast.show({data:'点击了取消'});
                                    }
                                },
                                {
                                    text:"确定", onPress:()=>{
                                        this.saveAllotDetailData();
                                        this.props.navigation.navigate("InventoryAllotList")
                                    }
                                }
                            ]);
                        }}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,,{flexDirection:'row',width:130,height:40,marginRight:35,marginTop:15}]}>
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}

const styles = StyleSheet.create({

    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', width:100, alignItems:'center', alignContent:'flex-end', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})