import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class NaturalGasPurchaseList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            //
            todayFlow:"",
            currentTime:"",
            gmtCreated:"",
            topBlockLayoutHeight:0,
            gmtCreated1:null,
            selectGmtCreated:null,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    initGmtCreated1=()=>{
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth());
        var currentDateMonth = ("0" + (currentDate.getMonth())).slice(-2);
        var currentDateDay = ("0" + (currentDate.getDate())).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectGmtCreated:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            gmtCreated1:_gmtCreated,
            initGmtCreated:_gmtCreated
        })
        return _gmtCreated;
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        var currentDate = this.initGmtCreated();
        this.setState({
            currentTime:currentDate
        })
        console.log(currentDate)
        var _gmtCreated = this.initGmtCreated1();
        this.loadNaturalGasPurchaseList(_gmtCreated);
    }

    _loadLastValueCallBack=(response) => {
        if (response.code == 200) {
            console.log("=========回调的值"+response.data+"=========");
            this.setState({
                todayFlow:response.data
            }
            )    
            }else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        }

    initGmtCreated=()=>{
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var currentHour = ("0" + (currentDate.getHours() + 8)).slice(-2);
        var currentMinute = ("0" + currentDate.getMinutes()).slice(-2);
        var currentSecond = ("0" + currentDate.getSeconds()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay +" "+ currentHour + ":" + currentMinute + ":" + currentSecond;
        return _gmtCreated;
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/natural/gas/flow/page/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            // "addTime": this.state.gmtCreated1 ? this.state.gmtCreated1 : null,
            "flowType":'I'
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);

        let url1= "/biz/natural/gas/flow/today";
        let loadRequest1={};
        httpPost(url1, loadRequest1, this._loadLastValueCallBack); 
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/natural/gas/flow/page/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            // "addTime": this.state.gmtCreated1 ? this.state.gmtCreated1 : null
            "flowType":'I'
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false

            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) > this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadNaturalGasPurchaseList();
    }

    loadNaturalGasPurchaseList=(_gmtCreated)=>{
        
        let url= "/biz/natural/gas/flow/page/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            // "addTime": _gmtCreated ? _gmtCreated : this.state.gmtCreated1,
            "flowType":'I'
        };
        httpPost(url, loadRequest, this.loadNaturalGasPurchaseListCallBack);
    }

    loadNaturalGasPurchaseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    openGmtCreated(){
        this.refs.SelectGmtCreated.showDate(this.state.selectGmtCreated)
    }

    callBackSelectGmtCreatedValue(value){
        console.log("==========时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectGmtCreated:value
        })
        if (this.state.selectGmtCreated && this.state.selectGmtCreated.length) {
            var _gmtCreated = "";
            var vartime;
            for(var index=0;index<this.state.selectGmtCreated.length;index++) {
                vartime = this.state.selectGmtCreated[index];
                if (index===0) {
                    _gmtCreated += vartime;
                }
                else if (index < 3){
                    _gmtCreated += "-" + vartime;
                }
                else if (index===3){
                    _gmtCreated += " " + vartime;
                }
                else {
                    _gmtCreated += ":" + vartime;
                }
            }
            this.setState({
                currentPage: 1,
                gmtCreated1:_gmtCreated
            })

            
            let url= "/biz/natural/gas/flow/page/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "addTime":_gmtCreated,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
            
        }
    }

    deleteNaturalGasPurchase=(flowId)=> {
        console.log("=======delete=flowId", flowId);
        let url= "/biz/natural/gas/flow/delete";
        let requestParams={'flowId':flowId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    // exportPdfFile=()=> {
    //     console.log("=======exportPdfFile");
    //     let url= "/biz/generate/pdf/natural_gas_flow";
    //     let requestParams={
    //         "addTime":this.state.gmtCreated1,
    //         "currentPage": 1,
    //         "pageSize": 1000,
    //     };
    //     httpPost(url, requestParams, (response)=>{
    //         if (response.code == 200 && response.data) {
    //             Clipboard.setString(response.data); 
    //             WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
    //             Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
    //                 {
    //                     text:"不打开", onPress:()=>{
    //                     WToast.show({data:'点击了不打开'});
    //                     }
    //                 },
    //                 {
    //                     text:"打开", onPress:()=>{
    //                         WToast.show({data:'点击了打开'});
    //                         // 直接打开外网链接 
    //                         Linking.openURL(response.data)
    //                     }
    //                 }
    //             ]);
    //         }
    //     });
    // }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }
    

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("NaturalGasPurchaseAdd", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row-reverse', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("NaturalGasPurchaseAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    });
                }}  >
                    <Image style={{ width:22, height:22, marginVertical: 2}} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.flowId} style={styles.innerViewStyle}>
                {
                    index == 0 ?
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>
                        :
                        <View></View>
                }
                <View style={CommonStyle.titleViewStyleSpecial}>
                    <Text style={CommonStyle.titleTextStyleSpecial}>购进日期：{item.addTime}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>购进量(m³)：{item.flowValue}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>供应商：{item.supplier}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>剩余量(m³)：{item.leftValue}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>消耗量(m³)：{item.consumeValue}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>操作人：{item.operator}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>更新时间：{item.gmtModified ? item.gmtModified : item.gmtCreated}</Text>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap',marginRight:15}]}>
                    <TouchableOpacity onPress={()=>{

                        if (dateDiffHours(this.state.currentTime, item.gmtCreated) > 24) {
                             return;
                        }
                        
                        Alert.alert('确认','您确定要删除该采购吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteNaturalGasPurchase(item.flowId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:80,flexDirection:"row"},dateDiffHours(this.state.currentTime, item.gmtCreated) > 24 ? CommonStyle.disableViewStyle : ""]}>
                        <Image  style={{width:20, height:20,marginRight:5, tintColor: 'rgba(145, 147, 152, 1)'}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={{  color: 'rgba(145, 147, 152, 1)', fontSize: 16}}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            if (dateDiffHours(this.state.currentTime, item.gmtCreated) > 24) {
                                return;
                            }
                            this.props.navigation.navigate("NaturalGasPurchaseAdd", 
                            {
                                // 传递参数
                                flowId:item.flowId,
                                shiftId:item.shiftId,
                                shiftName:item.shiftName,
                                staffName:item.staffName,
                                productionLineId:item.productionLineId,
                                flowValue:item.flowValue,
                                supplier:item.supplier,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[styles.itemEditBtnViewStyle,{width:80,flexDirection:'row'},dateDiffHours(this.state.currentTime, item.gmtCreated) > 24 ? CommonStyle.disableViewStyle : ""]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }


    render(){
        return(
            <View>
                <CommonHeadScreen title='天燃气购进'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>  */}
                        <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            ItemSeparatorComponent={this.space}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                        />
                    {/* </ScrollView> */}
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        marginBottom:10,
        // borderColor:"#F4F4F4",
        // borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    itemEditBtnViewStyle: {
        fontSize: 16,
        width: 100,
        height: 30,
        borderWidth: 1,
        borderColor: '#255BDA',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        borderRadius: 6,
        backgroundColor:'#255BDA'
    },
});