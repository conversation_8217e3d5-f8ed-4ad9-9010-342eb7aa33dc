import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,Linking,Clipboard,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');

const leftLabWidth = 180;
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class ProductSummart extends Component {
    constructor(props) {
        super(props);
        this.state = {
            productSummartTime:null,
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            productionSummaryList:[],
            materialInList:[],
            materialOutList:[],
            semiFinishedList:[],
            spEncastageRecordList:[],
            spSinteringRecordList:[],
            spProductCheckList:[],
            storageOutDetailList:[],

            materialInTotalWeight:0,
            materialInMonthTotalWeight:0,

            materialOutTotalWeight:0,
            materialOutMonthTotalWeight:0,

            dayCompleteWeight:0,
            monthCompleteWeight:0,

            dayRecordAmount:0,
            monthEncastageWeight:0,

            dayNaturalGasValue:0,
            monthNaturalGasValue:0,
            dayConsumption:0,
            monthConsumption:0,

            monthQualifiedRate:0,
            dayQualifiedWeight:0,
            yearQualifiedRate:0,
            monthQualifiedWeight:0,

            storageOutMonthTotalWeight:0,
            storageOutTotalWeight:0,
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        var _productionTime = this.initProductSummartTime();
        console.log('componentWillMount==_productionTime', _productionTime);
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }

        this.loadMaterialIn(_productionTime);
        this.loadMaterialOut(_productionTime);
        this.loadSemiFinished(_productionTime);
        this.loadEncastage(_productionTime);
        this.loadSintering(_productionTime);
        this.loadProductCheck(_productionTime);
        this.loadStorageOut(_productionTime); 

        // this.loadProductSummart(_productionTime);
    }

    loadMaterialIn=(productSummartTime)=>{
        let url= "/biz/production/summary/materialInList";
        let loadRequest={
            productSummartTime:productSummartTime
        };
        httpPost(url, loadRequest, this.loadMaterialInCallBack);

    }

    loadMaterialInCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                productionSummaryList:response.data.dataList,
                materialInList:response.data.dataList[0].materialInList,

                // 原料入库
                materialInTotalWeight:response.data.dataList[0].materialInTotalWeight,
                materialInMonthTotalWeight:response.data.dataList[0].materialInMonthTotalWeight,
            })
        }
    }

    loadMaterialOut=(productSummartTime)=>{
        let url= "/biz/production/summary/materialOutList";
        let loadRequest={
            productSummartTime:productSummartTime
        };
        httpPost(url, loadRequest, this.loadMaterialOutCallBack);

    }

    loadMaterialOutCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                materialOutList:response.data.dataList[0].materialOutList,

                // 原料出库
                materialOutTotalWeight:response.data.dataList[0].materialOutTotalWeight,
                materialOutMonthTotalWeight:response.data.dataList[0].materialOutMonthTotalWeight,
            })
        }
    }

    loadSemiFinished=(productSummartTime)=>{
        let url= "/biz/production/summary/semiFinishedList";
        let loadRequest={
            productSummartTime:productSummartTime
        };
        httpPost(url, loadRequest, this.loadSemiFinishedCallBack);

    }

    loadSemiFinishedCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                semiFinishedList:response.data.dataList[0].semiFinishedList,

                 // 半成品
                 dayCompleteWeight:response.data.dataList[0].dayCompleteWeight,
                 monthCompleteWeight:response.data.dataList[0].monthCompleteWeight, 
            })
        }
    }

    loadEncastage=(productSummartTime)=>{
        let url= "/biz/production/summary/encastageList";
        let loadRequest={
            productSummartTime:productSummartTime
        };
        httpPost(url, loadRequest, this.loadEncastageCallBack);

    }

    loadEncastageCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                spEncastageRecordList:response.data.dataList[0].spEncastageRecordList,

                //装窑
                dayRecordAmount:response.data.dataList[0].dayRecordAmount,
                monthEncastageWeight:response.data.dataList[0].monthEncastageWeight,
            })
        }
    }

    loadSintering=(productSummartTime)=>{
        let url= "/biz/production/summary/sinteringList";
        let loadRequest={
            productSummartTime:productSummartTime
        };
        httpPost(url, loadRequest, this.loadSinteringCallBack);

    }

    loadSinteringCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                spSinteringRecordList:response.data.dataList[0].spSinteringRecordList,

                //烧结
                dayNaturalGasValue:response.data.dataList[0].dayNaturalGasValue,
                monthNaturalGasValue:response.data.dataList[0].monthNaturalGasValue,
                dayConsumption:response.data.dataList[0].dayConsumption,
                monthConsumption:response.data.dataList[0].monthConsumption,
            })
        }
    }

    loadProductCheck=(productSummartTime)=>{
        let url= "/biz/production/summary/productCheckList";
        let loadRequest={
            productSummartTime:productSummartTime
        };
        httpPost(url, loadRequest, this.loadProductCheckCallBack);

    }

    loadProductCheckCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                spProductCheckList:response.data.dataList[0].spProductCheckList,

                 //成品检选
                monthQualifiedRate:response.data.dataList[0].monthQualifiedRate * 100,
                dayQualifiedWeight:response.data.dataList[0].dayQualifiedWeight,
                yearQualifiedRate:response.data.dataList[0].yearQualifiedRate * 100,
                monthQualifiedWeight:response.data.dataList[0].monthQualifiedWeight,
            })
        }
    }

    loadStorageOut=(productSummartTime)=>{
        let url= "/biz/production/summary/storageOutList";
        let loadRequest={
            productSummartTime:productSummartTime
        };
        httpPost(url, loadRequest, this.loadStorageOutCallBack);

    }

    loadStorageOutCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                storageOutDetailList:response.data.dataList[0].storageOutDetailList,

                 //成品出库
                storageOutMonthTotalWeight:response.data.dataList[0].storageOutMonthTotalWeight,
                storageOutTotalWeight:response.data.dataList[0].storageOutTotalWeight,
            })
        }
    }

    loadProductSummart=(productSummartTime)=>{
        let url= "/biz/production/summary/list";
        let loadRequest={
            productSummartTime:productSummartTime
        };
        httpPost(url, loadRequest, this.loadProductSummartCallBack);

    }

    loadProductSummartCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // console.log("======" + response.data.dataList[0].materialInTotalWeight);
            this.setState({
                productionSummaryList:response.data.dataList,
                materialInList:response.data.dataList[0].materialInList,
                materialOutList:response.data.dataList[0].materialOutList,
                semiFinishedList:response.data.dataList[0].semiFinishedList,
                spEncastageRecordList:response.data.dataList[0].spEncastageRecordList,
                spSinteringRecordList:response.data.dataList[0].spSinteringRecordList,
                spProductCheckList:response.data.dataList[0].spProductCheckList,
                storageOutDetailList:response.data.dataList[0].storageOutDetailList,

                // 原料入库
                materialInTotalWeight:response.data.dataList[0].materialInTotalWeight,
                materialInMonthTotalWeight:response.data.dataList[0].materialInMonthTotalWeight,
                
                // 原料出库
                materialOutTotalWeight:response.data.dataList[0].materialOutTotalWeight,
                materialOutMonthTotalWeight:response.data.dataList[0].materialOutMonthTotalWeight,

                // 半成品
                dayCompleteWeight:response.data.dataList[0].dayCompleteWeight,
                monthCompleteWeight:response.data.dataList[0].monthCompleteWeight,

                //装窑
                dayRecordAmount:response.data.dataList[0].dayRecordAmount,
                monthEncastageWeight:response.data.dataList[0].monthEncastageWeight,

                //烧结
                dayNaturalGasValue:response.data.dataList[0].dayNaturalGasValue,
                monthNaturalGasValue:response.data.dataList[0].monthNaturalGasValue,
                dayConsumption:response.data.dataList[0].dayConsumption,
                monthConsumption:response.data.dataList[0].monthConsumption,


                //成品检选
                monthQualifiedRate:response.data.dataList[0].monthQualifiedRate * 100,
                dayQualifiedWeight:response.data.dataList[0].dayQualifiedWeight,
                yearQualifiedRate:response.data.dataList[0].yearQualifiedRate * 100,
                monthQualifiedWeight:response.data.dataList[0].monthQualifiedWeight,

                //成品出库
                storageOutMonthTotalWeight:response.data.dataList[0].storageOutMonthTotalWeight,
                storageOutTotalWeight:response.data.dataList[0].storageOutTotalWeight,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => {
                    Alert.alert('确认', '您确定要导出PDF文件吗？', [
                        {
                            text: "取消", onPress: () => {
                                WToast.show({ data: '点击了取消' });
                            }
                        },
                        {
                            text: "确定", onPress: () => {
                                WToast.show({ data: '点击了确定' });
                                this.exportPdfFile()
                            }
                        }
                    ]);
                }} style={[{ marginBottom: 1.5 }]}>
                    <Image style={{ width: 23, height: 23 }} source={require('../../assets/icon/iconfont/outputBlack.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }

    changeTwoDecimal=(x)=>{
        var f_x = parseFloat(x);
        if (isNaN(f_x))
        {
            alert('function:changeTwoDecimal->parameter error');
            return false;
        }
        f_x = Math.round(f_x *100)/100;

        return f_x;
    }

    exportPdfFile=()=> {
        console.log("=======exportPdfFile");
        let url= "/biz/generate/pdf/product_summary_list";
        let requestParams={
            "productSummartTime": this.state.productSummartTime,
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data); 
                WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
                Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                    {
                        text:"不打开", onPress:()=>{
                        WToast.show({data:'点击了不打开'});
                        }
                    },
                    {
                        text:"打开", onPress:()=>{
                            WToast.show({data:'点击了打开'});
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }

    initProductSummartTime=()=>{
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth());
        var currentDateMonth = ("0" + (currentDate.getMonth() )).slice(-2);
        var currentDateDay = ("0" + (currentDate.getDate()-1)).slice(-2);

        if ((currentDate.getDate() - 1) == 0) {
            currentDate.setMonth(currentDate.getMonth() -1);
            var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
            currentDate.setMonth(currentDate.getMonth() +1 , 0);
            var lastDay = currentDate.getDate();
            var currentDateDay = ("0" + (lastDay)).slice(-2);
        }

        var _productSummartTime = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectProductSummartTime:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            productSummartTime:_productSummartTime
        })
        return _productSummartTime;
    }

    openProductSummartTime(){
        this.refs.SelectProductSummartTime.showDate(this.state.selectProductSummartTime)
    }
    callBackSelectProductSummartTimeValue(value){
        console.log("==========生产汇总时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectProductSummartTime:value
        })
        if (this.state.selectProductSummartTime && this.state.selectProductSummartTime.length) {
            var productSummartTime = "";
            var vartime;
            for(var index=0;index<this.state.selectProductSummartTime.length;index++) {
                vartime = this.state.selectProductSummartTime[index];
                if (index===0) {
                    productSummartTime += vartime;
                }
                else if (index < 3){
                    productSummartTime += "-" + vartime;
                }
                else if (index===3){
                    productSummartTime += " " + vartime;
                }
                else {
                    productSummartTime += ":" + vartime;
                }
            }
            this.setState({
                productSummartTime:productSummartTime
            })

            this.loadMaterialIn(productSummartTime);
            this.loadMaterialOut(productSummartTime);
            this.loadSemiFinished(productSummartTime);
            this.loadEncastage(productSummartTime);
            this.loadSintering(productSummartTime);
            this.loadProductCheck(productSummartTime);
            this.loadStorageOut(productSummartTime); 
        }
    }
    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            
            this.setState({
                productionSummaryList:response.data.dataList,
                materialInList:response.data.dataList[0].materialInList,
                materialOutList:response.data.dataList[0].materialOutList,
                semiFinishedList:response.data.dataList[0].semiFinishedList,
                spEncastageRecordList:response.data.dataList[0].spEncastageRecordList,
                spSinteringRecordList:response.data.dataList[0].spSinteringRecordList,
                spProductCheckList:response.data.dataList[0].spProductCheckList,
                storageOutDetailList:response.data.dataList[0].storageOutDetailList,

                // 原料入库
                materialInTotalWeight:response.data.dataList[0].materialInTotalWeight,
                materialInMonthTotalWeight:response.data.dataList[0].materialInMonthTotalWeight,
                
                // 原料出库
                materialOutTotalWeight:response.data.dataList[0].materialOutTotalWeight,
                materialOutMonthTotalWeight:response.data.dataList[0].materialOutMonthTotalWeight,

                // 半成品
                dayCompleteWeight:response.data.dataList[0].dayCompleteWeight,
                monthCompleteWeight:response.data.dataList[0].monthCompleteWeight,

                //装窑
                dayRecordAmount:response.data.dataList[0].dayRecordAmount,
                monthEncastageWeight:response.data.dataList[0].monthEncastageWeight,

                //烧结
                dayNaturalGasValue:response.data.dataList[0].dayNaturalGasValue,
                monthNaturalGasValue:response.data.dataList[0].monthNaturalGasValue,
                dayConsumption:response.data.dataList[0].dayConsumption,
                monthConsumption:response.data.dataList[0].monthConsumption,

                //成品检选
                monthQualifiedRate:response.data.dataList[0].monthQualifiedRate * 100,
                dayQualifiedWeight:response.data.dataList[0].dayQualifiedWeight,
                yearQualifiedRate:response.data.dataList[0].yearQualifiedRate * 100,
                monthQualifiedWeight:response.data.dataList[0].monthQualifiedWeight,

                //成品出库
                storageOutMonthTotalWeight:response.data.dataList[0].storageOutMonthTotalWeight,
                storageOutTotalWeight:response.data.dataList[0].storageOutTotalWeight,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='生产汇总'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.rightTop50FloatingBlockView,this.state.productSummartTime ? 
                    {
                        height: 32,
                        width: 110,
                        opacity: 0.6,
                        borderRadius: 8,
                        backgroundColor: "rgba(242, 245, 252, 1)"
                        } : {}]}>
                    <TouchableOpacity onPress={()=>this.openProductSummartTime()}>
                        <Text style={{ color: 'rgba(0,10,32,0.85)', fontSize: 14 }}>
                        {!this.state.productSummartTime ? "时间" : this.state.productSummartTime}
                        </Text>
                    </TouchableOpacity>
                </View>
                {/* <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {
                    width: 70, backgroundColor: "#F2C16D", flexDirection: "row"
                    ,top:screenHeight/9.5, zIndex: 100, position: 'absolute', right: 15, opacity: 0.6, alignItems: 'center', justifyContent: 'center'
                }]}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要导出PDF文件吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.exportPdfFile()
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 70, backgroundColor: "#F2C16D", flexDirection: "row" }]}>
                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/output.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>导出</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}

                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>原料车间</Text>
                </View>

                {/* 原料入库 */}
                {/* <View style={styles.inputRowStyle}> */}
                    {/* <View style={styles.leftLabView}>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16}]}>今日合计入库:{this.state.materialInTotalWeight}吨</Text>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16}]}>本月累计入库:{this.state.materialInMonthTotalWeight}吨</Text>
                    </View> */}

                <View style={[styles.itemContentViewStyle,{  }]}>
                    <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                        
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>今日入库:{this.state.materialInTotalWeight ? this.changeTwoDecimal(this.state.materialInTotalWeight) : this.state.materialInTotalWeight}吨</Text>
                    </View>
                    <View style={styles.itemContentRightChildViewStyle}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>本月入库:{this.state.materialInMonthTotalWeight ? this.changeTwoDecimal(this.state.materialInMonthTotalWeight) : this.state.materialInMonthTotalWeight}吨</Text>
                    </View>
                </View>
                
                    {/* <View style={styles.leftLabView}>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16}]}>今日合计入库:{this.state.materialInTotalWeight}吨</Text>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16,paddingLeft:7}]}>本月累计入库:{this.state.materialInMonthTotalWeight}吨</Text>
                    </View> */}
                {/* </View> */}

                <View>
                    {
                    (this.state.materialInList && this.state.materialInList.length > 0) 
                    ? 
                    this.state.materialInList.map((item, index)=>{
                        return(
                            <View key={item.inventoryId} style={styles._innerViewStyle}>
                                
                                <View style={styles._titleViewStyle}>
                                    <Text style={styles._titleTextStyle}>{item.parentClassifyName}入库{item.totalWeight ? this.changeTwoDecimal(item.totalWeight) : item.totalWeight}吨</Text>
                                </View>
                            </View>
                        )                           
                })
                    : <EmptyRowViewComponent/> 
                    }
                </View>
                <View style={[CommonStyle.lineBorderBottomStyle,{ marginLeft:0}]} />

                {/* 原料出库 */}
                {/* <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16}]}>今日合计出库:{this.state.materialOutTotalWeight}吨</Text>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16,paddingLeft:7}]}>本月累计出库:{this.state.materialOutMonthTotalWeight}吨</Text>
                    </View>
                </View> */}

                <View style={[styles.itemContentViewStyle,{  }]}>
                    <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                        
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>今日出库:{this.state.materialOutTotalWeight ? this.changeTwoDecimal(this.state.materialOutTotalWeight) : this.state.materialOutTotalWeight}吨</Text>
                    </View>
                    <View style={styles.itemContentRightChildViewStyle}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>本月出库:{this.state.materialOutMonthTotalWeight ? this.changeTwoDecimal(this.state.materialOutMonthTotalWeight) : this.state.materialOutMonthTotalWeight}吨</Text>
                    </View>
                </View>

                <View>
                    {
                    (this.state.materialOutList && this.state.materialOutList.length > 0) 
                    ? 
                    this.state.materialOutList.map((item, index)=>{
                        return(
                            <View key={item.inventoryId} style={styles._innerViewStyle}>
                                
                                <View style={styles._titleViewStyle}>
                                    <Text style={styles._titleTextStyle}>{item.parentClassifyName}出库{item.totalWeight ? this.changeTwoDecimal(item.totalWeight) : item.totalWeight}吨</Text>
                                </View>
                            </View>
                        )                           
                })
                    : <EmptyRowViewComponent/> 
                    }
                </View>


                
                <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>成型车间</Text>
                </View>

                {/* 半成品 */}
                {/* <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16}]}>今日合计产量:{this.state.dayCompleteWeight}吨</Text>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16,paddingLeft:7}]}>本月累计产量:{this.state.monthCompleteWeight ? this.changeTwoDecimal(this.state.monthCompleteWeight) : this.state.monthCompleteWeight}吨</Text>
                    </View>
                </View> */}

                <View style={[styles.itemContentViewStyle,{  }]}>
                    <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                        
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>今日产量:{this.state.dayCompleteWeight ? this.changeTwoDecimal(this.state.dayCompleteWeight) : this.state.dayCompleteWeight}吨</Text>
                    </View>
                    <View style={styles.itemContentRightChildViewStyle}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>本月产量:{this.state.monthCompleteWeight ? this.changeTwoDecimal(this.state.monthCompleteWeight) : this.state.monthCompleteWeight}吨</Text>
                    </View>
                </View>

                <View>
                    {
                    (this.state.semiFinishedList && this.state.semiFinishedList.length > 0) 
                    ? 
                    this.state.semiFinishedList.map((item, index)=>{
                        return(
                            <View key={item.inventoryId} style={styles._innerViewStyle}>
                                {
                                    item.productionLineName ?
                                    <View style={styles._titleViewStyle}>
                                        <Text style={styles._titleTextStyle}>{item.productionLineName}日产量{item.completeWeight ? this.changeTwoDecimal(item.completeWeight) : item.completeWeight}吨</Text>
                                    </View> :
                                    <View/>
                                }
                                
                            </View>
                        )                           
                })
                    : <EmptyRowViewComponent/> 
                    }
                </View>


                <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>隧道窑车间</Text>
                </View>

                {/* 装窑 */}
                {/* <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16}]}>今日合计装车:{this.state.dayRecordAmount}辆</Text>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16,paddingLeft:7}]}>本月累计装车:{this.state.monthEncastageWeight ? this.changeTwoDecimal(this.state.monthEncastageWeight) : this.state.monthEncastageWeight}吨</Text>
                    </View>
                </View> */}

                <View style={[styles.itemContentViewStyle,{  }]}>
                    <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                        
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>今日装车:{this.state.dayRecordAmount}辆</Text>
                    </View>
                    <View style={styles.itemContentRightChildViewStyle}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>本月装车:{this.state.monthEncastageWeight ? this.changeTwoDecimal(this.state.monthEncastageWeight) : this.state.monthEncastageWeight}吨</Text>
                    </View>
                </View>

                <View>
                    {
                    (this.state.spEncastageRecordList && this.state.spEncastageRecordList.length > 0) 
                    ? 
                    this.state.spEncastageRecordList.map((item, index)=>{
                        return(
                            <View key={item.inventoryId} style={styles._innerViewStyle}>
                                {
                                    item.kilnRoadName ?
                                    <View style={styles._titleViewStyle}>
                                        <Text style={styles._titleTextStyle}>{item.kilnRoadName}装车{item.recordAmount}辆{item.recordWeight ? this.changeTwoDecimal(item.recordWeight) : item.recordWeight}吨</Text>
                                    </View>
                                    : <View/>
                                }

                            </View>
                        )                           
                })
                    : <EmptyRowViewComponent/> 
                    }
                </View>



                <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>烧成车间</Text>
                </View>

                {/* 烧结 */}
                {/* <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16}]}>今日合计用气量:{this.state.dayNaturalGasValue}方</Text>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16,paddingLeft:7}]}>本月累计用气量:{this.state.monthNaturalGasValue}方</Text>
                    </View>  
                </View> */}

                <View style={[styles.itemContentViewStyle,{  }]}>
                    <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                        
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>今日用气量:{this.state.dayNaturalGasValue ? this.changeTwoDecimal(this.state.dayNaturalGasValue) : this.state.dayNaturalGasValue}方</Text>
                    </View>
                    <View style={styles.itemContentRightChildViewStyle}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>本月用气量:{this.state.monthNaturalGasValue ? this.changeTwoDecimal(this.state.monthNaturalGasValue) : this.state.monthNaturalGasValue}方</Text>
                    </View>
                </View>

                {/* <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16}]}>今日合计吨耗:{this.state.dayConsumption ? this.changeTwoDecimal(this.state.dayConsumption) : this.state.dayConsumption}方/吨</Text>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16,paddingLeft:7}]}>本月累计吨耗:{this.state.monthConsumption ? this.changeTwoDecimal(this.state.monthConsumption) : this.state.monthConsumption}方/吨</Text>
                    </View>
                </View> */}

                <View style={[styles.itemContentViewStyle,{  }]}>
                    <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                        
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>今日吨耗:{this.state.dayConsumption ? this.changeTwoDecimal(this.state.dayConsumption) : this.state.dayConsumption}方/吨</Text>
                    </View>
                    <View style={styles.itemContentRightChildViewStyle}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>本月吨耗:{this.state.monthConsumption ? this.changeTwoDecimal(this.state.monthConsumption) : this.state.monthConsumption}方/吨</Text>
                    </View>
                </View>

                <View>
                    {
                    (this.state.spSinteringRecordList && this.state.spSinteringRecordList.length > 0) 
                    ? 
                    this.state.spSinteringRecordList.map((item, index)=>{
                        return(
                            <View key={item.inventoryId} style={styles._innerViewStyle}>

                                {/* <View style={styles.leftLabView}>
                                    <Text style={[styles._titleTextStyle]}>{item.kilnRoadName}入窑:{this.state.inCarAmount}辆</Text>
                                    <Text style={[styles._titleTextStyle,{paddingLeft:7}]}>{item.kilnRoadName}出窑:{this.state.outCarAmount}辆</Text>
                                </View> */}
                                {
                                    item.kilnRoadName ?

                                    <View>
                                        <View style={[styles.itemContentViewStyle,{  }]}>
                                            <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                                <Text style={styles.itemContentChildTextStyle}>{item.kilnRoadName}入窑:{item.inCarAmount ? item.inCarAmount : 0}辆</Text>
                                            </View>
                                            <View style={styles.itemContentRightChildViewStyle}>
                                                <Text style={styles.itemContentChildTextStyle}>{item.kilnRoadName}出窑:{item.outCarAmount ? item.outCarAmount : 0}辆</Text>
                                            </View>
                                        </View>

                                        <View style={[styles.itemContentViewStyle,{  }]}>
                                            <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                            <Text style={styles._titleTextStyle}>{item.kilnRoadName}今日总用气量{item.naturalGasValue ? this.changeTwoDecimal(item.naturalGasValue) : item.naturalGasValue}方</Text>
                                            </View>
                                            <View style={styles.itemContentRightChildViewStyle}>
                                                <Text style={styles.itemContentChildTextStyle}>{item.kilnRoadName}窑速:{item.kilnSpeed ? item.kilnSpeed : 0}分钟/车</Text>
                                            </View>
                                        </View>

                                        {/* <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>{item.kilnRoadName}今日总用气量{item.naturalGasValue ? this.changeTwoDecimal(item.naturalGasValue) : item.naturalGasValue}方</Text>
                                        </View> */}

                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>当日吨耗:{item.consumption ? this.changeTwoDecimal(item.consumption) : item.consumption}方/吨</Text>
                                        </View>
                                    </View>
                                    
                                    : <View/>
                                }

                                
                               

                            </View>
                        )                           
                })
                    : <EmptyRowViewComponent/> 
                    }
                </View>




                <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>成品车间</Text>
                </View>

                {/* 成品检选 */}
                {/* <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16}]}>今日正品合计:{this.state.dayQualifiedWeight ? this.changeTwoDecimal(this.state.dayQualifiedWeight) : this.state.dayQualifiedWeight}吨</Text>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16,paddingLeft:7}]}>本月累计合格率:{this.state.monthQualifiedRate ? this.changeTwoDecimal(this.state.monthQualifiedRate) : this.state.monthQualifiedRate}%</Text>
                    </View>
                </View> */}

                <View style={[styles.itemContentViewStyle,{  }]}>
                    <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>本月正品:{this.state.monthQualifiedWeight ? this.changeTwoDecimal(this.state.monthQualifiedWeight) : this.state.monthQualifiedWeight}吨</Text>
                    </View>
                    <View style={styles.itemContentRightChildViewStyle}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>年合格率:{this.state.yearQualifiedRate ? this.changeTwoDecimal(this.state.yearQualifiedRate) : this.state.yearQualifiedRate}%</Text>
                    </View>
                </View>

                <View style={[styles.itemContentViewStyle,{  }]}>
                    <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>今日正品:{this.state.dayQualifiedWeight ? this.changeTwoDecimal(this.state.dayQualifiedWeight) : this.state.dayQualifiedWeight}吨</Text>
                    </View>
                    <View style={styles.itemContentRightChildViewStyle}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>本月合格率:{this.state.monthQualifiedRate ? this.changeTwoDecimal(this.state.monthQualifiedRate) : this.state.monthQualifiedRate}%</Text>
                    </View>
                </View>

                <View>
                    {
                    (this.state.spProductCheckList && this.state.spProductCheckList.length > 0) 
                    ? 
                    this.state.spProductCheckList.map((item, index)=>{
                        return(
                            <View key={item.inventoryId} style={styles._innerViewStyle}>

                                {/* <View style={styles.leftLabView}>
                                    <Text style={[styles._titleTextStyle]}>{item.productionLineName}正品吨数:{item.productCheckTotalWeight ? this.changeTwoDecimal(item.productCheckTotalWeight) : item.productCheckTotalWeight}</Text>
                                    <Text style={[styles._titleTextStyle,{paddingLeft:7}]}>当日合格率:{item.qualifiedRate ? this.changeTwoDecimal(item.qualifiedRate) : item.qualifiedRate}%</Text>
                                </View> */}
                                {
                                    item.productionLineName ?
                                    <View style={[styles.itemContentViewStyle,{  }]}>
                                        <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                            <Text style={styles.itemContentChildTextStyle}>{item.productionLineName}正品吨数:{item.productCheckTotalWeight ? this.changeTwoDecimal(item.productCheckTotalWeight) : item.productCheckTotalWeight}</Text>
                                        </View>
                                        <View style={styles.itemContentRightChildViewStyle}>
                                            <Text style={styles.itemContentChildTextStyle}>当日合格率:{(item.qualifiedRate * 100) ? this.changeTwoDecimal(item.qualifiedRate * 100) : (item.qualifiedRate * 100)}%</Text>
                                        </View>
                                    </View>
                                    : <View/>

                                }

                            </View>
                        )                           
                })
                    : <EmptyRowViewComponent/> 
                    }
                </View>



                <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>发运车间</Text>
                </View>

                {/* 装车发运 */}
                {/* <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16}]}>今日发货吨数：:{this.state.storageOutTotalWeight ? this.changeTwoDecimal(this.state.storageOutTotalWeight) : this.state.storageOutTotalWeight}吨</Text>
                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:16,paddingLeft:7}]}>本月累计发货吨数:{this.state.storageOutMonthTotalWeight ? this.changeTwoDecimal(this.state.storageOutMonthTotalWeight) : this.state.storageOutMonthTotalWeight}吨</Text>
                    </View>
                </View> */}

                <View style={[styles.itemContentViewStyle,{  }]}>
                    <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                        
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>今日发货:{this.state.storageOutTotalWeight ? this.changeTwoDecimal(this.state.storageOutTotalWeight) : this.state.storageOutTotalWeight}吨</Text>
                    </View>
                    <View style={styles.itemContentRightChildViewStyle}>
                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:16}]}>本月发货:{this.state.storageOutMonthTotalWeight ? this.changeTwoDecimal(this.state.storageOutMonthTotalWeight) : this.state.storageOutMonthTotalWeight}吨</Text>
                    </View>
                </View>

                <View>
                    {
                    (this.state.storageOutDetailList && this.state.storageOutDetailList.length > 0) 
                    ? 
                    this.state.storageOutDetailList.map((item, index)=>{
                        return(
                            <View key={item.inventoryId} style={styles._innerViewStyle}>
                                {
                                    item.customerName ?
                                    <View style={styles._titleViewStyle}>
                                        <Text style={styles._titleTextStyle}>{item.customerName}合计发货{item.totalWeight ? this.changeTwoDecimal(item.totalWeight) : item.totalWeight}吨</Text>
                                    </View>
                                    : <View/>
                                }
                             
                            </View>
                        )                           
                })
                    : <EmptyRowViewComponent/> 
                    }
                </View>

                </ScrollView>
                <BottomScrollSelect 
                    ref={'SelectProductSummartTime'} 
                    callBackDateValue={this.callBackSelectProductSummartTimeValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({

    addItemSplitRowView:{
        height:50, 
        backgroundColor:'#F6F9FA', 
        justifyContent:'center',
        marginTop:10
    },
    addItemSplitRowText:{
        color:'#999999', 
        fontSize:20, 
        paddingLeft:10
    },
    _titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    _titleTextStyle:{
        fontSize:16
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginTop:10
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:screenWidth * 0.4,
    },
    itemContentRightChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:screenWidth * 0.5,
    },
    itemContentChildTextStyle:{
        // marginLeft:10,
        marginBottom:10,
        fontSize:16
    },

});