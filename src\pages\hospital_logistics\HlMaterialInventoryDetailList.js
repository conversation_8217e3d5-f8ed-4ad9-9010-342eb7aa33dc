import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Clipboard,Linking,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class HlMaterialInventoryDetailList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            initGmtCreated:null,
            gmtCreated:null,
            selectGmtCreated:null,
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:6,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            brickTypeDataSource:[],
            materialId:"",
            materialName:"",
            unitName:"",
            materialNameLayoutHeight:0,
            materialPrice:"",
            materialModal:"",
            storageOutFlag:"",
            departmentId:null,
            departmentName:""
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        const { route, navigation } = this.props;
        if (route && route.params) {
            const {materialId, materialName, unitName, materialPrice, materialModal, storageOutFlag, departmentId,departmentName} = route.params;
            if (materialId && storageOutFlag) {
                this.setState({
                    materialId:materialId,
                    storageOutFlag:storageOutFlag
                })
                if(departmentId){
                    this.setState({
                        departmentId:departmentId
                    })
                    this.loadInventoryDetailList(materialId, storageOutFlag,departmentId);
                    
                }
                else{
                    this.loadInventoryDetailList(materialId, storageOutFlag);
                }
                
            }
            if (materialName) {
                this.setState({
                    materialName:materialName
                })
                
            }
            if (unitName) {
                this.setState({
                    unitName:unitName
                })
            }
            if (materialPrice) {
                this.setState({
                    materialPrice:materialPrice
                })
            }
            if (materialModal) {
                this.setState({
                    materialModal:materialModal
                })
            }
            if (departmentName) {
                this.setState({
                    departmentName:departmentName
                })
            }
        }

    }

    // loadDepartmentName=(departmentId)=>{
    //     let loadTypeUrl= "/biz/hl/department/get";
    //     let loadRequest={'departmentId':departmentId};
    //     httpPost(loadTypeUrl, loadRequest, this._loadDepartmentCallBack);
    // }

    // _loadDepartmentCallBack=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         console.log(response.data);
    //         this.setState({
    //             departmentName:response.data.departmentName,
    //         })
    //     }
    // }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/hl/material/inventory/detailList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "materialId":materialId ? materialId : this.state.materialId,
            "storageOutFlag": this.state.storageOutFlag,
            "departmentId":this.state.departmentId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.brickTypeId == null && this.state.gmtCreated === this.state.initGmtCreated) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/hl/material/inventory/detailList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "materialId":materialId ? materialId : this.state.materialId,
            "storageOutFlag": this.state.storageOutFlag,
            "departmentId":this.state.departmentId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========第一页即是最后一页，不加载=====");
            return;
        }
        this.loadInventoryDetailList();
    }

    loadInventoryDetailList=(materialId,storageOutFlag,departmentId)=>{
        let url= "/biz/hl/material/inventory/detailList";
        let loadRequest={
            "materialId":materialId ? materialId : this.state.materialId,
            "storageOutFlag":storageOutFlag ?storageOutFlag : this.state.storageOutFlag,
            "departmentId":departmentId?departmentId:this.state.departmentId
        };
        httpPost(url, loadRequest, this.loadInventoryDetailListCallBack);
    }

    loadInventoryDetailListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }


    renderRow=(item, index)=>{
        return (
            <View key={item.storageInId} style={[styles.innerViewStyle,item.storageInAmount ? {backgroundColor:'rgba(255,0,0,0.2)'} : {backgroundColor:'rgba(0,255,0,0.2)'}]}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>库存数量：{item.beforeInventoryAmount}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.storageInAmount ? "入库数量" : "出库数量"}：{item.storageInAmount ? item.storageInAmount : item.storageOutAmount}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>结余库存：{item.afterInventoryAmount}</Text>
                </View>
                {
                    item.bookKeeper ? 
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>记账人：{item.bookKeeper}</Text>
                    </View> 
                    : 
                    <View/>
                }
                {
                    item.recipient ? 
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>领用人：{item.recipient}</Text>
                    </View> 
                    : 
                    <View/>
                }
                {
                    item.carrier ? 
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>运送人：{item.carrier}</Text>
                    </View> 
                    : 
                    <View/>
                }                
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>经办人：{item.operator}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>更新时间：{item.gmtCreated}</Text>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} >
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View>
               
            </View>
        )
    }


    

    materialNameLayout=(event)=> {
        this.setState({
            materialNameLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='库存明细'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                 <View style={[styles.innerViewStyle,{marginTop:0, index:1000}]} onLayout={this.materialNameLayout.bind(this)}>
                    {
                        this.state.departmentName ? 
                            <Text style={[styles.titleTextStyle,{marginLeft:10, marginRight:100}]}>科室名称：{this.state.departmentName}</Text>
                        : 
                        <View/>
                    }
                     <Text style={[styles.titleTextStyle,{marginLeft:10, marginRight:100}]}>物资名称：{this.state.materialName}</Text>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, marginRight:100}]}>规格型号：{this.state.materialModal ? this.state.materialModal : "-"}  </Text>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, marginRight:100}]}>单价：{this.state.materialPrice?this.state.materialPrice:"-"}            单位：{this.state.unitName ? this.state.unitName : "-"}</Text>

                    <View style={{position:'absolute', backgroundColor:'rgba(255,0,0,0.2)', right:50, top:-5, height:30, padding:5}}>
                         <Text>入库</Text>
                     </View>
                     <View style={{position:'absolute', backgroundColor:'rgba(0,255,0,0.2)', right:10, top:-5, height:30, padding:5}}>
                         <Text>出库</Text>
                     </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.materialNameLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});