import React,{Component} from 'react';
import {
    Alert,
    View, 
    ScrollView, 
    Text, 
    TextInput, 
    StyleSheet, 
    FlatList ,
    TouchableOpacity,
    Dimensions
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

const leftLabWidth = 130;
class CheckOutAdd extends Component{

    constructor(props) {
        super(props);
        this.state ={
            // 客户数据源、运送人数据源、订单数据源
            customerDataSource:[],
            consigneeDataSource:[],
            orderDataSource:[],

            // 底部滚动选择返回结果
            selectCustomer:[],
            selectCheckOutTime:[],

            selOrderList:[],

            // 运送人选择信息
            selConsigneeId:0,
            selConsigneeName:'',

            customerId:"",
            customerName:"",
            checkOutTime:'',
            driverName: "",
            driverTel:"",
            licensePlate:"",

            orderList:{},
            _orderList:{},
            _checkOutOrderList:[],
            brickTypeList:[]
        }
    }

    UNSAFE_componentWillMount(){
        console.log('=aaaa=UNSAFE_componentWillMount==');

        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectCheckOutTime:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            checkOutTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })
        
        // 加载客户
        this.loadCustomerData();

        // 加载领料人
        this.loadConsigneeList();

    }

    loadCustomerData=()=>{
        let loadUrl= "/biz/tenant/customer/list";
        let loadRequest={'currentPage':1,'pageSize':100};
        httpPost(loadUrl, loadRequest, this.callBackLoadCustomerData);
    }

    callBackLoadCustomerData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                customerDataSource:response.data.dataList
            })
            // 客户给默认值
            // if (response.data.dataList.length>0) {
            //     var varCustomer= response.data.dataList[0];
            //     this.setState({
            //         customerId:varCustomer.customerId,
            //         customerName:varCustomer.customerName,
            //     })
            // }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    
    // 加载领料人
    loadConsigneeList=()=>{
        let loadUrl= "/biz/consignee/list";
        let loadRequest={'currentPage':1,'pageSize':100};
        httpPost(loadUrl, loadRequest, this.callBackLoadConsigneeList);
    }

    callBackLoadConsigneeList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                consigneeDataSource:response.data.dataList,
                selConsigneeId:response.data.dataList[0] ? response.data.dataList[0].consigneeId : 0,
                selConsigneeName:response.data.dataList[0] ? response.data.dataList[0].consigneeName : '',
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 加载订单
    loadOrderData=(varCustomerId)=>{
        let loadUrl= "/biz/order/list";
        let loadRequest={
            "currentPage":1,
            "pageSize":100,
            "customerId":varCustomerId,
            "display":"Y",
            "excludeOrderStateList":[
                "A","K"
            ], 
            // 返回的订单需要统计库存
            "brickTypeInventory":"YES"
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadOrderData);
    }

    callBackLoadOrderData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                orderDataSource:response.data.dataList
            })
            if (response.data.dataList.length>0) {
                var varOrder={
                    index:0,
                    orderId:response.data.dataList[0].orderId,
                    orderName:response.data.dataList[0].orderName,
                    brickTypeName:response.data.dataList[0].brickTypeName,
                    // 订单订购的砖库存
                    brickTypeStaticSum:response.data.dataList[0].brickTypeStaticSum,
                    thisTimeCheckOutAmount:0
                    // thisTimeCheckOutAmount:(response.data.dataList[0].brickAmount - response.data.dataList[0].orderCheckOutAmount)
                };
                this.setState({
                    selOrderList:this.state.selOrderList.concat(varOrder)
                })
            }
            else{
                WToast.show({data:"该客户没有要出库的订单，请重新选择"});
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("CheckOutList") 
            }}>
                <Text style={CommonStyle.headRightText}>出库管理</Text>
            </TouchableOpacity>
        )
    }

    // 领料人
    renderConsigneeView=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selConsigneeId:item.consigneeId,
                    selConsigneeName:item.consigneeName
                })
            }}>
                <View key={item.consigneeId} style={item.consigneeId===this.state.selConsigneeId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.consigneeId===this.state.selConsigneeId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.consigneeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 订单单项渲染
    renderOrderItem=(item, idx)=>{
        idx --;
        console.log("=====idx:", idx);
        return (
            <TouchableOpacity onPress={() => { 
                // this.state.brickTypeList[idx]({
                //     selBrickTypeId:item.brickTypeId
                // })
                console.log("=====idx====:", idx);
                // this.state.brickTypeList[idx].selBrickTypeId = item.brickTypeId;
                // this.setState({
                //     brickTypeList:this.state.brickTypeList,
                //     // selBrickTypeId:item.brickTypeId
                // }) 
            }}>
                <View key={item.orderId} style={item.orderId===this.state._orderList[idx].orderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.orderId===this.state._orderList[idx].orderId ? CommonStyle.selectedBlockItemTextStyle : CommonStyle.blockItemTextStyle }>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 渲染客户底部滚动数据
    openCustomerSelect(){
        this.refs.SelectCustomer.showCustomer(this.state.selectCustomer, this.state.customerDataSource)
    }
    

    callBackCustomerValue(value){
        console.log("==========客户选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectCustomer:value
        })
        var customerName = value.toString();
        let loadUrl= "/biz/tenant/customer/getCustomerByName";
        let loadRequest={
            "customerName":customerName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadCustomerDetailData);
    }

    callBackLoadCustomerDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            // 选择的客户ID
            var varCustomerId = response.data.customerId
            this.setState({
                customerName:response.data.customerName,
                customerId:varCustomerId,
            })
            // 加载订单
            this.loadOrderData(varCustomerId);

        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    openSchedulingProductionTime(){
        this.refs.SelectSchedulingProductionTime.showDate(this.state.selectCheckOutTime)
    }

    callBackSelectSchedulingProductionTimeValue(value){
        console.log("==========排产开始时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectCheckOutTime:value
        })
        if (this.state.selectCheckOutTime && this.state.selectCheckOutTime.length) {
            var checkOutTime = "";
            var vartime;
            for(var index=0;index<this.state.selectCheckOutTime.length;index++) {
                vartime = this.state.selectCheckOutTime[index];
                if (index===0) {
                    checkOutTime += vartime;
                }
                else{
                    checkOutTime += "-" + vartime;
                }
            }
            this.setState({
                checkOutTime:checkOutTime
            })
        }
    }

    // 渲染订单底部滚动数据
    openOrderSelect(){
        this.refs.SelectOrder.showOrder(this.state.selectOrder, this.state.orderDataSource)
    }

    callBackOrderValue(value){
        console.log("==========订单选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectOrder:value
        })
        // 取选定的订单ID
        var orderName = value.toString();
        let loadUrl= "/biz/order/getOrderByName";
        let loadRequest={"orderName":orderName, "brickTypeInventory":"YES"};
        console.log("==========loadDetailRequest", loadRequest)
        httpPost(loadUrl, loadRequest, this.loadOrderDetailData);
    }

    loadOrderDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            var varOrder={
                index:0,
                orderId:response.data.orderId,
                orderName:response.data.orderName,
                brickTypeName:response.data.brickTypeName,
                // 订单订购的砖库存
                brickTypeStaticSum:response.data.brickTypeStaticSum,
                thisTimeCheckOutAmount:0
                // thisTimeCheckOutAmount:(response.data.brickAmount - response.data.orderCheckOutAmount)
            };
            var varSelOrderList = this.state.selOrderList;
            varSelOrderList[0] = varOrder;
            this.setState({
                selOrderList:varSelOrderList
            })

            // this.state.selOrderList[0]=varOrder;
            console.log("===aaa========this.state.selOrderList==", this.state.selOrderList);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    render(){

        // 动态显示装窑砖类数据
        var pages = [];
        for (var i = 0; i < this.state.selOrderList.length; i++) {
            const _orderDataSource = _.cloneDeep(this.state.orderDataSource);
            _orderDataSource.map((elem, index)=>{
                elem._index = i;
                return elem;
            })
            pages.push(
                <View key={"view_" +this.state.selOrderList[i].orderId+"_"+i}>
                    <View style={CommonStyle.blockSplitViewStyle}></View>
                    {/* <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>出库订单</Text>
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>订单名称</Text>
                        </View>
                        <TouchableOpacity onPress={()=>{
                            if (this.state.customerId) {
                                // 只有选择了客户过后才会加载订单
                                this.openOrderSelect()
                            }
                            else {
                                WToast.show({data:"请先选择客户"});
                                return;
                            }
                        }}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.selOrderList[i].orderName ? "请选择订单" : this.state.selOrderList[i].orderName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={CommonStyle.rowSplitViewStyle}></View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>砖型</Text>
                        </View>
                        <View style={CommonStyle.inputTextStyleTextStyle}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.selOrderList[i].brickTypeName ? "订单砖型" : this.state.selOrderList[i].brickTypeName}
                            </Text>
                        </View>
                    </View>

                    <View style={CommonStyle.rowSplitViewStyle}></View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>库存数量</Text>
                        </View>
                        <View style={CommonStyle.inputTextStyleTextStyle}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {this.state.selOrderList[i].brickTypeStaticSum}
                            </Text>
                        </View>
                    </View>

                    <View style={CommonStyle.rowSplitViewStyle}></View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库数量</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入出库数量'}
                            orderId={this.state.selOrderList[i].orderId}
                            // onChangeText={(text) => {
                            //     this.setState({thisTimeCheckOutAmount:text})
                            // }}
                            onChange={(event) => {
                                // 通过回调事件查看控件属性
                                var orderId = event._dispatchInstances.memoizedProps.orderId;
                                var text = event.nativeEvent.text;
                                console.log("=====isNumber:", isNumber(text));
                                var varselOrder;
                                for(var index=0; index<this.state.selOrderList.length;index++){
                                    varselOrder = this.state.selOrderList[index];
                                    if (orderId === varselOrder.orderId) {
                                        varselOrder.thisTimeCheckOutAmount = text;
                                        this.state.selOrderList[index] = varselOrder;
                                        console.log("==数据更新==this.state.selOrderList", this.state.selOrderList);
                                    }
                                }
                            }}
                            style={styles.inputRightText}>
                                {this.state.selOrderList[i].thisTimeCheckOutAmount}
                        </TextInput>
                    </View>
                </View>
            );
        }

        return(

            <View>
                <CommonHeadScreen title='新增出库'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                 
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                         <Text style={styles.leftLabNameTextStyle}>
                             客户名称
                         </Text>
                         <Text style={styles.leftLabRedTextStyle}>*</Text>
                     </View>
                     <TouchableOpacity onPress={()=>this.openCustomerSelect()}>
                        <View style={CommonStyle.inputTextStyleTextStyle}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {this.state.customerName.length === 0 ? "请选择客户" : this.state.customerName}
                            </Text>
                        </View>
                    </TouchableOpacity>
                     {/* <TextInput style={styles.inputRightText}></TextInput> */}
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={CommonStyle.rowLabView}>
                         <Text style={styles.leftLabNameTextStyle}>
                             领料人
                         </Text>
                     </View>
                 </View>
                 <View style={{backgroundColor:'#F5F5F5'}}>
                     <FlatList 
                         numColumns = {3}
                         data={this.state.consigneeDataSource}
                         // ItemSeparatorComponent={this.space}
                         renderItem={({item}) => this.renderConsigneeView(item)}
                         ListEmptyComponent={EmptyRowViewComponent}
                         />
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                         <Text style={styles.leftLabNameTextStyle}>
                             出库时间
                         </Text>
                         <Text style={styles.leftLabRedTextStyle}>*</Text>
                     </View>
                     
                     <TouchableOpacity onPress={()=>this.openSchedulingProductionTime()}>
                        <View style={CommonStyle.inputTextStyleTextStyle}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {this.state.checkOutTime.length === 0 ? "请选择出库时间" : this.state.checkOutTime}
                            </Text>
                        </View>
                    </TouchableOpacity>
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                         <Text style={styles.leftLabNameTextStyle}>
                             驾驶员
                         </Text>
                         <Text style={styles.leftLabRedTextStyle}>*</Text>
                     </View>
                     <TextInput 
                        placeholder={'请输入驾驶员'}
                        onChangeText={(text) => this.setState({driverName:text})}
                        style={styles.inputRightText}></TextInput>
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                         <Text style={styles.leftLabNameTextStyle}>
                             联系电话
                         </Text>
                         <Text style={styles.leftLabRedTextStyle}>*</Text>
                     </View>
                     <TextInput 
                        keyboardType='numeric'
                        placeholder={'请输入联系电话'}
                        onChangeText={(text) => this.setState({driverTel:text})}
                        style={styles.inputRightText}></TextInput>
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                         <Text style={styles.leftLabNameTextStyle}>
                             货车车牌
                         </Text>
                         <Text style={styles.leftLabRedTextStyle}>*</Text>
                     </View>
                     <TextInput
                        placeholder={'请输入货车车牌'}
                        onChangeText={(text) => this.setState({licensePlate:text})}
                        style={styles.inputRightText}
                    ></TextInput>
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
 
                 {/* <View style={CommonStyle.addItemSplitRowView}>
                     <Text style={CommonStyle.addItemSplitRowText}>出库订单</Text>
                 </View> */}
                 {/* <View key="checkOutOrderView">
                     {checkOutOrderList}
                 </View> */}

                 <View>
                    {
                        pages.map((elem, index) => {
                            return elem;
                        })
                    }
                </View>
 
                 <View style={styles.btnRowView}>
                     <TouchableOpacity onPress={()=>{
                         WToast.show({data:"功能未开通"});
                     }}>
                         <View style={[styles.btnAddView, CommonStyle.disableViewStyle]}>
                             <Text style={styles.btnAddText}>+新增砖型</Text>
                         </View>
                     </TouchableOpacity>
                     
                     <TouchableOpacity onPress={()=>{
                         let toastOpts;
                         if (this.state.selOrderList.length <= 1) {
                            toastOpts = getFailToastOpts("出库至少出库一个订单");
                            WToast.show(toastOpts)
                            return
                         }
                         this.setState({
                            selOrderList:this.state.selOrderList.slice(0,-1)
                         })
                     }}>
                         <View style={[styles.btnDeleteView, CommonStyle.disableViewStyle]}>
                             <Text style={styles.btnDeleteText}>-删除</Text>
                         </View>
                     </TouchableOpacity>
                 </View>
 
                 <View style={CommonStyle.btnRowStyle}>
                     <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要取消吗？',[
                            {text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                            }},
                            {text:"确定", onPress:()=>{this.props.navigation.goBack()}}
                        ]);
                    }}>
                        <View style={CommonStyle.btnRowLeftCancelBtnView} >
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要完成出库吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    // 检验参数是否合法
                                    let toastOpts;
                                    if (!this.state.customerId || this.state.customerId === 0) {
                                        toastOpts = getFailToastOpts("请选择客户");
                                        WToast.show(toastOpts)
                                        return;
                                    }
                                    if (!this.state.selConsigneeId || this.state.selConsigneeId === 0) {
                                        toastOpts = getFailToastOpts("请选择运送人");
                                        WToast.show(toastOpts)
                                        return;
                                    }
                                    if (!this.state.checkOutTime || this.state.checkOutTime.length === 0) {
                                        toastOpts = getFailToastOpts("请选择出库时间");
                                        WToast.show(toastOpts)
                                        return;
                                    }
                                    if (!this.state.driverName || this.state.driverName.length === 0) {
                                        toastOpts = getFailToastOpts("请输入驾驶员");
                                        WToast.show(toastOpts)
                                        return;
                                    }
                                    if (!this.state.driverTel || this.state.driverTel.length === 0) {
                                        toastOpts = getFailToastOpts("请输入联系电话");
                                        WToast.show(toastOpts)
                                        return;
                                    }
                                    if (!this.state.licensePlate || this.state.licensePlate.length === 0) {
                                        toastOpts = getFailToastOpts("请输入车牌");
                                        WToast.show(toastOpts)
                                        return;
                                    }

                                    if (!this.state.selOrderList || this.state.selOrderList.length < 1) {
                                        toastOpts = getFailToastOpts("出库至少出库一个订单");
                                        WToast.show(toastOpts)
                                        return;
                                    }

                                    var _spCheckOutDetailDTOList = [];
                                    this.state.selOrderList.map((elem, index) => {
                                        var _spCheckOutDetailDTO = {
                                            orderId:elem.orderId,
                                            orderCheckOutAmount:elem.thisTimeCheckOutAmount
                                        }
                                        _spCheckOutDetailDTOList.push(_spCheckOutDetailDTO);
                                    })

                                    // 出库
                                    let url= "/biz/check/out/record/add";
                                    let requestParams={
                                        "customerId": this.state.customerId,
                                        "consigneeId": this.state.selConsigneeId,
                                        "checkOutTime": this.state.checkOutTime,
                                        "driverName": this.state.driverName,
                                        "driverTel": this.state.driverTel,
                                        "licensePlate": this.state.licensePlate,
                                        "spCheckOutDetailDTOList":_spCheckOutDetailDTOList
                                    };
                                    console.log("=========url:", url)
                                    console.log("=========requestParams:", requestParams)
                                    httpPost(url, requestParams, (response)=>{
                                        let toastOpts;
                                        switch (response.code) {
                                            case 200:
                                                if (this.props.route.params.refresh) {
                                                    this.props.route.params.refresh()
                                                }
                                                toastOpts = getSuccessToastOpts('保存完成');
                                                WToast.show(toastOpts);
                                                this.props.navigation.goBack()
                                                break;
                                            default:
                                                toastOpts = getFailToastOpts(response.message);
                                                WToast.show({data:response.message})
                                        }
                                    });
                                }
                            }
                        ]);
                    }}>
                        <View style={CommonStyle.btnRowRightSaveBtnView}>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>完成出库</Text>
                        </View>
                    </TouchableOpacity>
                 </View>
            </ScrollView>
            <BottomScrollSelect 
                ref={'SelectOrder'} 
                callBackOrderValue={this.callBackOrderValue.bind(this)}
            />
             <BottomScrollSelect 
                ref={'SelectCustomer'} 
                callBackCustomerValue={this.callBackCustomerValue.bind(this)}
            />
             <BottomScrollSelect 
                ref={'SelectSchedulingProductionTime'} 
                callBackDateValue={this.callBackSelectSchedulingProductionTimeValue.bind(this)}
            />
            </View>
            
        )
    }
}
const styles = StyleSheet.create({

    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    }
})
module.exports = CheckOutAdd;