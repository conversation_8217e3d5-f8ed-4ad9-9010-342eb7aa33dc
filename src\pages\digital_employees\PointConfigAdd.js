import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import DateTimePicker from '@react-native-community/datetimepicker';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
var currentTime=(new Date()).getTime() + 8*3600*1000

export default class PointConfigAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            pointConfigId:"",
            pointConfigAlias:"",
            pointConfigValue:"",
            pointClassId:"",
            pointClassName:"",
            departmentId:"",
            departmentName:"",
            pointConfigSort:0,
            pointClassIdDataSource: [],
            departmentDataSource: [],
            selectedPointClass: [],
            showSearchItemBlock:false,
            selectedDepartment: [],
            operate:"",
            selDepartmentId:null,
            selDepartmentName:null,
            submitWeeks:"",
            weekDataList:[],
            submitBeginTime:"",
            submitEndTime:"",

            teachingStart:"",
            teachingEnd:"",
            open1:false,
            open2:false,
            currentTime:currentTime,
            startTime:"",
            endTime:"",
            weekDataSource: [],
            selSubmitWeeksName:"",

            selectedSubmitBeginTime:[],
            selectedSubmitEndTime:[],
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadPointClassList();
        // 加载部门列表
        this.loadDepartmentList();
        console.log("==========积分类别数据源1：", this.state.pointClassIdDataSource);

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { pointConfigId} = route.params;
            if (pointConfigId) {
                console.log("========Edit==pointConfigId:", pointConfigId);
                this.setState({
                    pointConfigId:pointConfigId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/point/config/get";
                loadRequest={'pointConfigId':pointConfigId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditPointConfigDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
                // // 当前时间
                // var currentDate = new Date();
                // var currentDateHour = ("0" + (currentDate.getHour() + 1)).slice(-2);
                // var currentDateMinutes = ("0" + currentDate.getMinutes()).slice(-2);
                // this.setState({
                //     selectedSubmitBeginTime: [currentDateHour, currentDateMinutes],
                //     submitBeginTime:  currentDateHour + ":" + currentDateMinutes
                // })
            }
        }
        var _weekDataSource = [
            {
                "code":"1",
                "name":"周一"
            },
            {
                "code":"2",
                "name":"周二"
            },
            {
                "code":"3",
                "name":"周三"
            },
            {
                "code":"4",
                "name":"周四"
            },
            {
                "code":"5",
                "name":"周五"
            },
            {
                "code":"6",
                "name":"周六"
            },
            {
                "code":"7",
                "name":"周日"
            }
        ];
        this.setState({
            weekDataSource:_weekDataSource,
            selSubmitWeeks:_weekDataSource[0].code,
            selSubmitWeeksName:_weekDataSource[0].name,
        })
    }

    loadPointClassList = () => {
        let url = "/biz/point/class/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadPointClassListCallBack);
    }

    loadPointClassListCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                pointClassIdDataSource: response.data,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditPointConfigDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            var selectedSubmitBeginTime = response.data.submitBeginTime.split(":");
            var selectedSubmitEndTime = response.data.submitEndTime.split(":");
            this.setState({
                pointConfigId:response.data.pointConfigId,
                pointConfigAlias:response.data.pointConfigAlias,
                pointConfigValue:response.data.pointConfigValue,
                pointConfigSort:response.data.pointConfigSort,
                selDepartmentId: response.data.departmentId,
                selectedPointClass:[response.data.pointClassName],
                selDepartmentName:[response.data.departmentName],
                pointClassName:response.data.pointClassName,
                submitWeeks:response.data.submitWeeks,
                submitBeginTime:response.data.submitBeginTime,
                submitEndTime:response.data.submitEndTime,
                selectedSubmitBeginTime:selectedSubmitBeginTime,
                selectedSubmitEndTime:selectedSubmitEndTime
            })

            var list = [];
            if(response.data.submitWeeks){
                var weekDataList = response.data.submitWeeks.split(",")
                console.log("====weekDataList===="+weekDataList)
                for(var i=0; i< weekDataList.length; i++){
                  list =  list.concat(weekDataList[i])
                }
                console.log(list)
                this.setState({
                    weekDataList:list
                })
            }
            // var selectedSubmitBeginTime;
            // if (response.data.submitBeginTime) {
            //     selectedSubmitBeginTime = response.data.submitBeginTime.split(":");
            // }
            // var selectedSubmitEndTime;
            // if (response.data.submitEndTime) {
            //     selectedSubmitEndTime = response.data.submitEndTime.split(":");
            // }
        }
    }
    compare=(code)=>{
        for(var i=0;i<this.state.weekDataList.length;i++){
            if(this.state.weekDataList[i] == code){
                return true;
            }
        }
        return false;
    }

    // 日期单项渲染
    renderSubmitWeeksRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                var weekDataList = this.state.weekDataList;
                if (this.compare(item.code)) {
                    arrayRemoveItem(weekDataList, item.code);
                }
                else {
                    weekDataList = weekDataList.concat(item.code)
                }
                this.setState({
                    weekDataList:weekDataList,
                })
                WToast.show({data:'点击了' + item.name});
                console.log("======weekDataList:", weekDataList)
            }}>
                <View key={item.code} style={this.compare(item.code) ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={this.compare(item.code) ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.name}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 渲染底部积分类别选择器
    openPointClassIdSelect() {
        if (!this.state.pointClassIdDataSource || this.state.pointClassIdDataSource.length < 1) {
            WToast.show({ data: "请先添加积分类别" });
            return
        }
        this.refs.SelectPointClassId.showPointClassId(this.state.selectedPointClass, this.state.pointClassIdDataSource)
    }

    callBackPointClassIdValue(value) {
        console.log("==========积分类别选择的结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedPointClass: value,
        })
        var pointClassName = value.toString();
        // this.setState({
        //     pointClassName: pointClassName
        // })
        let loadUrl = "/biz/point/class/getPointClassByName";
        let loadRequest = {
            "pointClassName": pointClassName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadPointClassData);
    }
    callBackLoadPointClassData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                pointClassName: response.data.pointClassName,
                pointClassId:response.data.pointClassId
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    loadDepartmentList=()=>{
        let url= "/biz/department/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000
            //"qryAll":"Y"
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                var departmentData = response.data.dataList;
                departmentData.unshift({"departmentId":0,"departmentName":"全部"})
                this.setState({
                    departmentDataSource:departmentData
                })
                console.log("==========部门数据源：", this.state.departmentDataSource);
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    // 显示搜索项目
    showSearchItemSelect(){
        if (!this.state.departmentDataSource || this.state.departmentDataSource.length < 1) {
            WToast.show({data:"请先添加部门"});
            return
        }
        this.setState({
            showSearchItemBlock:true,
        })
    }
    // 部门
    renderDepartmentRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                selDepartmentId:item.departmentId,
                selDepartmentName:item.departmentName,
            }) }}>
                <View key={"department_" + item.departmentId} style={[item.departmentId===this.state.selDepartmentId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle, {padding:10, margin:5, }] }>
                    <Text style={[item.departmentId===this.state.selDepartmentId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                        {item.departmentName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("PointConfig")
            }}>
                <Text style={CommonStyle.headRightBlackText}>积分设置</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    savePointConfig =()=> {
        console.log("=======savePointConfig");
        let toastOpts;
        if (!this.state.selDepartmentName) {
            toastOpts = getFailToastOpts("请选择部门");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.pointClassName) {
            toastOpts = getFailToastOpts("请选择积分类别");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.pointConfigAlias) {
            toastOpts = getFailToastOpts("请输入积分描述"); 
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.weekDataList && this.state.pointClassId==1) {
            toastOpts = getFailToastOpts("请选择提交日期"); 
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.submitBeginTime && this.state.pointClassId==1) {
            toastOpts = getFailToastOpts("请设定开始时间"); 
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.submitEndTime && this.state.pointClassId==1) {
            toastOpts = getFailToastOpts("请设定截止时间"); 
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.pointConfigValue) {
            toastOpts = getFailToastOpts("请输入对应积分"); 
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.pointConfigSort) {
        //     toastOpts = getFailToastOpts("请输入排序"); 
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url= "/biz/point/config/add";
        if (this.state.pointConfigId) {
            console.log("=========Edit===pointConfigId", this.state.pointConfigId)
            url= "/biz/point/config/modify";
        }
        let requestParams={
            "pointConfigId":this.state.pointConfigId,
            "pointConfigAlias":this.state.pointConfigAlias,
            "pointConfigValue":this.state.pointConfigValue,
            "pointConfigSort":this.state.pointConfigSort,
            "pointClassId":this.state.pointClassId,
            "departmentId":this.state.selDepartmentId,
            // departmentId:this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId,
            "submitWeeks":this.state.weekDataList.toString(),
            "submitBeginTime":this.state.submitBeginTime,
            "submitEndTime":this.state.submitEndTime,
        };
        console.log("weekDataList.toString=========", this.state.weekDataList.toString())
        httpPost(url, requestParams, this.savePointConfigCallBack);
    }

    // 保存回调函数
    savePointConfigCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
        }
    }

        openBeginTimeDate(){
            this.refs.SelectSubmitBeginTimeDate.showTime(this.state.selectedSubmitBeginTime)
        }
        
        callBackSubmitBeginTimeValue(value){
            console.log("==========提交时间选择结果：", value)
            if (!value) {
                return;
            }
            this.setState({
                selectedSubmitBeginTime:value
            })
            if (value && value.length) {
                var submitBeginTime = "";
                var vartime;
                for(var index=0;index<value.length;index++) {
                    vartime = value[index];
                    if (index===0) {
                        submitBeginTime += vartime;
                    }
                    else{
                        submitBeginTime += ":" + vartime;
                    }
                }
                this.setState({
                    submitBeginTime:submitBeginTime
                })
            }
        }

        openEndTimeDate(){
            this.refs.SelectSubmitEndTimeDate.showTime(this.state.selectedSubmitEndTime)
        }

        callBackSubmitEndValue(value){
            console.log("==========提交时间选择结果：", value)
            if (!value) {
                return;
            }
            this.setState({
                selectedSubmitEndTime:value
            })
            if (value && value.length) {
                var submitEndTime = "";
                var vartime;
                for(var index=0;index<value.length;index++) {
                    vartime = value[index];
                    if (index===0) {
                        submitEndTime += vartime;
                    }
                    else{
                        submitEndTime += ":" + vartime;
                    }
                }
                this.setState({
                    submitEndTime:submitEndTime
                })
            }
        }
       
    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '积分设置'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                {
                this.state.showSearchItemBlock ?
                <View style={{
                    position: 'absolute', backgroundColor:'rgba(169,169,169,0.95)',width:screenWidth,
                    zIndex:101, padding:10,right: 0, left:0, top: 50,
                    }}>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)'}]}>
                            <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>部门：</Text>
                        </View>
                        {
                            (this.state.departmentDataSource && this.state.departmentDataSource.length > 0) 
                            ? 
                            this.state.departmentDataSource.map((item, index)=>{
                                return this.renderDepartmentRow(item)
                            })
                            : null 
                        }
                    </View>
                    <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                        <TouchableOpacity onPress={() => { this.setState({
                            showSearchItemBlock:false,
                        }) }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => { 
                            this.setState({
                                showSearchItemBlock:false,
                                // departmentId:this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId
                                departmentId:this.state.selDepartmentId
                            })
                            console.log("选择的部门====="+this.state.selDepartmentId)
                        }}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
                :
                null
                }
                <View style={[styles.inputRowStyle]} >
                    <View style={{width:'100%',flexWrap:'wrap', flexDirection:'row'}}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>部门设置</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.showSearchItemSelect()}>
                            <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}]}>
                                <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                                    {this.state.selDepartmentId && this.state.selDepartmentName ? (this.state.selDepartmentName) :
                                    (this.state.selDepartmentId==0?"全部":"选择部门") }
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>积分类别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openPointClassIdSelect()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.pointClassName ? "请选择积分类别" : this.state.pointClassName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>积分描述</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            // //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入积分描述'}
                            onChangeText={(text) => this.setState({pointConfigAlias:text})}
                        >
                            {this.state.pointConfigAlias}
                        </TextInput>
                    </View>
                    {
                        this.state.pointClassId==1 || this.state.pointClassName=="提交日报" || this.state.pointClassId==15 || this.state.pointClassName=="逾期提交日报"?
                        <View>
                            <View style={[styles.inputRowStyle,{height:120}]}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>
                                    提交日期
                                    </Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <View style={{width:screenWidth - leftLabWidth - 10, flexWrap:'wrap', flexDirection:'row'}}>
                                    {
                                        (this.state.weekDataSource && this.state.weekDataSource.length > 0) 
                                        ? 
                                        this.state.weekDataSource.map((item, index)=>{
                                            return this.renderSubmitWeeksRow(item)
                                        })
                                        : <EmptyRowViewComponent/> 
                                    }
                                </View>
                            </View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>提交时间</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <TouchableOpacity onPress={()=>this.openBeginTimeDate()}>
                                    <View style={[CommonStyle.inputTextStyleTextStyleNoWidth,{width:85,alignItems:'center'}]}>
                                        <Text style={{color:'#A0A0A0', fontSize:15}}>
                                            {!this.state.submitBeginTime ? "请选择开始时间" : this.state.submitBeginTime}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                                <View style={[{height:45,justifyContent:'center',marginLeft:10,marginRight:10}]}>
                                    <Text style={styles.leftLabNameTextStyle}>-</Text>
                                </View>
                                <TouchableOpacity onPress={()=>this.openEndTimeDate()}>
                                    <View style={[CommonStyle.inputTextStyleTextStyleNoWidth,{width:85,alignItems:'center'}]}>
                                        <Text style={{color:'#A0A0A0', fontSize:15}}>
                                            {!this.state.submitEndTime ? "请选择截止时间" : this.state.submitEndTime}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>  
                        </View>   
                        :
                        null
                    }
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>对应积分</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入对应积分'}
                            onChangeText={(text) => this.setState({pointConfigValue:text})}
                        >
                            {this.state.pointConfigValue}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({pointConfigSort:text})}
                        >
                            {this.state.pointConfigSort}
                        </TextInput>
                    </View>              
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row'}]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.savePointConfig.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect
                        ref={'SelectPointClassId'}
                        callBackPointClassIdValue={this.callBackPointClassIdValue.bind(this)}
                    />   
                    <BottomScrollSelect 
                        ref={'SelectSubmitBeginTimeDate'} 
                        callBackTimeValue={this.callBackSubmitBeginTimeValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectSubmitEndTimeDate'} 
                        callBackTimeValue={this.callBackSubmitEndValue.bind(this)}
                    />
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    itemViewStyle:{
        margin:10,  
        padding:15,  
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }

});