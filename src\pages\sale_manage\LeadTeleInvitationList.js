import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Linking,Clipboard,Image,TextInput,ScrollView,Modal,KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';

import BottomScrollSelect from '../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
var screenHeight = Dimensions.get('window').height;
export default class LeadTeleInvitationList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            selCompletionStateCode:'all',
            modal:false,
            initGmtCreated: null,
            selectGmtCreated:null,
            searchKeyWord:null,
            completionStateDataSource:[],
            telOperateResultList:"",
            operateResultDataSource:[],
            selOperateResultStateCode:"F",

            // 审核信息
            telOperateId:"",
            telJobTitle:"",
            telContactPerson:"",
            telContactTel:"",
            telOperateOpinion:"",
            telOperateResult:"",
            userId:"",
            leadId:"",

            //操作的模式：add-新增；edit-编辑
            operate:"新增",
            errorMsg:"",

            detailDTO:{},
            detailModal:false,
            
            gmtCreated:null,
            isVisit:false
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let operateResultDataSource = [
            {
                stateCode:'F',
                stateName:'跟进',
            },
            {
                stateCode:'W',
                stateName:'入库',
            },
            {
                stateCode:'G',
                stateName:'放弃',
            }
        ]
        this.setState({
            operateResultDataSource:operateResultDataSource,
        })                    
        let completionStateDataSource = [
            {
                stateCode:'all',
                stateName:'全部',
            },
            {
                stateCode:'Y',
                stateName:'已电邀',
            },
            {
                stateCode:'N',
                stateName:'未电邀',
            },
            {
                stateCode:'D',
                stateName:'待认领'
            }
        ]
        this.setState({
            completionStateDataSource:completionStateDataSource,
        })
        var _gmtCreated = this.initGmtCreated();
        this.loadLeadTelelnvitationList(_gmtCreated);
    }

    initGmtCreated=()=>{
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 3);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectGmtCreated: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            gmtCreated: _gmtCreated,
            initGmtCreated: _gmtCreated,
        })
        return _gmtCreated;
    }

        // 回调函数
        callBackFunction=()=>{
            let url= "/biz/lead/operate/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "operateType":"A",
                "operateResult":"F",
                "isTel": this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
                "searchKeyWord":this.state.searchKeyWord,
                "gmtCreated":this.state.gmtCreated
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    
        // 下拉触顶刷新到第一页
        _loadFreshData=()=>{
            if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.gmtCreated === this.state.initGmtCreated) {
                console.log("==========不刷新=====");
                return;
            }
            var _gmtCreated = this.initGmtCreated();
            this.setState({
                gmtCreated: _gmtCreated,
            })
                this.setState({
                currentPage:1
            })
            let url= "/biz/lead/operate/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "operateType":"A",
                "operateResult":"F",
                "isTel": this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
                "gmtCreated": _gmtCreated,
                "searchKeyWord":this.state.searchKeyWord
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    
        _loadFreshDataCallBack=(response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                var dataNew = response.data.dataList;
                // dataOld.unshift(dataNew);
                var dataAll = [...dataNew];
                this.setState({
                    dataSource:dataAll,
                    currentPage:response.data.currentPage + 1,
                    totalPage:response.data.totalPage,
                    totalRecord:response.data.totalRecord,
                    refreshing:false
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        }
    
        flatListFooterComponent=()=>{
            return(
                <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
            )
        }

        // 上拉触底加载下一页
        _loadNextData=()=>{
            if ((this.state.currentPage-1) >= this.state.totalPage) {
                WToast.show({data:"已经是最后一页了，我们也是有底线的"});
                return;
            }
            this.setState({
                refreshing:true
            })
            this.loadLeadTelelnvitationList();
        }
    
        loadLeadTelelnvitationList=(_gmtCreated)=>{
            let url= "/biz/lead/operate/list";
            let loadRequest={
                "currentPage": this.state.currentPage,
                "pageSize": this.state.pageSize,
                "operateType":"A",
                "operateResult":"F",
                "isTel": this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
                "gmtCreated": _gmtCreated ? _gmtCreated : this.state.gmtCreated,
                "searchKeyWord":this.state.searchKeyWord
            };
            httpPost(url, loadRequest, this.loadLeadTelelnvitationListCallBack);
        }
    
        loadLeadTelelnvitationListCallBack=(response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
    
                var dataNew = response.data.dataList;
                var dataOld = this.state.dataSource;
                // dataOld.unshift(dataNew);
                var dataAll = [...dataOld,...dataNew];
                this.setState({
                    dataSource:dataAll,
                    currentPage:response.data.currentPage + 1,
                    totalPage:response.data.totalPage,
                    totalRecord:response.data.totalRecord,
                    refreshing:false
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        }

        searchByKeyWord = () => {
            let loadUrl = "/biz/lead/operate/list";
            let loadRequest = {
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "operateType":"A",
                "operateResult":"F",
                "isTel": this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
                "searchKeyWord": this.state.searchKeyWord,
                "gmtCreated":this.state.gmtCreated
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }

        renderRow=(item, index)=>{
            return (
                <View key={item.leadId} style={styles.innerViewStyle}>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>客户名称：{item.customerName}</Text>
                        {
                            item.telOperateResult?
                            <View>
                            {
                                item.telOperateResult==="放弃" ?
                                <Text style={{color:"#CB4139"}}>{item.telOperateResult}</Text>
                                :
                                <Text style={{color:"#F2C16D"}}>{item.telOperateResult?item.telOperateResult:"未电邀"}</Text>
                            }
                            </View>
                            :
                            <Text style={{color:"#bfbfbf"}}>未电邀</Text>
                        }
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>联系人：{item.contactPerson}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>职务：{item.jobTitle}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>联系电话：{item.contactTel}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>审核意见：{item.auditOpinion?item.auditOpinion:"无"}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>线索审核人：{item.userName}</Text>
                    </View>
                    {
                        item.claimUserName
                        ?
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>电邀认领人：{item.claimUserName}</Text>
                        </View>
                        :<View/>
                    }
                
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>{item.claimTime?"电邀认领时间":"线索审核时间"}：{item.claimTime?item.claimTime:item.gmtCreated}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>电邀结果：{item.telOperateResult?item.telOperateResult:"未电邀"}</Text>
                    </View>
                    {
                        item.telOperateResult?
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>电邀意见：{item.telOperateOpinion?item.telOperateOpinion:"无"}</Text>
                        </View> 
                        :
                        <View></View>
                    }
                         
                     <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                        {
                            item.userId != constants.loginUser.userId && (item.telOperateResult=='入库' || item.telOperateResult=='放弃')? 
                            <TouchableOpacity onPress={()=>{
                                let loadUrl = "/biz/lead/operate/telClaim";
                                let loadRequest = {
                                    "operateId": item.operateId,
                                    'claimUserId':constants.loginUser.userId,
                                    'leadId':item.leadId
                                };
                                httpPost(loadUrl, loadRequest, (response)=>{
                                    if (response.code == 200 && response.data) {
                                        WToast.show({ data: '认领成功' });
                                        this.callBackFunction()
                                    }
                                    else{
                                        WToast.show({ data: response.message });
                                        this.props.navigation.navigate("LoginView");
                                    }
                            
                                });
                            }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {backgroundColor:"#3ab240",width: 75 ,flexDirection:"row"}]}>
                                <Image  style={{width:25, height:25,marginRight:3}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>认领</Text>
                                </View>
                            </TouchableOpacity>
                            :<View/>
                        }
                        {
                            (item.userId == constants.loginUser.userId && !item.claimUserId) || item.claimUserId == constants.loginUser.userId? 
                            <TouchableOpacity onPress={()=>{
                                let loadUrl = "/biz/lead/operate/getList";
                                let loadRequest = {
                                    "currentPage": 1,
                                    "pageSize": this.state.pageSize,
                                    "leadId":item.leadId,
                                    "operateType":"T",
                                };
                                httpPost(
                                    loadUrl, 
                                    loadRequest, 
                                    (response)=>{
                                    if (response.code == 200 && response.data && response.data.dataList) {
                                        console.log(response.data.dataList)
                                        if(response.data.dataList && response.data.dataList.length > 0){
                                            if(item.telOperateResult == '放弃'){
                                                this.setState({ 
                                                    isVisit:true,
                                                    errorMsg:"已放弃，不可编辑"
                                                })
                                            }
                                            if(item.telOperateResult == '入库'){
                                                this.setState({ 
                                                    isVisit:true,
                                                    errorMsg:"已入库，不可编辑"
                                                })
                                            }
                                            //将item赋入state
                                            var leadOperateDto = response.data.dataList[0]
                                            this.setState({ 
                                                telOperateId:leadOperateDto.operateId,
                                                selOperateResultStateCode:leadOperateDto.operateResult,
                                                customerName:leadOperateDto.customerName,
                                                telContactPerson:leadOperateDto.contactPerson,
                                                telJobTitle:leadOperateDto.jobTitle,
                                                telContactTel:leadOperateDto.contactTel,
                                                telOperateOpinion:leadOperateDto.operateOpinion,
                                                telOperateResult:leadOperateDto.operateResult,
                                                userId:leadOperateDto.userId,
                                                leadId:leadOperateDto.leadId,
                                                operate:"编辑",
                                            })
                                            loadUrl = "/biz/lead/operate/getList";
                                            loadRequest = {
                                                "currentPage": 1,
                                                "pageSize": 10,
                                                "leadId":item.leadId,
                                                "operateType":"V",
                                            };
                                            httpPost(loadUrl, loadRequest, (response)=>{
                                                if (response.code == 200 && response.data && response.data.dataList) {
                                                    console.log(response.data.dataList)
                                                    if(response.data.dataList && response.data.dataList.length > 0){
                                                        this.setState({ 
                                                            isVisit:true,
                                                            errorMsg:"已拜访，不可编辑"
                                                        })
                                                    }
                                                }
                                            })
                                        }
                                        else{
                                            this.setState({ 
                                                telOperateId:"",
                                                selOperateResultStateCode:"F",
                                                // telContactPerson:"",
                                                // telJobTitle:"",
                                                // telContactTel:"",
                                                telOperateOpinion:"",
                                                telOperateResult:"F",
                                                userId:constants.loginUser.userId,
                                                leadId:item.leadId,
                                                operate:"新增",
                                                errorMsg:"",
                                            })
                                            loadUrl = "/biz/lead/operate/getList";
                                            loadRequest = {
                                                "currentPage": 1,
                                                "pageSize": 10,
                                                "leadId":item.leadId,
                                                "operateType":"A",
                                            };
                                            httpPost(loadUrl, loadRequest, (response)=>{
                                                if (response.code == 200 && response.data && response.data.dataList) {
                                                    console.log(response.data.dataList)
                                                    if(response.data.dataList && response.data.dataList.length > 0){
                                                        var leadOperateDto = response.data.dataList[0]
                                                        this.setState({ 
                                                            telJobTitle:leadOperateDto.jobTitle,
                                                            telContactPerson:leadOperateDto.contactPerson,
                                                            telContactTel:leadOperateDto.contactTel,
                                                        })
                                                    }
                                                }
                                            })
                                        }
                                        this.setState({
                                            modal:true
                                        })
                                    }
                                    else if (response.code == 401) {
                                        WToast.show({data:response.message});
                                        this.props.navigation.navigate("LoginView");
                                    }
                                    }
                                );
                            }}>
                                <View style={[CommonStyle.itemBottomEditBtnViewStyle,{backgroundColor:"#ff8c1a",width:75,flexDirection:"row"}]}>
                                    <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/call2.png')}></Image>
                                    <Text style={CommonStyle.itemBottomEditBtnTextStyle}>结果</Text>
                                </View>
                            </TouchableOpacity>
                            :
                            <View/>
                        }
                        
                        <TouchableOpacity onPress={()=>{
                            let loadUrl = "/biz/customer/lead/detail";
                            let loadRequest = {
                                "leadId": item.leadId,
                            };
                            httpPost(loadUrl, loadRequest, (response)=>{
                                if (response.code == 200 && response.data) {
                                    this.setState({
                                        detailDTO: response.data,
                                        detailModal:true
                                    })
                                }
                        
                            });
                        }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { backgroundColor:"#3ab240",width: 75 ,flexDirection:"row"}]}>
                            <Image  style={{width:25, height:25,marginRight:3}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
            )
        }
        space(){
            return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
        }
        emptyComponent() {
            return <EmptyListComponent/>
        }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }
    renderLeadTeleInvitationItem() {
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.leadId) {
                    return;
                }
                this.setState({
                    selLeadId:item.leadId,
                    selContactPerson:item.contactPerson,
                    selJobTitle:item.jobTitle,
                    selContactTel:item.contactTel,
                    customerName:item.customerName
                })
            }}>
                <View key={item.leadId} style={item.leadId===this.state.selLeadId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.leadId===this.state.selLeadId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.customerName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    renderOperateResultRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    if(this.state.isVisit){
                        return;
                    }
                    this.setState({
                        selOperateResultStateCode:item.stateCode,
                        telOperateResult:item.stateCode
                    })
                }}>
                    <View 
                        key={item.stateCode} 
                        style={[item.stateCode===this.state.selOperateResultStateCode? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}
                        >
                        <Text style={[item.stateCode===this.state.selOperateResultStateCode? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    
    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    chooseStateRow=(item, index)=>{
        return (
            <View key={item.chooseCode} >
                <TouchableOpacity onPress={()=>{
                    var selStateCode = item.stateCode;
                    this.setState({
                        selCompletionStateCode:selStateCode
                    })

                    let loadUrl= "/biz/lead/operate/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "operateType":"A",
                        "operateResult":"F",        
                        "isTel": selStateCode === 'all' ? null : selStateCode,
                        "gmtCreated":this.state.gmtCreated,
                        "searchKeyWord": this.state.searchKeyWord,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.stateCode} style={[item.stateCode===this.state.selCompletionStateCode? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle,{paddingLeft:8,paddingRight:8}]}>
                        <Text style={[item.stateCode===this.state.selCompletionStateCode? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

        //保存函数
        saveTel=()=>{
            console.log("=======saveTel");
            let toastOpts;
            if (!this.state.telContactPerson) {
                this.setState({
                    errorMsg:"请输入联系人"
                })
                return;
            }
            
            if (!this.state.telJobTitle) {
                this.setState({
                    errorMsg:"请输入职务"
                })
                return;
            }
            
            if (!this.state.telContactTel) {
                this.setState({
                    errorMsg:"请输入联系电话"
                })
                return;
            }
            
            if (!this.state.selOperateResultStateCode) {
                this.setState({
                    errorMsg:"请选择拜访结果"
                })
                return;
            }
            let url= "/biz/lead/operate/add";
            if(this.state.operate === '编辑'){
                url= "/biz/lead/operate/modify";
            }
            let requestParams={
                operateId:this.state.telOperateId,
                contactPerson:this.state.telContactPerson,
                jobTitle:this.state.telJobTitle,
                contactTel:this.state.telContactTel,
                operateResult:this.state.selOperateResultStateCode,
                operateOpinion:this.state.telOperateOpinion,
                operateType:"T",
                userId:this.state.userId,
                leadId:this.state.leadId,
            };
            httpPost(url, requestParams, this.saveCallBack);
        }
    
        saveCallBack=(response)=>{
            let toastOpts;
            switch (response.code) {
                case 200:
                    //关闭弹窗
                    this.setState({
                        modal:false,
                        errorMsg:"",
                        isVisit:false
                    }) 
                    this.callBackFunction();
     
                    toastOpts = getSuccessToastOpts('保存完成');
                    WToast.show(toastOpts);
                    break;
                default:
                    toastOpts = getFailToastOpts(response.message);
                    WToast.show({data:response.message})
              }
        }
    
        openGmtCreated(){
            this.refs.SelectGmtCreated.showDate(this.state.selectGmtCreated)
        }
    
        callBackSelectGmtCreatedValue(value){
            console.log("==========时间选择结果：", value)
            if (!value) {
                return;
            }
            this.setState({
                selectGmtCreated:value
            })
            if (this.state.selectGmtCreated && this.state.selectGmtCreated.length) {
                var _gmtCreated = "";
                var vartime;
                for(var index=0;index<this.state.selectGmtCreated.length;index++) {
                    vartime = this.state.selectGmtCreated[index];
                    if (index===0) {
                        _gmtCreated += vartime;
                    }
                    else if (index < 3){
                        _gmtCreated += "-" + vartime;
                    }
                    else if (index===3){
                        _gmtCreated += " " + vartime;
                    }
                    else {
                        _gmtCreated += ":" + vartime;
                    }
                }
                this.setState({
                    currentPage: 1,
                    gmtCreated:_gmtCreated
                })
                let url= "/biz/lead/operate/list";
                let loadRequest={
                    "currentPage": 1,
                    "pageSize": this.state.pageSize,
                    "operateType":"A",
                    "operateResult":"F",
                    "isTel": this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
                    "gmtCreated":_gmtCreated,
                    "searchKeyWord":this.state.searchKeyWord
                    };
                httpPost(url, loadRequest, this._loadFreshDataCallBack);
            }
        }
    
       render(){
        return(
            <View>
                <CommonHeadScreen title='电邀管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.rightTop50FloatingBlockView,this.state.gmtCreated 
                    ? {borderRadius:3, width:null,height: 40, marginTop: 50,paddingLeft:15, paddingRight:15, opacity:0.5} : {}]}>
                    <TouchableOpacity onPress={()=>this.openGmtCreated()}>
                        <Text style={CommonStyle.rightTop50FloatingBlockText}>
                        {!this.state.gmtCreated ? "时间" : this.state.gmtCreated}
                        </Text>
                    </TouchableOpacity>
                </View>
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.completionStateDataSource && this.state.completionStateDataSource.length > 0)
                                ?
                                this.state.completionStateDataSource.map((item, index) => {
                                    return this.chooseStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                    <View style={{}}>
                        <View style={styles.inputOutsideText}>
                            <View style={styles.inputInsideText}>
                            <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'客户名称/线索审核人'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>                    
                </View>
            <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                <FlatList
                    data={this.state.dataSource}
                    ItemSeparatorComponent={this.space}
                    ListEmptyComponent={this.emptyComponent}
                    renderItem={({ item }) => this.renderRow(item)}
                    refreshControl={
                        <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={() => {
                                this._loadFreshData()
                            }}
                        />
                    }
                    // 底部加载
                    ListFooterComponent={() => this.flatListFooterComponent()}
                    onEndReached={() => this._loadNextData()}
                />
            </View>
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.modal}
                >
                    <KeyboardAvoidingView style={CommonStyle.fullScreenKeepOut} behavior="padding">
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle,{height:40, backgroundColor:"#ff8c1a",borderRadius:5, justifyContent:'center',alignItems:'center',marginTop:10}]}>
                                <Text style={[styles.titleTextStyle,{fontSize:18,fontWeight:'bold',color:"#ffffff"}]}>电邀结果</Text>
                            </View>
                            <ScrollView style={[CommonStyle.formContentViewStyle]}>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    </View>                                
                                    <TextInput 
                                        editable={!this.state.isVisit}
                                        style={styles.inputRightText}
                                        placeholder={'请输入联系人'}
                                        onChangeText={(text) => this.setState({telContactPerson:text})}
                                    >
                                        {this.state.telContactPerson}
                                    </TextInput>
                                </View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>职务</Text>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    </View>                                
                                    <TextInput 
                                        editable={!this.state.isVisit}
                                        style={styles.inputRightText}
                                        placeholder={'请输入职务'}
                                        onChangeText={(text) => this.setState({telJobTitle:text})}
                                    >
                                        {this.state.telJobTitle}
                                    </TextInput>
                                </View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    </View>                                
                                    <TextInput 
                                        editable={!this.state.isVisit}
                                        style={styles.inputRightText}
                                        placeholder={'请输联系电话'}
                                        onChangeText={(text) => this.setState({telContactTel:text})}
                                    >
                                        {this.state.telContactTel}
                                    </TextInput>
                                </View>   
                                
                                <View>
                                    {
                                        (this.state.operateResultDataSource && this.state.operateResultDataSource.length > 0)
                                            ?
                                            <View>
                                                <View style={styles.inputRowStyle}>
                                                    <View style={styles.leftLabView}>
                                                        <Text style={styles.leftLabNameTextStyle}>电邀结果</Text>
                                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                                    </View>
                                                </View>
                                                <View style={{ width: screenWidth-40, flexWrap: 'wrap', flexDirection: 'row' }}>
                                                    {
                                                        (this.state.operateResultDataSource && this.state.operateResultDataSource.length > 0)
                                                            ?
                                                            this.state.operateResultDataSource.map((item, index) => {
                                                                return this.renderOperateResultRow(item)
                                                            })
                                                            : <EmptyRowViewComponent />
                                                    }
                                                </View>
                                            </View>
                                            : <View />
                                    }
                                </View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>电邀意见</Text>
                                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                    </View>                                
                                    {/* <TextInput 
                                        style={styles.inputRightText}
                                        placeholder={'请输入电邀意见'}
                                        onChangeText={(text) => this.setState({telOperateOpinion:text})}
                                    >
                                        {this.state.telOperateOpinion}
                                    </TextInput> */}
                                </View>
                                <View style={[styles.inputRowStyle,{height:105}]}>
                                    <TextInput
                                        editable={!this.state.isVisit} 
                                        multiline={true}
                                        textAlignVertical="top"
                                        style={[CommonStyle.inputRowText,{height:100,width:screenWidth - 70}]}
                                        placeholder={'请输入电邀意见'}
                                        onChangeText={(text) => this.setState({telOperateOpinion:text})}
                                    >
                                        {this.state.telOperateOpinion}
                                    </TextInput>
                                </View>
                            </ScrollView>
                            <View style={CommonStyle.alignCenterStyle}>
                                <Text style={[CommonStyle.rowLabRedTextStyle, CommonStyle.boldTextStyle]}>{this.state.errorMsg}</Text>
                            </View>
                            <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                <TouchableOpacity onPress={() => { 
                                    this.setState({
                                        modal:false,
                                        errorMsg:"",
                                        isVisit:false
                                    }) 
                                }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                        <Image  style={{width:22, height:22,marginRight:10}} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={
                                    this.saveTel.bind(this)
                                    }>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                        <Image  style={{width:25, height:25,marginRight:10}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                        <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>保存</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>      
                    </KeyboardAvoidingView>
                </Modal>
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.detailModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle,{height:40,  backgroundColor:'#3ab240',borderRadius:5, justifyContent:'center',alignItems:'center',marginTop:10}]}>
                                <Text style={[styles.titleTextStyle,{fontSize:18,fontWeight:'bold',color:"#ffffff"}]}>线索详情</Text>
                            </View>
                            <ScrollView>
                                {
                                    this.state.detailDTO.customerLeadDTOList && this.state.detailDTO.customerLeadDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>线索</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>客户名称：{this.state.detailDTO.customerName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.customerLeadDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职位：{this.state.detailDTO.customerLeadDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.customerLeadDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>客户简介：{this.state.detailDTO.customerLeadDTOList[0].customerSummary?this.state.detailDTO.customerLeadDTOList[0].customerSummary:"无"}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>备注说明：{this.state.detailDTO.customerLeadDTOList[0].remark?this.state.detailDTO.customerLeadDTOList[0].remark:"无"}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.customerLeadDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.customerLeadDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                {
                                    this.state.detailDTO.auditLeadOperateDTOList && this.state.detailDTO.auditLeadOperateDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>审核</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.auditLeadOperateDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职位：{this.state.detailDTO.auditLeadOperateDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.auditLeadOperateDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>审核结果：{this.state.detailDTO.auditLeadOperateDTOList[0].operateResultName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>审核意见：{this.state.detailDTO.auditLeadOperateDTOList[0].operateOpinion}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.auditLeadOperateDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.auditLeadOperateDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                {
                                    this.state.detailDTO.telLeadOperateDTOList && this.state.detailDTO.telLeadOperateDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>电邀</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.telLeadOperateDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职位：{this.state.detailDTO.telLeadOperateDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.telLeadOperateDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>电邀结果：{this.state.detailDTO.telLeadOperateDTOList[0].operateResultName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>电邀意见：{this.state.detailDTO.telLeadOperateDTOList[0].operateOpinion}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.telLeadOperateDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.telLeadOperateDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                {
                                    this.state.detailDTO.visitLeadOperateDTOList && this.state.detailDTO.visitLeadOperateDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>拜访</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.visitLeadOperateDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职位：{this.state.detailDTO.visitLeadOperateDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.visitLeadOperateDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>拜访结果：{this.state.detailDTO.visitLeadOperateDTOList[0].operateResultName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>拜访意见：{this.state.detailDTO.visitLeadOperateDTOList[0].operateOpinion}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.visitLeadOperateDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.visitLeadOperateDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                {
                                    this.state.detailDTO.slSaleOpportunityDTOList && this.state.detailDTO.slSaleOpportunityDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>商机</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>商机名称：{this.state.detailDTO.slSaleOpportunityDTOList[0].opportunityName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.slSaleOpportunityDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.slSaleOpportunityDTOList[0].gmtCreated}</Text>
                                        </View>
                                        {
                                            this.state.detailDTO.slSaleOpportunityDTOList[0].slSaleOpportunityTrackDTOList && this.state.detailDTO.slSaleOpportunityDTOList[0].slSaleOpportunityTrackDTOList.length > 0
                                            ?
                                            <View>
                                                {
                                                    this.state.detailDTO.slSaleOpportunityDTOList[0].slSaleOpportunityTrackDTOList.map((trackItem,index)=>{
                                                        return(
                                                            <View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0,fontWeight:'bold'}]}>进展{index + 1}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>联系人：{trackItem.contactPerson}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>职务：{trackItem.contactPerson}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>联系电话：{trackItem.contactTel}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>进展说明：{trackItem.trackExplain}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>提交人：{trackItem.userName}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>提交时间：{trackItem.gmtCreated}</Text>
                                                                </View>
                                                            </View>
                                                        )
                                                    })
                                                }
                                            </View>
                                            :
                                            <View>
                                            </View>
                                        }
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }


                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    
                                    this.setState({
                                        detailModal:false
                                    })
                                }}>
                                <View style={[styles.btnRowLeftCancelBtnView]} >
                                {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                    <Text  style={[styles.titleTextStyle,{ fontWeight: 'bold',fontSize:18,color:'#a1a1a1'}]}>返       回</Text>
                                </View>
                                </TouchableOpacity>  
                            </View>
                        </View>
                    </View>
                </Modal>
                <BottomScrollSelect 
                    ref={'SelectGmtCreated'} 
                    callBackDateValue={this.callBackSelectGmtCreatedValue.bind(this)}
                />
        </View>
        )
    }
}
const styles = StyleSheet.create({
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    btnRowLeftCancelBtnView:{
        flexDirection:'row',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
        alignItems:'center',
        justifyContent:'center',
        borderWidth:1,
        borderColor:'#a1a1a1',
        borderRadius:5,
        height:40,        
    },
    fullScreenKeepOut:{
        height:screenHeight, 
        width:screenWidth,
        backgroundColor:'rgba(169,169,169,0.95)',
        alignItems:'center',
        justifyContent:'center',
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        marginTop:5,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        // width:screenWidth - 180,
        marginLeft:20
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 65),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputOutsideText:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },
    inputInsideText:{
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    }
});