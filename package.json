{"name": "sp_mobile_front", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"@ant-design/icons-react-native": "^2.3.2", "@react-native-community/async-storage": "^1.12.1", "@react-native-community/cameraroll": "^4.1.2", "@react-native-community/datetimepicker": "^5.0.1", "@react-native-community/masked-view": "^0.1.6", "@react-native-community/slider": "^3.0.3", "@react-navigation/bottom-tabs": "^5.11.15", "@react-navigation/drawer": "^5.12.9", "@react-navigation/material-bottom-tabs": "^5.3.19", "@react-navigation/material-top-tabs": "^5.3.19", "@react-navigation/native": "^5.9.8", "@react-navigation/stack": "^5.14.9", "moment": "^2.29.1", "react": "17.0.1", "react-native": "0.64.4", "react-native-calendars": "^1.1272.0", "react-native-check-box": "^2.1.7", "react-native-device-info": "^8.4.6", "react-native-elements": "^2.3.2", "react-native-fs": "^2.18.0", "react-native-gesture-handler": "^1.10.3", "react-native-image-picker": "^4.3.0", "react-native-image-zoom-viewer": "^3.0.1", "react-native-linear-gradient": "^2.5.6", "react-native-pager-view": "^5.4.9", "react-native-paper": "^4.10.1", "react-native-picker": "^4.3.7", "react-native-pwd-input": "^1.0.1", "react-native-reanimated": "^1.13.3", "react-native-safe-area-context": "^3.3.2", "react-native-screens": "^2.18.1", "react-native-smart-tip": "^2.3.0", "react-native-swiper": "^1.6.0", "react-native-tab-navigator": "^0.3.4", "react-native-tab-view": "^3.1.1", "react-native-vector-icons": "^7.1.0", "react-native-video": "^5.2.0", "react-native-webview": "^11.14.2", "react-native-wechat-lib": "^1.1.26"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/runtime": "^7.16.3", "@react-native-community/eslint-config": "^2.0.0", "babel-jest": "^26.6.3", "eslint": "^7.32.0", "jest": "^26.6.3", "metro-react-native-babel-preset": "^0.63.0", "react-test-renderer": "16.13.1"}, "jest": {"preset": "react-native"}}