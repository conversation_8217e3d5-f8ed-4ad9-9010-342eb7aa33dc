import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, Modal , StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image,Alert } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class HLMedicineStorageOutAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            storageOutId:"",
            selectStorageOutDate: [],
            selectMedicineStorageOutDate: [],
            hlMedicineStorageOutDetailDTOList:[],
            diagnosisResult:"",
            storageOutDate:"",
            selSickPersonName:"",
            selSickPersonId:"",
            sickPersonAge:"",
            gender:"",
            sickPersonDataSource:[],
            _sickPersonDataSource:[],
            sickPersonModal:false,
            selDoctorId:"",
            selDoctorName:"",
            doctorDataSource:[],
            _doctorDataSource:[],
            doctorModal:false,
            selPharmacyId:"",
            selPharmacyName:"",
            pharmacyAddr:"",
            selectPharmacyName:[],
            pharmacyDataSource:[], 

            //院区
            selHospitalId:"",
            selHospitalName:"",
            selectHospitalName:[],
            hospitalDataSource:[], 
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadSickPersonData();
        this.loadDoctorData();
         //加载院区列表
         this.loadHospitalData();
        // this.loadPharmacyData();
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageOutId } = route.params;
            if (storageOutId) {
                console.log("=============storageOutId" + storageOutId + "");
                this.setState({
                    storageOutId:storageOutId,
                    operate:"编辑",
                })
                let loadTypeUrl = "/biz/hl/medicine/storage/out/get";
                let loadRequest = { 'storageOutId': storageOutId };
                httpPost(loadTypeUrl, loadRequest, this.loadMedicineStorageOutDDCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                    // operator:constants.loginUser.userName,
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectStorageOutDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    storageOutDate: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                })
            }        
        }
    }

    loadHospitalData=()=>{
        let url= "/biz/hl/hospital/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    hospitalDataSource: response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    openHospitalName() {
        if (!this.state.hospitalDataSource || this.state.hospitalDataSource.length < 1) {
            WToast.show({ data: "请先添加院区" });
            return
        }
        this.refs.SelectHospitalName.showHospital(this.state.selectHospitalName, this.state.hospitalDataSource)
    }

    callBackSelectHospitalValue(value) {
        console.log("==========院区选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selPharmacyName:"",
            pharmacyAddr:"",
            // selectPharmacyName:"",
            selectHospitalName: value,
            selHospitalName:value.toString(),
    
        })
        var hospitalName = value.toString();
        let loadUrl = "/biz/hl/hospital/getHospitalByName";
        let loadRequest = {
            "hospitalName": hospitalName
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    selHospitalName:response.data.hospitalName,
                    selHospitalId:response.data.hospitalId,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({data:response.message});
            }

            //记载药房列表
            this.loadPharmacyData(response.data.hospitalId)

        }

        );
    }

    loadPharmacyData=(hospitalId)=>{
        let url= "/biz/hl/pharmacy/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            "hospitalId":hospitalId
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    pharmacyDataSource: response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    openPharmacyName() {
        if (!this.state.selHospitalId){
            WToast.show({ data: "请先选择院区" });
            return
        }
        if (!this.state.pharmacyDataSource || this.state.pharmacyDataSource.length < 1) {
            WToast.show({ data: "请先添加药房" });
            return
        }
        this.refs.SelectPharmacyName.showPharmacy(this.state.selectPharmacyName, this.state.pharmacyDataSource)
    }

    callBackSelectPharmacyValue(value) {
        console.log("==========药房选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectPharmacyName: value,
            selPharmacyName:value.toString()
        })
        var pharmacyName = value.toString();
        let loadUrl = "/biz/hl/pharmacy/getPharmacyByName";
        let loadRequest = {
            "pharmacyName": pharmacyName,
            "hospitalId":this.state.selHospitalId,
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    selPharmacyName:response.data.pharmacyName,
                    selPharmacyId:response.data.pharmacyId,
                    selHospitalId:response.data.hospitalId,
                    pharmacyAddr:response.data.pharmacyAddr,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({data:response.message});
            }
        });
    }

    loadSickPersonData=()=>{
        let loadUrl= "/biz/hl/sick/person/list";
        let loadRequest={'currentPage':1,'pageSize':100};
        httpPost(loadUrl, loadRequest, this.loadSickPersonDataCallBack);
    }
        
    loadSickPersonDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                sickPersonDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadSickPerson = () => {
        var _sickPersonDataSource = copyArr(this.state.sickPersonDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _sickPersonDataSource = _sickPersonDataSource.filter(item => item.sickPersonName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _sickPersonDataSource: _sickPersonDataSource,
        })
    }

    renderSickPersonItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.detailId) {
                    return;
                }
                this.setState({
                    selSickPersonId:item.sickPersonId,
                    selSickPersonName:item.sickPersonName,
                    gender:item.gender
                })
                
            }}>
                <View key={item.sickPersonId} style={item.sickPersonId===this.state.selSickPersonId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.sickPersonId===this.state.selSickPersonId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.sickPersonName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    loadDoctorData=()=>{
        let loadUrl= "/biz/hl/doctor/list";
        let loadRequest={'currentPage':1,'pageSize':100};
        httpPost(loadUrl, loadRequest, this.loadDoctorDataCallBack);
    }
        
    loadDoctorDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                doctorDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    
    loadDoctor = () => {
        var _doctorDataSource = copyArr(this.state.doctorDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _doctorDataSource = _doctorDataSource.filter(item => item.doctorName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _doctorDataSource: _doctorDataSource,
        })
    }
    
    renderDoctorItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.detailId) {
                    return;
                }
                this.setState({
                    selDoctorId:item.doctorId,
                    selDoctorName:item.doctorName,
                })
                
            }}>
                <View key={item.doctorId} style={item.doctorId===this.state.selDoctorId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.doctorId===this.state.selDoctorId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.doctorName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    loadMedicineStorageOutDDCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectStorageOutDate = response.data.storageOutDate.split("-");
            this.setState({
                storageOutId:response.data.storageOutId,
                storageOutDate:response.data.storageOutDate,
                selSickPersonId: response.data.sickPersonId,
                selSickPersonName: response.data.sickPersonName,
                sickPersonAge: response.data.sickPersonAge,
                gender:response.data.gender,
                diagnosisResult: response.data.diagnosisResult,
                selDoctorId:response.data.doctorId,
                selDoctorName: response.data.doctorName,
                selPharmacyId:response.data.pharmacyId,
                selPharmacyName:response.data.pharmacyName,
                selectPharmacyName:[response.data.pharmacyName],
                pharmacyAddr:response.data.pharmacyAddr,
                selHospitalId:response.data.hospitalId,
                selectStorageOutDate:selectStorageOutDate,

                hospitalName:response.data.hospitalName,
                selHospitalName:response.data.hospitalName,
                selectHospitalName:[response.data.hospitalName]
            })
            if (response.data.hlMedicineStorageOutDetailDTOList && response.data.hlMedicineStorageOutDetailDTOList.length > 0) {
                this.setState({
                    // 出库详细
                    hlMedicineStorageOutDetailDTOList:response.data.hlMedicineStorageOutDetailDTOList,
                })
            }
            // console.log("--------明细"+ hlStorageOutDetailDTOList)
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("HLMedicineStorageOutList")
            }}>
                <Text style={CommonStyle.headRightText}>药品出库</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }
    saveMedicineStorageOutDD = () => {
        console.log("=======saveMedicineStorageOutDD");
        let toastOpts;
        
        if (!this.state.storageOutDate) {
            toastOpts = getFailToastOpts("请选择出库日期");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selSickPersonId) {
            toastOpts = getFailToastOpts("请选择病患");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.sickPersonAge) {
        //     toastOpts = getFailToastOpts("请填写病患年龄");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.diagnosisResult) {
            toastOpts = getFailToastOpts("请填写医疗诊断");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selDoctorId) {
            toastOpts = getFailToastOpts("请选择开立医生");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selHospitalId) {
            toastOpts = getFailToastOpts("请选择院区");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selPharmacyId) {
            toastOpts = getFailToastOpts("请选择药房");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.hlMedicineStorageOutDetailDTOList || this.state.hlMedicineStorageOutDetailDTOList.length < 1) {
            toastOpts = getFailToastOpts("至少新增一条明细");
            WToast.show(toastOpts)
            return;
        }

        let url = "/biz/hl/medicine/storage/out/add";
        if (this.state.storageOutId) {
            console.log("=========Edit===storageOutId", this.state.storageOutId)
            url = "/biz/hl/medicine/storage/out/modify";
        }

        let requestParams = {
            storageOutId:this.state.storageOutId,
            sickPersonId: this.state.selSickPersonId,
            sickPersonAge: this.state.sickPersonAge,
            diagnosisResult: this.state.diagnosisResult,
            storageOutDate:this.state.storageOutDate,
            doctorId:this.state.selDoctorId,
            pharmacyId:this.state.selPharmacyId,
            hospitalId:this.state.selHospitalId,
            hlMedicineStorageOutDetailDTOList:this.state.hlMedicineStorageOutDetailDTOList,
        };
        httpPost(url, requestParams, this.saveMedicineStorageOutDDCallBack);
    }

    // 保存回调函数
    saveMedicineStorageOutDDCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    openMedicineStorageOutDate() {
        this.refs.SelectStorageOutDate.showDate(this.state.selectStorageOutDate)
    }
    callBackSelectStorageOutDateValue(value) {
        console.log("==========出库日期选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectStorageOutDate: value
        })
        if (value && value.length) {
            var storageOutDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    storageOutDate += vartime;
                }
                else{
                    storageOutDate += "-" + vartime;
                }
            }
            this.setState({
                storageOutDate: storageOutDate
            })
        }
        var dateString = this.state.signingTime + ' 00:00:01';
        dateString = dateString.substring(0, 19);
        dateString = dateString.replace(/-/g, '/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var SevenDaysLast = new Date(dateStringTimestamp);
        // 用获取毫秒数 加上30天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        SevenDaysLast.setTime(dateStringTimestamp + (30 * 86400000));
        //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
        //格式化月，如果小于9，前面补0  
        var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0  
        var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
        this.setState({
            selectDeliveryDate: [SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
            deliveryDate: SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
        })
        if (this.state.selectDeliveryDate && this.state.selectDeliveryDate.length) {
            var deliveryDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectDeliveryDate.length; index++) {
                vartime = this.state.selectDeliveryDate[index];
                if (index === 0) {
                    deliveryDate += vartime;
                }
                else {
                    deliveryDate += "-" + vartime;
                }
            }
            this.setState({
                deliveryDate: deliveryDate
            })
        }
    }

    _loadFreshHlMedicineStorageOutDetailDTOList=(_hlMedicineStorageOutDetailDTOList)=>{
        if (_hlMedicineStorageOutDetailDTOList && _hlMedicineStorageOutDetailDTOList.length > 0) {
            console.log("=========回退数据：", _hlMedicineStorageOutDetailDTOList);
            this.setState({
                hlMedicineStorageOutDetailDTOList:_hlMedicineStorageOutDetailDTOList,
            })
        }
        else {
            console.log("=========回退不成功");
        }
    }

    render(){
        return(
            <ScrollView style={[CommonStyle.contentViewStyle]}>
                <CommonHeadScreen title={this.state.operate + '出库'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>出库日期</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TouchableOpacity onPress={() => this.openMedicineStorageOutDate()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 30)}]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.storageOutDate ? "请选择出库日期" : this.state.storageOutDate}
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>
                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>病患姓名</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[(!this.state.sickPersonDataSource || this.state.sickPersonDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={()=>{
                            if (this.state.sickPersonDataSource && this.state.sickPersonDataSource.length > 0) {
                                this.setState({
                                    _sickPersonDataSource: copyArr(this.state.sickPersonDataSource),
                                })
                            }
                            if (!this.state.sickPersonDataSource || this.state.sickPersonDataSource.length === 0) {
                                
                                let errorMsg = '暂无病患';
                                Alert.alert('确认', errorMsg, [
                                    {
                                        text: "确定", onPress: () => {
                                            WToast.show({ data: '点击了确定' });
                                        }
                                    }
                                ]);
                                return;
                            }

                            this.setState({
                                sickPersonModal:true,
                                searchKeyWord: ""
                            })

                            if (!this.state.selSickPersonId && this.state.sickPersonDataSource && this.state.sickPersonDataSource.length > 0) {
                                this.setState({
                                    selSickPersonId:this.state.sickPersonDataSource[0].sickPersonId,
                                    selSickPersonName:this.state.sickPersonDataSource[0].sickPersonName,
                                    gender:this.state.sickPersonDataSource[0].gender,
                                })
                            }
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}]}>
                                <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                                    {this.state.selSickPersonId && this.state.selSickPersonName ? (this.state.selSickPersonName) : "选择病患"}
                                </Text>
                            </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.sickPersonModal}>
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={CommonStyle.modalContentViewStyle}>
                            <View style={CommonStyle.rowLabView}>
                                <View style={styles.leftLabViewImage}>
                                    <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                    <TextInput
                                        style={[styles.searchInputText]}
                                        returnKeyType="search"
                                        returnKeyLabel="搜索"
                                        onSubmitEditing={e => {
                                            this.loadSickPerson();
                                    }}        
                                        placeholder={'病患姓名'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                </View>
                            </View>
                            <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._sickPersonDataSource && this.state._sickPersonDataSource.length > 0)
                                                ?
                                                this.state._sickPersonDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderSickPersonItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                            <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                <TouchableOpacity onPress={() => { 
                                    this.setState({
                                        sickPersonModal:false,
                                    }) 
                                }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                    <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => {
                                    if (!this.state.selSickPersonId) {
                                        let toastOpts = getFailToastOpts("您还没有选择患者");
                                        WToast.show(toastOpts);
                                        return;
                                    }
                                    this.setState({
                                        sickPersonModal: false,
                                    })
                                }}>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                    <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                        <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>性别</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.gender ? "请先选择病患" : (this.state.gender == "M" ? "男" : "女")}
                            </Text>
                    </View>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>年龄</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入年龄'}
                            onChangeText={(text) => this.setState({ sickPersonAge: text })}
                        >
                            {this.state.sickPersonAge}
                        </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>医疗诊断</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入医疗诊断'}
                            onChangeText={(text) => this.setState({ diagnosisResult: text })}
                        >
                            {this.state.diagnosisResult}
                        </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>开立医生</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[(!this.state.doctorDataSource || this.state.doctorDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={()=>{
                            if (this.state.doctorDataSource && this.state.doctorDataSource.length > 0) {
                                this.setState({
                                    _doctorDataSource: copyArr(this.state.doctorDataSource),
                                })
                            }
                            if (!this.state.doctorDataSource || this.state.doctorDataSource.length === 0) {
                                
                                let errorMsg = '暂无医生';
                                Alert.alert('确认', errorMsg, [
                                    {
                                        text: "确定", onPress: () => {
                                            WToast.show({ data: '点击了确定' });
                                        }
                                    }
                                ]);
                                return;
                            }

                            this.setState({
                                doctorModal:true,
                                searchKeyWord: ""
                            })

                            if (!this.state.selDoctorId && this.state.doctorDataSource && this.state.doctorDataSource.length > 0) {
                                this.setState({
                                    selDoctorId:this.state.doctorDataSource[0].doctorId,
                                    selDoctorName:this.state.doctorDataSource[0].doctorName,
                                })
                            }
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}]}>
                                <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                                    {this.state.selDoctorId && this.state.selDoctorName ? (this.state.selDoctorName) : "选择开立医生"}
                                </Text>
                            </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.doctorModal}>
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={CommonStyle.modalContentViewStyle}>
                            <View style={CommonStyle.rowLabView}>
                                <View style={styles.leftLabViewImage}>
                                    <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                    <TextInput
                                        style={[styles.searchInputText]}
                                        returnKeyType="search"
                                        returnKeyLabel="搜索"
                                        onSubmitEditing={e => {
                                            this.loadDoctor();
                                    }}        
                                        placeholder={'医生姓名'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                </View>
                                {/* <TouchableOpacity onPress={()=>{
                                    this.loadDoctor();
                                    }}>
                                    <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                        <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                    </View>
                                </TouchableOpacity> */}
                            </View>
                            <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._doctorDataSource && this.state._doctorDataSource.length > 0)
                                                ?
                                                this.state._doctorDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderDoctorItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                            <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                <TouchableOpacity onPress={() => { 
                                    this.setState({
                                        doctorModal:false,
                                    }) 
                                }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                    <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => {
                                    if (!this.state.selDoctorId) {
                                        let toastOpts = getFailToastOpts("您还没有选择开立医生");
                                        WToast.show(toastOpts);
                                        return;
                                    }
                                    this.setState({
                                        doctorModal: false,
                                    })
                                }}>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                    <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                        <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>院区名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openHospitalName()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 30) }]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.selHospitalName ? "请选择院区" : this.state.selHospitalName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>药房名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openPharmacyName()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 30) }]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.selHospitalName ? "请选择院区" :(!this.state.selPharmacyName ? "请选择药房" : this.state.selPharmacyName) }
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>药房位置</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {this.state.pharmacyAddr ? this.state.pharmacyAddr : "请选择药房"}
                            </Text>
                    </View>
                </View>
                    
                <View style={CommonStyle.rowSplitViewStyle}></View>
                <View style={styles.btnRowView}>
                    <TouchableOpacity onPress={()=>{
                        if (!this.state.selSickPersonId) {
                            let toastOpts = getFailToastOpts("请您先选择病患");
                            WToast.show(toastOpts);
                            return;
                        }
                        if (!this.state.selHospitalId) {
                            let toastOpts = getFailToastOpts("请您先选择院区");
                            WToast.show(toastOpts);
                            return;
                        }
                        if (!this.state.selPharmacyId) {
                            let toastOpts = getFailToastOpts("请您先选择药房");
                            WToast.show(toastOpts);
                            return;
                        }
                            this.props.navigation.navigate("HLMedicineStorageOutDetailAdd", 
                        {
                            storageOutId:this.state.storageOutId,
                            pharmacyId:this.state.selPharmacyId,
                            hospitalId:this.state.selHospitalId,
                            hlMedicineStorageOutDetailDTOList:this.state.hlMedicineStorageOutDetailDTOList,
                            // 传递回调函数
                            refresh: this._loadFreshHlMedicineStorageOutDetailDTOList 
                        })
                    }}>
                         <View style={[styles.btnAddView]}>
                             <Text style={styles.btnAddText}>+ 出库明细</Text>
                         </View>
                    </TouchableOpacity>
                </View>
                <View>
                    <FlatList 
                    data={this.state.hlMedicineStorageOutDetailDTOList}
                    renderItem={({item}) => 
                    <View key={item._index} style={styles.titleViewStyle}>

                        <View style={{marginTop:10 }}>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                名称：{item.medicineName}
                            </Text>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                规格：{item.medicineSpec}
                            </Text>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                单位：{item.unitName}
                            </Text>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                零售价格：{item.salePrice}
                            </Text>
                        </View>
                        <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10,marginTop:10}]}>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>总量：{item.totalAmount}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>每次用量：{item.everyTimeAmount}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>频率：{item.frequency}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>用法：{item.medicineUsage?item.medicineUsage:"无"}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>金额：{item.moneyAmount}</Text>
                            </View>
                        </View>

                        <TouchableOpacity
                            style={{marginLeft:-145,zIndex:1000,marginTop:130}}
                            onPress={() => {
                                console.log("========deleteStorageInDetailDTO")
                                var urls = this.state.hlMedicineStorageOutDetailDTOList;
                                urls.splice(item._index,1);                   
                                console.log(urls)
                                this.setState({
                                    hlMedicineStorageOutDetailDTOList:urls,
                                })
                            }}
                        >
                            <View style={styles.btnDeleteView}>
                                <Text style={styles.btnDeleteText}>-删除</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    }
                    />
                </View>
                <View style={CommonStyle.btnRowStyle}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveMedicineStorageOutDD.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row', width: 130, height: 40, marginRight: 35, marginTop: 15 }]}>
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                <BottomScrollSelect
                    ref={'SelectStorageOutDate'}
                    callBackDateValue={this.callBackSelectStorageOutDateValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectPharmacyName'} 
                    callBackPharmacyValue={this.callBackSelectPharmacyValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectHospitalName'} 
                    callBackHospitalValue={this.callBackSelectHospitalValue.bind(this)}
                />  
            </ScrollView>
        )
    }
}
const styles = StyleSheet.create({
    searchInputText: {
        width: screenWidth -100,
        // borderColor: '#000000',
        // borderBottomWidth: 1,
        // marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        // marginLeft: 0,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabViewImage: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        borderWidth:1,
        borderColor:"#E4E4E4",
        borderRadius:5,
        marginTop:5,
        // marginRight:5
    },   
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        // marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})