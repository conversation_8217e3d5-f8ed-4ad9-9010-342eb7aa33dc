import React,{Component} from 'react';
import {Alert, View, Text, StyleSheet, Image, FlatList,Dimensions, ScrollView, TouchableOpacity
    ,Clipboard,Linking,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';

// 公共组件及样式
import EmptyListComponent from '../../component/EmptyListComponent';
import CommonHeadScreen from '../../component/CommonHeadScreen';

// 引入公共样式
// import CommonStyle from '../../assets/css/CommonStyle';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class OrderStateTracking extends Component {
    
    constructor(props) {
        super(props);
        this.state = {
            // 订单信息
            orderItem:{},
            dataSource:[],
            orderCheckOutAmount:0,
            orderCheckOutWeight:0,
            moreModal:false,
            deleteModal:false,
        }

    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { orderId, _orderItem } = route.params;
            console.log("=========_orderItem:", _orderItem)
            if (_orderItem) {
                this.setState({
                    orderItem:_orderItem,
                })
            }
            if (orderId) {
                console.log("=======orderId:", orderId);
                this.setState({
                    orderId:orderId
                })
                loadRequest={'orderId':orderId};
                loadTypeUrl= "/biz/order/qryCheckOutAmountSpOrderById";
                httpPost(loadTypeUrl, loadRequest, (response)=>{
                    if (response.code == 200 && response.data) {
                        if (response.data.storageOutAmountSum) {
                            this.setState({
                                orderCheckOutAmount:response.data.storageOutAmountSum,
                            })
                        }
                        if (response.data.storageOutWeightSum) {
                            this.setState({
                                orderCheckOutWeight:response.data.storageOutWeightSum,
                            })
                        }
                        if (response.data.gmtModified) {
                            this.setState({
                                orderGmtModified:response.data.gmtModified,
                            })
                        }
                        
                    }
                });

                loadRequest={'currentPage':1,'pageSize':100,'orderId':orderId};
                loadTypeUrl= "/biz/order/state/record/list";
                httpPost(loadTypeUrl, loadRequest, this.loadOrderStateRecordDataCallBack);
            }
        }
    }

    loadOrderStateRecordDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                dataSource:response.data.dataList
            })
            console.log("==========dataSource:", this.state.dataSource);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                }}  >
                    {/* <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
                </TouchableOpacity>
            </View>
        )
    }

    unitShow=(item)=>{
        if (item.orderStatorOrdinal >= 6) {
           return "完成数量";
        }
        else if (item.orderStatorOrdinal == 0) {
           return "数量";
        }
        else {
           return "完成数量(块)";
        }
    }
    
    deleteOrder =(orderId)=> {
        console.log("=======delete=orderId", orderId);
        let requestUrl= "/biz/order/delete";
        let requestParams={'orderId':orderId};
        httpPost(requestUrl, requestParams, this.callBackDeleteOrder);
    }
    callBackDeleteOrder=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"成功删除"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='订单跟踪'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View>
                    <View key={this.state.orderItem.orderId} style={styles.innerViewStyle}>
                        {/* <View style={styles.titleViewStyle}>
                            <Text style={[styles.titleTextStyle,{}]}>{this.state.orderItem.customerName}</Text>
                        </View> */}

                        <View style={[CommonStyle.newTitleViewStyle]}>
                            <View>
                                <Text style={[CommonStyle.newTitleTextStyle,{width:null,fontSize:20,fontWeight:'bold',color: '#404956'}]} numberOfLines={2}>客户名称：</Text>
                            </View>
                            <View>
                                <Text style={[CommonStyle.newTitleTextStyle,{fontSize:20,fontWeight:'bold',color: '#404956'}]} numberOfLines={2}>{this.state.orderItem.customerName}</Text>
                            </View>
                        </View>
                        {/* <View style={CommonStyle.titleViewStyleSpecial}>
                            <Text style={[CommonStyle.titleTextStyleSpecial,{}]}>客户名称：{this.state.orderItem.customerName + "11111111"}</Text>
                        </View> */}
                        <View style={[styles.titleViewStyle, { position: 'absolute', right: 0, top: 0, flexDirection: 'column' }]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    moreModal: true,
                                    // modalItem:item
                                })
                            }}>
                                <View style={[{ width: 35, height: 35, alignItems: 'center' }]}>
                                    <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                                </View>
                            </TouchableOpacity>
                        </View> 
                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    // if (this.state.dailyItem.dailyState != "0BB" || this.state.dailyItem.auditScore || dateDiffHours(this.state.currentTime, this.state.dailyItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                    //     WToast.show({ data: '该日报不可编辑' });
                                    //     return;
                                    // }
                                    if (dateDiffHours(constants.nowDateTime, this.state.orderItem.gmtCreated) > constants.editDeleteTimeLimit) {
                                        return;
                                    }
                                    this.setState({
                                        moreModal: false,
                                    })
                                    this.props.navigation.navigate("OrderAdd",
                                        {
                                            // 传递参数
                                            orderId: this.state.orderItem.orderId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , dateDiffHours(constants.nowDateTime, this.state.orderItem.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""]}>
                                        {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => {
                                    console.log("dailyItem=================",this.state.dailyItem)
                                    // if (this.state.dailyItem.dailyState != "0BB" && this.state.dailyItem.auditScore) {
                                    //     WToast.show({ data: '日报已审核不可删除' });
                                    //     return;
                                    // }
                                    // if (this.state.dailyItem.dailyState != "0BB" && dateDiffHours(this.state.currentTime, this.state.dailyItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                    //     WToast.show({ data: '日报已超出删除时限' });
                                    //     return;
                                    // }
                                    if (dateDiffHours(constants.nowDateTime, this.state.orderItem.gmtCreated) > constants.editDeleteTimeLimit) {
                                        WToast.show({ data: '该记录不可删除' });
                                        return;
                                    }
                                    // 删除弹窗Modal
                                    this.setState({
                                        moreModal: false,
                                        deleteModal: true
                                    })
                                    
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , dateDiffHours(constants.nowDateTime, this.state.orderItem.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""]}>
                                        {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该订单?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17,  fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteOrder(this.state.orderItem.orderId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                        {/* <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>订单号：{this.state.orderItem.orderCode}</Text>
                        </View> */}
                        <View style={[CommonStyle.newTitleViewStyle]}>
                            <View>
                                <Text style={[CommonStyle.newTitleTextStyle,{width:null}]} numberOfLines={2}>订单名称：</Text>
                            </View>
                            <View>
                                <Text style={[CommonStyle.newTitleTextStyle]} numberOfLines={2}>{this.state.orderItem.orderName}</Text>
                            </View>
                        </View>

                        {/* <View style={CommonStyle.titleViewStyle}>
                            <Text style={[CommonStyle.titleTextStyle,{}]}>订单名称：{this.state.orderItem.orderName}</Text>
                        </View> */}

                        <View style={CommonStyle.titleViewStyle}>
                            <Text style={CommonStyle.titleTextStyle}>订单时间：{this.state.orderItem.gmtCreated}</Text>
                        </View>
                        <View style={[CommonStyle.newTitleViewStyle]}>
                            <View>
                                <Text style={[CommonStyle.newTitleTextStyle,{width:null}]} numberOfLines={2}>所属合同：</Text>
                            </View>
                            <View>
                                <Text style={[CommonStyle.newTitleTextStyle]} numberOfLines={2}>{this.state.orderItem.contractName}</Text>
                            </View>
                        </View>
                        {/* <View style={CommonStyle.titleViewStyle}>
                            <Text style={[CommonStyle.titleTextStyle,{}]}>所属合同：{this.state.orderItem.contractName}</Text>
                        </View> */}

                        {/* <View style={styles.bodyViewStyle}>
                            <View style={styles.bodyRowLeftView} >
                                <Text style={CommonStyle.bodyTextStyle}>砖型：{this.state.orderItem.brickTypeName}</Text>
                            </View>
                            <View style={styles.bodyRowRightView}>
                                <Text style={CommonStyle.bodyTextStyle}>数量：{this.state.orderItem.brickAmount}</Text>
                            </View>
                        </View> */}

                        <View style={[CommonStyle.newTitleViewStyle]}>
                            <View>
                                <Text style={[CommonStyle.newTitleTextStyle,{width:null}]} numberOfLines={2}>砖型：</Text>
                            </View>
                            <View>
                                <Text style={[CommonStyle.newTitleTextStyle]} numberOfLines={2}>{this.state.orderItem.seriesName}-{this.state.orderItem.brickTypeName}</Text>
                            </View>
                        </View>

                        {/* <View style={CommonStyle.titleViewStyle}>
                            <Text style={[CommonStyle.titleTextStyle,{}]}>砖型：{this.state.orderItem.seriesName}-{this.state.orderItem.brickTypeName}</Text>
                        </View> */}
                        <View style={CommonStyle.titleViewStyle}>
                            <Text style={[CommonStyle.titleTextStyle,{}]}>数量：{this.state.orderItem.brickAmount}</Text>
                        </View>
                        <View style={CommonStyle.titleViewStyle}>
                            <Text style={[CommonStyle.titleTextStyle,{}]}>重量：{this.state.orderItem.orderWeight}</Text>
                        </View>

                        {/* <View style={styles.bodyViewStyle}>
                            <View style={styles.bodyRowLeftView} >
                                <Text style={CommonStyle.bodyTextStyle}>预计生产时间：{this.state.orderItem.schedulingProductionTime}</Text>
                            </View>
                            <View style={styles.bodyRowRightView}>
                                <Text style={CommonStyle.bodyTextStyle}>预计完成时间：{this.state.orderItem.schedulingCompletedTime}</Text>
                            </View>
                        </View> */}
                        <View style={CommonStyle.titleViewStyle}>
                            <Text style={[CommonStyle.titleTextStyle,{}]}>预计生产时间：{this.state.orderItem.schedulingProductionTime}</Text>
                        </View>
                        <View style={CommonStyle.titleViewStyle}>
                            <Text style={[CommonStyle.titleTextStyle,{}]}>预计完成时间：{this.state.orderItem.schedulingCompletedTime}</Text>
                        </View>
                        {/* <View style={styles.bodyViewStyle}>
                            <View style={styles.bodyRowLeftView} >
                                <Text style={CommonStyle.bodyTextStyle}>联系人：{this.state.orderItem.orderContact}</Text>
                            </View>
                            <View style={styles.bodyRowRightView}>
                                <Text style={CommonStyle.bodyTextStyle}>联系电话：{this.state.orderItem.orderContactTel}</Text>
                            </View>
                        </View> */}
                        {/* <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{color:'red',fontWeight:'bold'}]}>订单当前进展：{this.state.orderItem.orderStateName}</Text>
                        </View> */}
                        <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                            <TouchableOpacity onPress={()=>{
                                let netWork = constants.service_addr + '/html/order/track.html?orderCode=' + this.state.orderItem.orderCode;
                                Clipboard.setString(netWork);
                                WToast.show({data:"访问网址:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + netWork});
                                Alert.alert('确认','访问网址已复制到粘贴板，使用浏览器打开:\n' + netWork + ' ?',[
                                    {
                                        text:"不打开", onPress:()=>{
                                        WToast.show({data:'点击了不打开'});
                                        }
                                    },
                                    {
                                        text:"打开", onPress:()=>{
                                            WToast.show({data:'点击了打开'});
                                            // 直接打开外网链接 
                                            Linking.openURL(netWork)
                                        }
                                    }
                                ]);
                            }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {width:75,flexDirection:"row", backgroundColor:'#1E6EFA'}]}>
                                <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/share.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>分享</Text>
                            </View>
                            </TouchableOpacity>
                            {/* <TouchableOpacity onPress={()=>{
                                if (dateDiffHours(constants.nowDateTime, this.state.orderItem.gmtCreated) > constants.editDeleteTimeLimit) {
                                    return;
                                }
                                Alert.alert('确认','您确定要删除该订单吗？',[
                                    {
                                        text:"取消", onPress:()=>{
                                        WToast.show({data:'点击了取消'});
                                        // this在这里可用，传到方法里还有问题
                                        // this.props.navigation.goBack();
                                        }
                                    },
                                    {
                                        text:"确定", onPress:()=>{
                                            WToast.show({data:'点击了确定'});
                                            this.deleteOrder(this.state.orderItem.orderId)
                                        }
                                    }
                                ]);
                                }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:75,flexDirection:"row"}
                                ,dateDiffHours(constants.nowDateTime, this.state.orderItem.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""]}>
                                    <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                </View>
                            </TouchableOpacity> */}
                            
                            {/* <TouchableOpacity onPress={()=>{
                                if (dateDiffHours(constants.nowDateTime, this.state.orderItem.gmtCreated) > constants.editDeleteTimeLimit) {
                                    return;
                                }
                                    this.props.navigation.navigate("OrderAdd", 
                                    {
                                        orderId:this.state.orderItem.orderId,
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                                <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:75,flexDirection:"row"}
                                ,dateDiffHours(constants.nowDateTime, this.state.orderItem.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                                ]}>
                                    <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                    <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                </View>
                            </TouchableOpacity> */}
                        </View>
                    </View>
                    <View>
                        {
                            (this.state.orderCheckOutAmount && this.state.orderCheckOutAmount > 0) ? 
                            <View style={styles._innerViewStyle}>
                                <View style={[CommonStyle.titleViewStyle]}>
                                    <Text style={[CommonStyle.titleTextStyle,{fontWeight:'bold', fontSize:18}]}>开始装车发运</Text>
                                </View>
                                <View style={CommonStyle.titleViewStyle}>
                                    <Text style={CommonStyle.titleTextStyle}>发货数量(块)：{this.state.orderCheckOutAmount}</Text>
                                </View>
                                <View style={CommonStyle.titleViewStyle}>
                                    <Text style={CommonStyle.titleTextStyle}>发货重量(吨)：{this.state.orderCheckOutWeight}</Text>
                                </View>
                                {/* <View style={styles._titleViewStyle}>
                                    <Text style={styles._titleTextStyle}>发货重量(吨)：{this.state.orderItem.standardWeight ? (this.state.orderCheckOutAmount * this.state.orderItem.standardWeight / 1000).toFixed(2)  : '-'}</Text>
                                </View> */}
                                <View style={CommonStyle.titleViewStyle}>
                                    <Text style={CommonStyle.titleTextStyle}>未发货数量(块)：{this.state.orderItem.brickAmount ? (this.state.orderItem.brickAmount - this.state.orderCheckOutAmount) : '-'}</Text>
                                </View>
                                <View style={CommonStyle.titleViewStyle}>
                                    <Text style={CommonStyle.titleTextStyle}>更新时间：{this.state.orderGmtModified}</Text>
                                </View>
                            </View>
                            :
                            <View/>
                        }
                        
                    {this.state.dataSource.map((item, index)=>{
                            if (index == (this.state.dataSource.length - 1)) {
                                return(
                                    <View key={item.checkId} style={styles._innerViewStyle}>
                                        <View style={[CommonStyle.titleViewStyle]}>
                                            <Text style={[CommonStyle.titleTextStyle,{fontWeight:'bold', fontSize:18}]}>{item.orderStateName}</Text>
                                        </View>
                                        {
                                            (item.completeNumber != 0) ? 
                                            <View style={CommonStyle.titleViewStyle}>
                                                <Text style={CommonStyle.titleTextStyle}>{this.unitShow(item)}：{item.completeNumber}</Text>
                                            </View> :
                                            <View>
                                                <View style={CommonStyle.titleViewStyle}>
                                                    <Text style={CommonStyle.titleTextStyle}>预计生产时间：{this.state.orderItem.schedulingProductionTime}</Text>
                                                </View>
                                                <View style={CommonStyle.titleViewStyle}>
                                                    <Text style={CommonStyle.titleTextStyle}>预计完成时间：{this.state.orderItem.schedulingCompletedTime}</Text>
                                                </View>
                                            </View>
                                        }
                                        <View style={CommonStyle.titleViewStyle}>
                                            <Text style={CommonStyle.titleTextStyle}>重量：{this.state.orderItem.orderWeight}</Text>
                                        </View>
                                        <View style={CommonStyle.titleViewStyle}>
                                            <Text style={CommonStyle.titleTextStyle}>{item.gmtCreated}</Text>
                                        </View>
                                    </View>
                                )
                            }
                            else {
                                return(
                                    <View key={item.checkId} style={styles._innerViewStyle}>
                                        <View style={[CommonStyle.titleViewStyle]}>
                                            <Text style={[CommonStyle.titleTextStyle,{fontWeight:'bold', fontSize:18}]}>{item.orderStateName}</Text>
                                        </View>
                                        {
                                            (item.completeNumber != 0) ? 
                                            <View style={CommonStyle.titleViewStyle}>
                                                <Text style={CommonStyle.titleTextStyle}>{this.unitShow(item)}：{item.completeNumber}</Text>
                                            </View> :
                                            <View>
                                                <View style={CommonStyle.titleViewStyle}>
                                                    <Text style={CommonStyle.titleTextStyle}>预计生产时间：{this.state.orderItem.schedulingProductionTime}</Text>
                                                </View>
                                                <View style={CommonStyle.titleViewStyle}>
                                                    <Text style={CommonStyle.titleTextStyle}>预计完成时间：{this.state.orderItem.schedulingCompletedTime}</Text>
                                                </View>
                                            </View>
                                        }
                                        <View style={CommonStyle.titleViewStyle}>
                                            <Text style={CommonStyle.titleTextStyle}>{item.gmtCreated}</Text>
                                        </View>
                                    </View>
                                )
                            }
                        })
                    }
                    </View>

                    {/* <TimeAxis
                            itemStyle={{}}
                            rowHeight={60}
                            dataSource={this.state.dataSource}
                            row={(rowData, i, count) => {
                                var fontColor = '#757575';
                                if (i == 0) {
                                    fontColor = 'green';
                                }
                                return (
                                    
                                );
                            }}
                        /> */}
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    
    _innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:0,
        marginBottom:15,
    },
    _titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    _titleTextStyle:{
        fontSize:16
    },

    innerViewStyle:{
        marginTop:10,
        backgroundColor:"#FFFFFF",
        marginBottom:10,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10
    },

    titleTextStyle:{
        fontSize:18
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyRowView:{
        flexDirection:'row', 
        // backgroundColor:'yellow'
    },
    bodyRowLeftView:{
        width:screenWidth/2-40, 
        flexDirection:'row', 
        // backgroundColor:'yellow'
    },
    bodyRowRightView:{
        // backgroundColor:'green', 
        flexDirection:'row', 
        alignItems:'flex-start',
        paddingLeft:10,
        marginRight:5, 
        // alignItems:'flex-start', 
        justifyContent:'flex-start',
        alignContent:'flex-start'
    },
});