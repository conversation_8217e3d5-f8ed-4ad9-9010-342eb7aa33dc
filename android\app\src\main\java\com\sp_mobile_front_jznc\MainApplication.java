package com.sp_mobile_front_jznc;

import android.app.Application;
import android.content.Context;
import android.graphics.Color;
import android.os.Build;

import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;
import com.facebook.soloader.SoLoader;
import java.lang.reflect.InvocationTargetException;
import java.io.File;
import java.util.List;
import com.imagepicker.ImagePickerPackage;

import androidx.core.content.ContextCompat;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import com.alibaba.sdk.android.push.CloudPushService;
import com.alibaba.sdk.android.push.CommonCallback;
import com.alibaba.sdk.android.push.huawei.HuaWeiRegister;
import com.alibaba.sdk.android.push.noonesdk.PushServiceFactory;
import com.alibaba.sdk.android.push.register.MiPushRegister;
import com.alibaba.sdk.android.push.register.OppoRegister;
import com.alibaba.sdk.android.push.register.VivoRegister;
import com.sp_mobile_front_jznc.push.PushModule;
import com.sp_mobile_front_jznc.push.PushPackage;


public class MainApplication extends Application implements ReactApplication {

  private final ReactNativeHost mReactNativeHost =
      new ReactNativeHost(this) {
        @Override
        public boolean getUseDeveloperSupport() {
          return BuildConfig.DEBUG;
        }

        @Override
        protected List<ReactPackage> getPackages() {
          @SuppressWarnings("UnnecessaryLocalVariable")
          List<ReactPackage> packages = new PackageList(this).getPackages();
          // Packages that cannot be autolinked yet can be added manually here, for example:
          packages.add(new PushPackage());
          return packages;
        }

        @Override
        protected String getJSMainModuleName() {
          return "index";
        }
      };

  @Override
  public ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }

  // @Override
  public ImagePickerPackage getImagePickerPackage() {
    return new ImagePickerPackage();
  }

  @Override
  public void onCreate() {
    super.onCreate();
    SoLoader.init(this, /* native exopackage */ false);

    PushServiceFactory.init(this);
    initNotificationChannel();
    initCloudChannel();

    // initializeFlipper(this, getReactNativeHost().getReactInstanceManager());


    
  }

  /**
     * 初始化通知渠道
     */
    private void initNotificationChannel() {
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
          NotificationManager mNotificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
          // 通知渠道的id。
          String id = "1";
          // 用户可以看到的通知渠道的名字。
          CharSequence name = "notification channel(jizhigaoke)";
          // 用户可以看到的通知渠道的描述。
          String description = "notification description-descriptionn(jizhigaoke)";
          int importance = NotificationManager.IMPORTANCE_HIGH;
          NotificationChannel mChannel = new NotificationChannel(id, name, importance);
          // 配置通知渠道的属性。
          mChannel.setDescription(description);
          // 设置通知出现时的闪灯（如果Android设备支持的话）。
          mChannel.enableLights(true);
          mChannel.setLightColor(Color.RED);
          // 设置通知出现时的震动（如果Android设备支持的话）。
          mChannel.enableVibration(true);
          mChannel.setVibrationPattern(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});
          // 最后在notificationmanager中创建该通知渠道。
          mNotificationManager.createNotificationChannel(mChannel);
      }
  }

  private boolean pushInit;
  /**
   * 注册通知
   */
  public void initCloudChannel() {
    File is_privacy = new File(ContextCompat.getDataDir(this).getAbsolutePath(), ContVar.P_FILE);
    System.out.println("================privacyBl:[3]getAbsolutePath:" + is_privacy.getAbsolutePath());
    System.out.println("================privacyBl:[4]exists():" + is_privacy.exists());
    if (!is_privacy.exists()) {
        return;
    }
    if (pushInit) {
        return;
    }

    pushInit = true;

    if (BuildConfig.DEBUG) {
        //仅适用于Debug包，正式包不需要此行
        PushServiceFactory.getCloudPushService().setLogLevel(CloudPushService.LOG_DEBUG);
    }
    

    PushServiceFactory.getCloudPushService().register(this.getApplicationContext(), new CommonCallback() {
        @Override
        public void onSuccess(String s) {
            pushInit = true;
            WritableMap params = Arguments.createMap();
            params.putBoolean("success", true);
            PushModule.sendEvent("onInit", params);
            initCS();
        }

        @Override
        public void onFailed(String s, String s1) {
            pushInit = false;
            WritableMap params = Arguments.createMap();
            params.putBoolean("success", false);
            params.putString("errorMsg", "errorCode:" + s + ". errorMsg:" + s1);
            PushModule.sendEvent("onInit", params);
        }
    });
  }

  /**
   * 初始化厂商
   */
  private void initCS() {
    System.out.println("================initCS()=start");
    // 接入华为辅助推送
    HuaWeiRegister.register(this);
    // 初始化小米辅助推送
    MiPushRegister.register(this, "2882303761519993784", "5771999357784"); 
    // 初始化Vivo辅助推送
   VivoRegister.register(this);
    // 初始化Opop辅助推送
    OppoRegister.register(this, "6bd949dc270b4802bbf48afef230c80b", "5778d7c46f4a480887159cd3686bbe94");
    // MeizuRegister.register(this, "MEIZU_ID", "MEIZU_KEY");
    // GcmRegister.register(this, "send_id", "application_id"); // 接入FCM/GCM初始化推送

    // 获取设备标识
    String deviceId = PushServiceFactory.getCloudPushService().getDeviceId();
    System.out.println("================ali=push=deviceId========== " + deviceId);
    System.out.println("================initCS()=start2");
  }


  /**
   * Loads Flipper in React Native templates. Call this in the onCreate method with something like
   * initializeFlipper(this, getReactNativeHost().getReactInstanceManager());
   *
   * @param context
   * @param reactInstanceManager
   */
  private static void initializeFlipper(
      Context context, ReactInstanceManager reactInstanceManager) {
    if (BuildConfig.DEBUG) {
      try {
        /*
         We use reflection here to pick up the class that initializes Flipper,
        since Flipper library is not available in release mode
        */
        Class<?> aClass = Class.forName("com.sp_mobile_front_jznc.ReactNativeFlipper");
        aClass
            .getMethod("initializeFlipper", Context.class, ReactInstanceManager.class)
            .invoke(null, context, reactInstanceManager);
      } catch (ClassNotFoundException e) {
        e.printStackTrace();
      } catch (NoSuchMethodException e) {
        e.printStackTrace();
      } catch (IllegalAccessException e) {
        e.printStackTrace();
      } catch (InvocationTargetException e) {
        e.printStackTrace();
      }
    }
  }
}
