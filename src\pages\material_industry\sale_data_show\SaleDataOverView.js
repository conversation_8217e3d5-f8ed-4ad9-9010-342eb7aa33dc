import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import NaicaiGuidBugHeadScreen from '../../../component/NaicaiGuidBugHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import ProgressBar from '../../../component/ProgressBar';
import ProductEmptyRowViewComponent from '../../../component/ProductEmptyRowViewComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import moment from 'moment';
const { fullScreenIfIphoneXContentViewHeight,ifIphoneXBodyViewHeight} = require('../../../utils/ScreenUtil');

var CommonStyle = require('../../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
var currentYear= (moment(new Date()).format('YYYY'));
var currentMonth= (moment(new Date()).format('MM'));

export default class SaleDataOverView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            salesDataSource:[],
            receiveDataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            selectSalesYear:currentYear,
            selectSalesMonth:currentMonth,
            selectSalesQuarter:"",
            selectReceiveYear:currentYear,
            selectReceiveMonth:currentMonth,
            selectReceiveQuarter:"",
            salesMonth:[currentYear+"年",currentMonth.replace(/\b(0+)/gi,"")+'月'],
            receiveMonth:[currentYear+"年",currentMonth.replace(/\b(0+)/gi,"")+'月'],
            salesMonthList:[],
            permissionCodeList:[]
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        console.log(currentMonth.replace(/\b(0+)/gi,""));
        console.log(currentYear);
        // 加载销售数据
        this.loadSalesData();
        // 加载回款数据
        this.loadReceiveData();
        // 获取查看权限
        this.loadPermission();
        var maxMonth = currentMonth.replace(/\b(0+)/gi,"");
        var salesMonthList = [];
        var salesMonthDTO = {};
        for(var i = 1; i < (maxMonth * 1 + 1); i++) {
            salesMonthDTO ={
                "monthName":i + "月",
                "monthCode":i < 10 ? ("0"+i) : i.toString()
            };
            salesMonthList = salesMonthList.concat(salesMonthDTO)
        }
        console.log(salesMonthList)
        this.setState({
            salesMonthList:salesMonthList
        })
    }

    loadPermission=()=>{
        let url= "/biz/permission/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            "userId":constants.loginUser.userId
        };
        httpPost(url, loadRequest, this.loadPermissionCallBack);
    }

    loadPermissionCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var permissionDataList = response.data.dataList;
            var permissionCodeList =[];
            permissionDataList.forEach(item=>{
                permissionCodeList.push(item.permissionCode)
            })
            console.log("permissionCodeList====",permissionCodeList)
            this.setState({
                permissionCodeList:permissionCodeList,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadSalesData=()=>{
        let url= "/biz/sales/kanban/getSales";
        let loadRequest={
            "searchYear":this.state.selectSalesYear,
            "searchMonth":this.state.selectSalesMonth
        };
        httpPost(url, loadRequest, this.loadSalesDataCallBack);
    }

    loadSalesDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                salesDataSource:response.data,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadReceiveData=()=>{
        let url= "/biz/sales/kanban/getReceive";
        let loadRequest={
            "searchYear":this.state.selectReceiveYear,
            "searchMonth":this.state.selectReceiveMonth
        };
        httpPost(url, loadRequest, this.loadReceiveDataCallBack);
    }

    loadReceiveDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                receiveDataSource:response.data,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }


    // 下拉触顶刷新
    _loadFreshData=()=>{
        this.loadSalesData();
        this.loadReceiveData();
    }


    openSalesMonthSelect() {
        console.log("当前的日期",this.state.salesMonth)
        this.refs.SelectSalesMonth.showMonthToThisMonth(this.state.salesMonth)
    }

    callBackSalesMonthValue(value) {
        console.log("==========销售月份选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            salesMonth:value
        })
        // console.log("==========销售月份选择结果：", value[0].split("年")[0],value[1].split("月")[0])
        var year = value[0].split("年")[0];
        var month = value[1].split("月")[0];
        this.setState({
            selectSalesMonth:month < 10 ? ("0"+month) : month.toString(),
            selectSalesYear:year.toString()
        })
        let url= "/biz/sales/kanban/getSales";
        let loadRequest={
            "searchYear":year.toString(),
            "searchMonth":month < 10 ? ("0"+month) : month.toString()
        };
        httpPost(url, loadRequest, this.loadSalesDataCallBack);
    }

    openReceiveMonthSelect() {
        this.refs.SelectReceiveMonth.showMonthToThisMonth(this.state.receiveMonth)
    }

    callBackReceiveMonthValue(value) {
        console.log("==========回款月份选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            receiveMonth:value
        })
        var year = value[0].split("年")[0];
        var month = value[1].split("月")[0];
        this.setState({
            selectReceiveMonth:month < 10 ? ("0"+month) : month.toString(),
            selectReceiveYear:year.toString()
        })
        let url= "/biz/sales/kanban/getReceive";
        let loadRequest={
            "searchYear":year.toString(),
            "searchMonth":month < 10 ? ("0"+month) : month.toString()
        };
        httpPost(url, loadRequest, this.loadReceiveDataCallBack);
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    renderSalesTargetAndPerformanceRow = (item, index)=>{
        return(
            <View style={{marginLeft:10,flexDirection:'row'}}>
                <View>
                    {
                        item.userPhoto?
                        <View style={styles.imgContentStyle}>
                            <Image source={{ uri: (constants.image_addr + '/' + item.userPhoto) }} style={{width:60,height:60, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                        </View>
                        :
                        <View style={styles.imgContentStyle}>
                            <Image source={require('../../../assets/image/userPhoto.png')} style={{width:60,height:60, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                        </View>
                    }
                </View>
                <View style={{flexDirection:'column',alignItems:'center',justifyContent:'center'}}>
                    <View style={{flexDirection:'row',alignItems:'center',justifyContent:'flex-start',width:screenWidth*0.6}}>
                        <View style={{width:150}}>
                            <Text>{item.userName}</Text>
                        </View>
                        <View>
                            <Text>{item.monthSales}/{item.monthTargetSales?parseFloat(item.monthTargetSales):"0"}(万元)</Text>
                        </View>
                    </View>
                    <View style={{flexDirection:'row',alignItems:'center',justifyContent:'flex-start',width:screenWidth*0.6,marginTop:10}}>
                        <View style={{width:230,marginRight:10}}>
                            {/* <Text>进度条</Text> */}
                            <ProgressBar fillColor={'#9de83e'} height={10} progress={item.monthSaleFinishRate/100}/>
                        </View>
                        <View>
                            <Text>{item.monthSaleFinishRate}%</Text>
                        </View>
                    </View>
                </View>
                
            </View>
        )
    }

    renderSalesRankRow=(item, index)=>{
        return(
            <View>
                <View style={{flexDirection:'row',marginLeft:0}}>
                    <View style={{margin:5,width:screenWidth/6,justifyContent:'center',alignItems:'center'}}>
                        {
                            (index + 1) == 1
                            ?
                            <Image source={require('../../../assets/icon/FirstRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) == 2
                            ?
                            <Image source={require('../../../assets/icon/SecondRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) == 3
                            ?
                            <Image source={require('../../../assets/icon/ThirdRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) != 1 && (index + 1) != 2 && (index + 1) != 3
                            ?
                            <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{index + 1}</Text>
                            :null
                        }
                    </View>
                    <View style={{margin:5,width:screenWidth/6,justifyContent:'center',alignItems:'center'}}>
                        {
                            item.userPhoto?
                            <Image source={{ uri: (constants.image_addr + '/' + item.userPhoto) }} style={{width:40,height:40, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :
                            <Image source={require('../../../assets/image/userPhoto.png')} style={{width:40,height:40, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                        }
                    </View>
                    <View style={{margin:5,width:screenWidth/6,justifyContent:'center',alignItems:'center'}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{item.userName}</Text>
                    </View>
                    <View style={{margin:5,width:screenWidth/4.5,justifyContent:'center',alignItems:'center'}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{item.monthSales}万元</Text>
                    </View>
                </View>
            </View>
        )
    }

    renderSalesRankRowNoPermission=(item, index)=>{
        return(
            <View>
                <View style={{flexDirection:'row',marginLeft:0}}>
                    <View style={{margin:5,width:screenWidth/4,justifyContent:'center',alignItems:'center'}}>
                        {
                            (index + 1) == 1
                            ?
                            <Image source={require('../../../assets/icon/FirstRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) == 2
                            ?
                            <Image source={require('../../../assets/icon/SecondRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) == 3
                            ?
                            <Image source={require('../../../assets/icon/ThirdRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) != 1 && (index + 1) != 2 && (index + 1) != 3
                            ?
                            <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{index + 1}</Text>
                            :null
                        }
                    </View>
                    <View style={{margin:5,width:screenWidth/4,justifyContent:'center',alignItems:'center'}}>
                        {
                            item.userPhoto?
                            <Image source={{ uri: (constants.image_addr + '/' + item.userPhoto) }} style={{width:40,height:40, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :
                            <Image source={require('../../../assets/image/userPhoto.png')} style={{width:40,height:40, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                        }
                    </View>
                    <View style={{margin:5,width:screenWidth/4.5,justifyContent:'center',alignItems:'center'}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{item.userName}</Text>
                    </View>
                </View>
            </View>
        )
    }

    renderReceiveTargetAndPerformanceRow = (item, index)=>{
        return(
            <View style={{marginLeft:10,flexDirection:'row'}}>
                <View>
                    {
                        item.userPhoto?
                        <View style={styles.imgContentStyle}>
                            <Image source={{ uri: (constants.image_addr + '/' + item.userPhoto) }} style={{width:60,height:60, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                        </View>
                        :
                        <View style={styles.imgContentStyle}>
                            <Image source={require('../../../assets/image/userPhoto.png')} style={{width:60,height:60, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                        </View>
                    }
                </View>
                <View style={{flexDirection:'column',alignItems:'center',justifyContent:'center'}}>
                    <View style={{flexDirection:'row',alignItems:'center',justifyContent:'flex-start',width:screenWidth*0.6}}>
                        <View style={{width:150}}>
                            <Text>{item.userName}</Text>
                        </View>
                        <View>
                            <Text>{item.monthReceiveSales}/{item.monthTargetReceiveSales?parseFloat(item.monthTargetReceiveSales):"0"}(万元)</Text>
                        </View>
                    </View>
                    <View style={{flexDirection:'row',alignItems:'center',justifyContent:'flex-start',width:screenWidth*0.6,marginTop:10}}>
                        <View style={{width:230,marginRight:10}}>
                            {/* <Text>进度条</Text> */}
                            <ProgressBar fillColor={'#9de83e'} height={10} progress={item.monthReceiveFinishRate/100}/>
                        </View>
                        <View>
                            <Text>{item.monthReceiveFinishRate}%</Text>
                        </View>
                    </View>
                </View>
                
            </View>
        )
    }

    renderReceiveRankRow=(item, index)=>{
        return(
            <View>
                <View style={{flexDirection:'row',marginLeft:0}}>
                    <View style={{margin:5,width:screenWidth/6,justifyContent:'center',alignItems:'center'}}>
                        {
                            (index + 1) == 1
                            ?
                            <Image source={require('../../../assets/icon/FirstRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) == 2
                            ?
                            <Image source={require('../../../assets/icon/SecondRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) == 3
                            ?
                            <Image source={require('../../../assets/icon/ThirdRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) != 1 && (index + 1) != 2 && (index + 1) != 3
                            ?
                            <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{index + 1}</Text>
                            :null
                        }
                    </View>
                    <View style={{margin:5,width:screenWidth/6,justifyContent:'center',alignItems:'center'}}>
                        {
                            item.userPhoto?
                            <Image source={{ uri: (constants.image_addr + '/' + item.userPhoto) }} style={{width:40,height:40, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :
                            <Image source={require('../../../assets/image/userPhoto.png')} style={{width:40,height:40, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                        }
                    </View>
                    <View style={{margin:5,width:screenWidth/6,justifyContent:'center',alignItems:'center'}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{item.userName}</Text>
                    </View>
                    <View style={{margin:5,width:screenWidth/4.5,justifyContent:'center',alignItems:'center'}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{item.monthReceiveSales}万元</Text>
                    </View>
                </View>
            </View>
        )
    }

    renderReceiveRankRowNoPermission=(item, index)=>{
        return(
            <View>
                <View style={{flexDirection:'row',marginLeft:0}}>
                    <View style={{margin:5,width:screenWidth/4,justifyContent:'center',alignItems:'center'}}>
                        {
                            (index + 1) == 1
                            ?
                            <Image source={require('../../../assets/icon/FirstRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) == 2
                            ?
                            <Image source={require('../../../assets/icon/SecondRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) == 3
                            ?
                            <Image source={require('../../../assets/icon/ThirdRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) != 1 && (index + 1) != 2 && (index + 1) != 3
                            ?
                            <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{index + 1}</Text>
                            :null
                        }
                    </View>
                    <View style={{margin:5,width:screenWidth/4,justifyContent:'center',alignItems:'center'}}>
                        {
                            item.userPhoto?
                            <Image source={{ uri: (constants.image_addr + '/' + item.userPhoto) }} style={{width:40,height:40, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :
                            <Image source={require('../../../assets/image/userPhoto.png')} style={{width:40,height:40, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                        }
                    </View>
                    <View style={{margin:5,width:screenWidth/4.5,justifyContent:'center',alignItems:'center'}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{item.userName}</Text>
                    </View>
                </View>
            </View>
        )
    }

    render(){
        return(
            <View style={{backgroundColor:'#FFFFFF',height:screenHeight}}>
                <NaicaiGuidBugHeadScreen title='销售看板'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[{backgroundColor:'#f3f6f8',height:ifIphoneXBodyViewHeight()}]}>
                    <ScrollView
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                    >
                        <View style={[styles.containerBox,{justifyContent:'center'}]}>
                            <View style={{marginLeft:10,marginTop:10,marginBottom:10,flexDirection:'row'}}>
                                <View style={{width:screenWidth/2,justifyContent:'center'}}>
                                    <Text>销售概览</Text>
                                </View>
                                <View style={{paddingBottom:5,paddingTop:5,paddingLeft:10,paddingRight:10,marginRight:5,backgroundColor:"#0fce05",borderRadius:5}}>
                                    <Text style={{color:"#f0f0f0f0"}}>{this.state.salesMonth[0]+this.state.salesMonth[1]}</Text>
                                </View>
                                <TouchableOpacity onPress={() => { this.openSalesMonthSelect()}}  style={{flexDirection:'row',alignItems:'center'}}>
                                    <Text style={{color:'#44b7eb'}}>选择</Text>
                                    <Image style={{ width: 15, height: 15}} source={require('../../../assets/icon/iconfont/selectBlue.png')}></Image>
                                </TouchableOpacity>
                            </View>
                        </View>
                        {
                            this.state.permissionCodeList.includes("SALES_DATA")
                            ?
                            <View style={[styles.containerBox,{justifyContent:'center',flexDirection:'row'}]}>
                                <View style={{marginLeft:10,width:(screenWidth - 20)/2,marginTop:10,marginBottom:10,alignItems:'center',flexDirection:'column'}}>
                                    <Text style={{color:"#00000085",marginBottom:5}}>本月完成销售额（万元）</Text>
                                    <Text style={{fontSize:16,fontWeight:'bold',marginBottom:5}}>{this.state.salesDataSource.monthSales}</Text>
                                    <Text style={{color:"#00000085",marginBottom:5}}>{this.state.salesDataSource.monthTargetSales && this.state.salesDataSource.monthTargetSales!="0"?(this.state.salesDataSource.monthSales*100/parseFloat(this.state.salesDataSource.monthTargetSales)).toFixed(2)+'%':'--'}</Text>
                                    <View style={{width:(screenWidth - 20)/2 - 20}}>
                                        <ProgressBar fillColor={'#efb336'} height={10} progress={this.state.salesDataSource.monthTargetSales && this.state.salesDataSource.monthTargetSales!="0"?(this.state.salesDataSource.monthSales/parseFloat(this.state.salesDataSource.monthTargetSales)).toFixed(2):0}/>
                                    </View>
                                </View>
                                <View style={{marginLeft:10,width:(screenWidth - 20)/2,marginTop:10,marginBottom:10,alignItems:'center',flexDirection:'column'}}>
                                    <Text style={{color:"#00000085",marginBottom:5}}>本年完成销售额（万元）</Text>
                                    <Text style={{fontSize:16,fontWeight:'bold',marginBottom:5}}>{this.state.salesDataSource.yearSales}</Text>
                                    <Text style={{color:"#00000085",marginBottom:5}}>{this.state.salesDataSource.yearTargetSales && this.state.salesDataSource.yearTargetSales!="0"?(this.state.salesDataSource.yearSales*100/parseFloat(this.state.salesDataSource.yearTargetSales)).toFixed(2)+'%':'--'}</Text>
                                    <View style={{width:(screenWidth - 20)/2 - 20}}>
                                        <ProgressBar fillColor={'#9de83e'} height={10} progress={this.state.salesDataSource.yearTargetSales && this.state.salesDataSource.yearTargetSales!="0"?(this.state.salesDataSource.yearSales/parseFloat(this.state.salesDataSource.yearTargetSales)).toFixed(2):0}/>
                                    </View>
                                </View>
                            </View>
                            :null
                        }

                       

                        <View style={[styles.containerBox,{justifyContent:'center'}]}>
                            <View style={{marginLeft:10,marginTop:10,marginBottom:10,flexDirection:'row'}}>
                                <View style={{width:screenWidth/1.25}}>
                                    <Text>销售排名<Text style={{color:"#00000085"}}>(销售额前10名)</Text></Text>
                                </View>
                                <TouchableOpacity onPress={() => { 
                                    this.props.navigation.navigate("SalesRankDetail", 
                                    {
                                        // 传递回调函数
                                        monthCode: this.state.selectSalesMonth,
                                        yearCode:this.state.selectSalesYear,
                                        permissionCodeList:this.state.permissionCodeList
                                    })
                                }}  style={{flexDirection:'row',alignItems:'center'}}>
                                    <Text style={{color:'#09c119'}}>查看</Text>
                                    <Image style={{ width: 15, height: 15}} source={require('../../../assets/icon/iconfont/searchGreenArrow.png')}></Image>
                                </TouchableOpacity>
                            </View>
                            <View>
                                {
                                    this.state.salesDataSource.spSalesUserDTOListRankByAmount && this.state.salesDataSource.spSalesUserDTOListRankByAmount.length > 0
                                    ?
                                    (
                                        this.state.salesDataSource.spSalesUserDTOListRankByAmount.map((item,index)=>{
                                            if(index < 10){
                                                return (
                                                    this.state.permissionCodeList.includes("SALES_DATA")
                                                    ?
                                                    this.renderSalesRankRow(item,index)
                                                    :
                                                    this.renderSalesRankRowNoPermission(item,index)
                                                )
                                            }
                                        })
                                    )
                                    :
                                    <ProductEmptyRowViewComponent height={200} />
                                }
                            </View>
                        </View>
                        {
                            this.state.permissionCodeList.includes("SALES_DATA")
                            ?
                            <View style={[styles.containerBox,{justifyContent:'center'}]}>
                                <View style={{marginLeft:10,marginTop:10,marginBottom:10,flexDirection:'row'}}>
                                    <View style={{width:screenWidth/1.25}}>
                                        <Text>销售目标及业绩<Text style={{color:"#00000085"}}>(完成率前10名)</Text></Text>
                                    </View>
                                    <TouchableOpacity onPress={() => {
                                        this.props.navigation.navigate("SalesTargetAndPerformance", 
                                        {
                                            // 传递回调函数
                                            monthCode: this.state.selectSalesMonth,
                                            yearCode:this.state.selectSalesYear,
                                        })
                                    }}  style={{flexDirection:'row',alignItems:'center'}}>
                                        <Text style={{color:'#09c119'}}>查看</Text>
                                        <Image style={{ width: 15, height: 15}} source={require('../../../assets/icon/iconfont/searchGreenArrow.png')}></Image>
                                    </TouchableOpacity>
                                </View>
                                <View>
                                    {
                                        this.state.salesDataSource.spSalesUserDTOListRankByRate && this.state.salesDataSource.spSalesUserDTOListRankByRate.length > 0
                                        ?
                                        (
                                            this.state.salesDataSource.spSalesUserDTOListRankByRate.map((item,index)=>{
                                                if(index < 10){
                                                    return this.renderSalesTargetAndPerformanceRow(item,index)
                                                }
                                            })
                                        )
                                        :
                                        <ProductEmptyRowViewComponent height={200} />
                                    }
                                    
                                </View>
                            </View>
                            :null
                        }

                        <View style={[styles.containerBox,{justifyContent:'center'}]}>
                            <View style={{marginLeft:10,marginTop:10,marginBottom:10,flexDirection:'row'}}>
                                <View style={{width:screenWidth/2,justifyContent:'center'}}>
                                    <Text>回款概览</Text>
                                </View>
                                <View style={{paddingBottom:5,paddingTop:5,paddingLeft:10,paddingRight:10,marginRight:5,backgroundColor:"#0fce05",borderRadius:5}}>
                                    <Text style={{color:"#f0f0f0f0"}}>{this.state.receiveMonth[0]+this.state.receiveMonth[1]}</Text>
                                </View>
                                <TouchableOpacity onPress={() => { this.openReceiveMonthSelect()}}  style={{flexDirection:'row',alignItems:'center'}}>
                                    <Text style={{color:'#44b7eb'}}>选择</Text>
                                    <Image style={{ width: 15, height: 15}} source={require('../../../assets/icon/iconfont/selectBlue.png')}></Image>
                                </TouchableOpacity>
                            </View>
                        </View>
                        {
                            this.state.permissionCodeList.includes("RECEIVE_DATA")
                            ?
                            <View style={[styles.containerBox,{justifyContent:'center',flexDirection:'row'}]}>
                                <View style={{marginLeft:10,width:(screenWidth - 20)/2,marginTop:10,marginBottom:10,alignItems:'center',flexDirection:'column'}}>
                                    <Text style={{color:"#00000085",marginBottom:5}}>本月回款额（万元）</Text>
                                    <Text style={{fontSize:16,fontWeight:'bold',marginBottom:5}}>{this.state.receiveDataSource.monthReceiveSales}</Text>
                                    <Text style={{color:"#00000085",marginBottom:5}}>{this.state.receiveDataSource.monthTargetReceiveSales && this.state.receiveDataSource.monthTargetReceiveSales!="0"?(this.state.receiveDataSource.monthReceiveSales*100/parseFloat(this.state.receiveDataSource.monthTargetReceiveSales)).toFixed(2)+'%':'--'}</Text>
                                    <View style={{width:(screenWidth - 20)/2 - 20}}>
                                        <ProgressBar fillColor={'#efb336'} height={10} progress={this.state.receiveDataSource.monthTargetReceiveSales && this.state.receiveDataSource.monthTargetReceiveSales!="0"?(this.state.receiveDataSource.monthReceiveSales/parseFloat(this.state.receiveDataSource.monthTargetReceiveSales)).toFixed(2):0}/>
                                    </View>
                                </View>
                                <View style={{marginLeft:10,width:(screenWidth - 20)/2,marginTop:10,marginBottom:10,alignItems:'center',flexDirection:'column'}}>
                                    <Text style={{color:"#00000085",marginBottom:5}}>本年回款额（万元）</Text>
                                    <Text style={{fontSize:16,fontWeight:'bold',marginBottom:5}}>{this.state.receiveDataSource.yearReceiveSales}</Text>
                                    <Text style={{color:"#00000085",marginBottom:5}}>{this.state.receiveDataSource.yearTargetReceiveSales && this.state.receiveDataSource.yearTargetReceiveSales!="0"?(this.state.receiveDataSource.yearReceiveSales*100/parseFloat(this.state.receiveDataSource.yearTargetReceiveSales)).toFixed(2)+'%':'--'}</Text>
                                    <View style={{width:(screenWidth - 20)/2 - 20}}>
                                        <ProgressBar fillColor={'#9de83e'} height={10} progress={this.state.receiveDataSource.yearTargetReceiveSales && this.state.receiveDataSource.yearTargetReceiveSales!="0"?(this.state.receiveDataSource.yearReceiveSales/parseFloat(this.state.receiveDataSource.yearTargetReceiveSales)).toFixed(2):0}/>
                                    </View>
                                </View>
                            </View>
                            :null
                        }

                        

                        <View style={[styles.containerBox,{justifyContent:'center'}]}>
                            <View style={{marginLeft:10,marginTop:10,marginBottom:10,flexDirection:'row'}}>
                                <View style={{width:screenWidth/1.25}}>
                                    <Text>回款排名<Text style={{color:"#00000085"}}>(回款额前10名)</Text></Text>
                                </View>
                                <TouchableOpacity onPress={() => {
                                    this.props.navigation.navigate("ReceiveRankDetail", 
                                    {
                                        // 传递回调函数
                                        monthCode: this.state.selectReceiveMonth,
                                        yearCode:this.state.selectReceiveYear,
                                        permissionCodeList:this.state.permissionCodeList
                                    })
                                }}  style={{flexDirection:'row',alignItems:'center'}}>
                                    <Text style={{color:'#09c119'}}>查看</Text>
                                    <Image style={{ width: 15, height: 15}} source={require('../../../assets/icon/iconfont/searchGreenArrow.png')}></Image>
                                </TouchableOpacity>
                            </View>
                            <View>
                                {
                                    this.state.receiveDataSource.spSalesUserReceiveDTOListRankByAmount && this.state.receiveDataSource.spSalesUserReceiveDTOListRankByAmount.length > 0
                                    ?
                                    (
                                        this.state.receiveDataSource.spSalesUserReceiveDTOListRankByAmount.map((item,index)=>{
                                            if(index < 10){
                                                return (
                                                    this.state.permissionCodeList.includes("RECEIVE_DATA")
                                                    ?
                                                    this.renderReceiveRankRow(item,index)
                                                    :
                                                    this.renderReceiveRankRowNoPermission(item,index)
                                                )
                                            }
                                        })
                                    )
                                    :
                                    <ProductEmptyRowViewComponent height={200} />
                                }
                            </View>
                        </View>

                        {
                            this.state.permissionCodeList.includes("RECEIVE_DATA")
                            ?
                            <View style={[styles.containerBox,{justifyContent:'center',marginBottom:10}]}>
                                <View style={{marginLeft:10,marginTop:10,marginBottom:10,flexDirection:'row'}}>
                                    <View style={{width:screenWidth/1.25}}>
                                        <Text>回款目标及业绩<Text style={{color:"#00000085"}}>(完成率前10名)</Text></Text>
                                    </View>
                                    <TouchableOpacity onPress={() => {
                                        this.props.navigation.navigate("ReceiveTargetAndPerformance", 
                                        {
                                            // 传递回调函数
                                            monthCode: this.state.selectReceiveMonth,
                                            yearCode:this.state.selectReceiveYear,
                                        })
                                    }}  style={{flexDirection:'row',alignItems:'center'}}>
                                        <Text style={{color:'#09c119'}}>查看</Text>
                                        <Image style={{ width: 15, height: 15}} source={require('../../../assets/icon/iconfont/searchGreenArrow.png')}></Image>
                                    </TouchableOpacity>
                                </View>
                                <View>
                                    {
                                        this.state.receiveDataSource.spSalesUserReceiveDTOListRankByRate && this.state.receiveDataSource.spSalesUserReceiveDTOListRankByRate.length > 0
                                        ?
                                        (
                                            this.state.receiveDataSource.spSalesUserReceiveDTOListRankByRate.map((item,index)=>{
                                                if(index < 10){
                                                    return this.renderReceiveTargetAndPerformanceRow(item,index)
                                                }
                                            })
                                        )
                                        :
                                        <ProductEmptyRowViewComponent height={200} />
                                    }
                                    
                                </View>
                            </View>
                            :null
                        }

                        
                    </ScrollView>
                </View>
                <BottomScrollSelect
                    ref={'SelectSalesMonth'}
                    callBackMonthToThisMonthValue={this.callBackSalesMonthValue.bind(this)}
                />
                <BottomScrollSelect
                    ref={'SelectReceiveMonth'}
                    callBackMonthToThisMonthValue={this.callBackReceiveMonthValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    containerBox:{
        width:screenWidth - 20,
        marginLeft:10,
        marginTop:10,
        backgroundColor:"#FFFFFF"
    },
    imgContentStyle: {
        marginTop:8,
        marginBottom:8,
        marginLeft:0,
        marginRight:0,
        width:screenWidth/5
    },
    titleTextStyle: {
        fontSize: 16,
        alignItems:"center",
        // textAlign:"center"
    },
});