import React,{Component} from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Image, ScrollView,TextInput,Modal 
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class HLSickPersonAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            sickPersonName:"",
            sickPersonId:"",
            gender:"",
            idCard:"",
            contactTel:"",
            medicalCardNo:"",
            socialSecurityCardNo:"",
            maritalFlag:"",
            job:"",
            workUnits:"",
            // 医保类型下拉框
            medicalInsuranceTypeId:"",
            medicalInsuranceTypeName:"",
            medicalInsuranceTypeDataSource:[],
            selectmedicalInsuranceType:[],
            // 性别块状选择
            sexDataSource:[
                {
                    sexId:1,
                    gender:"男",
                    sexName:"M"
                },
                {
                    sexId:2,
                    gender:"女",
                    sexName:"L"
                }
            ],
            selSexId:1,
            selSexName:"M",
            // 性别块状选择
            maritalFlagDataSource:[
                {
                    maritalFlagId:1,
                    maritalFlag:"已婚",
                    maritalFlagName:"Y"
                },
                {
                    maritalFlagId:2,
                    maritalFlag:"未婚",
                    maritalFlagName:"N"
                }
            ],
            selMaritalFlagId:1,
            selMaritalFlagName:"Y",

            hospitalName:"",
            hospitalId:"",
            selectHospital:[],
            hospitalDataSource:[],
            selHospitalId:"",

            //患者出生日期
            birthDate:"",
            selectSickBrithDate: [],
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        //加载医保类型
        this.loadMedicalInsureanceType();
        //获取院区
        this.loadHospitalList();

        if (route && route.params) {
            const { sickPersonId,hospitalId,hospitalName } = route.params;
            if (sickPersonId) {
                console.log("=============sickPersonId" + sickPersonId + "");
                this.setState({
                    sickPersonId:sickPersonId,
                    operate:"编辑"
                })
               let loadTypeUrl = "/biz/hl/sick/person/get";
               let loadRequest = { 'sickPersonId': sickPersonId };
                httpPost(loadTypeUrl, loadRequest, this.loadHLSickPersonCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })

                //当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectSickBrithDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    birthDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
            if (hospitalId) {
                console.log("========hospitalId:", hospitalId);
                this.setState({
                    hospitalId:hospitalId,
                })
            }
            if (hospitalName) {
                console.log("========hospitalName:", hospitalName);
                this.setState({
                    hospitalName:hospitalName,
                    selectHospital:[hospitalName]
                })
            }
        }
    }
     // 获取院区
     loadHospitalList=()=>{
        let url= "/biz/hl/hospital/list";
        let data={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, data, this.callBackLoadHospitalList);
    }

    callBackLoadHospitalList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log("======load==hospital==", response.data.dataList);
            this.setState({
                hospitalDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    loadMedicalInsureanceType=()=>{
        let loadTypeUrl= "/biz/hl/medical/insurance/type/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000
        };

        httpPost(loadTypeUrl, loadRequest, this.loadMedicalInsureanceTypeCallBack);

    }

    loadMedicalInsureanceTypeCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                medicalInsuranceTypeDataSource:response.data.dataList
            })
        }
    }
    loadHLSickPersonCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectSickBrithDate = response.data.birthDate.split("-");
            if(response.data.gender == 'M'){
                this.setState({
                    selSexId:1
                })
            }
            if(response.data.gender == 'L'){
                this.setState({
                    selSexId:2
                })
            }
            if(response.data.maritalFlag == 'Y'){
                this.setState({
                    selMaritalFlagId:1
                })
            }
            if(response.data.maritalFlag == 'N'){
                this.setState({
                    selMaritalFlagId:2
                })
            }
            this.setState({
                sickPersonName:response.data.sickPersonName,
                idCard:response.data.idCard,
                contactTel:response.data.contactTel,
                medicalCardNo:response.data.medicalCardNo,
                socialSecurityCardNo:response.data.socialSecurityCardNo,
                medicalInsuranceTypeId:response.data.medicalInsuranceTypeId,
                birthDate:response.data.birthDate,
                workUnits:response.data.workUnits,
                selectSickBrithDate:selectSickBrithDate,
                gender: response.data.gender,
                maritalFlag:response.data.maritalFlag,
                job:response.data.job,
                selectStaffBirthday:[response.data.birthDate],
                // 医保类型
                medicalInsuranceTypeId:response.data.medicalInsuranceTypeId,
                medicalInsuranceTypeName:response.data.medicalInsuranceTypeName,
                selectmedicalInsuranceType:[response.data.medicalInsuranceTypeName,],
                // 性别  
                staffSex: response.data.staffSex,

                hospitalId:response.data.hospitalId,
                selHospitalId:response.data.hospitalId,
                hospitalName:response.data.hospitalName,
                selectHospital:[response.data.hospitalName],

            })
        }
    }
     //sex列表展示
     renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selSexId:item.sexId,
                        selSexName:item.sexName,
                        selGender :item.gender
                    })
                }}>
                <View key={item.sexId} style={[item.sexId===this.state.selSexId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.sexId===this.state.selSexId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.gender}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    //婚姻状况列表展示
    renderMaritalFlagRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selMaritalFlagId:item.maritalFlagId,
                        selMaritalFlagName:item.maritalFlagName,
                        selMaritalFlag :item.maritalFlag
                    })
                }}>
                <View key={item.maritalFlagId} style={[item.maritalFlagId===this.state.selMaritalFlagId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.maritalFlagId===this.state.selMaritalFlagId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.maritalFlag}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    saveHLSickPerson = () => {
        console.log("=======saveHLSickPerson");
        let toastOpts;
        if (!this.state.sickPersonName) {
            toastOpts = getFailToastOpts("请输入病患姓名");
            WToast.show(toastOpts)
            return;
        }
        //  if (!this.state.gender) {
        //     toastOpts = getFailToastOpts("请选择性别");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.idCard) {
            toastOpts = getFailToastOpts("请输入身份证号");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.selHospitalId) {
        //     toastOpts = getFailToastOpts("请选择所属院区");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.medicalInsuranceTypeId) {
            toastOpts = getFailToastOpts("请输入医保类型");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.medicalInsuranceTypeId) {
            toastOpts = getFailToastOpts("请输入医保类型");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/hl/sick/person/add";
        if (this.state.sickPersonId) {
            console.log("=========Edit===sickPersonId", this.state.sickPersonId)
            url = "/biz/hl/sick/person/modify";
        }
        let requestParams = {
            sickPersonId:this.state.sickPersonId,
            sickPersonName:this.state.sickPersonName,
            idCard:this.state.idCard,
            contactTel:this.state.contactTel,
            medicalCardNo:this.state.medicalCardNo,
            socialSecurityCardNo:this.state.socialSecurityCardNo,
            medicalInsuranceTypeId:this.state.medicalInsuranceTypeId,
            gender: this.state.selSexName,
            birthDate:this.state.birthDate,
            maritalFlag:this.state.selMaritalFlagName,
            job:this.state.job,
            hospitalId:this.state.selHospitalId,
            workUnits:this.state.workUnits,
            // 医保类型
            medicalInsuranceTypeId:this.state.medicalInsuranceTypeId,
            // // 性别
            // staffSex: this.state.sexDataSource[this.state.selSexId-1].sexName,
            // 婚姻状况
            maritalFlag: this.state.maritalFlagDataSource[this.state.selMaritalFlagId-1].maritalFlagName,

        };
        console.log("======requestParams======",requestParams)
        httpPost(url, requestParams, this.saveHLSickPersonCallBack);
    }

    // 保存回调函数
    saveHLSickPersonCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }
    openMedicalInsuranceTypeSelect(){
        if (!this.state.medicalInsuranceTypeDataSource || this.state.medicalInsuranceTypeDataSource.length < 1) {
            WToast.show({data:"请先添加医保类型"});
            return
        }
        this.refs.SelectMedicalInsuranceType.showMedicalInsuranceType(this.state.selectmedicalInsuranceType, this.state.medicalInsuranceTypeDataSource)
    }
    callBackMedicalInsuranceTypeValue(value){
        console.log("==========医保类型选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectMedicalInsuranceType:value
        })
        var medicalInsuranceTypeName = value.toString();
        let loadUrl= "/biz/hl/medical/insurance/type/getMedicalInsuranceTypeByName";
        let loadRequest={
            "medicalInsuranceTypeName":medicalInsuranceTypeName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadMedicalInsuranceTypeDetailData);
    }

    callBackLoadMedicalInsuranceTypeDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                medicalInsuranceTypeId:response.data.medicalInsuranceTypeId,
                medicalInsuranceTypeName:response.data.medicalInsuranceTypeName,     
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLSickPersonList", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>病患信息</Text>
            </TouchableOpacity>
        )
    }
     //渲染院区底部滚动数据
     openHospitalSelect() {
        
        if (!this.state.hospitalDataSource || this.state.hospitalDataSource.length < 1) {
            WToast.show({ data: "没有有效状态的院区，请确认" });
            return
        }
        console.log("==========院区数据源：", this.state.hospitalDataSource);
        this.refs.SelectHospital.showHospital(this.state.selectHospital, this.state.hospitalDataSource)
    }
    callBackHospitalValue(value) {
        console.log("==========院区选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectHospital: value
        })
        var hospitalName = value.toString();
        let loadUrl = "/biz/hl/hospital/getHospitalByName";
        let loadRequest = {
            "hospitalName": hospitalName
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    hospitalName: response.data.hospitalName,
                    selHospitalId: response.data.hospitalId
                    
                })
            }
            else if (response.code == 401) {
                WToast.show({ data: response.message });
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }
    openSickBirthDate(){
        this.refs.SelectSickBirthDate.showDate(this.state.selectSickBrithDate)
    }
    
    callBackSelectSickBirthDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectSickBrithDate:value
        })
        if (value && value.length) {
            var birthDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    birthDate += vartime;
                }
                else{
                    birthDate += "-" + vartime;
                }
            }
            this.setState({
                birthDate:birthDate
            })
        }
    }


    render() {
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '病患'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()} />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>姓名</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={[styles.inputRightText]}
                            placeholder={'请输入病患姓名'}
                            onChangeText={(text) => this.setState({ sickPersonName: text })}
                        >
                            {this.state.sickPersonName}
                        </TextInput>
                    </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>性别</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            
                            <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                                {
                                    (this.state.sexDataSource && this.state.sexDataSource.length > 0)
                                    ?
                                    this.state.sexDataSource.map((item, index)=>{
                                        return this.renderRow(item)
                                    })
                                    : <EmptyRowViewComponent/>
                                }
                            </View>
                        </View>
                        
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>身份证号</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText]}
                            placeholder={'请输入身份证号'}
                            onChangeText={(text) => this.setState({ idCard: text })}
                        >
                            {this.state.idCard}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText]}
                            placeholder={'请输入联系电话'}
                            onChangeText={(text) => this.setState({ contactTel: text })}
                        >
                            {this.state.contactTel}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>就诊卡号</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText]}
                            placeholder={'请输入就诊卡号'}
                            onChangeText={(text) => this.setState({ medicalCardNo: text })}
                        >
                            {this.state.medicalCardNo}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>社保卡号</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText]}
                            placeholder={'请输入社保卡号'}
                            onChangeText={(text) => this.setState({ socialSecurityCardNo: text })}
                        >
                            {this.state.socialSecurityCardNo}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>医保类型</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openMedicalInsuranceTypeSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth +30)}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.medicalInsuranceTypeName ? "请选择医保类型" : this.state.medicalInsuranceTypeName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出生日期</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openSickBirthDate()}>
                            {/* <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text>{this.state.schedulingProductionTime}</Text>
                            </View> */}
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth +30)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.birthDate ? "请选择出生日期" : this.state.birthDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>婚姻状况</Text>
                        </View>                     
                        <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                            {
                                (this.state.maritalFlagDataSource && this.state.maritalFlagDataSource.length > 0)
                                ?
                                this.state.maritalFlagDataSource.map((item, index)=>{
                                    return this.renderMaritalFlagRow(item)
                                })
                                : <EmptyRowViewComponent/>
                            }
                            </View>
                        </View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>职业</Text>
                            </View>
                            <TextInput
                                //keyboardType='text'
                                style={[styles.inputRightText]}
                                placeholder={'请输入职业'}
                                onChangeText={(text) => this.setState({ job: text })}
                            >
                                {this.state.job}
                            </TextInput>
                        </View>
                    
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>工作单位</Text>
                            </View>
                            <TextInput
                                //keyboardType='text'
                                style={[styles.inputRightText]}
                                placeholder={'请输入单位名称'}
                                onChangeText={(text) => this.setState({ workUnits: text })}
                            >
                                {this.state.workUnits}
                            </TextInput>
                        </View>
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>院区</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openHospitalSelect()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.hospitalName ? "请选择院区" : this.state.hospitalName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                   <View style={CommonStyle.btnRowStyle}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack(); } }>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]}>
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveHLSickPerson.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row' }]}>
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                 </View>
                    <BottomScrollSelect
                        ref={'SelectMedicalInsuranceType'}
                        callBackMedicalInsuranceTypeValue={this.callBackMedicalInsuranceTypeValue.bind(this)} />
                     <BottomScrollSelect
                        ref={'SelectHospital'}
                        callBackHospitalValue={this.callBackHospitalValue.bind(this)}
                    />
                     <BottomScrollSelect 
                        ref={'SelectSickBirthDate'} 
                        callBackDateValue={this.callBackSelectSickBirthDateValue.bind(this)}
                    />
                </ScrollView>           
            </View>
        )
    }
}
const styles = StyleSheet.create({
    leftLabNameTextStyle:{
        fontSize:18,
    },
    contentViewStyle:{
       
        height:screenHeight - 90,
        
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight:30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth +30),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },

});