import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Linking, Clipboard,ScrollView,
    FlatList, RefreshControl, Image, Modal, TextInput,ImageBackground
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../../utils/CameraRollUtils';
import moment from 'moment';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;

export default class StudentCourseTrackDetail extends Component{
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            searchKeyWord: "",
            topBlockLayoutHeight:0,

            syCourseTaskDto:null,

            courseTaskId:null,
            trackModal:false,
            trackContent:"",

            checkOutUserPhoto:null,
            checkOutUserName:null,
            courseSort:"0",
            courseName:"无",
            taskState:"",
            courseTypeName:"无",
            courseContent:"暂无",
            coursePhoto:null,//实习封面
            gmtCreated: "2023-09-01 16:23:31",
            planCompletionTime: "2024-01-10",

            selTaskStateCode:'c',
            taskStateDataSource:[
                // {
                //     stateCode: 'a',
                //     stateName: '视频',
                //     lenths:0,
                // },
                // {
                //     stateCode: 'b',
                //     stateName: '文档',
                //     lenths:0,
                // },
                {
                    stateCode: 'c',
                    stateName: '进展',
                    lenths:0,
                }
            ],
            vidioDataSource:[
            ],
            documentDataSource:[
            ],
            trackingDataSource:[
            ],
            maxlen:0
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { courseTaskId ,syCourseTaskDto} = route.params;
            if (courseTaskId) {
                console.log("=============courseTaskId" + courseTaskId +JSON.stringify(syCourseTaskDto, null, 6) +"");

                let {courseTaskId,courseSort,courseName,taskState,courseTypeName,courseContent,coursePhoto,checkOutUserId,checkOutUserPhoto,checkOutUserName,gmtCreated,planCompletionTime,actualCompletionTime}=syCourseTaskDto;
                
                let a=syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TV').length
                let b=syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TD').length
                let c=syCourseTaskDto.portalTrackDetailDTOList.length
                
                this.setState({

                    courseTaskId:courseTaskId,
                    checkOutUserId:checkOutUserId,

                    checkOutUserPhoto:checkOutUserPhoto,
                    checkOutUserName:checkOutUserName,
                    courseSort:courseSort,
                    courseName:courseName,
                    taskState:taskState?taskState:null,
                    courseTypeName:courseTypeName,
                    courseContent:courseContent,
                    coursePhoto:coursePhoto,
                    gmtCreated: gmtCreated,
                    planCompletionTime: planCompletionTime,
                    actualCompletionTime:actualCompletionTime,

                    documentDataSource: syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TD'),
                    vidioDataSource: syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TV'),
                    trackingDataSource: syCourseTaskDto.portalTrackDetailDTOList,
                    taskStateDataSource:[
                        // {
                        //     stateCode: 'a',
                        //     stateName: '视频',
                        //     lenths:syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TV').length,
                        // },
                        // {
                        //     stateCode: 'b',
                        //     stateName: '文档',
                        //     lenths:syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TD').length,
                        // },
                        {
                            stateCode: 'c',
                            stateName: '进展',
                            lenths:syCourseTaskDto.portalTrackDetailDTOList.length,
                        }
                    ],
                    // maxlen:a>b?(a>c?a:c):(b>c?b:c)
                    maxlen:c
                })
            }
            this.loadCourseTask(courseTaskId);
        }
    }

    loadCourseTask(courseTaskId){
        let url = "/biz/course/task/get_more";
        let loadRequest = {
            "courseTaskId": courseTaskId,
            "documentTypeList":["TV","TD"]
        };
        httpPost(url, loadRequest, this._loadCourseTaskCallBack);
    }
    _loadCourseTaskCallBack=(response)=>{        
        if (response.code == 200 && response.data ) {
            let syCourseTaskDto=response.data
            let {courseTaskId,courseSort,courseName,taskState,courseTypeName,courseContent,coursePhoto,gmtCreated,planCompletionTime,actualCompletionTime}=syCourseTaskDto;
            console.log("=============courseTaskId" + syCourseTaskDto.courseTaskId +JSON.stringify(syCourseTaskDto, null, 6));

            let a=syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TV').length
            let b=syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TD').length
            let c=syCourseTaskDto.portalTrackDetailDTOList.length

            this.setState({

                courseTaskId:courseTaskId,

                courseSort:courseSort,
                courseName:courseName,
                taskState:taskState?taskState:null,
                courseTypeName:courseTypeName,
                courseContent:courseContent,
                coursePhoto:coursePhoto,
                gmtCreated: gmtCreated,
                planCompletionTime: planCompletionTime,
                actualCompletionTime:actualCompletionTime,

                documentDataSource: syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TD'),
                vidioDataSource: syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TV'),
                trackingDataSource: syCourseTaskDto.portalTrackDetailDTOList,
                taskStateDataSource:[
                    // {
                    //     stateCode: 'a',
                    //     stateName: '视频',
                    //     lenths:syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TV').length,
                    // },
                    // {
                    //     stateCode: 'b',
                    //     stateName: '文档',
                    //     lenths:syCourseTaskDto.syDocumentDTOList.filter((item) => item.documentType === 'TD').length,
                    // },
                    {
                        stateCode: 'c',
                        stateName: '进展',
                        lenths:syCourseTaskDto.portalTrackDetailDTOList.length,
                    }
                ],
                // maxlen:a>b?(a>c?a:c):(b>c?b:c)
                maxlen:c
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 保存进展
    saveTrackDetail =()=> {
        console.log("=======saveTrackDetail");
        let toastOpts;
        if (!this.state.trackContent) {
            toastOpts = getFailToastOpts("请输入进展说明");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/track/detail/add";
        let requestParams={
            trackRemark: this.state.trackContent,
            trackType: "CT",
            trackFkId: this.state.courseTaskId,
            userId: constants.loginUser.userId
        };
        httpPost(url, requestParams, this.saveTrackDetailCallBack);
    }
    
    // 保存回调函数
    saveTrackDetailCallBack=(response)=>{
        this.setState({
            trackContent: ""
        })
        let toastOpts;
        switch (response.code) {
            case 200:
                WToast.show({ data: "进展提交成功" });
                this.loadCourseTask(this.state.courseTaskId);
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }
    renderTaskStateRow = (item, index) => {
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={() => {
                    let selTaskStateCode = item.stateCode;
                    this.setState({
                        "selTaskStateCode": selTaskStateCode
                    })
                }}>
                    <View key={item.stateCode} style={CommonStyle.tabItemViewStyle}>
                        <Text style={[item.stateCode === this.state.selTaskStateCode ?
                            [CommonStyle.selectedtabItemTextStyle]
                            :
                            [CommonStyle.tabItemTextStyle]
                        ]}>
                            {item.stateName+"("+item.lenths+")"}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    vidioRenderRow = (item, index) => {
        return (
            <View>
            <View key={item.vidioId+"vidioId"} style={{
                backgroundColor:"white",
                marginTop:2,
                height:80,
                flexDirection: 'row',
                alignItems:"center"
                }}>
                <View style={{
                    // backgroundColor:"green",
                    //外边距
                    marginLeft: 14,
                    marginRight: 0,
                    marginTop: 0,
                    marginBottom: 0,
                    height:48,
                    width:48,
                    justifyContent: 'center',
                    alignItems:"center"
                }}>
                    <Text>{index+1}</Text>
                </View>
                <View style={{
                    // backgroundColor:"red",
                    //外边距
                    marginLeft: 11,
                    marginRight: 0,
                    marginTop: 0,
                    marginBottom: 0,
                    height:48,
                    width:screenWidth-48-14-11-16-24,
                    justifyContent: 'space-between',
                }}>
                    <View style={{
                        // backgroundColor:"green",
                        height:23,justifyContent: 'center',
                    }}>
                        <Text style={{fontSize:16}}>{item.documentName}</Text>
                    </View>
                    <View style={{
                        // backgroundColor:"green",
                        height:23,flexDirection: 'row',alignItems:"center"
                    }}>
                        <View style={{
                            width:42,
                            height:20,
                            borderColor:"#ECEEF2",
                            borderRadius:2,
                            borderWidth:1,
                            justifyContent: 'center',
                            alignItems:"center"
                        }}>
                            <Text style={{ fontSize: 12}}>视频</Text>
                        </View>
                        {/* <Image style={{ height: 13 , width: 12, marginLeft: 10}} source={require('../../../assets/icon/iconfont/clock.png')}></Image>
                        <Text style={{ fontSize:12,marginLeft: 10}}>45分     已学完</Text> */}
                    </View>
                </View>
                <TouchableOpacity onPress={
                    ()=>{
                        console.log("播放",JSON.stringify(item, null, 6))
                        this.props.navigation.navigate("CourseVidioList", {
                            dataItem:{...item,vidioDataSource:this.state.vidioDataSource},
                            // 传递回调函数
                            refresh: this.callBackFunction
                        })
                    }
                }>
                    <View style={{
                        // backgroundColor:"green",
                        //外边距
                        marginLeft: 0,
                        marginRight: 16,
                        marginTop: 0,
                        marginBottom: 0,
                        height:24,
                        width:24
                    }}>
                        <Image style={{ height: 24 , width: 24}} source={require('../../../assets/icon/iconfont/play_new.png')}></Image>
                    </View>
                </TouchableOpacity>
            </View>
            <View style={styles.lineViewStyle}/>
            </View>
        )
    }
    doucumentRenderRow = (item, index) => {
        return (
            <View>
            <View key={item.vidioId+"vidioId"} style={{
                backgroundColor:"white",
                marginTop:2,
                height:80,
                flexDirection: 'row',
                alignItems:"center"
                }}>
                <View style={{
                    // backgroundColor:"green",
                    //外边距
                    marginLeft: 14,
                    marginRight: 0,
                    marginTop: 0,
                    marginBottom: 0,
                    height:48,
                    width:48,
                    justifyContent: 'center',
                    alignItems:"center"
                }}>
                    <Text>{index+1}</Text>
                </View>
                <View style={{
                    // backgroundColor:"red",
                    //外边距
                    marginLeft: 11,
                    marginRight: 0,
                    marginTop: 0,
                    marginBottom: 0,
                    height:48,
                    width:screenWidth-48-14-11-16-24,
                    justifyContent: 'space-between',
                }}>
                    <View style={{
                        // backgroundColor:"green",
                        height:23,justifyContent: 'center',
                    }}>
                        <Text style={{fontSize:16}}>{item.documentName}</Text>
                    </View>
                    <View style={{
                        // backgroundColor:"green",
                        height:23,flexDirection: 'row',alignItems:"center"
                    }}>
                        <View style={{
                            width:42,
                            height:20,
                            borderColor:"#ECEEF2",
                            borderRadius:2,
                            borderWidth:1,
                            justifyContent: 'center',
                            alignItems:"center"
                        }}>
                            <Text style={{ fontSize: 12}}>文档</Text>
                        </View>
                        {/* <Image style={{ height: 13 , width: 12, marginLeft: 10}} source={require('../../../assets/icon/iconfont/clock.png')}></Image>
                        <Text style={{ fontSize:12,marginLeft: 10}}>45分     已学完</Text> */}
                    </View>
                </View>

                <TouchableOpacity onPress={
                    ()=>{
                        console.log("阅读",JSON.stringify(item, null, 6))
                        let netWork = item.documentAccessPath;
                        Clipboard.setString(netWork);
                        // WToast.show({ data: "访问网址:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + netWork });
                        Alert.alert('确认', '访问网址已复制到粘贴板，使用浏览器打开:\n' + netWork + '', [
                            {
                                text: "不打开", onPress: () => {
                                    WToast.show({ data: '点击了不打开' });
                                }
                            },
                            {
                                text: "打开", onPress: () => {
                                    WToast.show({ data: '点击了打开' });
                                    // 直接打开外网链接 
                                    Linking.openURL(netWork)
                                }
                            }
                        ]);
                    }
                }>
                    <View style={{
                        // backgroundColor:"green",
                        //外边距
                        marginLeft: 0,
                        marginRight: 16,
                        marginTop: 0,
                        marginBottom: 0,
                        height:24,
                        width:24
                    }}>
                        <Image style={{ height: 24 , width: 24}} source={require('../../../assets/icon/iconfont/preview_new.png')}></Image>
                    </View>
                </TouchableOpacity>
            </View>
            <View style={styles.lineViewStyle}/>
            </View>
        )
    }
    trackingRenderRow = (item, index) => {
        return (
            <View key={item.trackingId+"trackingId"} style={{
                backgroundColor:"white",
                marginTop:2}}>
                {/* 成果顶部信息 */}
                <View style={{flexDirection: 'row', marginLeft: 14,marginRight:11, marginTop: 11,alignItems:"center",
                    // backgroundColor:"red"
                }}>
                    {
                        this.state.checkOutUserPhoto ?
                            <Image source={{ uri:constants.image_addr + '/'+ this.state.checkOutUserPhoto }} style={{ height: 32, width: 32, borderRadius: 50}} />
                            :
                            <ImageBackground source={require('../../../assets/icon/iconfont/profilePicture.png')} style={{ width: 32, height: 32}}>
                                <View style={{height: 32,width:32,justifyContent: "center",alignItems: "center"}}>
                                    {
                                        this.state.checkOutUserName&&this.state.checkOutUserName <= 2 ? 
                                        <Text style={{color:'#FFFFFF',fontSize:12,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {this.state.checkOutUserName}
                                        </Text>
                                        :
                                        <Text style={{color:'#FFFFFF',fontSize:12,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                            {this.state.checkOutUserName?this.state.checkOutUserName.slice(-2):""}
                                        </Text>
                                    }
                                </View>
                            </ImageBackground>
                    }
                    <View style={{marginLeft:11, flexDirection: 'row' ,width:screenWidth-14*2-32-35-11,
                    // backgroundColor:"white",
                    alignItems:"center"}}>
                        <Text style={{ fontSize: 16 }}>{this.state.checkOutUserName}</Text>

                            <Image style={{ height: 13 , width: 12, marginLeft: 9}} source={require('../../../assets/icon/iconfont/clock.png')}></Image>
                            <Text style={{ fontSize: 13 ,marginLeft: 3}}>{item.gmtCreated}</Text>
                    </View>
                    {/* <View style={[{width: 35,  
                        // backgroundColor:"green",
                        height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                        <Image style={{ width: 28, height: 28 }} source={require('../../../assets/icon/iconfont/more.png')}></Image>
                    </View> */}
                </View>
                <View style={{marginLeft: 55,marginRight: 11 ,marginBottom:17,
                    // backgroundColor:"green"
                    }}>
                    <Text style={{fontSize:12,color:'rgba(0, 10, 32, 0.65)'}}>{item.trackRemark}</Text>
                </View>
                <View style={styles.lineViewStyle}/> 
            </View>
        )
    }
    render(){
        return(
            <View >
                <CommonHeadScreen title={'任务' + this.state.courseSort + ' ' + this.state.courseName}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                {/* 封面 */}
                <View 
                // tyle={CommonStyle.onlyContainHeaderContentViewStyle}
                >
                    {
                        this.state.coursePhoto?
                        <Image style={{ width: screenWidth, height: screenWidth*0.637}} source={{uri:constants.image_addr + '/' + this.state.coursePhoto }}/>
                        :
                        <View>
                            <View style={{         
                                //外边距
                                position: 'absolute',
                                // backgroundColor:"white",
                                height:143,width:313,
                                // justifyContent: 'center',
                                alignItems:"center",
                                zIndex: 1,
                                top: (screenWidth*0.637-143)/2,
                                left: (screenWidth-313)/2,
                            }}>
                                <Text style={{fontSize:30,color:"white"}}>{'任务' + this.state.courseSort + ' '}</Text>
                                <Text style={{fontSize:30,color:"white"}}>{this.state.courseName}</Text>                        
                            </View>
                            <Image style={{ width: screenWidth, height: screenWidth*0.637}} source={require('../../../assets/image/defaultCover.png')}/>                        
                        </View>
                    }
                </View>
                {/* 详情 */}
                <View style={{position: 'relative',top:-28,zIndex: 2}}>
                    {/* 标题与头部 */}
                    <View style={{ width: screenWidth,backgroundColor:"white",borderTopLeftRadius:10,borderTopRightRadius:10 ,
                    }}>
                        <View style={{ flexDirection: 'row' ,height:23+8,borderTopLeftRadius:10,borderTopRightRadius:10}}>
                            <View style={{         
                                //外边距
                                marginLeft: 13,
                                marginRight: 0,
                                marginTop: 8,
                                marginBottom: 0,
                                width: screenWidth-55-13
                            }}
                            >
                                <Text>{'任务' + this.state.courseSort + ' ' + this.state.courseName}</Text>
                            </View>

                            {
                                this.state.taskState === '0AA' ?
                                    <View style={{         
                                        //外边距
                                        marginLeft: 0,
                                        marginRight: 0,
                                        marginTop: 0,
                                        marginBottom: 0,
                                        borderTopRightRadius:10,
                                        borderBottomLeftRadius:10,
                                        backgroundColor:"#1E6EFA",
                                        height:19,width:55
                                    }}
                                    >
                                        <Text style={{color:"white",marginLeft:5}}>实习中</Text>
                                    </View>
                                    :
                                    (
                                        this.state.taskState === '0BB' ?
                                            <View style={{         
                                                //外边距
                                                marginLeft: 0,
                                                marginRight: 0,
                                                marginTop: 0,
                                                marginBottom: 0,
                                                borderTopRightRadius:10,
                                                borderBottomLeftRadius:10,
                                                backgroundColor:"#FD4246",
                                                height:19,width:55
                                            }}
                                            >
                                                <Text style={{color:"white",marginLeft:5}}>已超期</Text>
                                            </View> 
                                            :
                                            (
                                                this.state.taskState === '0CC' ?
                                                <View style={{         
                                                    //外边距
                                                    marginLeft: 0,
                                                    marginRight: 0,
                                                    marginTop: 0,
                                                    marginBottom: 0,
                                                    borderTopRightRadius:10,
                                                    borderBottomLeftRadius:10,
                                                    backgroundColor:"green",
                                                    height:19,width:55
                                                }}
                                                >
                                                    <Text style={{color:"white",marginLeft:5}}>已完成</Text>
                                                </View>
                                                    :
                                                    <Text />
                                            )
                                    )
                            }                                  
                        </View>
                        <View style={{         
                                //外边距
                                marginLeft: 14,
                                marginRight: 14,
                                height:20+7+7,
                                flexDirection:"row",
                                justifyContent:"space-between",
                                alignItems:"center"
                            }}>
                            <View  style={{         
                                    //外边距
                                    borderRadius:10,
                                    backgroundColor:'rgba(27,188,130,0.2)',
                                    height:20,
                                    paddingLeft:10,paddingRight:10,
                                    justifyContent: 'center',
                                    alignItems:"center"
                                }}>
                                <Text
                                    style={{fontSize:12,color:"#1BBC82"}}
                                >{this.state.courseTypeName}</Text>
                            </View>                              
                        </View>

                        <View  style={{         
                                //外边距
                                marginLeft: 14,
                                marginRight: 14,
                                marginTop: 14,
                                marginBottom: 8,
                                borderRadius:4,
                                backgroundColor:'#F8F9FC',
                                width:screenWidth-25
                            }}>
                                <Text
                                    style={{fontSize:12,color:"rgba(0,10,32,0.85)",margin:5}}
                                >{"实习内容： "+(this.state.courseContent?this.state.courseContent:"无")}</Text>
                        </View>
                        <View  style={{         
                                //外边距
                                marginLeft: 14,
                                marginRight: 14,
                                marginBottom: 8,
                                height:20,
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems:"center"
                            }}>
                            <View style={{         
                                // backgroundColor:"blue",
                                height:17,width:135,
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems:"center"
                            }}>
                                <Image style={{ width: 10, height: 11}} source={require('../../../assets/icon/iconfont/time.png')}></Image>
                                <Text style={{ width: 120, height: 17,fontSize:11}}>{
                                    this.state.gmtCreated.slice(0,10)
                                    + " 开始实习"}</Text>
                            </View>

                            {
                                this.state.taskState=="0CC"?
                                <View style={{         
                                    //外边距
                                    // backgroundColor:"blue",
                                    height:17,width:135,
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    alignItems:"center"
                                }}>
                                    <Image style={{ width: 10, height: 11}} source={require('../../../assets/icon/iconfont/time.png')}></Image>
                                    <Text style={{ width: 120, height: 17,fontSize:11}}>{this.state.actualCompletionTime + " 已经完成"}</Text>
                                </View>                                
                                :
                                <View style={{         
                                    //外边距
                                    // backgroundColor:"blue",
                                    height:17,width:135,
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    alignItems:"center"
                                }}>
                                    <Image style={{ width: 10, height: 11}} source={require('../../../assets/icon/iconfont/time.png')}></Image>
                                    <Text style={{ width: 120, height: 17,fontSize:11}}>{this.state.planCompletionTime + " 计划完成"}</Text>
                                </View>
                            }
                        </View>
                        <View style={styles.lineViewStyle}/> 
                    </View>
                    {/* tab分类与列表 */}
                    <View style={{
                    // borderColor:"blue",borderWidth:10,
                    // height:"100%",
                    }}>
                        {/* tab分类 */}
                        <View style={[CommonStyle.headViewStyle]}>
                            <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                                {
                                    (this.state.taskStateDataSource && this.state.taskStateDataSource.length > 0)
                                        ?
                                        this.state.taskStateDataSource.map((item, index) => {
                                            return this.renderTaskStateRow(item)
                                        })
                                        : <View />
                                }
                            </View>
                        </View>
                        {/* 列表 */}
                        <View style={{height:this.state.maxlen*(82)+28}}>
                            <FlatList
                                data={this.state.trackingDataSource}
                                renderItem={({ item, index }) => this.trackingRenderRow(item, index)}
                                ListEmptyComponent={this.emptyComponent}
                            />
                        </View>
                    </View>
                </View>
                </ScrollView>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentTextStyle: {
        marginLeft: 12,
        marginRight: 16,
        marginTop: 3,
        lineHeight: 24,
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    lineViewStyle:{
        // height:1,
        marginLeft: 13,
        marginRight: 13,
        // marginTop: 15,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },
});
