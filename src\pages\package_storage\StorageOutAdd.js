import React,{Component} from 'react';
import {
    Alert,Modal,
    View, 
    ScrollView, 
    Text, 
    TextInput, 
    StyleSheet, 
    FlatList ,
    TouchableOpacity,
    Dimensions,
    Image,
    KeyboardAvoidingView
} from 'react-native';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadMultiImageLibrary } from '../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

const leftLabWidth = 130;
export default class StorageOutAdd extends Component {
    constructor(props) {
        super(props);
        this.state ={
            labelEnable:true,
            storageOutId:'',
            // 客户数据源、运送人数据源、订单数据源
            customerDataSource:[],

            // 底部滚动选择返回结果
            selectCustomer:[],
            selectContract:[],
            contractDataSource: [],
            contractId:"",
            contractName:"",

            selectCheckOutTime:[],
            selectExpectDeliveryTime:[],
            modal: false,
            searchKeyWord: null,
            _customerDataSource: [],
            selCustomerId:"",
            selCustomerName:"",
            customerId:"",
            customerName:"",
            checkOutTime:'',
            driverName: "",
            driverNameExt:"",
            driverTel:"",
            licensePlate:"",
            expectDeliveryTime:"",

            // 成品出库详细
            spStorageOutDetailDTOList:[],
            // 配送方式
            deliveryModeEnumDataSource:[],
            // 车型
            carModelEnumDataSource:[],
            selDeliveryMode:"",
            selDeliveryModeName:"",
            selCarModel:"",
            selCarModelName:"",
            consignee:"",
            consigneeTel:"",
            remark:"",
            operate:"",
            compressFileList:[],
            isShowImage: false,     //  显示弹窗组件  
            urls:[
                // {
                //     url:'http://10.162.210.158/image_a/10/2021-12/rn_image_picker_lib_temp_a45ead39-8c25-4fb5-b388-12be9d46cf62_200052052_small.jpg'
                // }
            ],
            pictureIndex:0
        }
    }

    UNSAFE_componentWillMount(){
        console.log('=aaaa=UNSAFE_componentWillMount==');

        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);

        // 5天后
        var xDay = 5;
        var currentNextXDayDate = new Date();
        currentNextXDayDate.setDate(currentNextXDayDate.getDate() + xDay);
        var currentNextXDayDateMonth = ("0" + (currentNextXDayDate.getMonth() + 1)).slice(-2);
        var currentNextXDayDateDay = ("0" + currentNextXDayDate.getDate()).slice(-2);

        this.setState({
            checkOutTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
            selectCheckOutTime:[currentDate.getFullYear(), currentDateMonth, currentDateDay],


            expectDeliveryTime:currentNextXDayDate.getFullYear() + "-" + currentNextXDayDateMonth + "-" + currentNextXDayDateDay,
            selectExpectDeliveryTime:[currentNextXDayDate.getFullYear(), currentNextXDayDateMonth, currentNextXDayDateDay],
        })
        
        // 加载客户
        this.loadCustomerData();

        var _deliveryModeEnumDataSource = [
            {
                "code":"T",
                "name":"来车自提"
            },
            {
                "code":"E",
                "name":"本厂车送"
            },
            {
                "code":"R",
                "name":"火车运输"
            }
        ];
        var _carModelEnumDataSource = [
            {
                "code":"A",
                "name":"13米半挂"
            },
            {
                "code":"B",
                "name":"4.2米"
            }
        ];
        this.setState({
            deliveryModeEnumDataSource:_deliveryModeEnumDataSource,
            carModelEnumDataSource:_carModelEnumDataSource,
            selDeliveryMode:_deliveryModeEnumDataSource[0].code,
            selDeliveryModeName:_deliveryModeEnumDataSource[0].name,
        })
        if(constants.loginUser.tenantId === 66) {
            this.setState({
                selCarModel:_carModelEnumDataSource[0].code,
                selCarModelName:_carModelEnumDataSource[0].name,
            })
        }

        // 豫兴默认承运人
        // if (constants.loginUser.tenantId === 66) {
        //     this.setState({
        //         driverName:"河南省中小企业上市服务公司郑州市分公司"
        //     })
        // }

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageOutId,compressFileList } = route.params;
            if (storageOutId) {
                console.log("========Edit==storageOutId:", storageOutId);
                this.setState({
                    labelEnable:false,
                    storageOutId:storageOutId,
                    operate:"继续"
                })
                loadRequest={"storageOutId": storageOutId};
                loadTypeUrl= "/biz/storage/out/get";
                httpPost(loadTypeUrl, loadRequest, this.loadEditStorageOutDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
            if (compressFileList) {
                this.setState({
                    compressFileList:compressFileList,
                })
            }
        }
    }

    loadEditStorageOutDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            var selectCheckOutTime = response.data.checkOutTime.split("-");
            this.setState({
                storageOutId:response.data.storageOutId,
                selCustomerId:response.data.customerId,
                selCustomerName:response.data.customerName,
                contractId:response.data.contractId,
                contractName:response.data.contractName,
                driverName: response.data.driverName,
                driverNameExt: response.data.driveNameExt,
                driverTel:response.data.driverTel,
                licensePlate:response.data.licensePlate,
                selDeliveryMode:response.data.deliveryMode,
                selDeliveryModeName:response.data.deliveryModeName,
                selCarModel:response.data.carModels,
                selCarModelName:response.data.carModelsName,
                consignee:response.data.consignee,
                consigneeTel:response.data.consigneeTel,
                remark:response.data.remark,
                compressFileList:response.data.compressFileList,
                checkOutTime:response.data.checkOutTime,
                selectCheckOutTime:selectCheckOutTime,

            })
            var urls = [];
            if(response.data.compressFileList && response.data.compressFileList.length > 0){
                for(var i=0;i<response.data.compressFileList.length;i++){
                    var url = {
                        url:constants.image_addr + '/' +  response.data.compressFileList[i].compressFile
                    } 
                    urls=urls.concat(url)
                    console.log(url)
                }
            }
            this.setState({
                urls:urls
            })

            if (response.data.expectDeliveryTime) {
                var selectExpectDeliveryTime = response.data.expectDeliveryTime.split("-");
                this.setState({
                    expectDeliveryTime:response.data.expectDeliveryTime,
                    selectExpectDeliveryTime:selectExpectDeliveryTime,
                })
            }

            if (response.data.spStorageOutDetailDTOList && response.data.spStorageOutDetailDTOList.length > 0) {
                this.setState({
                    // 成品出库详细
                    // spStorageOutDetailDTOList:response.data.spStorageOutDetailDTOList,
                })
            }
            console.log("=======read=edit=data", this.state)
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadCustomerData=()=>{
        let loadUrl= "/biz/tenant/customer/list";
        let loadRequest={'currentPage':1,'pageSize':100};
        httpPost(loadUrl, loadRequest, this.callBackLoadCustomerData);
    }

    callBackLoadCustomerData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                customerDataSource:response.data.dataList,
                customerId:response.data.customerId,
            })
            // this.loadContractList(this.state.selCustomerId);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadCustomer = () => {
        var _customerDataSource = copyArr(this.state.customerDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _customerDataSource = _customerDataSource.filter(item => item.customerName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _customerDataSource: _customerDataSource,
        })
    }

    renderCustomerRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.storageOutId) {
                    return;
                }
                this.setState({
                    selCustomerId: item.customerId,
                    selCustomerName: item.customerName,
                    // selectContract:[],
                    // contractId:"",
                    // contractName:"",
                })
                // this.loadContractList()
                // let loadUrl = "/biz/contract/list";
                // let loadRequest = {
                //     "currentPage":1,
                //     "pageSize":1000,
                //     "customerId": item.customerId,
                // };
                // httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
            }}>
                <View key={item.customerId} style={[item.customerId === this.state.selCustomerId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.customerId === this.state.selCustomerId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.customerName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // loadContractList =()=>{
    //     let loadUrl = "/biz/contract/list";
    //     let loadRequest = {
    //         "currentPage":1,
    //         "pageSize":1000,
    //         "customerId": this.state.selCustomerId,
    //         "qryContent":"contract"
    //     };
    //     httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
    // }

    loadContractListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                contractDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    openContractSelect() {
        if (this.state.selCustomerName) {
            if (!this.state.contractDataSource || this.state.contractDataSource.length < 1) {
                WToast.show({ data: "请先添加合同" });
                return;
            }
            this.setState({
                orderDataSource: []
            })
            this.refs.SelectContract.showContract(this.state.selectContract, this.state.contractDataSource)
        }
        else {
            WToast.show({ data: "请先选择需货单位"});
            return;
        }
    }

    callBackContractValue(value) {
        console.log("==========合同选择的结果：", value)
        if (!value) {
            return;
        }
        var contractName = value.toString();
        this.setState({
            selectContract: value,
            contractName: contractName,
        })
        // this.loadOrderList(contractName);
        let loadUrl= "/biz/contract/getContractByName";
        let loadRequest={
            "contractName":contractName
        };
        httpPost(loadUrl, loadRequest, this.loadContractDetailCallBack);
    }

    loadContractDetailCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                contractId : response.data.contractId,
            })
            // this.loadOrderListByContractId(response.data.contractId);
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} >
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("StorageOutList") 
            // }}>
            //     <Text style={CommonStyle.headRightText}>出库管理</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>出库管理</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 渲染客户底部滚动数据
    openCustomerSelect(){
        this.refs.SelectCustomer.showCustomer(this.state.selectCustomer, this.state.customerDataSource)
    }

    callBackCustomerValue(value){
        console.log("==========客户选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectCustomer:value
        })
        var customerName = value.toString();
        let loadUrl= "/biz/tenant/customer/getCustomerByName";
        let loadRequest={
            "customerName":customerName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadCustomerDetailData);
    }

    callBackLoadCustomerDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            // 选择的客户ID
            var varCustomerId = response.data.customerId
            this.setState({
                customerName:response.data.customerName,
                customerId:varCustomerId,
            })

        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    openSchedulingProductionTime(){
        this.refs.SelectSchedulingProductionTime.showDate(this.state.selectCheckOutTime)
    }

    callBackSelectSchedulingProductionTimeValue(value){
        console.log("==========排产开始时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectCheckOutTime:value
        })
        if (this.state.selectCheckOutTime && this.state.selectCheckOutTime.length) {
            var checkOutTime = "";
            var vartime;
            for(var index=0;index<this.state.selectCheckOutTime.length;index++) {
                vartime = this.state.selectCheckOutTime[index];
                if (index===0) {
                    checkOutTime += vartime;
                }
                else{
                    checkOutTime += "-" + vartime;
                }
            }
            this.setState({
                checkOutTime:checkOutTime
            })
        }
    }

    openExpectDeliveryTime(){
        this.refs.SelectExpectDeliveryTime.showDate(this.state.selectExpectDeliveryTime)
    }

    callBackSelectExpectDeliveryTimeValue(value){
        console.log("==========预计送达时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectExpectDeliveryTime:value
        })
        if (this.state.selectExpectDeliveryTime && this.state.selectExpectDeliveryTime.length) {
            var expectDeliveryTime = "";
            var vartime;
            for(var index=0;index<this.state.selectExpectDeliveryTime.length;index++) {
                vartime = this.state.selectExpectDeliveryTime[index];
                if (index===0) {
                    expectDeliveryTime += vartime;
                }
                else{
                    expectDeliveryTime += "-" + vartime;
                }
            }
            this.setState({
                expectDeliveryTime:expectDeliveryTime
            })
        }
    }

    _loadFreshSpStorageOutDetailDTOList=(_spStorageOutDetailDTOList)=>{
        if (_spStorageOutDetailDTOList && _spStorageOutDetailDTOList.length > 0) {
            console.log("=========回退数据：", _spStorageOutDetailDTOList);
            this.setState({
                spStorageOutDetailDTOList:_spStorageOutDetailDTOList,
            })
        }
        else {
            console.log("=========回退不成功");
        }
    }

    // 运输方式
    renderDeliveryModeRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (!this.state.labelEnable) {
                    return;
                }
                this.setState({
                selDeliveryMode:item.code,
                selDeliveryModeName:item.name
            }) }}>
                <View key={item.code} style={[item.code === this.state.selDeliveryMode ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.code === this.state.selDeliveryMode ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.name}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 车型
    renderCarModelsRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (!this.state.labelEnable) {
                    return;
                }
                this.setState({
                selCarModel:item.code,
                selCarModelName:item.name
            }) }}>
                <View key={item.code} style={[item.code===this.state.selCarModel? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle,{} ]}>
                    <Text style={item.code===this.state.selCarModel? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.name}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
            <CommonHeadScreen title={this.state.operate+ '出库'} 
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
            />
            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: -2 }} />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>需货单位</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[(!this.state.customerDataSource || this.state.customerDataSource.length === 0|| !this.state.labelEnable) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (!this.state.labelEnable) {
                                    return;
                                }
                                if (this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                                    this.setState({
                                        _customerDataSource: copyArr(this.state.customerDataSource),
                                    })
                                }
                                this.setState({
                                    modal: true,
                                    searchKeyWord: ""
                                })

                                if (!this.state.selCustomerId && this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                                    this.setState({
                                        selCustomerId: this.state.customerDataSource[0].customerId,
                                        selCustomerName: this.state.customerDataSource[0].customerName,
                                        // contractId:"",
                                        // contractName:"",
                                        // selectContract:[],
                                    })
                                }
                            }}>
                                <View style={[this.state.selCustomerId && this.state.selCustomerName ?
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    :
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginRight: 8,
                                        marginTop: 4,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        paddingLeft:6,
                                        paddingRight:6,
                                        // width: (screenWidth - 54)/2,
                                        borderRadius: 4,
                                    }
                                ]}>
                                    <Text style={[this.state.selCustomerId && this.state.selCustomerName ?
                                        {
                                            color: '#1E6EFA'
                                        }
                                        :
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>
                                        {this.state.selCustomerId && this.state.selCustomerName ? ( this.state.selCustomerName) : '选择需货单位'}
                                    </Text>
                                </View>
                                {/* <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, {borderWidth:0, height:40, flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                    {this.state.selCustomerId && this.state.selCustomerName ? 
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.selCustomerName}
                                        </Text>
                                        :
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        选择需货单位
                                        </Text>
                                    }
                                </View> */}
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>客户合同</Text>
                        </View>
                        <TouchableOpacity onPress={() => {
                            if (!this.state.labelEnable) {
                                return;
                            }
                            this.openContractSelect()
                            }}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, {borderWidth:0}]}>
                                <Text style={[!this.state.labelEnable ? CommonStyle.disableViewStyle : { color: '#A0A0A0', fontSize: 15 } ]}>
                                    {!this.state.contractName ? "请选择" : this.state.contractName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadCustomer();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._customerDataSource && this.state._customerDataSource.length > 0)
                                                ?
                                                this.state._customerDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderCustomerRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selCustomerId) {
                                            let toastOpts = getFailToastOpts("您还没有选择需货单位");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        let loadUrl = "/biz/contract/list";
                                        let loadRequest = {
                                            "currentPage":1,
                                            "pageSize":1000,
                                            "partyA": this.state.selCustomerId,
                                        };
                                        httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>

                 <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                     <Text style={styles.leftLabRedTextStyle}>*</Text>
                         <Text style={styles.leftLabNameTextStyle}>
                         发货时间
                         </Text>
                         {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                     </View>
                     
                     <TouchableOpacity onPress={()=>{
                        if (!this.state.labelEnable) {
                            return;
                        }
                        this.openSchedulingProductionTime()
                     }}>
                     <View style={[CommonStyle.inputTextStyleTextStyle, { borderWidth:0, width: screenWidth - (leftLabWidth + 50) }]}>
                            <Text style={[!this.state.labelEnable ? CommonStyle.disableViewStyle : {color:'#A0A0A0', fontSize:15}]}>
                                {!this.state.checkOutTime ? "请选择发货时间" : this.state.checkOutTime}
                            </Text>
                        </View>
                    </TouchableOpacity>
                 </View>
                 <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                     <Text style={styles.leftLabRedTextStyle}>*</Text>
                         <Text style={styles.leftLabNameTextStyle}>
                         预计送达时间
                         </Text>
                         {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                     </View>
                     
                     <TouchableOpacity onPress={()=>{
                        if (!this.state.labelEnable) {
                            return;
                        }
                         this.openExpectDeliveryTime()
                     }}>
                     <View style={[CommonStyle.inputTextStyleTextStyle, { borderWidth:0, width: screenWidth - (leftLabWidth + 50) }]}>
                            <Text style={[!this.state.labelEnable ? CommonStyle.disableViewStyle : {color:'#A0A0A0', fontSize:15}]}>
                                {!this.state.checkOutTime ? "请选择预计送达时间" : this.state.expectDeliveryTime}
                            </Text>
                        </View>
                    </TouchableOpacity>
                 </View>
                 <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                 <View style={[styles.inputRowStyle]}>
                    <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                         <Text style={styles.leftLabNameTextStyle}>
                         运输方式
                         </Text>
                         {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                 </View>
                 <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                    {
                        (this.state.deliveryModeEnumDataSource && this.state.deliveryModeEnumDataSource.length > 0) 
                        ? 
                        this.state.deliveryModeEnumDataSource.map((item, index)=>{
                            return this.renderDeliveryModeRow(item)
                        })
                        : <EmptyRowViewComponent/> 
                    }
                </View>
                 {
                     (constants.loginUser.tenantId === 66)?
                     <View>
                        <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                        <View style={[styles.inputRowStyle,{}]}>
                            <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>
                                车型
                                </Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                            <View style={{width:screenWidth - leftLabWidth - 10, flexWrap:'wrap', flexDirection:'row'}}>
                                {
                                    (this.state.carModelEnumDataSource && this.state.carModelEnumDataSource.length > 0) 
                                    ? 
                                    this.state.carModelEnumDataSource.map((item, index)=>{
                                        return this.renderCarModelsRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                        </View>
                    </View>
                    :<View/>
                 }
                 <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                 <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                         <Text style={styles.leftLabNameTextStyle}>
                         运输车号
                         </Text>
                         {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <TextInput
                        placeholder={'请输入'}
                        editable={this.state.labelEnable}
                        onChangeText={(text) => {
                            this.setState({licensePlate:text})
                        }}
                        style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }, !this.state.labelEnable ? CommonStyle.disableViewStyle : null ]}>
                        {this.state.licensePlate}
                    </TextInput>
                 </View>
                 <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                     <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                         <Text style={styles.leftLabNameTextStyle}>
                         承运人
                         </Text>
                         {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                     </View>
                     <TextInput 
                        placeholder={'请输入'}
                        editable={this.state.labelEnable}
                        onChangeText={(text) => {
                            this.setState({driverName:text})
                        }}
                        style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }, !this.state.labelEnable ? CommonStyle.disableViewStyle : null ]}>
                            {this.state.driverName}
                        </TextInput>
                 </View>
                 {
                     (constants.loginUser.tenantId === 66)?
                     <View>
                        <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>
                                司机
                                </Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                            <TextInput 
                            placeholder={'请输入司机'}
                            editable={this.state.labelEnable}
                            onChangeText={(text) => {
                                this.setState({driverNameExt:text})
                            }}
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }, !this.state.labelEnable ? CommonStyle.disableViewStyle : null ]}>
                            {this.state.driverNameExt}
                            </TextInput>
                        </View>
                    </View>
                    :<View/>
                 }
                 <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                     <Text style={styles.leftLabRedTextStyle}>*</Text>
                         <Text style={styles.leftLabNameTextStyle}>
                             联系电话
                         </Text>
                         {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                     </View>
                     <TextInput 
                        keyboardType='numeric'
                        editable={this.state.labelEnable}
                        placeholder={'请输入'}
                        onChangeText={(text) => {
                            this.setState({driverTel:text})
                        }}
                        style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }, !this.state.labelEnable ? CommonStyle.disableViewStyle : null ]}>
                        {this.state.driverTel}
                    </TextInput>
                 </View>

                 <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                     <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                         <Text style={styles.leftLabNameTextStyle}>
                         收货人
                         </Text>
                         <Text style={styles.leftLabRedTextStyle}></Text>
                     </View>
                     <TextInput 
                        placeholder={'请输入'}
                        editable={this.state.labelEnable}
                        onChangeText={(text) => {
                            this.setState({consignee:text})
                        }}
                        style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }, !this.state.labelEnable ? CommonStyle.disableViewStyle : null ]}>
                            {this.state.consignee}
                        </TextInput>
                 </View>
                 <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                 <View style={styles.inputRowStyle}>
                     <View style={styles.leftLabView}>
                     <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                         <Text style={styles.leftLabNameTextStyle}>
                             收货人电话
                         </Text>
                     </View>
                     <TextInput 
                        keyboardType='numeric'
                        editable={this.state.labelEnable}
                        placeholder={'请输入'}
                        onChangeText={(text) => {
                            this.setState({consigneeTel:text})
                        }}
                        style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }, !this.state.labelEnable ? CommonStyle.disableViewStyle : null ]}>
                        {this.state.consigneeTel}
                    </TextInput>
                 </View>
                 
                <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                         <Text style={styles.leftLabNameTextStyle}>
                         备注
                         </Text>
                         {/* <Text style={styles.leftLabRedTextStyle}></Text> */}
                    </View>
                    <TextInput 
                        placeholder={'请输入'}
                        editable={this.state.labelEnable}
                        onChangeText={(text) => {
                            this.setState({remark:text})
                        }}
                        style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }, !this.state.labelEnable ? CommonStyle.disableViewStyle : null ]}>
                            {this.state.remark}
                    </TextInput>
                </View>
                <View style={CommonStyle.rowAddSegmentLineStyle}></View>

                <View style={styles.leftLabView}>
                <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>附件</Text>
                </View>
                <View>
                    {
                        this.state.compressFileList && this.state.compressFileList.length > 0 ?
                        (
                            <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                {
                                    this.state.compressFileList.map((item,index) =>{
                                        return(
                                            <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex'}]}>
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    isShowImage:true,
                                                    pictureIndex:index
                                                })
                                            }}>
                                                <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />                                                    
                                            </TouchableOpacity>
                                            <Modal visible={this.state.isShowImage} transparent={true}>
                                                <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} onSave={() => alert("点击了保存图片")} onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} />
                                            </Modal>
                                        </View>
                                        )
                                        
                                    })
                                }
                                <View style={[{ width: 120,height:150,marginLeft:24,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.labelEnable) {
                                            return;
                                        }
                                        uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                            console.log("========imageUploadResponse", imageUploadResponse)
                                            if (imageUploadResponse.code === 200) {
                                                WToast.show({ data: "上传成功" });
                                                let compressFileList = imageUploadResponse.data
                                                this.setState({
                                                    compressFileList: compressFileList
                                                })
                                            }
                                            else {
                                                WToast.show({ data: imageUploadResponse.message });
                                            }
                                        });

                                    }}>
                                        <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                            <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={[{width:66,height:66}, !this.state.labelEnable ? CommonStyle.disableViewStyle : null]}></Image>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            
                        )
                        :
                        <View style={[{ width: 120,height:150,marginLeft:24,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                        <TouchableOpacity onPress={() => {
                                if (!this.state.labelEnable) {
                                    return;
                                }
                                uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                    console.log("========imageUploadResponse", imageUploadResponse)
                                    if (imageUploadResponse.code === 200) {
                                        WToast.show({ data: "上传成功" });
                                        let compressFileList = imageUploadResponse.data
                                        this.setState({
                                            compressFileList: compressFileList
                                        })
                                    }
                                    else {
                                        WToast.show({ data: imageUploadResponse.message });
                                    }
                                });

                            }}>
                                <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                    <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={[{width:66,height:66}, !this.state.labelEnable ? CommonStyle.disableViewStyle : null]}></Image>
                                </View>
                            </TouchableOpacity>
                        </View>
                    }
                </View>
                <View style={CommonStyle.rowAddSegmentLineStyle}></View>
                <View style={styles.btnRowView}>
                    <TouchableOpacity onPress={()=>{
                        //  WToast.show({data:"功能未开通"});
                        if (this.state.selCustomerName) {
                            this.props.navigation.navigate("StorageOutAddSelDetail",
                            {
                                customerId:this.state.customerId,
                                spStorageOutDetailDTOList:this.state.spStorageOutDetailDTOList,
                                // 传递回调函数
                                refresh: this._loadFreshSpStorageOutDetailDTOList 
                            })
                        }
                        else {
                            WToast.show({data:"请先选择需货单位"});
                            return;
                        }
                     }}>
                         <View style={[styles.btnAddView,{backgroundColor: '#F2F5FC'}]}>
                             <Text style={[styles.btnAddText,{color: '#404956'}]}>+ 出库产品</Text>
                         </View>
                    </TouchableOpacity>
                    
                </View>
                 <View>
                    <FlatList 
                    data={this.state.spStorageOutDetailDTOList}
                    renderItem={({item}) => 
                    <View key={item._index} style={styles.titleViewStyle}>
                        <View style={{ }}>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                产品：{item.brickTypeName}
                            </Text>
                        </View>
                        <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10}]}>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>件数：{item.packageAmount ? item.packageAmount : "无"}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>出库数量：{item.outAmount}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>库区：{item.locationAreaName}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>库位：{item.locationName}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>重量(吨)：{item.outWeight}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>包装形式：{item.packageStyleName}</Text>
                            </View>
                        </View>
                    </View>}
                    />
                </View>
 
                <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        {/* // Alert.alert('确认','您确定要取消吗？',[
                        //     {text:"取消", onPress:()=>{
                        //         WToast.show({data:'点击了取消'});
                        //         // this在这里可用，传到方法里还有问题
                        //         // this.props.navigation.goBack();
                        //     }},
                        //     {text:"确定", onPress:()=>{this.props.navigation.goBack()}}
                        // ]);
                    // }}> */}
                        <View style={[CommonStyle.btnAddCancelBtnView]} >
                            {/* <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        // 检验参数是否合法
                        let toastOpts;
                        if (!this.state.selCustomerName || this.state.selCustomerName === 0) {
                            toastOpts = getFailToastOpts("请选择需货单位");
                            WToast.show(toastOpts)
                            return;
                        }
                        if (!this.state.checkOutTime || this.state.checkOutTime.length === 0) {
                            toastOpts = getFailToastOpts("请选择发货日期");
                            WToast.show(toastOpts)
                            return;
                        }
                        if (!this.state.licensePlate || this.state.licensePlate.length === 0) {
                            toastOpts = getFailToastOpts("请输入运输车号");
                            WToast.show(toastOpts)
                            return;
                        }
                        if(constants.loginUser.tenantId === 66){
                            if (!this.state.selDeliveryMode) {
                                toastOpts = getFailToastOpts("请选择车型");
                                WToast.show(toastOpts)
                                return;
                            }
                        }
                        // if (!this.state.driverName || this.state.driverName.length === 0) {
                        //     toastOpts = getFailToastOpts("请输入承运人");
                        //     WToast.show(toastOpts)
                        //     return;
                        // }
                        if (!this.state.driverTel || this.state.driverTel.length === 0) {
                            toastOpts = getFailToastOpts("请输入联系电话");
                            WToast.show(toastOpts)
                            return;
                        }
                        if (!isMobileNumber(this.state.driverTel)) {
                            toastOpts = getFailToastOpts("联系电话格式错误");
                            WToast.show(toastOpts)
                            return;
                        }

                        if (this.state.consigneeTel.length > 0 && !isMobileNumber(this.state.consigneeTel)) {
                            toastOpts = getFailToastOpts("收货人电话格式错误");
                            WToast.show(toastOpts)
                            return;
                        }

                        if (!this.state.spStorageOutDetailDTOList || this.state.spStorageOutDetailDTOList.length < 1) {
                            toastOpts = getFailToastOpts("至少出库一个产品");
                            WToast.show(toastOpts)
                            return;
                        }
                        Alert.alert('确认','您确定要保存吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    // 出库
                                    let url= "/biz/storage/out/add";
                                    // if (this.state.storageOutId) {
                                    //     console.log("=========Edit===storageOutId", this.state.storageOutId)
                                    //     url= "/biz/storage/out/modify";
                                    // }
                                    let requestParams={
                                        "storageOutId":this.state.storageOutId,
                                        "customerId": this.state.selCustomerId,
                                        "contractId": this.state.contractId,
                                        "driverName": this.state.driverName,
                                        "driverTel": this.state.driverTel,
                                        "licensePlate": this.state.licensePlate,
                                        "checkOutTime":this.state.checkOutTime,
                                        "expectDeliveryTime":this.state.expectDeliveryTime,
                                        "deliveryMode":this.state.selDeliveryMode,
                                        "carModels":this.state.selCarModel,
                                        "consignee":this.state.consignee,
                                        "consigneeTel":this.state.consigneeTel,
                                        "remark":this.state.remark,
                                        "operator":constants.loginUser.userName,
                                        "spStorageOutDetailDTOList":this.state.spStorageOutDetailDTOList,
                                        "driveNameExt":this.state.driverNameExt,
                                        "compressFileList":this.state.storageOutId ? null : this.state.compressFileList,
                                    };
                                    console.log("=========url:", url)
                                    console.log("=========requestParams:", requestParams)
                                    httpPost(url, requestParams, (response)=>{
                                        let toastOpts;
                                        switch (response.code) {
                                            case 200:
                                                if (this.props.route.params.refresh) {
                                                    this.props.route.params.refresh()
                                                }
                                                toastOpts = getSuccessToastOpts('保存完成');
                                                WToast.show(toastOpts)
                                                this.props.navigation.goBack()
                                                break;
                                            default:
                                                toastOpts = getFailToastOpts(response.message);
                                                WToast.show({data:response.message})
                                        }
                                    });
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.btnAddSaveBtnView]}>
                            {/* <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                        </View>
                    </TouchableOpacity>
                 </View>
            </ScrollView>
            <BottomScrollSelect 
                ref={'SelectCustomer'} 
                callBackCustomerValue={this.callBackCustomerValue.bind(this)}
            />
            <BottomScrollSelect 
                ref={'SelectSchedulingProductionTime'} 
                callBackDateValue={this.callBackSelectSchedulingProductionTimeValue.bind(this)}
            />
            <BottomScrollSelect 
                ref={'SelectExpectDeliveryTime'} 
                callBackDateValue={this.callBackSelectExpectDeliveryTimeValue.bind(this)}
            />
            <BottomScrollSelect
                ref={'SelectContract'}
                callBackContractValue={this.callBackContractValue.bind(this)}
            />
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({

    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:5,
        marginRight:3,
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    textCertain: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#FFFFFF',
        lineHeight: 24,
        marginTop:10,
        textAlign: 'center',
        // fontStyle: 'normal',
    },
    textCancel: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#404956',
        lineHeight: 24,
        marginTop:10,
        textAlign:'center'
        // fontStyle: 'normal',
    },
    textContainerCertain: {
        width: 180,
        height: 48,
        marginRight:8,
        backgroundColor: '#255BDA',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    textContainerCancel: {
        width: 180,
        height: 48,
        marginLeft:8,
        backgroundColor: '#FFFFFF',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
})