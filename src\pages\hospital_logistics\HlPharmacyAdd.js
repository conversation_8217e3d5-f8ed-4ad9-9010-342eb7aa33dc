import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class HlPharmacyAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            pharmacyId:"",
            pharmacyName:"",
            pharmacyAddr:"",
            pharmacyCode:"",
            pharmacySort:0,
            pharmacyDataSource:[
                {
                    hlPharmacyId:1,
                    pharmacyName:"门诊药房"
                },
                {
                    hlPharmacyId:2,
                    pharmacyName:"急诊药房"
                }
            ],
            selHlPharmacyId:1,
            selPharmacyName:"门诊药房",
            // selectedPharmacyDate:[],

            hospitalName:"",
            hospitalId:"",
            selectHospital:[],
            hospitalDataSource:[],
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadHospitalList()
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { pharmacyId, hospitalId, hospitalName} = route.params;
            if (pharmacyId) {
                console.log("=============pharmacyId" + pharmacyId + "");
                this.setState({
                    pharmacyId:pharmacyId,
                    operate:"编辑"
                })
                let loadTypeUrl= "/biz/hl/pharmacy/get";
                let loadRequest={'pharmacyId':pharmacyId};
                httpPost(loadTypeUrl, loadRequest, this.loadPharmacyCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }

            if (hospitalId) {
                console.log("========hospitalId:", hospitalId);
                this.setState({
                    hospitalId:hospitalId,
                })
            }
            if (hospitalName) {
                console.log("========hospitalName:", hospitalName);
                this.setState({
                    hospitalName:hospitalName,
                    selectHospital:[hospitalName]
                })
            }
        }
    }

    loadPharmacyCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                pharmacyName: response.data.pharmacyName,
                pharmacyAddr: response.data.pharmacyAddr,
                pharmacyCode: response.data.pharmacyCode,
                pharmacySort: response.data.pharmacySort,

                hospitalId:response.data.hospitalId,
                hospitalName:response.data.hospitalName,
                selectHospital:[response.data.hospitalName],
            })
        }
    }

    // 获取院区
    loadHospitalList=()=>{
        let url= "/biz/hl/hospital/list";
        let data={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, data, this.callBackLoadHospitalList);
    }

    callBackLoadHospitalList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log("======load==hospital==", response.data.dataList);
            this.setState({
                hospitalDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    //保存函数
    savePharmacy =()=> {
        console.log("=======savePharmacy");
        let toastOpts;
        
        if (!this.state.pharmacyName) {
            toastOpts = getFailToastOpts("请填写药房名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.pharmacyAddr) {
            toastOpts = getFailToastOpts("请填写药房位置");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.hospitalId) {
            toastOpts = getFailToastOpts("请选择所属院区");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/hl/pharmacy/add";
        if (this.state.pharmacyId) {
            console.log("=========Edit===pharmacyId", this.state.pharmacyId)
            url= "/biz/hl/pharmacy/modify";
        }
        let requestParams={
            pharmacyId:this.state.pharmacyId,
            pharmacyName: this.state.pharmacyName,
            pharmacyAddr: this.state.pharmacyAddr,
            hospitalId: this.state.hospitalId,
            pharmacySort:this.state.pharmacySort
        };
        httpPost(url, requestParams, this.savePharmacyCallBack);
    }

    // 保存回调函数
    savePharmacyCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    //药房名称展示
    renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selHlPharmacyId:item.hlPharmacyId,
                        selPharmacyName:item.pharmacyName
                    })
                    console.log("=======  " +item.pharmacyName);
                }}>
                    
                <View key={item.hlPharmacyId} style={[item.pharmacyName===this.state.selPharmacyName ? [CommonStyle.selectedBlockItemViewStyle,{paddingLeft:5, paddingRight:5}]
                    : [CommonStyle.blockItemViewStyle,{paddingLeft:5, paddingRight:5}]] }>
                    <Text style={item.pharmacyName===this.state.selPharmacyName ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.pharmacyName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    //渲染院区底部滚动数据
    openHospitalSelect() {
        
        if (!this.state.hospitalDataSource || this.state.hospitalDataSource.length < 1) {
            WToast.show({ data: "没有有效状态的院区，请确认" });
            return
        }
        console.log("==========院区数据源：", this.state.hospitalDataSource);
        this.refs.SelectHospital.showHospital(this.state.selectHospital, this.state.hospitalDataSource)
    }

    callBackHospitalValue(value) {
        console.log("==========院区选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectHospital: value
        })
        var hospitalName = value.toString();
        let loadUrl = "/biz/hl/hospital/getHospitalByName";
        let loadRequest = {
            "hospitalName": hospitalName
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    hospitalName: response.data.hospitalName,
                    hospitalId: response.data.hospitalId
                    
                })
            }
            else if (response.code == 401) {
                WToast.show({ data: response.message });
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLPharmacyList", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>药房设置</Text>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '药房'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}> 
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>药房名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            placeholder={'请输入药房名称'}
                            onChangeText={(text) => this.setState({pharmacyName:text})}
                            style={[styles.inputRightText]}>
                            {this.state.pharmacyName}
                        </TextInput>
                        {/* <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.pharmacyDataSource && this.state.pharmacyDataSource.length > 0) 
                                ? 
                                this.state.pharmacyDataSource.map((item, index)=>{
                                    return this.renderRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View> */}
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>所属院区</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openHospitalSelect()}>
                            <View style={styles.inputTextStyleTextStyle}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.hospitalName ? "请选择院区" : this.state.hospitalName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>药房位置</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            placeholder={'请输入药房位置'}
                            onChangeText={(text) => this.setState({pharmacyAddr:text})}
                            style={[styles.inputRightText]}>
                            {this.state.pharmacyAddr}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>关联ID号</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            placeholder={'请输入关联ID号'}
                            onChangeText={(text) => this.setState({pharmacyCode:text})}
                            style={[styles.inputRightText]}>
                            {this.state.pharmacyCode}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText]}
                            placeholder={'0'}
                            onChangeText={(text) => this.setState({ pharmacySort: text })}
                        >
                            {this.state.pharmacySort}
                        </TextInput>
                    </View>

                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.savePharmacy.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <BottomScrollSelect
                        ref={'SelectHospital'}
                        callBackHospitalValue={this.callBackHospitalValue.bind(this)}
                    />
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    inputTextStyleTextStyle: {
        width: screenWidth - (leftLabWidth + 30),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    }
});