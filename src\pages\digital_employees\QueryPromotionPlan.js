import React, {Component} from 'react';
import {
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  ImageBackground,
  Linking,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class QueryPromotionPlan extends Component {
  constructor(props) {
    super(props);
    this.state = {
      operate: '',
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      selCompletionStateCode: 'all',

      showSearchItemBlock: false,
      departmentDataSource: null,

      selDepartmentId: null,
      selDepartmentName: null,
      selDepartmentStaffList: null,
      selStaffId: null,
      selStaffName: null,
      selDepartmentStaffDataSource: [],

      qryStartTime: null,
      // qryStartTime:"2021-01-01",
      selectedQryStartDate: [],
      userPhotoUrl: constants.image_addr + constants.loginUser.userPhoto,
      userPhoto: '',
      exportPdfModal: false,
      closeOrRestartModal: false,
      completionState: '',
      PlanItem: [],
      isBottomHidden: false,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  initqryStartTime = () => {
    // 当前时间
    var currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 1);
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _qryStartTime =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      selectedQryStartDate: [
        currentDate.getFullYear(),
        currentDateMonth,
        currentDateDay,
      ],
      qryStartTime: _qryStartTime,
    });
    return _qryStartTime;
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    var _qryStartTime = this.initqryStartTime();
    console.log('componentWillMount==_qryStartTime', _qryStartTime);
    let loadTypeUrl = '/biz/department/list_for_tenant';
    let loadRequest = {qryAll_NoPower: 'Y', currentPage: 1, pageSize: 1000};
    httpPost(loadTypeUrl, loadRequest, (response) => {
      if (response.code == 200 && response.data) {
        this.setState({
          departmentDataSource: response.data,
        });
      }
    });

    httpPost(
      '/biz/portal/user/get',
      {userId: constants.loginUser.userId},
      (response) => {
        if (response.code === 200) {
          let userPhoto = response.data.userPhoto;
          this.setState({
            userPhotoUrl: constants.image_addr + '/' + userPhoto,
            userPhoto: userPhoto,
          });
        }
      },
    );

    let completionStateDataSource = [
      {
        stateCode: 'all',
        stateName: '全部',
      },
      {
        stateCode: 'I',
        stateName: '未完成',
      },
      {
        stateCode: 'C',
        stateName: '已完成',
      },
    ];
    this.setState({
      completionStateDataSource: completionStateDataSource,
    });

    const {route, navigation} = this.props;
    if (route && route.params) {
      const {selStaffId} = route.params;
      if (selStaffId) {
        console.log('=============selStaffId' + selStaffId + '');
        this.setState({
          selStaffId: selStaffId,
          operate: '成长明细',
          isBottomHidden: true,
        });
        this.loadPromotionPlanList(_qryStartTime, selStaffId);
      } else {
        this.setState({
          operate: '任务跟踪',
        });
        this.loadPromotionPlanList(_qryStartTime);
      }
    } else {
      this.setState({
        operate: '任务跟踪',
      });
      this.loadPromotionPlanList(_qryStartTime);
    }
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/promotion/plan/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      completionState:
        this.state.selCompletionStateCode === 'all'
          ? null
          : this.state.selCompletionStateCode,
      departmentId: this.state.selDepartmentId,
      userId: this.state.selStaffId,
      qryStartTime: this.state.qryStartTime,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/promotion/plan/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      completionState:
        this.state.selCompletionStateCode === 'all'
          ? null
          : this.state.selCompletionStateCode,
      departmentId: this.state.selDepartmentId,
      userId: this.state.selStaffId,
      qryStartTime: this.state.qryStartTime,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    if (this.state.refreshing) {
      WToast.show({data: 'loading...'});
      return;
    }
    this.setState({ refreshing: true }, () => {
          console.log('refreshing 已更新:', this.state.refreshing);
          // 在这里执行后续操作
          this.loadPromotionPlanList();
    });
  };

  loadPromotionPlanList = (_qryStartTime, selStaffId) => {
    let url = '/biz/promotion/plan/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      completionState:
        this.state.selCompletionStateCode === 'all'
          ? null
          : this.state.selCompletionStateCode,
      departmentId: this.state.selDepartmentId,
      qryStartTime: _qryStartTime ? _qryStartTime : this.state.qryStartTime,
      userId: selStaffId ? selStaffId : this.state.selStaffId,
    };
    httpPost(url, loadRequest, this.loadPromotionPlanListCallBack);
  };

  loadPromotionPlanListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deletePromotionPlan = (planId) => {
    console.log('=======delete=planId', planId);
    let url = '/biz/promotion/plan/delete';
    let requestParams = {planId: planId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  setPromotionPlan = (planItem, index) => {
    console.log('=======setPromotionPlan=planItem', planItem);
    let requestUrl = '/biz/promotion/plan/modify';
    let requestParams = {
      planId: planItem.planId,
      completionState: planItem.completionState === 'C' ? 'I' : 'C',
    };
    httpPost(requestUrl, requestParams, (response) => {
      if (response.code == 200) {
        // 更新页面上显示
        planItem.completionState = planItem.completionState === 'C' ? 'I' : 'C';
        let promotionPlanDataSource = this.state.dataSource;
        // JS 数组遍历
        promotionPlanDataSource.forEach((obj) => {
          if (obj.planId === planItem.planId) {
            obj.completionState = planItem.completionState;
            // obj.actualCompletionTime = response.data.actualCompletionTime;
            WToast.show({
              data:
                (planItem.completionState === 'C' ? '关闭' : '重启') + '完成',
            });
          }
        });
        this.callBackFunction();
      } else {
        WToast.show({data: response.message});
      }
    });
  };

  renderCompletionStateRow = (item, index) => {
    return (
      <View key={item.stateCode}>
        <TouchableOpacity
          onPress={() => {
            let selCompletionStateCode = item.stateCode;
            this.setState({
              selCompletionStateCode: selCompletionStateCode,
            });
            let loadUrl = '/biz/promotion/plan/list';
            let loadRequest = {
              currentPage: 1,
              pageSize: this.state.pageSize,
              completionState:
                selCompletionStateCode === 'all'
                  ? null
                  : selCompletionStateCode,
              departmentId: this.state.selDepartmentId,
              qryStartTime: this.state.qryStartTime,
              userId: this.state.selStaffId,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
          }}>
          {/* fsno */}
          <View
            key={item.stateCode}
            style={[
              {
                width: screenWidth / 3,
                height: 30,
                flexDirection: 'row',
                justifyContent: 'center',
              },
              // ,item.stateCode === this.state.selCompletionStateCode ?
              //     [styles.selectedBlockItemViewStyle]
              //     :
              //     [styles.blockItemViewStyle],
            ]}>
            <Text
              style={[
                item.stateCode === this.state.selCompletionStateCode
                  ? {
                      color: '#255BDA',
                      fontSize: 16,
                      fontWeight: '500',
                      lineHeight: 30,
                      textAlign: 'center',
                      borderColor: '#255BDA',
                      borderBottomWidth: 2,
                      paddingLeft: 5,
                      paddingRight: 5,
                    }
                  : {
                      color: '#2B333F',
                      fontSize: 16,
                      fontWeight: '500',
                      lineHeight: 30,
                      textAlign: 'center',
                    },
              ]}>
              {item.stateName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  renderRow = (item, index) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('PromotionPlanDetail', {
            // 传递参数
            planId: item.planId,
            userName: item.userName,
            trackFkId: item.planId,
            trackType: 'PP',
            // 传递回调函数
            refresh: this.callBackFunction,
          });
        }}>
        <View key={item.planId} style={[CommonStyle.innerViewStyle]}>
          {/* 任务顶部信息 */}
          <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
            {item.userPhoto ? (
              <Image
                source={{uri: constants.image_addr + '/' + item.userPhoto}}
                style={{height: 48, width: 48, borderRadius: 50}}
              />
            ) : (
              <ImageBackground
                source={require('../../assets/icon/iconfont/profilePicture.png')}
                style={{width: 48, height: 48}}>
                <View
                  style={{
                    height: 48,
                    width: 48,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  {item.userName.length <= 2 ? (
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: 17,
                        fontWeight: 'normal',
                        textAlign: 'center',
                        lineHeight: 22,
                      }}>
                      {item.userName}
                    </Text>
                  ) : (
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: 17,
                        fontWeight: 'normal',
                        textAlign: 'center',
                        lineHeight: 22,
                      }}>
                      {item.userName.slice(-2)}
                    </Text>
                  )}
                </View>
              </ImageBackground>
            )}

            <View style={{marginLeft: 11, flexDirection: 'column'}}>
              <View style={{flexDirection: 'row', marginTop: 4}}>
                <View style={{flexDirection: 'row'}}>
                  <Text style={{fontSize: 16}}>{item.userName}的任务</Text>
                </View>

                <View style={{flexDirection: 'row'}}>
                  {item.completionState === 'C' ? (
                    <View
                      style={{
                        width: 52,
                        height: 20,
                        marginLeft: 7,
                        borderRadius: 2,
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: '#1BBC82',
                      }}>
                      <Text style={{fontSize: 13, color: '#FFFFFF'}}>
                        已完成
                      </Text>
                    </View>
                  ) : (
                    <View
                      style={{
                        width: 52,
                        height: 20,
                        marginLeft: 7,
                        borderRadius: 2,
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: '#D50400',
                      }}>
                      <Text style={{fontSize: 13, color: '#FFFFFF'}}>
                        未完成
                      </Text>
                    </View>
                  )}
                </View>
              </View>

              <View style={{flexDirection: 'row'}}>
                <Image
                  style={{
                    height: 13,
                    width: 12,
                    marginTop: 5,
                    marginLeft: 1,
                    marginRight: 5,
                  }}
                  source={require('../../assets/icon/iconfont/clock.png')}></Image>
                <View style={{marginTop: 4, marginBottom: 3, marginRight: 4}}>
                  <Text
                    style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)'}]}>
                    {item.planCreatedTime} 提交
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* 分隔线 */}
          <View style={styles.lineViewStyle} />

          <View style={styles.titleViewStyle}>
            {/* <Text style={styles.titleTextStyle}>标题</Text> */}
            <View
              style={{
                height: 24,
                paddingLeft: 6,
                paddingRight: 6,
                position: 'absolute',
                right: 0,
                top: -12,
                borderRadius: 2,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#ECEEF2',
              }}>
              <Text style={{fontSize: 14, lineHeight: 20, color: '#404956'}}>
                {item.belongClassName}
              </Text>
            </View>
          </View>
          <View style={styles.itemContentTextStyle}>
              <Text style={[styles.itemContentStyle,{marginLeft: 15,marginTop:5,fontWeight: 'bold',fontSize: 20,color: '#404956'}]}>{item.planTitle}</Text>
          </View>
          {/* <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>内容</Text>
          </View> */}
          <View style={[styles.itemContentTextStyle, { marginBottom: 12 }]}>
              <Text style={[styles.itemContentStyle,{marginLeft:15}]}>{item.planContent}</Text>
          </View>

          {this.state.operate == '任务跟踪' ? (
            <View style={[CommonStyle.itemBottomBtnStyle, {flexWrap: 'wrap'}]}>
              <TouchableOpacity
                onPress={() => {
                  if (constants.loginUser.userId === item.checkInUserId) {
                    // let message = '您确定要' + (item.completionState === 'C' ? '重启' : '关闭') + '该任务吗？';
                    // Alert.alert('确认', message, [
                    //     {
                    //         text: "取消", onPress: () => {
                    //             WToast.show({ data: '点击了取消' });
                    //         }
                    //     },
                    //     {
                    //         text: "确定", onPress: () => {
                    //             WToast.show({ data: '点击了确定' });
                    //             this.setPromotionPlan(item);
                    //         }
                    //     }
                    // ]);

                    this.setState({
                      closeOrRestartModal: true,
                      completionState: item.completionState,
                      PlanItem: item,
                    });
                  }
                }}>
                <View>
                  {constants.loginUser.userId === item.checkInUserId ? (
                    <View
                      style={
                        item.completionState === 'C'
                          ? {
                              height: 30,
                              width: 75,
                              backgroundColor: '#FD4246',
                              margin: 10,
                              marginRight: 16,
                              flexDirection: 'row',
                              justifyContent: 'center',
                              alignItems: 'center',
                              borderRadius: 3,
                            }
                          : {
                              height: 30,
                              width: 75,
                              backgroundColor: '#FD4246',
                              margin: 10,
                              marginRight: 16,
                              flexDirection: 'row',
                              justifyContent: 'center',
                              alignItems: 'center',
                              borderRadius: 3,
                            }
                      }>
                      <Image
                        style={{width: 20, height: 20, marginRight: 5}}
                        source={require('../../assets/icon/iconfont/close.png')}></Image>
                      <Text style={{color: '#FFFFFF', fontSize: 16}}>
                        {item.completionState === 'C' ? '重启' : '关闭'}
                      </Text>
                    </View>
                  ) : (
                    <View
                      style={[
                        CommonStyle.itemBottomDetailBtnViewStyle,
                        {
                          width: 75,
                          flexDirection: 'row',
                          backgroundColor: 'grey',
                        },
                      ]}>
                      <Image
                        style={{width: 20, height: 20, marginRight: 5}}
                        source={require('../../assets/icon/iconfont/close.png')}></Image>
                      <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                        {item.completionState === 'C' ? '重启' : '关闭'}
                      </Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            </View>
          ) : (
            <View />
          )}
        </View>
      </TouchableOpacity>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 部门
  renderDepartmentRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selDepartmentId: item.departmentId,
            selDepartmentName: item.departmentName,
            selDepartmentStaffDataSource: item.departmentUserDTOList,
            selStaffId: null,
            selStaffName: null,
          });
        }}>
        <View
          key={'department_' + item.departmentId}
          style={[
            item.departmentId === this.state.selDepartmentId
              ? CommonStyle.choseToSearchItemsSelectedViewColor
              : CommonStyle.choseToSearchItemsViewColor,
            CommonStyle.choseToSearchItemsViewSize,
          ]}>
          <Text
            style={[
              item.departmentId === this.state.selDepartmentId
                ? CommonStyle.choseToSearchItemsSelectedTextStyle
                : CommonStyle.choseToSearchItemsTextStyle,
            ]}>
            {item.departmentName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderDepartmentStaffRow = (item, index) => {
    return (
      <View key={item.jobUserId}>
        <TouchableOpacity
          onPress={() => {
            this.setState({
              selStaffId: item.userId,
              selStaffName: item.staffName,
            });
          }}>
          <View
            key={'jobuser_' + item.jobUserId}
            style={[
              item.userId === this.state.selStaffId
                ? CommonStyle.choseToSearchItemsSelectedViewColor
                : CommonStyle.choseToSearchItemsViewColor,
              CommonStyle.choseToSearchItemsViewSize,
            ]}>
            <Text
              style={[
                item.userId === this.state.selStaffId
                  ? CommonStyle.choseToSearchItemsSelectedTextStyle
                  : CommonStyle.choseToSearchItemsTextStyle,
              ]}>
              {item.staffName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          // 触发-导出弹窗Modal
          this.setState({
            exportPdfModal: true,
          });
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 24, height: 24}}
          source={require('../../assets/icon/iconfont/Export.png')}></Image>
      </TouchableOpacity>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  // 显示搜索项目
  showSearchItemSelect() {
    if (
      !this.state.departmentDataSource ||
      this.state.departmentDataSource.length < 1
    ) {
      WToast.show({data: '请先添加部门'});
      return;
    }
    this.setState({
      showSearchItemBlock: true,
    });
  }

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/promotion_plan_query';
    let requestParams = {
      // "userId": constants.loginUser.userId,
      userId: this.state.selStaffId,
      qryStartTime: this.state.qryStartTime,
      currentPage: 1,
      pageSize: 1000,
      departmentId: this.state.selDepartmentId,
      completionState:
        this.state.selCompletionStateCode === 'all'
          ? null
          : this.state.selCompletionStateCode,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        // 直接打开外网链接
        Linking.openURL(response.data);
      }
    });
  };

  openQryStartDate() {
    this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate);
  }

  callBackSelectQryStartDateValue(value) {
    console.log('==========提交时间选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectedQryStartDate: value,
    });
    if (value && value.length) {
      var qryStartTime = '';
      var vartime;
      for (var index = 0; index < value.length; index++) {
        vartime = value[index];
        if (index === 0) {
          qryStartTime += vartime;
        } else {
          qryStartTime += '-' + vartime;
        }
      }
      this.setState({
        qryStartTime: qryStartTime,
      });
      let loadUrl = '/biz/promotion/plan/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        departmentId: this.state.selDepartmentId,
        userId: this.state.selStaffId,
        qryStartTime: qryStartTime,
        completionState:
          this.state.selCompletionStateCode === 'all'
            ? null
            : this.state.selCompletionStateCode,
      };
      httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }
  }

  render() {
    return (
      <View
        style={{
          height: this.state.isBottomHidden ? screenHeight : null,
          backgroundColor: '#ffffff',
        }}>
        <CommonHeadScreen
          title={this.state.operate}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.headViewStyle,
            {width: screenWidth, borderWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View style={{flexDirection: 'row'}}>
            {this.state.operate == '任务跟踪' &&
            this.state.completionStateDataSource &&
            this.state.completionStateDataSource.length > 0 ? (
              this.state.completionStateDataSource.map((item, index) => {
                return this.renderCompletionStateRow(item);
              })
            ) : (
              <View />
            )}
          </View>
        </View>
        <View
          style={[
            CommonStyle.headViewStyle,
            {
              flexDirection: 'row', // 添加这一行以使子元素水平排列
              alignItems: 'center', // 添加这一行以使子元素垂直居中对齐
              borderWidth: 2,
            },
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={{
              flexDirection: 'row', // 添加这一行以使子元素水平排列
              alignItems: 'center', // 添加这一行以使子元素垂直居中对齐
            }}>
            {this.state.operate == '任务跟踪' ? (
              <View
                style={[CommonStyle.headViewStyle]}
                onLayout={this.topBlockLayout.bind(this)}>
                <View
                  style={[
                    {
                      backgroundColor: '#ffffff',
                      flexDirection: 'row',
                      width: screenWidth / 1.75,
                      justifyContent: 'flex-start',
                    },
                  ]}>
                  <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                    {this.state.showSearchItemBlock ? (
                      <View style={[CommonStyle.choseToSearchViewStyle]}>
                        <Text
                          style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                          {this.state.selDepartmentId &&
                          this.state.selDepartmentName
                            ? this.state.selDepartmentName +
                              (this.state.selStaffId && this.state.selStaffName
                                ? ' - ' + this.state.selStaffName
                                : '')
                            : '选择部门'}
                        </Text>
                        <Image
                          style={[CommonStyle.choseToSearchClosedIconSize]}
                          source={require('../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                      </View>
                    ) : (
                      <View style={[CommonStyle.choseToSearchViewStyle]}>
                        <Text
                          style={[CommonStyle.choseToSearchClosedTextStyle]}>
                          {this.state.selDepartmentId &&
                          this.state.selDepartmentName
                            ? this.state.selDepartmentName +
                              (this.state.selStaffId && this.state.selStaffName
                                ? ' - ' + this.state.selStaffName
                                : '')
                            : '选择部门'}
                        </Text>
                        <Image
                          style={[CommonStyle.choseToSearchOpenedIconSize]}
                          source={require('../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                      </View>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <View />
            )}
            <TouchableOpacity
              style={{
                width: screenWidth / 3,
                flexDirection: 'row',
                justifyContent: 'flex-end',
                marginLeft: 10,
              }}
              onPress={() => this.openQryStartDate()}>
              <View
                style={[
                  {
                    height: 32,
                    paddingLeft: 10,
                    paddingRight: 10,
                    opacity: 0.6,
                    borderRadius: 8,
                    backgroundColor: 'rgba(242, 245, 252, 1)',
                    justifyContent: 'center',
                    alignItems: 'center',
                  },
                ]}>
                <Text style={{color: 'rgba(0,10,32,0.85)', fontSize: 14}}>
                  {!this.state.qryStartTime ? '时间' : this.state.qryStartTime}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        <View>
          {this.state.showSearchItemBlock ? (
            <View
              style={[
                CommonStyle.choseToSearchBigBoxViewStyle,
                {
                  height: ifIphoneXContentViewDynamicHeight(
                    this.state.topBlockLayoutHeight,
                  ),
                },
              ]}>
              <View style={CommonStyle.heightLimited}>
                <ScrollView>
                  <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                    <View
                      style={[
                        {backgroundColor: 'rgba(255,255,255,1)'},
                        CommonStyle.choseToSearchItemsViewSize,
                      ]}>
                      <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                        部门：
                      </Text>
                    </View>
                    {this.state.departmentDataSource &&
                    this.state.departmentDataSource.length > 0
                      ? this.state.departmentDataSource.map((item, index) => {
                          return this.renderDepartmentRow(item);
                        })
                      : null}
                  </View>
                  {this.state.selDepartmentStaffDataSource &&
                  this.state.selDepartmentStaffDataSource.length > 0 ? (
                    <View
                      style={[
                        {
                          paddingTop: 13,
                          paddingBottom: 7,
                          paddingLeft: 10,
                          paddingRight: 10,
                          width: screenWidth,
                          flexWrap: 'wrap',
                          flexDirection: 'row',
                          backgroundColor: 'rgba(255,255,255,1)',
                          borderBottomWidth: 1,
                          borderBottomColor: 'rgba(0,10,32,0.15)',
                          // borderStyle : 'solid',
                          // borderTopWidth: 0,
                          // borderTopColor: "rgba(0,10,32,0.15)"
                        },
                      ]}>
                      <View
                        style={[
                          {backgroundColor: 'rgba(255,255,255,1)'},
                          CommonStyle.choseToSearchItemsViewSize,
                        ]}>
                        <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                          提交人：
                        </Text>
                      </View>
                      {this.state.selDepartmentStaffDataSource.map(
                        (item, index) => {
                          return this.renderDepartmentStaffRow(item);
                        },
                      )}
                    </View>
                  ) : null}
                </ScrollView>
              </View>
              <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]}>
                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    let loadUrl = '/biz/promotion/plan/list';
                    let loadRequest = {
                      currentPage: 1,
                      pageSize: this.state.pageSize,
                      completionState:
                        this.state.selCompletionStateCode === 'all'
                          ? null
                          : this.state.selCompletionStateCode,
                      departmentId: this.state.selDepartmentId,
                      userId: this.state.selStaffId,
                      qryStartTime: this.state.qryStartTime,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>
                      确定搜索
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}
          {this.state.showSearchItemBlock ? (
            <View
              style={[
                CommonStyle.choseToSearchBigBoxViewStyle,
                {
                  height: ifIphoneXContentViewDynamicHeight(
                    this.state.topBlockLayoutHeight,
                  ),
                },
              ]}>
              <View style={CommonStyle.heightLimited}>
                <ScrollView>
                  <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                    <View
                      style={[
                        {backgroundColor: 'rgba(255,255,255,1)'},
                        CommonStyle.choseToSearchItemsViewSize,
                      ]}>
                      <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                        部门：
                      </Text>
                    </View>
                    {this.state.departmentDataSource &&
                    this.state.departmentDataSource.length > 0
                      ? this.state.departmentDataSource.map((item, index) => {
                          return this.renderDepartmentRow(item);
                        })
                      : null}
                  </View>
                  {this.state.selDepartmentStaffDataSource &&
                  this.state.selDepartmentStaffDataSource.length > 0 ? (
                    <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                      <View
                        style={[
                          {backgroundColor: 'rgba(255,255,255,1)'},
                          CommonStyle.choseToSearchItemsViewSize,
                        ]}>
                        <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                          提交人：
                        </Text>
                      </View>
                      {this.state.selDepartmentStaffDataSource.map(
                        (item, index) => {
                          return this.renderDepartmentStaffRow(item);
                        },
                      )}
                    </View>
                  ) : null}
                </ScrollView>
              </View>
              <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]}>
                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    let loadUrl = '/biz/promotion/plan/list';
                    let loadRequest = {
                      currentPage: 1,
                      pageSize: this.state.pageSize,
                      completionState:
                        this.state.selCompletionStateCode === 'all'
                          ? null
                          : this.state.selCompletionStateCode,
                      departmentId: this.state.selDepartmentId,
                      userId: this.state.selStaffId,
                      qryStartTime: this.state.qryStartTime,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>
                      确定搜索
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}

          <View
            style={[
              CommonStyle.contentViewStyle,
              {
                height: ifIphoneXContentViewDynamicHeight(
                  this.state.topBlockLayoutHeight,
                ),
              },
            ]}>
            <FlatList
              data={this.state.dataSource}
              keyExtractor={(item) => item.planId}
              renderItem={({item, index}) => this.renderRow(item, index)}
              ListEmptyComponent={this.emptyComponent}
              // 自定义下拉刷新
              refreshControl={
                <RefreshControl
                  tintColor="#FF0000"
                  title="loading"
                  colors={['#FF0000', '#00FF00', '#0000FF']}
                  progressBackgroundColor="#FFFF00"
                  refreshing={this.state.refreshing}
                  onRefresh={() => {
                    this._loadFreshData();
                  }}
                />
              }
              // 底部加载
              ListFooterComponent={() => this.flatListFooterComponent()}
              onEndReached={() => this._loadNextData()}
            />
          </View>
        </View>

        <BottomScrollSelect
          ref={'SelectQryStartDate'}
          callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
        />

        {/* 导出pdf弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.exportPdfModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认导出任务？</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  导出地址已复制到粘贴板，使用浏览器打开
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 291,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      exportPdfModal: false,
                    });
                    WToast.show({data: '点击了不打开'});
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      不打开
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    WToast.show({data: '点击了打开'});
                    this.setState({
                      exportPdfModal: false,
                    });
                    this.exportPdfFile();
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderLeftWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      打开
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* 关闭或重启弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.closeOrRestartModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 292,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  {'您确认要' +
                    (this.state.completionState === 'C' ? '重启' : '关闭') +
                    '该任务吗？'}
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 292,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      closeOrRestartModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      closeOrRestartModal: false,
                    });
                    WToast.show({data: '点击了确定'});
                    this.setPromotionPlan(this.state.PlanItem);
                  }}>
                  <View
                    style={[
                      {
                        width: 146,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      确认
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    // marginTop:10,
    backgroundColor: '#ffffff',
    borderColor: '#ffffff',
    borderWidth: 8,
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },

  // 分段器样式
  blockItemViewStyle: {
    margin: 5,
    width: 60,
    borderRadius: 0,
    paddingTop: 2,
    paddingBottom: 0,
    paddingLeft: 2,
    paddingRight: 2,
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    // marginTop: 0,
  },

  selectedBlockItemViewStyle: {
    margin: 5,
    width: 60,
    borderRadius: 0,
    paddingTop: 2,
    paddingBottom: 0,
    paddingLeft: 2,
    paddingRight: 2,
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    // marginTop: 0,
  },
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#F2F5FC',
    backgroundColor: '#FFFFFF',
    borderRadius: 50,
    marginTop: 5,
  },

  // modal全屏遮挡
  fullScreenKeepOut: {
    opacity: 1,
    height: screenHeight,
    width: screenWidth,
    backgroundColor: 'rgba(0, 10, 2,0.15)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // modal全屏遮挡 -- 内容样式
  modalContentViewStyle: {
    // height:ifIphoneXContentViewHeight(),
    width: screenHeight,
    backgroundColor: '#FFFFFF',
    padding: 10,
    borderRadius: 5,
  },
  modalSearchInputText: {
    width: screenWidth - 200,
    borderColor: '#000000',
    borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 18,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
  },
  itemContentStyle: {
    fontSize: 14,
    lineHeight: 24,
    textAlign: 'left',
    textAlignVertical: 'top',
    color: 'rgba(0, 10, 32, 0.65)',
  },
  itemContentTextStyle: {
    marginLeft: 14,
    marginRight: 16,
    marginTop: 3,
    lineHeight: 24,
  },
  titleViewStyle: {
    flexDirection: 'row',
    marginLeft: 14,
    marginRight: 16,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
    lineHeight: 22,
  },
  lineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    marginBottom: 6,
    borderBottomWidth: 0.5,
    borderColor: '#E8E9EC',
  },
});
