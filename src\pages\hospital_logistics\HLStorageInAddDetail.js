import React,{Component} from 'react';
import {
    Alert,
    View, 
    ScrollView, Image,Text,TextInput,Modal, StyleSheet, FlatList,TouchableOpacity, Dimensions
} from 'react-native';

import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class HLStorageInAddDetail extends Component {

    constructor(props) {
        super(props);
        this.state ={
            // orderDataSource:[],
            // checkOutDetailOrderList:[],
            detailId:'',
            searchKeyWord:"",
            selDetailId:0,
            materialId:'',
            selMaterialId:"",
            selMaterialName:"",
            materialDataSource:[],
            _materialDataSource:[],
            materialModal:'',
            unitId:'',
            materialAmount:0,
            materialPrice:0,
            materialTotalPrice:0,
            detailState:'',
            storageInId:'',
            modal:false,
            storageInDetailList:[],
        }
    }

    UNSAFE_componentWillMount(){
        console.log('=aaaa=UNSAFE_componentWillMount==');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageInId ,storageInFlag,hlStorageInDetailDTOList } = route.params;
            if (storageInId) {
                console.log("=========storageInId:", storageInId);
                this.setState({
                    storageInId:storageInId
                })
                // this.loadOrderData(customerId);
            } 
            if(storageInFlag) {
                console.log("=========storageInFlag:", storageInFlag);
                this.setState({
                    storageInFlag:storageInFlag
                })
            }
            if(storageInFlag) {
                console.log("=========hlStorageInDetailDTOList:", hlStorageInDetailDTOList);
                this.setState({
                    storageInDetailList:hlStorageInDetailDTOList
                })
            }
        }
        this.loadMaterialData();
    }

    //加载物资数据
    loadMaterialData=()=>{
        let loadUrl= "/biz/hl/material/listing/list";
        let loadRequest={'currentPage':1,'pageSize':100};
        httpPost(loadUrl, loadRequest, this.loadMaterialDataCallBack);
    }
        
    loadMaterialDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                materialDataSource: response.data.dataList,
                materialId:response.data.materialId
            })
            // this.loadBrickSeriesList();
            // console.log("===-=设置之后的id===" + this.state.brickClassId); 

        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    //加载物资
    loadMaterial = () => {
        var _materialDataSource = copyArr(this.state.materialDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _materialDataSource = _materialDataSource.filter(item => item.materialName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _materialDataSource: _materialDataSource,
        })
    }

    loadProtalMaterialListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                materialDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh(this.state.storageInDetailList)
                }
                if( this.state.storageInFlag == 'M') {
                    this.props.navigation.navigate("HLStorageInMAdd") 
                }
                if (this.state.storageInFlag == 'D') {
                    this.props.navigation.navigate("HLStorageInDAdd")
                }
                
            }}>
                <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                {/* <Text style={CommonStyle.headRightText}>完成</Text> */}
            </TouchableOpacity>
        )
    }

    // 渲染订单底部滚动数据
    openOrderSelect(){
        this.refs.SelectOrder.showOrder(this.state.selectOrder, this.state.orderDataSource)
    }

    renderMaterialItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.detailId) {
                    return;
                }
                this.setState({
                    selMaterialId:item.materialId,
                    selMaterialName:item.materialName,
                    unitName:item.unitName,
                    materialModal:item.materialModal,
                    materialPrice:item.materialPrice,
                })
            }}>
                <View key={item.materialId} style={item.materialId===this.state.selMaterialId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.materialId===this.state.selMaterialId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.materialName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='新增明细'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}> 
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>物资</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[(!this.state.materialDataSource || this.state.materialDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={()=>{
                            if (this.state.materialDataSource && this.state.materialDataSource.length > 0) {
                                this.setState({
                                    _materialDataSource: copyArr(this.state.materialDataSource),
                                })
                            }
                            if (!this.state.materialDataSource || this.state.materialDataSource.length === 0) {
                                
                                let errorMsg = '暂无物资';
                                Alert.alert('确认', errorMsg, [
                                    {
                                        text: "确定", onPress: () => {
                                            WToast.show({ data: '点击了确定' });
                                        }
                                    }
                                ]);
                                return;
                            }

                            this.setState({
                                modal:true,
                                searchKeyWord: ""
                            })

                            if (!this.state.selMaterialId && this.state.materialDataSource && this.state.materialDataSource.length > 0) {
                                this.setState({
                                    selMaterialId:this.state.materialDataSource[0].materialId,
                                    selMaterialName:this.state.materialDataSource[0].materialName,
                                    unitName:this.state.materialDataSource[0].unitName,
                                    unitId:this.state.materialDataSource[0].unitId,
                                    materialModal:this.state.materialDataSource[0].materialModal,
                                    materialPrice:this.state.materialDataSource[0].materialPrice,
                                })
                            }
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}]}>
                                <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                                    {this.state.selMaterialId && this.state.selMaterialName ? (this.state.selMaterialName) : "选择物资"}
                                </Text>
                            </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.modal}>
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={CommonStyle.modalContentViewStyle}>
                            <View style={CommonStyle.rowLabView}>
                                <View style={styles.leftLabViewImage}>
                                    <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                    <TextInput
                                        style={[styles.searchInputText]}
                                        returnKeyType="search"
                                        returnKeyLabel="搜索"
                                        onSubmitEditing={e => {
                                            this.loadMaterial();
                                    }}        
                                        placeholder={'物资名称'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                </View>
                                {/* <TouchableOpacity onPress={()=>{
                                    this.loadMaterial();
                                    }}>
                                    <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                        <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                    </View>
                                </TouchableOpacity> */}
                            </View>
                            <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._materialDataSource && this.state._materialDataSource.length > 0)
                                                ?
                                                this.state._materialDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderMaterialItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                            <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                <TouchableOpacity onPress={() => { 
                                    this.setState({
                                        modal:false,
                                    }) 
                                }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                    <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => {
                                    if (!this.state.selMaterialId) {
                                        let toastOpts = getFailToastOpts("您还没有选择物资");
                                        WToast.show(toastOpts);
                                        return;
                                    }
                                    let loadUrl = "/biz/protal/supplier/list";
                                        let loadRequest = {
                                            "currentPage":1,
                                            "pageSize":1000,
                                            // 此处可能存在问题 yty
                                            "material":this.state.selMaterialId,
                                            // "partyA": this.state.selCustomerId,
                                        };
                                        httpPost(loadUrl, loadRequest, this.loadProtalMaterialListCallBack);
                                        this.setState({
                                            modal: false,
                                            aterialAmount:0,
                                            materialTotalPrice:0
                                        })
                                }}>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                    <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                        <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>规格型号</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle,, { width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.materialModal ? "请先选择物资" : this.state.materialModal}
                            </Text>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>单位</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle,, { width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.unitName ? "请先选择物资" : this.state.unitName}
                            </Text>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>数量</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入数量'}
                            style={[styles.inputRightText]}
                            onChangeText={(text) => {
                                if (this.state.materialPrice) {
                                    this.setState({
                                        materialTotalPrice:(text*this.state.materialPrice).toFixed(2)
                                    })
                                }
                                this.setState({materialAmount:text})
                            }}
                            >
                                {this.state.materialAmount}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>价格</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle,, { width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.materialPrice ? "请先选择物资" : this.state.materialPrice}
                            </Text>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>金额</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入总金额'}
                            // onChangeText={(text) => this.setState({materialTotalPrice:text})}
                            onChangeText={(text) => this.setState({materialTotalPrice: this.state.materialAmount * this.state.materialPrice})}
                            style={[styles.inputRightText]}>
                            {this.state.materialTotalPrice}
                        </TextInput>
                        {/* <View style={CommonStyle.inputTextStyleTextStyle}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.materialTotalPrice ? "请先选择物资" : this.state.materialTotalPrice}
                            </Text>
                        </View> */}
                    </View>
                    <View style={styles.btnRowView}>
                     <TouchableOpacity onPress={()=>{
                        if (!this.state.selMaterialId) {
                            WToast.show({data:"请选择物资"});
                            return;
                        }
                        if (!this.state.materialAmount || this.state.materialAmount === "0") {
                            WToast.show({data:"请输入数量"});
                            return;
                        }
                        var storageInDetailDTO = {
                            _index:this.state.storageInDetailList.length,
                            materialId: this.state.selMaterialId,
                            materialName: this.state.selMaterialName,
                            materialModal:this.state.materialModal,
                            unitName:this.state.unitName,
                            unitId:this.state.unitId,
                            materialAmount:this.state.materialAmount,
                            materialPrice: this.state.materialPrice,
                            materialTotalPrice:this.state.materialTotalPrice,
                        }
                        var _hlStorageInDetailDTOList = this.state.storageInDetailList;
                        _hlStorageInDetailDTOList = _hlStorageInDetailDTOList.concat(storageInDetailDTO);
                        this.setState({
                            storageInDetailList:_hlStorageInDetailDTOList
                        })
                        this.setState({
                            selMaterialId:"",
                            selMaterialName:"",
                            // materialDataSource:[],
                            materialModal:"",
                            unitId:"",
                            unitName:"",
                            materialAmount:"",
                            materialPrice:"",
                            materialTotalPrice:"",
                        })
                     }}>
                         <View style={{marginRight:screenWidth/8}}>
                         {/* <View style={[styles.btnAddView]}> */}
                             {/* <Text style={styles.btnAddText}>新增</Text> */}
                             <Image style={{ width:25, height:25,justifyContent:'center'}} source={require('../../assets/icon/iconfont/add1.png')}></Image>
                        </View>
                     </TouchableOpacity>
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View>
                    <FlatList 
                    data={this.state.storageInDetailList}
                    renderItem={({item}) => 
                    <View key={item._index} style={styles.titleViewStyle}>

                        <View style={{marginTop:10 }}>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                名称：{item.materialName}
                            </Text>
                        </View>
                        <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10,marginTop:10}]}>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>规格型号：{item.materialModal ? item.materialModal : "无"}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>单位：{item.unitName ? item.unitName : "无"}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>数量：{item.materialAmount ? item.materialAmount : "无"}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>价格：{item.materialPrice ? item.materialPrice : "无"}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>金额：{item.materialTotalPrice ? item.materialTotalPrice : "无"}</Text>
                            </View>
                        </View>

                        
                        <TouchableOpacity
                            style={{marginLeft:-145,zIndex:1000,marginTop:130}}
                            onPress={() => {
                                console.log("========deleteStorageInDetailDTO")
                                var urls = this.state.storageInDetailList;
                                urls.splice(item._index,1);                   
                                console.log(urls)
                                this.setState({
                                    storageInDetailList:urls,
                                })
                            }}
                        >
                            <View style={styles.btnDeleteView}>
                                <Text style={styles.btnDeleteText}>-删除</Text>
                            </View>
                        </TouchableOpacity>


                    </View>
                    }
                    />
                </View>
                </ScrollView>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    searchInputText: {
        width: screenWidth -100,
        // borderColor: '#000000',
        // borderBottomWidth: 1,
        // marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        // marginLeft: 0,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabViewImage: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        borderWidth:1,
        borderColor:"#E4E4E4",
        borderRadius:5,
        marginTop:5,
        // marginRight:5
    },   
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth +30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', width:100, alignItems:'center', alignContent:'flex-end', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})