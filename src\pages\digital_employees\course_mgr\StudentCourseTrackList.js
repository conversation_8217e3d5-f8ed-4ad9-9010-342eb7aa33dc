import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  ImageBackground,
  Linking,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import EmptyListComponent from '../../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../../utils/ScreenUtil';

var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

const courseTypeBgColor = {
  0: '#FB7B04',
  1: '#1084FD',
  2: '#1E85A3',
  3: '#FBB100',
  4: '#BF181E',
  5: '#1B9342',
};

export default class StudentCourseTrackList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      operate: '',
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 5,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      showSearchItemBlock: false,
      departmentDataSource: null,
      selDepartmentId: null,
      selDepartmentName: null,
      selDepartmentStaffList: null,
      selStaffId: null,
      selStaffName: null,
      selDepartmentStaffDataSource: null,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    // 部门
    let loadTypeUrl = '/biz/department/list_for_tenant';
    let loadRequest = {qryAll: 'Y', currentPage: 1, pageSize: 1000};
    httpPost(loadTypeUrl, loadRequest, (response) => {
      if (response.code == 200 && response.data) {
        this.setState({
          departmentDataSource: response.data,
        });
      }
    });
    this.loadCourseTrackList();
  }

  // 回调函数
  callBackFunction = () => {
    console.log('==========callback=====');
    let url = '/biz/student/course/track/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      departmentId:
        this.state.selDepartmentId === 0 ? null : this.state.selDepartmentId,
      checkOutUserId: this.state.selStaffId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let loadTypeUrl = '/biz/student/course/track/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      departmentId:
        this.state.selDepartmentId === 0 ? null : this.state.selDepartmentId,
      checkOutUserId: this.state.selStaffId,
    };
    httpPost(loadTypeUrl, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    if (this.state.refreshing) {
      WToast.show({data: 'loading...'});
      return;
    }
    this.setState({ refreshing: true }, () => {
          console.log('refreshing 已更新:', this.state.refreshing);
          // 在这里执行后续操作
          this.loadCourseTrackList();
    });
  };

  loadCourseTrackList = () => {
    let url = '/biz/student/course/track/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      departmentId:
        this.state.selDepartmentId === 0 ? null : this.state.selDepartmentId,
    };
    httpPost(url, loadRequest, this.loadCourseTrackListCallBack);
  };

  loadCourseTrackListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      var dataAll = [...dataOld, ...dataNew];
      console.log(
        'loadCourseTrackListCallBack' + JSON.stringify(dataAll, null, 6),
      );
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  renderRow = (item, index) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('StudentCourseTrackDetailList', {
            selStaffId: item.userId,
            selStaffName: item.userName,
            checkOutUser: item.userName,
          });
        }}>
        <View
          key={item.trackId}
          style={[CommonStyle.innerViewStyle, {paddingBottom: 10}]}>
          {/* 实习顶部信息 */}
          <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
            {item.userPhoto ? (
              <Image
                source={{uri: constants.image_addr + '/' + item.userPhoto}}
                style={{height: 48, width: 48, borderRadius: 50}}
              />
            ) : (
              <ImageBackground
                source={require('../../../assets/icon/iconfont/profilePicture.png')}
                style={{width: 48, height: 48}}>
                <View
                  style={{
                    height: 48,
                    width: 48,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  {item.userName.length <= 2 ? (
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: 17,
                        fontWeight: 'normal',
                        textAlign: 'center',
                        lineHeight: 22,
                      }}>
                      {item.userName}
                    </Text>
                  ) : (
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: 17,
                        fontWeight: 'normal',
                        textAlign: 'center',
                        lineHeight: 22,
                      }}>
                      {item.userName.slice(-2)}
                    </Text>
                  )}
                </View>
              </ImageBackground>
            )}

            <View style={{marginLeft: 11, flexDirection: 'column'}}>
              <View style={{flexDirection: 'row', marginTop: 4}}>
                <View style={{flexDirection: 'row'}}>
                  <Text style={{fontSize: 16}}>{item.userName}</Text>
                </View>
              </View>
              <View style={{flexDirection: 'row'}}>
                <Text style={{color: 'rgba(0, 10, 32, 0.65)', fontSize: 12}}>
                  {item.departmentName}
                </Text>
              </View>
            </View>
            {/* <View style={{ position:'absolute', right: 13, top: 0}}>
                        <View style={[{width: 35, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                            <Image style={{ width: 28, height: 28 }} source={require('../../../assets/icon/iconfont/more.png')}></Image>
                        </View>
                    </View> */}
          </View>
          {/* 分隔线 */}
          <View style={styles.lineViewStyle} />
          <View
            style={[
              styles.titleViewStyle,
              {marginTop: 5, flexDirection: 'row'},
            ]}>
            <Text style={styles.titleTextStyle}>实习进度</Text>
            {/* 修改后端，在PortalUserDTO中添加了courseNumber字段 */}
            <Text style={{color: 'rgba(153, 153, 153, 1)'}}>
              {' (' + item.courseNumber + ')'}
            </Text>
          </View>
          <View>
            {item.studentCourseTrackDTOList &&
            item.studentCourseTrackDTOList.length > 0 ? (
              item.studentCourseTrackDTOList.map((item, index) => {
                return (
                  <View
                    key={item.courseId}
                    style={{
                      flexDirection: 'row',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginLeft: 12,
                      marginRight: 12,
                      marginTop: 3,
                      lineHeight: 24,
                      width: screenWidth - 24,
                    }}>
                    <View
                      style={{
                        fontWeight: '400',
                        paddingLeft: 4,
                        paddingRight: 4,
                        paddingTop: 2,
                        paddingBottom: 2,
                        backgroundColor: '#FD4246',
                        borderRadius: 6,
                        marginLeft: 3,
                      }}>
                      <Text style={{color: '#FFFFFF', fontSize: 12}}>
                        {item.courseLevelName}
                      </Text>
                    </View>
                    <Text
                      numberOfLines={1}
                      ellipsizeMode="tail"
                      style={[styles.itemContentStyle]}>
                      {'任务' + item.courseSort + ' ' + item.courseName}
                    </Text>

                    <View
                      style={{
                        fontWeight: '400',
                        backgroundColor:
                          courseTypeBgColor[(parseInt(index) + 1) % 6],
                        paddingLeft: 8,
                        paddingRight: 8,
                        height: 17,
                        borderRadius: 10,
                        paddingTop: 2,
                        paddingBottom: 2,
                      }}>
                      <Text style={{fontSize: 12, color: '#ffffff'}}>
                        {item.courseTypeName}
                      </Text>
                    </View>
                  </View>
                );
              })
            ) : (
              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: 40,
                }}>
                <Text>暂无进度</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 部门
  renderDepartmentRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selDepartmentId: item.departmentId,
            selDepartmentName: item.departmentName,
            selDepartmentStaffDataSource: item.departmentUserDTOList,
            selStaffId: null,
            selStaffName: null,
          });
        }}>
        <View
          key={'department_' + item.departmentId}
          style={[
            item.departmentId === this.state.selDepartmentId
              ? CommonStyle.choseToSearchItemsSelectedViewColor
              : CommonStyle.choseToSearchItemsViewColor,
            CommonStyle.choseToSearchItemsViewSize,
          ]}>
          <Text
            style={[
              item.departmentId === this.state.selDepartmentId
                ? CommonStyle.choseToSearchItemsSelectedTextStyle
                : CommonStyle.choseToSearchItemsTextStyle,
            ]}>
            {item.departmentName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderDepartmentStaffRow = (item, index) => {
    return (
      <View key={item.jobUserId}>
        <TouchableOpacity
          onPress={() => {
            this.setState({
              selStaffId: item.userId,
              selStaffName: item.staffName,
            });
          }}>
          <View
            key={'jobuser_' + item.jobUserId}
            style={[
              item.userId === this.state.selStaffId
                ? CommonStyle.choseToSearchItemsSelectedViewColor
                : CommonStyle.choseToSearchItemsViewColor,
              CommonStyle.choseToSearchItemsViewSize,
            ]}>
            <Text
              style={[
                item.userId === this.state.selStaffId
                  ? CommonStyle.choseToSearchItemsSelectedTextStyle
                  : CommonStyle.choseToSearchItemsTextStyle,
              ]}>
              {item.staffName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 22, height: 22}}
          source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          Alert.alert('确认', '您确定要导出PDF文件吗？', [
            {
              text: '取消',
              onPress: () => {
                WToast.show({data: '点击了取消'});
              },
            },
            {
              text: '确定',
              onPress: () => {
                WToast.show({data: '点击了确定'});
                this.exportPdfFile();
              },
            },
          ]);
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 23, height: 23}}
          source={require('../../../assets/icon/iconfont/newExport.png')}></Image>
      </TouchableOpacity>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  // 显示搜索项目
  showSearchItemSelect() {
    if (
      !this.state.departmentDataSource ||
      this.state.departmentDataSource.length < 1
    ) {
      WToast.show({data: '请先添加部门'});
      return;
    }
    this.setState({
      showSearchItemBlock: true,
    });
  }

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/student_course_track';
    let requestParams = {
      currentPage: 1,
      pageSize: 1000,
      departmentId:
        this.state.selDepartmentId === 0 ? null : this.state.selDepartmentId,
      checkOutUserId: this.state.selStaffId,
      // "dailyState":"0AA"
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="成员进度"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[CommonStyle.headViewStyle]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View style={{width: '100%', flexWrap: 'wrap', flexDirection: 'row'}}>
            <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
              {this.state.showSearchItemBlock ? (
                <View style={[CommonStyle.choseToSearchViewStyle]}>
                  <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                    {this.state.selDepartmentId && this.state.selDepartmentName
                      ? this.state.selDepartmentName
                      : '选择部门'}
                  </Text>
                  <Image
                    style={[CommonStyle.choseToSearchClosedIconSize]}
                    source={require('../../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                </View>
              ) : (
                <View style={[CommonStyle.choseToSearchViewStyle]}>
                  <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                    {this.state.selDepartmentId && this.state.selDepartmentName
                      ? this.state.selDepartmentName
                      : '选择部门'}
                  </Text>
                  <Image
                    style={[CommonStyle.choseToSearchOpenedIconSize]}
                    source={require('../../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                </View>
              )}
            </TouchableOpacity>
            {this.state.selStaffId && this.state.selStaffName ? (
              <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                {this.state.showSearchItemBlock ? (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                      {this.state.selStaffName}
                    </Text>
                  </View>
                ) : (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                      {this.state.selStaffName}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            ) : null}
          </View>
        </View>
        <View>
          {this.state.showSearchItemBlock ? (
            <View
              style={[
                CommonStyle.choseToSearchBigBoxViewStyle,
                {
                  height: ifIphoneXContentViewDynamicHeight(
                    this.state.topBlockLayoutHeight,
                  ),
                },
              ]}>
              <View style={CommonStyle.heightLimited}>
                <ScrollView>
                  <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                    <View
                      style={[
                        {backgroundColor: 'rgba(255,255,255,1)'},
                        CommonStyle.choseToSearchItemsViewSize,
                      ]}>
                      <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                        部门：
                      </Text>
                    </View>
                    {this.state.departmentDataSource &&
                    this.state.departmentDataSource.length > 0
                      ? this.state.departmentDataSource.map((item, index) => {
                          return this.renderDepartmentRow(item);
                        })
                      : null}
                  </View>
                  {this.state.selDepartmentStaffDataSource &&
                  this.state.selDepartmentStaffDataSource.length > 0 ? (
                    <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                      <View
                        style={[
                          {backgroundColor: 'rgba(255,255,255,1)'},
                          CommonStyle.choseToSearchItemsViewSize,
                        ]}>
                        <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                          提交人：
                        </Text>
                      </View>
                      {this.state.selDepartmentStaffDataSource.map(
                        (item, index) => {
                          return this.renderDepartmentStaffRow(item);
                        },
                      )}
                    </View>
                  ) : null}
                </ScrollView>
              </View>
              <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]}>
                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    let loadUrl = '/biz/student/course/track/list';
                    let loadRequest = {
                      currentPage: 1,
                      pageSize: this.state.pageSize,
                      departmentId:
                        this.state.selDepartmentId === 0
                          ? null
                          : this.state.selDepartmentId,
                      checkOutUserId: this.state.selStaffId,
                    };
                    console.log('选择的部门=====' + this.state.selDepartmentId);
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>
                      确定搜索
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}

          <View
            style={[
              CommonStyle.contentViewStyle,
              {
                height: ifIphoneXContentViewDynamicHeight(
                  this.state.topBlockLayoutHeight,
                ),
              },
            ]}>
            <FlatList
              data={this.state.dataSource}
              keyExtractor={(item) => item.userId}
              renderItem={({item, index}) => this.renderRow(item, index)}
              ListEmptyComponent={this.emptyComponent}
              // 自定义下拉刷新
              refreshControl={
                <RefreshControl
                  tintColor="#FF0000"
                  title="loading"
                  colors={['#FF0000', '#00FF00', '#0000FF']}
                  progressBackgroundColor="#FFFF00"
                  refreshing={this.state.refreshing}
                  onRefresh={() => {
                    this._loadFreshData();
                  }}
                />
              }
              // 底部加载
              ListFooterComponent={() => this.flatListFooterComponent()}
              onEndReached={() => this._loadNextData()}
            />
          </View>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  itemContentTextStyle: {
    marginLeft: 12,
    marginRight: 16,
    marginTop: 3,
    lineHeight: 24,
  },
  titleViewStyle: {
    flexDirection: 'row',
    marginLeft: 12,
    marginRight: 16,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    width: screenWidth / 2,
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 24,
    textAlign: 'left',
    textAlignVertical: 'top',
    marginLeft: 5,
    // color: 'rgba(242, 245, 252, 1)'
  },
  lineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    marginBottom: 6,
    borderBottomWidth: 0.5,
    borderColor: '#E8E9EC',
  },
});
