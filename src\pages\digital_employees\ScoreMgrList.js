import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Image,
    FlatList, RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyRowViewComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class ScoreMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 1500,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            departmentDataSource: [],
            topBlockLayoutHeight: 0,
            selDepartmentId: null,
            courseId: null,
            courseName: null,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { courseId, courseName } = route.params;
            if (courseId) {
                this.setState({
                    courseId: courseId,
                })
            }
            if (courseName) {
                this.setState({
                    courseName: courseName,
                })
            }

            let selDepartmentId = null;
            if (constants.loginUser.portalDepartmentDTOList && constants.loginUser.portalDepartmentDTOList.length > 0) {
                selDepartmentId = constants.loginUser.portalDepartmentDTOList[0].departmentId;
            }
            this.setState({
                departmentDataSource: constants.loginUser.portalDepartmentDTOList,
                selDepartmentId: selDepartmentId,
            })

            this.loadStaffList(selDepartmentId, courseId);
        }
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/score/get_same_department_staff_score";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId": this.state.selDepartmentId,
            "courseId": this.state.courseId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/score/get_same_department_staff_score";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId": this.state.selDepartmentId,
            "courseId": this.state.courseId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var dataNew = response.data;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        if (this.state.refreshing) {
            WToast.show({data: 'loading...'});
            return;
        }
        this.setState({ refreshing: true }, () => {
            console.log('refreshing 已更新:', this.state.refreshing);
            // 在这里执行后续操作
            this.loadStaffList();
        });
    }

    loadStaffList = (selDepartmentId, courseId) => {
        let url = "/biz/score/get_same_department_staff_score";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "departmentId": selDepartmentId ? selDepartmentId : this.state.selDepartmentId,
            "courseId": courseId ? courseId : this.state.courseId,
        };
        httpPost(url, loadRequest, this.loadStaffListCallBack);
    }

    loadStaffListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data) {

            var dataNew = response.data;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow = (item, index) => {
        return (
            // <TouchableOpacity onPress={()=>{
            //     this.setState({
            //         selCourseId:item.courseId
            //     })
            //     this.props.navigation.navigate("ScoreMgrList", 
            //     {
            //         // 传递参数
            //         courseId:item.courseId,
            //         courseName:item.courseName,
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            <View key={item.jobUserId} style={CommonStyle.innerViewStyle}>
                <View style={[styles.titleViewStyle, { marginTop: 10 }]}>
                    <Text style={styles.titleTextStyle}>学员名称：</Text>
                    <Text style={styles.itemContentStyle}>{item.staffName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>成绩：</Text>
                    <Text style={styles.itemContentStyle}>{item.score ? item.score : (item.score == 0 ? 0 : '无')}</Text>
                </View>
                {item.score == null ?
                    <View></View>
                    :
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>录入时间：</Text>
                        {item.gmtModified == null ?
                            <Text style={styles.itemContentStyle}>{item.gmtCreated}</Text>
                            :
                            <Text style={styles.itemContentStyle}>{item.gmtModified}</Text>
                        }
                    </View>
                }
                {/* <View style={{width: 40, height: 40, 
                        backgroundColor: 'rgba(255,0,0,0.0)', 
                        position:'absolute', 
                        alignItems:'center',
                        justifyContent:'center',
                        right: 20,bottom:0
                        }}>
                        <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/in.png')}></Image>
                    </View> */}
            </View>
            // </TouchableOpacity>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("ScoreMgrAdd",
                    {
                        courseId: this.state.courseId,
                        courseName: this.state.courseName,
                        selDepartmentId: this.state.selDepartmentId,
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Text style={CommonStyle.headRightText}>录成绩</Text>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    renDepartmentRow = (item, index) => {
        return (
            <View key={item.departmentId} >
                <TouchableOpacity onPress={() => {
                    let selDepartmentId = item.departmentId;
                    this.setState({
                        "selDepartmentId": selDepartmentId
                    })

                    let loadUrl = "/biz/score/get_same_department_staff_score";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "departmentId": selDepartmentId ? selDepartmentId : null,
                        "courseId": this.state.courseId,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.departmentId} style={[item.departmentId === this.state.selDepartmentId ? CommonStyle.choseToSearchItemsSelectedViewColor
                        :
                        CommonStyle.choseToSearchItemsViewColor
                        ,
                    CommonStyle.choseToSearchItemsViewSize
                    ]}>
                        <Text style={[item.departmentId === this.state.selDepartmentId ?
                            CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                        ]}>
                            {item.departmentName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={'成绩管理'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle, { marginLeft: 0, borderWidth: 0, width: '100%', marginTop: 0, height: 70, justifyContent: "flex-start" }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={[styles.innerViewStyle, { marginLeft: 10, borderWidth: 0, justifyContent: "flex-start", flexDirection: 'row', marginTop: 10, index: 1000, width: screenWidth - 50 }]} >
                        {/* <Text style={[styles.titleTextStyle,{fontWeight:'bold', marginRight:50}]}>实习名称：{this.state.courseName}</Text> */}
                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>实习名称：</Text>
                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', marginRight: 50 }]}>{this.state.courseName}</Text>
                    </View>
                    <View style={{ marginLeft: 12, flexDirection: 'row', marginTop: 0 }}>
                        {
                            // 如果只属于一个部门，不显示可选部门
                            (this.state.departmentDataSource && this.state.departmentDataSource.length > 1)
                                ?
                                this.state.departmentDataSource.map((item, index) => {
                                    return this.renDepartmentRow(item)
                                })
                                : <View />
                        }
                    </View>
                </View>
                {/* <View style={{height:5,backgroundColor:'#FFFFFF'}}></View> */}
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                    // // 自定义下拉刷新
                    // refreshControl={
                    //     <RefreshControl
                    //     tintColor="#FF0000"
                    //     title="loading"
                    //     colors={['#FF0000', '#00FF00', '#0000FF']}
                    //     progressBackgroundColor="#FFFF00"
                    //     refreshing={this.state.refreshing}
                    //     onRefresh={()=>{
                    //         this._loadFreshData()
                    //     }}
                    //     />
                    // }
                    // 底部加载
                    // ListFooterComponent={()=>this.flatListFooterComponent()}
                    // onEndReached={()=>this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    }
});