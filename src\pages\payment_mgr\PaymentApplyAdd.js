import React,{Component} from 'react';
import {
    Alert,Modal,
    View, 
    ScrollView, 
    Text, 
    TextInput, 
    StyleSheet, 
    FlatList ,
    TouchableOpacity,
    Dimensions,
    Image,
    KeyboardAvoidingView
} from 'react-native';

import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

const leftLabWidth = 130;
export default class PaymentApplyAdd extends Component {
    constructor(props) {
        super(props);
        this.state ={
            operate:"",
            applyAuditId:"",        //付款申请审核标识
            paymentApplyName:"",    //付款申请审核名称
            applyUserId:"",         //付款申请人
            customerId:"",          //支付对象标识
            selCustomerId:"",
            customerName:"",        //支付对象名称
            selCustomerName:"",
            customerDataSource:[],  //支付对象信息列表                
            searchKeyWord:"",       //查询关键字
            bankOfDeposit:"",       //开户行
            selBankOfDeposit:"", 
            bankAccount:"",         //开户账号
            selBankAccount:"",
            bankAccountHolder:"",   //开户名
            selBankAccountHolder:"",
            auditPatchAttach:"",    //审核补充附件（支付凭证截图）

            tenantId:"",

            paymentClassId:"",      //支付类别标识
            paymentClassName:"",
            paymentClassDataSource:[],

            paymentMode:"",         //支付方式
            paymentModeDataSource:[],

            paymentAmount:"",       //付款金额
            paymentDate:"",         //支付日期
            selectedPaymentDate: [],
            paymentReason:"",       //支付事由
            modal:false,            //弹出框显示状态

            reviewerName:"",
            reviewerId:"",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadreviewer();
        console.log("========applyUserId==:", constants.loginUser.userId);
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { applyAuditId } = route.params;
            if (applyAuditId) {
                console.log("========Edit==applyAuditId:", applyAuditId);
                this.setState({
                    applyAuditId:applyAuditId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/payment/apply/audit/get";
                loadRequest={'applyAuditId':applyAuditId};
                httpPost(loadTypeUrl, loadRequest, this._loadPaymentApplyAuditCallBack);
            }
            else
            {
                this.setState({
                    operate:"新增",
                    paymentMode:"E"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectedPaymentDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    paymentDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
        let paymentModeDataSource = [
           
            {
                stateCode:'E',
                stateName:'电子承兑',
            },
            {
                stateCode:'C',
                stateName:'现金支付',
            },
            {
                stateCode:'B',
                stateName:'银行转账',
            },
        ]
        this.setState({
            paymentModeDataSource:paymentModeDataSource,
            applyUserId:constants.loginUser.userId
        })
        this.loadpaymentClassList();
        this.loadCustomerList()
    }

    loadreviewer = ()=>{
        let loadTypeUrl= "/biz/payment/apply/audit/reviewer";
        let loadRequest={
            "applyUserId": constants.loginUser.userId
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    reviewerName:response.data.userName,
                    reviewerId:response.data.userId,
                })
            }
        });
    }

    savePaymentApply=()=>{
        console.log("=======save====");
        let toastOpts;
        if (!this.state.paymentApplyName) {
            toastOpts = getFailToastOpts("请输入付款名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.customerId) {
            toastOpts = getFailToastOpts("请选择支付对象");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.paymentClassId) {
            toastOpts = getFailToastOpts("请选择支付类别");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.paymentDate) {
            toastOpts = getFailToastOpts("请选择支付日期");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.paymentAmount) {
            toastOpts = getFailToastOpts("请输入付款金额");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.paymentMode) {
            toastOpts = getFailToastOpts("请选择支付方式");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/payment/apply/audit/add";
        if (this.state.applyAuditId) {
            console.log("=========Edit===applyAuditId", this.state.applyAuditId)
            url= "/biz/payment/apply/audit/modify";
        }
        let requestParams={
            "applyAuditId":this.state.applyAuditId,
            "paymentApplyName":this.state.paymentApplyName,
            "customerId":this.state.customerId,
            "paymentClassId":this.state.paymentClassId,
            "paymentDate":this.state.paymentDate,
            "paymentAmount":this.state.paymentAmount,
            "paymentReason":this.state.paymentReason,
            "paymentMode":this.state.paymentMode,
            "applyUserId":constants.loginUser.userId,
            "auditUserId":this.state.reviewerId,
            "auditState":"1",
            // "auditPatchAttach":"",    //审核补充附件（支付凭证截图）
        };
        httpPost(url, requestParams, this._savePaymentApplyCallBack);
    }

    _loadPaymentApplyAuditCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            var selectedPaymentDate = response.data.paymentDate.split("-");
            this.setState({
                applyAuditId:response.data.applyAuditId,
                paymentApplyName:response.data.paymentApplyName,
                customerId:response.data.customerId,
                selCustomerId:response.data.customerId,
                customerName:response.data.customerName,
                selCustomerName:response.data.customerName,
                paymentClassId:response.data.paymentClassId,
                paymentDate:response.data.paymentDate,
                selectedPaymentDate:selectedPaymentDate,
                paymentAmount:response.data.paymentAmount,
                paymentReason:response.data.paymentReason,
                paymentMode:response.data.paymentMode,
                bankOfDeposit:response.data.bankOfDeposit,
                selBankOfDeposit:response.data.bankOfDeposit,
                bankAccount:response.data.bankAccount,
                selBankAccount:response.data.bankAccount,
                bankAccountHolder:response.data.bankAccountHolder,
                selBankAccountHolder:response.data.bankAccountHolder
            })
        }
    }

    _savePaymentApplyCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    loadpaymentClassList=()=>{
        let url= "/biz/payment/class/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,

        };
        httpPost(url, loadRequest, this._loadpaymentClassListCallBack);
    }
    _loadpaymentClassListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log("======load==paymentclass==", response.data);
            this.setState({
                paymentClassDataSource:response.data.dataList,
                refreshing:false
            })
            if(this.state.operate==="新增")
            this.setState({
                paymentClassId:response.data.dataList[0].paymentClassId
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    loadCustomerList=()=>{
        let url= "/biz/tenant/customer/list";
        let data={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "customerType":"P",
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, data, this.callBackLoadCustomerList);
    }
    callBackLoadCustomerList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log("======load==customerList==", response.data.dataList);
            this.setState({
                customerDataSource:response.data.dataList,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    paymentClassRow = (item) => {
        return (
            <TouchableOpacity 
                key={item.paymentClassId}
                onPress={() => {
                console.log("======paymentClassChoose==", item.paymentClassId);
                this.setState({
                    paymentClassId: item.paymentClassId
                })
            }}>
                <View key={item.paymentClassId} style={[item.paymentClassId === this.state.paymentClassId ? 
                    // CommonStyle.selectedBlockItemViewStyle 
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    : 
                    // CommonStyle.blockItemViewStyle
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginLeft:16,
                        // marginRight: 8,
                        marginTop: 8,
                        marginBottom: 6,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                    ]}>
                    <Text style={[item.paymentClassId === this.state.paymentClassId ? 
                        // CommonStyle.selectedBlockItemTextStyle16 
                        {
                            color: '#1E6EFA'
                        }
                        : 
                        // CommonStyle.blockItemTextStyle16
                        {
                            color: '#404956'
                        }
                        ,
                        {
                            fontSize: 16, textAlign : 'center'
                        }
                        ]}>
                        {item.paymentClassName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    customerRow = (item) => {
        return (
            <TouchableOpacity 
                key={item.customerId}
                onPress={() => {
                console.log("======customerChoose==", item.customerId,",",item.customerName);
                this.setState({
                    selCustomerId: item.customerId,
                    selCustomerName: item.customerName,
                    selBankOfDeposit: item.bankOfDeposit,       //开户行
                    selBankAccount: item.bankAccount,         //开户账号
                    selBankAccountHolder: item.bankAccountHolder,   //开户名
                })
                
            }}>
                <View key={item.customerId} style={item.customerId === this.state.selCustomerId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.customerId === this.state.selCustomerId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.customerName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    paymentModeRow = (item) => {
        return (
            <TouchableOpacity 
                key={item.stateCode}
                onPress={() =>{
                let stateCode=item.stateCode
                this.setState({
                    paymentMode:item.stateCode
                })
                console.log("======paymentModeChoose==", this.state.paymentMode,",",item.stateName,",",item.stateCode)
            }}>
                <View key={item.stateCode} style={[item.stateCode === this.state.paymentMode ? 
                    // CommonStyle.selectedBlockItemViewStyle 
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    : 
                    // CommonStyle.blockItemViewStyle
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginLeft:16,
                        // marginRight: 8,
                        marginTop: 8,
                        marginBottom: 6,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                    ]}>
                    <Text style={[item.stateCode === this.state.paymentMode ? 
                        // CommonStyle.selectedBlockItemTextStyle16 
                        {
                            color: '#1E6EFA'
                        }
                        : 
                        // CommonStyle.blockItemTextStyle16
                        {
                            color: '#404956'
                        }
                        ,
                        {
                            fontSize: 16, textAlign : 'center'
                        }
                        ]}>
                        {item.stateName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} >
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("PaymentApplyList") 
            // }}>
            //     <Text style={CommonStyle.headRightText}>付款申请</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>付款申请</Text>
                </TouchableOpacity>
            </View>
        )
    }
    openselectPaymentDate() {
        this.refs.SelectPaymentDate.showDate(this.state.selectedPaymentDate)
    }
    callBackselectPaymentDateValue(value) {
        console.log("==========支付日期选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedPaymentDate: value
        })
        if (this.state.selectedPaymentDate && this.state.selectedPaymentDate.length) {
            var paymentDate = "";//此处var存疑
            var vartime;
            for (var index = 0; index < this.state.selectedPaymentDate.length; index++) {
                vartime = this.state.selectedPaymentDate[index];
                if (index === 0) {
                    paymentDate += vartime;
                }
                else {
                    paymentDate += "-" + vartime;
                }
            }
            this.setState({
                paymentDate: paymentDate
            })
        }
    }
    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]}  behavior="padding">
            <CommonHeadScreen title={this.state.operate + '申请'}
                leftItem={() => this.renderLeftItem()}
                rightItem={() => this.renderRightItem()}
            />
            <View style={CommonStyle.lineHeadBorderStyle} />

            <ScrollView style={CommonStyle.formContentViewStyle}>    
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>
                            付款名称
                        </Text>
                    </View>
                    <TextInput 
                        style={styles.inputRightText}
                        placeholder={'请输入'}
                        onChangeText={(text) => this.setState({paymentApplyName:text})}
                    >
                        {this.state.paymentApplyName}
                    </TextInput>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>支付对象</Text>
                    </View>
                    <TouchableOpacity 
                        onPress={() => { 
                            this.setState({
                                modal:true,
                            })
                        if (!this.state.selCustomerId && this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                            this.setState({
                                selCustomerId: this.state.customerDataSource[0].customerId,
                                selCustomerName: this.state.customerDataSource[0].customerName,
                                selBankOfDeposit: this.state.customerDataSource[0].bankOfDeposit,
                                selBankAccountHolder: this.state.customerDataSource[0].bankAccountHolder,
                                selBankAccount: this.state.customerDataSource[0].bankAccount, 
                            })
                        }
                    }}
                    >
                        <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5),borderWidth:0 }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {this.state.customerName ? this.state.customerName : "点击选择"}
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>{'\t'}</Text>

                        <Text style={styles.leftLabNameTextStyle}>开户名</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5) ,borderWidth:0}]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                            {this.state.bankAccountHolder ? this.state.bankAccountHolder : "请先选择支付对象"}
                        </Text>
                    </View>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>{'\t'}</Text>

                        <Text style={styles.leftLabNameTextStyle}>开户名银行</Text>
                    </View>
                    <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5) ,borderWidth:0}]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                            {this.state.bankOfDeposit ? this.state.bankOfDeposit : "请先选择支付对象"}
                        </Text>
                    </View>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>{'\t'}</Text>

                        <Text style={styles.leftLabNameTextStyle}>银行账号</Text>
                    </View>
                    <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5) ,borderWidth:0}]}>
                        <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                            {this.state.bankAccount ? this.state.bankAccount : "请先选择支付对象"}
                        </Text>
                    </View>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                <View>
                        {
                            (this.state.paymentClassDataSource && this.state.paymentClassDataSource.length > 0)
                                ?
                                <View>
                                    <View style={styles.inputRowStyle}>
                                        <View style={styles.leftLabView}>
                                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                                            <Text style={styles.leftLabNameTextStyle}>支付类别</Text>
                                        </View>
                                    </View>
                                    <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row' }}>
                                        {
                                            (this.state.paymentClassDataSource && this.state.paymentClassDataSource.length > 0)
                                                ?
                                                this.state.paymentClassDataSource.map((item, index) => {
                                                    return this.paymentClassRow(item)
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                                </View>
                                : <View />
                        }
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>支付日期</Text>
                    </View>
                    <TouchableOpacity onPress={() => this.openselectPaymentDate()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {this.state.paymentDate ? this.state.paymentDate : "点击选择支付日期"}
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>付款金额</Text>
                    </View>
                    <TextInput 
                        keyboardType='numeric'
                        style={styles.inputRightText}
                        placeholder={'请输入'}
                        onChangeText={(text) => this.setState({paymentAmount:text})}
                    >
                        {this.state.paymentAmount}
                    </TextInput>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>{'\t'}</Text>

                        <Text style={styles.leftLabNameTextStyle}>付款事由</Text>
                    </View>
                    {/* <TextInput 
                        style={styles.inputRightText}
                        placeholder={'请输入付款事由'}
                        onChangeText={(text) => this.setState({paymentReason:text})}
                    >
                        {this.state.paymentReason}
                    </TextInput> */}
                </View>
                <View style={[styles.inputRowStyle,{height:100}]}>
                    <TextInput 
                        multiline={true}
                        textAlignVertical="top"
                        style={[CommonStyle.inputRowText,{height:100,marginLeft:16,borderWidth:0}]}
                        placeholder={'请输入'}
                        onChangeText={(text) => this.setState({paymentReason:text})}
                    >
                        {this.state.paymentReason}
                    </TextInput>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                <View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>

                            <Text style={styles.leftLabNameTextStyle}>支付方式</Text>
                        </View>
                    </View>
                    <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                        (this.state.paymentModeDataSource && this.state.paymentModeDataSource.length > 0)
                        ?
                        this.state.paymentModeDataSource.map((item, index) => {
                            return this.paymentModeRow(item)
                        })
                        : <EmptyRowViewComponent />
                        }
                    </View>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                {/* <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>审核补充附件</Text>
                    </View>
                </View> */}
                <View>

                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>{'\t'}</Text>

                            <Text style={styles.leftLabNameTextStyle}>审核人</Text>
                        </View>
                        <TextInput 
                            editable={false}
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({reviewerName:text})}
                        >
                            {this.state.reviewerName}
                        </TextInput>
                    </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                            
                           
                </View>
                <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={CommonStyle.btnAddCancelBtnView} >
                        {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.savePaymentApply.bind(this)}>
                        <View style={CommonStyle.btnAddSaveBtnView}>
                        {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </ScrollView>        
            <Modal
                animationType={'slide'}
                transparent={true}
                onRequestClose={() => console.log('onRequestClose...')}
                visible={this.state.modal}
            >
                <View style={CommonStyle.fullScreenKeepOut}>
                    <View style={[CommonStyle.modalContentViewStyle]}>
                        <View style={CommonStyle.rowLabView}>
                            <TextInput
                                style={[CommonStyle.modalSearchInputText]}
                                placeholder={'请输入关键字'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                            <TouchableOpacity onPress={() => {
                                        this.loadCustomerList();
                            }}>
                                <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                    <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <ScrollView>
                        <View>
                            {
                            (this.state.customerDataSource && this.state.customerDataSource.length > 0)
                                ?
                                    <View style={{ width: screenWidth-50, flexWrap: 'wrap', flexDirection: 'row' }}>
                                        {
                                            (this.state.customerDataSource && this.state.customerDataSource.length > 0)
                                                ?
                                                this.state.customerDataSource.map((item, index) => {
                                                    return this.customerRow(item)
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                : <View />
                            }
                            </View>
                        </ScrollView> 
                        <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                            <TouchableOpacity onPress={() => { 
                                this.setState({
                                    modal:false,
                                }) 
                            }}>
                                <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                    <Image  style={{width:22, height:22,marginRight:10}} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                    <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => { 
                                this.setState({
                                    modal:false,
                                    customerId:this.state.selCustomerId,
                                    customerName: this.state.selCustomerName,
                                    bankOfDeposit: this.state.selBankOfDeposit,       //开户行
                                    bankAccount: this.state.selBankAccount,         //开户账号
                                    bankAccountHolder: this.state.selBankAccountHolder  //开户名
                                })
                                console.log("==========支付对象选择结果：",this.state.customerId) //此处无法在node上打印customerId
                            }}>
                                <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                    <Image  style={{width:25, height:25,marginRight:10}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                    <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确认</Text>
                                </View>
                            </TouchableOpacity>    
                        </View>
                    </View>
                </View>
            </Modal>
            <BottomScrollSelect
                ref={'SelectPaymentDate'}
                callBackDateValue={this.callBackselectPaymentDateValue.bind(this)}
            />
        </KeyboardAvoidingView>

        )
    }
}

const styles = StyleSheet.create({
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
         alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#FFFFFF',
        // borderWidth:1,
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    textCertain: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#FFFFFF',
        lineHeight: 24,
        marginTop:10,
        textAlign: 'center',
        // fontStyle: 'normal',
    },
    textCancel: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#404956',
        lineHeight: 24,
        marginTop:10,
        textAlign:'center'
        // fontStyle: 'normal',
    },
    textContainerCertain: {
        width: 180,
        height: 48,
        marginRight:8,
        backgroundColor: '#255BDA',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    textContainerCancel: {
        width: 180,
        height: 48,
        marginLeft:8,
        backgroundColor: '#FFFFFF',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
})