import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import CrHeadScreen from '../../component/CrHeadScreen'
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class EnterprisecrHiringPositionList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            positionTypeChooseDataSource: [],
            selPositionType: "all"

        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let positionTypeChooseDataSource = [
            {
                positionType: 'all',
                positionTypeName: '全部',
            },
            {
                positionType: "F",
                positionTypeName: "全职",
            },
            {
                positionType: "P",
                positionTypeName: "兼职",
            },
            {
                positionType: "I",
                positionTypeName: "实习"
            }

        ]
        this.setState({
            positionTypeChooseDataSource: positionTypeChooseDataSource,
        })
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        if (constants && constants.loginUser) {
            this.setState({
                enterpriseId: constants.loginUser.enterpriseId
            })
        }
        this.loadHiringPositionList();
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/hiring/position/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "enterpriseId": constants.loginUser.enterpriseId,
            "positionType": this.state.selPositionType === "all" ? null : this.state.selPositionType,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/hiring/position/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "enterpriseId": constants.loginUser.enterpriseId,
            "positionType": this.state.selPositionType === "all" ? null : this.state.selPositionType,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadHiringPositionList();
    }

    loadHiringPositionList = () => {
        let url = "/biz/hiring/position/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "enterpriseId": constants.loginUser.enterpriseId,
            "positionType": this.state.selPositionType === "all" ? null : this.state.selPositionType
        };
        httpPost(url, loadRequest, this.loadHiringPositionListCallBack);
    }

    loadHiringPositionListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteHiringPosition = (positionId) => {
        console.log("=======delete=positionId", positionId);
        let url = "/biz/hiring/position/delete";
        let requestParams = { 'positionId': positionId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    positionTypeChooseStateRow = (item, index) => {
        return (
            <View key={item.positionType} >
                <TouchableOpacity onPress={() => {
                    var selPositionType = item.positionType;
                    this.setState({
                        selPositionType: selPositionType
                    })

                    let url = "/biz/hiring/position/list";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "enterpriseId": constants.loginUser.enterpriseId,
                        "positionType": selPositionType === "all" ? null : selPositionType
                    };
                    // console.log("selYearsChooseName+1:"+ this.addOneYear(selYearsChooseName))
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.positionType} style={[item.positionType === this.state.selPositionType ? 
                        [styles.selectedBlockItemViewStyle]
                        :
                        [styles.blockItemViewStyle] ]}>
                        <Text style={[item.positionType === this.state.selPositionType ? 
                            [{ color: "rgba(0, 10, 2, 0.8)", fontSize: 16, textAlign: 'center' }]
                            :
                            [{ color: "rgba(0, 10, 2, 0.45)", fontSize: 14, textAlign: 'center' }],
                            { fontWeight: 'bold' }
                        ]}>
                            {item.positionTypeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }
    modifyPositionState = (positionItem) => {
        console.log("========modify=positionItem", positionItem);
        let loadUrl = "/biz/hiring/position/modify";
        let loadRequest = {
            "positionId": positionItem.positionId,
            "enterpriseId": constants.loginUser.enterpriseId,
            "positionState": positionItem.positionState === '0AA' ? '0AD' : '0AA',
        };
        httpPost(loadUrl, loadRequest, this.modifyPositionStateBack);
    }

    modifyPositionStateBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: response.data.positionState == '0AA' ? "已重启岗位" : "该岗位已关闭" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }

    }

    renderRow = (item, index) => {
        return (
            <View key={item.dailyId} style={{ borderColor: '#F2F5FC', borderBottomWidth: 7, borderTopWidth: 8 }}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>岗位名称：{item.positionName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>岗位类型：{item.positionTypeName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>工作地点：{item.workingPlace}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>岗位描述</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.positionDescribe ? item.positionDescribe : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>任职要求</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.positionRequirements ? item.positionRequirements : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle, { fontWeight: 'bold' }]}>薪资待遇</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.positionTreatment ? item.positionTreatment : "无"}</Text>
                </View>
                {
                item.positionState ==='0AA' ? 
                    null
                :
                    <View style={styles.bodyViewStyle}>
                        <Text style={{fontSize:14,color:'#FD4246'}}>岗位已关闭</Text>
                    </View>
                }
                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                    <TouchableOpacity onPress={() => { this.modifyPositionState(item) }}>
                        {
                            item.positionState === '0AA'?
                            <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle,{borderColor:'rgba(145, 147, 152, 0.5)',flexDirection:"row"}]}>
                                <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/closeGrey.png')}></Image>
                                <Text style={[{ color: 'rgba(145, 147, 152, 0.5)', fontSize: 14, lineHeight: 20 }]}>关闭</Text>
                            </View>
                            :
                            <View style={[{
                                width: 65,
                                height: 28,
                                flexDirection: "row",
                                justifyContent: 'center',
                                alignItems: 'center',
                                margin: 10,
                                marginRight: 0,
                                borderColor: 'rgba(27, 188, 130, 1)',
                                borderWidth: 0.85,
                                borderRadius: 6
                            }]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                <Text style={[{ color: 'rgba(27, 188, 130, 1)', fontSize: 14, lineHeight: 20 }]}>重启</Text>
                            </View>
                        }
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        Alert.alert('确认', '您确定要删除该岗位吗？', [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                    // this在这里可用，传到方法里还有问题
                                    // this.props.navigation.goBack();
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteHiringPosition(item.positionId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle]}>
                            <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        this.props.navigation.navigate("EnterprisecrHiringPositionAdd",
                            {
                                // 传递参数
                                positionId: item.positionId,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle,{marginRight:10}]}>
                            <Image style={{ width: 17, height: 17, marginRight: 7 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20}}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:24, height:24}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("EnterprisecrHiringPositionAdd",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='招聘岗位'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle, { marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }]} 
                    onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.positionTypeChooseDataSource && this.state.positionTypeChooseDataSource.length > 0)
                                ?
                                this.state.positionTypeChooseDataSource.map((item, index) => {
                                    return this.positionTypeChooseStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle: {
        // marginTop: 10,
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    bodyViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 8,
        marginTop: 8
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:screenWidth - 120,
    },
    itemContentRightChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:120,
    },
    // 分段器样式
    blockItemViewStyle: {
        margin: 5,
        width: 45, 
        borderRadius: 0,
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
        // marginTop: 0, 
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        width: 45, borderRadius: 0, 
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: "#FFFFFF", 
        // marginTop: 0, 
    },    
    enterpriseLogoStyle:{
        borderRadius:10,
        width:65,
        height:65,
        marginTop:15
    }

});