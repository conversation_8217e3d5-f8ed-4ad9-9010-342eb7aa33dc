import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class InventoryDetailBrickClassifyList extends Component {
  constructor(props) {
    super(props);
    const {route} = props;
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 10,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      selBrickClassId: '',
      topBlockLayoutHeight: 0,
      searchKeyWord: '',
      isOutsourcingInventoryDetail:
        route.params.code === 'OUTSOURCING_INVENTORY_DETAIL_BRICK_CLASSIFY_LIST'
          ? 'Y'
          : 'N',
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    this.loadBrickClassIfyList();
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/brick/class/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/brick/class/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadBrickClassIfyList();
  };

  loadBrickClassIfyList = () => {
    let url = '/biz/brick/class/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this.loadKilnCarListCallBack);
  };

  loadKilnCarListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  updateKilnCarState = (seriesId, kilnCarState) => {
    console.log('=======delete=seriesId', seriesId);
    let url = '/biz/brick/class/series/modify';
    let requestParams = {seriesId: seriesId, kilnCarState: kilnCarState};
    httpPost(url, requestParams, this.updateKilnCarStateCallBack);
  };

  // 更新状态的回调操作
  updateKilnCarStateCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '操作成功'});
      this._loadFreshData();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  deleteBrickClassify = (seriesId) => {
    console.log('=======delete=seriesId', seriesId);
    let url = '/biz/brick/class/series/delete';
    let requestParams = {seriesId: seriesId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selBrickClassId: item.brickClassId,
          });
          this.props.navigation.navigate(
            'InventoryDetailBrickClassifySeriesList',
            {
              // 传递参数
              brickClassId: item.brickClassId,
              brickClassName: item.brickClassName,
              isOutsourcingInventoryDetail:
                this.state.isOutsourcingInventoryDetail,
              // 传递回调函数
              refresh: this.callBackFunction,
            },
          );
        }}>
        {index == 0 ? (
          <View style={CommonStyle.lineListHeadRenderRowStyle}></View>
        ) : (
          <View></View>
        )}
        <View
          key={item.brickClassId}
          style={[
            styles.innerViewStyle,
            this.state.selBrickClassId == item.brickClassId
              ? {
                  backgroundColor: 'rgba(255,0,0,0.4)',
                  borderRadius: 20,
                  hight: 80,
                }
              : {},
          ]}>
          <View style={styles.titleViewStyle}>
            <Text style={styles.titleTextStyle}>编号：{index + 1}</Text>
          </View>
          <View style={styles.titleViewStyle}>
            <Text style={styles.titleTextStyle}>
              分类名称：{item.brickClassName}
            </Text>
          </View>
          <View
            style={{
              width: 40,
              height: 40,
              marginBottom: 10,
              backgroundColor: 'rgba(255,0,0,0.0)',
              position: 'absolute',
              alignItems: 'center',
              justifyContent: 'center',
              right: 20,
              bottom: 0,
            }}>
            <Image
              style={{width: 22, height: 22}}
              source={require('../../assets/icon/iconfont/enter4.png')}></Image>
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}style={[{marginBottom:1.5}]}>
      //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
      // </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[{flexDirection: 'row', alignItems: 'center'}]}>
          {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
          {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={{color: '#3C6CDE', fontWeight: 'bold'}}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity onPress={() => {}}>
          {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
          <Text style={{color: '#FFFFFF'}}>库存明细</Text>
          {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
        </TouchableOpacity>
      </View>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  searchByKeyWord = () => {
    let url = '/biz/brick/class/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      // "customerType":"P",
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title={'产品分类'}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.headViewStyle,
            {borderLeftWidth: 0, borderRightWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View style={CommonStyle.singleSearchBox}>
            <View style={CommonStyle.searchBoxWithoutOthers}>
              {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
              <Image
                style={{width: 16, height: 16, marginLeft: 7}}
                source={require('../../assets/icon/iconfont/search.png')}></Image>
              <TextInput
                style={{
                  color: 'rgba(rgba(0, 10, 32, 0.45))',
                  fontSize: 14,
                  marginLeft: 5,
                  paddingTop: 0,
                  paddingBottom: 0,
                  paddingRight: 0,
                  paddingLeft: 0,
                  width: '100%',
                }}
                returnKeyType="search"
                returnKeyLabel="搜索"
                onSubmitEditing={(e) => {
                  this.searchByKeyWord();
                }}
                placeholder={'分类名称'}
                onChangeText={(text) => this.setState({searchKeyWord: text})}>
                {this.state.searchKeyWord}
              </TextInput>
            </View>
          </View>
        </View>
        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> */}
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            ListEmptyComponent={this.emptyComponent}
            keyExtractor={(item) => item.brickClassId}
            ItemSeparatorComponent={this.space}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
            onEndReachedThreshold={0.5}
          />
          {/* </ScrollView> */}
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
    marginTop: 5,
  },
  innerViewStyle: {
    // marginLeft:40,
    // marginTop:10,
    // borderColor:"#F4F4F4",
    // borderWidth:8,
  },
  innerViewStyleSearch: {
    // marginTop:10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 40,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  searchInputText: {
    width: screenWidth - 100,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },
  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
});
