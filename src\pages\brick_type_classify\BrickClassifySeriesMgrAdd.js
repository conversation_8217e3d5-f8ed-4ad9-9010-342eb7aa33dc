import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class BrickClassifySeriesMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            brickClassId:null,
            seriesId:"",
            seriesName:"",
            seriesSort:0,
            operate:"",
            pcBigScreenHomeStatisticalFlag:"N",
            pcBigScreenProductStatisticalFlag:"N",
            longTermProduction:"N"
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { seriesId, brickClassId } = route.params;
            if (brickClassId) {
                this.setState({
                    brickClassId:brickClassId,
                })
            }
            if (seriesId) {
                console.log("========Edit==seriesId:", seriesId);
                this.setState({
                    seriesId:seriesId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/brick/class/series/get";
                loadRequest={'seriesId':seriesId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditMachineDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    loadEditMachineDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                seriesId:response.data.seriesId,
                seriesName:response.data.seriesName,
                seriesSort:response.data.seriesSort,
                pcBigScreenHomeStatisticalFlag:response.data.pcBigScreenHomeStatisticalFlag,
                pcBigScreenProductStatisticalFlag:response.data.pcBigScreenProductStatisticalFlag,
                longTermProduction:response.data.longTermProduction
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("BrickClassifyMgrList")
            }}>
                <Text style={CommonStyle.headRightText}>产品管理</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveBrickClassify =()=> {
        console.log("=======saveBrickClassify");
        let toastOpts;
        if (!this.state.seriesName) {
            toastOpts = getFailToastOpts("请输入砖型分类名称");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/brick/class/series/add";
        if (this.state.seriesId) {
            console.log("=========Edit===seriesId", this.state.seriesId)
            url= "/biz/brick/class/series/modify";
        }
        let requestParams={
            "brickClassId":this.state.brickClassId,
            "seriesId":this.state.seriesId,
            "seriesName":this.state.seriesName,
            "seriesSort":this.state.seriesSort,
            "pcBigScreenHomeStatisticalFlag":this.state.pcBigScreenHomeStatisticalFlag,
            "pcBigScreenProductStatisticalFlag":this.state.pcBigScreenProductStatisticalFlag,
            "longTermProduction":this.state.longTermProduction
        };
        httpPost(url, requestParams, this.saveBrickClassifyCallBack);
    }
    
    // 保存回调函数
    saveBrickClassifyCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '产品名称'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>产品名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入产品名称'}
                            onChangeText={(text) => this.setState({seriesName:text})}
                        >
                            {this.state.seriesName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入产品排序'}
                            onChangeText={(text) => this.setState({seriesSort:text})}
                        >
                            {this.state.seriesSort}
                        </TextInput>
                    </View>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>大屏首页统计</Text>
                        </View>
                        <View style={[CommonStyle.selectViewItem, (this.state.pcBigScreenHomeStatisticalFlag === 'Y') ? { backgroundColor: 'red' } : ""]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    pcBigScreenHomeStatisticalFlag: "Y",
                                })
                            }}>
                                <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.pcBigScreenHomeStatisticalFlag === 'Y') ? { color: '#FFF' } : { color: '#000' }]}>是</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={[CommonStyle.selectViewItem, (this.state.pcBigScreenHomeStatisticalFlag === 'N') ? { backgroundColor: 'red' } : {}]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    pcBigScreenHomeStatisticalFlag: "N",
                                })
                            }}>
                                <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.pcBigScreenHomeStatisticalFlag === 'N') ? { color: '#FFF' } : { color: '#000' }]}>否</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>大屏生产统计</Text>
                        </View>
                        <View style={[CommonStyle.selectViewItem, (this.state.pcBigScreenProductStatisticalFlag === 'Y') ? { backgroundColor: 'red' } : ""]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    pcBigScreenProductStatisticalFlag: "Y",
                                })
                            }}>
                                <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.pcBigScreenProductStatisticalFlag === 'Y') ? { color: '#FFF' } : { color: '#000' }]}>是</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={[CommonStyle.selectViewItem, (this.state.pcBigScreenProductStatisticalFlag === 'N') ? { backgroundColor: 'red' } : {}]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    pcBigScreenProductStatisticalFlag: "N",
                                })
                            }}>
                                <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.pcBigScreenProductStatisticalFlag === 'N') ? { color: '#FFF' } : { color: '#000' }]}>否</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>是否长期生产</Text>
                        </View>
                        <View style={[CommonStyle.selectViewItem, (this.state.longTermProduction === 'Y') ? { backgroundColor: 'red' } : ""]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    longTermProduction: "Y",
                                })
                            }}>
                                <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.longTermProduction === 'Y') ? { color: '#FFF' } : { color: '#000' }]}>是</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={[CommonStyle.selectViewItem, (this.state.longTermProduction === 'N') ? { backgroundColor: 'red' } : {}]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    longTermProduction: "N",
                                })
                            }}>
                                <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.longTermProduction === 'N') ? { color: '#FFF' } : { color: '#000' }]}>否</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveBrickClassify.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={[CommonStyle.rowLabView, CommonStyle.bodyTextStyle]}>
                        <View>
                            <Text style={[CommonStyle.rowLabTextStyle, CommonStyle.boldTextStyle]}>
                                提示：
                            </Text>
                        </View>
                        <View>
                            <Text style={[CommonStyle.rowLabTextStyle, CommonStyle.boldTextStyle]}>
                                当前分类下最多设置5个产品参于生产管理-工厂产品
                            </Text>
                        </View>
                        <View>
                            <Text style={[CommonStyle.rowLabTextStyle, CommonStyle.boldTextStyle]}>
                                最多设置5个产品参于首页-核心产品
                            </Text>
                        </View>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})