import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Modal,
    FlatList,RefreshControl,Image, Clipboard, Linking,ScrollView,CameraRoll
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
const { ifIphoneXContentViewHeight,ifIphoneXBodyViewHeight, isIphoneX, ifIphoneXHeaderHeight } = require('../../utils/ScreenUtil');
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
import <PERSON><PERSON>iewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../utils/CameraRollUtils';


var screenHeight = Dimensions.get('window').height;
export default class ExamApply extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            examPersonPhoto:"",
            isShowImage: false,
            pictureIndex:0,
            totalPage:1,
            totalRecord:1,
            display:"N",
            compressFileList:[],
            urls:[],
            pictureIndex:0,
            
            //显示图片
            modal:false,
            examTicket:"",
            showExamTicket:false,
            examPersonName:"",
            paramValue:"",
            roleId:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            console.log(route.params);
            const { tenantId} = route.params;
            console.log('tenant',tenantId);
            if(tenantId){
                this.setState({
                    tenantId: tenantId
                })
            }
        }
        this.loadChargeDescription();
        this.loadExamApplyList();
        // this.loadRoleList();
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }
    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    loadExamApplyList=()=>{
        let url= "/biz/exam/apply/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "excludeParamCode":"ADMISSION_TICKET_NOTICE",
            "examPersonName":constants.loginUser.userName,
            //"chargeDescription":"CHARGE_DESCRIPTION"
        };
        httpPost(url, loadRequest, this.callBackLoadExamApplyList);
    }
    
    callBackLoadExamApplyList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N",vx2DCode:false ,pictureDisplay: "N",personPictureDisplay:false}))
            })
            this.setState({
                dataSource: listNew,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/exam/apply/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "excludeParamCode":"ADMISSION_TICKET_NOTICE",
            "examPersonName":constants.loginUser.userName,
            //"chargeDescription":"CHARGE_DESCRIPTION"
            
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/exam/apply/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "excludeParamCode":"ADMISSION_TICKET_NOTICE",
            "examPersonName":constants.loginUser.userName,
            //"chargeDescription":"CHARGE_DESCRIPTION"
            
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N",vx2DCode:false ,pictureDisplay: "N",personPictureDisplay:false}))
            })
            this.setState({
                dataSource: listNew,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadExamApplyList();
    }

    deleteExam =(applyId)=> {
        console.log("=======delete=applyId", applyId);
        let url= "/biz/exam/apply/delete";
        let requestParams={'applyId':applyId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }
    
    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"成功删除"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    exportPdfFile=(applyId)=> {
        console.log("=======exportPdfFile");
        let url= "/biz/generate/pdf/exam_ticket";
        let requestParams={
            "currentPage": 1,
            "pageSize": 15,
            "applyId":applyId,
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                // Clipboard.setString(response.data); 
                // WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});

                // let ticket = response.data
                // let Ticket = value.setString(response.data)
                // console.log("zhunkai1",Ticket);
                this.setState({
                    examTicket:response.data,
                    modal:true,
                })

                // Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                //     {
                //         text:"不打开", onPress:()=>{
                //         WToast.show({data:'点击了不打开'});
                //         }
                //     },
                //     {
                //         text:"打开", onPress:()=>{
                //             WToast.show({data:'点击了打开'});
                //             // 直接打开外网链接 
                //             Linking.openURL(response.data)
                //         }
                //     }
                // ]);
            }
        });
    }

    OpenExamTicket=() => {
        Linking.openURL(this.state.examTicket);
    }
    
    loadChargeDescription=()=>{
        let loadUrl = "/biz/tenant/config/get";
        let loadRequest = {
            "tenantId":this.state.tenantId,
            "paramCode":"CHARGE_DESCRIPTION"
        };
        httpPost(loadUrl, loadRequest, (response)=>{
            
            if (response.code == 200 && response.data) {
                this.setState({ 
                    paramValue:response.data.paramValue
                })
            }
        });
        
    }
    renderRow=(item,index)=>{
        return (
            <View key={item.applyId} style={styles.innerViewStyle}>
                {
                    item.auditState ==1 ?
                    <View style={[styles.titleViewStyle,{height:35, backgroundColor:'rgba(191,191,191,0.7)',borderRadius:5, justifyContent:'center',alignItems:'center'}]}>
                        <Text style={[styles.titleTextStyle,{fontWeight:'bold',fontSize:18,color:'#636363'}]}>
                        待审核</Text>
                        {/* {
                            item.internalFlag != 'Y' ? 
                            <Text></Text>
                            :
                            <Text style={{paddingTop:3, paddingBottom:3,paddingRight:5,position:'relative',paddingLeft:5,left:screenWidth / 4,borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>经川</Text>
                        } */}
                    </View> :
                    (
                        item.auditState ==3 ?
                        <View style={[styles.titleViewStyle,{height:35, backgroundColor:'rgba(255,0,0,0.5)',borderRadius:5, justifyContent:'center',alignItems:'center'}]}>
                            <Text style={[styles.titleTextStyle,{fontWeight:'bold',fontSize:18,color:'#832f2f'}]}>
                            驳回</Text>
                            {/* {
                                item.internalFlag != 'Y' ? 
                                <Text></Text>
                                :
                                <Text style={{paddingTop:3, paddingBottom:3,paddingRight:5,position:'relative',paddingLeft:5,left:screenWidth / 4,borderRadius:12, backgroundColor:'rgba(0,0,255,0.4)', color:'#FFFFFF'}}>经川</Text>
                            } */}
                        </View> : 
                        <View style={[styles.titleViewStyle,{height:35, backgroundColor:'rgba(57,195,66,0.5)',borderRadius:5, justifyContent:'center',alignItems:'center'}]}>
                            <Text style={[styles.titleTextStyle,{fontWeight:'bold',fontSize:18,color:'#237a28'}]}>
                            报名成功</Text>
                            {/* {
                                item.internalFlag != 'Y' ? 
                                <Text></Text>
                                :
                                <Text style={{paddingTop:3, paddingBottom:3,paddingRight:5,position:'relative',paddingLeft:5,left:screenWidth / 4,borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>经川</Text>
                            } */}
                        </View> 
                    )
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>姓名：{item.examPersonName}</Text>
                </View>
                <View style={{position:'absolute',right:10,top:45}}>
                    {
                        item.examPersonPhoto?
                        <View style={{height: 80, width:80}}>
                            <View>   
                            <TouchableOpacity onPress={() => {
                                let list = this.state.dataSource;
                                list.map((elem, index) => {
                                    if(elem.applyId == item.applyId){
                                        item.personPictureDisplay = true;
                                    }
                                })
                                console.log("personPictureDisplay",item.personPictureDisplay)
                                this.setState({
                                    dataSource:list,
                                })
                            }}>
                            <Image source={{ uri: (constants.image_addr + '/' + item.examPersonPhoto) }} style={{width:80,height:80,justifyContent:'center',alignItems:'center'}} />                                                    
                            </TouchableOpacity>
                            <Modal visible={item.personPictureDisplay} transparent={true}>
                            {/* <ImageViewer enableSwipeDown={false} menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} onSave={() => alert("点击了保存图片")}  */}
                                <ImageViewer enableSwipeDown={false} 
                                saveToLocalByLongPress={false}
                                onClick={() => { // 图片单击事件
                                    let list = this.state.dataSource;
                                    list.map((elem, index) => {
                                        if(elem.applyId == item.applyId){
                                            item.personPictureDisplay = false;
                                        }
                                    })
                                    console.log("personPictureDisplay",item.personPictureDisplay)
                                    this.setState({
                                        dataSource:list,
                                    })
                                }}
                                imageUrls={[{url:(constants.image_addr + '/' + item.examPersonPhoto)}]} />
                            </Modal>
                            </View>
                            {/* <Text>{constants.image_addr+ '/' + item.exchangeGoodsImage}</Text> */}
                        </View>
                        :
                        <View style={{height: 80, width:80,borderColor:'#AAAAAA',borderWidth:0.3,justifyContent:'center',alignItems:'center'}}>
                            <Text style={[styles.titleTextStyle,{color:"#aaaaaa"}]}>无</Text>
                        </View>
                    }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系方式：{item.contactNbr}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>出生日期：{item.birthAte}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>身份证号：{item.identityCardNbr}</Text>
                </View>   
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>注册号：{item.registrationNumber}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>报考科目：{item.examCourse}</Text>
                </View> 
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考试日期：{item.examDate}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考试时间：{item.examTime}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>邮箱：{item.examPersonEmail}</Text>
                </View>
                {
                    item.examReamk ?
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>注意事项：{item.examReamk}</Text>
                    </View> : <View/>
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>收费详情：{this.state.paramValue}</Text>
                </View>
                <View style={styles.titleViewStyle,{justifyContent:'center'}}>
                    {
                        item.display === "Y"?
                        <TouchableOpacity onPress={() => {
                            let list = this.state.dataSource;
                            list.map((elem, index) => {
                                if(elem.applyId == item.applyId){
                                    elem.display = "N"
                                }
                            })
                            this.setState({
                                dataSource:list
                            })
                            // console.log("==============",list)
                        }}>
                                <Text style={[styles.titleTextStyle,{color:"#CB4139",textAlign:'center'}]}>点击收起</Text>
                            {/* <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/backRoll.png')}></Image> */}
                            </TouchableOpacity>
                        :
                        <View/>
                    }
                </View>
                {
                    item.compressFileList && item.compressFileList.length > 0 ?
                    (
                        <View>
                            {
                                item.pictureDisplay === "N"?
                                //   -----------------请及时付款-可点击
                                    <View style={[styles.titleViewStyle, { justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                                        <TouchableOpacity onPress={() => {
                                            var urls = [];
                                            if(item.compressFileList && item.compressFileList.length > 0){
                                                for(var i=0;i<item.compressFileList.length;i++){
                                                    var url = {
                                                        url:constants.image_addr + '/' +  item.compressFileList[i].paramValue
                                                    } 
                                                    urls=urls.concat(url)
                                                    console.log(url)
                                                }
                                            }
                                            this.setState({
                                                urls:urls
                                            })
                                            let list = this.state.dataSource;
                                            list.map((elem, index) => {
                                                if(elem.applyId == item.applyId){
                                                    elem.pictureDisplay = "Y"
                                                }
                                            })
                                            this.setState({
                                                dataSource:list
                                            })
                                            // console.log("==============",list)
                                        }}>
                                            <View style={{flexDirection:"row"}}>
                                                <Text style={[styles.titleTextStyle,{color:"#CB4139"}]}>请及时付款</Text>
                                                <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/showRoll.png')}></Image>
                                            </View>
                                            {/* <Text style={[styles.titleTextStyle,{color:"#CB4139"}]}>点击展开</Text> */}
                                        </TouchableOpacity>
                                    </View>
                                :
                                <View>
                            {/* //   -----------------请及时付款-不可点击 */}
                                    <View style={styles.titleViewStyle}>
                                        <Text style={[styles.titleTextStyle,{color:"#CB4139"}]}>请及时付款</Text>
                                    </View>
                                    <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                        {
                                            item.compressFileList.map((item,index) =>{
                                            return(
                                                <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex'}]}>

                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        isShowImage:true,
                                                        pictureIndex:index
                                                    })
                                                    // uploadMultiImageLibrary(6, "user_header", (imageUploadResponse) => {
                                                    //     console.log("========imageUploadResponse", imageUploadResponse)
                                                    //     if (imageUploadResponse.code === 200) {
                                                    //         WToast.show({ data: "上传成功" });
                                                    //         let compressFileList = imageUploadResponse.data
                                                    //         this.setState({
                                                    //             compressFileList: compressFileList
                                                    //         })
                                                    //     }
                                                    //     else {
                                                    //         WToast.show({ data: imageUploadResponse.message });
                                                    //     }
                                                    // });
                                                }}>
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.paramValue) }} style={{ height: 150, width:120 }} />                                                    
                                                </TouchableOpacity>
                                                <Modal visible={this.state.isShowImage} transparent={true}>
                                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} index={this.state.pictureIndex} 
                                                    onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} saveToLocalByLongPress={false}/>
                                                </Modal>
                                            </View>
                                            )
                                            })
                                        }
                                    </View>
                                    {/* ---------------点击收起 */}
                                    <View style={[styles.titleViewStyle,{justifyContent:'center'}]}>
                                        {
                                            item.pictureDisplay === "Y"?
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    urls:[]
                                                })
                                                let list = this.state.dataSource;
                                                list.map((elem, index) => {
                                                    if(elem.applyId == item.applyId){
                                                        elem.pictureDisplay = "N"
                                                    }
                                                })
                                                this.setState({
                                                    dataSource:list
                                                })
                                                // console.log("==============",list)
                                            }}>
                                                <Text style={[styles.titleTextStyle,{color:"#CB4139",textAlign:'center'}]}>点击收起</Text>
                                                {/* <Image  style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/backRoll.png')}></Image> */}
                                            </TouchableOpacity>
                                            :
                                            <View/>
                                        }
                                    </View>
                                </View>
                            }
                        </View>
                    ):
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>收款码：无</Text>
                    </View>
                }
                {
                    item.internalFlag ==='N' ?
                    <View>
                        <View style={styles.titleViewStyle}>
                            <Text style={[styles.titleTextStyle,{color:'rgba(255,0,0,0.9)'}]}>请添加微信，及时联系老师审核报名信息！</Text>
                        </View>
                        <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                        {
                            item.applyChargePersonWechat?
                            <View style={{height: 80, width:80}}>
                                <View>
                                <TouchableOpacity onPress={() => {
                                    let list = this.state.dataSource;
                                    list.map((elem, index) => {
                                        if(elem.applyId == item.applyId){
                                            item.vx2DCode = true;
                                        }
                                    })
                                    console.log("vx2DCode",item.vx2DCode)
                                    this.setState({
                                        dataSource:list,
                                    })
                                }}>
                                    <Image source={{ uri: (constants.image_addr + '/' + item.applyChargePersonWechat) }} style={{width:80,height:80,justifyContent:'center',alignItems:'center'}} />                                                    
                                </TouchableOpacity>
                                <Modal visible={item.vx2DCode} transparent={true}>
                                    <ImageViewer enableSwipeDown={false} menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                        saveToLocalByLongPress={true}
                                        onClick={() => { // 图片单击事件
                                            let list = this.state.dataSource;
                                            list.map((elem, index) => {
                                                if(elem.applyId == item.applyId){
                                                    item.vx2DCode = false;
                                                }
                                            })
                                            console.log("vx2DCode",item.vx2DCode)
                                            this.setState({
                                                dataSource:list,
                                            })
                                        }}
                                        imageUrls={[{url:(constants.image_addr + '/' + item.applyChargePersonWechat)}]} 
                                        onSave ={() => {
                                            var imageUrl = constants.image_addr + '/' + item.applyChargePersonWechat;
                                            saveImage( imageUrl); 
                                        } }
                                    />
                                </Modal>
                                </View>
                            </View>
                        :
                        <View style={{height: 80, width:80,borderColor:'#AAAAAA',borderWidth:0.3,justifyContent:'center',alignItems:'center'}}>
                            <Text style={[styles.titleTextStyle,{color:"#aaaaaa"}]}>无</Text>
                        </View>
                        }
                        </View>
                    </View>
                    : 
                    <View/>
                }
                {
                    item.auditExplain ?
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle,{color:'rgba(255,0,0,0.9)'}]}>审核意见：{item.auditExplain}</Text>
                    </View> : <View/>
                }
                <View style={CommonStyle.itemBottomBtnStyle}>
                {
                    item.auditState ==4 ?
                    <TouchableOpacity onPress={()=>{
                        // Alert.alert('确认','您确定要导出准考证吗？',[
                        //     {
                        //         text:"取消", onPress:()=>{
                        //         WToast.show({data:'点击了取消'});
                        //         }
                        //     },
                        //     {
                        //         text:"确定", onPress:()=>{
                        //             WToast.show({data:'点击了确定'});
                        //             this.exportPdfFile(item.applyId)
                        //         }
                        //     }
                        // ]);

                        this.exportPdfFile(item.applyId);
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{backgroundColor: '#F2C16D',width:85,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/output.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>准考证</Text>
                        </View>
                    </TouchableOpacity>
                    :
                    <View/>
                }
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','确定要删除该报名信息吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteExam(item.applyId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:70,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        if (item.auditState ==4) {
                            return;
                        }
                        this.props.navigation.navigate("ExamApplyAdd",
                    {
                        applyId:item.applyId,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })}}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,flexDirection:"row"}
                        ,item.auditState ==4 ? CommonStyle.disableViewStyle : ""
                        ]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>修改</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("ExamApplyAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='我的报名'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle  }>
                <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>

                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.modal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle,{width:screenWidth,padding:0, height:ifIphoneXBodyViewHeight() - 60}]}>
                            <ScrollView>   
                                <View styles = {{width:screenWidth}}>
                                    {
                                        this.state.examTicket
                                            ?
                                            <View>
                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        showExamTicket:true,
                                                    })
                                                    console.log("准考证地址",this.state.examTicket);
                                                }}

                                                onLongPress={ () => {

                                                    Alert.alert('确认', '您确定要保存该准考证吗？', [
                                                        {
                                                            text: "取消", onPress: () => {
                                                                WToast.show({ data: '点击了取消' });
                                                                // this在这里可用，传到方法里还有问题
                                                                // this.props.navigation.goBack();
                                                            }
                                                        },
                                                        {
                                                            text: "确定", onPress: () => {
                                                                WToast.show({ data: '点击了确定' });
                                                                var ExamTicket = this.state.examTicket;
                                                                saveImage( ExamTicket);
                                                            }
                                                        }
                                                    ]);

                                                        
                                                }}
                                                >
                                                    <Image source={{ uri:  this.state.examTicket}} style={{width:screenWidth ,height:ifIphoneXContentViewHeight(),justifyContent:'center',alignItems:'center'}} />
                                                    <TouchableOpacity style={{position:'absolute',justifyContent:'center',width:screenWidth,alignItems:"center",bottom:50}}
                                                    onPress={() => {
                                                        this.setState({
                                                            modal:false,
                                                        })
                                                    }}
                                                    >
                                                    <Image style={{ width:50, height:50,justifyContent:'center'}} source={require('../../assets/icon/iconfont/cancel2.png')}></Image>
                                                    </TouchableOpacity>
                                                    
                                                </TouchableOpacity>
                                                <Modal visible={this.state.showExamTicket} transparent={true}>
                                                    <ImageViewer enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                        saveToLocalByLongPress={true}
                                                        onClick={() => { // 图片单击事件
                                                            this.setState({
                                                                showExamTicket:false,
                                                            })
                                                    }}
                                                    imageUrls={[{url:this.state.examTicket}]} 
                                                    onSave ={() => {
                                                        var ExamTicket = this.state.examTicket;
                                                        saveImage( ExamTicket); 
                                                     } }
                                                    />
                                                </Modal>
                                            </View>
                                                
                                            : <EmptyListComponent />
                                    }
                                </View>
                            </ScrollView>

                            
                            {/* <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                <TouchableOpacity onPress={() => { 
                                    this.setState({
                                        modal:false,
                                    }) 
                                }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                        <Image  style={{width:22, height:22,marginRight:10}} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={
                                    this.OpenExamTicket.bind(this)
                                    }>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                        <Image  style={{width:25, height:25,marginRight:10}} source={require('../../assets/icon/iconfont/download.png')}></Image>
                                        <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>下载</Text>
                                    </View>
                                </TouchableOpacity>
                            </View> */}
                        </View>      
                    </View>
                </Modal>

            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
   //     height:screenHeight - 70,
   //     backgroundColor:'#FFFFFF'
   // },
   inputRowStyle: {
       paddingLeft: 5,
       height: 40,
       flexDirection: 'row',
   },

   leftLabView: {
       height: 45,
       flexDirection: 'row',
       alignItems: 'center',
       paddingLeft: 10,
   },
   leftLabNameTextStyle: {
       fontSize: 18,
   },
   searchInputText: {
       width: screenWidth / 2,
       borderColor: '#000000',
       borderBottomWidth: 1,
       marginRight: 5,
       color: '#A0A0A0',
       fontSize: 16,
       marginLeft: 10,
       paddingLeft: 10,
       paddingRight: 10,
       paddingBottom: 0
   },
   innerViewStyle: {
    //    marginTop: 10,
       borderColor: "#F4F4F4",
       borderWidth: 8
   },
   titleViewStyle: {
       flexDirection: 'row',
       justifyContent: 'space-between',
       marginLeft: 10,
       marginRight: 10,
       marginBottom: 5,
       marginTop: 5,
   },
   titleTextStyle: {
       fontSize: 16
   },
   itemContentStyle: {
       flexDirection: 'row',
       alignItems: 'center'
   },
   itemContentImageStyle: {
       width: 120,
       height: 120
   },
   itemContentViewStyle: {
       flexDirection: 'row',
       justifyContent: 'space-between',
       marginLeft: 25
   },
   itemContentChildViewStyle: {
       flexDirection: 'column'
   },
   itemContentChildTextStyle: {
       marginLeft: 10,
       marginTop: 15,
       fontSize: 16
   },
})
