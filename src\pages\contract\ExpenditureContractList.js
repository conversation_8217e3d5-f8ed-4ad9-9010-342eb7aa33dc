import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import <PERSON>Viewer from 'react-native-image-zoom-viewer';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {saveImage} from '../../utils/CameraRollUtils';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class ExpenditureContractList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      searchKeyWordEx: '',
      topBlockLayoutHeight: 0,
      year: null,
      selYearsChooseName: '全部',
      display: 'N',
      compressFileList: [],
      urls: [],
      isShowImage: false,
      pictureIndex: 0,
      moreModal: false,
      modalItem: {},
      deleteModal: false,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    var currentDate = new Date();
    var year = currentDate.getFullYear();
    this.setState({
      year: year,
    });

    let yearsChooseDataSource = [
      {
        chooseCode: 'all',
        chooseName: '全部',
      },
      {
        chooseCode: 1,
        chooseName: year,
      },
      {
        chooseCode: 2,
        chooseName: year - 1,
      },
      {
        chooseCode: 3,
        chooseName: year - 2,
      },
      {
        chooseCode: 4,
        chooseName: year - 3,
      },
      // {
      //     chooseCode:5,
      //     chooseName:year-4,
      // }
    ];
    this.setState({
      yearsChooseDataSource: yearsChooseDataSource,
    });
    this.loadExpenditureContractList();

    // const { route, navigation } = this.props;
    // if (route && route.params) {
    //     const { tenantId } = route.params;
    //     if (tenantId) {
    //         console.log("=============tenantId" + tenantId + "");
    //     }
    // }
  }

  //回调函数
  callBackFunction = () => {
    let url = '/biz/contract/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWordEx: this.state.searchKeyWordEx,
      contractType: 'E',
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  //下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/contract/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWordEx: this.state.searchKeyWordEx,
      contractType: 'E',
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataAll = response.data.dataList;
      let list = dataAll;
      let listNew = [];
      list.map((item, index) => {
        listNew.push(
          Object.assign({}, item, {display: 'N', pictureDisplay: 'N'}),
        );
      });
      this.setState({
        dataSource: listNew,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };

  //上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadExpenditureContractList();
  };

  loadExpenditureContractList = () => {
    let url = '/biz/contract/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      searchKeyWordEx: this.state.searchKeyWordEx,
      contractType: 'E',
    };
    httpPost(url, loadRequest, this.loadExpenditureContractListCallBack);
  };

  loadExpenditureContractListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      let list = dataAll;
      let listNew = [];
      list.map((item, index) => {
        listNew.push(
          Object.assign({}, item, {display: 'N', pictureDisplay: 'N'}),
        );
      });
      this.setState({
        dataSource: listNew,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  searchByKeyWord = () => {
    // let toastOpts;
    // if (!this.state.searchKeyWordEx) {
    //     toastOpts = getFailToastOpts("请输入客户、合同或签订时间");
    //     WToast.show(toastOpts)
    //     return;
    // }
    let loadUrl = '/biz/contract/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWordEx: this.state.searchKeyWordEx,
      contractType: 'E',
      qryStartYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.state.selYearsChooseName,
      qryEndYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.addOneYear(this.state.selYearsChooseName),
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  deleteExpenditureContract = (contractId) => {
    console.log('=======delete=contractId', contractId);
    let url = '/biz/contract/delete';
    let requestParams = {contractId: contractId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  modifyExpenditureContractState = (contractId, contractState) => {
    console.log('=======delete=contractId', contractId);
    let url = '/biz/contract/modify';
    let requestParams = {contractId: contractId, contractState: contractState};
    httpPost(url, requestParams, this.modifyExpenditureContractStateCallBack);
  };

  // 修改状态操作的回调操作
  modifyExpenditureContractStateCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '状态修改完成'});
      this._loadFreshData();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.contractId} style={styles.innerViewStyle}>
        {index == 0 ? (
          <View style={CommonStyle.lineListHeadRenderRowStyle}></View>
        ) : (
          <View></View>
        )}
        <View style={{position: 'absolute', right: 0, top: 0, marginRight: 15}}>
          <TouchableOpacity
            onPress={() => {
              this.setState({
                moreModal: true,
                modalItem: item,
              });
            }}>
            <View
              style={[
                {
                  width: 35,
                  height: 35,
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <Image
                style={{width: 28, height: 28}}
                source={require('../../assets/icon/iconfont/more.png')}></Image>
            </View>
          </TouchableOpacity>
        </View>
        <View style={CommonStyle.titleViewStyleSpecial}>
          <Text
            style={[
              CommonStyle.titleTextStyleSpecial,
              {width: screenWidth - 120},
            ]}>
            {item.contractName}
          </Text>
          {/* <Text style={[CommonStyle.titleTextStyleSpecial, {width:screenWidth - 120}]}>合同名称：{item.contractName}</Text> */}
          {/* {
                        item.partyAEnterpriseName ? 
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                            {item.partyAEnterpriseAbbreviation ? item.partyAEnterpriseAbbreviation : item.partyAEnterpriseName}
                        </Text>
                        :
                        null
                    } */}
        </View>
        {/* <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>合同编号：{item.contractCode}</Text>
                </View> */}
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            合同编号：{item.contractCode}
          </Text>
          {item.partyAEnterpriseName ? (
            <Text
              style={{
                paddingTop: 3,
                paddingBottom: 3,
                paddingLeft: 5,
                paddingRight: 5,
                height: 23,
                borderRadius: 12,
                backgroundColor: 'rgba(255,0,0,0.4)',
                color: '#FFFFFF',
              }}>
              {item.partyAEnterpriseAbbreviation
                ? item.partyAEnterpriseAbbreviation
                : item.partyAEnterpriseName}
            </Text>
          ) : null}
        </View>
        <View style={[CommonStyle.titleViewStyle]}>
          <Text style={CommonStyle.titleTextStyle}>
            合同金额：{item.contractAmount ? item.contractAmount : '无'}
          </Text>
        </View>
        <View style={[CommonStyle.titleViewStyle]}>
          <Text style={CommonStyle.titleTextStyle}>
            乙方：{item.supplierName ? item.supplierName : '无'}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            签订日期：{item.signingTime}
          </Text>
        </View>
        {/* <View style={[styles.inputRowStyle,{margin:0,padding:0}]}>
                        <View style={[CommonStyle.titleViewStyle]}>
                            <Text style={styles.titleTextStyle}>合同金额：</Text>
                        </View>
                        <Text style={[CommonStyle.titleTextStyle]} numberOfLines={2}>
                            {item.parentContractName ? item.parentContractName : "无"}
                        </Text>
                </View> */}
        <View style={[CommonStyle.newTitleViewStyle]}>
          <View>
            <Text
              style={[CommonStyle.newTitleTextStyle, {width: null}]}
              numberOfLines={2}>
              总包合同名称：
            </Text>
          </View>
          <View>
            <Text style={[CommonStyle.newTitleTextStyle]} numberOfLines={2}>
              {item.parentContractName ? item.parentContractName : '无'}
            </Text>
          </View>
        </View>

        <View style={[CommonStyle.newTitleViewStyle]}>
          <View>
            <Text
              style={[CommonStyle.newTitleTextStyle, {width: null}]}
              numberOfLines={2}>
              总包合同甲方：
            </Text>
          </View>
          <View>
            <Text style={[CommonStyle.newTitleTextStyle]} numberOfLines={2}>
              {item.parentContractCustomerName
                ? item.parentContractCustomerName
                : '无'}
            </Text>
          </View>
        </View>
        {/* <View style={[CommonStyle.titleViewStyle]}>
                    <Text style={[CommonStyle.titleTextStyle]} numberOfLines={2} textBreakStrategy ={true}>总包合同甲方：{item.parentContractCustomerName ? item.parentContractCustomerName : "无"}</Text>
                </View> */}
        {item.compressFileList && item.compressFileList.length > 0 ? (
          <View>
            {item.pictureDisplay === 'N' ? (
              <View
                style={[
                  CommonStyle.titleViewStyle,
                  {justifyContent: 'flex-start', flexWrap: 'wrap'},
                ]}>
                <Text style={CommonStyle.titleTextStyle}>附件：</Text>
                <TouchableOpacity
                  onPress={() => {
                    var urls = [];
                    if (
                      item.compressFileList &&
                      item.compressFileList.length > 0
                    ) {
                      for (var i = 0; i < item.compressFileList.length; i++) {
                        var url = {
                          url:
                            constants.image_addr +
                            '/' +
                            item.compressFileList[i].compressFile,
                        };
                        urls = urls.concat(url);
                        console.log(url);
                      }
                    }
                    this.setState({
                      urls: urls,
                    });
                    let list = this.state.dataSource;
                    list.map((elem, index) => {
                      if (elem.contractId == item.contractId) {
                        elem.pictureDisplay = 'Y';
                      }
                    });
                    this.setState({
                      dataSource: list,
                    });
                    // console.log("==============",list)
                  }}>
                  <Text
                    style={[CommonStyle.titleTextStyle, {color: '#CB4139'}]}>
                    点击展开
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View>
                <View style={[CommonStyle.titleViewStyle]}>
                  <Text style={CommonStyle.titleTextStyle}>附件：</Text>
                </View>
                <View style={[{flexDirection: 'row', flexWrap: 'wrap'}]}>
                  {item.compressFileList.map((item, index) => {
                    return (
                      <View
                        style={[
                          {
                            width: 120,
                            height: 150,
                            marginLeft: 10,
                            marginBottom: 10,
                            display: 'flex',
                          },
                        ]}>
                        <TouchableOpacity
                          onPress={() => {
                            this.setState({
                              isShowImage: true,
                              pictureIndex: index,
                            });
                            // uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                            //     console.log("========imageUploadResponse", imageUploadResponse)
                            //     if (imageUploadResponse.code === 200) {
                            //         WToast.show({ data: "上传成功" });
                            //         let compressFileList = imageUploadResponse.data
                            //         this.setState({
                            //             compressFileList: compressFileList
                            //         })
                            //     }
                            //     else {
                            //         WToast.show({ data: imageUploadResponse.message });
                            //     }
                            // });
                          }}>
                          <Image
                            source={{
                              uri:
                                constants.image_addr + '/' + item.compressFile,
                            }}
                            style={{height: 150, width: 120}}
                          />
                        </TouchableOpacity>
                        <Modal
                          visible={this.state.isShowImage}
                          transparent={true}>
                          <ImageViewer
                            onClick={() => {
                              this.setState({isShowImage: false});
                            }}
                            index={this.state.pictureIndex}
                            enableSwipeDown
                            menuContext={{
                              saveToLocal: '保存到本地',
                              cancel: '取消',
                            }}
                            onSwipeDown={() => {
                              this.setState({isShowImage: false});
                            }}
                            imageUrls={this.state.urls}
                            onSave={() => {
                              saveImage(
                                this.state.urls[this.state.pictureIndex].url,
                              );
                            }}
                          />
                        </Modal>
                      </View>
                    );
                  })}
                </View>
                <View
                  style={[
                    CommonStyle.titleViewStyle,
                    {justifyContent: 'center'},
                  ]}>
                  {item.pictureDisplay === 'Y' ? (
                    <TouchableOpacity
                      onPress={() => {
                        this.setState({
                          urls: [],
                        });
                        let list = this.state.dataSource;
                        list.map((elem, index) => {
                          if (elem.contractId == item.contractId) {
                            elem.pictureDisplay = 'N';
                          }
                        });
                        this.setState({
                          dataSource: list,
                        });
                        // console.log("==============",list)
                      }}>
                      <Text
                        style={[
                          CommonStyle.titleTextStyle,
                          {color: '#CB4139', textAlign: 'center'},
                        ]}>
                        点击收起
                      </Text>
                    </TouchableOpacity>
                  ) : (
                    <View />
                  )}
                </View>
              </View>
            )}
          </View>
        ) : (
          <View style={CommonStyle.titleViewStyle}>
            <Text style={CommonStyle.titleTextStyle}>附件：无</Text>
          </View>
        )}

        <View
          style={[
            CommonStyle.itemBottomBtnStyle,
            {flexWrap: 'wrap', marginRight: 15},
          ]}>
          <TouchableOpacity
            onPress={() => {
              this.props.navigation.navigate('ExpenditureMoneyPlanList', {
                // 传递参数
                contractId: item.contractId,
                contractAmount: item.contractAmount,
                // 传递回调函数
                refresh: this.callBackFunction,
              });
            }}>
            <View
              style={[
                CommonStyle.itemBottomEditBtnViewStyle,
                {width: 95, flexDirection: 'row', backgroundColor: '#FFB800'},
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 2}}
                source={require('../../assets/icon/iconfont/planCollectMoney.png')}></Image>
              <Text style={CommonStyle.itemBottomEditBtnTextStyle}>
                付款计划
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              this.props.navigation.navigate('ExpenditureMoneyActualList', {
                // 传递参数
                contractId: item.contractId,
                contractAmount: item.contractAmount,
                // 传递回调函数
                refresh: this.callBackFunction,
              });
            }}>
            <View
              style={[
                CommonStyle.itemBottomEditBtnViewStyle,
                {
                  width: 95,
                  flexDirection: 'row',
                  backgroundColor: '#FA353F',
                  marginLeft: 0,
                },
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 2}}
                source={require('../../assets/icon/iconfont/expenditureActualMoney.png')}></Image>
              <Text style={CommonStyle.itemBottomEditBtnTextStyle}>
                实际付款
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
      //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
      // </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[{flexDirection: 'row', alignItems: 'center'}]}>
          {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
          {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={{color: '#3C6CDE', fontWeight: 'bold'}}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      // <TouchableOpacity onPress={() => {
      //     this.props.navigation.navigate("ExpenditureContractAdd",
      //     {
      //         // 传递回调函数
      //         refresh: this.callBackFunction
      //     })
      // }}>
      //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
      // </TouchableOpacity>
      <View
        style={{flexDirection: 'row-reverse', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('ExpenditureContractAdd', {
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Image
            style={{width: 22, height: 22, marginVertical: 2}}
            source={require('../../assets/icon/iconfont/add.png')}></Image>
        </TouchableOpacity>
      </View>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  addOneYear = (year) => {
    var date = new Date(year, 10, 24, 10, 33, 0, 0);
    var addYear = date.getFullYear() + 1;
    return addYear;
  };

  yearsChooseStateRow = (item, index) => {
    return (
      <View key={item.chooseCode}>
        <TouchableOpacity
          onPress={() => {
            var selYearsChooseName = item.chooseName;
            this.setState({
              selYearsChooseName: selYearsChooseName,
            });

            let loadUrl = '/biz/contract/list';
            let loadRequest = {
              currentPage: 1,
              pageSize: this.state.pageSize,
              qryStartYear:
                selYearsChooseName === '全部' ? null : selYearsChooseName,
              qryEndYear:
                selYearsChooseName === '全部'
                  ? null
                  : this.addOneYear(selYearsChooseName),
              searchKeyWordEx: this.state.searchKeyWordEx,
              contractType: 'E',
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
          }}>
          <View
            key={item.chooseCode}
            style={[
              {
                width: screenWidth / 5,
                height: 49,
                flexDirection: 'row',
                justifyContent: 'center',
              },
              // ,item.stateCode === this.state.selCompletionStateCode ?
              //     [styles.selectedBlockItemViewStyle]
              //     :
              //     [styles.blockItemViewStyle],
            ]}>
            <Text
              style={[
                item.chooseName === this.state.selYearsChooseName
                  ? {
                      color: '#255BDA',
                      fontSize: 16,
                      fontWeight: '500',
                      lineHeight: 49,
                      textAlign: 'center',
                      borderColor: '#255BDA',
                      borderBottomWidth: 2,
                      paddingLeft: 5,
                      paddingRight: 5,
                    }
                  : {
                      color: '#2B333F',
                      fontSize: 16,
                      fontWeight: '500',
                      lineHeight: 49,
                      textAlign: 'center',
                    },
              ]}>
              {item.chooseName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="外协合同"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />

        <View
          style={[
            CommonStyle.headViewStyle,
            {width: screenWidth, borderWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={{
              marginTop: 0,
              index: 1000,
              flexWrap: 'wrap',
              flexDirection: 'row',
            }}>
            {this.state.yearsChooseDataSource &&
            this.state.yearsChooseDataSource.length > 0 ? (
              this.state.yearsChooseDataSource.map((item, index) => {
                return this.yearsChooseStateRow(item);
              })
            ) : (
              <View />
            )}
          </View>
          <View
            style={[
              CommonStyle.headViewStyle,
              {borderLeftWidth: 0, borderRightWidth: 0},
            ]}
            onLayout={this.topBlockLayout.bind(this)}>
            <View style={CommonStyle.singleSearchBox}>
              <View style={CommonStyle.searchBoxWithoutOthers}>
                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                <Image
                  style={{width: 16, height: 16, marginLeft: 7}}
                  source={require('../../assets/icon/iconfont/search.png')}></Image>
                <TextInput
                  style={{
                    color: 'rgba(rgba(0, 10, 32, 0.45))',
                    fontSize: 14,
                    marginLeft: 5,
                    paddingTop: 0,
                    paddingBottom: 0,
                    paddingRight: 0,
                    paddingLeft: 0,
                    width: '100%',
                  }}
                  returnKeyType="search"
                  returnKeyLabel="搜索"
                  onSubmitEditing={(e) => {
                    this.searchByKeyWord();
                  }}
                  placeholder={'总包合同/合同/甲方/乙方/签订时间'}
                  onChangeText={(text) =>
                    this.setState({searchKeyWordEx: text})
                  }>
                  {this.state.searchKeyWordEx}
                </TextInput>
              </View>
            </View>
          </View>
        </View>

        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> */}
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.contractId}
            ListEmptyComponent={this.emptyComponent}
            ItemSeparatorComponent={this.space}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
          {/* </ScrollView> */}
        </View>
        {/* 更多操作弹窗Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.moreModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    if (
                      dateDiffHours(
                        constants.nowDateTime,
                        this.state.modalItem.gmtCreated,
                      ) > constants.editDeleteTimeLimit
                    ) {
                      return;
                    }
                    this.setState({
                      moreModal: false,
                    });
                    this.props.navigation.navigate('ExpenditureContractAdd', {
                      contractId: this.state.modalItem.contractId,
                      // productionLineId:item.productionLineId,
                      // 传递回调函数
                      refresh: this.callBackFunction,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                      dateDiffHours(
                        constants.nowDateTime,
                        this.state.modalItem.gmtCreated,
                      ) > constants.editDeleteTimeLimit
                        ? CommonStyle.disableViewStyle
                        : '',
                    ]}>
                    {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                    <Text
                      style={{
                        color: 'rgba(0, 10, 32, 0.85)',
                        fontSize: 18,
                        lineHeight: 52,
                      }}>
                      编辑
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              <View>
                <TouchableOpacity
                  onPress={() => {
                    if (
                      dateDiffHours(
                        constants.nowDateTime,
                        this.state.modalItem.gmtCreated,
                      ) > constants.editDeleteTimeLimit
                    ) {
                      return;
                    }
                    this.setState({
                      moreModal: false,
                      deleteModal: true,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                      dateDiffHours(
                        constants.nowDateTime,
                        this.state.modalItem.gmtCreated,
                      ) > constants.editDeleteTimeLimit
                        ? CommonStyle.disableViewStyle
                        : '',
                    ]}>
                    {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                    <Text
                      style={[
                        {
                          color: 'rgba(0, 10, 32, 0.85)',
                          fontSize: 18,
                          lineHeight: 52,
                        },
                      ]}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: 291,
                  height: 50,
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      moreModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 105,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontFamily: 'PingFangSC',
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 删除弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.deleteModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 292,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>您确定要删除该合同吗?</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  删除后数据不可恢复，请谨慎操作
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 292,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontFamily: 'PingFangSC',
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了确定'});
                    this.deleteExpenditureContract(
                      this.state.modalItem.contractId,
                    );
                  }}>
                  <View
                    style={[
                      {
                        width: 146,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontFamily: 'PingFangSC',
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
    marginTop: 5,
  },

  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
  },
  searchInputText: {
    width: screenWidth - 100,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },
  innerViewStyle: {
    marginTop: 10,
    // marginLeft:15
    // borderColor:"#F4F4F4",
    // borderWidth:8,
  },
  innerViewStyleSearch: {
    // marginTop:10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
});
