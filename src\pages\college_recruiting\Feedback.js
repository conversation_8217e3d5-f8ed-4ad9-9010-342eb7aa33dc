import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,KeyboardAvoidingView,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CrHeadScreen from '../../component/CrHeadScreen';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class Feedback extends Component {
    constructor(){
        super()
        this.state = {
            operate:"",
            feedbackId: "",
            feedbackContent: "",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
    
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { feedbackId } = route.params;
            if (feedbackId) {
                console.log("========Edit==feedbackId:", feedbackId);
                this.setState({
                    feedbackId:feedbackId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/suggest/feedback/get";
                loadRequest={'feedbackId':feedbackId};
                httpPost(loadTypeUrl, loadRequest, this.loadSuggestionFeedbackDataCallBack);
            }
            else{
                this.setState({
                    operate:""
                })
            }
        }
    }
    loadSuggestionFeedbackDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                feedbackId:response.data.feedbackId,
                feedbackContent:response.data.feedbackContent,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                <Image  style={{width:24, height:24}} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("SuggestionFeedbackList")
            }}>
                {/* <Text style={CommonStyle.headRightText}>查看反馈</Text> */}
            </TouchableOpacity>
        )
    }

    saveSuggestionFeedback =()=> {
        console.log("=======saveSuggestionFeedback");
        let toastOpts;
        if (!this.state.feedbackContent) {
            toastOpts = getFailToastOpts("请输入意见反馈");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/suggest/feedback/add";
        if (this.state.feedbackId) {
            console.log("=========Edit===feedbackId", this.state.feedbackId)
            url= "/biz/suggest/feedback/modify";
        }
        let requestParams={
            feedbackId:this.state.feedbackId,
            feedbackContent: this.state.feedbackContent,
        };
        httpPost(url, requestParams, this.saveSuggestionFeedbackCallBack);
    }
    
    // 保存回调函数
    saveSuggestionFeedbackCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('提交完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    

    render(){
        return (
            <View>
                <CrHeadScreen title={'意见反馈'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    {/* <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>意见反馈</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>

                    <View style={[styles.inputRowStyle,{height:255}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:255}]}
                            placeholder={'请输入意见反馈'}
                            onChangeText={(text) => this.setState({feedbackContent:text})}
                        >
                            {this.state.feedbackContent}
                        </TextInput>
                    </View> */}

                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle]}>意见反馈</Text>
                            <View style={[styles.inputRowStyle,{height:100}]}>
                            <TextInput 
                                style={styles.inputRightText}
                                placeholder={'必填 请输入'}
                                keyboardType='numeric'
                                textAlignVertical="top"
                                multiline={true}
                                onChangeText={(text) => this.setState({feedbackContent:text})}       
                                placeholderTextColor={'#00000040'}             
                            >
                                {this.state.feedbackContent}
                            </TextInput>
                        </View>
                    </View>

                    {/* <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>意见反馈</Text>
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                            placeholder={'请输入未完成工作'}
                            onChangeText={(text) => this.setState({unfinishedWork:text})}
                        >
                            {this.state.unfinishedWork}
                        </TextInput>
                    </View> */}
                    
                    
                    {/* <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveSuggestionFeedback.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>提交</Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                </ScrollView>

                <View style={[CommonStyle.btnRowStyle,{display:'flex',justifyContent:'center',alignItems:'center',bottom:10,position:'absolute',zIndex:100,left:40}]}>
                    <TouchableOpacity onPress={this.saveSuggestionFeedback.bind(this)}>
                        <View style={[styles.btnRowRightSaveBtnView1, {width:screenWidth - 80,height:45,borderRadius:40,opacity:0.8 }]}>
                            {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    titleTextStyle: {
        fontSize: 14, 
        color: '#00000073' ,
        marginTop:5,
        marginBottom:0,
    },
    titleViewStyle: {
        flexDirection: 'column',
        marginLeft: 20,
        marginTop: 15,
        marginBottom: 0,
        justifyContent:'center',
        width:screenWidth - 40,
    },
    inputRightText: {
        marginTop:0,
        width: screenWidth - 40,
        borderColor: '#EEE',
        borderBottomWidth: 1,
        color: '#000000D9',
        fontSize: 15,
        height:150,
        // justifyContent:'flex-start',
        alignItems:'center',

    },
    btnRowRightSaveBtnView1:{
        backgroundColor:'#3366FF',
        alignItems:'center',
        // alignContent:'center',
        justifyContent:'center',
        borderRadius:30,
        flexDirection:'row',
        width:130,height:40,
        marginRight:35,
        marginTop:15
    },
    selectViewItem:{
        width:100, justifyContent:'center', alignItems:'center'
    },
    selectTextItem:{
        fontSize:18,
        fontWeight:'bold'
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
})