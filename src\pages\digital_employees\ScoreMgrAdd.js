import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Image, TextInput,
    FlatList, RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ScrollView } from 'react-native-gesture-handler';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = (screenWidth - 40) / 2;
export default class ScoreMgrAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 1500,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            departmentDataSource: [],
            topBlockLayoutHeight: 0,
            selDepartmentId: null,
            courseId: null,
            courseName: null,
            oldStaffScoreList: [],
            newStaffScoreList: [],
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { courseId, courseName, selDepartmentId } = route.params;
            if (courseId) {
                this.setState({
                    courseId: courseId,
                })
            }
            if (courseName) {
                this.setState({
                    courseName: courseName,
                })
            }
            if (selDepartmentId) {
                this.setState({
                    selDepartmentId: selDepartmentId,
                })
                this.loadStaffList(selDepartmentId, courseId);
            }
        }


    }

    // 回调函数
    callBackFunction = () => {
        let loadUrl = "/biz/score/get_same_department_staff_score";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId": this.state.selDepartmentId,
            "courseId": this.state.courseId,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let loadUrl = "/biz/score/get_same_department_staff_score";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId": this.state.selDepartmentId,
            "courseId": this.state.courseId,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var dataNew = response.data;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadStaffList();
    }

    loadStaffList = (selDepartmentId, courseId) => {
        let loadUrl = "/biz/score/get_same_department_staff_score";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "departmentId": selDepartmentId ? selDepartmentId : this.state.selDepartmentId,
            "courseId": courseId ? courseId : this.state.courseId,
        };
        httpPost(loadUrl, loadRequest, this.loadStaffListCallBack);
    }

    loadStaffListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data) {

            var dataNew = response.data;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
            var oldScoreDTOList = [];
            var _scoreDTO;
            dataAll.forEach((item) => {
                if (item.score || item.score == 0) {
                    _scoreDTO = {
                        "courseId": this.state.courseId,
                        "userId": item.userId,
                        "scoreId": item.scoreId,
                        "score": parseInt(item.score),
                    }
                }
                else {
                    _scoreDTO = {
                        "courseId": this.state.courseId,
                        "userId": item.userId,
                        "scoreId": item.scoreId,
                        "score": null,
                    }
                }
                oldScoreDTOList.push(_scoreDTO);
            });
            this.setState({
                oldStaffScoreList: oldScoreDTOList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow = (item, index) => {
        return (
            <View key={item.userId} style={[styles.innerViewStyle]}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>学员名称：{item.staffName}</Text>
                </View>
                <View style={[styles.titleViewStyle, { flexDirection: 'row', justifyContent: 'flex-start', height: 40, alignItems: 'center' }]}>
                    <Text style={styles.titleTextStyle}>成绩：</Text>
                    <TextInput
                        keyboardType='numeric'
                        style={[styles.inputRightText]}
                        placeholder={'请输入成绩'}
                        onChangeText={(text) => {
                            let newText = (text != '' && text.substr(0, 1) == '.') ? '' : text;
                            newText = newText.replace(/^0+[0-9]+/g, "0"); //不能以0开头输入
                            newText = newText.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
                            newText = newText.replace(/\.{2,}/g, "."); //只保留第一个, 清除多余的
                            newText = newText.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
                            newText = newText.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数  
                            this.setState({ text: newText })
                            item.score = newText
                        }}
                    >
                        {item.score}
                    </TextInput>
                </View>
            </View >
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                let toastOpts;
                // 遍历录过的成绩
                var newStaffScoreList = [];
                var _scoreDTO;
                this.state.dataSource.forEach((item) => {
                    if (item.score && !isNumber(item.score)) {
                        toastOpts = getFailToastOpts(item.staffName + "成绩不是数字");
                        WToast.show(toastOpts)
                        return;
                    }
                    if (parseInt(item.score) >= 0 && parseInt(item.score) <= 100) {
                        _scoreDTO = {
                            "courseId": this.state.courseId,
                            "userId": item.userId,
                            "scoreId": item.scoreId,
                            "score": parseInt(item.score),
                        }
                    }
                    else {
                        _scoreDTO = {
                            "courseId": this.state.courseId,
                            "userId": item.userId,
                            "scoreId": item.scoreId
                        }
                    }
                    newStaffScoreList.push(_scoreDTO);
                });
                this.setState({
                    newStaffScoreList: newStaffScoreList,
                })

                console.log("=====newStaffScoreList:", newStaffScoreList);
                console.log("=====oldStaffScoreList:", this.state.oldStaffScoreList);

                let loadUrl = "/biz/score/save_same_department_staff_score";
                let requestParams = {
                    "newStaffScoreList": newStaffScoreList,
                    "oldStaffScoreList": this.state.oldStaffScoreList,
                };
                httpPost(loadUrl, requestParams, (response) => {
                    if (response.code == 200) {
                        let toastOpts;
                        if (this.props.route.params.refresh) {
                            this.props.route.params.refresh();
                        }
                        toastOpts = getSuccessToastOpts('保存完成');
                        WToast.show(toastOpts);
                        this.props.navigation.goBack()
                    }
                });

            }}>
                <Image style={{ width: 30, height: 30 }} source={require('../../assets/icon/iconfont/ok1.png')}></Image>
                {/* <Text style={CommonStyle.headRightText}>完成</Text> */}
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={'录成绩'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle, { marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={[styles.innerViewStyle, { marginTop: 0, index: 1000 }]} >
                        <Text style={[styles.titleTextStyle, { fontWeight: 'bold', marginRight: 100 }]}>实习名称：{this.state.courseName}</Text>
                    </View>
                </View>
                <ScrollView style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    {
                        (this.state.dataSource && this.state.dataSource.length > 0)
                            ?
                            this.state.dataSource.map((item, index) => {
                                return this.renderRow(item, index)
                            })
                            : <EmptyRowViewComponent title="部门没有人啊" />
                    }
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRightText: {
        width: 100,
        height: 40,
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    innerViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});