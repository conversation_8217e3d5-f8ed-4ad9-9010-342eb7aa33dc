import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Linking,Clipboard,
    FlatList,RefreshControl,TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class AssessAuditList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,

            topBlockLayoutHeight:0,
            selAssessRecordStateCode:'all',

        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        let assessRecordState = [
            {
                stateCode:'all',
                stateName:'全部',
            },
            {
                stateCode:'0AA',
                stateName:'未审批',
            },
            {
                stateCode:'0AB',
                stateName:'已审批',
            }
        ]
        this.setState({
            assessRecordState:assessRecordState,
        })

        this.loadAssessAuditList();
    }

    callBackFunction=()=>{
        let url= "/biz/assess/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "assessUserId":constants.loginUser.userId,
            "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            console.log("=====assessUserId=====",constants.loginUser.userId)
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/assess/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "assessUserId":constants.loginUser.userId,
            "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadAssessAuditList();
    }

    loadAssessAuditList=()=>{
        let url= "/biz/assess/record/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "assessUserId":constants.loginUser.userId,
            "assessRecordState": this.state.selAssessRecordStateCode === 'all' ? null : this.state.selAssessRecordStateCode,
        };
        httpPost(url, loadRequest, this.loadAssessAuditListCallBack);
    }

    loadAssessAuditListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            console.log(dataNew)
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    renderAssessRecordStateRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    let selAssessRecordStateCode = item.stateCode;
                    this.setState({
                        "selAssessRecordStateCode":selAssessRecordStateCode
                    })
                    let loadUrl= "/biz/assess/record/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "assessUserId":constants.loginUser.userId,
                        "assessRecordState": selAssessRecordStateCode === 'all' ? null : selAssessRecordStateCode,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.stateCode} style={[CommonStyle.tabItemViewStyle]}>
                        <Text style={[item.stateCode === this.state.selAssessRecordStateCode ?
                            [CommonStyle.selectedtabItemTextStyle]
                            :
                            [CommonStyle.tabItemTextStyle]
                        ]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.assessRecordId} style={[CommonStyle.innerViewStyle]}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>申请日期：{item.applyDate}</Text>
                    {
                        item.assessRecordState ==="0AB" ? 
                        <View>
                            <Text style={{color:"#1BBC82"}}>已审批</Text>
                        </View>
                        :
                        <View>
                            <Text style={{color:'#FD4246'}}>未审批</Text>
                        </View>
                    }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>申请人：{item.applyUserName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核标题：{item.assessTitle}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核内容：{item.assessContent}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核类别：{item.assessClassName}</Text>
                </View>
                {
                    item.assessDifficulty?
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>考核难度：{item.assessDifficultyName}</Text>
                    </View>                
                    :
                    <View/>
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>考核人：{item.assessUserName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>预计考核日期：{item.expectAssessDate}</Text>
                </View>
                {
                    item.assessRecordState ==="0AB"?
                    <View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>实际考核日期：{item.actualAssessDate}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>考核结果：{item.assessResultName}</Text>
                        </View>
                        <View style={[styles.titleViewStyle]}>
                            <Text style={[styles.titleTextStyle,{marginBottom:10}]}>考核意见：{item.assessOpinion?item.assessOpinion:"无"}</Text>
                        </View>
                    </View>                 
                    :
                    <View/>
                }

                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    {
                        item.assessRecordState === "0AB" ?
                        <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                            <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("AssessAudit", 
                                    {
                                        // 传递参数
                                        assessRecordId: item.assessRecordId,
                                        operate:"edit_audit",
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                                <View style={[CommonStyle.itemBottomEditBtnViewStyle,{backgroundColor:"#1BBC82",width:75,flexDirection:"row"}]}>
                                    <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/restart.png')}></Image>
                                    <Text style={CommonStyle.itemBottomEditBtnTextStyle}>重审</Text>
                                </View>
                            </TouchableOpacity>
                        </View>                        
                        :
                        <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                            <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("AssessAudit", 
                                    {
                                        // 传递参数
                                        assessRecordId: item.assessRecordId,
                                        operate:"audit",
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                                <View style={[CommonStyle.itemBottomEditBtnViewStyle,{backgroundColor:"#FD4246",width:75,flexDirection:"row"}
                                ]}>
                                    <Image  style={{width:18, height:18,marginRight:5}} source={require('../../assets/icon/iconfont/examine.png')}></Image>
                                    <Text style={CommonStyle.itemBottomEditBtnTextStyle}>审批</Text>
                                </View>
                            </TouchableOpacity>
                        </View> 
                        
                    }
                    
                    
                </View>
            </View>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='考核审批'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                    {
                        (this.state.assessRecordState && this.state.assessRecordState.length > 0) 
                        ? 
                        this.state.assessRecordState.map((item, index)=>{
                            return this.renderAssessRecordStateRow(item)
                        })
                        : <View/>
                    }  
                    </View> 
                </View>    

                <View style={[CommonStyle.contentViewStyle,{ height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerHeadViewStyle:{
        borderColor:"#ffffff",
        borderWidth:4,
        backgroundColor:"#ffffff"
    },
    // 分段器样式
    blockItemViewStyle: {
        margin: 5,
        width: 60, 
        borderRadius: 0,
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        width: 60, borderRadius: 0, 
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: "#FFFFFF", 
    },
    
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});