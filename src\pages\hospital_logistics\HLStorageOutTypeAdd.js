import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class HLStorageOutTypeAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            storageOutTypeId:"",
            storageOutTypeName:"",
            // storageOutTypeCode:"",
            storageOutTypeSort:0,
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageOutTypeId } = route.params;
            if (storageOutTypeId) {
                console.log("=============storageOutTypeId" + storageOutTypeId + "");
                this.setState({
                    storageOutTypeId:storageOutTypeId,
                    operate:"编辑",
                })
                let loadTypeUrl = "/biz/hl/storage/out/type/get";
                let loadRequest = { 'storageOutTypeId': storageOutTypeId };
                httpPost(loadTypeUrl, loadRequest, this.loadHLStorageOutTypeCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }

    loadHLStorageOutTypeCallBack = (response) => {
        if (response.code == 200 && response.data) {

            this.setState({
                storageOutTypeName: response.data.storageOutTypeName,
                storageOutTypeCode: response.data.storageOutTypeCode,
                storageOutTypeSort: response.data.storageOutTypeSort,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLStorageOutTypeList")
            }}>
                <Text style={CommonStyle.headRightText}>出库类型</Text>
            </TouchableOpacity>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveHLStorageOutType = () => {
        console.log("=======saveHLStorageOutType");
        let toastOpts;
        if (!this.state.storageOutTypeName) {
            toastOpts = getFailToastOpts("请填写出库类型名称");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.storageOutTypeSort) {
        //     toastOpts = getFailToastOpts("请输入排序");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url = "/biz/hl/storage/out/type/add";
        if (this.state.storageOutTypeId) {
            console.log("=========Edit===storageOutTypeId", this.state.storageOutTypeId)
            url = "/biz/hl/storage/out/type/modify";
        }

        let requestParams = {
            storageOutTypeId: this.state.storageOutTypeId,
            storageOutTypeName: this.state.storageOutTypeName,
            // storageOutTypeCode: this.state.storageOutTypeCode,
            storageOutTypeSort: this.state.storageOutTypeSort,
        };
        httpPost(url, requestParams, this.saveHLStorageOutTypeCallBack);
    }

    // 保存回调函数
    saveHLStorageOutTypeCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }


    render(){
        return(
            <ScrollView style={[CommonStyle.contentViewStyle]}>
                <CommonHeadScreen title={this.state.operate + '出库类型'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>类型名称</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput
                        //keyboardType='text'
                        style={styles.inputRightText}
                        placeholder={'请填写出库类型名称'}
                        onChangeText={(text) => this.setState({storageOutTypeName: text })}
                    >
                        {this.state.storageOutTypeName}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>
                        排序
                        </Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput 
                        keyboardType='numeric'
                        style={styles.inputRightText}
                        placeholder={'请输入排序'}
                        onChangeText={(text) => this.setState({storageOutTypeSort:text})}
                    >
                        {this.state.storageOutTypeSort}
                    </TextInput>
                </View>
                <View style={CommonStyle.btnRowStyle}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveHLStorageOutType.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row', width: 130, height: 40, marginRight: 35, marginTop: 15 }]}>
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        )
    }
}
const styles = StyleSheet.create({
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        // marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth +30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})