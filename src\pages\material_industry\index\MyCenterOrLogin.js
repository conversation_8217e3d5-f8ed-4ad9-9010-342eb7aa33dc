import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, TextInput, TouchableWithoutFeedback, Image,
    Platform, ScrollView, NativeModules, DeviceEventEmitter,StatusBar
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import NaicaiGuidBugHeadScreen from '../../../component/NaicaiGuidBugHeadScreen';
import { ifIphoneXScreenHeightMinusBottomBarHeight } from '../../../utils/ScreenUtil';
import AsyncStorage from '@react-native-community/async-storage';
import CheckBox from 'react-native-check-box'
import LinearGradinet from 'react-native-linear-gradient';
// import DeviceInfo from 'react-native-device-info';
import UserPrivacyComponent from '../../../component/UserPrivacyComponent';

// 公共组件及样式
// import EmptyListComponent from '../../component/EmptyListComponent';
// import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import CommonHeadScreen from '../../../component/CommonHeadScreen';

var CommonStyle = require('../../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
var mPush = NativeModules.MPush;
export default class MyCenterOrLogin extends Component {
    constructor() {
        super();
        this.state = {
            userCode: "",
            userPwd: "",
            aliPushInit: false,
            aliPushDeviceId: null,
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            clientInfoStorage: null,
            appFirstStartPopup: false,
            privacyAndUserAgreementChecked: false,
            tenantAbbreviation: "极致耐材",
            tenantLogo: "http://image.njjzgk.com/images/logo/jznc_app_logo.jpg",
            tenantLoginBackground: "#FFFFFF",
            loginBtnDisabled: true,
            rnDeviceId: null
        }
        // this.logout();
    }

    // 获取Ali推送标识，调用Native方法, 初始化
    getAliPushInit = (privacyAndUserAgreementChecked) => {
        if (!privacyAndUserAgreementChecked) {
            console.log("==========没有权限，privacyAndUserAgreementChecked:", privacyAndUserAgreementChecked);
            // 如果没有勾选用户，不自动加载登录名
            return null;
        }
        if (null != mPush) {
            if (Platform.OS !== 'ios') {
                // 初始化推送
                mPush.pushInit();
            }
        }
    }
    // 获取Ali推送标识，调用Native方法
    getAliPushDeviceId = (privacyAndUserAgreementChecked) => {
        if (!privacyAndUserAgreementChecked) {
            console.log("==========没有权限，privacyAndUserAgreementChecked:", privacyAndUserAgreementChecked);
            // 如果没有勾选用户，不自动加载登录名
            return null;
        }
        if (null != mPush) {
            var that = this;
            mPush.getDeviceId(function (args) {
                that.setState({
                    aliPushDeviceId: args
                });
                console.log("================getAliPushDeviceId::", args);
            });
        }
        return null;
    }

    // 获取设备标识
    getRNDeviceId = (privacyAndUserAgreementChecked) => {
        if (!privacyAndUserAgreementChecked) {
            console.log("==========没有权限，privacyAndUserAgreementChecked:", privacyAndUserAgreementChecked);
            // 如果没有勾选用户，不自动加载登录名
            return null;
        }
        const DeviceInfo = require('react-native-device-info').default;
        let rnDeviceId = DeviceInfo.getUniqueId();
        console.log("================getRNDeviceId():", rnDeviceId);
        this.setState({
            rnDeviceId: rnDeviceId
        })
        return rnDeviceId;
    }



    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        this.listener = DeviceEventEmitter.addListener('action', (appFirstStartPopup, triggerTime) => {//（）中为携带参数，可以为空
            //调接口……刷新页面
            console.log("============同步到了=XX==MyCenterOrLogin=======", appFirstStartPopup, triggerTime)
            this.setState(({
                appFirstStartPopup: appFirstStartPopup
            }))
        });
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }

        // 租户简称
        AsyncStorage.getItem("tenantAbbreviation", (err, result) => {
            if (err) {
                console.log('===AsyncStorage==getItem==tenantAbbreviation error' + err);
                return;
            }
            if (result != null) {
                // console.log('===AsyncStorage==getItem==tenantAbbreviation' + result);
                this.setState({
                    tenantAbbreviation: result,
                })
            }
            return result;
        });

        // 获取租户LOGO
        AsyncStorage.getItem("tenantExtInfo", (err, result) => {
            if (err) {
                console.log('===AsyncStorage==getItem==tenantExtInfo error' + err);
                return;
            }
            if (result != null) {
                let tenantExtInfo = JSON.parse(result);
                if (tenantExtInfo.tenantLogo) {
                    this.setState({
                        tenantLogo: tenantExtInfo.tenantLogo,
                    })
                }
                // if (tenantExtInfo.tenantLoginBackground) {
                //     this.setState({
                //         tenantLoginBackground:tenantExtInfo.tenantLoginBackground,
                //     })
                // }
            }
            return result;
        });

        AsyncStorage.getItem("clientInfoStorage", (err, result) => {
            let clientInfoStorage = JSON.parse(result);
            if (null !== clientInfoStorage) {
                this.setState({
                    clientInfoStorage: clientInfoStorage,
                })
                let appFirstStartPopup = clientInfoStorage.appFirstStartPopup === 'Y';
                let privacyAndUserAgreementChecked = clientInfoStorage.privacyAndUserAgreementChecked === 'Y';
                this.setState({
                    appFirstStartPopup: appFirstStartPopup,
                    privacyAndUserAgreementChecked: privacyAndUserAgreementChecked
                })
                let rnDeviceId = clientInfoStorage.rnDeviceId;
                // 加载上次登录密码
                if (null !== rnDeviceId && privacyAndUserAgreementChecked) {
                    rnDeviceId = this.getRNDeviceId(clientInfoStorage.privacyAndUserAgreementChecked);
                    this.loadUserCodePwd(rnDeviceId);
                }
            }
        });

        // 加载本地缓存，调ali推送的初始化或获取设置标识，不去查rnDeviceId
        this.loadOrUpdateClientInfoStorageAliPushDeviceId();

        let urlRequest = "/biz/version/get_latest_version";
        let loadRequest = { appCode: "jznc", appVersionId: config.currentVersionNaiCaiAppVersionId };
        httpPost(urlRequest, loadRequest, (response) => {
            // console.log('=====请求APP版本接口，返回：', response);
            if (response.code == 200 && response.data) {
                var latestVersion = response.data;
                if (latestVersion && latestVersion.appVersionId && latestVersion.appVersionId != config.currentVersionNaiCaiAppVersionId) {
                    Alert.alert('确认', '最新的版本号是' + latestVersion.appVersion + ",当前App版本为：" + config.currentVersionNaiCai + "，请重新下载并安装", [
                        {
                            text: "取消", onPress: () => {
                                WToast.show({ data: '点击了取消' });
                            }
                        },
                        {
                            text: "确定", onPress: () => {
                                WToast.show({ data: '点击了确定' });
                                // 下载最新的APP
                            }
                        }
                    ]);
                }
            }
        });

    }

    // 加载或更新同意隐私政策后才能存取的内容-rnDeviceId
    loadOrUpdateClientInfoStorageRNDeviceId = () => {
        // rnDeviceId相关
        AsyncStorage.getItem("clientInfoStorage", (err, result) => {
            if (err) {
                console.log('AsyncStorage.getItem error' + err);
                this.setState({
                    appFirstStartPopup: false,
                    privacyAndUserAgreementChecked: false,
                })
                return;
            }
            let clientInfoStorageNew = JSON.parse(result);
            this.setState({
                clientInfoStorage: clientInfoStorageNew
            })
            if (null != clientInfoStorageNew) {
                let appFirstStartPopup = clientInfoStorageNew.appFirstStartPopup === 'Y';
                let privacyAndUserAgreementChecked = clientInfoStorageNew.privacyAndUserAgreementChecked === 'Y';
                if (appFirstStartPopup && privacyAndUserAgreementChecked) {
                    if (null == clientInfoStorageNew.rnDeviceId) {
                        clientInfoStorageNew.rnDeviceId = this.getRNDeviceId(clientInfoStorageNew.privacyAndUserAgreementChecked);
                        // 本地储存--clientInfoStorage.rnDeviceId
                        AsyncStorage.setItem('clientInfoStorage', JSON.stringify(clientInfoStorageNew), (error) => {
                            if (error) {
                                console.log("==设置本地储存[clientInfoStorage]失败==update[rnDeviceId]", error);
                            } else {
                                console.log("==设置本地储存[clientInfoStorage]成功==update[rnDeviceId]");
                            }
                        });
                        this.setState({
                            clientInfoStorage: clientInfoStorageNew
                        })
                    }
                }
            }
            return result;
        });
    }

    // 加载或更新同意隐私政策后才能存取的内容
    loadOrUpdateClientInfoStorageAliPushDeviceId = () => {
        // 阿里推送相关
        AsyncStorage.getItem("clientInfoStorage", (err, result) => {
            if (err) {
                console.log('AsyncStorage.getItem error' + err);
                this.setState({
                    appFirstStartPopup: false,
                    privacyAndUserAgreementChecked: false,
                })
                return;
            }
            let clientInfoStorage = JSON.parse(result);
            this.setState({
                clientInfoStorage: clientInfoStorage
            })
            if (clientInfoStorage != null) {
                let appFirstStartPopup = clientInfoStorage.appFirstStartPopup === 'Y';
                let privacyAndUserAgreementChecked = clientInfoStorage.privacyAndUserAgreementChecked === 'Y';
                let aliPushInit = clientInfoStorage.aliPushInit === 'Y';
                this.setState({
                    // appFirstStartPopup: appFirstStartPopup,
                    // privacyAndUserAgreementChecked:privacyAndUserAgreementChecked,
                    aliPushInit: aliPushInit,
                })
                if (appFirstStartPopup && privacyAndUserAgreementChecked) {
                    if (!aliPushInit) {
                        this.getAliPushInit(privacyAndUserAgreementChecked);
                        clientInfoStorage.aliPushInit = 'Y';
                        // 本地储存--clientInfoStorage.aliPushInit
                        AsyncStorage.setItem('clientInfoStorage', JSON.stringify(clientInfoStorage), (error) => {
                            if (error) {
                                console.log("==设置本地储存[clientInfoStorage]失败==update[aliPushInit]", error);
                            } else {
                                console.log("==设置本地储存[clientInfoStorage]成功==update[aliPushInit]");
                            }
                        });
                        this.setState({
                            clientInfoStorage: clientInfoStorage
                        })
                    }
                    else if (null == clientInfoStorage.aliPushDeviceId) {
                        // 做不到同步，这里只是异常发起，将结果保存到this.state.aliPushDeviceId，在登录的时候录入到本地缓存中
                        this.getAliPushDeviceId(privacyAndUserAgreementChecked);
                    }
                }
            }
            return result;
        });
    }

    loadUserCodePwd = (rnDeviceId) => {
        if (null == rnDeviceId && null == this.state.rnDeviceId) {
            // 没有设备标识
            return;
        }

        let urlRequest = "/biz/user/get_latest_login_record";
        let loadRequest = {
            'deviceId': null == rnDeviceId ? this.state.rnDeviceId : rnDeviceId,
        };
        httpPost(urlRequest, loadRequest, (response) => {
            console.log('=====请求最近登录信息，返回：', response);
            if (response.code == 200 && response.data) {
                this.setState({
                    userCode: response.data.userCode,
                    userPwd: response.data.userPwd,
                    // testDouble:response.data.testDouble,
                })


                let tenantExtInfo = {
                    tenantAbbreviation: response.data.tenantAbbreviation,
                    tenantLogo: response.data.tenantLogo,
                    // tenantLoginBackground:response.data.tenantLoginBackground,
                    // tenantStyleTitle:response.data.tenantStyleTitle,
                }
                console.log('=====请求最近登录信息，tenantExtInfo：', JSON.stringify(tenantExtInfo));
                // 本地储存
                AsyncStorage.setItem('tenantExtInfo', JSON.stringify(tenantExtInfo), (error) => {
                    if (error) {
                        console.log("===AsyncStorage===设置本地储存失败==tenantExtInfo", error);
                    } else {
                        console.log("===AsyncStorage===设置本地储存成功==tenantExtInfo222", JSON.stringify(tenantExtInfo));
                        if (tenantExtInfo.tenantLogo) {
                            this.setState({
                                tenantLogo: tenantExtInfo.tenantLogo,
                            })
                        }
                        // if (tenantExtInfo.tenantLoginBackground) {
                        //     this.setState({
                        //         tenantLoginBackground:tenantExtInfo.tenantLoginBackground,
                        //     })
                        //     console.log("===tenantExtInfo.tenantLoginBackground", this.state.tenantLoginBackground);
                        // }
                    }
                });
            }
        });
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <View></View>
            // <View style={{ flexDirection: 'row', alignItems: 'center',widt:'100%'}}>
            //     <TouchableOpacity onPress={() => {  }}  style={[{marginBottom:1.5}]}>
            //         <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../../assets/icon/iconfont/back.png')}></Image>
            //     </TouchableOpacity>
            //     <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
            // </View>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
            // <View>
            //     <Text style={{ color: '#FFFFFF'}}>对称符</Text>
            // </View>
        )
    }

    logout = () => {
        let url = "/biz/user/logout?a=123&b=234"
        httpGet(url, (response) => {
            console.log("========登出结果：", response);
        });
    }

    onPressChang = () => {
        this.setState({
            imageState: !this.state.imageState,
        });
    };
    onRegister = () => {
        this.props.navigation.navigate("RegisterScreen2");
    }

    onPrivacy = () => {
        this.props.navigation.navigate("PrivacyScreen2");
    }
    onUserAgreement = () => {
        this.props.navigation.navigate("UserAgreementScreen2");
    }

    personLogin = () => {
        this.login("personLogin");
    }

    publicLogin = () => {
        this.login("publicLogin");
    }

    // 登录
    login = (type) => {
        let toastOpts;
        if (!this.state.privacyAndUserAgreementChecked) {
            toastOpts = getFailToastOpts("请勾选用户协议、隐私政策");
            WToast.show(toastOpts)
            return;
        }
        let userCode = this.state.userCode;
        let userPwd = this.state.userPwd;
        if (!userCode) {
            toastOpts = getFailToastOpts("请输入用户名");
            WToast.show(toastOpts)
            return;
        }
        if (!userPwd) {
            toastOpts = getFailToastOpts("请输入密码");
            WToast.show(toastOpts)
            return;
        }
        let clientInfoStorage = this.state.clientInfoStorage;
        if (null == clientInfoStorage.aliPushDeviceId && null !== this.state.aliPushDeviceId) {
            // 本地储存--clientInfoStorage.aliPushDeviceId
            clientInfoStorage.aliPushDeviceId = this.state.aliPushDeviceId;
            AsyncStorage.setItem('clientInfoStorage', JSON.stringify(clientInfoStorage), (error) => {
                if (error) {
                    console.log("==设置本地储存[clientInfoStorage]失败==update[aliPushDeviceId]", error);
                } else {
                    console.log("==设置本地储存[clientInfoStorage]成功==update[aliPushDeviceId]");
                }
            });
            console.log("=========login clientInfoStorage update", clientInfoStorage);
        }

        const DeviceInfo = require('react-native-device-info').default;
        let data = {
            'userCode': userCode,
            'userPwd': userPwd,
            'deviceId': this.state.rnDeviceId,
            'aliPushDeviceId': null === clientInfoStorage ? null : clientInfoStorage.aliPushDeviceId,
            'systemName': DeviceInfo.getSystemName(),
            'systemVersion': DeviceInfo.getSystemVersion(),
        };
        console.log("=========login data", data);
        let url = "/biz/user/login";
        httpPost(url, data, (response) => {
            let toastOpts;
            switch (response.code) {
                case 200:
                    constants.loginUser = response.data;
                    constants.currentTime = response.data.lastLoginTime;
                    //本地存储业务主管信息
                    if ( null != constants.loginUser.managerTenantId) {
                        AsyncStorage.setItem('managerTenantId', JSON.stringify(constants.loginUser.managerTenantId), (error) => {
                            if (error) {
                                console.log("==设置本地储存[managerTenantId]失败==update[managerTenantId]", error);
                            } else {
                                console.log("==设置本地储存[managerTenantId]成功==update[managerTenantId]");
                            }
                        });
                        console.log("=========login managerTenantId update", constants.loginUser.managerTenantId);
                    }else
                    {
                        AsyncStorage.setItem('managerTenantId',"", (error) => {
                            if (error) {
                                console.log("==设置本地储存[managerTenantId]失败==update[managerTenantId]", error);
                            } else {
                                console.log("==设置本地储存[managerTenantId]成功==update[managerTenantId]");
                            }
                        });
                        console.log("=========login managerTenantId update", constants.loginUser.managerTenantId);
                    }

                    if (isNumber(response.data.editDeleteTimeLimit)) {
                        constants.editDeleteTimeLimit = response.data.editDeleteTimeLimit;
                    }
                    if (response.data.tenantExtAttrJSON) {
                        constants.tenantExtAttrJSON = response.data.tenantExtAttrJSON;
                    }
                    // console.log(">>>>>>>success>>>constants", constants);
                    // 请求角色信息
                    // 根据权限加载菜单
                    let loadUrl = "/biz/role/get";
                    let loadRequest = {
                        'roleId': response.data.roleId,
                        'menuClass': "C"
                    };
                    httpPost(loadUrl, loadRequest, (roleResponse) => {
                        // 当前登录人的权限数据
                        if (roleResponse.code == 200) {
                            if (roleResponse.data && roleResponse.data.menuDTOList) {
                                constants.roleInfo = roleResponse.data;
                                toastOpts = getSuccessToastOpts('登录成功');
                                WToast.show(toastOpts)
                                if ("publicLogin" === type) {
                                    this.props.navigation.navigate("MainStack");
                                }
                                else if ("personLogin" === type) {
                                    this.props.navigation.replace("HomeStackScreen2",
                                        {
                                            // 传递参数
                                            productId: 1234567,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }
                            }
                            else {
                                WToast.show({ data: "登录账号所属角色没有菜单" })
                                return
                            }
                        }
                        else {
                            WToast.show({ data: "登录账号所属角色没有菜单" })
                            return
                        }
                    });

                    loadUrl = "/biz/portal/message/remind/count";
                    loadRequest = {
                        "currentPage": 1,
                        "pageSize": 1000,
                        "messageFlag": "0",
                        "messageToUserId": constants.loginUser.userId
                    };
                    httpPost(loadUrl, loadRequest, (response) => {
                        if (response.code == 200 && response.data && response.data.totalRecord) {
                            constants.noReadMessageCount = response.data.totalRecord;
                        }
                        else {
                            constants.noReadMessageCount = 0;
                        }
                    });
                    break;
                case 110000100:
                    // 首次登录
                    toastOpts = getFailToastOpts(response.message);
                    WToast.show(toastOpts)
                    this.props.navigation.navigate("ModifyPwd",
                        {
                            // 传递参数
                            userId: response.data.userId,
                            userNbr: response.data.userNbr,
                            tenantId: response.data.tenantId,
                            // 传递回调函数
                            refresh: null
                        });
                    break;
                case 110000101:
                    // 密码过期
                    toastOpts = getFailToastOpts(response.message);
                    WToast.show(toastOpts)
                    this.props.navigation.navigate("ModifyPwd",
                        {
                            // 传递参数
                            userId: response.data.userId,
                            userNbr: response.data.userNbr,
                            tenantId: response.data.tenantId,
                            // 传递回调函数
                            refresh: null
                        });
                    break;
                case 401:
                    toastOpts = getFailToastOpts(response.message);
                    // WToast.show(toastOpts)
                    WToast.show({ data: response.message })
                    // constants.loginUser = response;
                    console.log(">>>>>>>fail>>>constants.loginUser", constants.loginUser);
                    // constants.loginUser = "liminnzhi";
                    // this.props.navigation.navigate("MainPage");
                    // WToast.show({data: '已经登录过，不用重复登录'})
                    // alert("已经登录过，不用重复登录");
                    break;
                default:
                    toastOpts = getFailToastOpts(response.message);
                    WToast.show({ data: response.message })
            }
        });
    }


    render() {
        return (
            <ScrollView>
                {/* <StatusBar barStyle='light-content' /> */}
                <StatusBar
                    // animated={true} //指定状态栏的变化是否应以动画形式呈现。目前支持这几种样式：backgroundColor, barStyle和hidden
                    hidden={false}  //是否隐藏状态栏。
                    backgroundColor={'#FFFFFF'} //状态栏的背景色
                    // translucent={true}//指定状态栏是否透明。设置为true时，应用会在状态栏之下绘制（即所谓“沉浸式”——被状态栏遮住一部分）。常和带有半透明背景色的状态栏搭配使用。
                    barStyle={'dark-content'} // enum('default', 'light-content', 'dark-content')
                >
                </StatusBar>
                <NaicaiGuidBugHeadScreen title='极致耐材' titleStyle={{ fontWeight: 500,fontSize: '17px',
                                                            lineHeight: '24px',fontStyle: 'normal',}}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{ height: 1, backgroundColor: 'rgba(0, 0, 0, 0.1)', marginTop: -4}} />
                {this.state.appFirstStartPopup ? null : <UserPrivacyComponent />}
                <View style={[CommonStyle.contentViewStyle, styles.container, { backgroundColor: this.state.tenantLoginBackground, height: ifIphoneXScreenHeightMinusBottomBarHeight() }]}>
                    <View style={styles.center}>
                        <Image style={styles.logo} source={{ uri: this.state.tenantLogo }} />
                        <View style={{ alignContent: 'center', marginBottom: 20 }}>
                            <Text style={{ fontSize: 30, color: '#333333', fontWeight: 'bold' }}>{this.state.tenantAbbreviation}</Text>
                        </View>
                        <View style={{ height: 45, flexDirection: 'row', justifyContent: 'space-between', margin: 10, borderColor: '#33333365', borderWidth: 1, borderRadius: 22,}}>
                            <View style={{ width: 35, height: 40, justifyContent: 'center', alignItems: 'flex-end' }}>
                                <Image style={{width: 16, height: 16 }} 
                                    source={require('../../../assets/icon/iconfont/user1.png')}/>
                                {/* <Text style={pageStyle.textStyle}>用户名</Text> */}
                            </View>
                            <TextInput
                                placeholder={'请输入用户名'}
                                editable={true}//是否可编辑
                                onChangeText={(text) => this.setState({ userCode: text })}
                                style={pageStyle.textInfoStyle}>
                                {this.state.userCode}
                            </TextInput>
                        </View>
                        <View style={{ height: 45, flexDirection: 'row', justifyContent: 'space-between', margin: 10, marginBottom:20, borderColor: '#33333365', borderWidth: 1, borderRadius: 22,}}>
                            <View style={{ width: 35, height: 40, justifyContent: 'center', alignItems: 'flex-end' }}>
                            <Image style={{width: 16, height: 16, tintColor: 'rgba(0, 0, 0, 0.5)' }} 
                                    source={require('../../../assets/icon/iconfont/unlock.png')}/>
                                {/* <Text style={pageStyle.textStyle}>密    码</Text> */}
                            </View>
                            <TextInput
                                placeholder={'请输入密码'}
                                secureTextEntry={!this.state.imageState}//是否隐藏
                                editable={true}//是否可编辑
                                onChangeText={(text) => this.setState({ userPwd: text })}
                                style={pageStyle.textInfoStyle}>
                                {this.state.userPwd}
                            </TextInput>
                            <TouchableWithoutFeedback style={{ marginRight: 10 }} onPress={this.onPressChang}>
                                {this.state.imageState ? (
                                    <Image style={{ width: 21, height: 14, alignSelf: 'center', marginRight: 10, }}
                                        source={require('../../../assets/icon/password_show1.png')}
                                    />) : (<Image style={{ width: 20, height: 8, alignSelf: 'center', marginRight: 10, }}
                                        source={require('../../../assets/icon/password_hide.png')}
                                    />)}
                            </TouchableWithoutFeedback>
                        </View>

                        {/* 登录appFirstStartPopup */}
                        <TouchableOpacity
                            onPress={() => {
                                if (!this.state.appFirstStartPopup) {
                                    WToast.show({ data: '请先勾选隐私政策和用户协议' })
                                    return;
                                };
                                this.personLogin()
                            }}
                        >
                            <View style={[styles.loginBtnStyle, this.state.loginBtnDisabled && this.state.appFirstStartPopup ? null : CommonStyle.disableViewStyle]}>
                                <Text style={{ color: '#F0F0F0', fontSize: 20, fontWeight: 'bold' }}>个人登录</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                            // onPress={this.login("MainStack")}
                            onPress={() => {
                                if (!this.state.appFirstStartPopup) {
                                    WToast.show({ data: '请先勾选隐私政策和用户协议' })
                                    return;
                                };
                                this.publicLogin()
                            }
                            }
                        >
                            <View style={[styles.loginBtnStyle, this.state.loginBtnDisabled && this.state.appFirstStartPopup ? null : CommonStyle.disableViewStyle]}>
                                <Text style={{ color: '#F0F0F0', fontSize: 20, fontWeight: 'bold' }}>企业登录</Text>
                            </View>
                        </TouchableOpacity>
                        {/* <View style={styles.settingStyle}>
                                <Text></Text>
                                <TouchableOpacity onPress={this.onRegister.bind(this)}>
                                    <View style={{borderBottomColor:'#33333365', borderBottomWidth:1, paddingBottom:2}}>
                                        <Text style={{color:'#333333', fontSize:15}}>没有账号，去注册</Text>
                                    </View>
                                </TouchableOpacity>
                            </View> */}

                    </View>
                    <View style={[styles.otherLoginStyle, { flexDirection: 'column' }]}>
                        <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center',marginTop:10 }}>
                            <CheckBox
                                style={{ flex: 1, padding: 25, }}
                                onClick={() => {
                                    if (!this.state.appFirstStartPopup) {
                                        let toastOpts = getFailToastOpts("同意提示后才可勾选");
                                        WToast.show(toastOpts)
                                        return;
                                    }
                                    var privacyAndUserAgreementChecked = !this.state.privacyAndUserAgreementChecked;
                                    this.setState({
                                        privacyAndUserAgreementChecked: privacyAndUserAgreementChecked
                                    })
                                    // privacyAndUserAgreementChecked相关
                                    AsyncStorage.getItem("clientInfoStorage", (err, result) => {
                                        if (err) {
                                            console.log('AsyncStorage.getItem error' + err);
                                            return;
                                        }
                                        let clientInfoStorageNew = JSON.parse(result);
                                        if (clientInfoStorageNew != null) {
                                            clientInfoStorageNew.privacyAndUserAgreementChecked = privacyAndUserAgreementChecked ? "Y" : "N";
                                            // 本地储存--clientInfoStorage.privacyAndUserAgreementChecked
                                            AsyncStorage.setItem('clientInfoStorage', JSON.stringify(clientInfoStorageNew), (error) => {
                                                if (error) {
                                                    console.log("==设置本地储存[clientInfoStorage]失败==update[privacyAndUserAgreementChecked]", error);
                                                } else {
                                                    console.log("==设置本地储存[clientInfoStorage]成功==update[privacyAndUserAgreementChecked]", clientInfoStorageNew);
                                                    this.loadOrUpdateClientInfoStorageRNDeviceId();
                                                    this.loadOrUpdateClientInfoStorageAliPushDeviceId();
                                                }
                                            });
                                            this.setState({
                                                clientInfoStorage: clientInfoStorageNew
                                            })
                                        }
                                        return result;
                                    });
                                }}
                                isChecked={this.state.privacyAndUserAgreementChecked ? true : false}
                                // leftText={"CheckBox"}
                                checkedImage={<Image source={require('../../../assets/icon/iconfont/checkboxBlack.png')} style={{ tintColor: 'rgba(163, 163, 163, 1)', width: 18, height: 18 }} />}
                                unCheckedImage={<Image source={require('../../../assets/icon/iconfont/uncheckboxBlack.png')} style={{ tintColor: 'rgba(163, 163, 163, 1)', width: 18, height: 18 }} />}
                            />
                            <Text style={[styles.otherTextStyle]}>我已阅读并同意</Text>
                            <TouchableOpacity onPress={this.onUserAgreement.bind(this)}>
                                <Text style={[styles.anotherTextStyle]}>《用户协议》</Text>
                            </TouchableOpacity>
                            <Text style={styles.otherTextStyle}>与</Text>
                            <TouchableOpacity onPress={this.onPrivacy.bind(this)}>
                                <Text style={[styles.anotherTextStyle]}>《隐私政策》</Text>
                            </TouchableOpacity>
                        </View>
                        <View style={[{ flexDirection: 'row',justifyContent: 'center', }]}>
                            <Text style={{ color: 'rgba(163, 163, 163, 1)' }}>版本 Version：{config.currentVersionNaiCai}</Text>
                        </View>
                    </View>
                </View>
            </ScrollView>
        )
    }
}
const styles = StyleSheet.create({

    container: {
        width: screenWidth,
        height: screenHeight,
        // flexDirection: 'row',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
    },
    center: {
        width: screenWidth * 0.8,
        // position:'absolute',
        alignItems: 'center',
        marginTop: -120,
        justifyContent: 'center',
    },
    logo: {
        width: 100,
        height: 100,
        borderRadius: 50,
        // backgroundColor:'red',
        // marginTop:80,
        marginBottom: 15,
    },
    textIntputStyle: {
        height: 38,
        width: 350,
        backgroundColor: '#303F58',
        marginBottom: 1,
        // 内容居中
        textAlign: "center"

    },
    loginBtnStyle: {
        width: screenWidth * 0.76,
        height: 45,
        backgroundColor: 'rgba(37, 91, 218, 1)',
        marginTop: 20,
        marginLeft: 10,
        marginRight: 10,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 22
    },
    settingStyle: {
        // 设置主轴的方向
        flexDirection: 'row',
        // 主轴的对齐方式
        justifyContent: 'space-between',
        width: screenWidth * 0.76,
        marginTop: 20,
    },

    otherLoginStyle: {
        marginTop:5,
        marginLeft:-15,
        // position: 'absolute',
        // zIndex: 99999,
        // justifyContent: 'center',
        // alignItems: 'center',
        // bottom: 145,
        // marginTop: -250,
    },
    otherImageStyle: {
        width: 50,
        height: 50,
        borderRadius: 25,
        marginLeft: 5,
        backgroundColor: '#F5F5F5'
    },
    otherTextStyle: {
        alignItems: 'center',
        color: 'rgba(163, 163, 163, 1)',
        fontFamily: 'PingFangSC-Regular',
        fontWeight: 'normal',
        textAlign: 'left',
        lineHeight: 17,
    },
    anotherTextStyle: {
        alignItems: 'center',
        color: 'rgba(37, 91, 218, 1)',
        // fontSize: 12,
        fontFamily: 'PingFangSC-Regular',
        fontWeight: 'normal',
        textAlign: 'left',
        lineHeight: 17,
    }

});

const pageStyle = StyleSheet.create({
    textInfoStyle: {
        alignSelf: 'center',
        marginLeft: 10,
        color: '#33333395',
        fontSize: 16,
        flex: 1,
    },
    textStyle: {
        //   alignSelf: 'center',
        marginLeft: 10,
        color: '#33333395',
        fontSize: 16,
        fontWeight: 'bold'

    },
});
