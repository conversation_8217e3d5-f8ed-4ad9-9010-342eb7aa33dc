import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,TextInput,Image,Modal
} from 'react-native';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { uploadMultiImageLibrary } from '../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class HLMedicineStorageInAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            storageInId:"",
            //backPeople:"",
            //storageInFlag:"",
            //sourceId:"",
            auditState:"",
            storageInState:"",
            //供应商系列变量
            selSupplierId:"",
            selSupplierName:"",
            selSupplierAbbreviation:"",
            supplierId:"",
            supplierName:"",
            supplierAbbreviation:"",

            supplierDataSource:[],
            _supplierDataSource:[],
            //入库时间
            storageInDate:"",
            selectStorageInDate:[],

            operator:constants.loginUser.userName,
            receiptRemark:"",
            bookKeeper:"",
            bookKeepingDate:"",
            auditDate:"",
            // selectStorageInTime:[],
            selectBookKeepingTime:[],
            
            selectAuditTime:[],
            reviewerName:"",
            reviewerId:"",
            modal: false,
            searchKeyWord: null,
            hlMedicineStorageInDetailDTOList:[],
            //药房
            selPharmacyId:"",
            selPharmacyName:"",
            pharmacyAddr:"",
            selectPharmacyName:[],
            pharmacyDataSource:[], 

            //院区
            selHospitalId:"",
            selHospitalName:"",
            selectHospitalName:[],
            hospitalDataSource:[], 
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        //加载审核人
        this.loadreviewer();
        // 加载供应商列表
        this.loadSupplierData();
        //加载院区列表
        this.loadHospitalData();
        //加载药房列表
        // this.loadPharmacyData();
        //加载院区列表

        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageInId } = route.params;
            if (storageInId) {
                console.log("=============courseId" + storageInId + "");
                this.setState({
                    storageInId:storageInId,
                    operate:"编辑"
                });
                let loadTypeUrl= "/biz/hl/medicine/storage/in/get";
                let loadRequest={'storageInId':storageInId};
                httpPost(loadTypeUrl, loadRequest, this.loadHlMedicineStorageInDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                });
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectStorageInDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    storageInDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                    selectBookKeepingTime:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    bookKeepingDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                    selectAuditTime:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    auditDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
    }
    loadHlMedicineStorageInDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            if(response.data.storageInDate!=null){
                var selectStorageInDate = response.data.storageInDate.split("-");
            }
            if(response.data.bookKeepingDate!=null){
                var selectBookKeepingTime = response.data.bookKeepingDate.split("-");
            }
            this.setState({
                // storageInId:response.data.storageInId,
                selSupplierId:response.data.supplierId,
                selSupplierName:response.data.supplierName,
                //backPeople:response.data.backPeople,
                storageInDate:response.data.storageInDate,
                operator:response.data.operator,
                receiptRemark:response.data.receiptRemark,
                auditState:response.data.auditState,
                auditDate:response.data.auditDate,
                currentAuditUserId:response.data.currentAuditUserId,
                bookKeeper:response.data.bookKeeper,
                bookKeepingDate:response.data.bookKeepingDate,
                storageInState:response.data.storageInState,
                //storageOutFlag:response.data.storageOutFlag,
                //sourceId:response.data.sourceId,               
                selectStorageInDate:selectStorageInDate,
                selectBookKeepingTime:selectBookKeepingTime,

                selPharmacyId:response.data.pharmacyId,
                selPharmacyName:response.data.pharmacyName,
                selectPharmacyName:[response.data.pharmacyName],
                pharmacyAddr:response.data.pharmacyAddr,
                //dselectPharmacy:response.data.pharmacyName,
                selHospitalId:response.data.hospitalId,
                hospitalName:response.data.hospitalName,
                selHospitalName:response.data.hospitalName,
                selectHospitalName:[response.data.hospitalName]
            })
                //默认时间？
            // if (response.data.expectDeliveryTime) {
            //     var selectExpectDeliveryTime = response.data.expectDeliveryTime.split("-");
            //     this.setState({
            //         expectDeliveryTime:response.data.expectDeliveryTime,
            //         selectExpectDeliveryTime:selectExpectDeliveryTime,
            //     })
            // }

            if (response.data.hlMedicineStorageInDetailDTOList && response.data.hlMedicineStorageInDetailDTOList.length > 0) {
                this.setState({
                    // 物资入库详细
                    hlMedicineStorageInDetailDTOList:response.data.hlMedicineStorageInDetailDTOList,
                })
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    loadreviewer = ()=>{
        let loadTypeUrl= "/biz/hl/medicine/storage/in/reviewer";
        let loadRequest={
            "operaterId": constants.loginUser.userId
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    reviewerName:response.data.userName,
                    reviewerId:response.data.userId,
                })
            }
        });
    }

    loadHospitalData=()=>{
        let url= "/biz/hl/hospital/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    hospitalDataSource: response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    openHospitalName() {
        if (!this.state.hospitalDataSource || this.state.hospitalDataSource.length < 1) {
            WToast.show({ data: "请先添加院区" });
            return
        }
        this.refs.SelectHospitalName.showHospital(this.state.selectHospitalName, this.state.hospitalDataSource)
    }

    callBackSelectHospitalValue(value) {
        console.log("==========院区选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selPharmacyName:"",
            pharmacyAddr:"",
            // selectPharmacyName:"",
            selectHospitalName: value,
            selHospitalName:value.toString(),
    
        })
        var hospitalName = value.toString();
        let loadUrl = "/biz/hl/hospital/getHospitalByName";
        let loadRequest = {
            "hospitalName": hospitalName
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    selHospitalName:response.data.hospitalName,
                    selHospitalId:response.data.hospitalId,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({data:response.message});
            }

            //记载药房列表
            this.loadPharmacyData(response.data.hospitalId)

        }

        );
    }

    loadPharmacyData=(hospitalId)=>{
        let url= "/biz/hl/pharmacy/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            "hospitalId":hospitalId
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    pharmacyDataSource: response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    openPharmacyName() {
        if (!this.state.selHospitalId){
            WToast.show({ data: "请先选择院区" });
            return
        }
        if (!this.state.pharmacyDataSource || this.state.pharmacyDataSource.length < 1) {
            WToast.show({ data: "请先添加药房" });
            return
        }
        this.refs.SelectPharmacyName.showPharmacy(this.state.selectPharmacyName, this.state.pharmacyDataSource)
    }

    callBackSelectPharmacyValue(value) {
        console.log("==========药房选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectPharmacyName: value,
            selPharmacyName:value.toString()
        })
        var pharmacyName = value.toString();
        let loadUrl = "/biz/hl/pharmacy/getPharmacyByName";
        let loadRequest = {
            "pharmacyName": pharmacyName,
            "hospitalId":this.state.selHospitalId,
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    selPharmacyName:response.data.pharmacyName,
                    selPharmacyId:response.data.pharmacyId,
                    selHospitalId:response.data.hospitalId,
                    pharmacyAddr:response.data.pharmacyAddr,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({data:response.message});
            }
        });
    }

    //加载供应商
    loadSupplierData=()=>{
        let loadUrl= "/biz/portal/supplier/list";
        let loadRequest={'currentPage':1,'pageSize':100,'supplierType':"M"};//供应商类型存疑
        httpPost(loadUrl, loadRequest, this.callBackLoadSupplierData);
    }

    callBackLoadSupplierData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                supplierDataSource:response.data.dataList,
                supplierId:response.data.supplierId,
            })
            // this.loadContractList(this.state.selCustomerId);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    //筛选供应商
    loadSupplier = () => {
        var _supplierDataSource = copyArr(this.state.supplierDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _supplierDataSource = _supplierDataSource.filter(item => item.supplierName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _supplierDataSource: _supplierDataSource,
        })
    }
    renderSupplierRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                
                this.setState({
                    selSupplierId: item.supplierId,
                    selSupplierName: item.supplierName,
                    
                })
                
            }}>
                <View key={item.supplierId} style={[item.supplierId === this.state.selSupplierId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.supplierId === this.state.selSupplierId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.supplierName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    loadProtalSupplierListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                supplierDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} >
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("HLMedicineStorageInList") 
            }}>
                <Text style={CommonStyle.headRightText}>药品入库</Text>
            </TouchableOpacity>
        )
    }
    //入库日期渲染
    openStorageInTime(){
        this.refs.SelectStorageInTime.showDate(this.state.selectStorageInDate)
    }
    //入库日期
    callBackSelectStorageInTimeValue(value){
        console.log("==========提交入库日期时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectStorageInDate:value
        })
        if (value && value.length) {
            var storageInDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    storageInDate += vartime;
                }
                else{
                    storageInDate += "-" + vartime;
                }
            }
            this.setState({
                storageInDate:storageInDate
            })
        }
    }

    //记账日期渲染
    openBookKeepingTime(){
        this.refs.SelectBookKeepingTime.showDate(this.state.selectBookKeepingTime)
    }
    //记账日期
    callBackSelectBookKeepingTimeValue(value){
        console.log("==========提交入库日期时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectBookKeepingTime:value
        })
        if (value && value.length) {
            var bookKeepingDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    bookKeepingDate += vartime;
                }
                else{
                    bookKeepingDate += "-" + vartime;
                }
            }
            this.setState({
                bookKeepingDate:bookKeepingDate
            })
        }
    }

    //审核日期渲染
    openAuditTime(){
        this.refs.SelectAuditTime.showDate(this.state.selectAuditTime)
    }
    //审核日期
    callBackSelectAuditTimeValue(value){
        console.log("==========提交入库日期时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectAuditTime:value
        })
        if (value && value.length) {
            var auditDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    auditDate += vartime;
                }
                else{
                    auditDate += "-" + vartime;
                }
            }
            this.setState({
                auditDate:auditDate
            })
        }
    }
    //保存
    saveHLMedicineStorageIn =()=> {
        console.log("=======saveHLStorageIn");
        let toastOpts;
        if (!this.state.selSupplierId) {
            toastOpts = getFailToastOpts("请选择供货单位");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selHospitalId) {
            toastOpts = getFailToastOpts("请选择院区");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selPharmacyId) {
            toastOpts = getFailToastOpts("请选择药房");
            WToast.show(toastOpts)
            return;
        }

        if (!this.state.operator) {
            toastOpts = getFailToastOpts("请输入经办人");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.bookKeeper) {
        //     toastOpts = getFailToastOpts("请输入记账人");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.hlMedicineStorageInDetailDTOList || this.state.hlMedicineStorageInDetailDTOList.length < 1) {
            toastOpts = getFailToastOpts("至少新增一条明细");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/hl/medicine/storage/in/add";
        if (this.state.storageInId) {
            console.log("=========Edit===storageInId", this.state.storageInId)
            url= "/biz/hl/medicine/storage/in/modify";
        }
        let requestParams={
            "supplierId" : this.state.selSupplierId,
            "storageInDate": this.state.storageInDate,
            "receiptRemark": this.state.receiptRemark,
            "auditDate": this.state.auditDate,
            "bookKeeper": this.state.bookKeeper,
            "bookKeepingDate": this.state.bookKeepingDate,
            //"storageOutFlag": "M",
            // "operator": this.state.operator,
            "operator": this.state.operator,
            //"operatorId":this.state.userId,
            "currentAuditUserId": this.state.reviewerId,
            //"sourceId":1,
            "hlMedicineStorageInDetailDTOList":this.state.hlMedicineStorageInDetailDTOList,
            "storageInId":this.state.storageInId,
            "selSupplierId":this.state.selSupplierId,
            "hospitalId":this.state.selHospitalId,
            "pharmacyId":this.state.selPharmacyId
        };
        httpPost(url, requestParams, this.saveHLMedicineStorageInMCallBack);

    }
    // 保存回调函数
    saveHLMedicineStorageInMCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    _loadFreshHlMedicineStorageInDetailDTOList=(hlMedicineStorageInDetailDTOList)=>{
        if (hlMedicineStorageInDetailDTOList && hlMedicineStorageInDetailDTOList.length > 0) {
            console.log("=========回退数据：", hlMedicineStorageInDetailDTOList);
            this.setState({
                hlMedicineStorageInDetailDTOList:hlMedicineStorageInDetailDTOList,
        })
        }
        else {
            console.log("=========回退不成功");
        }
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '入库'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={CommonStyle.rowLabView}>
                        <Text style={CommonStyle.rowLabTextStyle}>供货单位</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <View style={[{flexWrap:'wrap'}, (!this.state.supplierDataSource || this.state.supplierDataSource.length === 0) ? [CommonStyle.disableViewStyle,{height:40}] : null]}>
                        <TouchableOpacity onPress={() => {
                            if (this.state.supplierDataSource && this.state.supplierDataSource.length > 0) {
                                this.setState({
                                    _supplierDataSource: copyArr(this.state.supplierDataSource),
                                })
                            }
                            this.setState({
                                modal: true,
                                searchKeyWord: ""
                            })
                            if (!this.state.selSupplierId && this.state.supplierDataSource && this.state.supplierDataSource.length > 0) {
                                this.setState({
                                    selSupplierId: this.state.supplierDataSource[0].supplierId,
                                    selSupplierName: this.state.supplierDataSource[0].supplierName,
                                    // selSupplierAbbreviation: this.state.supplierDataSource[0].supplierAbbreviation,
                                    // contractId:"",
                                    // contractName:"",
                                    // selectContract:[],
                                })
                            }
                        }}>
                            <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, 
                                {paddingLeft:5,paddingRight:5,height:40, flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)',marginLeft:10}]}>
                                {this.state.selSupplierId && this.state.selSupplierName ? 
                                    <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                    {this.state.selSupplierName}
                                    </Text>
                                    :
                                    <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                    选择供货单位
                                    </Text>
                                }
                            </View>
                        </TouchableOpacity>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <View style={styles.leftLabViewImage}>
                                        <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                        <TextInput
                                            style={[styles.searchInputText]}
                                            returnKeyType="search"
                                            returnKeyLabel="搜索"
                                            onSubmitEditing={e => {
                                                this.loadSupplier();
                                        }}        
                                            placeholder={'供货单位'}
                                            onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                        >
                                            {this.state.searchKeyWord}
                                        </TextInput>
                                    </View>
                                    {/* <TouchableOpacity onPress={() => {
                                        this.loadSupplier();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity> */}
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._supplierDataSource && this.state._supplierDataSource.length > 0)
                                                ?
                                                this.state._supplierDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderSupplierRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selSupplierId) {
                                            let toastOpts = getFailToastOpts("您还没有选择供货单位");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        let loadUrl = "/biz/protal/supplier/list";
                                        let loadRequest = {
                                            "currentPage":1,
                                            "pageSize":1000,
                                            // 此处可能存在问题 yty
                                            "supplieId":this.state.selSupplierId,
                                            // "partyA": this.state.selCustomerId,
                                        };
                                        httpPost(loadUrl, loadRequest, this.loadProtalSupplierListCallBack);
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    {/* <View style={CommonStyle.rowSplitViewStyle}></View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                            入库日期
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                     
                        <TouchableOpacity onPress={()=>this.openStorageInTime()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 30)}]}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.storageInDate ? "请选择入库日期" : this.state.storageInDate}
                            </Text>
                        </View>
                        </TouchableOpacity>
                    </View>
                    {/* <View style={CommonStyle.rowSplitViewStyle}></View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                经办人
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            // editable={flase}
                            placeholder={'请输入经办人'}
                            onChangeText={(text) => this.setState({operator:text})}
                            style={[styles.inputRightText]}>
                            {this.state.operator}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                单据备注
                            </Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        {/* <TextInput 
                            
                            placeholder={'请输入单据备注'}
                            onChangeText={(text) => this.setState({receiptRemark:text})}
                            style={[styles.inputRightText]}>
                            {this.state.receiptRemark}
                        </TextInput> */}
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            placeholder={'请输入单据备注'}
                            onChangeText={(text) => this.setState({receiptRemark:text})}
                            style={[CommonStyle.inputRowText,{height:100,width:screenWidth - (leftLabWidth/3)}]}
                        >
                            {this.state.receiptRemark}
                        </TextInput>
                    </View>
                    
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                            审核日期
                            </Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>                     
                        <TouchableOpacity onPress={()=>this.openAuditTime()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle]}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.auditDate ? "请选择审核日期" : this.state.auditDate}
                            </Text>
                        </View>
                        </TouchableOpacity>
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                                记账人
                            </Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            placeholder={'请输入记账人'}
                            onChangeText={(text) => this.setState({bookKeeper:text})}
                            style={[styles.inputRightText]}>
                            {this.state.bookKeeper}
                        </TextInput>
                    </View>
                    {/* <View style={CommonStyle.rowSplitViewStyle}></View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                            记账日期
                            </Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>                     
                        <TouchableOpacity onPress={()=>this.openBookKeepingTime()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.bookKeepingDate ? "请选择记账日期" : this.state.bookKeepingDate}
                            </Text>
                        </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>审核人</Text>
                        </View>
                        <TextInput 
                            editable={false}
                            style={[styles.inputRightText]}
                            placeholder={'请输入审核人'}
                            onChangeText={(text) => this.setState({reviewerName:text})}
                        >
                            {this.state.reviewerName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>院区名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openHospitalName()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 30)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.selHospitalName ? "请选择院区" : this.state.selHospitalName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>药房名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openPharmacyName()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.selHospitalName ? "请选择院区" :(!this.state.selPharmacyName ? "请选择药房" : this.state.selPharmacyName) }
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>药房位置</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 30)}]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {this.state.pharmacyAddr ? this.state.pharmacyAddr : "请选择药房"}
                            </Text>
                        </View>
                    </View>
                    <View style={CommonStyle.rowSplitViewStyle}></View>
                    <View style={styles.btnRowView}>
                    <TouchableOpacity onPress={()=>{
                        if (!this.state.selSupplierId) {
                            let toastOpts = getFailToastOpts("请您先选择供货单位");
                            WToast.show(toastOpts);
                            return;
                        }
                        if (!this.state.selHospitalId) {
                            let toastOpts = getFailToastOpts("请您先选择院区");
                            WToast.show(toastOpts);
                            return;
                        }
                        if (!this.state.selPharmacyId) {
                            let toastOpts = getFailToastOpts("请您先选择药房");
                            WToast.show(toastOpts);
                            return;
                        }
                            this.props.navigation.navigate("HLMedicineStorageInDetailAdd", 
                        {
                            storageInId:this.state.storageInId,
                            pharmacyId:this.state.selPharmacyId,
                            hospitalId:this.state.selHospitalId,
                            hlMedicineStorageInDetailDTOList:this.state.hlMedicineStorageInDetailDTOList,
                            // 传递回调函数
                            refresh: this._loadFreshHlMedicineStorageInDetailDTOList 
                        })
                    }}>
                         <View style={[styles.btnAddView]}>
                             <Text style={styles.btnAddText}>+ 入库明细</Text>
                         </View>
                    </TouchableOpacity>
                </View>
                    <View>
                        <FlatList 
                        data={this.state.hlMedicineStorageInDetailDTOList}
                        renderItem={({item}) => 
                        <View key={item._index} style={styles.titleViewStyle}>
                           
                            <View style={{ }}>
                                <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap",marginTop:10}]}>
                                    名称：{item.medicineName}
                                </Text>
                            </View>

                            <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10,marginTop:10}]}>
                                <View style={[styles.itemContentChildViewStyle]}>
                                    <Text style={styles.titleTextStyle}>规格型号：{item.medicineSpec}</Text>
                                </View>
                                <View style={[styles.itemContentChildViewStyle]}>
                                    <Text style={styles.titleTextStyle}>单位：{item.unitName}</Text>
                                </View>
                                <View style={[styles.itemContentChildViewStyle]}>
                                    <Text style={styles.titleTextStyle}>数量：{item.amount}</Text>
                                </View>
                                <View style={[styles.itemContentChildViewStyle]}>
                                    <Text style={styles.titleTextStyle}>价格：{item.guidingPrice}</Text>
                                </View>
                                <View style={[styles.itemContentChildViewStyle]}>
                                    <Text style={[styles.titleTextStyle]}>零售价格：{item.salePrice}</Text>
                                </View>
                            </View>

                            <TouchableOpacity
                            style={{marginLeft:-145,zIndex:1000,marginTop:130}}
                            onPress={() => {
                                console.log("========deleteStorageInDetailDTO")
                                var urls = this.state.hlMedicineStorageInDetailDTOList;
                                urls.splice(item._index,1);                   
                                console.log(urls)
                                this.setState({
                                    hlMedicineStorageInDetailDTOList:urls,
                                })
                            }}
                        >
                            <View style={styles.btnDeleteView}>
                                <Text style={styles.btnDeleteText}>-删除</Text>
                            </View>
                        </TouchableOpacity>

                        </View>
                        }
                        />
                    </View>

                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{flexDirection:'row',width:130,height:40,marginLeft:35,marginTop:15}]} >
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveHLMedicineStorageIn.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row',width:130,height:40,marginRight:35,marginTop:15}]}>
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    
                </ScrollView>
                <BottomScrollSelect 
                    ref={'SelectStorageInTime'} 
                    callBackDateValue={this.callBackSelectStorageInTimeValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectBookKeepingTime'} 
                    callBackDateValue={this.callBackSelectBookKeepingTimeValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectAuditTime'} 
                    callBackDateValue={this.callBackSelectAuditTimeValue.bind(this)}
                />
               <BottomScrollSelect 
                    ref={'SelectPharmacyName'} 
                    callBackPharmacyValue={this.callBackSelectPharmacyValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectHospitalName'} 
                    callBackHospitalValue={this.callBackSelectHospitalValue.bind(this)}
                />                
            </View>
        )
    }
}
const styles = StyleSheet.create({
    searchInputText: {
        width: screenWidth -100,
        // borderColor: '#000000',
        // borderBottomWidth: 1,
        // marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        // marginLeft: 0,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabViewImage: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        borderWidth:1,
        borderColor:"#E4E4E4",
        borderRadius:5,
        marginTop:5,
        // marginRight:5
    },  
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },

});