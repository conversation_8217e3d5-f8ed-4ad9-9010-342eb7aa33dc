import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class CheckClassifyMaterialInventoryList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      parentClassifyId: null,
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      selClassifyId: null,
      materialLocationList: [],
      locationTotalRecord: 0,
      topBlockLayoutHeight: 0,
      selLocationId: 0,
      selLocationName: '全部',
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {parentClassifyId, classifyType, locationId, locationName} =
        route.params;
      if (parentClassifyId) {
        this.setState({
          parentClassifyId: parentClassifyId,
        });
      }
      if (locationId) {
        this.setState({
          selLocationId: locationId,
        });
      }
      if (locationName) {
        this.setState({
          selLocationName: locationName,
        });
      }
      console.log('parentClassifyId', parentClassifyId);
      this.loadMaterialBigClassifyList(parentClassifyId);
      this.loadMaterialLocationList();
    } else {
      this.loadMaterialBigClassifyList();
    }
  }

  loadMaterialLocationList = () => {
    let url = '/biz/storage/location/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: 1000,
      locationType: 'M',
    };
    httpPost(url, loadRequest, this.loadMaterialLocationListCallBack);
  };

  loadMaterialLocationListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataSource = response.data.dataList;
      dataSource.unshift({
        locationName: '全部',
        locationId: 0,
        locationType: 'M',
        locationSort: 0,
      });
      this.setState({
        materialLocationList: dataSource,
        locationTotalRecord: response.data.totalRecord,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/material/classify/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      parentClassifyId: this.state.parentClassifyId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/material/classify/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      parentClassifyId: this.state.parentClassifyId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadMaterialBigClassifyList();
  };

  loadMaterialBigClassifyList = (parentClassifyId) => {
    let loadUrl = '/biz/material/classify/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      parentClassifyId: parentClassifyId
        ? parentClassifyId
        : this.state.parentClassifyId,
    };
    httpPost(loadUrl, loadRequest, this.loadMaterialBigClassifyListCallBack);
  };

  loadMaterialBigClassifyListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteMaterialBigClassify = (classifyId) => {
    console.log('=======delete=classifyId', classifyId);
    let url = '/biz/material/classify/delete';
    let requestParams = {classifyId: classifyId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selClassifyId: item.classifyId,
          });
          if (!item.parentClassifyId) {
            // 类别选择原料名称
            this.props.navigation.push('CheckClassifyMaterialInventoryList', {
              // 传递参数
              parentClassifyId: item.classifyId,
              locationName: this.state.selLocationName,
              locationId: this.state.selLocationId,
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          } else {
            // 原料名称选择好进入入库页面
            this.props.navigation.navigate('MaterialInventoryList', {
              // 传递参数
              classifyId: item.classifyId,
              classifyName: item.classifyName,
              locationName: this.state.selLocationName,
              locationId: this.state.selLocationId,
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }
        }}>
        <View
          key={item.classifyId}
          style={[
            styles.innerViewStyle,
            this.state.selClassifyId == item.classifyId
              ? {
                  backgroundColor: 'rgba(255,0,0,0.4)',
                  borderRadius: 20,
                  hight: 80,
                }
              : {},
          ]}>
          {index == 0 ? (
            <View style={CommonStyle.lineListHeadRenderRowStyle}></View>
          ) : (
            <View></View>
          )}
          <View style={CommonStyle.titleViewStyleSpecial}>
            <Text style={CommonStyle.titleTextStyleSpecial}>
              编号：{index + 1}
            </Text>
          </View>
          <View style={CommonStyle.titleViewStyle}>
            <Text style={CommonStyle.titleTextStyle}>
              {this.state.parentClassifyId ? '原料名称' : '原料类别'}：
              {item.classifyName}
            </Text>
          </View>

          <View
            style={{
              width: 40,
              height: 40,
              marginBottom: 10,
              backgroundColor: 'rgba(255,0,0,0.0)',
              position: 'absolute',
              alignItems: 'center',
              justifyContent: 'center',
              right: 20,
              bottom: 0,
            }}>
            <Image
              style={{width: 22, height: 22}}
              source={require('../../assets/icon/iconfont/enter4.png')}></Image>
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      //     <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/back.png')}></Image>
      // </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[{flexDirection: 'row', alignItems: 'center'}]}>
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={{color: '#3C6CDE', fontWeight: 'bold'}}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity onPress={() => {}}>
          {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
          <Text style={{color: '#FFFFFF'}}>原料类别</Text>
        </TouchableOpacity>
      </View>
    );
  }

  materialLocationRow = (item, index) => {
    return (
      <View key={item.locationId}>
        <TouchableOpacity
          onPress={() => {
            var selLocationName = item.locationName;
            var selLocationId = item.locationId;
            this.setState({
              selLocationId: selLocationId,
              selLocationName: selLocationName,
            });
          }}>
          <View
            key={item.locationId}
            style={[
              {
                width: screenWidth / (this.state.locationTotalRecord + 1),
                height: 49,
                flexDirection: 'row',
                justifyContent: 'center',
              },
              // ,item.stateCode === this.state.selCompletionStateCode ?
              //     [styles.selectedBlockItemViewStyle]
              //     :
              //     [styles.blockItemViewStyle],
            ]}>
            <Text
              style={[
                item.locationId === this.state.selLocationId
                  ? {
                      color: '#255BDA',
                      fontSize: 16,
                      fontWeight: '500',
                      lineHeight: 49,
                      textAlign: 'center',
                      borderColor: '#255BDA',
                      borderBottomWidth: 2,
                      paddingLeft: 5,
                      paddingRight: 5,
                    }
                  : {
                      color: '#2B333F',
                      fontSize: 16,
                      fontWeight: '500',
                      lineHeight: 49,
                      textAlign: 'center',
                    },
              ]}>
              {item.locationName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title={this.state.parentClassifyId ? '原料名称' : '原料类别'}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[styles.innerViewStyle, {marginTop: 0}]}
          onLayout={this.topBlockLayout.bind(this)}>
          {this.state.parentClassifyId ? (
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginLeft: 10,
              }}>
              <Text style={styles.titleTextStyle}>
                所属库位：{this.state.selLocationName}
              </Text>
            </View>
          ) : (
            <View
              style={{
                marginTop: 0,
                index: 1000,
                flexWrap: 'wrap',
                flexDirection: 'row',
              }}>
              {this.state.materialLocationList &&
              this.state.materialLocationList.length > 0 ? (
                this.state.materialLocationList.map((item, index) => {
                  return this.materialLocationRow(item);
                })
              ) : (
                <View />
              )}
            </View>
          )}
        </View>
        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>  */}
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.classifyId}
            ListEmptyComponent={this.emptyComponent}
            ItemSeparatorComponent={this.space}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
          {/* </ScrollView> */}
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    backgroundColor: '#ffffff',
    borderColor: '#ffffff',
    // borderWidth: 8
  },
  innerViewStyleSpecial: {
    // marginTop:10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
});
