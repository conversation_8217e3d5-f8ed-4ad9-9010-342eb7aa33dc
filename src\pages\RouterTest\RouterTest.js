import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import RouterTestDetail from './RouterTestDetail';

class RouterTest extends React.Component{
    
    constructor(props) {
        // console.log("=======constructor=props:", props) 
        super(props);
        this.props = props;
        this.state = {
            initMessage:'initMessage',
            nameCode:"12345"
        };
    }
    render(){
        return(
            <View style={styles.container}>
                <Text>{this.state.initMessage}</Text>
                <Text>{this.state.nameCode}</Text>
                {/* onPress={this.pushToDetail.bind(this)} */}
                <TouchableOpacity onPress={this._pressButton.bind(this)} >
                    <View style={styles.loginBtnStyle}>
                        <Text style={{color:'white'}}>查看详情</Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    _pressButton() {
        const { navigator } = this.props;
        //为什么这里可以取得 props.navigator?请看上文:
        //<Component {...route.params} navigator={navigator} />
        //这里传递了navigator作为props
        this.setState({
            initMessage:"Begin"
        });
        if(navigator) {
            navigator.push({
                name: 'RouterTestDetail',
                component: RouterTestDetail,
                passProps: {
                    code:"123456",
                     callback:((transCode)=>{
                       this.setState({initMessage:'transCode'})
                     })
                   }
            })
        }
        this.setState({
            initMessage:"End"
        });
    }

    // 跳转到二级页面
    pushToDetail=()=>{
        this.setState({
            initMessage:"Begin"
        });

        this.props.navigator.push(
            {
                // 要跳转的版块
                component:RouterTestDetail,
                title:'Home详情页'
            }
        )
        this.props.navigator.pushToDetail({
            component: RouterTestDetail,
            title: 'Detail',
            props: {
             code:"123456"
            }
          });
        this.setState({
            initMessage:"End"
        });
    }
}
const styles = StyleSheet.create({
    loginBtnStyle:{
        height:35,
        width:350,
        backgroundColor:'blue',
        marginTop:30,
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8
    }
})
module.exports = RouterTest;