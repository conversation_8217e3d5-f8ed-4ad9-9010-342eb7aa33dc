import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, TouchableOpacity, KeyboardAvoidingView,Image, Dimensions, Alert, Modal } from 'react-native';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class OrderScheduling extends Component {
    constructor(props) {
        super(props);
        this.state = {
            orderDataSource: [],
            customerDataSource: [],
            contractDataSource: [],
            selectOrder: [],
            selectCustomer: [],
            selectContract: [],
            selectSchedulingProductionTime: [],
            selectSchedulingCompletedTime: [],
            orderId: "",
            orderName: "",
            selCustomerId: "",
            // customerName: "",
            selCustomerName: "",
            _customerDataSource:[],
            contractName: "",
            brickTypeName: "",
            brickAmount: "",
            schedulingProductionTime: "",
            schedulingCompletedTime: "",
            selOutsourcingTenantId: null,
            plannedProductionQuantity: "",
            theorySingleWeight: "",
            theoryTotalWeight: "",

            productionLineDataSource: [],
            selProductionLineId: null,
            // productionLineId:""
            customerContractOrderTree: [],
            modal: false,
            customerModal: false,
            _orderDataSource: [],
            OutSouringTenantDataSource: [],
            operate: "",
            customerId:"",
            contractId:"",
            customerSearchKeyWord: "",
            searchKeyWord:null
        }
    }
    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;

        this.loadCustomerList();
        // this.loadContractList();

        // loadTypeUrl = "/biz/tenant/customer/list";
        // loadRequest = {'currentPage': 1, 'pageSize': 1000};
        // httpPost(loadTypeUrl, loadRequest, this.callBackLoadCustomerData);

        loadTypeUrl = "/biz/tenant/outsourcing/list";
        loadRequest = { 'currentPage': 1, 'pageSize': 1000 };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadOutSourcing);


        this.loadProductionLineList(constants.loginUser.tenantId);

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { orderId, productionLineId, contractName } = route.params;
            console.log("================" + productionLineId + "============");
            console.log("===========" + contractName + "=======");
            if (contractName) {
                this.setState({
                    contractName: contractName,
                })
            }
            if (orderId) {
                console.log("========Edit==orderId:", orderId);
                this.setState({
                    orderId: orderId,
                    selProductionLineId: productionLineId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/order/get";
                loadRequest = { 'orderId': orderId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditOrderDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
                this.loadSchedulData();
            }
        }
        else {
            this.loadSchedulData();
        }
        // console.log("================================" + this.state.selProductionLineId);
    }
    
    loadCustomerList = () => {
        let loadTypeUrl = "/biz/tenant/customer/list";
        let loadRequest = {
            'currentPage': 1,
            'pageSize': 1000
        };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadCustomerData);
    }

    loadProductionLineList = (tenantId) => {
        let loadTypeUrl = "/biz/production/line/list";
        let loadRequest = {
            'currentPage': 1,
            'pageSize': 1000,
            'operateTenantId': tenantId
        };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadProductionLine);
    }

    callBackLoadProductionLine = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            let productionLineDataSource = response.data.dataList;
            let selProductionLineId = response.data.dataList[0].productionLineId;
            // console.log("======现在的iD" + this.state.selProductionLineId);

            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                selProductionLineId = constants.loginUser.spUserExtDTO.productionLineId;
            }
            if (this.state.selProductionLineId) {
                selProductionLineId = this.state.selProductionLineId;
            }
            // console.log("===-=设置之前的id===" + this.state.selproductionLineId);
            this.setState({
                productionLineDataSource: productionLineDataSource,
                selProductionLineId: selProductionLineId,
            })
            // console.log("设置后的sel=" + selProductionLineId);
            // console.log("===-=设置之后的id===" + this.state.selProductionLineId);
        }
    }

    loadProductionLineData = (tenantId) => {
        let loadTypeUrl = "/biz/production/line/list";
        let loadRequest = {
            'currentPage': 1,
            'pageSize': 1000,
            'operateTenantId': tenantId
        };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadProductionLineData);
    }

    callBackLoadProductionLineData = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                productionLineDataSource: response.data.dataList,
                selProductionLineId: response.data.dataList[0].productionLineId,
            })
        }
    }

    callBackLoadOutSourcing = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            let outSourceTenantList = response.data.dataList;
            outSourceTenantList.unshift({ "outsourcingTenantId": null, "outsourcingName": "本厂", "outsourcingAbbreviation": "本厂" })
            this.setState({
                OutSouringTenantDataSource: outSourceTenantList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    callBackLoadCustomerData = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                customerContractOrderTree: response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditOrderDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectSchedulingProductionTime = response.data.schedulingProductionTime.split("-");
            var selectSchedulingCompletedTime = response.data.schedulingCompletedTime.split("-");
            this.setState({
                orderName: response.data.orderName,
                brickTypeId: response.data.brickTypeId,
                brickTypeName: response.data.brickTypeName,
                brickAmount: response.data.brickAmount,
                contractAmont: response.data.contractAmont,
                selCustomerId: response.data.customerId,
                selCustomerName: response.data.customerName,
                schedulingProductionTime: response.data.schedulingProductionTime,
                schedulingCompletedTime: response.data.schedulingCompletedTime,
                plannedProductionQuantity: response.data.plannedProductionQuantity,
                theorySingleWeight: response.data.theorySingleWeight,
                theoryTotalWeight: response.data.theoryTotalWeight,

                selectSchedulingProductionTime: selectSchedulingProductionTime,
                selectSchedulingCompletedTime: selectSchedulingCompletedTime,
                selOutsourcingTenantId: response.data.outsourcingTenantId,

            })
            if (constants.loginUser.tenantId == response.data.outsourcingTenantId) {
                console.log("==========123", this.state.OutSouringTenantDataSource)
                var outSourceTenantList = this.state.OutSouringTenantDataSource;
                outSourceTenantList[0].outsourcingTenantId = constants.loginUser.tenantId;
                this.setState({
                    OutSouringTenantDataSource: outSourceTenantList
                })
            }
        }
    }

    loadSchedulData = () => {
        // let loadUrl= "/biz/tenant/customer/list";
        // let loadUrl= "/biz/tenant/customer/getCustomerContractOrderTree";
        // let loadRequest={"currentPage":1,"pageSize":1000};
        // httpPost(loadUrl, loadRequest, this.callBackLoadCustomerData);
        // console.log("=========加载客户over===========");

        // 加载未排产的订单
        // let loadUrl1= "/biz/order/list";
        // let loadRequest1={"currentPage":1,"pageSize":1000,"orderState":"A"};
        // httpPost(loadUrl1, loadRequest1, this.loadOrderData);

        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectSchedulingProductionTime: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            schedulingProductionTime: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })

        var dateString = this.state.schedulingProductionTime + ' 00:00:01';
        dateString = dateString.substring(0, 19);
        dateString = dateString.replace(/-/g, '/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var SevenDaysLast = new Date(dateStringTimestamp);
        //获取当前时间的毫秒数
        var nowMilliSeconds = currentDate.getTime();
        // 用获取毫秒数 加上七天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        SevenDaysLast.setTime(nowMilliSeconds + (7 * 86400000));
        //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
        //格式化月，如果小于9，前面补0  
        var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0  
        var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
        this.setState({
            selectSchedulingCompletedTime: [SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
            schedulingCompletedTime: SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
        })
    }

    // loadContractData = (response) => {
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         this.setState({
    //             contractDataSource: response.data.dataList
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // loadOrderData = (response) => {
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         if (response.data.dataList.length <= 0) {
    //             Alert.alert('确认', '没有需要排产的订单', [
    //                 {
    //                     text: "确定", onPress: () => {
    //                         WToast.show({ data: '点击了确定' });
    //                     }
    //                 }
    //             ]);
    //         }
    //         this.setState({
    //             orderDataSource: response.data.dataList
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                    {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }

    // 头部中间
    renderTitleItem() {
        return (
            <TouchableOpacity onPress={() => { }}>
                <View>
                    <Text style={{ fontWeight: '600' }}>新增订单排产</Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>新增排产</Text>
                    {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
                </TouchableOpacity>
            </View>
        )
    }

    renderProductLineRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                var selMachineId = null;
                // 切换生产车间时，下面的机台也要跟着变，机台默认选择第一个
                this.setState({
                    selProductionLineId: item.productionLineId,
                    // machineDataSource:item.machineDTOList,
                })
                // if (item.machineDTOList && item.machineDTOList.length > 0) {
                //     selMachineId = item.machineDTOList[0].machineId;
                // }
                // this.setState({
                //     selMachineId:selMachineId,
                // })
            }}>
                <View key={item.productionLineId} style={[item.productionLineId === this.state.selProductionLineId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/2,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.productionLineId === this.state.selProductionLineId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.productionLineName}
                    </Text>
                </View>
                {/* <View key={item.productionLineId} style={item.productionLineId === this.state.selProductionLineId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.productionLineId === this.state.selProductionLineId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.productionLineName}
                    </Text>
                </View> */}
            </TouchableOpacity>
        )
    }

    // // 渲染底部客户选择器
    // openCustomerSelect() {
    //     if (!this.state.customerContractOrderTree || this.state.customerContractOrderTree.length < 1) {
    //         WToast.show({ data: "请先添加客户" });
    //         return
    //     }
    //     this.setState({
    //         contractDataSource: [],
    //         orderDataSource: []
    //     })
    //     this.refs.SelectCustomer.showCustomer(this.state.selectCustomer, this.state.customerContractOrderTree)
    // }

    openContractSelect() {
        if (this.state.selCustomerName) {
            if (!this.state.contractDataSource || this.state.contractDataSource.length < 1) {
                WToast.show({ data: "请先添加合同" });
                return;
            }
            this.setState({
                orderDataSource: []
            })
            this.refs.SelectContract.showContract(this.state.selectContract, this.state.contractDataSource)
        }
        else {
            WToast.show({ data: "请先添加客户" });
            return;
        }

    }

    openSchedulingProductionTime() {
        this.refs.SelectSchedulingProductionTime.showDate(this.state.selectSchedulingProductionTime)
    }

    openSchedulingCompletedTime() {
        this.refs.SelectSchedulingCompletedTime.showDate(this.state.selectSchedulingCompletedTime)
    }

    loadContractList =(customerId)=>{
        let loadUrl = "/biz/contract/list";
        let loadRequest = {
            "currentPage":1,
            "pageSize":1000,
            "partyA": customerId,
            "qryContent":"contract"
        };
        httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
    }

    loadContractListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                contractDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    callBackContractValue(value) {
        console.log("==========合同选择的结果：", value)
        if (!value) {
            return;
        }
        var contractName = value.toString();
        this.setState({
            selectContract: value,
            contractName: contractName
        })
        let loadUrl= "/biz/contract/getContractByName";
        let loadRequest={
            "contractName":contractName
        };
        httpPost(loadUrl, loadRequest, this.loadContractDetailCallBack);
    }

    loadContractDetailCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                contractId : response.data.contractId,
                orderId:"",
                orderName:"",
                orderDataSource:[],
                _orderDataSource:[],
                brickTypeName:"",
                brickAmount:""
            })
            this.loadOrderListByContractId(response.data.contractId);
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadOrderListByContractId=(contractId)=>{
        let loadUrl = "/biz/order/list";
        let loadRequest = {
            "currentPage":1,
            "pageSize":1000,
            "contractId": contractId,
            "qryContent":"order",
            // "productionLineId":this.state.selProductionLineId,
            "orderState": "A"
        };
        httpPost(loadUrl, loadRequest, this.loadOrderListByContractIdCallBack);
    }

    loadOrderListByContractIdCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                Alert.alert('确认', '没有需要排产的订单', [
                    {
                        text: "确定", onPress: () => {
                            WToast.show({ data: '点击了确定' });
                        }
                    }
                ]);
            }
            this.setState({
                orderDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }


    // 搜索订单
    searchOrder = () => {
        var _orderDataSource = copyArr(this.state.orderDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _orderDataSource = _orderDataSource.filter(item => item.orderName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _orderDataSource: _orderDataSource,
        })
    }

    callBackSelectSchedulingProductionTimeValue(value) {
        console.log("==========排产开始时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectSchedulingProductionTime: value
        })
        if (this.state.selectSchedulingProductionTime && this.state.selectSchedulingProductionTime.length) {
            var schedulingProductionTime = "";
            var vartime;
            for (var index = 0; index < this.state.selectSchedulingProductionTime.length; index++) {
                vartime = this.state.selectSchedulingProductionTime[index];
                if (index === 0) {
                    schedulingProductionTime += vartime;
                }
                else {
                    schedulingProductionTime += "-" + vartime;
                }
            }
            this.setState({
                schedulingProductionTime: schedulingProductionTime
            })

            // var dateString = this.state.schedulingProductionTime +' 00:00:01';
            // dateString = dateString.substring(0,19);    
            // dateString = dateString.replace(/-/g,'/');
            // var dateStringTimestamp = new Date(dateString).getTime();
            // // 根据毫秒数构建 Date 对象
            // var SevenDaysLast = new Date(dateStringTimestamp);
            // //获取当前时间的毫秒数
            // var nowMilliSeconds = SevenDaysLast.getTime();
            // // 用获取毫秒数 加上七天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
            // SevenDaysLast.setTime(nowMilliSeconds+(7*86400000));
            // //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
            // //格式化月，如果小于9，前面补0  
            // var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
            // //格式化日，如果小于9，前面补0  
            // var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
            // this.setState({
            //     selectSchedulingCompletedTime:[SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
            //     schedulingCompletedTime:SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
            // })
        }
    }
    callBackSelectSchedulingCompletedTimeValue(value) {
        console.log("==========排产完成时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectSchedulingCompletedTime: value
        })
        if (this.state.selectSchedulingCompletedTime && this.state.selectSchedulingCompletedTime.length) {
            var schedulingCompletedTime = "";
            var vartime;
            for (var index = 0; index < this.state.selectSchedulingCompletedTime.length; index++) {
                vartime = this.state.selectSchedulingCompletedTime[index];
                if (index === 0) {
                    schedulingCompletedTime += vartime;
                }
                else {
                    schedulingCompletedTime += "-" + vartime;
                }
            }
            this.setState({
                schedulingCompletedTime: schedulingCompletedTime
            })
        }
    }

    saveOrder = () => {
        console.log("=======saveOrder");
        let toastOpts;
        if (!this.state.selCustomerName) {
            toastOpts = getFailToastOpts("请选择客户");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contractName) {
            toastOpts = getFailToastOpts("请选择合同");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.orderId) {
            toastOpts = getFailToastOpts("请选择订单");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.plannedProductionQuantity) {
        //     toastOpts = getFailToastOpts("请输入计划生产数量");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.theorySingleWeight) {
        //     toastOpts = getFailToastOpts("请输入理论单重");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.theoryTotalWeight) {
        //     toastOpts = getFailToastOpts("请输入理论总重");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.schedulingProductionTime) {
            toastOpts = getFailToastOpts("请选择预计生产时间");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.schedulingCompletedTime) {
            toastOpts = getFailToastOpts("请选择预计完成时间");
            WToast.show(toastOpts)
            return;
        }

        let url = "/biz/order/scheduling";
        let requestParams = {
            'orderId': this.state.orderId,
            'schedulingProductionTime': this.state.schedulingProductionTime,
            'schedulingCompletedTime': this.state.schedulingCompletedTime,
            'outsourcingTenantId': this.state.selOutsourcingTenantId,
            'productionLineId': this.state.selProductionLineId,

            'plannedProductionQuantity': this.state.plannedProductionQuantity,
            'theorySingleWeight': this.state.theorySingleWeight,
            'theoryTotalWeight': this.state.theoryTotalWeight ? this.changeTwoDecimal(this.state.theoryTotalWeight) : this.state.theoryTotalWeight
        };
        httpPost(url, requestParams, this.saveOrder_call_back);
    }

    // 保存回调函数
    saveOrder_call_back = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    renderOutSourcingRow = (item) => {
        return (

            <TouchableOpacity onPress={() => {
                this.setState({
                    selOutsourcingTenantId: item.outsourcingTenantId
                })
                this.loadProductionLineData(!item.outsourcingTenantId ? constants.loginUser.tenantId : item.outsourcingTenantId)
            }}>
                <View key={item.outsourcingTenantId} style={[item.outsourcingTenantId === this.state.selOutsourcingTenantId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.outsourcingTenantId === this.state.selOutsourcingTenantId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.outsourcingAbbreviation}
                    </Text>
                </View>

            </TouchableOpacity>
        )
    }
    renderCustomerItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    selCustomerId:item.customerId,
                    selCustomerName:item.customerName,
                    selectContract:[],
                    contractName:"",
                    orderId:"",
                    orderName:"",
                    orderDataSource:[],
                    _orderDataSource:[],
                    brickTypeName:"",
                    brickAmount:""
                })
                this.loadContractList(item.customerId);
            }}>
                <View key={item.customerId} style={item.customerId===this.state.selCustomerId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.customerId===this.state.selCustomerId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.customerName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    changeTwoDecimal = (x) => {
        var f_x = parseFloat(x);
        if (isNaN(f_x)) {
            alert('function:changeTwoDecimal->parameter error');
            return false;
        }
        f_x = Math.round(f_x * 100) / 100;

        return f_x;
    }

    searchCustomer = () => {
        var _customerDataSource = copyArr(this.state.customerContractOrderTree);
        if (this.state.customerSearchKeyWord && this.state.customerSearchKeyWord.length > 0) {
            _customerDataSource = _customerDataSource.filter(item => item.customerName.indexOf(this.state.customerSearchKeyWord) > -1);
        }
        this.setState({
            _customerDataSource: _customerDataSource,
        })
    }
    // 订单项
    renderOrderItem = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.checkId) {
                    return;
                }
                this.setState({
                    selBrickTypeId: item.brickTypeId,
                    selBrickTypeName: item.brickTypeName,
                    brickTypeId: item.brickTypeId,
                    brickTypeName: item.brickTypeName,
                    brickAmount: item.brickAmount,
                    orderId: item.orderId,
                    orderName: item.orderName,
                    selCustomerName: item.customerName,
                })
            }}>
                <View key={item.orderId} style={item.orderId === this.state.orderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.orderId === this.state.orderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '排产'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>客户名称</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[{flexWrap:'wrap'},(this.state.operate == "编辑") ? CommonStyle.disableViewStyle : null]}>
                        <TouchableOpacity onPress={()=>{
                            if (this.state.operate == "编辑") {
                                WToast.show({ data: "编辑只能编辑客户、合同、订单名称及砖型以外的信息" });
                                return;
                            }
                            if (this.state.customerContractOrderTree && this.state.customerContractOrderTree.length > 0) {
                                this.setState({
                                    _customerDataSource: copyArr(this.state.customerContractOrderTree),
                                })
                            }
                            this.setState({ 
                                customerModal:true,
                            })

                            if (!this.state.selCustomerId && this.state.customerContractOrderTree && this.state.customerContractOrderTree.length > 0) {
                                this.setState({
                                    selCustomerId:this.state.customerContractOrderTree[0].customerId,
                                    selCustomerName:this.state.customerContractOrderTree[0].customerName,
                                })
                                this.loadContractList(this.state.customerContractOrderTree[0].customerId);
                            }
                        }}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 10), flexWrap: 'wrap', borderWidth: 0}]}>
                                {
                                    this.state.selCustomerId && this.state.selCustomerName ?
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>{this.state.selCustomerName}</Text>
                                        :
                                    <Text style={[{ color: '#A0A0A0', fontSize: 15,}]}>选择客户</Text>
                                }
                                <Image style={{ width: 22, height: 22, position:'absolute', right: 10, top: 11 }} source={require('../../assets/icon/iconfont/arrowRight.png')}></Image>
                            </View>
                            {/* <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, { flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                {
                                    this.state.selCustomerId && this.state.selCustomerName ?
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>{this.state.selCustomerName}</Text>
                                    :
                                    <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>选择客户</Text>
                                }
                                </View> */}
                        </TouchableOpacity>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.customerModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput 
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({customerSearchKeyWord:text})}
                                    >
                                        {this.state.customerSearchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={()=>{
                                        this.searchCustomer();
                                        }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{flexDirection:'row', flexWrap:'wrap', overflow:'scroll'}}>
                                    {
                                        (this.state._customerDataSource && this.state._customerDataSource.length > 0) 
                                        ? 
                                        this.state._customerDataSource.map((item, index)=>{
                                            if (index < 1000) {
                                                return this.renderCustomerItem(item)
                                            }
                                        })
                                        : <EmptyRowViewComponent/> 
                                    }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                    <TouchableOpacity onPress={() => { 
                                        this.setState({
                                            customerModal:false,
                                            customerSearchKeyWord:"",                         
                                            // selCustomerId: null,
                                            // selCustomerName: null,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            customerModal:false,
                                            customerSearchKeyWord:"",
                                            selCustomerId: this.state.selCustomerId,
                                            selCustomerName: this.state.selCustomerName,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>合同名称</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={() => {
                            if (this.state.operate == "编辑") {
                                WToast.show({ data: "编辑只能编辑客户、合同、订单名称及砖型以外的信息" });
                                return;
                            }
                            else {
                                this.openContractSelect()
                            }
                        }}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.contractName ? "请选择" : this.state.contractName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>
                                订单名称
                            </Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        {/* <TouchableOpacity onPress={()=>{
                        if (this.state.orderId) {
                            WToast.show({data:"编辑只能编辑客户、合同、订单名称及砖型以外的信息"})
                        }
                        else {
                            this.openOrderSelect()
                        }
                    }}>
                        <View style={CommonStyle.inputTextStyleTextStyle}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.orderName ? "请选择订单" : this.state.orderName}
                            </Text>
                        </View>
                    </TouchableOpacity> */}
                        <View style={[(!this.state.orderDataSource || this.state.orderDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (this.state.operate == "编辑") {
                                    WToast.show({ data: "编辑只能编辑客户、合同、订单名称及砖型以外的信息" });
                                    return;
                                }
                                if (!this.state.orderDataSource || this.state.orderDataSource.length === 0) {
                                    let errorMsg = '没有需要排产的订单';
                                    if (!this.state.selCustomerName || this.state.selCustomerName.length === 0) {
                                        errorMsg = "请先选择客户";
                                    }
                                    else if (!this.state.contractName || this.state.contractName.length === 0) {
                                        errorMsg = "请先选择合同";
                                    }
                                    Alert.alert('错误', errorMsg, [
                                        {
                                            text: "错误", onPress: () => {
                                                WToast.show({ data: '点击了确定' });
                                            }
                                        }
                                    ]);
                                    return;
                                }
                                if (!this.state._orderDataSource || this.state._orderDataSource.length === 0) {
                                    this.setState({
                                        _orderDataSource: copyArr(this.state.orderDataSource),
                                    })
                                }
                                this.setState({
                                    modal: true,
                                })

                                if (!this.state.orderId && this.state.orderDataSource && this.state.orderDataSource.length > 0) {
                                    this.setState({
                                        selBrickTypeId: this.state.orderDataSource[0].brickTypeId,
                                        selBrickTypeName: this.state.orderDataSource[0].brickTypeName,
                                        brickTypeId: this.state.orderDataSource[0].brickTypeId,
                                        brickTypeName: this.state.orderDataSource[0].brickTypeName,
                                        brickAmount: this.state.orderDataSource[0].brickAmount,
                                        orderId: this.state.orderDataSource[0].orderId,
                                        orderName: this.state.orderDataSource[0].orderName,
                                        selCustomerName: this.state.orderDataSource[0].customerName,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 10), flexWrap: 'wrap', borderWidth: 0}]}>
                                    {
                                        this.state.orderId && this.state.orderName ?
                                        <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>{this.state.orderName}</Text>
                                            :
                                        <Text style={[{ color: '#A0A0A0', fontSize: 15,}]}>选择订单</Text>
                                    }
                                    <Image style={{ width: 22, height: 22, position:'absolute', right: 10, top: 11 }} source={require('../../assets/icon/iconfont/arrowRight.png')}></Image>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                    <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                </View> */}
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.searchOrder();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._orderDataSource && this.state._orderDataSource.length > 0)
                                                ?
                                                this.state._orderDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderOrderItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                            searchKeyWord:""
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.orderId) {
                                            let toastOpts = getFailToastOpts("您还没有选择砖型");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            modal: false,
                                            searchKeyWord:""
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>

                        </View>
                    </Modal>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>砖型</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.orderId ? "请选择" : this.state.brickTypeName}
                            </Text>
                        </View>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>数量(吨/块)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                            {/* <Text style={{color:'#A0A0A0', fontSize:15}}>{this.state.brickAmount}</Text> */}
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.orderId ? "请选择" : (this.state.brickAmount ? this.state.brickAmount : "无")}
                            </Text>
                        </View>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>计划生产数量</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ plannedProductionQuantity: text, theoryTotalWeight: text * this.state.theorySingleWeight / 1000 })}
                        >
                            {this.state.plannedProductionQuantity}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>理论单重(Kg)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ theorySingleWeight: text, theoryTotalWeight: text * this.state.plannedProductionQuantity / 1000 })}
                        >
                            {this.state.theorySingleWeight}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>理论总重(吨)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ theoryTotalWeight: text })}
                        >
                            {this.state.theoryTotalWeight ? this.changeTwoDecimal(this.state.theoryTotalWeight) : this.state.theoryTotalWeight}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>预计生产时间</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openSchedulingProductionTime()}>
                            {/* <View style={CommonStyle.inputTextStyleTextStyle}>
                            <Text>{this.state.schedulingProductionTime}</Text>
                        </View> */}
                            <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.schedulingProductionTime ? "请选择预计生产时间" : this.state.schedulingProductionTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>预计完成时间</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openSchedulingCompletedTime()}>
                            {/* <View style={CommonStyle.inputTextStyleTextStyle}>
                            <Text>{this.state.schedulingCompletedTime}</Text>
                        </View> */}
                            <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.schedulingCompletedTime ? "请选择预计完成时间" : this.state.schedulingCompletedTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>生产单位</Text>
                        </View>
                    </View>
                    <View style={{ width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15 }}>
                        {
                            (this.state.OutSouringTenantDataSource && this.state.OutSouringTenantDataSource.length > 0)
                                ?
                                this.state.OutSouringTenantDataSource.map((item, index) => {
                                    return this.renderOutSourcingRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>生产车间</Text>
                            </View>
                        </View>
                        <View style={{ width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15 }}>
                            {
                                (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0)
                                    ?
                                    this.state.productionLineDataSource.map((item, index) => {
                                        return this.renderProductLineRow(item)
                                    })
                                    : <EmptyRowViewComponent />
                            }
                        </View>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    {/* <View>
                    {
                     (!this.state.selOutsourcingTenantId) ? 
                     <View>
                         <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>生产车间</Text>
                            </View>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0) 
                                ? 
                                this.state.productionLineDataSource.map((item, index)=>{
                                    return this.renderProductLineRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                     </View>
                     :
                     <View/>   
                    }
                </View> */}



                    <View style={[CommonStyle.blockAddCancelSaveStyle]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveOrder.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect
                        ref={'SelectContract'}
                        callBackContractValue={this.callBackContractValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectSchedulingProductionTime'}
                        callBackDateValue={this.callBackSelectSchedulingProductionTimeValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectSchedulingCompletedTime'}
                        callBackDateValue={this.callBackSelectSchedulingCompletedTimeValue.bind(this)}
                    />
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}

const styles = StyleSheet.create({
    // contentViewStyle:{
    //     backgroundColor:'#FFFFFF',
    //     height:screenHeight - 140
    // },
    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:5,
        marginRight:5,
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        // borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
})
// module.exports = OrderScheduling;