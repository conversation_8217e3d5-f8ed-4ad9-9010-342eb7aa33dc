import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  ImageBackground,
  Linking,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import ClassHeadScreen from '../../../component/ClassHeadScreen';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import EmptyListComponent from '../../../component/EmptyListComponent';
import ProgressBar from '../../../component/ProgressBar';
import {ifIphoneXContentViewDynamicHeight} from '../../../utils/ScreenUtil';

var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

const commonStyles = {
  color: 'rgba(255, 255, 255, 1)',
  fontSize: 12,
  width: 55,
  height: 19,
  paddingLeft: 9,
  paddingTop: 2,
};

const taskStateBgColor = {
  '0AA': '#0000ff',
  '0BB': '#ff0000',
  '0CC': '#008000',
  default: 'rgba(0,10,32,0.45)',
};

const taskStateText = {
  '0AA': '实习中',
  '0BB': '已超期',
  '0CC': '已完成',
  default: '未开始',
};

const taskTitleBgColor = {
  0: '#FB7B04',
  1: '#1084FD',
  2: '#1E85A3',
  3: '#FBB100',
  4: '#BF181E',
  5: '#1B9342',
};

export default class CourseTrackList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      operate: '',
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      selTaskStateCode: 'all',
      showSearchItemBlock: false,
      departmentDataSource: null,
      courseDataSource: [],
      selDepartmentId: null,
      selDepartmentName: null,
      selDepartmentStaffList: null,
      selStaffId: null,
      selStaffName: null,
      qryStartTime: null,
      selectedQryStartDate: [],
      moreModal: false,
      closeModal: false,
      resetModal: false,
      courseItem: {},
      modalDataItem: {},
    };
  }
  componentDidMount() {
    console.log('componentDidMount');
    const _qryStartTime = this.initqryStartTime();
    this.loadData(_qryStartTime);
  }

  loadData = (_qryStartTime) => {
    // 部门检索
    let loadTypeUrl = '/biz/department/list_for_tenant';
    let loadRequest = {qryAll_NoPower: 'Y', currentPage: 1, pageSize: 1000};
    httpPost(loadTypeUrl, loadRequest, (response) => {
      if (response.code == 200 && response.data) {
        this.setState({departmentDataSource: response.data});
      } else {
        WToast.show({data: '部门数据加载失败'});
      }
    });

    // 租户实习
    let loadCourseUrl = '/biz/course/course_list';
    let loadCourseRequest = {currentPage: 1, pageSize: 10000};
    httpPost(loadCourseUrl, loadCourseRequest, (response) => {
      if (response.code == 200 && response.data.dataList) {
        this.setState({courseDataSource: response.data.dataList});
      } else {
        WToast.show({data: '实习数据加载失败'});
      }
    });

    // 状态分栏
    const taskStateDataSource = [
      {stateCode: 'all', stateName: '全部'},
      {stateCode: '0AA', stateName: '实习中'},
      {stateCode: '0BB', stateName: '已超期'},
      {stateCode: '0CC', stateName: '已完成'},
    ];

    // 合并状态更新
    this.setState(
      {
        taskStateDataSource,
      },
      () => {
        this.loadCourseTaskList(_qryStartTime);
      },
    );
  };

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/course/task/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      taskState:
        this.state.selTaskStateCode === 'all'
          ? null
          : this.state.selTaskStateCode,
      departmentId: this.state.selDepartmentId,
      checkOutUserId: this.state.selStaffId,
      qryStartTime: this.state.qryStartTime,
    };
    httpPost(url, loadRequest, (response) => {
      if (response.code == 200 && response.data && response.data.dataList) {
        var dataNew = response.data.dataList;
        var dataAll = [...dataNew];
        // console.log("新积分2数据", JSON.stringify(dataAll,null, 6));
        this.setState({
          dataSource: dataAll,
          currentPage: response.data.currentPage + 1,
          totalPage: response.data.totalPage,
          totalRecord: response.data.totalRecord,
        });
      } else if (response.code == 401) {
        WToast.show({data: response.message});
        this.props.navigation.navigate('LoginView');
      }
    });
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/course/task/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      taskState:
        this.state.selTaskStateCode === 'all'
          ? null
          : this.state.selTaskStateCode,
      departmentId: this.state.selDepartmentId,
      checkOutUserId: this.state.selStaffId,
      qryStartTime: this.state.qryStartTime,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };
  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      console.log('积分2数据', JSON.stringify(dataAll, null, 6));
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };

  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    if (this.state.refreshing) {
      WToast.show({data: 'loading...'});
      return;
    }
    this.setState({ refreshing: true }, () => {
          console.log('refreshing 已更新:', this.state.refreshing);
          // 在这里执行后续操作
          this.loadCourseTaskList();
    });
  };

  initqryStartTime = () => {
    // 当前时间
    var currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 1);
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _qryStartTime =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      selectedQryStartDate: [
        currentDate.getFullYear(),
        currentDateMonth,
        currentDateDay,
      ],
      qryStartTime: _qryStartTime,
    });
    return _qryStartTime;
  };

  loadCourseTaskList = (_qryStartTime) => {
    let url = '/biz/course/task/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      taskState:
        this.state.selTaskStateCode === 'all'
          ? null
          : this.state.selTaskStateCode,
      departmentId: this.state.selDepartmentId,
      qryStartTime: _qryStartTime ? _qryStartTime : this.state.qryStartTime,
      checkOutUserId: this.state.selStaffId,
    };
    httpPost(url, loadRequest, this.loadCourseTaskListCallBack);
  };

  loadCourseTaskListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      // console.log("loadCourseTaskListCallBack", JSON.stringify(dataAll,null, 6));
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 右上角导出pdf
  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/course_track';
    let requestParams = {
      // "userId": constants.loginUser.userId,
      checkOutUserId: this.state.selStaffId,
      qryStartTime: this.state.qryStartTime,
      currentPage: 1,
      pageSize: 1000,
      departmentId: this.state.selDepartmentId,
      taskState:
        this.state.selTaskStateCode === 'all'
          ? null
          : this.state.selTaskStateCode,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 22, height: 22}}
          source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          Alert.alert('确认', '您确定要导出PDF文件吗？', [
            {
              text: '取消',
              onPress: () => {
                WToast.show({data: '点击了取消'});
              },
            },
            {
              text: '确定',
              onPress: () => {
                WToast.show({data: '点击了确定'});
                this.exportPdfFile();
              },
            },
          ]);
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 24, height: 24}}
          source={require('../../../assets/icon/iconfont/newExport.png')}></Image>
      </TouchableOpacity>
    );
  }

  openQryStartDate() {
    this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate);
  }

  resetCourseTask = (taskItem) => {
    console.log('重启', JSON.stringify(taskItem, null, 6));
    let requestUrl = '/biz/course/task/modify';
    let requestParams = {
      courseTaskId: taskItem.courseTaskId,
      checkOutUserId: taskItem.checkOutUserId,
      checkInUserId: constants.loginUser.userId,
      taskState: '0AA',
    };
    httpPost(requestUrl, requestParams, (response) => {
      // console.log(response)
      if (response.code == 200) {
        // 更新页面上显示
        taskItem.taskState = taskItem.taskState === '0CC' ? '0AA' : '0CC';
        let courseTaskDataSource = this.state.dataSource;
        // JS 数组遍历
        courseTaskDataSource.forEach((obj) => {
          //console.log("=======actualCompletionTime:", actualCompletionTime);
          if (obj.courseTaskId === taskItem.courseTaskId) {
            obj.taskState = taskItem.taskState;
            //obj.actualCompletionTime = response.data.actualCompletionTime;
            WToast.show({
              data: (taskItem.taskState === '0CC' ? '关闭' : '重启') + '完成',
            });
          }
        });
        this.callBackFunction();
      } else {
        WToast.show({data: response.message});
      }
    });
  };

  closeCourseTask = (taskItem) => {
    console.log('=======close==courseDataSource', this.state.courseDataSource);
    const _dataSource = this.state.courseDataSource;
    if (!_dataSource || _dataSource.length <= 0) {
      WToast.show({data: '数据加载中，请稍后再试'});
      console.log('@_courseDataSource 为 null_@');
      return;
    }
    let requestUrl = '/biz/course/task/modify';
    let requestParams = {
      courseTaskId: taskItem.courseTaskId,
      checkOutUserId: taskItem.checkOutUserId,
      checkInUserId: constants.loginUser.userId,
      taskState: '0CC',
    };
    httpPost(requestUrl, requestParams, (response) => {
      // console.log(response)
      if (response.code == 200) {
        // 更新页面上显示
        taskItem.taskState = taskItem.taskState === '0CC' ? '0AA' : '0CC';
        let courseTaskDataSource = this.state.dataSource;
        // JS 数组遍历
        courseTaskDataSource.forEach((obj) => {
          //console.log("=======actualCompletionTime:", actualCompletionTime);
          if (obj.courseTaskId === taskItem.courseTaskId) {
            obj.taskState = taskItem.taskState;
            //obj.actualCompletionTime = response.data.actualCompletionTime;
            WToast.show({
              data: (taskItem.taskState === '0CC' ? '关闭' : '重启') + '完成',
            });
          }
        });
        this.callBackFunction();
      } else {
        WToast.show({data: response.message});
      }
    });
  };

  renderRow = (item, index) => {
    if (isNaN(Date.parse(item.gmtCreated.replace(' ', 'T')))) {
      console.error('Invalid date format in gmtCreated:', item.gmtCreated);
      return null; // 或者抛出异常：throw new Error('Invalid date format in gmtCreated');
    }
    //计算最后学习时间
    item.lastStudyTime =
      item.portalTrackDetailDTOList &&
      item.portalTrackDetailDTOList[0] &&
      item.portalTrackDetailDTOList[0].gmtCreated
        ? item.portalTrackDetailDTOList[0].gmtCreated
        : item.gmtCreated;

    const state = item.taskState || 'default';
    const indexOfBgColor = (parseInt(index) + 1) % 6;
    // 计算学习天数
    const studyDays =
      item.taskState == '0CC'
        ? item.courseDurations
        : Math.abs(
            (
              (Date.now() - Date.parse(item.gmtCreated.replace(' ', 'T'))) /
              (1000 * 3600 * 24)
            ).toFixed(0),
          );

    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('CourseTrackDetail', {
            // 传递参数
            dataItem: item,
            courseDataSource: this.state.courseDataSource,
            dataIndex: index,
            // 传递回调函数
            refresh: this.callBackFunction,
          });
          // console.log("courseTaskId============",item.courseTaskId)
        }}>
        <View
          key={item.courseTaskId}
          style={[CommonStyle.innerViewStyle, {backgroundColor: 'white'}]}>
          {/* 实习顶部信息 */}
          <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
            {item.checkOutUserPhoto ? (
              <Image
                source={{
                  uri: constants.image_addr + '/' + item.checkOutUserPhoto,
                }}
                style={{height: 48, width: 48, borderRadius: 50}}
              />
            ) : (
              <ImageBackground
                source={require('../../../assets/icon/iconfont/profilePicture.png')}
                style={{width: 48, height: 48}}>
                <View
                  style={{
                    height: 48,
                    width: 48,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  {item.checkOutUserName &&
                  item.checkOutUserName.length <= 2 ? (
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: 17,
                        fontWeight: 'normal',
                        textAlign: 'center',
                        lineHeight: 22,
                      }}>
                      {item.checkOutUserName}
                    </Text>
                  ) : (
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: 17,
                        fontWeight: 'normal',
                        textAlign: 'center',
                        lineHeight: 22,
                      }}>
                      {item.checkOutUserName.slice(-2)}
                    </Text>
                  )}
                </View>
              </ImageBackground>
            )}

            <View style={{marginLeft: 11, flexDirection: 'column'}}>
              <View style={{flexDirection: 'row', marginTop: 4}}>
                <View style={{flexDirection: 'row'}}>
                  <Text style={{fontSize: 16}}>
                    {item.checkOutUserName}的实习
                  </Text>
                </View>
              </View>
              <View style={{flexDirection: 'row'}}>
                <Image
                  style={{
                    height: 13,
                    width: 12,
                    marginTop: 5,
                    marginLeft: 1,
                    marginRight: 5,
                  }}
                  source={require('../../../assets/icon/iconfont/clock.png')}></Image>
                <View style={{marginTop: 4, marginBottom: 3, marginRight: 4}}>
                  <Text
                    style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)'}]}>
                    {item.gmtModified
                      ? item.gmtModified.slice(0, 10)
                      : item.lastStudyTime.slice(0, 10)}{' '}
                    最后实习时间
                  </Text>
                </View>
              </View>
            </View>
            <View style={{position: 'absolute', right: 13, top: 0}}>
              <TouchableOpacity
                onPress={() => {
                  this.setState({
                    moreModal: true,
                    modalDataItem: item,
                  });
                }}>
                <View
                  style={[
                    {
                      width: 35,
                      height: 35,
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                    },
                  ]}>
                  <Image
                    style={{width: 28, height: 28}}
                    source={require('../../../assets/icon/iconfont/more.png')}></Image>
                </View>
              </TouchableOpacity>
            </View>
          </View>
          {/* 分隔线 */}
          <View style={styles.lineViewStyle} />
          {/* 实习中部信息 */}
          <View
            style={{
              flexDirection: 'row',
              height: 129,
              width: screenWidth - 13,
              // backgroundColor:"red"
            }}>
            <View
              style={{
                width: 130 + 28,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <View>
                {item.coursePhoto ? (
                  <View>
                    <Image
                      source={{
                        uri: constants.image_addr + '/' + item.coursePhoto,
                      }}
                      style={{
                        width: 130,
                        height: 93,
                        borderRadius: 10,
                      }}></Image>
                  </View>
                ) : (
                  <View>
                    <View
                      style={{
                        width: 130,
                        height: 93,
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: 8,
                        borderRadius: 10,
                        backgroundColor: taskTitleBgColor[indexOfBgColor],
                      }}>
                      <Text
                        style={{
                          fontSize: 18,
                          color: 'white',
                          fontWeight: 'bold',
                        }}>
                        {item.courseName}
                      </Text>
                    </View>
                  </View>
                )}
                <View
                  style={{
                    position: 'absolute',
                    zIndex: 10,
                    borderTopLeftRadius: 10,
                    borderBottomRightRadius: 10,
                    backgroundColor: taskStateBgColor[state],
                  }}>
                  <Text style={commonStyles}>{taskStateText[state]}</Text>
                </View>
              </View>
            </View>

            <View
              style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
              <View style={{height: 93, width: '100%'}}>
                {/* 自定义组件 */}
                <ClassHeadScreen
                  redTitle={item.courseLevelName}
                  blackTitle={' 任务' + item.courseSort + ' ' + item.courseName}
                />
                {/* <View style={{backgroundColor:"white", height:44 ,flexDirection:"row"}}>
                                <View style={{
                                    position:'absolute',
                                    backgroundColor:"red",
                                    borderRadius:6,
                                    borderBottomLeftRadius:0,
                                    height:23,
                                    // width:19,
                                    justifyContent: 'center',
                                    alignItems:"center",
                                    paddingLeft:2,
                                    paddingRight:2,
                                    zIndex:10
                                }}>
                                    <Text style={{fontSize:16,color:"white",}}>{item.courseLevelName?item.courseLevelName:"?"}</Text>
                                </View>
                                <Text style={{fontSize:16}}>{item.courseLevelName+" 第" + item.courseSort + "课 " + item.courseName}</Text>
                            </View> */}

                <View style={{height: 20, flexDirection: 'row'}}>
                  <View
                    style={{
                      //外边距
                      borderRadius: 10,
                      backgroundColor: 'rgba(27,188,130,0.2)',
                      height: 20,
                      paddingLeft: 10,
                      paddingRight: 10,
                      marginTop: 8, 
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Text style={{fontSize: 12, color: '#1BBC82'}}>
                      {item.courseTypeName}
                    </Text>
                  </View>
                </View>
                <View
                  style={{
                    height: 12,
                    marginTop: 15,
                  }}>
                  <ProgressBar
                    fillColor={'rgba(30, 110, 250, 1)'}
                    height={3}
                    progress={
                      studyDays / item.courseDurations >= 1
                        ? 1
                        : studyDays / item.courseDurations
                    }
                  />
                </View>
                <View style={{height: 17, flexDirection: 'row'}}>
                  <Text>{'已实习(天）：'}</Text>
                  {item.taskState == '0AA' ? (
                    <Text>{studyDays}</Text>
                  ) : item.taskState == '0BB' ? (
                    <Text style={{color: 'red'}}>{'超期'}</Text>
                  ) : (
                    <Text>{item.courseDurations}</Text>
                  )}
                  <Text>{'/' + item.courseDurations}</Text>
                </View>
              </View>
            </View>
          </View>

          {
            // item.taskState === '0CC' ?
            //     <View style={[styles.titleViewStyle, { marginTop: 5 }]}>
            //         <Text style={styles.titleTextStyle}>实际完成时间：</Text>
            //         <Text style={styles.itemContentStyle}>{item.actualCompletionTime}</Text>
            //     </View>
            //     :
            //     <View />
          }

          <View
            style={{
              backgroundColor: 'white',
              width: screenWidth,
              height: 38,
              flexDirection: 'row',
              justifyContent: 'flex-end',
              alignItems: 'flex-end',
              paddingRight: 16,
            }}>
            <TouchableOpacity
              onPress={() => {
                item.taskState === '0CC'
                  ? // '重启'
                    this.setState({
                      resetModal: true,
                      modalDataItem: item,
                    })
                  : // '关闭'
                    this.setState({
                      closeModal: true,
                      modalDataItem: item,
                    });
              }}>
              <View
                style={
                  item.taskState === '0CC'
                    ? [
                        CommonStyle.itemBottomEditBlueBtnViewStyle,
                        {width: 64, backgroundColor: 'rgba(253, 66, 70, 1)'},
                      ]
                    : [
                        CommonStyle.itemBottomEditBlueBtnViewStyle,
                        {
                          width: 64,
                          flexDirection: 'row',
                          backgroundColor: 'rgba(253, 66, 70, 1)',
                        },
                      ]
                }>
                <Image
                  style={{width: 17, height: 17, marginRight: 3}}
                  source={require('../../../assets/icon/iconfont/close.png')}></Image>
                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                  {item.taskState === '0CC' ? '重启' : '关闭'}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  addCourseTask = (taskItem) => {
    console.log('=======courseDataSource', this.state.courseDataSource);
    const _dataSource = this.state.courseDataSource;
    if (!_dataSource || _dataSource.length <= 0) {
      WToast.show({data: '数据加载中，请稍后再试'});
      console.log('@_courseDataSource 为 null_@');
      return;
    }
    let nextTaskIndex = -1;

    if (_dataSource.length > 0) {
      nextTaskIndex = _dataSource.findIndex(
        (obj) => obj.courseSort > taskItem.courseSort,
      );
    }

    if (nextTaskIndex === -1) {
      WToast.show({data: '无后续实习'});
      console.log('@_后续无实习_@');
      return;
    }

    const nextTask = _dataSource[nextTaskIndex];
    const planCompletionTime = this.calculatePlanCompletionTime(
      nextTask.courseDuration,
    );
    console.log('@_planCompletionTime_@', planCompletionTime);
    const requestUrl = '/biz/course/task/add';
    const requestParams = {
      courseId: nextTask.courseId,
      planCompletionTime: planCompletionTime,
      checkOutUserId: taskItem.checkOutUserId,
    };

    httpPost(requestUrl, requestParams, this.addCourseTaskCallBack);
  };

  calculatePlanCompletionTime = (courseDuration) => {
    const dateTime = new Date();
    console.log('Initial dateTime:', dateTime.toString()); // 添加日志

    // 检查 dateTime 是否是一个有效的 Date 对象
    if (isNaN(dateTime.getTime())) {
      console.error('Invalid dateTime object:', dateTime);
      return 'Invalid Date';
    }

    // 假设当前时间已经是 UTC+8，不需要额外增加8小时
    // 如果需要增加8小时，请确保当前时间是 UTC+0
    // dateTime.setHours(dateTime.getHours() + 8);

    dateTime.setDate(dateTime.getDate() + courseDuration);
    console.log('After adding courseDuration:', dateTime.toString()); // 添加日志

    // 检查 dateTime 是否是一个有效的 Date 对象
    if (isNaN(dateTime.getTime())) {
      console.error(
        'Invalid dateTime object after adding courseDuration:',
        dateTime,
      );
      return 'Invalid Date';
    }

    return this.formatDate(dateTime);
  };

  formatDate = (date) => {
    // 检查 date 是否是一个有效的 Date 对象
    if (isNaN(date.getTime())) {
      console.error('Invalid date object in formatDate:', date);
      return 'Invalid Date';
    }

    const Y = date.getFullYear() + '-';
    const M =
      (date.getMonth() + 1 < 10
        ? '0' + (date.getMonth() + 1)
        : date.getMonth() + 1) + '-';
    const D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    console.log('Formatted date:', Y + M + D); // 添加日志
    return Y + M + D;
  };
  // 开始实习按操作的回调
  addCourseTaskCallBack = (response) => {
    if (response.code == 200 && response.data) {
      var _dataSource = this.state.courseDataSource;
      let outPut = _dataSource.filter(
        (item) => item.courseId == response.data.courseId,
      );
      // console.log("@_addCourseTaskCallBack_@", JSON.stringify(response, null, 6));
      // console.log("@_outPut_@", JSON.stringify(outPut, null, 6));
      WToast.show({
        data:
          '任务' +
          outPut[0].courseSort +
          ' ' +
          outPut[0].courseName +
          '实习任务已开始',
      });
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  callBackSelectQryStartDateValue(value) {
    // console.log("==========提交时间选择结果：", value)
    if (!value) {
      return;
    }
    this.setState({
      selectedQryStartDate: value,
    });
    if (value && value.length) {
      var qryStartTime = '';
      var vartime;
      for (var index = 0; index < value.length; index++) {
        vartime = value[index];
        if (index === 0) {
          qryStartTime += vartime;
        } else {
          qryStartTime += '-' + vartime;
        }
      }
      this.setState({
        qryStartTime: qryStartTime,
      });
      let loadUrl = '/biz/course/task/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        departmentId: this.state.selDepartmentId,
        checkOutUserId: this.state.selStaffId,
        qryStartTime: qryStartTime,
        taskState:
          this.state.selTaskStateCode === 'all'
            ? null
            : this.state.selTaskStateCode,
      };
      httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  // 部门
  renderDepartmentRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selDepartmentId: item.departmentId,
            selDepartmentName: item.departmentName,
            selDepartmentStaffDataSource: item.departmentUserDTOList,
            selStaffId: null,
            selStaffName: null,
          });
        }}>
        <View
          key={'department_' + item.departmentId}
          style={[
            item.departmentId === this.state.selDepartmentId
              ? CommonStyle.choseToSearchItemsSelectedViewColor
              : CommonStyle.choseToSearchItemsViewColor,
            CommonStyle.choseToSearchItemsViewSize,
          ]}>
          <Text
            style={[
              item.departmentId === this.state.selDepartmentId
                ? CommonStyle.choseToSearchItemsSelectedTextStyle
                : CommonStyle.choseToSearchItemsTextStyle,
            ]}>
            {item.departmentName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderDepartmentStaffRow = (item, index) => {
    return (
      <View key={item.jobUserId}>
        <TouchableOpacity
          onPress={() => {
            this.setState({
              selStaffId: item.userId,
              selStaffName: item.staffName,
            });
          }}>
          <View
            key={'jobuser_' + item.jobUserId}
            style={[
              item.userId === this.state.selStaffId
                ? CommonStyle.choseToSearchItemsSelectedViewColor
                : CommonStyle.choseToSearchItemsViewColor,
              CommonStyle.choseToSearchItemsViewSize,
            ]}>
            <Text
              style={[
                item.userId === this.state.selStaffId
                  ? CommonStyle.choseToSearchItemsSelectedTextStyle
                  : CommonStyle.choseToSearchItemsTextStyle,
              ]}>
              {item.staffName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  renderTaskStateRow = (item, index) => {
    return (
      <View key={item.stateCode}>
        <TouchableOpacity
          onPress={() => {
            let selTaskStateCode = item.stateCode;
            this.setState({
              selTaskStateCode: selTaskStateCode,
            });
            let loadUrl = '/biz/course/task/list';
            let loadRequest = {
              currentPage: 1,
              pageSize: this.state.pageSize,
              taskState: selTaskStateCode === 'all' ? null : selTaskStateCode,
              departmentId: this.state.selDepartmentId,
              qryStartTime: this.state.qryStartTime,
              checkOutUserId: this.state.selStaffId,
            };
            // console.log("==========selTaskStateCode：", selTaskStateCode)
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
          }}>
          <View
            key={item.stateCode}
            style={[CommonStyle.tabItemViewStyle, {flexDirection: 'row'}]}>
            <Text
              style={[
                item.stateCode === this.state.selTaskStateCode
                  ? {
                      color: '#255BDA',
                      fontSize: 16,
                      fontWeight: '500',
                      textAlign: 'center',
                      paddingBottom: 12,
                      borderColor: '#255BDA',
                      borderBottomWidth: 2,
                      paddingLeft: 3,
                      paddingRight: 3,
                    }
                  : {
                      color: '#2B333F',
                      fontSize: 16,
                      fontWeight: '500',
                      textAlign: 'center',
                      paddingBottom: 12,
                    },
              ]}>
              {item.stateName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // 显示搜索项目
  showSearchItemSelect() {
    if (
      !this.state.departmentDataSource ||
      this.state.departmentDataSource.length < 1
    ) {
      WToast.show({data: '请先添加部门'});
      return;
    }
    this.setState({
      showSearchItemBlock: true,
    });
  }

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="实习跟踪"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        {/* 时间检索*/}

        {/* 状态检索 */}
        <View
          style={[
            CommonStyle.headViewStyle,
            {width: screenWidth, borderWidth: 0, paddingRight: 10},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={{
              width: '100%',
              flexWrap: 'wrap',
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            {this.state.taskStateDataSource &&
            this.state.taskStateDataSource.length > 0 ? (
              this.state.taskStateDataSource.map((item, index) => {
                return this.renderTaskStateRow(item);
              })
            ) : (
              <View />
            )}
          </View>

          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
            }}>
            <View
              style={{
                flexWrap: 'wrap',
                flexDirection: 'row',
                width: '65%',
              }}>
              <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                {this.state.showSearchItemBlock ? (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                      {this.state.selDepartmentId &&
                      this.state.selDepartmentName
                        ? this.state.selDepartmentName
                        : '选择部门'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchClosedIconSize]}
                      source={require('../../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                  </View>
                ) : (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                      {this.state.selDepartmentId &&
                      this.state.selDepartmentName
                        ? this.state.selDepartmentName
                        : '选择部门'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchOpenedIconSize]}
                      source={require('../../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                  </View>
                )}
              </TouchableOpacity>
              {this.state.selStaffId && this.state.selStaffName ? (
                <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                  {this.state.showSearchItemBlock ? (
                    <View style={[CommonStyle.choseToSearchViewStyle]}>
                      <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                        {this.state.selStaffName}
                      </Text>
                    </View>
                  ) : (
                    <View style={[CommonStyle.choseToSearchViewStyle]}>
                      <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                        {this.state.selStaffName}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              ) : null}
            </View>
            <View style={styles.datePickerStyle}>
              <TouchableOpacity onPress={() => this.openQryStartDate()}>
                <Text style={{color: 'rgba(0,10,32,0.85)', fontSize: 14}}>
                  {!this.state.qryStartTime ? '时间' : this.state.qryStartTime}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <View>
          {/* 部门检索 */}
          {this.state.showSearchItemBlock ? (
            <View
              style={[
                CommonStyle.choseToSearchBigBoxViewStyle,
                {
                  height: ifIphoneXContentViewDynamicHeight(
                    this.state.topBlockLayoutHeight,
                  ),
                },
              ]}>
              <View style={CommonStyle.heightLimited}>
                <ScrollView>
                  <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                    <View
                      style={[
                        {backgroundColor: 'rgba(255,255,255,1)'},
                        CommonStyle.choseToSearchItemsViewSize,
                      ]}>
                      <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                        部门：
                      </Text>
                    </View>
                    {this.state.departmentDataSource &&
                    this.state.departmentDataSource.length > 0
                      ? this.state.departmentDataSource.map((item, index) => {
                          return this.renderDepartmentRow(item);
                        })
                      : null}
                  </View>

                  {this.state.selDepartmentStaffDataSource &&
                  this.state.selDepartmentStaffDataSource.length > 0 ? (
                    <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                      <View
                        style={[
                          {backgroundColor: 'rgba(255,255,255,1)'},
                          CommonStyle.choseToSearchItemsViewSize,
                        ]}>
                        <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                          提交人：
                        </Text>
                      </View>
                      {this.state.selDepartmentStaffDataSource.map(
                        (item, index) => {
                          return this.renderDepartmentStaffRow(item);
                        },
                      )}
                    </View>
                  ) : null}
                </ScrollView>
              </View>

              <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]}>
                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    let loadUrl = '/biz/course/task/list';
                    let loadRequest = {
                      currentPage: 1,
                      pageSize: this.state.pageSize,
                      taskState:
                        this.state.selTaskStateCode === 'all'
                          ? null
                          : this.state.selTaskStateCode,
                      departmentId: this.state.selDepartmentId,
                      checkOutUserId: this.state.selStaffId,
                      qryStartTime: this.state.qryStartTime,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>
                      确定搜索
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}

          <View
            style={[
              CommonStyle.contentViewStyle,
              {
                height: ifIphoneXContentViewDynamicHeight(
                  this.state.topBlockLayoutHeight,
                ),
              },
            ]}>
            <FlatList
              data={this.state.dataSource}
              keyExtractor={(item) => item.courseTaskId}
              renderItem={({item, index}) => this.renderRow(item, index)}
              ListEmptyComponent={this.emptyComponent}
              // 自定义下拉刷新
              refreshControl={
                <RefreshControl
                  tintColor="#FF0000"
                  title="loading"
                  colors={['#FF0000', '#00FF00', '#0000FF']}
                  progressBackgroundColor="#FFFF00"
                  refreshing={this.state.refreshing}
                  onRefresh={() => {
                    this._loadFreshData();
                  }}
                />
              }
              // 底部加载
              ListFooterComponent={() => this.flatListFooterComponent()}
              onEndReached={() => this._loadNextData()}
            />
          </View>
          {/* 更多操作弹窗Modal */}
          <Modal
            animationType="fade"
            transparent={true}
            visible={this.state.moreModal}
            //  onShow={this.onShow.bind(this)}
            onRequestClose={() => console.log('onRequestClose...')}>
            <View
              style={[
                CommonStyle.fullScreenKeepOut,
                {backgroundColor: 'rgba(0,0,0,0.64)'},
              ]}>
              <View
                style={{
                  width: 291,
                  bottom: screenHeight / 2 - 80,
                  position: 'absolute',
                  backgroundColor: '#FFFFFF',
                  borderRadius: 10,
                }}>
                <View>
                  <TouchableOpacity
                    onPress={() => {
                      this.setState({
                        moreModal: false,
                      });
                      this.props.navigation.navigate('CourseTrackDetail', {
                        // 传递参数
                        dataItem: this.state.modalDataItem,
                        refresh: this.callBackFunction,
                      });
                    }}>
                    <View
                      style={[
                        {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                        this.state.modalDataItem.taskState != '0XX'
                          ? ''
                          : CommonStyle.disableViewStyle,
                      ]}>
                      {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                      <Text
                        style={{
                          color: 'rgba(0, 10, 32, 0.85)',
                          fontSize: 18,
                          lineHeight: 52,
                        }}>
                        详情
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>

                <View>
                  <TouchableOpacity
                    onPress={() => {
                      // console.log("courseItem=================",this.state.modalDataItem)
                      // 关闭/重启弹窗Modal
                      {
                        this.state.modalDataItem.taskState === '0CC'
                          ? // '重启'
                            this.setState({
                              moreModal: false,
                              resetModal: true,
                            })
                          : // '关闭'
                            this.setState({
                              moreModal: false,
                              closeModal: true,
                            });
                      }
                    }}>
                    <View
                      style={[
                        {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                        this.state.modalDataItem.taskState != '0XX'
                          ? ''
                          : CommonStyle.disableViewStyle,
                      ]}>
                      {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                      <Text
                        style={[
                          {
                            color: 'rgba(0, 10, 32, 0.85)',
                            fontSize: 18,
                            lineHeight: 52,
                          },
                        ]}>
                        {this.state.modalDataItem.taskState === '0CC'
                          ? '重启'
                          : '关闭'}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    width: 291,
                    height: 50,
                    alignItems: 'flex-end',
                    justifyContent: 'flex-end',
                    marginTop: 10,
                    borderTopWidth: 1,
                    borderColor: '#DFE3E8',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      this.setState({
                        moreModal: false,
                      });
                      WToast.show({data: '点击了取消'});
                    }}>
                    <View
                      style={{
                        width: 105,
                        height: 50,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          fontSize: 17,
                          fontWeight: '400',
                          color: '#1E6EFA',
                        }}>
                        取消
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
          {/* 关闭弹窗 */}
          <Modal
            animationType="fade"
            transparent={true}
            visible={this.state.closeModal}
            //  onShow={this.onShow.bind(this)}
            onRequestClose={() => console.log('onRequestClose...')}>
            <View
              style={[
                CommonStyle.fullScreenKeepOut,
                {backgroundColor: 'rgba(0,0,0,0.64)'},
              ]}>
              <View
                style={{
                  width: 291,
                  height: 122,
                  bottom: screenHeight / 2 - 80,
                  position: 'absolute',
                  backgroundColor: '#FFFFFF',
                  borderRadius: 10,
                }}>
                <View
                  style={{
                    height: 50,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: 10,
                  }}>
                  <Text style={{fontSize: 18}}>您确定要关闭该任务吗？</Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    width: 291,
                    height: 50,
                    marginTop: 10,
                    borderTopWidth: 1,
                    borderColor: '#DFE3E8',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      this.setState({
                        closeModal: false,
                      });
                      WToast.show({data: '点击了取消'});
                    }}>
                    <View
                      style={{
                        width: 145,
                        height: 50,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          fontSize: 17,
                          fontWeight: '400',
                          color: '#000A20',
                        }}>
                        取消
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => {
                      WToast.show({data: '点击了确定'});
                      this.setState({
                        closeModal: false,
                      });
                      if (
                        this.state.modalDataItem.taskState == '0BB' ||
                        this.state.modalDataItem.taskState == '0AA'
                      ) {
                        this.addCourseTask(this.state.modalDataItem);
                      }
                      this.closeCourseTask(this.state.modalDataItem);
                    }}>
                    <View
                      style={{
                        width: 145,
                        height: 50,
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderLeftWidth: 1,
                        borderColor: '#DFE3E8',
                      }}>
                      <Text
                        style={{
                          fontSize: 17,
                          fontWeight: '400',
                          color: '#1E6EFA',
                        }}>
                        确定
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
          {/* 重启弹窗 */}
          <Modal
            animationType="fade"
            transparent={true}
            visible={this.state.resetModal}
            //  onShow={this.onShow.bind(this)}
            onRequestClose={() => console.log('onRequestClose...')}>
            <View
              style={[
                CommonStyle.fullScreenKeepOut,
                {backgroundColor: 'rgba(0,0,0,0.64)'},
              ]}>
              <View
                style={{
                  width: 291,
                  height: 122,
                  bottom: screenHeight / 2 - 80,
                  position: 'absolute',
                  backgroundColor: '#FFFFFF',
                  borderRadius: 10,
                }}>
                <View
                  style={{
                    height: 50,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: 10,
                  }}>
                  <Text style={{fontSize: 18}}>您确定要重启该任务吗？</Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    width: 291,
                    height: 50,
                    marginTop: 10,
                    borderTopWidth: 1,
                    borderColor: '#DFE3E8',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      this.setState({
                        resetModal: false,
                      });
                      WToast.show({data: '点击了取消'});
                    }}>
                    <View
                      style={{
                        width: 145,
                        height: 50,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          fontSize: 17,
                          fontWeight: '400',
                          color: '#000A20',
                        }}>
                        取消
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => {
                      WToast.show({data: '点击了确定'});
                      this.setState({
                        resetModal: false,
                      });
                      this.resetCourseTask(this.state.modalDataItem);
                    }}>
                    <View
                      style={{
                        width: 145,
                        height: 50,
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderLeftWidth: 1,
                        borderColor: '#DFE3E8',
                      }}>
                      <Text
                        style={{
                          fontSize: 17,
                          fontWeight: '400',
                          color: '#1E6EFA',
                        }}>
                        确定
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
        </View>

        <BottomScrollSelect
          ref={'SelectQryStartDate'}
          callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  datePickerStyle: {
    height: 32,
    width: '35%',
    opacity: 0.6,
    borderRadius: 8,
    backgroundColor: 'rgba(242, 245, 252, 1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemContentTextStyle: {
    marginLeft: 12,
    marginRight: 16,
    marginTop: 3,
    lineHeight: 24,
  },
  titleViewStyle: {
    flexDirection: 'row',
    marginLeft: 12,
    marginRight: 16,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    fontSize: 14,
    lineHeight: 24,
    textAlign: 'left',
    textAlignVertical: 'top',
    color: 'rgba(0, 10, 32, 0.65)',
  },
  lineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    marginBottom: 6,
    borderBottomWidth: 0.5,
    borderColor: '#E8E9EC',
  },
});
