import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 170;

export default class BrickClassifyMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            brickClassId:"",
            brickClassName:"",
            brickClassSort:0,
            operate:"",
            pcBigScreenProductStatisticalFlag:"Y",
            longTermProduction:"Y"
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { brickClassId } = route.params;
            if (brickClassId) {
                console.log("========Edit==brickClassId:", brickClassId);
                this.setState({
                    brickClassId:brickClassId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/brick/class/get";
                loadRequest={'brickClassId':brickClassId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditMachineDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    loadEditMachineDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                brickClassId:response.data.brickClassId,
                brickClassName:response.data.brickClassName,
                brickClassSort:response.data.brickClassSort,
                pcBigScreenProductStatisticalFlag:response.data.pcBigScreenProductStatisticalFlag,
                longTermProduction:response.data.longTermProduction
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("BrickClassifyMgrList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>产品分类管理</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>产品分类管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveBrickClassify =()=> {
        console.log("=======saveBrickClassify");
        let toastOpts;
        if (!this.state.brickClassName) {
            toastOpts = getFailToastOpts("请输入产品分类名称");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/brick/class/add";
        if (this.state.brickClassId) {
            console.log("=========Edit===brickClassId", this.state.brickClassId)
            url= "/biz/brick/class/modify";
        }
        let requestParams={
            "brickClassId":this.state.brickClassId,
            "brickClassName":this.state.brickClassName,
            "brickClassSort":this.state.brickClassSort,
            "pcBigScreenProductStatisticalFlag":this.state.pcBigScreenProductStatisticalFlag,
            "longTermProduction":this.state.longTermProduction
        };
        httpPost(url, requestParams, this.saveBrickClassifyCallBack);
    }
    
    // 保存回调函数
    saveBrickClassifyCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '产品分类'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0}} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>产品分类名称</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({brickClassName:text})}
                        >
                            {this.state.brickClassName}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0}} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({brickClassSort:text})}
                        >
                            {this.state.brickClassSort}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0}} />
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'white'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>大屏工厂产品统计</Text>
                        </View>
                        <View >
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    pcBigScreenProductStatisticalFlag: "Y",
                                })
                            }}>
                                <View style={[this.state.pcBigScreenProductStatisticalFlag === 'Y' ?
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    :
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        paddingLeft:6,
                                        paddingRight:6,
                                        width: 100,
                                        // width: (screenWidth - 54)/2,
                                        borderRadius: 4,
                                    }
                                ]}>
                                    <Text style={[this.state.pcBigScreenProductStatisticalFlag === 'Y' ?
                                        {
                                            color: '#1E6EFA'
                                        }
                                        :
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>
                                        是
                                    </Text>
                                </View>
                                {/* <View style={CommonStyle.selectViewItem}>
                                    <Text style={[CommonStyle.selectTextItem, (this.state.pcBigScreenProductStatisticalFlag === 'Y') ? { color: '#FFF' } : { color: '#000' }]}>是</Text>
                                </View> */}
                            </TouchableOpacity>
                        </View>
                        <View >
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    pcBigScreenProductStatisticalFlag: "N",
                                })
                            }}>
                                <View style={[this.state.pcBigScreenProductStatisticalFlag === 'N' ?
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    :
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        paddingLeft:6,
                                        paddingRight:6,
                                        width: 100,
                                        // width: (screenWidth - 54)/2,
                                        borderRadius: 4,
                                    }
                                ]}>
                                    <Text style={[this.state.pcBigScreenProductStatisticalFlag === 'N' ?
                                        {
                                            color: '#1E6EFA'
                                        }
                                        :
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>
                                        否
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0}} />
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'white'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>是否长期生产</Text>
                        </View>
                        <View >
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    longTermProduction: "Y",
                                })
                            }}>
                                <View style={[this.state.longTermProduction === 'Y' ?
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    :
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        paddingLeft:6,
                                        paddingRight:6,
                                        width: 100,
                                        // width: (screenWidth - 54)/2,
                                        borderRadius: 4,
                                    }
                                ]}>
                                    <Text style={[this.state.longTermProduction === 'Y' ?
                                        {
                                            color: '#1E6EFA'
                                        }
                                        :
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>
                                        是
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View >
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    longTermProduction: "N",
                                })
                            }}>
                                <View style={[this.state.longTermProduction === 'N' ?
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    :
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        paddingLeft:6,
                                        paddingRight:6,
                                        width: 100,
                                        // width: (screenWidth - 54)/2,
                                        borderRadius: 4,
                                    }
                                ]}>
                                    <Text style={[this.state.longTermProduction === 'N' ?
                                        {
                                            color: '#1E6EFA'
                                        }
                                        :
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>
                                        否
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0}} />
                    <View style={{height:ifIphoneXContentViewHeight()-300, backgroundColor:'#F2F5FC', display:"flex"}}>
                        <View style={[styles.rowLabView]}>
                            <Text style={[CommonStyle.rowLabTextStyle, CommonStyle.boldTextStyle]}>
                                提示：
                                最多设置2个分类参于生产管理-工厂产品参于统计
                            </Text>
                        </View>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={styles.textContainerCancel} >
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={styles.textCancel}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveBrickClassify.bind(this)}>
                            <View style={styles.textContainerCertain}>
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={styles.textCertain}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },

    rowLabView:{
        height:ifIphoneXContentViewHeight(),
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:15,
        paddingRight:10,
        alignContent:'flex-end',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#FFFFFF',
        // borderWidth:1,
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
    },
    textCertain: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#FFFFFF',
        lineHeight: 24,
        marginTop:10,
        textAlign: 'center',
        // fontStyle: 'normal',
    },
    textCancel: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#404956',
        lineHeight: 24,
        marginTop:10,
        textAlign:'center'
        // fontStyle: 'normal',
    },
    textContainerCertain: {
        width: 180,
        height: 48,
        marginRight:8,
        backgroundColor: '#255BDA',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    textContainerCancel: {
        width: 180,
        height: 48,
        marginLeft:8,
        backgroundColor: '#FFFFFF',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        // paddingTop:5,
        // paddingBottom:5,
        marginTop:4,
        marginBottom:4,
        marginLeft:15, 
        // borderTopWidth:1,
        // borderTopColor:'#F1F1F1',
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:0,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'#E63633',
        marginLeft:4,
        marginRight:3
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:4,
        marginRight:3,
    },
})