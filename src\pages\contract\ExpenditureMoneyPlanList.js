import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class ExpenditureMoneyPlanList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            contractId:"",
            contractAmount:'',
            invoiceType:""
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { contractId, contractAmount } = route.params;
            if(contractAmount){
                this.setState({
                    contractAmount:contractAmount
                })
            }
            if (contractId) {
                console.log("=============contractId" + contractId);
                this.setState({
                    contractId:contractId
                })
                this.loadContractCollectMoneyPlanList(contractId);
            }
        }
        console.log(this.state.contractId);
        
    }

    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    // 回调函数
    callBackFunction=(contractId)=>{
        let url= "/biz/contract/collect/money/plan/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "contractId": contractId ? contractId : this.state.contractId,
        };
        console.log(loadRequest)
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=(contractId)=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/contract/collect/money/plan/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "contractId": contractId ? contractId : this.state.contractId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    _loadNextData=()=>{
      if ((this.state.currentPage-1) >= this.state.totalPage) {
          WToast.show({data:"已经是最后一页了，我们也是有底线的"});
          return;
      }
      this.setState({
          refreshing:true
      })
      this.loadCollectMoneyPlanList();
    }

    // 加载收款计划列表
    loadContractCollectMoneyPlanList=(contractId)=>{
        let url= "/biz/contract/collect/money/plan/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "contractId":contractId ? contractId : this.state.contractId
        };
        httpPost(url, loadRequest, this.loadCollectMoneyPlanListCallBack);
    }

    loadCollectMoneyPlanListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // console.log(response.data)
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 列表底部组件
    flatListFooterComponent=()=>{
      return(
          <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
      )
    }

    deleteCollectMoneyPlan=(planId)=>{
      console.log("=======delete=planId", planId);
      // 编写删除的请求
      let url= "/biz/contract/collect/money/plan/delete";
        let requestParams={'planId':planId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除回调
    deleteCallBack=(response)=>{
      if (response.code == 200 && response.data) {
        WToast.show({data:"删除完成"});
        this.callBackFunction();
      }
      else if (response.code == 401) {
          WToast.show({data:response.message});
          this.props.navigation.navigate("LoginView");
      }
      else {
          WToast.show({data:response.message});
      }
    }

    renderRow=(item, index)=>{
      return(
        <View key={item.planId} style={styles.innerViewStyle}>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>付款节点：{item.pointName}</Text>
            </View>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>发票类型：{item.invoiceType=='P'? "普通发票":"增值税专用发票"}</Text>
            </View>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>计划付款比例：{item.planProportion}%</Text>
            </View>    
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>计划付款金额：{item.planAmount}</Text>
            </View>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>计划付款日期：{item.planDate?item.planDate:"无"}</Text>
            </View>
            {/* <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>排序：{item.pointSort}</Text>
            </View> */}

            <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
              <TouchableOpacity onPress={()=>{
                Alert.alert('确认','您确定要删除该计划吗？',[
                  {
                      text:"取消", onPress:()=>{
                      WToast.show({data:'点击了取消'});
                      // this在这里可用，传到方法里还有问题
                      // this.props.navigation.goBack();
                      }
                  },
                  {
                      text:"确定", onPress:()=>{
                          WToast.show({data:'点击了确定'});
                          this.deleteCollectMoneyPlan(item.planId)
                      }
                  }
                ]);
              }}>
                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:80,flexDirection:'row'}]}>
                <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                  <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity onPress={()=>{
                  this.props.navigation.navigate("ExpenditureMoneyPlanAdd", 
                  {
                      // 传递参数
                      planId:item.planId,
                      dataSource: this.state.dataSource,
                      contractAmount:this.state.contractAmount,
                      // 传递回调函数
                      refresh: this.callBackFunction 
                  })
              }}>
                <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:'row'}]}>
                <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                  <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                </View>
              </TouchableOpacity>
            </View>
        </View>
      )
    }

    space(){
      return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("ExpenditureMoneyPlanAdd", 
                {
                    contractId: this.state.contractId,
                    contractAmount:this.state.contractAmount,
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                {/* <Text style={CommonStyle.headRightText}>新增</Text> */}
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>   
                <CommonHeadScreen title='付款计划'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    }
});