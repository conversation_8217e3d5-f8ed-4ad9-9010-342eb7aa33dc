import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,ScrollView,Image,KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
var CommonStyle = require('../../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class MyAskBugReleaseAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            releaseId: "",
            productName: "",
            productModel: "",
            productMaterial: "",
            productWeight: "",
            productionAddr: "",
            productExplain: "",
            contactPerson: "",
            contactTel: "",
            operate:"",
            topBlockLayoutHeight:0,
            productTypeList:[
                {
                    "typeId":0,
                    "typeName":"制品求购",
                    "typeCode":"P"
                },
                {
                    "typeId":1,
                    "typeName":"原料求购",
                    "typeCode":"M"
                }
            ],
            productType:"P",
            expDate: "",
            selectExpDate: [],
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { releaseId } = route.params;
            if (releaseId) {
                console.log("=============releaseId" + releaseId + "");
                this.setState({
                    releaseId:releaseId,
                    operate:"编辑"
                })
                let loadTypeUrl= "/biz/product/release/get";
                let loadRequest={'releaseId':releaseId};
                httpPost(loadTypeUrl, loadRequest, this.loadMyAskBugReleaseCallBack);
            }
            else {
                this.setState({
                    operate:"发布"
                })
                const d = new Date();
                //获取当前后三个月的日期
                const SY=d.getFullYear();
                const SM=d.getMonth()+1;
                const SD=d.getDate();
                //结果
                const mydate =new Date(SY,SM+3,SD);
                const yyyy = mydate.getFullYear();
                let MM = ("0" + mydate.getMonth()).slice(-2);
                let dd = ("0" + mydate.getDate()).slice(-2);;
                //如果月份是2月，就进行闰年和非闰年处理，防止出现类似2月30的情况
                if (MM==2 && dd>28){
                if (yyyy%4==0){
                    dd = 29;
                }else {
                    dd = 28;
                }
                }
                console.log(yyyy,'-',MM,'-',dd);
                this.setState({
                    selectExpDate: [yyyy,MM,dd],
                    expDate:`${yyyy}-${MM}-${dd}`
                })
            }
        }
    }
    loadMyAskBugReleaseCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                productName:response.data.productName,
                productModel:response.data.productModel,
                productMaterial:response.data.productMaterial,
                productWeight:response.data.productWeight,
                productionAddr:response.data.productionAddr,
                productExplain:response.data.productExplain,
                contactPerson:response.data.contactPerson,
                contactTel:response.data.contactTel,
                productType:response.data.productType,
            })
            var expDate = response.data.expDate?response.data.expDate.split(" "):null;
            if(null != expDate){
                this.setState({
                    expDate:expDate[0],
                    selectExpDate:expDate[0].split("-")
                })
            }
            console.log("expDate==",expDate)
        }
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("MyAskBugRelease")
            }}>
                <Text style={CommonStyle.headRightText}>我的求购</Text>
            </TouchableOpacity>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveMyAskBugRelease =()=> {
        console.log("=======saveSupplier");
        let toastOpts;
        if (!this.state.productName) {
            toastOpts = getFailToastOpts("请填写产品名称");
            WToast.show(toastOpts)
            return;
        }
        if(this.state.productType == "P"){
            if (!this.state.productModel) {
                toastOpts = getFailToastOpts("请填写型号");
                WToast.show(toastOpts)
                return;
            }
            if (!this.state.productMaterial) {
                toastOpts = getFailToastOpts("请填写材质");
                WToast.show(toastOpts)
                return;
            }
        }
        if (!this.state.productWeight) {
            toastOpts = getFailToastOpts("请填写数量");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.productionAddr) {
            toastOpts = getFailToastOpts("请填写期望产地");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contactPerson) {
            toastOpts = getFailToastOpts("请填写联系人");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contactTel) {
            toastOpts = getFailToastOpts("请填写联系电话");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/product/release/add";
        if (this.state.releaseId) {
            console.log("=========Edit===releaseId", this.state.releaseId)
            url= "/biz/product/release/modify";
        }
        let requestParams={
            releaseId:this.state.releaseId,
            productName: this.state.productName,
            productModel: this.state.productType == "P" ? this.state.productModel : null,
            productMaterial:this.state.productType == "P" ? this.state.productMaterial : null,
            productWeight:this.state.productWeight,
            productionAddr:this.state.productionAddr,
            productExplain:this.state.productExplain,
            contactPerson:this.state.contactPerson,
            contactTel:this.state.contactTel,
            releaseType:"P",
            userId:constants.loginUser.userId,
            productType:this.state.productType,
            expDate:this.state.expDate + " 23:59:59",
            auditState:"1",
        };
        httpPost(url, requestParams, this.saveMyAskBugReleaseCallBack);
    }

    // 保存回调函数
    saveMyAskBugReleaseCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    productTypeRow=(item, index)=>{
        return (
            <View key={item.typeId} >
                <TouchableOpacity onPress={()=>{
                    var typeCode = item.typeCode;
                    this.setState({
                        productType:typeCode
                    })
                }}>
                    <View key={item.typeCode} style={[item.typeCode===this.state.productType? [CommonStyle.selectedBlockItemViewStyle,
                        {backgroundColor:'#FFF',borderBottomWidth:2,borderBottomColor:'#FC783D',borderRadius:0,paddingBottom:0,marginBottom:0}] : CommonStyle.blockItemViewStyle,{paddingLeft:8,backgroundColor:'#FFF',paddingBottom:0,marginBottom:0.8}]}>
                        <Text style={[item.typeCode===this.state.productType? styles.selectedBlockItemTextStyle : styles.blockItemTextStyle]}>
                            {item.typeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    openExpDate() {
        this.refs.SelectExpDate.showDate(this.state.selectExpDate)
    }
    callBackSelectExpDateValue(value) {
        console.log("==========失效时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectExpDate: value
        })
        if (this.state.selectExpDate && this.state.selectExpDate.length) {
            var expDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectExpDate.length; index++) {
                vartime = this.state.selectExpDate[index];
                if (index === 0) {
                    expDate += vartime;
                }
                else {
                    expDate += "-" + vartime;
                }
            }
            this.setState({
                expDate: expDate
            })
            console.log("==========expDate", (expDate + " 23:59:59" ))
        }
        
    }


    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle,{backgroundColor:"#F5F5F5"}]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '求购'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[{marginTop:0,backgroundColor:'#FFF'}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop: 5,marginBottom:5, index: 1000, flexWrap: 'wrap', flexDirection: 'row' ,borderBottomColor:'#33333333',borderBottomWidth:1,alignItems:'flex-end'}}>
                        {
                            (this.state.productTypeList && this.state.productTypeList.length > 0)
                                ?
                                this.state.productTypeList.map((item, index) => {
                                    return this.productTypeRow(item)
                                })
                                : <View />
                        }
                    </View>
                </View>
                <ScrollView style={[CommonStyle.formContentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>产品名称</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入产品名称'}
                            onChangeText={(text) => this.setState({ productName: text })}
                        >
                            {this.state.productName}
                        </TextInput>
                    </View>
                    {
                        this.state.productType == "P"
                        ?
                        <View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>型号</Text>
                                </View>
                                <TextInput
                                    //keyboardType='text'
                                    style={styles.inputRightText}
                                    placeholder={'请输入型号'}
                                    onChangeText={(text) => this.setState({ productModel: text })}
                                >
                                    {this.state.productModel}
                                </TextInput>
                            </View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>材质</Text>
                                </View>
                                <TextInput
                                    //keyboardType='text'
                                    style={styles.inputRightText}
                                    placeholder={'请输入材质'}
                                    onChangeText={(text) => this.setState({ productMaterial: text })}
                                >
                                    {this.state.productMaterial}
                                </TextInput>
                            </View>
                        </View>
                        :null
                    }
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>数量（吨）</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入数量'}
                            onChangeText={(text) => this.setState({ productWeight: text })}
                        >
                            {this.state.productWeight}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>期望产地</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入目标产地'}
                            onChangeText={(text) => this.setState({ productionAddr: text })}
                        >
                            {this.state.productionAddr}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabNameTextStyle,{marginLeft:15}]}>说明</Text>
                        </View>
                        <TextInput 
                            style={[styles.inputRightText,{height:100}]}
                            multiline={true}
                            textAlignVertical="top"
                            placeholder={'请输入求购相关说明（最多100个字符）'}
                            onChangeText={(text) => this.setState({productExplain:text})}
                        >
                            {this.state.productExplain}
                        </TextInput>
                    </View>
                     <View style={[styles.inputRowStyle,{marginTop:10}]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入联系人'}
                            onChangeText={(text) => this.setState({ contactPerson: text })}
                        >
                            {this.state.contactPerson}
                        </TextInput>
                    </View> 
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入联系电话'}
                            onChangeText={(text) => this.setState({ contactTel: text })}
                        >
                            {this.state.contactTel}
                        </TextInput>
                    </View> 
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={[styles.leftLabNameTextStyle,{}]}>失效时间</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openExpDate()}>
                            <View style={[styles.inputRightText,{marginTop:10}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.expDate ? "请选择失效时间" : this.state.expDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={[CommonStyle.btnRowStyle,{flexDirection:'column',alignItems:'center'}]}>
                        <TouchableOpacity onPress={this.saveMyAskBugRelease.bind(this)}>
                            <View style={[styles.btnRowRightSaveBtnView]}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确认</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[styles.btnRowLeftCancelBtnView]} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect
                        ref={'SelectExpDate'}
                        callBackDateValue={this.callBackSelectExpDateValue.bind(this)}
                    />
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        borderBottomColor:'#33333315',
        borderBottomWidth:0.8,
    },

    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    btnRowRightSaveBtnView:{
        backgroundColor:'#559ff3',
        alignItems:'center',
        // alignContent:'center',
        justifyContent:'center',
        borderRadius:6,
        flexDirection:'column',
        width:screenWidth - 40,
        height:45,
        // marginRight:35,
        marginTop:15
    },
    btnRowLeftCancelBtnView:{
        backgroundColor:'#FFFFFF',
        alignItems:'center',
        justifyContent:'center',
        borderWidth:1,
        borderColor:'#A0A0A0',
        borderRadius:6,
        flexDirection:'column',
        width:screenWidth - 40,
        height:45,
        // marginLeft:35,
        marginTop:15
    },
    
    selectedBlockItemTextStyle: {
        fontSize:19,
        color:'#FC783D',
        marginBottom:5
    },
    blockItemTextStyle: {
        fontSize:17,
        color:'#33333399',
        marginBottom:5
    },
   
});