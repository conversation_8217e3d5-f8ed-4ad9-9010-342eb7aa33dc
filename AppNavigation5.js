import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';

import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {Colors} from 'react-native/Libraries/NewAppScreen';
// import { from } from 'rxjs';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';

import Home from './src/pages/home/<USER>';
import WelcomePage from './src/pages/home/<USER>';
// import SemiFinished from './src/pages/semi_finished/SemiFinishedList'
// import EncastageList from './src/pages/encastage/EncastageList';
// import WarmManage from './src/pages/warm/List'
// import CheckOut from './src/pages/check_out/CheckOutList'

// import SemiFinishedAdd from './src/pages/semi_finished/SemiFinishedAdd';

import OrgMgrAdd from './src/pages/admin/OrgMgrAdd';
import OrgMgrList from './src/pages/admin/OrgMgrList';
import TenantAdd from './src/pages/admin/TenantAdd';
import TenantList from './src/pages/admin/TenantList';

import OutsourcingTenantAdd from './src/pages/admin/OutsourcingTenantAdd';
import OutsourcingTenantList from './src/pages/admin/OutsourcingTenantList';
import ProductionLineMgrAdd from './src/pages/admin/ProductionLineMgrAdd';
import ProductionLineMgrList from './src/pages/admin/ProductionLineMgrList';

import CollectMoneyActualAdd from './src/pages/contract/CollectMoneyActualAdd';
import CollectMoneyActualList from './src/pages/contract/CollectMoneyActualList';
import CollectMoneyPlanAdd from './src/pages/contract/CollectMoneyPlanAdd';
import CollectMoneyPlanList from './src/pages/contract/CollectMoneyPlanList';
import CollectMoneyPointAdd from './src/pages/contract/CollectMoneyPointAdd';
import CollectMoneyPointList from './src/pages/contract/CollectMoneyPointList';
import ContractAcceptMoneyMgr from './src/pages/contract/ContractAcceptMoneyMgr';
import ContractAdd from './src/pages/contract/ContractAdd';
import ContractList from './src/pages/contract/ContractList';
import ContractProgressDetail from './src/pages/contract/ContractProgressDetail';
import ContractProgressQuery from './src/pages/contract/ContractProgressQuery';
import ContractTracking from './src/pages/contract/ContractTracking';
import ContractTrackingAdd from './src/pages/contract/ContractTrackingAdd';
import ContractTrackingList from './src/pages/contract/ContractTrackingList';
import ExpenditureContractReport from './src/pages/contract/ExpenditureContractReport';
import OutsourcingProcess from './src/pages/contract/OutsourcingProcess';
import CustomerAdd from './src/pages/customer/CustomerAdd';
import CustomerList from './src/pages/customer/CustomerList';
import OrderAdd from './src/pages/order/OrderAdd';
import OrderList from './src/pages/order/OrderList';
import OrderOperateList from './src/pages/order/OrderOperateList';
import OrderScheduling from './src/pages/order/OrderScheduling';
import OrderSchedulingList from './src/pages/order/OrderSchedulingList';
import OrderStateTracking from './src/pages/order/OrderStateTracking';

import SemiFinishedAdd from './src/pages/semi_finished/SemiFinishedAdd';
import SemiFinishedList from './src/pages/semi_finished/SemiFinishedList';

import EncastageAdd from './src/pages/encastage/EncastageAdd';
import EncastageList from './src/pages/encastage/EncastageList';

import AddWarmRecord from './src/pages/warm/AddWarmRecord';
import FireRecord from './src/pages/warm/FireRecord';
import WarmRecordList from './src/pages/warm/WarmRecordList';

import UnLoadedKilnAdd from './src/pages/unloaded_kiln/UnLoadedKilnAdd';
import UnLoadedKilnList from './src/pages/unloaded_kiln/UnLoadedKilnList';

import CheckInList from './src/pages/check_in/CheckInList';

import CheckOutAdd from './src/pages/check_out/CheckOutAdd';
import CheckOutList from './src/pages/check_out/CheckOutList';

import BelongsProductionLineSetting from './src/pages/setting/BelongsProductionLineSetting';
import HelpCenterList from './src/pages/setting/HelpCenterList';
import MessageRemind from './src/pages/setting/MessageRemind';
import Profile from './src/pages/setting/Profile';
import ResetPwd from './src/pages/setting/ResetPwd';
import SettingHome from './src/pages/setting/SettingHome';

import SuggestionFeedbackAdd from './src/pages/setting/SuggestionFeedbackAdd';
import SuggestionFeedbackList from './src/pages/setting/SuggestionFeedbackList';

import SinteringAdd from './src/pages/sintering/SinteringAdd';
import SinteringList from './src/pages/sintering/SinteringList';

import DryKoleInMgrAdd from './src/pages/dry_kole/DryKoleInMgrAdd';
import DryKoleInMgrList from './src/pages/dry_kole/DryKoleInMgrList';
import DryKoleMgrAdd from './src/pages/dry_kole/DryKoleMgrAdd';
import DryKoleMgrList from './src/pages/dry_kole/DryKoleMgrList';
import DryKoleOutMgrAdd from './src/pages/dry_kole/DryKoleOutMgrAdd';
import DryKoleOutMgrList from './src/pages/dry_kole/DryKoleOutMgrList';
import DryKoleWasteCauseMgrAdd from './src/pages/dry_kole/DryKoleWasteCauseMgrAdd';
import DryKoleWasteCauseMgrList from './src/pages/dry_kole/DryKoleWasteCauseMgrList';

import LocationAreaAdd from './src/pages/package_storage/LocationAreaAdd';
import LocationAreaList from './src/pages/package_storage/LocationAreaList';
import StorageLocationAdd from './src/pages/package_storage/StorageLocationAdd';
import StorageLocationList from './src/pages/package_storage/StorageLocationList';

import BlockWorkStorageInMgrAdd from './src/pages/blockwork_storage/BlockWorkStorageInMgrAdd';
import BlockWorkStorageInMgrList from './src/pages/blockwork_storage/BlockWorkStorageInMgrList';
import BlockWorkStorageOutMgrAdd from './src/pages/blockwork_storage/BlockWorkStorageOutMgrAdd';
import BlockWorkStorageOutMgrAddDetail from './src/pages/blockwork_storage/BlockWorkStorageOutMgrAddDetail';
import BlockWorkStorageOutMgrList from './src/pages/blockwork_storage/BlockWorkStorageOutMgrList';
import InventoryBlockWorkContractList from './src/pages/blockwork_storage/InventoryBlockWorkContractList';
import InventoryBlockWorkDetailList from './src/pages/blockwork_storage/InventoryBlockWorkDetailList';
import InventoryBlockWorkList from './src/pages/blockwork_storage/InventoryBlockWorkList';
import InventoryBlockWorkPositionList from './src/pages/blockwork_storage/InventoryBlockWorkPositionList';
import EngineeringAcceptanceMgrAdd from './src/pages/package_storage/EngineeringAcceptanceMgrAdd';
import EngineeringAcceptanceMgrList from './src/pages/package_storage/EngineeringAcceptanceMgrList';
import EngineeringBackMgrAdd from './src/pages/package_storage/EngineeringBackMgrAdd';
import EngineeringBackMgrList from './src/pages/package_storage/EngineeringBackMgrList';
import InventoryAdjust from './src/pages/package_storage/InventoryAdjust';
import InventoryBrickTypeQuery from './src/pages/package_storage/InventoryBrickTypeQuery';
import InventoryDetailBrickClassifyList from './src/pages/package_storage/InventoryDetailBrickClassifyList';
import InventoryDetailBrickClassifySeriesList from './src/pages/package_storage/InventoryDetailBrickClassifySeriesList';
import InventoryDetailBrickTypeList from './src/pages/package_storage/InventoryDetailBrickTypeList';
import InventoryDetailList from './src/pages/package_storage/InventoryDetailList';
import InventoryLocationQuery from './src/pages/package_storage/InventoryLocationQuery';
import InventoryQuery from './src/pages/package_storage/InventoryQuery';
import InventoryStorageAdd from './src/pages/package_storage/InventoryStorageAdd';
import OutsourcingStorageOutAdd from './src/pages/package_storage/OutsourcingStorageOutAdd';
import OutsourcingStorageOutAddSelDetail from './src/pages/package_storage/OutsourcingStorageOutAddSelDetail';
import OutsourcingStorageOutList from './src/pages/package_storage/OutsourcingStorageOutList';
import ProductCheckMgrAdd from './src/pages/package_storage/ProductCheckMgrAdd';
import ProductCheckMgrList from './src/pages/package_storage/ProductCheckMgrList';
import StorageInAdd from './src/pages/package_storage/StorageInAdd';
import StorageInList from './src/pages/package_storage/StorageInList';
import StorageOutAdd from './src/pages/package_storage/StorageOutAdd';
import StorageOutAddSelDetail from './src/pages/package_storage/StorageOutAddSelDetail';
import StorageOutList from './src/pages/package_storage/StorageOutList';
import YieldQuery from './src/pages/package_storage/YieldQuery';

import PaymengApplyDetail from './src/pages/payment_mgr/PaymengApplyDetail';
import PaymentApplyAdd from './src/pages/payment_mgr/PaymentApplyAdd';
import PaymentApplyList from './src/pages/payment_mgr/PaymentApplyList';
import PaymentAudit from './src/pages/payment_mgr/PaymentAudit';
import PaymentAuditList from './src/pages/payment_mgr/PaymentAuditList';
import PaymentClassAdd from './src/pages/payment_mgr/PaymentClassAdd';
import PaymentClassList from './src/pages/payment_mgr/PaymentClassList';
import PaymentObjectAdd from './src/pages/payment_mgr/PaymentObjectAdd';
import PaymentObjectList from './src/pages/payment_mgr/PaymentObjectList';

import VerifyExternalResultAdd from './src/pages/verify/VerifyExternalResultAdd';
import VerifyExternalResultList from './src/pages/verify/VerifyExternalResultList';
import VerifyExternalSettingAdd from './src/pages/verify/VerifyExternalSettingAdd';
import VerifyExternalSettingList from './src/pages/verify/VerifyExternalSettingList';
import VerifyExternalStandardAdd from './src/pages/verify/VerifyExternalStandardAdd';
import VerifyExternalStandardList from './src/pages/verify/VerifyExternalStandardList';
import VerifyInternalResultAdd from './src/pages/verify/VerifyInternalResultAdd';
import VerifyInternalResultList from './src/pages/verify/VerifyInternalResultList';
import VerifyInternalSettingAdd from './src/pages/verify/VerifyInternalSettingAdd';
import VerifyInternalSettingList from './src/pages/verify/VerifyInternalSettingList';
import VerifyInternalStandardAdd from './src/pages/verify/VerifyInternalStandardAdd';
import VerifyInternalStandardList from './src/pages/verify/VerifyInternalStandardList';

import BrickClassifyMgrAdd from './src/pages/brick_type_classify/BrickClassifyMgrAdd';
import BrickClassifyMgrList from './src/pages/brick_type_classify/BrickClassifyMgrList';
import BrickClassifySeriesMgrAdd from './src/pages/brick_type_classify/BrickClassifySeriesMgrAdd';
import BrickClassifySeriesMgrList from './src/pages/brick_type_classify/BrickClassifySeriesMgrList';
import BrickTypeMgrAdd from './src/pages/brick_type_classify/BrickTypeMgrAdd';
import BrickTypeMgrList from './src/pages/brick_type_classify/BrickTypeMgrList';
import KilnCarMgrAdd from './src/pages/kiln_car/KilnCarMgrAdd';
import KilnCarMgrList from './src/pages/kiln_car/KilnCarMgrList';
import MachineMgrAdd from './src/pages/machine/MachineMgrAdd';
import MachineMgrList from './src/pages/machine/MachineMgrList';

import CheckDepartmentList from './src/pages/admin/CheckDepartmentList';
import CheckEquipmentList from './src/pages/admin/CheckEquipmentList';
import CheckEquipmentStateList from './src/pages/admin/CheckEquipmentStateList';
import DepartmentAdd from './src/pages/admin/DepartmentAdd';
import DepartmentList from './src/pages/admin/DepartmentList';
import DepartmentStaffMgrList from './src/pages/admin/DepartmentStaffMgrList';
import EquipmentMgrAdd from './src/pages/admin/EquipmentMgrAdd';
import EquipmentMgrList from './src/pages/admin/EquipmentMgrList';
import EquipmentStateMgrAdd from './src/pages/admin/EquipmentStateMgrAdd';
import EquipmentStateMgrList from './src/pages/admin/EquipmentStateMgrList';
import InformationConfigAdd from './src/pages/admin/InformationConfigAdd';
import InformationConfigList from './src/pages/admin/InformationConfigList';
import JobMgrAdd from './src/pages/admin/JobMgrAdd';
import JobMgrList from './src/pages/admin/JobMgrList';
import JobStaffMgrAdd from './src/pages/admin/JobStaffMgrAdd';
import JobStaffMgrList from './src/pages/admin/JobStaffMgrList';
import QuickMenuAdd from './src/pages/admin/QuickMenuAdd';
import QuickMenuList from './src/pages/admin/QuickMenuList';
import RoleAdd from './src/pages/admin/RoleAdd';
import RoleList from './src/pages/admin/RoleList';
import RoleMenuAdd from './src/pages/admin/RoleMenuAdd';
import RoleMenuList from './src/pages/admin/RoleMenuList';
import RoleUserAdd from './src/pages/admin/RoleUserAdd';
import RoleUserList from './src/pages/admin/RoleUserList';
import UngradedCauseMgrAdd from './src/pages/admin/UngradedCauseMgrAdd';
import UngradedCauseMgrList from './src/pages/admin/UngradedCauseMgrList';
import ProductSummart from './src/pages/report/ProductSummart';
import ProductionQtyQuery from './src/pages/report/ProductionQtyQuery';
import ProductionQuery from './src/pages/report/ProductionQuery';
import ConfigPreview from './src/pages/setting/ConfigPreview';
import HomeResourceDisplay from './src/pages/setting/HomeResourceDisplay';
import MemberContactConfig from './src/pages/setting/MemberContactConfig';
import MemberContactConfigAdd from './src/pages/setting/MemberContactConfigAdd';
import OrderPositionMgrAdd from './src/pages/setting/OrderPositionMgrAdd';
import OrderPositionMgrList from './src/pages/setting/OrderPositionMgrList';
import PortalStaffMgrAdd from './src/pages/setting/PortalStaffMgrAdd';
import PortalStaffMgrList from './src/pages/setting/PortalStaffMgrList';
import PortalTenantParam from './src/pages/setting/PortalTenantParam';
import PortalTenantParamAdd from './src/pages/setting/PortalTenantParamAdd';
import PortalTenantParamData from './src/pages/setting/PortalTenantParamData';
import PortalTenantParamDataAdd from './src/pages/setting/PortalTenantParamDataAdd';
import PortalTenantParamItem from './src/pages/setting/PortalTenantParamItem';
import TenantEnterpriseAdd from './src/pages/setting/TenantEnterpriseAdd';
import TenantEnterpriseList from './src/pages/setting/TenantEnterpriseList';
import WorkingShiftMgrAdd from './src/pages/setting/WorkingShiftMgrAdd';
import WorkingShiftMgrList from './src/pages/setting/WorkingShiftMgrList';
import WorkingShiftRelStaffMgr from './src/pages/setting/WorkingShiftRelStaffMgr';

import CheckClassifyMaterialCheckoutList from './src/pages/material/CheckClassifyMaterialCheckoutList';
import CheckClassifyMaterialInventoryList from './src/pages/material/CheckClassifyMaterialInventoryList';
import CheckClassifyMaterialPurchaseList from './src/pages/material/CheckClassifyMaterialPurchaseList';
import CheckClassifyMaterialStorageList from './src/pages/material/CheckClassifyMaterialStorageList';
import MaterialCheckoutAdd from './src/pages/material/MaterialCheckoutAdd';
import MaterialCheckoutList from './src/pages/material/MaterialCheckoutList';
import MaterialClassifyAdd from './src/pages/material/MaterialClassifyAdd';
import MaterialClassifyList from './src/pages/material/MaterialClassifyList';
import MaterialInventoryList from './src/pages/material/MaterialInventoryList';
import MaterialSmallClassifyAdd from './src/pages/material/MaterialSmallClassifyAdd';
import MaterialSmallClassifyList from './src/pages/material/MaterialSmallClassifyList';
import MaterialSpecAdd from './src/pages/material/MaterialSpecAdd';
import MaterialSpecList from './src/pages/material/MaterialSpecList';

import MaterialInventoryInAdd from './src/pages/material/MaterialInventoryInAdd';
import MaterialInventoryInList from './src/pages/material/MaterialInventoryInList';
import MaterialInventoryOutAdd from './src/pages/material/MaterialInventoryOutAdd';
import MaterialInventoryOutList from './src/pages/material/MaterialInventoryOutList';

import ExpenditureContractAdd from './src/pages/contract/ExpenditureContractAdd';
import ExpenditureContractList from './src/pages/contract/ExpenditureContractList';
import ExpenditureMoneyActualAdd from './src/pages/contract/ExpenditureMoneyActualAdd';
import ExpenditureMoneyActualList from './src/pages/contract/ExpenditureMoneyActualList';
import ExpenditureMoneyPlanAdd from './src/pages/contract/ExpenditureMoneyPlanAdd';
import ExpenditureMoneyPlanList from './src/pages/contract/ExpenditureMoneyPlanList';
import ExpenditureMoneyPointAdd from './src/pages/contract/ExpenditureMoneyPointAdd';
import ExpenditureMoneyPointList from './src/pages/contract/ExpenditureMoneyPointList';
import AuditCcConfigurationAdd from './src/pages/material/AuditCcConfigurationAdd';
import AuditCcConfigurationList from './src/pages/material/AuditCcConfigurationList';
import AuditConfigMgrAdd from './src/pages/material/AuditConfigMgrAdd';
import AuditConfigMgrList from './src/pages/material/AuditConfigMgrList';
import AuditPointAdd from './src/pages/material/AuditPointAdd';
import AuditPointList from './src/pages/material/AuditPointList';
import InventoryAuditBacklogDetail from './src/pages/material/InventoryAuditBacklogDetail';
import MaterialAudit from './src/pages/material/MaterialAudit';
import MaterialAuditBacklogDetail from './src/pages/material/MaterialAuditBacklogDetail';
import MaterialAuditBacklogList from './src/pages/material/MaterialAuditBacklogList';
import MaterialAuditDoneList from './src/pages/material/MaterialAuditDoneList';
import MaterialInventoryAudit from './src/pages/material/MaterialInventoryAudit';
import MaterialPurchaseAdd from './src/pages/material/MaterialPurchaseAdd';
import MaterialPurchaseList from './src/pages/material/MaterialPurchaseList';
import MaterialStorageAdd from './src/pages/material/MaterialStorageAdd';
import MaterialStorageList from './src/pages/material/MaterialStorageList';
import NaturalGasFlowMgrAdd from './src/pages/material/NaturalGasFlowMgrAdd';
import NaturalGasFlowMgrList from './src/pages/material/NaturalGasFlowMgrList';
import NaturalGasPurchaseAdd from './src/pages/material/NaturalGasPurchaseAdd';
import NaturalGasPurchaseList from './src/pages/material/NaturalGasPurchaseList';
import SupplierMgrAdd from './src/pages/material/SupplierMgrAdd';
import SupplierMgrList from './src/pages/material/SupplierMgrList';

import AskBugReleaseMarket from './src/pages/material_industry/product_sale_bug_release/AskBugReleaseMarket';
import MyAskBugRelease from './src/pages/material_industry/product_sale_bug_release/MyAskBugRelease';
import MyAskBugReleaseAdd from './src/pages/material_industry/product_sale_bug_release/MyAskBugReleaseAdd';
import MyProductSaleRelease from './src/pages/material_industry/product_sale_bug_release/MyProductSaleRelease';
import MyProductSaleReleaseAdd from './src/pages/material_industry/product_sale_bug_release/MyProductSaleReleaseAdd';
import ProductReleaseAskBuyAuditList from './src/pages/material_industry/product_sale_bug_release/ProductReleaseAskBuyAuditList';
import ProductReleaseAuditList from './src/pages/material_industry/product_sale_bug_release/ProductReleaseAuditList';
import ProductReleaseMarket from './src/pages/material_industry/product_sale_bug_release/ProductReleaseMarket';

import AskForPurchase from './src/pages/material_industry/product_sale_bug_release/AskForPurchase';
import Product from './src/pages/material_industry/product_sale_bug_release/Product';
import ProductHome from './src/pages/material_industry/product_sale_bug_release/ProductHome';

import SortingCheckoutAdd from './src/pages/material/SortingCheckoutAdd';
import SortingCheckoutList from './src/pages/material/SortingCheckoutList';
import SortingStorageAdd from './src/pages/material/SortingStorageAdd';
import SortingStorageList from './src/pages/material/SortingStorageList';
import SpecCheckoutAdd from './src/pages/material/SpecCheckoutAdd';
import SpecCheckoutList from './src/pages/material/SpecCheckoutList';
import SpecStorageAdd from './src/pages/material/SpecStorageAdd';
import SpecStorageList from './src/pages/material/SpecStorageList';

import CourseMgrAdd from './src/pages/digital_employees/CourseMgrAdd';
import CourseMgrList from './src/pages/digital_employees/CourseMgrList';
import CourseScheduling from './src/pages/digital_employees/CourseScheduling';
import CourseSchedulingAdd from './src/pages/digital_employees/CourseSchedulingAdd';
import CourseSchedulingTable from './src/pages/digital_employees/CourseSchedulingTable';
import DailyAdd from './src/pages/digital_employees/DailyAdd';
import DailyDetail from './src/pages/digital_employees/DailyDetail';
import DailyList from './src/pages/digital_employees/DailyList';
import DailyMessageAdd from './src/pages/digital_employees/DailyMessageAdd';
import DailyMessageList from './src/pages/digital_employees/DailyMessageList';
import DigitalEmployeesHome from './src/pages/digital_employees/DigitalEmployeesHome';
import DocumentLibraryMgrList from './src/pages/digital_employees/DocumentLibraryMgrList';
import ExamApply from './src/pages/digital_employees/ExamApply';
import ExamApplyAdd from './src/pages/digital_employees/ExamApplyAdd';
import ExamApplyAudit from './src/pages/digital_employees/ExamApplyAudit';
import ExamConfig from './src/pages/digital_employees/ExamConfig';
import ExamConfigAdd from './src/pages/digital_employees/ExamConfigAdd';
import FormCollection from './src/pages/digital_employees/FormCollection';
import GoodHarvestList from './src/pages/digital_employees/GoodHarvestList';
import HarvestCircleList from './src/pages/digital_employees/HarvestCircleList';
import HarvestDetail from './src/pages/digital_employees/HarvestDetail';
import HarvestGoodMgrList from './src/pages/digital_employees/HarvestGoodMgrList';
import HarvestMgrAdd from './src/pages/digital_employees/HarvestMgrAdd';
import HarvestMgrList from './src/pages/digital_employees/HarvestMgrList';
import MyBacklogDailyAdd from './src/pages/digital_employees/MyBacklogDailyAdd';
import MyBacklogDailyList from './src/pages/digital_employees/MyBacklogDailyList ';
import MyDocumentMgrList from './src/pages/digital_employees/MyDocumentMgrList';
import MyDoneDailyList from './src/pages/digital_employees/MyDoneDailyList';
import PointConfig from './src/pages/digital_employees/PointConfig';
import PointConfigAdd from './src/pages/digital_employees/PointConfigAdd';
import PointExchange from './src/pages/digital_employees/PointExchange';
import PointExchangeAdd from './src/pages/digital_employees/PointExchangeAdd';
import PointRanking from './src/pages/digital_employees/PointRanking';
import PointRecord from './src/pages/digital_employees/PointRecord';
import PointReward from './src/pages/digital_employees/PointReward';
import PointRewardAdd from './src/pages/digital_employees/PointRewardAdd';
import PortalTrackingAdd from './src/pages/digital_employees/PortalTrackingAdd';
import PortalTrackingList from './src/pages/digital_employees/PortalTrackingList';
import QueryDaily from './src/pages/digital_employees/QueryDaily';
import QueryHarvest from './src/pages/digital_employees/QueryHarvest';
import QueryMyScore from './src/pages/digital_employees/QueryMyScore';
import QueryPromotionPlan from './src/pages/digital_employees/QueryPromotionPlan';
import SYContactUs from './src/pages/digital_employees/SYContactUs';
import SYPointMall from './src/pages/digital_employees/SYPointMall';
import WorkDaily from './src/pages/digital_employees/WorkDaily';

import CustomerLeadAdd from './src/pages/sale_manage/CustomerLeadAdd';
import CustomerLeadList from './src/pages/sale_manage/CustomerLeadList';
import LeadAuditList from './src/pages/sale_manage/LeadAuditList';
import LeadTeleInvitationList from './src/pages/sale_manage/LeadTeleInvitationList';
import LeadVisitList from './src/pages/sale_manage/LeadVisitList';
import SaleopportunityAdd from './src/pages/sale_manage/SaleopportunityAdd';
import SaleopportunityList from './src/pages/sale_manage/SaleopportunityList';

import DocumentLibraryView from './src/pages/digital_employees/DocumentLibraryView';
import HarvestDiscussAdd from './src/pages/digital_employees/HarvestDiscussAdd';
import HarvestDiscussList from './src/pages/digital_employees/HarvestDiscussList';
import PromotionPlanAdd from './src/pages/digital_employees/PromotionPlanAdd';
import PromotionPlanDetail from './src/pages/digital_employees/PromotionPlanDetail';
import PromotionPlanList from './src/pages/digital_employees/PromotionPlanList';
import PromotionPlanSelUser from './src/pages/digital_employees/PromotionPlanSelUser';
import ScoreMgrAdd from './src/pages/digital_employees/ScoreMgrAdd';
import ScoreMgrCourseList from './src/pages/digital_employees/ScoreMgrCourseList';
import ScoreMgrList from './src/pages/digital_employees/ScoreMgrList';
import VideoLibraryMgrList from './src/pages/digital_employees/VideoLibraryMgrList';
import VideoLibraryView from './src/pages/digital_employees/VideoLibraryView';

import ChangePassword from './src/pages/college_recruiting/ChangePassword';
import CollegClassGradesAdd from './src/pages/college_recruiting/CollegClassGradesAdd';
import CollegClassGradesList from './src/pages/college_recruiting/CollegClassGradesList';
import CollegClassStudentList from './src/pages/college_recruiting/CollegClassStudentList';
import CollegPositionQuery from './src/pages/college_recruiting/CollegPositionQuery';
import CollegProfessionalAdd from './src/pages/college_recruiting/CollegProfessionalAdd';
import CollegProfessionalList from './src/pages/college_recruiting/CollegProfessionalList';
import CollegStudentAdd from './src/pages/college_recruiting/CollegStudentAdd';
import CollegStudentList from './src/pages/college_recruiting/CollegStudentList';
import CollegStudentResumeQuery from './src/pages/college_recruiting/CollegStudentResumeQuery';
import CollegeEvaluation from './src/pages/college_recruiting/CollegeEvaluation';
import ConfiguringTenants from './src/pages/college_recruiting/ConfiguringTenants';
import ConfiguringTenantsParam from './src/pages/college_recruiting/ConfiguringTenantsParam';
import ConfiguringTenantsParamItem from './src/pages/college_recruiting/ConfiguringTenantsParamItem';
import EnterpriseInvitedApply from './src/pages/college_recruiting/EnterpriseInvitedApply';
import EnterpriseInvitedInterView from './src/pages/college_recruiting/EnterpriseInvitedInterView';
import EnterprisePositionDetail from './src/pages/college_recruiting/EnterprisePositionDetail';
import EnterpriseRecruiterAdd from './src/pages/college_recruiting/EnterpriseRecruiterAdd';
import EnterpriseRecruiterList from './src/pages/college_recruiting/EnterpriseRecruiterList';
import EnterpriseResumeCollection from './src/pages/college_recruiting/EnterpriseResumeCollection';
import EnterprisecrHiringPositionAdd from './src/pages/college_recruiting/EnterprisecrHiringPositionAdd';
import EnterprisecrHiringPositionDetail from './src/pages/college_recruiting/EnterprisecrHiringPositionDetail';
import EnterprisecrHiringPositionList from './src/pages/college_recruiting/EnterprisecrHiringPositionList';
import Feedback from './src/pages/college_recruiting/Feedback';
import MemberManagementAdd from './src/pages/college_recruiting/MemberManagementAdd';
import MemberManagementDetail from './src/pages/college_recruiting/MemberManagementDetail';
import MemberManagementExamine from './src/pages/college_recruiting/MemberManagementExamine';
import MemberManagementExamineDetail from './src/pages/college_recruiting/MemberManagementExamineDetail';
import MemberManagementList from './src/pages/college_recruiting/MemberManagementList';
import MemberTypeAdd from './src/pages/college_recruiting/MemberTypeAdd';
import MemberTypeList from './src/pages/college_recruiting/MemberTypeList';
import NewItem from './src/pages/college_recruiting/NewItem';
import PersonalHonor from './src/pages/college_recruiting/PersonalHonor';
import PersonalInformation from './src/pages/college_recruiting/PersonalInformation';
import PortalEnterpriseAdd from './src/pages/college_recruiting/PortalEnterpriseAdd';
import PortalEnterpriseList from './src/pages/college_recruiting/PortalEnterpriseList';
import StudentInterViewInvited from './src/pages/college_recruiting/StudentInterViewInvited';
import StudentMyChance from './src/pages/college_recruiting/StudentMyChance';
import StudentMyInterView from './src/pages/college_recruiting/StudentMyInterView';
import StudentMyInterViewPreview from './src/pages/college_recruiting/StudentMyInterViewPreview';

import HLDepartmentAdd from './src/pages/hospital_logistics/HLDepartmentAdd';
import HLDepartmentList from './src/pages/hospital_logistics/HLDepartmentList';
import HLDepartmentStoreKeeperRelAdd from './src/pages/hospital_logistics/HLDepartmentStoreKeeperRelAdd';
import HLDepartmentStoreKeeperRelList from './src/pages/hospital_logistics/HLDepartmentStoreKeeperRelList';
import HLHospitalAdd from './src/pages/hospital_logistics/HLHospitalAdd';
import HLHospitalList from './src/pages/hospital_logistics/HLHospitalList';
import HLMaterialListingAdd from './src/pages/hospital_logistics/HLMaterialListingAdd';
import HLMaterialListingList from './src/pages/hospital_logistics/HLMaterialListingList';
import HLPortalSupplierAdd from './src/pages/hospital_logistics/HLPortalSupplierAdd';
import HLPortalSupplierList from './src/pages/hospital_logistics/HLPortalSupplierList';
import HLPortalUnitAdd from './src/pages/hospital_logistics/HLPortalUnitAdd';
import HLPortalUnitList from './src/pages/hospital_logistics/HLPortalUnitList';
import HLStorageInAddDetail from './src/pages/hospital_logistics/HLStorageInAddDetail';
import HLStorageInDAdd from './src/pages/hospital_logistics/HLStorageInDAdd';
import HLStorageInDList from './src/pages/hospital_logistics/HLStorageInDList';
import HLStorageInListDetail from './src/pages/hospital_logistics/HLStorageInListDetail';
import HLStorageInMAdd from './src/pages/hospital_logistics/HLStorageInMAdd';
import HLStorageInMList from './src/pages/hospital_logistics/HLStorageInMList';
import HLStorageOutMAdd from './src/pages/hospital_logistics/HLStorageOutMAdd';
import HLStorageOutMList from './src/pages/hospital_logistics/HLStorageOutMList';
import HLStorageOutTypeAdd from './src/pages/hospital_logistics/HLStorageOutTypeAdd';
import HLStorageOutTypeList from './src/pages/hospital_logistics/HLStorageOutTypeList';
import HLTransferModeAdd from './src/pages/hospital_logistics/HLTransferModeAdd';
import HLTransforModeList from './src/pages/hospital_logistics/HLTransforModeList';
// import HLStorageInDetailDList from './src/pages/hospital_logistics/HLStorageInDetailDList'
import HLDoctorAdd from './src/pages/hospital_logistics/HLDoctorAdd';
import HLDoctorList from './src/pages/hospital_logistics/HLDoctorList';
import HLMaterialInventoryDList from './src/pages/hospital_logistics/HLMaterialInventoryDList';
import HLMaterialInventoryMList from './src/pages/hospital_logistics/HLMaterialInventoryMList';
import HLMedicalInsuranceTypeAdd from './src/pages/hospital_logistics/HLMedicalInsuranceTypeAdd';
import HLMedicalInsuranceTypeList from './src/pages/hospital_logistics/HLMedicalInsuranceTypeList';
import HLMedicineAdd from './src/pages/hospital_logistics/HLMedicineAdd';
import HLMedicineInventoryDetailList from './src/pages/hospital_logistics/HLMedicineInventoryDetailList';
import HLMedicineInventoryMList from './src/pages/hospital_logistics/HLMedicineInventoryMList';
import HLMedicineList from './src/pages/hospital_logistics/HLMedicineList';
import HLMedicineStorageInAdd from './src/pages/hospital_logistics/HLMedicineStorageInAdd';
import HLMedicineStorageInAudit from './src/pages/hospital_logistics/HLMedicineStorageInAudit';
import HLMedicineStorageInAuditDetail from './src/pages/hospital_logistics/HLMedicineStorageInAuditDetail';
import HLMedicineStorageInDetailAdd from './src/pages/hospital_logistics/HLMedicineStorageInDetailAdd';
import HLMedicineStorageInDetailList from './src/pages/hospital_logistics/HLMedicineStorageInDetailList';
import HLMedicineStorageInList from './src/pages/hospital_logistics/HLMedicineStorageInList';
import HLMedicineStorageOutAdd from './src/pages/hospital_logistics/HLMedicineStorageOutAdd';
import HLMedicineStorageOutDetailAdd from './src/pages/hospital_logistics/HLMedicineStorageOutDetailAdd';
import HLMedicineStorageOutDetailList from './src/pages/hospital_logistics/HLMedicineStorageOutDetailList';
import HLMedicineStorageOutList from './src/pages/hospital_logistics/HLMedicineStorageOutList';
import HLPharmacyList from './src/pages/hospital_logistics/HLPharmacyList';
import HLSickPersonAdd from './src/pages/hospital_logistics/HLSickPersonAdd';
import HLSickPersonList from './src/pages/hospital_logistics/HLSickPersonList';
import HLStorageInAudit from './src/pages/hospital_logistics/HLStorageInAudit';
import HLStorageInAuditDetail from './src/pages/hospital_logistics/HLStorageInAuditDetail';
import HLStorageInAuditList from './src/pages/hospital_logistics/HLStorageInAuditList';
import HLStorageOutAddDetail from './src/pages/hospital_logistics/HLStorageOutAddDetail';
import HLStorageOutAudit from './src/pages/hospital_logistics/HLStorageOutAudit';
import HLStorageOutAuditDetail from './src/pages/hospital_logistics/HLStorageOutAuditDetail';
import HLStorageOutAuditList from './src/pages/hospital_logistics/HLStorageOutAuditList';
import HLStorageOutDAdd from './src/pages/hospital_logistics/HLStorageOutDAdd';
import HLStorageOutDList from './src/pages/hospital_logistics/HLStorageOutDList';
import HLStorageOutListDetail from './src/pages/hospital_logistics/HLStorageOutListDetail';
import HlMaterialInventoryDetailList from './src/pages/hospital_logistics/HlMaterialInventoryDetailList';
import HlPharmacyAdd from './src/pages/hospital_logistics/HlPharmacyAdd';
import LinkShareList from './src/pages/setting/LinkShareList';

import LoginView from './src/pages/LoginView';
import ModifyPwd from './src/pages/ModifyPwd';
import Privacy from './src/pages/Privacy';
import Register from './src/pages/Register';
import UserAgreement from './src/pages/UserAgreement';
// import HomeIconActive from './src/assets/icon/foot_home_icon.png'
// import HomeIconNormal from './src/assets/icon/foot_home_icon.png';

import Myself from './src/pages/setting/Myself';

// import HomeIconNormal from './src/assets/icon/bottom_navigate/workbench.png';
// import HomeIconActive from './src/assets/icon/bottom_navigate/workbench_lighted.png';

import './src/utils/Global';

import ReactNativeDemo from './src/pages/demo/ReactNativeDemo';
import AskBugGoods from './src/pages/material_industry/index/AskBugGoods';
import MyCenterOrLogin from './src/pages/material_industry/index/MyCenterOrLogin';
import NaicaiIndex from './src/pages/material_industry/index/NaicaiIndex';
import ProductDetail from './src/pages/material_industry/index/ProductDetail';
import ProductList from './src/pages/material_industry/index/ProductList';
import {ifIphoneXTabBarOptionHeight} from './src/utils/ScreenUtil';

import AIChat from './src/pages/digital_employees/AIChat';
import AssessApplyAdd from './src/pages/digital_employees/AssessApplyAdd';
import AssessApplyList from './src/pages/digital_employees/AssessApplyList';
import AssessAudit from './src/pages/digital_employees/AssessAudit';
import AssessAuditList from './src/pages/digital_employees/AssessAuditList';
import AssessClassAdd from './src/pages/digital_employees/AssessClassAdd';
import AssessClassList from './src/pages/digital_employees/AssessClassList';
import AssessQueryList from './src/pages/digital_employees/AssessQueryList';
import PointRecordList from './src/pages/digital_employees/PointRecordList';
import ViewDocument from './src/pages/digital_employees/ViewDocument';
import AskQuestionsQuery from './src/pages/digital_employees/ask_questions/AskQuestionsQuery';
import AskQuestionsSolveTrackingAdd from './src/pages/digital_employees/ask_questions/AskQuestionsSolveTrackingAdd';
import AskQuestionsSolveTrackingList from './src/pages/digital_employees/ask_questions/AskQuestionsSolveTrackingList';
import MyAskQuestionsAdd from './src/pages/digital_employees/ask_questions/MyAskQuestionsAdd';
import MyAskQuestionsList from './src/pages/digital_employees/ask_questions/MyAskQuestionsList';
import MyReceiveAdd from './src/pages/digital_employees/ask_questions/MyReceiveAdd';
import MyReceiveList from './src/pages/digital_employees/ask_questions/MyReceiveList';
import CourseLevelMgrAdd from './src/pages/digital_employees/course_mgr/CourseLevelMgrAdd';
import CourseLevelMgrList from './src/pages/digital_employees/course_mgr/CourseLevelMgrList';
import CourseLevelStaffMgrAdd from './src/pages/digital_employees/course_mgr/CourseLevelStaffMgrAdd';
import CourseLevelStaffMgrList from './src/pages/digital_employees/course_mgr/CourseLevelStaffMgrList';
import CourseTaskMgrDetail from './src/pages/digital_employees/course_mgr/CourseTaskMgrDetail';
import CourseTaskMgrList from './src/pages/digital_employees/course_mgr/CourseTaskMgrList';
import CourseTrackDetail from './src/pages/digital_employees/course_mgr/CourseTrackDetail';
import CourseTrackList from './src/pages/digital_employees/course_mgr/CourseTrackList';
import CourseTypeMgrAdd from './src/pages/digital_employees/course_mgr/CourseTypeMgrAdd';
import CourseTypeMgrList from './src/pages/digital_employees/course_mgr/CourseTypeMgrList';
import CourseVidioList from './src/pages/digital_employees/course_mgr/CourseVidioList';
import MyCourseDetail from './src/pages/digital_employees/course_mgr/MyCourseDetail';
import MyCourseList from './src/pages/digital_employees/course_mgr/MyCourseList';
import StudentCourseTrackDetail from './src/pages/digital_employees/course_mgr/StudentCourseTrackDetail';
import StudentCourseTrackDetailList from './src/pages/digital_employees/course_mgr/StudentCourseTrackDetailList';
import StudentCourseTrackList from './src/pages/digital_employees/course_mgr/StudentCourseTrackList';
import MaterialInventoryLocationChange from './src/pages/material/MaterialInventoryLocationChange';
import ReceiveRankDetail from './src/pages/material_industry/sale_data_show/ReceiveRankDetail';
import ReceiveTargetAndPerformance from './src/pages/material_industry/sale_data_show/ReceiveTargetAndPerformance';
import SaleDataOverView from './src/pages/material_industry/sale_data_show/SaleDataOverView';
import SalesRankDetail from './src/pages/material_industry/sale_data_show/SalesRankDetail';
import SalesTargetAndPerformance from './src/pages/material_industry/sale_data_show/SalesTargetAndPerformance';
import PermissionList from './src/pages/setting/PermissionList';
import PermissionUserAddList from './src/pages/setting/PermissionUserAddList';
import PermissionUserList from './src/pages/setting/PermissionUserList';

const RootStack = createStackNavigator();

const LoginStack = createStackNavigator();
const MainStack = createBottomTabNavigator();
// 首页
const HomeStack = createStackNavigator();
// 租户管理
const TenantStack = createStackNavigator();
// 半成品
const SemiFinishedStack = createStackNavigator();
// 装窑管理
const EncastageStack = createStackNavigator();
// 隧道窑高温区管理
const WarmManageStack = createStackNavigator();
// 出库管理
const CheckOutStack = createStackNavigator();
// 个人中心
const PersonalCenterStack = createStackNavigator();
// demo测试中心
const DEMOStack = createStackNavigator();
// 学社首页
const DigitalEmployeesHomeStack = createStackNavigator();

// 客户管理
const CustomerListStack = createStackNavigator();
// 订单管理
const OrderListStack = createStackNavigator();
// 排产管理
const OrderSchedulingListStack = createStackNavigator();
// 烧结管理
const SinteringListStack = createStackNavigator();
// 库存管理-入库管理
const StorageInListStack = createStackNavigator();
// 库存管理-出库管理
const StorageOutListStack = createStackNavigator();
// 窑车管理
const KilnCarMgrListStack = createStackNavigator();
// 机台管理
const MachineMgrListStack = createStackNavigator();
// 产品管理
const BrickClassifyMgrListStack = createStackNavigator();
// 角色管理
const RoleListStack = createStackNavigator();
// 合同管理
const ContractStack = createStackNavigator();
// 日报查询
const QueryDailyStack = createStackNavigator();
// 成果圈
const HarvestCircleStack = createStackNavigator();
// 优秀成果
const GoodHarvestStack = createStackNavigator();
// 文档库
const DocumentLibraryStack = createStackNavigator();
// 首页
const HomePageStack = createStackNavigator();
// 校招
const SchoolreCruitmentStack = createStackNavigator();
// 我的
const MyselfStack = createStackNavigator();
// 消息未读
const MessageRemindStack = createStackNavigator();

// 耐材销售购买页面-堆栈
const NaicaiIndexStack = new createBottomTabNavigator();
// 耐材销售购买页面 - 首页
const NaicaiIndexStackDefautStack = createStackNavigator();
// 耐材销售购买页面 - 产品
const NaicaiIndexStackSellGoodsStack = createStackNavigator();
// 耐材销售购买页面 - 求购
const NaicaiIndexStackAskBugGoodsStack = createStackNavigator();
// 耐材销售购买页面 - 我的
const NaicaiIndexStackMyCenterOrLoginStack = createStackNavigator();

//在图标上添加徽章
function IconWithBadge({icon, badgeCount, size}) {
  return (
    <View></View>
    // <View style={{ width: 24, height: 24, margin: 5 }}>
    //   <Image source={icon} style={{
    //     width: size,
    //     height: size
    //   }} />
    //   {badgeCount > 0 && (
    //     <View
    //       style={{
    //         // On React Native < 0.57 overflow outside of parent will not work on Android, see https://git.io/fhLJ8
    //         position: 'absolute',
    //         right: -6,
    //         top: -3,
    //         backgroundColor: 'red',
    //         borderRadius: 6,
    //         width: 12,
    //         height: 12,
    //         justifyContent: 'center',
    //         alignItems: 'center',
    //       }}
    //     >
    //       {/* <Text style={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>
    //         {badgeCount}
    //       </Text> */}
    //     </View>
    //   )}
    // </View>
  );
}

function HomeIconWithBadge(props) {
  // You should pass down the badgeCount in some other ways like React Context API, Redux, MobX or event emitters.
  return <IconWithBadge {...props} badgeCount={0} />;
}

// 登录的路由栈
const LoginStackScreen = () => {
  return (
    <LoginStack.Navigator
      headerMode={'none'}
      // screenOptions={{                 //用来定制头部信息、根据自己需要更改
      //   title: '测试标题',
      //   headerStyle: {
      //     backgroundColor: '#ee7530'
      //   },
      //   headerTintColor: '#fff',
      //   headerTitleStyle: {
      //     fontWeight: 'bold',
      //     fontSize: 20
      //   }
      // }}
    >
      <LoginStack.Screen name="LoginView" component={LoginView} />
      <LoginStack.Screen name="Privacy" component={Privacy} />
      <LoginStack.Screen name="UserAgreement" component={UserAgreement} />
      <LoginStack.Screen name="Register" component={Register} />
      <LoginStack.Screen name="ModifyPwd" component={ModifyPwd} />
    </LoginStack.Navigator>
  );
};

// 首页的路由栈
const HomeStackScreen = () => {
  return (
    <HomeStack.Navigator initialRouteName="Home" headerMode={'none'}>
      <HomeStack.Screen
        name="Home"
        component={Home}
        // options={{
        //   title: '首页'
        // }}
      />

      <HomeStack.Screen name="WelcomePage" component={WelcomePage} />

      <HomeStack.Screen name="OrgMgrAdd" component={OrgMgrAdd} />
      <HomeStack.Screen name="OrgMgrList" component={OrgMgrList} />
      <HomeStack.Screen name="OrderAdd" component={OrderAdd} />
      <HomeStack.Screen name="OrderList" component={OrderList} />
      <HomeStack.Screen name="OrderScheduling" component={OrderScheduling} />
      <HomeStack.Screen
        name="OrderSchedulingList"
        component={OrderSchedulingList}
      />
      <HomeStack.Screen name="OrderOperateList" component={OrderOperateList} />
      <HomeStack.Screen
        name="OrderStateTracking"
        component={OrderStateTracking}
      />
      <HomeStack.Screen
        name="OutsourcingProcess"
        component={OutsourcingProcess}
      />
      <HomeStack.Screen
        name="CheckClassifyMaterialPurchaseList"
        component={CheckClassifyMaterialPurchaseList}
      />
      <HomeStack.Screen
        name="CheckClassifyMaterialStorageList"
        component={CheckClassifyMaterialStorageList}
      />
      <HomeStack.Screen
        name="CheckClassifyMaterialCheckoutList"
        component={CheckClassifyMaterialCheckoutList}
      />
      <HomeStack.Screen
        name="CheckClassifyMaterialInventoryList"
        component={CheckClassifyMaterialInventoryList}
      />
      <HomeStack.Screen
        name="MaterialInventoryInAdd"
        component={MaterialInventoryInAdd}
      />
      <HomeStack.Screen
        name="MaterialInventoryInList"
        component={MaterialInventoryInList}
      />
      <HomeStack.Screen
        name="MaterialInventoryOutAdd"
        component={MaterialInventoryOutAdd}
      />
      <HomeStack.Screen
        name="MaterialInventoryOutList"
        component={MaterialInventoryOutList}
      />
      <HomeStack.Screen
        name="MaterialInventoryList"
        component={MaterialInventoryList}
      />
      <HomeStack.Screen
        name="MaterialInventoryLocationChange"
        component={MaterialInventoryLocationChange}
      />

      <HomeStack.Screen name="CustomerAdd" component={CustomerAdd} />
      <HomeStack.Screen name="CustomerList" component={CustomerList} />
      <HomeStack.Screen name="ContractAdd" component={ContractAdd} />
      <HomeStack.Screen name="ContractList" component={ContractList} />
      <HomeStack.Screen
        name="ContractAcceptMoneyMgr"
        component={ContractAcceptMoneyMgr}
      />
      <HomeStack.Screen
        name="ContractProgressQuery"
        component={ContractProgressQuery}
      />
      <HomeStack.Screen
        name="ContractProgressDetail"
        component={ContractProgressDetail}
      />
      <HomeStack.Screen
        name="ContractTrackingAdd"
        component={ContractTrackingAdd}
      />
      <HomeStack.Screen
        name="ContractTrackingList"
        component={ContractTrackingList}
      />
      <HomeStack.Screen
        name="CollectMoneyPointAdd"
        component={CollectMoneyPointAdd}
      />
      <HomeStack.Screen
        name="CollectMoneyPointList"
        component={CollectMoneyPointList}
      />
      <HomeStack.Screen
        name="CollectMoneyPlanAdd"
        component={CollectMoneyPlanAdd}
      />
      <HomeStack.Screen
        name="CollectMoneyPlanList"
        component={CollectMoneyPlanList}
      />
      <HomeStack.Screen
        name="CollectMoneyActualAdd"
        component={CollectMoneyActualAdd}
      />
      <HomeStack.Screen
        name="CollectMoneyActualList"
        component={CollectMoneyActualList}
      />
      <HomeStack.Screen
        name="ExpenditureContractReport"
        component={ExpenditureContractReport}
      />
      <HomeStack.Screen name="ContractTracking" component={ContractTracking} />

      <HomeStack.Screen name="SemiFinishedAdd" component={SemiFinishedAdd} />
      <HomeStack.Screen name="SemiFinishedList" component={SemiFinishedList} />
      <HomeStack.Screen name="EncastageAdd" component={EncastageAdd} />
      <HomeStack.Screen name="EncastageList" component={EncastageList} />
      <HomeStack.Screen name="AddWarmRecord" component={AddWarmRecord} />
      <HomeStack.Screen name="WarmRecordList" component={WarmRecordList} />
      <HomeStack.Screen name="FireRecord" component={FireRecord} />
      <HomeStack.Screen name="UnLoadedKilnAdd" component={UnLoadedKilnAdd} />
      <HomeStack.Screen name="UnLoadedKilnList" component={UnLoadedKilnList} />
      <HomeStack.Screen name="CheckInList" component={CheckInList} />
      <HomeStack.Screen name="CheckOutList" component={CheckOutList} />
      <HomeStack.Screen name="CheckOutAdd" component={CheckOutAdd} />
      <HomeStack.Screen name="ResetPwd" component={ResetPwd} />
      <HomeStack.Screen
        name="BelongsProductionLineSetting"
        component={BelongsProductionLineSetting}
      />
      <HomeStack.Screen name="Profile" component={Profile} />
      <HomeStack.Screen name="SinteringList" component={SinteringList} />
      <HomeStack.Screen name="SinteringAdd" component={SinteringAdd} />

      <HomeStack.Screen name="DryKoleMgrAdd" component={DryKoleMgrAdd} />
      <HomeStack.Screen name="DryKoleMgrList" component={DryKoleMgrList} />
      <HomeStack.Screen name="DryKoleInMgrAdd" component={DryKoleInMgrAdd} />
      <HomeStack.Screen name="DryKoleInMgrList" component={DryKoleInMgrList} />
      <HomeStack.Screen name="DryKoleOutMgrAdd" component={DryKoleOutMgrAdd} />
      <HomeStack.Screen
        name="DryKoleOutMgrList"
        component={DryKoleOutMgrList}
      />
      <HomeStack.Screen
        name="DryKoleWasteCauseMgrAdd"
        component={DryKoleWasteCauseMgrAdd}
      />
      <HomeStack.Screen
        name="DryKoleWasteCauseMgrList"
        component={DryKoleWasteCauseMgrList}
      />

      <HomeStack.Screen name="LocationAreaList" component={LocationAreaList} />
      <HomeStack.Screen name="LocationAreaAdd" component={LocationAreaAdd} />
      <HomeStack.Screen
        name="StorageLocationList"
        component={StorageLocationList}
      />
      <HomeStack.Screen
        name="StorageLocationAdd"
        component={StorageLocationAdd}
      />
      <HomeStack.Screen name="StorageInList" component={StorageInList} />
      <HomeStack.Screen name="StorageInAdd" component={StorageInAdd} />
      <HomeStack.Screen
        name="InventoryStorageAdd"
        component={InventoryStorageAdd}
      />
      <HomeStack.Screen name="StorageOutList" component={StorageOutList} />
      <HomeStack.Screen name="StorageOutAdd" component={StorageOutAdd} />
      <HomeStack.Screen
        name="StorageOutAddSelDetail"
        component={StorageOutAddSelDetail}
      />
      <HomeStack.Screen name="InventoryQuery" component={InventoryQuery} />
      <HomeStack.Screen
        name="InventoryBrickTypeQuery"
        component={InventoryBrickTypeQuery}
      />
      <HomeStack.Screen
        name="InventoryLocationQuery"
        component={InventoryLocationQuery}
      />
      <HomeStack.Screen
        name="InventoryDetailBrickClassifyList"
        component={InventoryDetailBrickClassifyList}
      />
      <HomeStack.Screen
        name="InventoryDetailBrickClassifySeriesList"
        component={InventoryDetailBrickClassifySeriesList}
      />
      <HomeStack.Screen
        name="InventoryDetailBrickTypeList"
        component={InventoryDetailBrickTypeList}
      />
      <HomeStack.Screen
        name="InventoryDetailList"
        component={InventoryDetailList}
      />
      <HomeStack.Screen name="InventoryAdjust" component={InventoryAdjust} />
      <HomeStack.Screen name="YieldQuery" component={YieldQuery} />
      <HomeStack.Screen
        name="ProductCheckMgrAdd"
        component={ProductCheckMgrAdd}
      />
      <HomeStack.Screen
        name="ProductCheckMgrList"
        component={ProductCheckMgrList}
      />
      <HomeStack.Screen
        name="EngineeringAcceptanceMgrAdd"
        component={EngineeringAcceptanceMgrAdd}
      />
      <HomeStack.Screen
        name="EngineeringAcceptanceMgrList"
        component={EngineeringAcceptanceMgrList}
      />
      <HomeStack.Screen
        name="EngineeringBackMgrAdd"
        component={EngineeringBackMgrAdd}
      />
      <HomeStack.Screen
        name="EngineeringBackMgrList"
        component={EngineeringBackMgrList}
      />
      <HomeStack.Screen
        name="OutsourcingStorageOutAdd"
        component={OutsourcingStorageOutAdd}
      />
      <HomeStack.Screen
        name="OutsourcingStorageOutList"
        component={OutsourcingStorageOutList}
      />
      <HomeStack.Screen
        name="OutsourcingStorageOutAddSelDetail"
        component={OutsourcingStorageOutAddSelDetail}
      />
      <HomeStack.Screen
        name="BlockWorkStorageInMgrList"
        component={BlockWorkStorageInMgrList}
      />
      <HomeStack.Screen
        name="BlockWorkStorageInMgrAdd"
        component={BlockWorkStorageInMgrAdd}
      />
      <HomeStack.Screen
        name="BlockWorkStorageOutMgrList"
        component={BlockWorkStorageOutMgrList}
      />
      <HomeStack.Screen
        name="BlockWorkStorageOutMgrAdd"
        component={BlockWorkStorageOutMgrAdd}
      />
      <HomeStack.Screen
        name="BlockWorkStorageOutMgrAddDetail"
        component={BlockWorkStorageOutMgrAddDetail}
      />
      <HomeStack.Screen
        name="InventoryBlockWorkList"
        component={InventoryBlockWorkList}
      />
      <HomeStack.Screen
        name="InventoryBlockWorkContractList"
        component={InventoryBlockWorkContractList}
      />
      <HomeStack.Screen
        name="InventoryBlockWorkPositionList"
        component={InventoryBlockWorkPositionList}
      />
      <HomeStack.Screen
        name="InventoryBlockWorkDetailList"
        component={InventoryBlockWorkDetailList}
      />

      <HomeStack.Screen
        name="PaymentObjectList"
        component={PaymentObjectList}
      />
      <HomeStack.Screen name="PaymentObjectAdd" component={PaymentObjectAdd} />
      <HomeStack.Screen name="PaymentClassList" component={PaymentClassList} />
      <HomeStack.Screen name="PaymentApplyList" component={PaymentApplyList} />
      <HomeStack.Screen name="PaymentAuditList" component={PaymentAuditList} />
      <HomeStack.Screen name="PaymentApplyAdd" component={PaymentApplyAdd} />
      <HomeStack.Screen name="PaymentClassAdd" component={PaymentClassAdd} />
      <HomeStack.Screen
        name="PaymengApplyDetail"
        component={PaymengApplyDetail}
      />
      <HomeStack.Screen name="PaymentAudit" component={PaymentAudit} />

      <HomeStack.Screen
        name="VerifyInternalSettingList"
        component={VerifyInternalSettingList}
      />
      <HomeStack.Screen
        name="VerifyInternalSettingAdd"
        component={VerifyInternalSettingAdd}
      />
      <HomeStack.Screen
        name="VerifyInternalStandardList"
        component={VerifyInternalStandardList}
      />
      <HomeStack.Screen
        name="VerifyInternalStandardAdd"
        component={VerifyInternalStandardAdd}
      />
      <HomeStack.Screen
        name="VerifyInternalResultList"
        component={VerifyInternalResultList}
      />
      <HomeStack.Screen
        name="VerifyInternalResultAdd"
        component={VerifyInternalResultAdd}
      />
      <HomeStack.Screen
        name="VerifyExternalSettingList"
        component={VerifyExternalSettingList}
      />
      <HomeStack.Screen
        name="VerifyExternalSettingAdd"
        component={VerifyExternalSettingAdd}
      />
      <HomeStack.Screen
        name="VerifyExternalStandardList"
        component={VerifyExternalStandardList}
      />
      <HomeStack.Screen
        name="VerifyExternalStandardAdd"
        component={VerifyExternalStandardAdd}
      />
      <HomeStack.Screen
        name="VerifyExternalResultList"
        component={VerifyExternalResultList}
      />
      <HomeStack.Screen
        name="VerifyExternalResultAdd"
        component={VerifyExternalResultAdd}
      />

      <HomeStack.Screen name="KilnCarMgrList" component={KilnCarMgrList} />
      <HomeStack.Screen name="KilnCarMgrAdd" component={KilnCarMgrAdd} />
      <HomeStack.Screen name="MachineMgrList" component={MachineMgrList} />
      <HomeStack.Screen name="MachineMgrAdd" component={MachineMgrAdd} />

      <HomeStack.Screen
        name="BrickClassifySeriesMgrList"
        component={BrickClassifySeriesMgrList}
      />
      <HomeStack.Screen
        name="BrickClassifySeriesMgrAdd"
        component={BrickClassifySeriesMgrAdd}
      />
      <HomeStack.Screen
        name="BrickClassifyMgrList"
        component={BrickClassifyMgrList}
      />
      <HomeStack.Screen
        name="BrickClassifyMgrAdd"
        component={BrickClassifyMgrAdd}
      />
      <HomeStack.Screen name="BrickTypeMgrList" component={BrickTypeMgrList} />
      <HomeStack.Screen name="BrickTypeMgrAdd" component={BrickTypeMgrAdd} />

      <HomeStack.Screen name="DepartmentList" component={DepartmentList} />
      <HomeStack.Screen name="DepartmentAdd" component={DepartmentAdd} />
      <HomeStack.Screen name="JobMgrList" component={JobMgrList} />
      <HomeStack.Screen name="JobMgrAdd" component={JobMgrAdd} />
      <HomeStack.Screen
        name="DepartmentStaffMgrList"
        component={DepartmentStaffMgrList}
      />
      <HomeStack.Screen name="JobStaffMgrList" component={JobStaffMgrList} />
      <HomeStack.Screen name="JobStaffMgrAdd" component={JobStaffMgrAdd} />
      <HomeStack.Screen name="EquipmentMgrList" component={EquipmentMgrList} />
      <HomeStack.Screen name="EquipmentMgrAdd" component={EquipmentMgrAdd} />
      <HomeStack.Screen
        name="EquipmentStateMgrAdd"
        component={EquipmentStateMgrAdd}
      />
      <HomeStack.Screen
        name="EquipmentStateMgrList"
        component={EquipmentStateMgrList}
      />
      <HomeStack.Screen
        name="CheckEquipmentList"
        component={CheckEquipmentList}
      />
      <HomeStack.Screen
        name="CheckEquipmentStateList"
        component={CheckEquipmentStateList}
      />
      <HomeStack.Screen
        name="CheckDepartmentList"
        component={CheckDepartmentList}
      />
      <HomeStack.Screen
        name="WorkingShiftRelStaffMgr"
        component={WorkingShiftRelStaffMgr}
      />
      <HomeStack.Screen
        name="PortalStaffMgrAdd"
        component={PortalStaffMgrAdd}
      />
      <HomeStack.Screen
        name="PortalStaffMgrList"
        component={PortalStaffMgrList}
      />
      <HomeStack.Screen name="ProductSummart" component={ProductSummart} />
      <HomeStack.Screen
        name="ProductionQtyQuery"
        component={ProductionQtyQuery}
      />
      <HomeStack.Screen name="ProductionQuery" component={ProductionQuery} />
      <HomeStack.Screen
        name="EnterpriseInvitedApply"
        component={EnterpriseInvitedApply}
      />
      <HomeStack.Screen
        name="InformationConfigAdd"
        component={InformationConfigAdd}
      />
      <HomeStack.Screen
        name="InformationConfigList"
        component={InformationConfigList}
      />

      <HomeStack.Screen name="DailyList" component={DailyList} />
      <HomeStack.Screen name="DailyAdd" component={DailyAdd} />
      <HomeStack.Screen name="DailyMessageList" component={DailyMessageList} />
      <HomeStack.Screen name="DailyMessageAdd" component={DailyMessageAdd} />
      <HomeStack.Screen name="HarvestMgrList" component={HarvestMgrList} />
      <HomeStack.Screen name="HarvestMgrAdd" component={HarvestMgrAdd} />
      <HomeStack.Screen name="HarvestDetail" component={HarvestDetail} />
      <HomeStack.Screen name="DailyDetail" component={DailyDetail} />

      <HomeStack.Screen name="CourseMgrAdd" component={CourseMgrAdd} />
      <HomeStack.Screen name="CourseMgrList" component={CourseMgrList} />
      <HomeStack.Screen name="GoodHarvestList" component={GoodHarvestList} />
      <HomeStack.Screen
        name="HarvestCircleList"
        component={HarvestCircleList}
      />
      <HomeStack.Screen
        name="HarvestGoodMgrList"
        component={HarvestGoodMgrList}
      />
      <HomeStack.Screen
        name="MyBacklogDailyList"
        component={MyBacklogDailyList}
      />
      <HomeStack.Screen
        name="MyBacklogDailyAdd"
        component={MyBacklogDailyAdd}
      />
      <HomeStack.Screen name="MyDoneDailyList" component={MyDoneDailyList} />
      <HomeStack.Screen name="PromotionPlanAdd" component={PromotionPlanAdd} />
      <HomeStack.Screen
        name="PromotionPlanList"
        component={PromotionPlanList}
      />
      <HomeStack.Screen
        name="PromotionPlanDetail"
        component={PromotionPlanDetail}
      />
      <HomeStack.Screen
        name="PromotionPlanSelUser"
        component={PromotionPlanSelUser}
      />
      <HomeStack.Screen name="ScoreMgrAdd" component={ScoreMgrAdd} />
      <HomeStack.Screen name="ScoreMgrList" component={ScoreMgrList} />
      <HomeStack.Screen
        name="HarvestDiscussAdd"
        component={HarvestDiscussAdd}
      />
      <HomeStack.Screen
        name="HarvestDiscussList"
        component={HarvestDiscussList}
      />
      <HomeStack.Screen
        name="ScoreMgrCourseList"
        component={ScoreMgrCourseList}
      />
      <HomeStack.Screen name="QueryDaily" component={QueryDaily} />
      <HomeStack.Screen name="WorkDaily" component={WorkDaily} />
      <HomeStack.Screen name="QueryHarvest" component={QueryHarvest} />
      <HomeStack.Screen name="QueryMyScore" component={QueryMyScore} />
      <HomeStack.Screen
        name="QueryPromotionPlan"
        component={QueryPromotionPlan}
      />
      <HomeStack.Screen
        name="PortalTrackingList"
        component={PortalTrackingList}
      />
      <HomeStack.Screen
        name="PortalTrackingAdd"
        component={PortalTrackingAdd}
      />
      <HomeStack.Screen
        name="MyDocumentMgrList"
        component={MyDocumentMgrList}
      />
      <HomeStack.Screen
        name="DocumentLibraryMgrList"
        component={DocumentLibraryMgrList}
      />
      <HomeStack.Screen name="ViewDocument" component={ViewDocument} />
      <HomeStack.Screen
        name="VideoLibraryMgrList"
        component={VideoLibraryMgrList}
      />

      <HomeStack.Screen name="PointConfig" component={PointConfig} />
      <HomeStack.Screen name="PointConfigAdd" component={PointConfigAdd} />
      <HomeStack.Screen name="PointRecord" component={PointRecord} />
      <HomeStack.Screen name="PointReward" component={PointReward} />
      <HomeStack.Screen name="PointRewardAdd" component={PointRewardAdd} />
      <HomeStack.Screen name="PointExchange" component={PointExchange} />
      <HomeStack.Screen name="PointExchangeAdd" component={PointExchangeAdd} />
      <HomeStack.Screen name="PointRanking" component={PointRanking} />
      <HomeStack.Screen name="ExamConfig" component={ExamConfig} />
      <HomeStack.Screen name="ExamConfigAdd" component={ExamConfigAdd} />
      <HomeStack.Screen name="FormCollection" component={FormCollection} />
      <HomeStack.Screen name="ExamApply" component={ExamApply} />
      <HomeStack.Screen name="ExamApplyAdd" component={ExamApplyAdd} />
      <HomeStack.Screen name="CourseScheduling" component={CourseScheduling} />
      <HomeStack.Screen
        name="CourseSchedulingAdd"
        component={CourseSchedulingAdd}
      />
      <HomeStack.Screen
        name="CourseSchedulingTable"
        component={CourseSchedulingTable}
      />
      <HomeStack.Screen name="ExamApplyAudit" component={ExamApplyAudit} />
      <HomeStack.Screen name="SYPointMall" component={SYPointMall} />
      <HomeStack.Screen name="SYContactUs" component={SYContactUs} />
      <HomeStack.Screen name="AssessClassList" component={AssessClassList} />
      <HomeStack.Screen name="AssessClassAdd" component={AssessClassAdd} />
      <HomeStack.Screen name="AssessApplyAdd" component={AssessApplyAdd} />
      <HomeStack.Screen name="AssessApplyList" component={AssessApplyList} />
      <HomeStack.Screen name="AssessAuditList" component={AssessAuditList} />
      <HomeStack.Screen name="AssessQueryList" component={AssessQueryList} />
      <HomeStack.Screen name="AssessAudit" component={AssessAudit} />
      <HomeStack.Screen name="MyReceiveAdd" component={MyReceiveAdd} />
      <HomeStack.Screen name="MyReceiveList" component={MyReceiveList} />
      <HomeStack.Screen
        name="AskQuestionsQuery"
        component={AskQuestionsQuery}
      />
      <HomeStack.Screen
        name="MyAskQuestionsAdd"
        component={MyAskQuestionsAdd}
      />
      <HomeStack.Screen
        name="MyAskQuestionsList"
        component={MyAskQuestionsList}
      />
      <HomeStack.Screen
        name="AskQuestionsSolveTrackingAdd"
        component={AskQuestionsSolveTrackingAdd}
      />
      <HomeStack.Screen
        name="AskQuestionsSolveTrackingList"
        component={AskQuestionsSolveTrackingList}
      />

      <HomeStack.Screen name="CustomerLeadList" component={CustomerLeadList} />
      <HomeStack.Screen name="CustomerLeadAdd" component={CustomerLeadAdd} />
      <HomeStack.Screen name="LeadAuditList" component={LeadAuditList} />
      <HomeStack.Screen
        name="LeadTeleInvitationList"
        component={LeadTeleInvitationList}
      />
      <HomeStack.Screen name="LeadVisitList" component={LeadVisitList} />
      <HomeStack.Screen
        name="SaleopportunityList"
        component={SaleopportunityList}
      />
      <HomeStack.Screen
        name="SaleopportunityAdd"
        component={SaleopportunityAdd}
      />

      <HomeStack.Screen
        name="CollegClassGradesAdd"
        component={CollegClassGradesAdd}
      />
      <HomeStack.Screen
        name="CollegClassGradesList"
        component={CollegClassGradesList}
      />
      <HomeStack.Screen
        name="CollegClassStudentList"
        component={CollegClassStudentList}
      />
      <HomeStack.Screen
        name="CollegProfessionalList"
        component={CollegProfessionalList}
      />
      <HomeStack.Screen
        name="CollegProfessionalAdd"
        component={CollegProfessionalAdd}
      />
      <HomeStack.Screen name="CollegStudentAdd" component={CollegStudentAdd} />
      <HomeStack.Screen
        name="CollegStudentList"
        component={CollegStudentList}
      />
      <HomeStack.Screen
        name="CollegStudentResumeQuery"
        component={CollegStudentResumeQuery}
      />
      <HomeStack.Screen
        name="EnterpriseInvitedInterView"
        component={EnterpriseInvitedInterView}
      />
      <HomeStack.Screen
        name="EnterpriseResumeCollection"
        component={EnterpriseResumeCollection}
      />
      <HomeStack.Screen
        name="PortalEnterpriseAdd"
        component={PortalEnterpriseAdd}
      />
      <HomeStack.Screen
        name="PortalEnterpriseList"
        component={PortalEnterpriseList}
      />
      <HomeStack.Screen
        name="StudentInterViewInvited"
        component={StudentInterViewInvited}
      />
      <HomeStack.Screen name="StudentMyChance" component={StudentMyChance} />
      {/* <HomeStack.Screen
        name="StudentMyInterViewPreview"
        component={StudentMyInterViewPreview}
      /> */}
      <HomeStack.Screen
        name="EnterpriseRecruiterList"
        component={EnterpriseRecruiterList}
      />
      <HomeStack.Screen
        name="EnterpriseRecruiterAdd"
        component={EnterpriseRecruiterAdd}
      />
      <HomeStack.Screen
        name="EnterprisecrHiringPositionAdd"
        component={EnterprisecrHiringPositionAdd}
      />
      <HomeStack.Screen
        name="EnterprisecrHiringPositionList"
        component={EnterprisecrHiringPositionList}
      />
      <HomeStack.Screen
        name="EnterprisecrHiringPositionDetail"
        component={EnterprisecrHiringPositionDetail}
      />
      <HomeStack.Screen
        name="CollegPositionQuery"
        component={CollegPositionQuery}
      />
      <HomeStack.Screen
        name="PersonalInformation"
        component={PersonalInformation}
      />
      <HomeStack.Screen name="PersonalHonor" component={PersonalHonor} />
      <HomeStack.Screen
        name="CollegeEvaluation"
        component={CollegeEvaluation}
      />
      <HomeStack.Screen name="NewItem" component={NewItem} />
      <HomeStack.Screen
        name="EnterprisePositionDetail"
        component={EnterprisePositionDetail}
      />
      <HomeStack.Screen
        name="MemberManagementAdd"
        component={MemberManagementAdd}
      />
      <HomeStack.Screen
        name="MemberManagementList"
        component={MemberManagementList}
      />
      {/* <HomeStack.Screen name="MemberManagementDetail" component={MemberManagementDetail} /> */}
      <HomeStack.Screen
        name="MemberManagementExamine"
        component={MemberManagementExamine}
      />
      {/* <HomeStack.Screen name="MemberManagementExamineDetail" component={MemberManagementExamineDetail} /> */}
      <HomeStack.Screen name="MemberTypeAdd" component={MemberTypeAdd} />
      <HomeStack.Screen name="MemberTypeList" component={MemberTypeList} />
      <HomeStack.Screen
        name="ConfiguringTenants"
        component={ConfiguringTenants}
      />
      <HomeStack.Screen
        name="ConfiguringTenantsParam"
        component={ConfiguringTenantsParam}
      />
      <HomeStack.Screen
        name="ConfiguringTenantsParamItem"
        component={ConfiguringTenantsParamItem}
      />
      <HomeStack.Screen
        name="TenantEnterpriseAdd"
        component={TenantEnterpriseAdd}
      />
      <HomeStack.Screen
        name="TenantEnterpriseList"
        component={TenantEnterpriseList}
      />
      <HomeStack.Screen
        name="PortalTenantParam"
        component={PortalTenantParam}
      />
      <HomeStack.Screen
        name="PortalTenantParamAdd"
        component={PortalTenantParamAdd}
      />
      <HomeStack.Screen
        name="PortalTenantParamItem"
        component={PortalTenantParamItem}
      />
      <HomeStack.Screen
        name="PortalTenantParamData"
        component={PortalTenantParamData}
      />
      <HomeStack.Screen
        name="PortalTenantParamDataAdd"
        component={PortalTenantParamDataAdd}
      />
      <HomeStack.Screen
        name="HomeResourceDisplay"
        component={HomeResourceDisplay}
      />
      <HomeStack.Screen
        name="MemberContactConfig"
        component={MemberContactConfig}
      />
      <HomeStack.Screen
        name="MemberContactConfigAdd"
        component={MemberContactConfigAdd}
      />
      <HomeStack.Screen name="ConfigPreview" component={ConfigPreview} />
      <HomeStack.Screen name="PermissionList" component={PermissionList} />
      <HomeStack.Screen
        name="PermissionUserList"
        component={PermissionUserList}
      />
      <HomeStack.Screen
        name="PermissionUserAddList"
        component={PermissionUserAddList}
      />

      <HomeStack.Screen
        name="NaturalGasPurchaseList"
        component={NaturalGasPurchaseList}
      />
      <HomeStack.Screen
        name="NaturalGasPurchaseAdd"
        component={NaturalGasPurchaseAdd}
      />
      <HomeStack.Screen name="SupplierMgrList" component={SupplierMgrList} />
      <HomeStack.Screen name="SupplierMgrAdd" component={SupplierMgrAdd} />
      <HomeStack.Screen
        name="AuditConfigMgrList"
        component={AuditConfigMgrList}
      />
      <HomeStack.Screen
        name="AuditConfigMgrAdd"
        component={AuditConfigMgrAdd}
      />
      <HomeStack.Screen name="AuditPointList" component={AuditPointList} />
      <HomeStack.Screen name="AuditPointAdd" component={AuditPointAdd} />
      <HomeStack.Screen
        name="AuditCcConfigurationList"
        component={AuditCcConfigurationList}
      />
      <HomeStack.Screen
        name="AuditCcConfigurationAdd"
        component={AuditCcConfigurationAdd}
      />
      <HomeStack.Screen
        name="ExpenditureContractList"
        component={ExpenditureContractList}
      />
      <HomeStack.Screen
        name="ExpenditureContractAdd"
        component={ExpenditureContractAdd}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyPointList"
        component={ExpenditureMoneyPointList}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyPointAdd"
        component={ExpenditureMoneyPointAdd}
      />
      <HomeStack.Screen
        name="MaterialAuditBacklogList"
        component={MaterialAuditBacklogList}
      />
      <HomeStack.Screen
        name="MaterialAuditBacklogDetail"
        component={MaterialAuditBacklogDetail}
      />
      <HomeStack.Screen
        name="MaterialAuditDoneList"
        component={MaterialAuditDoneList}
      />
      <HomeStack.Screen name="MaterialAudit" component={MaterialAudit} />
      <HomeStack.Screen
        name="InventoryAuditBacklogDetail"
        component={InventoryAuditBacklogDetail}
      />
      <HomeStack.Screen
        name="MaterialInventoryAudit"
        component={MaterialInventoryAudit}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyActualAdd"
        component={ExpenditureMoneyActualAdd}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyActualList"
        component={ExpenditureMoneyActualList}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyPlanAdd"
        component={ExpenditureMoneyPlanAdd}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyPlanList"
        component={ExpenditureMoneyPlanList}
      />

      <HomeStack.Screen
        name="MyProductSaleRelease"
        component={MyProductSaleRelease}
      />
      <HomeStack.Screen
        name="MyProductSaleReleaseAdd"
        component={MyProductSaleReleaseAdd}
      />
      <HomeStack.Screen name="MyAskBugRelease" component={MyAskBugRelease} />
      <HomeStack.Screen
        name="MyAskBugReleaseAdd"
        component={MyAskBugReleaseAdd}
      />
      <HomeStack.Screen
        name="ProductReleaseMarket"
        component={ProductReleaseMarket}
      />
      <HomeStack.Screen
        name="AskBugReleaseMarket"
        component={AskBugReleaseMarket}
      />
      <HomeStack.Screen
        name="ProductReleaseAuditList"
        component={ProductReleaseAuditList}
      />
      <HomeStack.Screen
        name="ProductReleaseAskBuyAuditList"
        component={ProductReleaseAskBuyAuditList}
      />
      <HomeStack.Screen name="ProductHome" component={ProductHome} />
      <HomeStack.Screen name="Product" component={Product} />
      <HomeStack.Screen name="AskForPurchase" component={AskForPurchase} />

      <HomeStack.Screen
        name="CourseLevelMgrAdd"
        component={CourseLevelMgrAdd}
      />
      <HomeStack.Screen
        name="CourseLevelMgrList"
        component={CourseLevelMgrList}
      />
      <HomeStack.Screen
        name="CourseLevelStaffMgrAdd"
        component={CourseLevelStaffMgrAdd}
      />
      <HomeStack.Screen
        name="CourseLevelStaffMgrList"
        component={CourseLevelStaffMgrList}
      />
      <HomeStack.Screen
        name="CourseTaskMgrList"
        component={CourseTaskMgrList}
      />
      <HomeStack.Screen
        name="CourseTaskMgrDetail"
        component={CourseTaskMgrDetail}
      />
      <HomeStack.Screen
        name="CourseTrackDetail"
        component={CourseTrackDetail}
      />
      <HomeStack.Screen name="CourseTrackList" component={CourseTrackList} />
      <HomeStack.Screen name="CourseTypeMgrAdd" component={CourseTypeMgrAdd} />
      <HomeStack.Screen
        name="CourseTypeMgrList"
        component={CourseTypeMgrList}
      />
      <HomeStack.Screen name="MyCourseList" component={MyCourseList} />
      <HomeStack.Screen
        name="StudentCourseTrackList"
        component={StudentCourseTrackList}
      />
      <HomeStack.Screen name="CourseVidioList" component={CourseVidioList} />
      <HomeStack.Screen
        name="StudentCourseTrackDetailList"
        component={StudentCourseTrackDetailList}
      />
      <HomeStack.Screen
        name="StudentCourseTrackDetail"
        component={StudentCourseTrackDetail}
      />
      <HomeStack.Screen name="MyCourseDetail" component={MyCourseDetail} />

      <HomeStack.Screen name="HLHospitalList" component={HLHospitalList} />
      <HomeStack.Screen name="HLHospitalAdd" component={HLHospitalAdd} />
      <HomeStack.Screen name="HLDepartmentList" component={HLDepartmentList} />
      <HomeStack.Screen name="HLDepartmentAdd" component={HLDepartmentAdd} />
      <HomeStack.Screen
        name="HLDepartmentStoreKeeperRelList"
        component={HLDepartmentStoreKeeperRelList}
      />
      <HomeStack.Screen
        name="HLDepartmentStoreKeeperRelAdd"
        component={HLDepartmentStoreKeeperRelAdd}
      />
      <HomeStack.Screen
        name="HLTransferModeAdd"
        component={HLTransferModeAdd}
      />
      <HomeStack.Screen
        name="HLTransforModeList"
        component={HLTransforModeList}
      />
      <HomeStack.Screen name="HLPortalUnitAdd" component={HLPortalUnitAdd} />
      <HomeStack.Screen name="HLPortalUnitList" component={HLPortalUnitList} />
      <HomeStack.Screen
        name="HLPortalSupplierList"
        component={HLPortalSupplierList}
      />
      <HomeStack.Screen
        name="HLPortalSupplierAdd"
        component={HLPortalSupplierAdd}
      />
      <HomeStack.Screen
        name="HLMaterialListingList"
        component={HLMaterialListingList}
      />
      <HomeStack.Screen
        name="HLMaterialListingAdd"
        component={HLMaterialListingAdd}
      />
      <HomeStack.Screen
        name="HLStorageOutTypeList"
        component={HLStorageOutTypeList}
      />
      <HomeStack.Screen
        name="HLStorageOutTypeAdd"
        component={HLStorageOutTypeAdd}
      />
      <HomeStack.Screen name="HLStorageInMList" component={HLStorageInMList} />
      <HomeStack.Screen name="HLStorageInMAdd" component={HLStorageInMAdd} />
      <HomeStack.Screen
        name="HLStorageOutMList"
        component={HLStorageOutMList}
      />
      <HomeStack.Screen name="HLStorageOutMAdd" component={HLStorageOutMAdd} />
      <HomeStack.Screen name="HLStorageInDList" component={HLStorageInDList} />
      <HomeStack.Screen name="HLStorageInDAdd" component={HLStorageInDAdd} />
      <HomeStack.Screen
        name="HLStorageInAddDetail"
        component={HLStorageInAddDetail}
      />
      <HomeStack.Screen
        name="HLStorageInListDetail"
        component={HLStorageInListDetail}
      />
      {/* <HomeStack.Screen name="HLStorageInDetailDList" component={HLStorageInDetailDList}/> */}
      <HomeStack.Screen
        name="HLStorageOutListDetail"
        component={HLStorageOutListDetail}
      />
      <HomeStack.Screen name="HLStorageOutDAdd" component={HLStorageOutDAdd} />
      <HomeStack.Screen
        name="HLStorageOutAddDetail"
        component={HLStorageOutAddDetail}
      />
      <HomeStack.Screen
        name="HLStorageOutDList"
        component={HLStorageOutDList}
      />
      <HomeStack.Screen
        name="HLMaterialInventoryMList"
        component={HLMaterialInventoryMList}
      />
      <HomeStack.Screen
        name="HLMaterialInventoryDList"
        component={HLMaterialInventoryDList}
      />
      <HomeStack.Screen
        name="HlMaterialInventoryDetailList"
        component={HlMaterialInventoryDetailList}
      />
      <HomeStack.Screen
        name="HLMedicineInventoryDetailList"
        component={HLMedicineInventoryDetailList}
      />
      <HomeStack.Screen
        name="HLStorageInAuditList"
        component={HLStorageInAuditList}
      />
      <HomeStack.Screen
        name="HLStorageOutAuditList"
        component={HLStorageOutAuditList}
      />
      <HomeStack.Screen
        name="HLStorageOutAudit"
        component={HLStorageOutAudit}
      />
      <HomeStack.Screen
        name="HLStorageOutAuditDetail"
        component={HLStorageOutAuditDetail}
      />
      <HomeStack.Screen name="HLStorageInAudit" component={HLStorageInAudit} />
      <HomeStack.Screen
        name="HLStorageInAuditDetail"
        component={HLStorageInAuditDetail}
      />
      <HomeStack.Screen name="HLPharmacyList" component={HLPharmacyList} />
      <HomeStack.Screen name="HlPharmacyAdd" component={HlPharmacyAdd} />
      <HomeStack.Screen name="HLMedicineList" component={HLMedicineList} />
      <HomeStack.Screen name="HLMedicineAdd" component={HLMedicineAdd} />
      <HomeStack.Screen name="HLDoctorList" component={HLDoctorList} />
      <HomeStack.Screen name="HLDoctorAdd" component={HLDoctorAdd} />
      <HomeStack.Screen name="HLSickPersonList" component={HLSickPersonList} />
      <HomeStack.Screen name="HLSickPersonAdd" component={HLSickPersonAdd} />
      <HomeStack.Screen
        name="HLMedicineStorageInList"
        component={HLMedicineStorageInList}
      />
      <HomeStack.Screen
        name="HLMedicineStorageInAdd"
        component={HLMedicineStorageInAdd}
      />
      <HomeStack.Screen
        name="HLMedicineStorageInDetailList"
        component={HLMedicineStorageInDetailList}
      />
      <HomeStack.Screen
        name="HLMedicineStorageInDetailAdd"
        component={HLMedicineStorageInDetailAdd}
      />
      <HomeStack.Screen
        name="HLMedicineStorageOutDetailList"
        component={HLMedicineStorageOutDetailList}
      />
      <HomeStack.Screen
        name="HLMedicineStorageOutDetailAdd"
        component={HLMedicineStorageOutDetailAdd}
      />
      <HomeStack.Screen
        name="HLMedicineStorageOutList"
        component={HLMedicineStorageOutList}
      />
      <HomeStack.Screen
        name="HLMedicineStorageOutAdd"
        component={HLMedicineStorageOutAdd}
      />
      <HomeStack.Screen
        name="HLMedicalInsuranceTypeList"
        component={HLMedicalInsuranceTypeList}
      />
      <HomeStack.Screen
        name="HLMedicalInsuranceTypeAdd"
        component={HLMedicalInsuranceTypeAdd}
      />
      <HomeStack.Screen
        name="HLMedicineInventoryMList"
        component={HLMedicineInventoryMList}
      />
      <HomeStack.Screen
        name="HLMedicineStorageInAudit"
        component={HLMedicineStorageInAudit}
      />
      <HomeStack.Screen
        name="HLMedicineStorageInAuditDetail"
        component={HLMedicineStorageInAuditDetail}
      />
      <HomeStack.Screen name="LinkShareList" component={LinkShareList} />
      <HomeStack.Screen name="PointRecordList" component={PointRecordList} />

      <RoleListStack.Screen name="RoleList" component={RoleList} />
      <RoleListStack.Screen name="RoleAdd" component={RoleAdd} />
      <RoleListStack.Screen name="RoleMenuList" component={RoleMenuList} />
      <RoleListStack.Screen name="RoleMenuAdd" component={RoleMenuAdd} />
      <RoleListStack.Screen name="QuickMenuList" component={QuickMenuList} />
      <RoleListStack.Screen name="QuickMenuAdd" component={QuickMenuAdd} />
      <RoleListStack.Screen name="RoleUserList" component={RoleUserList} />
      <RoleListStack.Screen name="RoleUserAdd" component={RoleUserAdd} />
      <RoleListStack.Screen
        name="UngradedCauseMgrList"
        component={UngradedCauseMgrList}
      />
      <RoleListStack.Screen
        name="UngradedCauseMgrAdd"
        component={UngradedCauseMgrAdd}
      />
      <RoleListStack.Screen
        name="OrderPositionMgrList"
        component={OrderPositionMgrList}
      />
      <RoleListStack.Screen
        name="OrderPositionMgrAdd"
        component={OrderPositionMgrAdd}
      />
      <RoleListStack.Screen
        name="WorkingShiftMgrList"
        component={WorkingShiftMgrList}
      />
      <RoleListStack.Screen
        name="WorkingShiftMgrAdd"
        component={WorkingShiftMgrAdd}
      />

      <PersonalCenterStack.Screen
        name="SuggestionFeedbackAdd"
        component={SuggestionFeedbackAdd}
      />
      <PersonalCenterStack.Screen
        name="SuggestionFeedbackList"
        component={SuggestionFeedbackList}
      />
      <PersonalCenterStack.Screen
        name="HelpCenterList"
        component={HelpCenterList}
      />

      <TenantStack.Screen name="TenantList" component={TenantList} />
      <TenantStack.Screen name="TenantAdd" component={TenantAdd} />
      <TenantStack.Screen
        name="OutsourcingTenantList"
        component={OutsourcingTenantList}
      />
      <TenantStack.Screen
        name="OutsourcingTenantAdd"
        component={OutsourcingTenantAdd}
      />
      <TenantStack.Screen
        name="ProductionLineMgrAdd"
        component={ProductionLineMgrAdd}
      />
      <TenantStack.Screen
        name="ProductionLineMgrList"
        component={ProductionLineMgrList}
      />

      <TenantStack.Screen
        name="MaterialCheckoutAdd"
        component={MaterialCheckoutAdd}
      />
      <TenantStack.Screen
        name="MaterialCheckoutList"
        component={MaterialCheckoutList}
      />
      <TenantStack.Screen
        name="MaterialClassifyAdd"
        component={MaterialClassifyAdd}
      />
      <TenantStack.Screen
        name="MaterialClassifyList"
        component={MaterialClassifyList}
      />
      <TenantStack.Screen
        name="MaterialSmallClassifyAdd"
        component={MaterialSmallClassifyAdd}
      />
      <TenantStack.Screen
        name="MaterialSmallClassifyList"
        component={MaterialSmallClassifyList}
      />
      <TenantStack.Screen name="MaterialSpecAdd" component={MaterialSpecAdd} />
      <TenantStack.Screen
        name="MaterialSpecList"
        component={MaterialSpecList}
      />
      <TenantStack.Screen
        name="MaterialPurchaseAdd"
        component={MaterialPurchaseAdd}
      />
      <TenantStack.Screen
        name="MaterialPurchaseList"
        component={MaterialPurchaseList}
      />
      <TenantStack.Screen
        name="MaterialStorageAdd"
        component={MaterialStorageAdd}
      />
      <TenantStack.Screen
        name="MaterialStorageList"
        component={MaterialStorageList}
      />
      <TenantStack.Screen
        name="NaturalGasFlowMgrList"
        component={NaturalGasFlowMgrList}
      />
      <TenantStack.Screen
        name="NaturalGasFlowMgrAdd"
        component={NaturalGasFlowMgrAdd}
      />
      <TenantStack.Screen
        name="SortingCheckoutAdd"
        component={SortingCheckoutAdd}
      />
      <TenantStack.Screen
        name="SortingCheckoutList"
        component={SortingCheckoutList}
      />
      <TenantStack.Screen
        name="SortingStorageAdd"
        component={SortingStorageAdd}
      />
      <TenantStack.Screen
        name="SortingStorageList"
        component={SortingStorageList}
      />
      <TenantStack.Screen name="SpecCheckoutAdd" component={SpecCheckoutAdd} />
      <TenantStack.Screen
        name="SpecCheckoutList"
        component={SpecCheckoutList}
      />
      <TenantStack.Screen name="SpecStorageAdd" component={SpecStorageAdd} />
      <TenantStack.Screen name="SpecStorageList" component={SpecStorageList} />

      {/* <HomeStack.Screen name="Myself" component={Myself}/> */}
    </HomeStack.Navigator>
  );
};

const QueryDailyStackScreen = () => {
  return (
    <QueryDailyStack.Navigator headerMode={'none'}>
      <QueryDailyStack.Screen name="QueryDaily" component={QueryDaily} />
      <QueryDailyStack.Screen
        name="DailyMessageList"
        component={DailyMessageList}
      />
      <QueryDailyStack.Screen
        name="DailyMessageAdd"
        component={DailyMessageAdd}
      />
      <QueryDailyStack.Screen name="DailyDetail" component={DailyDetail} />
    </QueryDailyStack.Navigator>
  );
};
const HarvestCircleStackScreen = () => {
  return (
    <HarvestCircleStack.Navigator headerMode={'none'}>
      <HarvestCircleStack.Screen
        name="HarvestCircleList"
        component={HarvestCircleList}
      />
    </HarvestCircleStack.Navigator>
  );
};
const GoodHarvestStackScreen = () => {
  return (
    <GoodHarvestStack.Navigator headerMode={'none'}>
      <GoodHarvestStack.Screen
        name="GoodHarvestList"
        component={GoodHarvestList}
      />
    </GoodHarvestStack.Navigator>
  );
};
const DocumentLibraryStackScreen = () => {
  return (
    <DocumentLibraryStack.Navigator headerMode={'none'}>
      <DocumentLibraryStack.Screen
        name="DocumentLibraryMgrList"
        component={DocumentLibraryMgrList}
      />
    </DocumentLibraryStack.Navigator>
  );
};

const TenantStackScreen = () => {
  return (
    <TenantStack.Navigator headerMode={'none'}>
      <TenantStack.Screen name="TenantList" component={TenantList} />
      <TenantStack.Screen name="TenantAdd" component={TenantAdd} />
      <TenantStack.Screen
        name="OutsourcingTenantList"
        component={OutsourcingTenantList}
      />
      <TenantStack.Screen
        name="OutsourcingTenantAdd"
        component={OutsourcingTenantAdd}
      />
    </TenantStack.Navigator>
  );
};

const SemiFinishedStackScreen = () => {
  return (
    <SemiFinishedStack.Navigator headerMode={'none'}>
      <SemiFinishedStack.Screen
        name="SemiFinishedList"
        component={SemiFinishedList}
        options={{
          title: '半成品点验',
        }}
      />
      <SemiFinishedStack.Screen
        name="SemiFinishedAdd"
        component={SemiFinishedAdd}
        options={{
          title: '新增点验',
        }}
      />
    </SemiFinishedStack.Navigator>
  );
};

const EncastageStackScreen = () => {
  return (
    <EncastageStack.Navigator headerMode={'none'}>
      <EncastageStack.Screen
        name="EncastageList"
        component={EncastageList}
        options={{
          title: '装窑管理',
        }}
      />
      <EncastageStack.Screen name="EncastageAdd" component={EncastageAdd} />
    </EncastageStack.Navigator>
  );
};

const WarmManageStackScreen = () => {
  return (
    <WarmManageStack.Navigator headerMode={'none'}>
      <WarmManageStack.Screen
        name="WarmRecordList"
        component={WarmRecordList}
        options={{title: '温度管理'}}
      />
      <WarmManageStack.Screen name="AddWarmRecord" component={AddWarmRecord} />
      <WarmManageStack.Screen name="FireRecord" component={FireRecord} />
    </WarmManageStack.Navigator>
  );
};

const CheckOutStackScreen = () => {
  return (
    <CheckOutStack.Navigator headerMode={'none'}>
      <CheckOutStack.Screen
        name="CheckOutList"
        component={CheckOutList}
        options={{title: '出库管理'}}
      />
      <CheckOutStack.Screen name="CheckOutAdd" component={CheckOutAdd} />
    </CheckOutStack.Navigator>
  );
};

const PersonalCenterStackScreen = () => {
  return (
    <PersonalCenterStack.Navigator
      headerMode={'none'}
      initialRouteName="SettingHome">
      <PersonalCenterStack.Screen name="SettingHome" component={SettingHome} />
      <PersonalCenterStack.Screen name="Profile" component={Profile} />
      <PersonalCenterStack.Screen name="ResetPwd" component={ResetPwd} />
      <PersonalCenterStack.Screen
        name="BelongsProductionLineSetting"
        component={BelongsProductionLineSetting}
      />
      <PersonalCenterStack.Screen
        name="SuggestionFeedbackAdd"
        component={SuggestionFeedbackAdd}
      />
      <PersonalCenterStack.Screen
        name="SuggestionFeedbackList"
        component={SuggestionFeedbackList}
      />
      <PersonalCenterStack.Screen
        name="MessageRemind"
        component={MessageRemind}
      />
      <PersonalCenterStack.Screen
        name="HelpCenterList"
        component={HelpCenterList}
      />
      <PersonalCenterStack.Screen name="DailyList" component={DailyList} />
      <PersonalCenterStack.Screen
        name="MyBacklogDailyList"
        component={MyBacklogDailyList}
      />
      <PersonalCenterStack.Screen
        name="PromotionPlanList"
        component={PromotionPlanList}
      />
      <PersonalCenterStack.Screen
        name="HarvestMgrList"
        component={HarvestMgrList}
      />
      <PersonalCenterStack.Screen name="DailyDetail" component={DailyDetail} />
      <PersonalCenterStack.Screen name="DailyAdd" component={DailyAdd} />
      <PersonalCenterStack.Screen
        name="PromotionPlanAdd"
        component={PromotionPlanAdd}
      />
      <PersonalCenterStack.Screen
        name="PromotionPlanDetail"
        component={PromotionPlanDetail}
      />
      <PersonalCenterStack.Screen
        name="PromotionPlanSelUser"
        component={PromotionPlanSelUser}
      />
      <PersonalCenterStack.Screen
        name="HarvestMgrAdd"
        component={HarvestMgrAdd}
      />
      <PersonalCenterStack.Screen
        name="HarvestDetail"
        component={HarvestDetail}
      />
    </PersonalCenterStack.Navigator>
  );
};

const CustomerListStackScreen = () => {
  return (
    <CustomerListStack.Navigator headerMode={'none'}>
      <CustomerListStack.Screen name="CustomerList" component={CustomerList} />
      <CustomerListStack.Screen name="CustomerAdd" component={CustomerAdd} />
    </CustomerListStack.Navigator>
  );
};

const OrderListStackScreen = () => {
  return (
    <OrderListStack.Navigator headerMode={'none'}>
      <OrderListStack.Screen name="OrderList" component={OrderList} />
      <OrderListStack.Screen name="OrderAdd" component={OrderAdd} />
      <OrderListStack.Screen
        name="OrderStateTracking"
        component={OrderStateTracking}
      />
    </OrderListStack.Navigator>
  );
};

const OrderSchedulingListStackScreen = () => {
  return (
    <OrderSchedulingListStack.Navigator headerMode={'none'}>
      <OrderSchedulingListStack.Screen
        name="OrderSchedulingList"
        component={OrderSchedulingList}
      />
      <OrderSchedulingListStack.Screen
        name="OrderScheduling"
        component={OrderScheduling}
      />
    </OrderSchedulingListStack.Navigator>
  );
};

const SinteringListStackScreen = () => {
  return (
    <SinteringListStack.Navigator headerMode={'none'}>
      <SinteringListStack.Screen
        name="SinteringList"
        component={SinteringList}
      />
      <SinteringListStack.Screen name="SinteringAdd" component={SinteringAdd} />
    </SinteringListStack.Navigator>
  );
};

const StorageInListStackScreen = () => {
  return (
    <StorageInListStack.Navigator headerMode={'none'}>
      <StorageInListStack.Screen
        name="StorageInList"
        component={StorageInList}
      />
      <StorageInListStack.Screen name="StorageInAdd" component={StorageInAdd} />
    </StorageInListStack.Navigator>
  );
};

const StorageOutListStackScreen = () => {
  return (
    <StorageOutListStack.Navigator headerMode={'none'}>
      <StorageOutListStack.Screen
        name="StorageOutList"
        component={StorageOutList}
      />
      <StorageOutListStack.Screen
        name="StorageOutAdd"
        component={StorageOutAdd}
      />
      <StorageOutListStack.Screen
        name="StorageOutAddSelDetail"
        component={StorageOutAddSelDetail}
      />
    </StorageOutListStack.Navigator>
  );
};

const KilnCarMgrListStackScreen = () => {
  return (
    <KilnCarMgrListStack.Navigator headerMode={'none'}>
      <KilnCarMgrListStack.Screen
        name="KilnCarMgrList"
        component={KilnCarMgrList}
      />
      <KilnCarMgrListStack.Screen
        name="KilnCarMgrAdd"
        component={KilnCarMgrAdd}
      />
    </KilnCarMgrListStack.Navigator>
  );
};

const MachineMgrListStackScreen = () => {
  return (
    <MachineMgrListStack.Navigator headerMode={'none'}>
      <MachineMgrListStack.Screen
        name="MachineMgrList"
        component={MachineMgrList}
      />
      <MachineMgrListStack.Screen
        name="MachineMgrAdd"
        component={MachineMgrAdd}
      />
    </MachineMgrListStack.Navigator>
  );
};

const BrickClassifyMgrListStackScreen = () => {
  return (
    <BrickClassifyMgrListStack.Navigator headerMode={'none'}>
      <BrickClassifyMgrListStack.Screen
        name="BrickClassifyMgrList"
        component={BrickClassifyMgrList}
      />
      <BrickClassifyMgrListStack.Screen
        name="BrickClassifyMgrAdd"
        component={BrickClassifyMgrAdd}
      />
      <BrickClassifyMgrListStack.Screen
        name="BrickClassifySeriesMgrList"
        component={BrickClassifySeriesMgrList}
      />
      <BrickClassifyMgrListStack.Screen
        name="BrickClassifySeriesMgrAdd"
        component={BrickClassifySeriesMgrAdd}
      />
      <BrickClassifyMgrListStack.Screen
        name="BrickTypeMgrList"
        component={BrickTypeMgrList}
      />
      <BrickClassifyMgrListStack.Screen
        name="BrickTypeMgrAdd"
        component={BrickTypeMgrAdd}
      />
    </BrickClassifyMgrListStack.Navigator>
  );
};

const RoleListStackScreen = () => {
  return (
    <RoleListStack.Navigator headerMode={'none'}>
      <RoleListStack.Screen name="RoleList" component={RoleList} />
      <RoleListStack.Screen name="RoleAdd" component={RoleAdd} />
      <RoleListStack.Screen name="RoleMenuList" component={RoleMenuList} />
      <RoleListStack.Screen name="RoleMenuAdd" component={RoleMenuAdd} />
      <RoleListStack.Screen name="RoleUserList" component={RoleUserList} />
      <RoleListStack.Screen name="RoleUserAdd" component={RoleUserAdd} />
      <RoleListStack.Screen
        name="UngradedCauseMgrAdd"
        component={UngradedCauseMgrAdd}
      />
      <RoleListStack.Screen
        name="UngradedCauseMgrList"
        component={UngradedCauseMgrList}
      />
    </RoleListStack.Navigator>
  );
};

const ContractStackScreen = () => {
  return (
    <ContractStack.Navigator headerMode={'none'}>
      <ContractStack.Screen name="ContractList" component={ContractList} />
      <ContractStack.Screen name="ContractAdd" component={ContractAdd} />
    </ContractStack.Navigator>
  );
};

const DEMOStackScreen = () => {
  return (
    <DEMOStack.Navigator headerMode={'none'}>
      <DEMOStack.Screen name="ReactNativeDemo" component={ReactNativeDemo} />
    </DEMOStack.Navigator>
  );
};

//实习平台首页
const HomePageStackScreen = () => {
  return (
    <HomePageStack.Navigator headerMode={'none'}>
      <HomePageStack.Screen name="WelcomePage" component={WelcomePage} />
      <HomePageStack.Screen
        name="StudentInterViewInvited"
        component={StudentInterViewInvited}
      />
      <HomePageStack.Screen
        name="StudentMyChance"
        component={StudentMyChance}
      />
    </HomePageStack.Navigator>
  );
};

//校招
const SchoolreCruitmentStackScreen = () => {
  return (
    <SchoolreCruitmentStack.Navigator headerMode={'none'}>
      <SchoolreCruitmentStack.Screen
        name="CollegPositionQuery"
        component={CollegPositionQuery}
      />
      <SchoolreCruitmentStack.Screen
        name="EnterprisecrHiringPositionDetail"
        component={EnterprisecrHiringPositionDetail}
      />
    </SchoolreCruitmentStack.Navigator>
  );
};

//我的
const MyselfStackScreen = () => {
  return (
    <MyselfStack.Navigator headerMode={'none'}>
      <MyselfStack.Screen name="Myself" component={Myself} />
      <MyselfStack.Screen name="ChangePassword" component={ChangePassword} />
      <MyselfStack.Screen name="Feedback" component={Feedback} />
      {/* <MyselfStack.Screen
        name="StudentMyInterViewPreview"
        component={StudentMyInterViewPreview}
      /> */}
    </MyselfStack.Navigator>
  );
};

//消息提醒
const MessageRemindStackScreen = () => {
  return (
    <MessageRemindStack.Navigator headerMode={'none'}>
      <MessageRemindStack.Screen
        name="MessageRemind"
        component={MessageRemind}
      />
    </MessageRemindStack.Navigator>
  );
};

//学社首页
const DigitalEmployeesHomeStackScreen = () => {
  return (
    <DigitalEmployeesHomeStack.Navigator
      initialRouteName="DigitalEmployeesHome"
      headerMode={'none'}>
      <DigitalEmployeesHomeStack.Screen
        name="DigitalEmployeesHome"
        component={DigitalEmployeesHome}
      />

      <HomeStack.Screen name="Home" component={Home} />
      <HomeStack.Screen name="WelcomePage" component={WelcomePage} />

      <HomeStack.Screen name="OrgMgrAdd" component={OrgMgrAdd} />
      <HomeStack.Screen name="OrgMgrList" component={OrgMgrList} />
      <HomeStack.Screen name="OrderAdd" component={OrderAdd} />
      <HomeStack.Screen name="OrderList" component={OrderList} />
      <HomeStack.Screen name="OrderScheduling" component={OrderScheduling} />
      <HomeStack.Screen
        name="OrderSchedulingList"
        component={OrderSchedulingList}
      />
      <HomeStack.Screen name="OrderOperateList" component={OrderOperateList} />
      <HomeStack.Screen
        name="OrderStateTracking"
        component={OrderStateTracking}
      />
      <HomeStack.Screen
        name="OutsourcingProcess"
        component={OutsourcingProcess}
      />
      <HomeStack.Screen
        name="CheckClassifyMaterialPurchaseList"
        component={CheckClassifyMaterialPurchaseList}
      />
      <HomeStack.Screen
        name="CheckClassifyMaterialStorageList"
        component={CheckClassifyMaterialStorageList}
      />
      <HomeStack.Screen
        name="CheckClassifyMaterialCheckoutList"
        component={CheckClassifyMaterialCheckoutList}
      />
      <HomeStack.Screen
        name="CheckClassifyMaterialInventoryList"
        component={CheckClassifyMaterialInventoryList}
      />
      <HomeStack.Screen
        name="MaterialInventoryInAdd"
        component={MaterialInventoryInAdd}
      />
      <HomeStack.Screen
        name="MaterialInventoryInList"
        component={MaterialInventoryInList}
      />
      <HomeStack.Screen
        name="MaterialInventoryOutAdd"
        component={MaterialInventoryOutAdd}
      />
      <HomeStack.Screen
        name="MaterialInventoryOutList"
        component={MaterialInventoryOutList}
      />
      <HomeStack.Screen
        name="MaterialInventoryList"
        component={MaterialInventoryList}
      />
      <HomeStack.Screen
        name="MaterialInventoryLocationChange"
        component={MaterialInventoryLocationChange}
      />

      <HomeStack.Screen name="CustomerAdd" component={CustomerAdd} />
      <HomeStack.Screen name="CustomerList" component={CustomerList} />
      <HomeStack.Screen name="ContractAdd" component={ContractAdd} />
      <HomeStack.Screen name="ContractList" component={ContractList} />
      <HomeStack.Screen
        name="ContractAcceptMoneyMgr"
        component={ContractAcceptMoneyMgr}
      />
      <HomeStack.Screen
        name="ContractProgressQuery"
        component={ContractProgressQuery}
      />
      <HomeStack.Screen
        name="ContractProgressDetail"
        component={ContractProgressDetail}
      />
      <HomeStack.Screen
        name="ContractTrackingAdd"
        component={ContractTrackingAdd}
      />
      <HomeStack.Screen
        name="ContractTrackingList"
        component={ContractTrackingList}
      />
      <HomeStack.Screen
        name="CollectMoneyPointAdd"
        component={CollectMoneyPointAdd}
      />
      <HomeStack.Screen
        name="CollectMoneyPointList"
        component={CollectMoneyPointList}
      />
      <HomeStack.Screen
        name="CollectMoneyPlanAdd"
        component={CollectMoneyPlanAdd}
      />
      <HomeStack.Screen
        name="CollectMoneyPlanList"
        component={CollectMoneyPlanList}
      />
      <HomeStack.Screen
        name="CollectMoneyActualAdd"
        component={CollectMoneyActualAdd}
      />
      <HomeStack.Screen
        name="CollectMoneyActualList"
        component={CollectMoneyActualList}
      />
      <HomeStack.Screen
        name="ExpenditureContractReport"
        component={ExpenditureContractReport}
      />
      <HomeStack.Screen name="ContractTracking" component={ContractTracking} />

      <HomeStack.Screen name="SemiFinishedAdd" component={SemiFinishedAdd} />
      <HomeStack.Screen name="SemiFinishedList" component={SemiFinishedList} />
      <HomeStack.Screen name="EncastageAdd" component={EncastageAdd} />
      <HomeStack.Screen name="EncastageList" component={EncastageList} />
      <HomeStack.Screen name="AddWarmRecord" component={AddWarmRecord} />
      <HomeStack.Screen name="WarmRecordList" component={WarmRecordList} />
      <HomeStack.Screen name="FireRecord" component={FireRecord} />
      <HomeStack.Screen name="UnLoadedKilnAdd" component={UnLoadedKilnAdd} />
      <HomeStack.Screen name="UnLoadedKilnList" component={UnLoadedKilnList} />
      <HomeStack.Screen name="CheckInList" component={CheckInList} />
      <HomeStack.Screen name="CheckOutList" component={CheckOutList} />
      <HomeStack.Screen name="CheckOutAdd" component={CheckOutAdd} />
      <HomeStack.Screen name="ResetPwd" component={ResetPwd} />
      <HomeStack.Screen
        name="BelongsProductionLineSetting"
        component={BelongsProductionLineSetting}
      />
      <HomeStack.Screen name="Profile" component={Profile} />
      <HomeStack.Screen name="SinteringList" component={SinteringList} />
      <HomeStack.Screen name="SinteringAdd" component={SinteringAdd} />

      <HomeStack.Screen name="DryKoleMgrAdd" component={DryKoleMgrAdd} />
      <HomeStack.Screen name="DryKoleMgrList" component={DryKoleMgrList} />
      <HomeStack.Screen name="DryKoleInMgrAdd" component={DryKoleInMgrAdd} />
      <HomeStack.Screen name="DryKoleInMgrList" component={DryKoleInMgrList} />
      <HomeStack.Screen name="DryKoleOutMgrAdd" component={DryKoleOutMgrAdd} />
      <HomeStack.Screen
        name="DryKoleOutMgrList"
        component={DryKoleOutMgrList}
      />
      <HomeStack.Screen
        name="DryKoleWasteCauseMgrAdd"
        component={DryKoleWasteCauseMgrAdd}
      />
      <HomeStack.Screen
        name="DryKoleWasteCauseMgrList"
        component={DryKoleWasteCauseMgrList}
      />

      <HomeStack.Screen name="LocationAreaList" component={LocationAreaList} />
      <HomeStack.Screen name="LocationAreaAdd" component={LocationAreaAdd} />
      <HomeStack.Screen
        name="StorageLocationList"
        component={StorageLocationList}
      />
      <HomeStack.Screen
        name="StorageLocationAdd"
        component={StorageLocationAdd}
      />
      <HomeStack.Screen name="StorageInList" component={StorageInList} />
      <HomeStack.Screen name="StorageInAdd" component={StorageInAdd} />
      <HomeStack.Screen
        name="InventoryStorageAdd"
        component={InventoryStorageAdd}
      />
      <HomeStack.Screen name="StorageOutList" component={StorageOutList} />
      <HomeStack.Screen name="StorageOutAdd" component={StorageOutAdd} />
      <HomeStack.Screen
        name="StorageOutAddSelDetail"
        component={StorageOutAddSelDetail}
      />
      <HomeStack.Screen name="InventoryQuery" component={InventoryQuery} />
      <HomeStack.Screen
        name="InventoryBrickTypeQuery"
        component={InventoryBrickTypeQuery}
      />
      <HomeStack.Screen
        name="InventoryLocationQuery"
        component={InventoryLocationQuery}
      />
      <HomeStack.Screen
        name="InventoryDetailBrickClassifyList"
        component={InventoryDetailBrickClassifyList}
      />
      <HomeStack.Screen
        name="InventoryDetailBrickClassifySeriesList"
        component={InventoryDetailBrickClassifySeriesList}
      />
      <HomeStack.Screen
        name="InventoryDetailBrickTypeList"
        component={InventoryDetailBrickTypeList}
      />
      <HomeStack.Screen
        name="InventoryDetailList"
        component={InventoryDetailList}
      />
      <HomeStack.Screen name="InventoryAdjust" component={InventoryAdjust} />
      <HomeStack.Screen name="YieldQuery" component={YieldQuery} />
      <HomeStack.Screen
        name="ProductCheckMgrAdd"
        component={ProductCheckMgrAdd}
      />
      <HomeStack.Screen
        name="ProductCheckMgrList"
        component={ProductCheckMgrList}
      />
      <HomeStack.Screen
        name="EngineeringAcceptanceMgrAdd"
        component={EngineeringAcceptanceMgrAdd}
      />
      <HomeStack.Screen
        name="EngineeringAcceptanceMgrList"
        component={EngineeringAcceptanceMgrList}
      />
      <HomeStack.Screen
        name="EngineeringBackMgrAdd"
        component={EngineeringBackMgrAdd}
      />
      <HomeStack.Screen
        name="EngineeringBackMgrList"
        component={EngineeringBackMgrList}
      />
      <HomeStack.Screen
        name="OutsourcingStorageOutAdd"
        component={OutsourcingStorageOutAdd}
      />
      <HomeStack.Screen
        name="OutsourcingStorageOutList"
        component={OutsourcingStorageOutList}
      />
      <HomeStack.Screen
        name="OutsourcingStorageOutAddSelDetail"
        component={OutsourcingStorageOutAddSelDetail}
      />
      <HomeStack.Screen
        name="BlockWorkStorageInMgrList"
        component={BlockWorkStorageInMgrList}
      />
      <HomeStack.Screen
        name="BlockWorkStorageInMgrAdd"
        component={BlockWorkStorageInMgrAdd}
      />
      <HomeStack.Screen
        name="BlockWorkStorageOutMgrList"
        component={BlockWorkStorageOutMgrList}
      />
      <HomeStack.Screen
        name="BlockWorkStorageOutMgrAdd"
        component={BlockWorkStorageOutMgrAdd}
      />
      <HomeStack.Screen
        name="BlockWorkStorageOutMgrAddDetail"
        component={BlockWorkStorageOutMgrAddDetail}
      />
      <HomeStack.Screen
        name="InventoryBlockWorkList"
        component={InventoryBlockWorkList}
      />
      <HomeStack.Screen
        name="InventoryBlockWorkContractList"
        component={InventoryBlockWorkContractList}
      />
      <HomeStack.Screen
        name="InventoryBlockWorkPositionList"
        component={InventoryBlockWorkPositionList}
      />
      <HomeStack.Screen
        name="InventoryBlockWorkDetailList"
        component={InventoryBlockWorkDetailList}
      />

      <HomeStack.Screen
        name="PaymentObjectList"
        component={PaymentObjectList}
      />
      <HomeStack.Screen name="PaymentObjectAdd" component={PaymentObjectAdd} />
      <HomeStack.Screen name="PaymentClassList" component={PaymentClassList} />
      <HomeStack.Screen name="PaymentApplyList" component={PaymentApplyList} />
      <HomeStack.Screen name="PaymentAuditList" component={PaymentAuditList} />
      <HomeStack.Screen name="PaymentApplyAdd" component={PaymentApplyAdd} />
      <HomeStack.Screen name="PaymentClassAdd" component={PaymentClassAdd} />
      <HomeStack.Screen
        name="PaymengApplyDetail"
        component={PaymengApplyDetail}
      />
      <HomeStack.Screen name="PaymentAudit" component={PaymentAudit} />

      <HomeStack.Screen
        name="VerifyInternalSettingList"
        component={VerifyInternalSettingList}
      />
      <HomeStack.Screen
        name="VerifyInternalSettingAdd"
        component={VerifyInternalSettingAdd}
      />
      <HomeStack.Screen
        name="VerifyInternalStandardList"
        component={VerifyInternalStandardList}
      />
      <HomeStack.Screen
        name="VerifyInternalStandardAdd"
        component={VerifyInternalStandardAdd}
      />
      <HomeStack.Screen
        name="VerifyInternalResultList"
        component={VerifyInternalResultList}
      />
      <HomeStack.Screen
        name="VerifyInternalResultAdd"
        component={VerifyInternalResultAdd}
      />
      <HomeStack.Screen
        name="VerifyExternalSettingList"
        component={VerifyExternalSettingList}
      />
      <HomeStack.Screen
        name="VerifyExternalSettingAdd"
        component={VerifyExternalSettingAdd}
      />
      <HomeStack.Screen
        name="VerifyExternalStandardList"
        component={VerifyExternalStandardList}
      />
      <HomeStack.Screen
        name="VerifyExternalStandardAdd"
        component={VerifyExternalStandardAdd}
      />
      <HomeStack.Screen
        name="VerifyExternalResultList"
        component={VerifyExternalResultList}
      />
      <HomeStack.Screen
        name="VerifyExternalResultAdd"
        component={VerifyExternalResultAdd}
      />

      <HomeStack.Screen name="KilnCarMgrList" component={KilnCarMgrList} />
      <HomeStack.Screen name="KilnCarMgrAdd" component={KilnCarMgrAdd} />
      <HomeStack.Screen name="MachineMgrList" component={MachineMgrList} />
      <HomeStack.Screen name="MachineMgrAdd" component={MachineMgrAdd} />

      <HomeStack.Screen
        name="BrickClassifySeriesMgrList"
        component={BrickClassifySeriesMgrList}
      />
      <HomeStack.Screen
        name="BrickClassifySeriesMgrAdd"
        component={BrickClassifySeriesMgrAdd}
      />
      <HomeStack.Screen
        name="BrickClassifyMgrList"
        component={BrickClassifyMgrList}
      />
      <HomeStack.Screen
        name="BrickClassifyMgrAdd"
        component={BrickClassifyMgrAdd}
      />
      <HomeStack.Screen name="BrickTypeMgrList" component={BrickTypeMgrList} />
      <HomeStack.Screen name="BrickTypeMgrAdd" component={BrickTypeMgrAdd} />

      <HomeStack.Screen name="DepartmentList" component={DepartmentList} />
      <HomeStack.Screen name="DepartmentAdd" component={DepartmentAdd} />
      <HomeStack.Screen name="JobMgrList" component={JobMgrList} />
      <HomeStack.Screen name="JobMgrAdd" component={JobMgrAdd} />
      <HomeStack.Screen
        name="DepartmentStaffMgrList"
        component={DepartmentStaffMgrList}
      />
      <HomeStack.Screen name="JobStaffMgrList" component={JobStaffMgrList} />
      <HomeStack.Screen name="JobStaffMgrAdd" component={JobStaffMgrAdd} />
      <HomeStack.Screen name="EquipmentMgrList" component={EquipmentMgrList} />
      <HomeStack.Screen name="EquipmentMgrAdd" component={EquipmentMgrAdd} />
      <HomeStack.Screen
        name="EquipmentStateMgrAdd"
        component={EquipmentStateMgrAdd}
      />
      <HomeStack.Screen
        name="EquipmentStateMgrList"
        component={EquipmentStateMgrList}
      />
      <HomeStack.Screen
        name="CheckEquipmentList"
        component={CheckEquipmentList}
      />
      <HomeStack.Screen
        name="CheckEquipmentStateList"
        component={CheckEquipmentStateList}
      />
      <HomeStack.Screen
        name="CheckDepartmentList"
        component={CheckDepartmentList}
      />
      <HomeStack.Screen
        name="WorkingShiftRelStaffMgr"
        component={WorkingShiftRelStaffMgr}
      />
      <HomeStack.Screen
        name="PortalStaffMgrAdd"
        component={PortalStaffMgrAdd}
      />
      <HomeStack.Screen
        name="PortalStaffMgrList"
        component={PortalStaffMgrList}
      />
      <HomeStack.Screen name="ProductSummart" component={ProductSummart} />
      <HomeStack.Screen
        name="ProductionQtyQuery"
        component={ProductionQtyQuery}
      />
      <HomeStack.Screen
        name="EnterpriseInvitedApply"
        component={EnterpriseInvitedApply}
      />
      <HomeStack.Screen
        name="InformationConfigAdd"
        component={InformationConfigAdd}
      />
      <HomeStack.Screen
        name="InformationConfigList"
        component={InformationConfigList}
      />

      <HomeStack.Screen name="DailyList" component={DailyList} />
      <HomeStack.Screen name="DailyAdd" component={DailyAdd} />
      <HomeStack.Screen name="DailyMessageList" component={DailyMessageList} />
      <HomeStack.Screen name="DailyMessageAdd" component={DailyMessageAdd} />
      <HomeStack.Screen name="HarvestMgrList" component={HarvestMgrList} />
      <HomeStack.Screen name="HarvestMgrAdd" component={HarvestMgrAdd} />
      <HomeStack.Screen name="DailyDetail" component={DailyDetail} />

      <HomeStack.Screen name="CourseMgrAdd" component={CourseMgrAdd} />
      <HomeStack.Screen name="CourseMgrList" component={CourseMgrList} />
      <HomeStack.Screen name="GoodHarvestList" component={GoodHarvestList} />
      <HomeStack.Screen
        name="HarvestCircleList"
        component={HarvestCircleList}
      />
      <HomeStack.Screen
        name="HarvestGoodMgrList"
        component={HarvestGoodMgrList}
      />
      <HomeStack.Screen
        name="MyBacklogDailyList"
        component={MyBacklogDailyList}
      />
      <HomeStack.Screen
        name="MyBacklogDailyAdd"
        component={MyBacklogDailyAdd}
      />
      <HomeStack.Screen name="MyDoneDailyList" component={MyDoneDailyList} />
      <HomeStack.Screen name="PromotionPlanAdd" component={PromotionPlanAdd} />
      <HomeStack.Screen
        name="PromotionPlanList"
        component={PromotionPlanList}
      />
      <HomeStack.Screen
        name="PromotionPlanDetail"
        component={PromotionPlanDetail}
      />
      <HomeStack.Screen
        name="PromotionPlanSelUser"
        component={PromotionPlanSelUser}
      />
      <HomeStack.Screen name="ScoreMgrAdd" component={ScoreMgrAdd} />
      <HomeStack.Screen name="ScoreMgrList" component={ScoreMgrList} />
      <HomeStack.Screen
        name="HarvestDiscussAdd"
        component={HarvestDiscussAdd}
      />
      <HomeStack.Screen
        name="HarvestDiscussList"
        component={HarvestDiscussList}
      />
      <HomeStack.Screen
        name="ScoreMgrCourseList"
        component={ScoreMgrCourseList}
      />
      <HomeStack.Screen name="QueryDaily" component={QueryDaily} />
      <HomeStack.Screen name="WorkDaily" component={WorkDaily} />
      <HomeStack.Screen name="QueryHarvest" component={QueryHarvest} />
      <HomeStack.Screen name="QueryMyScore" component={QueryMyScore} />
      <HomeStack.Screen
        name="QueryPromotionPlan"
        component={QueryPromotionPlan}
      />
      <HomeStack.Screen
        name="PortalTrackingList"
        component={PortalTrackingList}
      />
      <HomeStack.Screen
        name="PortalTrackingAdd"
        component={PortalTrackingAdd}
      />
      <HomeStack.Screen
        name="MyDocumentMgrList"
        component={MyDocumentMgrList}
      />
      <HomeStack.Screen
        name="DocumentLibraryMgrList"
        component={DocumentLibraryMgrList}
      />
      <HomeStack.Screen name="ViewDocument" component={ViewDocument} />
      <HomeStack.Screen
        name="VideoLibraryMgrList"
        component={VideoLibraryMgrList}
      />

      <HomeStack.Screen name="PointConfig" component={PointConfig} />
      <HomeStack.Screen name="PointConfigAdd" component={PointConfigAdd} />
      <HomeStack.Screen name="PointRecord" component={PointRecord} />
      <HomeStack.Screen name="PointReward" component={PointReward} />
      <HomeStack.Screen name="PointRewardAdd" component={PointRewardAdd} />
      <HomeStack.Screen name="PointExchange" component={PointExchange} />
      <HomeStack.Screen name="PointExchangeAdd" component={PointExchangeAdd} />
      <HomeStack.Screen name="PointRanking" component={PointRanking} />
      <HomeStack.Screen name="ExamConfig" component={ExamConfig} />
      <HomeStack.Screen name="ExamConfigAdd" component={ExamConfigAdd} />
      <HomeStack.Screen name="FormCollection" component={FormCollection} />
      <HomeStack.Screen name="ExamApply" component={ExamApply} />
      <HomeStack.Screen name="ExamApplyAdd" component={ExamApplyAdd} />
      <HomeStack.Screen name="CourseScheduling" component={CourseScheduling} />
      <HomeStack.Screen
        name="CourseSchedulingAdd"
        component={CourseSchedulingAdd}
      />
      <HomeStack.Screen
        name="CourseSchedulingTable"
        component={CourseSchedulingTable}
      />
      <HomeStack.Screen name="ExamApplyAudit" component={ExamApplyAudit} />
      <HomeStack.Screen name="SYPointMall" component={SYPointMall} />
      <HomeStack.Screen name="SYContactUs" component={SYContactUs} />
      <HomeStack.Screen name="AssessClassList" component={AssessClassList} />
      <HomeStack.Screen name="AssessClassAdd" component={AssessClassAdd} />
      <HomeStack.Screen name="AssessApplyAdd" component={AssessApplyAdd} />
      <HomeStack.Screen name="AssessApplyList" component={AssessApplyList} />
      <HomeStack.Screen name="AssessAuditList" component={AssessAuditList} />
      <HomeStack.Screen name="AssessQueryList" component={AssessQueryList} />
      <HomeStack.Screen name="AssessAudit" component={AssessAudit} />
      <HomeStack.Screen name="MyReceiveAdd" component={MyReceiveAdd} />
      <HomeStack.Screen name="MyReceiveList" component={MyReceiveList} />
      <HomeStack.Screen
        name="AskQuestionsQuery"
        component={AskQuestionsQuery}
      />
      <HomeStack.Screen
        name="MyAskQuestionsAdd"
        component={MyAskQuestionsAdd}
      />
      <HomeStack.Screen
        name="MyAskQuestionsList"
        component={MyAskQuestionsList}
      />
      <HomeStack.Screen
        name="AskQuestionsSolveTrackingAdd"
        component={AskQuestionsSolveTrackingAdd}
      />
      <HomeStack.Screen
        name="AskQuestionsSolveTrackingList"
        component={AskQuestionsSolveTrackingList}
      />

      <HomeStack.Screen name="CustomerLeadList" component={CustomerLeadList} />
      <HomeStack.Screen name="CustomerLeadAdd" component={CustomerLeadAdd} />
      <HomeStack.Screen name="LeadAuditList" component={LeadAuditList} />
      <HomeStack.Screen
        name="LeadTeleInvitationList"
        component={LeadTeleInvitationList}
      />
      <HomeStack.Screen name="LeadVisitList" component={LeadVisitList} />
      <HomeStack.Screen
        name="SaleopportunityList"
        component={SaleopportunityList}
      />
      <HomeStack.Screen
        name="SaleopportunityAdd"
        component={SaleopportunityAdd}
      />

      <HomeStack.Screen
        name="CollegClassGradesAdd"
        component={CollegClassGradesAdd}
      />
      <HomeStack.Screen
        name="CollegClassGradesList"
        component={CollegClassGradesList}
      />
      <HomeStack.Screen
        name="CollegClassStudentList"
        component={CollegClassStudentList}
      />
      <HomeStack.Screen
        name="CollegProfessionalList"
        component={CollegProfessionalList}
      />
      <HomeStack.Screen
        name="CollegProfessionalAdd"
        component={CollegProfessionalAdd}
      />
      <HomeStack.Screen name="CollegStudentAdd" component={CollegStudentAdd} />
      <HomeStack.Screen
        name="CollegStudentList"
        component={CollegStudentList}
      />
      <HomeStack.Screen
        name="CollegStudentResumeQuery"
        component={CollegStudentResumeQuery}
      />
      <HomeStack.Screen
        name="EnterpriseInvitedInterView"
        component={EnterpriseInvitedInterView}
      />
      <HomeStack.Screen
        name="EnterpriseResumeCollection"
        component={EnterpriseResumeCollection}
      />
      <HomeStack.Screen
        name="PortalEnterpriseAdd"
        component={PortalEnterpriseAdd}
      />
      <HomeStack.Screen
        name="PortalEnterpriseList"
        component={PortalEnterpriseList}
      />
      <HomeStack.Screen
        name="StudentInterViewInvited"
        component={StudentInterViewInvited}
      />
      <HomeStack.Screen name="StudentMyChance" component={StudentMyChance} />
      {/* <HomeStack.Screen
        name="StudentMyInterViewPreview"
        component={StudentMyInterViewPreview}
      /> */}
      <HomeStack.Screen
        name="EnterpriseRecruiterList"
        component={EnterpriseRecruiterList}
      />
      <HomeStack.Screen
        name="EnterpriseRecruiterAdd"
        component={EnterpriseRecruiterAdd}
      />
      <HomeStack.Screen
        name="EnterprisecrHiringPositionAdd"
        component={EnterprisecrHiringPositionAdd}
      />
      <HomeStack.Screen
        name="EnterprisecrHiringPositionList"
        component={EnterprisecrHiringPositionList}
      />
      <HomeStack.Screen
        name="EnterprisecrHiringPositionDetail"
        component={EnterprisecrHiringPositionDetail}
      />
      <HomeStack.Screen
        name="CollegPositionQuery"
        component={CollegPositionQuery}
      />
      <HomeStack.Screen
        name="PersonalInformation"
        component={PersonalInformation}
      />
      <HomeStack.Screen name="PersonalHonor" component={PersonalHonor} />
      <HomeStack.Screen
        name="CollegeEvaluation"
        component={CollegeEvaluation}
      />
      <HomeStack.Screen name="NewItem" component={NewItem} />
      <HomeStack.Screen
        name="EnterprisePositionDetail"
        component={EnterprisePositionDetail}
      />
      <HomeStack.Screen
        name="MemberManagementAdd"
        component={MemberManagementAdd}
      />
      <HomeStack.Screen
        name="MemberManagementList"
        component={MemberManagementList}
      />
      <HomeStack.Screen
        name="MemberManagementDetail"
        component={MemberManagementDetail}
      />
      <HomeStack.Screen
        name="MemberManagementExamine"
        component={MemberManagementExamine}
      />
      <HomeStack.Screen
        name="MemberManagementExamineDetail"
        component={MemberManagementExamineDetail}
      />
      <HomeStack.Screen name="MemberTypeAdd" component={MemberTypeAdd} />
      <HomeStack.Screen name="MemberTypeList" component={MemberTypeList} />
      <HomeStack.Screen
        name="ConfiguringTenants"
        component={ConfiguringTenants}
      />
      <HomeStack.Screen
        name="ConfiguringTenantsParam"
        component={ConfiguringTenantsParam}
      />
      <HomeStack.Screen
        name="ConfiguringTenantsParamItem"
        component={ConfiguringTenantsParamItem}
      />
      <HomeStack.Screen
        name="TenantEnterpriseAdd"
        component={TenantEnterpriseAdd}
      />
      <HomeStack.Screen
        name="TenantEnterpriseList"
        component={TenantEnterpriseList}
      />
      <HomeStack.Screen
        name="PortalTenantParam"
        component={PortalTenantParam}
      />
      <HomeStack.Screen
        name="PortalTenantParamAdd"
        component={PortalTenantParamAdd}
      />
      <HomeStack.Screen
        name="PortalTenantParamItem"
        component={PortalTenantParamItem}
      />
      <HomeStack.Screen
        name="PortalTenantParamData"
        component={PortalTenantParamData}
      />
      <HomeStack.Screen
        name="PortalTenantParamDataAdd"
        component={PortalTenantParamDataAdd}
      />
      <HomeStack.Screen
        name="HomeResourceDisplay"
        component={HomeResourceDisplay}
      />
      <HomeStack.Screen
        name="MemberContactConfig"
        component={MemberContactConfig}
      />
      <HomeStack.Screen
        name="MemberContactConfigAdd"
        component={MemberContactConfigAdd}
      />
      <HomeStack.Screen name="ConfigPreview" component={ConfigPreview} />
      <HomeStack.Screen name="PermissionList" component={PermissionList} />
      <HomeStack.Screen
        name="PermissionUserList"
        component={PermissionUserList}
      />
      <HomeStack.Screen
        name="PermissionUserAddList"
        component={PermissionUserAddList}
      />

      <HomeStack.Screen
        name="NaturalGasPurchaseList"
        component={NaturalGasPurchaseList}
      />
      <HomeStack.Screen
        name="NaturalGasPurchaseAdd"
        component={NaturalGasPurchaseAdd}
      />
      <HomeStack.Screen name="SupplierMgrList" component={SupplierMgrList} />
      <HomeStack.Screen name="SupplierMgrAdd" component={SupplierMgrAdd} />
      <HomeStack.Screen
        name="AuditConfigMgrList"
        component={AuditConfigMgrList}
      />
      <HomeStack.Screen
        name="AuditConfigMgrAdd"
        component={AuditConfigMgrAdd}
      />
      <HomeStack.Screen name="AuditPointList" component={AuditPointList} />
      <HomeStack.Screen name="AuditPointAdd" component={AuditPointAdd} />
      <HomeStack.Screen
        name="AuditCcConfigurationList"
        component={AuditCcConfigurationList}
      />
      <HomeStack.Screen
        name="AuditCcConfigurationAdd"
        component={AuditCcConfigurationAdd}
      />
      <HomeStack.Screen
        name="ExpenditureContractList"
        component={ExpenditureContractList}
      />
      <HomeStack.Screen
        name="ExpenditureContractAdd"
        component={ExpenditureContractAdd}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyPointList"
        component={ExpenditureMoneyPointList}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyPointAdd"
        component={ExpenditureMoneyPointAdd}
      />
      <HomeStack.Screen
        name="MaterialAuditBacklogList"
        component={MaterialAuditBacklogList}
      />
      <HomeStack.Screen
        name="MaterialAuditBacklogDetail"
        component={MaterialAuditBacklogDetail}
      />
      <HomeStack.Screen
        name="MaterialAuditDoneList"
        component={MaterialAuditDoneList}
      />
      <HomeStack.Screen name="MaterialAudit" component={MaterialAudit} />
      <HomeStack.Screen
        name="InventoryAuditBacklogDetail"
        component={InventoryAuditBacklogDetail}
      />
      <HomeStack.Screen
        name="MaterialInventoryAudit"
        component={MaterialInventoryAudit}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyActualAdd"
        component={ExpenditureMoneyActualAdd}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyActualList"
        component={ExpenditureMoneyActualList}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyPlanAdd"
        component={ExpenditureMoneyPlanAdd}
      />
      <HomeStack.Screen
        name="ExpenditureMoneyPlanList"
        component={ExpenditureMoneyPlanList}
      />

      <HomeStack.Screen
        name="MyProductSaleRelease"
        component={MyProductSaleRelease}
      />
      <HomeStack.Screen
        name="MyProductSaleReleaseAdd"
        component={MyProductSaleReleaseAdd}
      />
      <HomeStack.Screen name="MyAskBugRelease" component={MyAskBugRelease} />
      <HomeStack.Screen
        name="MyAskBugReleaseAdd"
        component={MyAskBugReleaseAdd}
      />
      <HomeStack.Screen
        name="ProductReleaseMarket"
        component={ProductReleaseMarket}
      />
      <HomeStack.Screen
        name="AskBugReleaseMarket"
        component={AskBugReleaseMarket}
      />
      <HomeStack.Screen
        name="ProductReleaseAuditList"
        component={ProductReleaseAuditList}
      />
      <HomeStack.Screen
        name="ProductReleaseAskBuyAuditList"
        component={ProductReleaseAskBuyAuditList}
      />
      <HomeStack.Screen name="ProductHome" component={ProductHome} />
      <HomeStack.Screen name="Product" component={Product} />
      <HomeStack.Screen name="AskForPurchase" component={AskForPurchase} />

      <HomeStack.Screen
        name="CourseLevelMgrAdd"
        component={CourseLevelMgrAdd}
      />
      <HomeStack.Screen
        name="CourseLevelMgrList"
        component={CourseLevelMgrList}
      />
      <HomeStack.Screen
        name="CourseTaskMgrList"
        component={CourseTaskMgrList}
      />
      <HomeStack.Screen
        name="CourseTaskMgrDetail"
        component={CourseTaskMgrDetail}
      />
      <HomeStack.Screen
        name="CourseTrackDetail"
        component={CourseTrackDetail}
      />
      <HomeStack.Screen name="CourseTrackList" component={CourseTrackList} />
      <HomeStack.Screen name="CourseTypeMgrAdd" component={CourseTypeMgrAdd} />
      <HomeStack.Screen
        name="CourseTypeMgrList"
        component={CourseTypeMgrList}
      />
      <HomeStack.Screen name="CourseVidioList" component={CourseVidioList} />
      <HomeStack.Screen name="MyCourseList" component={MyCourseList} />
      <HomeStack.Screen
        name="StudentCourseTrackList"
        component={StudentCourseTrackList}
      />
      <HomeStack.Screen
        name="StudentCourseTrackDetailList"
        component={StudentCourseTrackDetailList}
      />
      <HomeStack.Screen
        name="StudentCourseTrackDetail"
        component={StudentCourseTrackDetail}
      />
      <HomeStack.Screen name="HLHospitalList" component={HLHospitalList} />
      <HomeStack.Screen name="HLHospitalAdd" component={HLHospitalAdd} />
      <HomeStack.Screen name="HLDepartmentList" component={HLDepartmentList} />
      <HomeStack.Screen name="HLDepartmentAdd" component={HLDepartmentAdd} />
      <HomeStack.Screen
        name="HLDepartmentStoreKeeperRelList"
        component={HLDepartmentStoreKeeperRelList}
      />
      <HomeStack.Screen
        name="HLDepartmentStoreKeeperRelAdd"
        component={HLDepartmentStoreKeeperRelAdd}
      />
      <HomeStack.Screen
        name="HLTransferModeAdd"
        component={HLTransferModeAdd}
      />
      <HomeStack.Screen
        name="HLTransforModeList"
        component={HLTransforModeList}
      />
      <HomeStack.Screen name="HLPortalUnitAdd" component={HLPortalUnitAdd} />
      <HomeStack.Screen name="HLPortalUnitList" component={HLPortalUnitList} />
      <HomeStack.Screen
        name="HLPortalSupplierList"
        component={HLPortalSupplierList}
      />
      <HomeStack.Screen
        name="HLPortalSupplierAdd"
        component={HLPortalSupplierAdd}
      />
      <HomeStack.Screen
        name="HLMaterialListingList"
        component={HLMaterialListingList}
      />
      <HomeStack.Screen
        name="HLMaterialListingAdd"
        component={HLMaterialListingAdd}
      />
      <HomeStack.Screen
        name="HLStorageOutTypeList"
        component={HLStorageOutTypeList}
      />
      <HomeStack.Screen
        name="HLStorageOutTypeAdd"
        component={HLStorageOutTypeAdd}
      />
      <HomeStack.Screen name="HLStorageInMList" component={HLStorageInMList} />
      <HomeStack.Screen name="HLStorageInMAdd" component={HLStorageInMAdd} />
      <HomeStack.Screen
        name="HLStorageOutMList"
        component={HLStorageOutMList}
      />
      <HomeStack.Screen name="HLStorageOutMAdd" component={HLStorageOutMAdd} />
      <HomeStack.Screen name="HLStorageInDList" component={HLStorageInDList} />
      <HomeStack.Screen name="HLStorageInDAdd" component={HLStorageInDAdd} />
      <HomeStack.Screen
        name="HLStorageInAddDetail"
        component={HLStorageInAddDetail}
      />
      <HomeStack.Screen
        name="HLStorageInListDetail"
        component={HLStorageInListDetail}
      />
      <HomeStack.Screen
        name="HLStorageOutListDetail"
        component={HLStorageOutListDetail}
      />
      <HomeStack.Screen name="HLStorageOutDAdd" component={HLStorageOutDAdd} />
      <HomeStack.Screen
        name="HLStorageOutAddDetail"
        component={HLStorageOutAddDetail}
      />
      <HomeStack.Screen
        name="HLStorageOutDList"
        component={HLStorageOutDList}
      />
      <HomeStack.Screen
        name="HLMaterialInventoryMList"
        component={HLMaterialInventoryMList}
      />
      <HomeStack.Screen
        name="HLMaterialInventoryDList"
        component={HLMaterialInventoryDList}
      />
      <HomeStack.Screen
        name="HlMaterialInventoryDetailList"
        component={HlMaterialInventoryDetailList}
      />
      <HomeStack.Screen
        name="HLMedicineInventoryDetailList"
        component={HLMedicineInventoryDetailList}
      />
      <HomeStack.Screen
        name="HLStorageInAuditList"
        component={HLStorageInAuditList}
      />
      <HomeStack.Screen
        name="HLStorageOutAuditList"
        component={HLStorageOutAuditList}
      />
      <HomeStack.Screen
        name="HLStorageOutAudit"
        component={HLStorageOutAudit}
      />
      <HomeStack.Screen
        name="HLStorageOutAuditDetail"
        component={HLStorageOutAuditDetail}
      />
      <HomeStack.Screen name="HLStorageInAudit" component={HLStorageInAudit} />
      <HomeStack.Screen
        name="HLStorageInAuditDetail"
        component={HLStorageInAuditDetail}
      />
      <HomeStack.Screen name="HLPharmacyList" component={HLPharmacyList} />
      <HomeStack.Screen name="HlPharmacyAdd" component={HlPharmacyAdd} />
      <HomeStack.Screen name="HLMedicineList" component={HLMedicineList} />
      <HomeStack.Screen name="HLMedicineAdd" component={HLMedicineAdd} />
      <HomeStack.Screen name="HLDoctorList" component={HLDoctorList} />
      <HomeStack.Screen name="HLDoctorAdd" component={HLDoctorAdd} />
      <HomeStack.Screen name="HLSickPersonList" component={HLSickPersonList} />
      <HomeStack.Screen name="HLSickPersonAdd" component={HLSickPersonAdd} />
      <HomeStack.Screen
        name="HLMedicineStorageInList"
        component={HLMedicineStorageInList}
      />
      <HomeStack.Screen
        name="HLMedicineStorageInAdd"
        component={HLMedicineStorageInAdd}
      />
      <HomeStack.Screen
        name="HLMedicineStorageInDetailList"
        component={HLMedicineStorageInDetailList}
      />
      <HomeStack.Screen
        name="HLMedicineStorageInDetailAdd"
        component={HLMedicineStorageInDetailAdd}
      />
      <HomeStack.Screen
        name="HLMedicineStorageOutDetailList"
        component={HLMedicineStorageOutDetailList}
      />
      <HomeStack.Screen
        name="HLMedicineStorageOutDetailAdd"
        component={HLMedicineStorageOutDetailAdd}
      />
      <HomeStack.Screen
        name="HLMedicineStorageOutList"
        component={HLMedicineStorageOutList}
      />
      <HomeStack.Screen
        name="HLMedicineStorageOutAdd"
        component={HLMedicineStorageOutAdd}
      />
      <HomeStack.Screen
        name="HLMedicalInsuranceTypeList"
        component={HLMedicalInsuranceTypeList}
      />
      <HomeStack.Screen
        name="HLMedicalInsuranceTypeAdd"
        component={HLMedicalInsuranceTypeAdd}
      />
      <HomeStack.Screen
        name="HLMedicineInventoryMList"
        component={HLMedicineInventoryMList}
      />
      <HomeStack.Screen
        name="HLMedicineStorageInAudit"
        component={HLMedicineStorageInAudit}
      />
      <HomeStack.Screen
        name="HLMedicineStorageInAuditDetail"
        component={HLMedicineStorageInAuditDetail}
      />
      <HomeStack.Screen name="LinkShareList" component={LinkShareList} />
      <HomeStack.Screen name="PointRecordList" component={PointRecordList} />

      <RoleListStack.Screen name="RoleList" component={RoleList} />
      <RoleListStack.Screen name="RoleAdd" component={RoleAdd} />
      <RoleListStack.Screen name="RoleMenuList" component={RoleMenuList} />
      <RoleListStack.Screen name="RoleMenuAdd" component={RoleMenuAdd} />
      <RoleListStack.Screen name="QuickMenuList" component={QuickMenuList} />
      <RoleListStack.Screen name="QuickMenuAdd" component={QuickMenuAdd} />
      <RoleListStack.Screen name="RoleUserList" component={RoleUserList} />
      <RoleListStack.Screen name="RoleUserAdd" component={RoleUserAdd} />
      <RoleListStack.Screen
        name="UngradedCauseMgrList"
        component={UngradedCauseMgrList}
      />
      <RoleListStack.Screen
        name="UngradedCauseMgrAdd"
        component={UngradedCauseMgrAdd}
      />
      <RoleListStack.Screen
        name="OrderPositionMgrList"
        component={OrderPositionMgrList}
      />
      <RoleListStack.Screen
        name="OrderPositionMgrAdd"
        component={OrderPositionMgrAdd}
      />
      <RoleListStack.Screen
        name="WorkingShiftMgrList"
        component={WorkingShiftMgrList}
      />
      <RoleListStack.Screen
        name="WorkingShiftMgrAdd"
        component={WorkingShiftMgrAdd}
      />

      <PersonalCenterStack.Screen
        name="SuggestionFeedbackAdd"
        component={SuggestionFeedbackAdd}
      />
      <PersonalCenterStack.Screen
        name="SuggestionFeedbackList"
        component={SuggestionFeedbackList}
      />
      <PersonalCenterStack.Screen
        name="HelpCenterList"
        component={HelpCenterList}
      />

      <TenantStack.Screen name="TenantList" component={TenantList} />
      <TenantStack.Screen name="TenantAdd" component={TenantAdd} />
      <TenantStack.Screen
        name="OutsourcingTenantList"
        component={OutsourcingTenantList}
      />
      <TenantStack.Screen
        name="OutsourcingTenantAdd"
        component={OutsourcingTenantAdd}
      />
      <TenantStack.Screen
        name="ProductionLineMgrAdd"
        component={ProductionLineMgrAdd}
      />
      <TenantStack.Screen
        name="ProductionLineMgrList"
        component={ProductionLineMgrList}
      />

      <TenantStack.Screen
        name="MaterialCheckoutAdd"
        component={MaterialCheckoutAdd}
      />
      <TenantStack.Screen
        name="MaterialCheckoutList"
        component={MaterialCheckoutList}
      />
      <TenantStack.Screen
        name="MaterialClassifyAdd"
        component={MaterialClassifyAdd}
      />
      <TenantStack.Screen
        name="MaterialClassifyList"
        component={MaterialClassifyList}
      />
      <TenantStack.Screen
        name="MaterialSmallClassifyAdd"
        component={MaterialSmallClassifyAdd}
      />
      <TenantStack.Screen
        name="MaterialSmallClassifyList"
        component={MaterialSmallClassifyList}
      />
      <TenantStack.Screen name="MaterialSpecAdd" component={MaterialSpecAdd} />
      <TenantStack.Screen
        name="MaterialSpecList"
        component={MaterialSpecList}
      />
      <TenantStack.Screen
        name="MaterialPurchaseAdd"
        component={MaterialPurchaseAdd}
      />
      <TenantStack.Screen
        name="MaterialPurchaseList"
        component={MaterialPurchaseList}
      />
      <TenantStack.Screen
        name="MaterialStorageAdd"
        component={MaterialStorageAdd}
      />
      <TenantStack.Screen
        name="MaterialStorageList"
        component={MaterialStorageList}
      />
      <TenantStack.Screen
        name="NaturalGasFlowMgrList"
        component={NaturalGasFlowMgrList}
      />
      <TenantStack.Screen
        name="NaturalGasFlowMgrAdd"
        component={NaturalGasFlowMgrAdd}
      />
      <TenantStack.Screen
        name="SortingCheckoutAdd"
        component={SortingCheckoutAdd}
      />
      <TenantStack.Screen
        name="SortingCheckoutList"
        component={SortingCheckoutList}
      />
      <TenantStack.Screen
        name="SortingStorageAdd"
        component={SortingStorageAdd}
      />
      <TenantStack.Screen
        name="SortingStorageList"
        component={SortingStorageList}
      />
      <TenantStack.Screen name="SpecCheckoutAdd" component={SpecCheckoutAdd} />
      <TenantStack.Screen
        name="SpecCheckoutList"
        component={SpecCheckoutList}
      />
      <TenantStack.Screen name="SpecStorageAdd" component={SpecStorageAdd} />
      <TenantStack.Screen name="SpecStorageList" component={SpecStorageList} />
    </DigitalEmployeesHomeStack.Navigator>
  );
};

const NaicaiIndexStackDefautScreen = () => {
  return (
    <NaicaiIndexStackDefautStack.Navigator headerMode={'none'}>
      <NaicaiIndexStackDefautStack.Screen
        name="NaicaiIndex"
        component={NaicaiIndex}
      />
    </NaicaiIndexStackDefautStack.Navigator>
  );
};

const NaicaiIndexStackSellGoodStackScreen = () => {
  return (
    <NaicaiIndexStackSellGoodsStack.Navigator headerMode={'none'}>
      <NaicaiIndexStackSellGoodsStack.Screen
        name="ProductList"
        component={ProductList}
      />
    </NaicaiIndexStackSellGoodsStack.Navigator>
  );
};

const NaicaiIndexStackAskBugGoodsStackScreen = () => {
  return (
    <NaicaiIndexStackAskBugGoodsStack.Navigator headerMode={'none'}>
      <NaicaiIndexStackAskBugGoodsStack.Screen
        name="AskBugGoods"
        component={AskBugGoods}
      />
    </NaicaiIndexStackAskBugGoodsStack.Navigator>
  );
};

const NaicaiIndexStackMyCenterOrLoginStackScreen = () => {
  return (
    <NaicaiIndexStackMyCenterOrLoginStack.Navigator headerMode={'none'}>
      <NaicaiIndexStackMyCenterOrLoginStack.Screen
        name="MyCenterOrLogin"
        component={MyCenterOrLogin}
      />
      <NaicaiIndexStackMyCenterOrLoginStack.Screen
        name="HomeStackScreen2"
        component={HomeStackScreen}
      />
      <NaicaiIndexStackMyCenterOrLoginStack.Screen
        name="RegisterScreen2"
        component={Register}
      />
      <NaicaiIndexStackMyCenterOrLoginStack.Screen
        name="PrivacyScreen2"
        component={Privacy}
      />
      <NaicaiIndexStackMyCenterOrLoginStack.Screen
        name="UserAgreementScreen2"
        component={UserAgreement}
      />
      <NaicaiIndexStackMyCenterOrLoginStack.Screen
        name="ModifyPwd"
        component={ModifyPwd}
      />
      {/* <NaicaiIndexStackMyCenterOrLoginStack.Screen name="MainStack2" component={MainStack}/> */}
    </NaicaiIndexStackMyCenterOrLoginStack.Navigator>
  );
};

const NaicaiIndexStackScreen = () => {
  var menuDTOList = [
    {
      menuName: '首页',
      menuCode: 'NaicaiIndexStackDefautScreen',
    },
    {
      menuName: '产品',
      menuCode: 'NaicaiIndexStackSellGoodStackScreen',
    },
    {
      menuName: '求购',
      menuCode: 'NaicaiIndexStackAskBugGoodsStackScreen',
    },
    {
      menuName: '我的',
      menuCode: 'NaicaiIndexStackMyCenterOrLoginStackScreen',
    },
  ];

  return (
    <NaicaiIndexStack.Navigator
      headerMode="none"
      initialRouteName="我的"
      style={{height: platformos === 'ios' ? 62 : 100}}
      screenOptions={({route}) => ({
        tabBarIcon: ({focused, color, size}) => {
          if (route.name === '首页') {
            return (
              <Image
                source={
                  focused
                    ? require('./src/assets/icon/bottom_navigate/naicai_index_lighted.png')
                    : require('./src/assets/icon/bottom_navigate/naicai_index.png')
                }
                style={{width: size, height: size}}
              />
            );
          } else if (route.name === '产品') {
            return (
              <Image
                source={
                  focused
                    ? require('./src/assets/icon/bottom_navigate/naicai_product_lighted.png')
                    : require('./src/assets/icon/bottom_navigate/naicai_product.png')
                }
                style={{width: size, height: size}}
              />
            );
          } else if (route.name === '求购') {
            return (
              <Image
                source={
                  focused
                    ? require('./src/assets/icon/bottom_navigate/naicai_ask_bug_lighted.png')
                    : require('./src/assets/icon/bottom_navigate/naicai_ask_bug.png')
                }
                style={{width: size, height: size}}
              />
            );
          } else if (route.name === '我的') {
            return (
              <Image
                source={
                  focused
                    ? require('./src/assets/icon/bottom_navigate/naicai_my_lighted.png')
                    : require('./src/assets/icon/bottom_navigate/naicai_my.png')
                }
                style={{width: size, height: size}}
              />
            );
          } else if (route.name === '工作台') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/workbench_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/workbench1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    工作台
                  </Text>
                )}
              </View>
            );
          }
        },
      })}
      tabBarOptions={{
        activeTintColor: '#333333',
        inactiveTintColor: '#b6b6b6',
        pressColor: '#823453',
        pressOpacity: 0.8,
        style: [
          {
            borderTopColor: '#ebebeb',
            borderTopWidth: 1,
            backgroundColor: '#FFFFFF',
            height: ifIphoneXTabBarOptionHeight(),
          },
        ],
        labelStyle: {
          fontSize: 15,
          margin: 1,
          marginBottom: 2,
        },
      }}>
      {menuDTOList.map((elem, index) => {
        if ('NaicaiIndexStackDefautScreen' == elem.menuCode) {
          return (
            <NaicaiIndexStack.Screen
              key={elem.menuCode}
              name="首页"
              component={NaicaiIndexStackDefautScreen}
            />
          );
        }
        // else if ('NaicaiIndexStackSellGoodStackScreen' == elem.menuCode) {
        //   return (
        //     <NaicaiIndexStack.Screenn key={elem.menuCode} name="产品" component={NaicaiIndexStackSellGoodStackScreen} />
        //   )
        // }
        else if ('NaicaiIndexStackSellGoodStackScreen' == elem.menuCode) {
          return (
            <NaicaiIndexStack.Screen
              key={elem.menuCode}
              name="产品"
              component={NaicaiIndexStackSellGoodStackScreen}
            />
          );
        } else if ('NaicaiIndexStackAskBugGoodsStackScreen' == elem.menuCode) {
          return (
            <NaicaiIndexStack.Screen
              key={elem.menuCode}
              name="求购"
              component={NaicaiIndexStackAskBugGoodsStackScreen}
            />
          );
        } else if (
          'NaicaiIndexStackMyCenterOrLoginStackScreen' == elem.menuCode
        ) {
          return (
            <NaicaiIndexStack.Screen
              key={elem.menuCode}
              name="我的"
              component={NaicaiIndexStackMyCenterOrLoginStackScreen}
            />
          );
        }
      })}
      {/* <NaicaiIndexStack.Screen name="首页" component={NaicaiIndexStackDefautScreen} />
              <NaicaiIndexStack.Screen name="产品" component={NaicaiIndexStackSellGoodStackScreen} />
              <NaicaiIndexStack.Screen name="求购" component={NaicaiIndexStackAskBugGoodsStackScreen} />
              <NaicaiIndexStack.Screen name="我的" component={NaicaiIndexStackMyCenterOrLoginStackScreen}/> */}
    </NaicaiIndexStack.Navigator>
  );
};

const judgeIsDigitalEmployee = () => {
  if (null != constants.tenantExtAttrJSON) {
    if (
      null != constants.tenantExtAttrJSON.menuTypes &&
      (constants.tenantExtAttrJSON.menuTypes.indexOf('D') > 0 ||
        constants.tenantExtAttrJSON.menuTypes.indexOf('R') > 0)
    ) {
      return true;
    }
  }
  return false;
};

const MainStackScreen = () => {
  var menuDTOList = [
    {
      menuName: '个人中心',
      menuCode: 'PersonalCenterStackScreen',
    },
  ];

  if (
    constants &&
    constants.roleInfo &&
    constants.roleInfo.menuDTOList &&
    constants.roleInfo.menuDTOList.length > 0
  ) {
    menuDTOList = constants.roleInfo.menuDTOList;
  }

  return (
    <MainStack.Navigator
      headerMode="none"
      initialRouteName={judgeIsDigitalEmployee() ? '学社首页' : '工作台'}
      style={{height: platformos === 'ios' ? 62 : 100}}
      screenOptions={({route}) => ({
        tabBarIcon: ({focused, color, size}) => {
          if (route.name === '工作台') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/workbench_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/workbench1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                <View
                  style={{
                    position: 'relative',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 65,
                  }}>
                  {focused ? null : (
                    <Text
                      style={{
                        color: '#33333375',
                        fontSize: 14,
                        flexWrap: 'wrap',
                        alignItems: 'center',
                      }}>
                      工作台
                    </Text>
                  )}
                </View>
              </View>
            );
          } else if (route.name === '租户管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/customer_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/customer1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    租户管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '半成品管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/sime_finished_list_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/sime_finished_list1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    半成品
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '装窑管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/foot_encastage_icon_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/foot_encastage_icon.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    装窑管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '温度管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/foot_temperature_icon_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/foot_temperature_icon1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    温度管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '出库管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/check_out_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/check_out1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    出库管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '个人中心') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/user_center_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/user_center1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                <View
                  style={{
                    position: 'relative',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 65,
                  }}>
                  {focused ? null : (
                    <Text
                      style={{
                        color: '#33333375',
                        fontSize: 14,
                        flexWrap: 'wrap',
                        alignItems: 'center',
                      }}>
                      个人中心
                    </Text>
                  )}
                </View>
              </View>
            );
          } else if (route.name === '测试') {
            return (
              <Image
                source={
                  focused
                    ? require('./src/assets/icon/testIcon_lighted.png')
                    : require('./src/assets/icon/testIcon.png')
                }
                style={{width: size, height: size}}
              />
            );
          } else if (route.name === '客户管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/custer_list_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/custer_list1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    客户管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '订单管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/order_list_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/order_list1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    订单管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '排产管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/scheduling_list_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/scheduling_list1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    排产管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '烧结管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/sintering_list_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/sintering_list1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    烧结管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '入库管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/package_store_in_list_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/package_store_in_list1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    入库管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '出库管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/check_out_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/check_out1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    出库管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '窑车管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/foot_encastage_icon_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/foot_encastage_icon.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    窑车管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '机台管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/machine_list_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/machine_list1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    机台管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '产品管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/brick_classify_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/brick_classify1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    产品管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '角色管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/customer_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/customer1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    角色管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '合同管理') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/contract_list_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/contract_list1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    合同管理
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '日报查询') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/query_daily_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/query_daily1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                <View
                  style={{
                    position: 'relative',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 65,
                  }}>
                  {focused ? null : (
                    <Text
                      style={{
                        color: '#33333375',
                        fontSize: 14,
                        flexWrap: 'wrap',
                        alignItems: 'center',
                      }}>
                      日报查询
                    </Text>
                  )}
                </View>
              </View>
            );
          } else if (route.name === '成果圈') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/query_harvest_circle_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/query_harvest_circle1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    成果圈
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '优秀成果') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/query_good_harvest_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/query_good_harvest1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    优秀成果
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '资源库') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/document_library_lighted1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/bottom_navigate/document_library1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    资源库
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '首页') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/home_page_lighted_icon1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/home_page_icon1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    首页
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '校招') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/school_recruitment_lighted_icon1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/school_recruitment_icon1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    校招
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '我的') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/myself_lighted_icon1.png')}
                    style={{width: 50, height: 50}}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/myself_icon1.png')}
                    style={{width: 30, height: 30}}
                  />
                )}
                {focused ? null : (
                  <Text
                    style={{
                      color: '#33333375',
                      fontSize: 14,
                      flexWrap: 'wrap',
                      alignItems: 'center',
                    }}>
                    我的
                  </Text>
                )}
              </View>
            );
          } else if (route.name === '我的消息') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {/* {
                        constants.noReadMessageCount && constants.noReadMessageCount > 0 ? 
                        <View style={{
                          position:"absolute", top:-3, right:-5, zIndex:1000,
                          color:"red", fontWeight:"bold", fontSize:20, backgroundColor:"red",
                          borderRadius:3, width:12, height:6
                          }}></View>
                          : null
                      } */}
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/message_reminder_light_icon1.png')}
                    style={{
                      width: 50,
                      height: 50,
                      position: 'relative',
                      left: 0,
                      top: 0,
                    }}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/message_reminder_icon1.png')}
                    style={{
                      width: 30,
                      height: 30,
                      position: 'relative',
                      left: 0,
                      top: 0,
                    }}
                  />
                )}
                <View
                  style={{
                    position: 'relative',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 65,
                  }}>
                  {focused ? null : (
                    <Text
                      style={{
                        color: '#33333375',
                        fontSize: 14,
                        flexWrap: 'wrap',
                        alignItems: 'center',
                      }}>
                      我的消息
                    </Text>
                  )}
                </View>
              </View>
            );
          } else if (route.name === '学社首页') {
            return (
              <View
                style={{
                  position: 'relative',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 60,
                  height: size,
                }}>
                {focused ? (
                  <Image
                    source={require('./src/assets/icon/home_page_lighted_icon1.png')}
                    style={{
                      width: 50,
                      height: 50,
                      position: 'relative',
                      left: 0,
                      top: 0,
                    }}
                  />
                ) : (
                  <Image
                    source={require('./src/assets/icon/home_page_icon1.png')}
                    style={{
                      width: 30,
                      height: 30,
                      position: 'relative',
                      left: 0,
                      top: 0,
                    }}
                  />
                )}
                <View
                  style={{
                    position: 'relative',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 65,
                  }}>
                  {focused ? null : (
                    <Text
                      style={{
                        color: '#33333375',
                        fontSize: 14,
                        flexWrap: 'wrap',
                        alignItems: 'center',
                      }}>
                      首页
                    </Text>
                  )}
                </View>
              </View>
            );
          }
        },
      })}
      tabBarOptions={{
        showLabel: false,
        activeTintColor: '#333333',
        inactiveTintColor: '#b6b6b6',
        pressColor: '#823453',
        pressOpacity: 0.8,
        style: [
          {
            borderTopColor: '#ebebeb',
            borderTopWidth: 1,
            backgroundColor: '#FFFFFF',
            height: ifIphoneXTabBarOptionHeight(),
          },
        ],
        labelStyle: {
          fontSize: 15,
          margin: 1,
          marginBottom: 2,
        },
      }}>
      {menuDTOList.map((elem, index) => {
        if ('HomeStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="工作台"
              component={HomeStackScreen}
            />
          );
        } else if ('QueryDailyStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="日报查询"
              component={QueryDailyStackScreen}
            />
          );
        } else if ('HarvestCircleStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="成果圈"
              component={HarvestCircleStackScreen}
            />
          );
        } else if ('DocumentLibraryStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="资源库"
              component={DocumentLibraryStackScreen}
            />
          );
        } else if ('GoodHarvestStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="优秀成果"
              component={GoodHarvestStackScreen}
            />
          );
        } else if ('TenantStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="租户管理"
              component={TenantStackScreen}
            />
          );
        } else if ('SemiFinishedStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="半成品管理"
              component={SemiFinishedStackScreen}
            />
          );
        } else if ('EncastageStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="装窑管理"
              component={EncastageStackScreen}
            />
          );
        } else if ('WarmManageStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="温度管理"
              component={WarmManageStackScreen}
            />
          );
        } else if ('CheckOutStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="出库管理"
              component={CheckOutStackScreen}
            />
          );
        } else if ('PersonalCenterStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="个人中心"
              component={PersonalCenterStackScreen}
            />
          );
        } else if ('CustomerListStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="客户管理"
              component={CustomerListStackScreen}
            />
          );
        } else if ('OrderListStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="订单管理"
              component={OrderListStackScreen}
            />
          );
        } else if ('OrderSchedulingListStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="排产管理"
              component={OrderSchedulingListStackScreen}
            />
          );
        } else if ('SinteringListStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="烧结管理"
              component={SinteringListStackScreen}
            />
          );
        } else if ('StorageInListStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="入库管理"
              component={StorageInListStackScreen}
            />
          );
        } else if ('StorageOutListStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="出库管理"
              component={StorageOutListStackScreen}
            />
          );
        } else if ('KilnCarMgrListStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="窑车管理"
              component={KilnCarMgrListStackScreen}
            />
          );
        } else if ('MachineMgrListStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="机台管理"
              component={MachineMgrListStackScreen}
            />
          );
        } else if ('BrickClassifyMgrListStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="产品管理"
              component={BrickClassifyMgrListStackScreen}
            />
          );
        } else if ('RoleListStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="角色管理"
              component={RoleListStackScreen}
            />
          );
        } else if ('ContractStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="合同管理"
              component={ContractStackScreen}
            />
          );
        } else if ('HomePageStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="首页"
              component={HomePageStackScreen}
            />
          );
        } else if ('SchoolreCruitmentStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="校招"
              component={SchoolreCruitmentStackScreen}
            />
          );
        } else if ('MyselfStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="我的"
              component={MyselfStackScreen}
            />
          );
        } else if ('MessageRemindStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="我的消息"
              component={MessageRemindStackScreen}
            />
          );
        } else if ('DigitalEmployeesHomeStackScreen' == elem.menuCode) {
          return (
            <MainStack.Screen
              key={elem.menuCode}
              name="学社首页"
              component={DigitalEmployeesHomeStackScreen}
            />
          );
        }
      })}
      {/* <MainStack.Screen name="首页" component={HomeStackScreen} />
          <MainStack.Screen name="半成品管理" component={SemiFinishedStackScreen} />
          <MainStack.Screen name="装窑管理" component={EncastageStackScreen} />
          <MainStack.Screen name="温度管理" component={WarmManageStackScreen} />
          <MainStack.Screen name="出库管理" component={CheckOutStackScreen} /> */}
      {/* <MainStack.Screen name="个人中心" component={PersonalCenterStackScreen}/> */}
    </MainStack.Navigator>
  );
};

const AppNavigation5 = () => {
  return (
    <NavigationContainer>
      <RootStack.Navigator
        initialRouteName={'NaicaiIndexStackScreen'}
        screenOptions={{headerShown: false}}>
        <RootStack.Screen name="LoginStack" component={LoginStackScreen} />
        <RootStack.Screen name="MainStack" component={MainStackScreen} />
        <RootStack.Screen
          name="NaicaiIndexStackScreen"
          component={NaicaiIndexStackScreen}
        />
        <RootStack.Screen name="ProductDetail" component={ProductDetail} />
        <RootStack.Screen
          name="SaleDataOverView"
          component={SaleDataOverView}
        />
        <RootStack.Screen
          name="VideoLibraryView"
          component={VideoLibraryView}
        />
        <RootStack.Screen
          name="DocumentLibraryView"
          component={DocumentLibraryView}
        />
        <RootStack.Screen
          name="StudentMyInterView"
          component={StudentMyInterView}
        />
        <RootStack.Screen
          name="StudentMyInterViewPreview"
          component={StudentMyInterViewPreview}
        />
        <RootStack.Screen name="WorkDaily" component={WorkDaily} />
        <RootStack.Screen name="QueryHarvest" component={QueryHarvest} />
        <RootStack.Screen
          name="QueryPromotionPlan"
          component={QueryPromotionPlan}
        />
        <RootStack.Screen name="SalesRankDetail" component={SalesRankDetail} />
        <RootStack.Screen
          name="SalesTargetAndPerformance"
          component={SalesTargetAndPerformance}
        />
        <RootStack.Screen
          name="ReceiveRankDetail"
          component={ReceiveRankDetail}
        />
        <RootStack.Screen
          name="ReceiveTargetAndPerformance"
          component={ReceiveTargetAndPerformance}
        />

        {/* 页面不展示底部导航栏 */}
        <RootStack.Screen
          name="MemberManagementDetail"
          component={MemberManagementDetail}
        />
        <RootStack.Screen
          name="MemberManagementExamineDetail"
          component={MemberManagementExamineDetail}
        />
        <RootStack.Screen name="AIChat" component={AIChat} />
      </RootStack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    backgroundColor: Colors.lighter,
  },
  engine: {
    position: 'absolute',
    right: 0,
  },
  body: {
    backgroundColor: Colors.white,
  },
  sectionContainer: {
    marginTop: 32,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.black,
  },
  sectionDescription: {
    marginTop: 8,
    fontSize: 18,
    fontWeight: '400',
    color: Colors.dark,
  },
  highlight: {
    fontWeight: '700',
  },
  footer: {
    color: Colors.dark,
    fontSize: 12,
    fontWeight: '600',
    padding: 4,
    paddingRight: 12,
    textAlign: 'right',
  },
});

export default AppNavigation5;
