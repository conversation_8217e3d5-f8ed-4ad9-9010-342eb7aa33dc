import PropTypes from 'prop-types';
import React, {Component} from 'react';
import {StyleSheet, Text, View} from 'react-native';

class ClassHeadScreen extends Component {
  static defaultProps = {
    redTitle: '',
    redFont: 11,
    blackTitle: '',
    blackFont: 16,
    titleHeight: 44,
  };

  static propTypes = {
    redTitle: PropTypes.string,
    redFont: PropTypes.number,
    blackTitle: PropTypes.string,
    blackFont: PropTypes.number,
    titleHeight: PropTypes.number,
    titleWidth: PropTypes.number,
    redTitleItem: PropTypes.func,
    blackTitleItem: PropTypes.func,
  };

  renderRedTitleItem() {
    if (this.props.redTitleItem === undefined) return <></>;
    return this.props.redTitleItem();
  }

  renderBlackTitleItem() {
    if (this.props.blackTitleItem === undefined) return <></>;
    return this.props.blackTitleItem();
  }

  render() {
    const {redTitle, redFont, blackTitle, blackFont, titleHeight} = this.props;

    return (
      <View style={[styles.container, {height: titleHeight}]}>
        <View style={[styles.titleContainer]}>
          <Text
            numberOfLines={2}
            ellipsizeMode="tail"
            style={[styles.blackTitleText, {fontSize: blackFont}]}>
            <View style={styles.redTitleContainer}>
              <Text style={[styles.redTitleText, {fontSize: redFont}]}>
                {redTitle || this.renderRedTitleItem()}
              </Text>
            </View>
            <Text>{blackTitle || this.renderBlackTitleItem()}</Text>
          </Text>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    flexDirection: 'row',
  },
  titleContainer: {
    backgroundColor: 'white',
    flexDirection: 'row',
  },
  redTitleContainer: {
    backgroundColor: '#F07400',
    borderRadius: 6,
    paddingTop: 2,
    paddingBottom: 2,
    paddingLeft: 5,
    paddingRight: 5,
  },
  redTitleText: {
    color: 'white',
  },
  blackTitleText: {
    color: '#000000',
    fontWeight: '600',
  },
});

export default ClassHeadScreen;
