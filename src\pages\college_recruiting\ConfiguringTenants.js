import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Image, TextInput, ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
const {ifIphoneXContentViewHeight} = require('../../utils/ScreenUtil');

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class ConfiguringTenants extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            searchKeyWord:"",
            configDataList: [],
            menuTypeSource:[],
            showSearchItemBlock: false,
            selTypeCode: null,
            selTypeId: 1,
            selTypeName: "全部"
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let menuTypeSource = [
            {
                typeId:1,
                typeName:"全部",
                typeCode:null
            },
            {
                typeId:2,
                typeName:"耐材行业",
                typeCode:"M"
            },
            {
                typeId:3,
                typeName:"数字化管理",
                typeCode:"D"
            },
            {
                typeId:4,
                typeName:"就业实习平台",
                typeCode:"R"
            },
            {
                typeId:5,
                typeName:"后勤运营",
                typeCode:"H"
            },
            {
                typeId:6,
                typeName:"会员系统",
                typeCode:"V"
            },
            {
                typeId:7,
                typeName:"官网平台",
                typeCode:"O"
            }
        ]
        this.setState({
            menuTypeSource:menuTypeSource
        })
        this.loadTenantList();
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/tenant/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "menuTypes": this.state.selTypeCode,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/tenant/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "menuTypes": this.state.selTypeCode,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadTenantList();
    }

    loadTenantList = () => {
        let url = "/biz/tenant/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "menuTypes":this.state.selTypeCode,
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this.loadTenantListCallBack);
    }

    loadTenantListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord = () => {
        let url = "/biz/tenant/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "menuTypes": this.state.selTypeCode,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    renderRow = (item, index) => {
        return (
            <View key={item.tenantId} style={{backgroundColor:'rgba(255, 255, 255, 1)'}}>
                <TouchableOpacity onPress={() => {
                    this.props.navigation.navigate("ConfiguringTenantsParam", 
                    {
                        currentTenantId: item.tenantId,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })
                }}>
                    <View style={[{ flexDirection: 'row', backgroundColor: "#FFFFFF", borderRadius: 5, paddingLeft: 12, height: 80, paddingTop: 10, paddingBottom: 10}]}>
                        <View style={[{ width: screenWidth - 80, flexDirection: "row"}]}>
                            {
                                item.tenantLogo ?
                                <Image source={{ uri: (constants.image_addr + '/' + item.tenantLogo)}} style={{height: 48, width: 48, borderRadius: 50, marginTop:5}} />
                                :
                                <Image source={require('../../assets/icon/iconfont/tenantLogo.png')} style={{height: 48, width: 48, borderRadius: 50, marginTop:5}} /> 
                            }
                            
                            <View style={{width: screenWidth - 140, marginLeft: 10 , flexDirection: 'column', justifyContent: "center" }}>
                                <Text style={{ fontSize: 16, fontWeight:'500', lineHeight: 24 }}>{item.tenantName}</Text>
                            </View>
                        </View>
                    </View>
                    
                </TouchableOpacity>

                <View style={styles.lineViewStyle}/> 
            </View>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    // 显示搜索项目
    showSearchItemSelect() {
        this.setState({
            showSearchItemBlock: this.state.showSearchItemBlock ? false : true,
        })
    }

    // 菜单类型
    renderMenuTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selTypeId: item.typeId,
                    selTypeName: item.typeName,
                    selTypeCode: item.typeCode,
                  
                })
            }}>
                <View key={item.typeId} style={[item.typeId === this.state.selDepartmentId ?
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                CommonStyle.choseToSearchItemsViewSize
                ]}>
                    <Text style={[item.typeId === this.state.selTypeId ?
                        CommonStyle.choseToSearchItemsSelectedTextStyle : CommonStyle.choseToSearchItemsTextStyle
                    ]}>
                        {item.typeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='配置租户'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                        <TouchableOpacity style = {{width:160}} onPress={() => this.showSearchItemSelect()}>
                            {
                                this.state.showSearchItemBlock ?
                                    <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: '#ffffff', padding: 10, margin: 5, flexDirection: 'row' }]}>
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(30,110,250,1)", paddingRight: 10 }]}>
                                            {this.state.selTypeId ? this.state.selTypeName:"选择菜单类型"}
                                        </Text>
                                        <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                                    </View>
                                    :
                                    <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: '#ffffff', padding: 10, margin: 5, flexDirection: 'row' }]}>
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(30,110,250,1)", paddingRight: 10 }]}>
                                            {this.state.selTypeId ? this.state.selTypeName:"选择菜单类型"}
                                        </Text>
                                        <Image style={{ width: 20, height: 20 }} source={require('../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                                    </View>
                            }
                        </TouchableOpacity>

                        <View style={[styles.innerViewStyle, {justifyContent: 'center', alignItems: 'center', borderRadius:20}]} onLayout={this.topBlockLayout.bind(this)}>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                </View>
                                <TextInput
                                    style={[styles.searchInputText]}
                                    returnKeyType="search"
                                    returnKeyLabel="搜索"
                                    onSubmitEditing={e => {
                                        this.searchByKeyWord();
                                    }}
                                    placeholder={'搜索租户名称'}
                                    onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                >
                                    {this.state.searchKeyWord}
                                </TextInput>
                            </View>
                        </View>
                    </View>
                </View>

                <View>
                    {
                        this.state.showSearchItemBlock ?
                            <View style={[CommonStyle.choseToSearchBigBoxViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                                <View style={CommonStyle.heightLimited}>
                                    <ScrollView>
                                        <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                            <View style={[{ backgroundColor: 'rgba(255,255,255,1)' }, CommonStyle.choseToSearchItemsViewSize]}>
                                                <Text style={{ fontSize: 16, fontWeight: 'bold' }}>菜单类型：</Text>
                                            </View>
                                            {
                                                (this.state.menuTypeSource && this.state.menuTypeSource.length > 0)
                                                    ?
                                                    this.state.menuTypeSource.map((item, index) => {
                                                        return this.renderMenuTypeRow(item)
                                                    })
                                                    : null
                                            }
                                        </View>   
                                    </ScrollView>
                                </View>
                                <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        console.log("当前选择的code",this.state.selTypeCode)
                                        let loadUrl = "/biz/tenant/list";
                                        let loadRequest = {
                                            "currentPage": 1,
                                            "pageSize": this.state.pageSize,
                                            "menuTypes":this.state.selTypeCode,
                                            "searchKeyWord":this.state.searchKeyWord,
                                        };
                                        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                                        this.setState({
                                            showSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            null
                    }

                    <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                        <FlatList
                            data={this.state.dataSource}
                            renderItem={({ item, index }) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                    tintColor="#FF0000"
                                    title="loading"
                                    colors={['#FF0000', '#00FF00', '#0000FF']}
                                    progressBackgroundColor="#FFFF00"
                                    refreshing={this.state.refreshing}
                                    onRefresh={() => {
                                        this._loadFreshData()
                                    }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={() => this.flatListFooterComponent()}
                            onEndReached={() => this._loadNextData()}
                        />
                    </View>
                   
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 15,
        height: 40,
        flexDirection: 'row',
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 0,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth - 240,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 2,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop: 0
    },

    innerViewStyle:{
        borderColor: "#F4F4F4",
        borderWidth: 2,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    itemContentTextStyle: {
        paddingLeft: 14,
        paddingRight: 16,
        lineHeight: 24,
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        // marginTop: 15,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC',
    },
});