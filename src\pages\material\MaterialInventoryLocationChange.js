import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,ScrollView,Modal,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';

import { saveImage } from '../../utils/CameraRollUtils';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
export default class MaterialInventoryLocationChange extends Component {
    constructor(props) {
        super(props);
        this.state = {
            inventoryId: "",
            classifyId: "",
            classifyName: null,
            io:'I',
            weight: "",
            ioDate:"",
            operate:"",
            operator: "",
            selectIoDate:[],
            materialClassifyDataSource:[],
            materialNameDataSource:[],
            reviewerName:"",
            reviewerId:"",           
            // materialImage:"",
            // materialImageUrl:"",
            pictureIndex:0,
            isShowImage: false,     //  显示弹窗组件  
            urls:[],
            compressFileList:[],
            modal:false,
            storageLocationDataSource:[],
            storageLocationAreaDataSource:[],
            selBrickTypeId:"",
            selLocationId:"",
            oldLocationId:"",
            selLocationAreaId:0,
            location:"",       
            inventoryItem:""   
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        //加载审核人信息
        this.loadReviewer();
        //加载库区信息
        this.loadInitData();
        var selectIoDate;
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        selectIoDate = [currentDate.getFullYear(), currentDateMonth, currentDateDay];
        this.setState({
            selectIoDate:selectIoDate,
            ioDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })
        console.log("=========selectIoDate===3:", selectIoDate);
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { inventoryItem } = route.params;
            if (inventoryItem) {
                console.log("=============inventoryItem" + inventoryItem + "");
                this.setState({
                    inventoryItem:inventoryItem
                })
                var urls = [];
                if(inventoryItem.compressFileList && inventoryItem.compressFileList.length > 0){
                    for(var i=0;i<inventoryItem.compressFileList.length;i++){
                        var url = {
                            url:constants.image_addr + '/' +  inventoryItem.compressFileList[i].compressFile
                        } 
                        urls=urls.concat(url)
                        console.log(url)
                    }
                }
                this.setState({
                    urls:urls
                })
            }
            else {
                console.log("=============inventoryId ： 空");
            }
        }
    }

    loadReviewer = ()=>{
        let loadTypeUrl= "/biz/material/inventory/reviewer";
        let loadRequest={
            "io":this.state.io,
            "operaterId": constants.loginUser.userId
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    reviewerName:response.data.userName,
                    reviewerId:response.data.userId,
                })
            }
        });
    }

    // 加载对应库区的库位信息
    loadStorageLocationByAreaId=(locationAreaId)=>{
        let url= "/biz/storage/location/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':1000,
            'locationAreaId':locationAreaId,
            "locationType":'M'
        };
        httpPost(url, loadRequest, this.callBackLoadStorageLocationByAreaId);
    }

    // 库位信息回调
    callBackLoadStorageLocationByAreaId=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("该库区下没有添加原料库位");
                WToast.show(toastOpts);
                this.setState({
                    storageLocationDataSource:[]
                })
                return;
            }
            this.setState({
                storageLocationDataSource:response.data.dataList,
            })
            const { route, navigation } = this.props;
            if (route && route.params) {
                const { inventoryItem } = route.params;
                if (inventoryItem?.locationId) {
                    this.setState({
                        selLocationId:inventoryItem?.locationId,
                        oldLocationId:inventoryItem?.locationId,
                    })
                }
                else if (response.data.dataList.length > 0) {
                    this.setState({
                        selLocationId:response.data.dataList[0].locationId
                    })
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadInitData=()=>{
        // 加载库区列表
        let url= "/biz/storage/location/area/list";
        let request={'currentPage':1,'pageSize':1000};
        httpPost(url, request, this.callBackLoadStorageLocationArea);
    }

    // 库区回调加载
    callBackLoadStorageLocationArea=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库区");
                WToast.show(toastOpts);
                return;
            }
            this.setState({
                storageLocationAreaDataSource:response.data.dataList,
            })
            const { route, navigation } = this.props;
            if (route && route.params) {
                const { inventoryItem } = route.params;
                if(inventoryItem?.locationAreaId){
                    this.setState({
                        selLocationAreaId:inventoryItem?.locationAreaId,
                    })
                    this.loadStorageLocationByAreaId(inventoryItem?.locationAreaId)
                }
                else if (response.data.dataList.length > 0) {
                    this.setState({
                        selLocationAreaId:response.data.dataList[0].locationAreaId
                    })
                    this.loadStorageLocationByAreaId(response.data.dataList[0].locationAreaId);
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    openIoDate(){
        this.refs.SelectIoDate.showDate(this.state.selectIoDate)
    }
    callBackSelectIoDateValue(value){
        console.log("==========入库时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectIoDate:value
        })
        if (this.state.selectIoDate && this.state.selectIoDate.length) {
            var ioDate = "";
            var vartime;
            for(var index=0;index<this.state.selectIoDate.length;index++) {
                vartime = this.state.selectIoDate[index];
                if (index===0) {
                    ioDate += vartime;
                }
                else{
                    ioDate += "-" + vartime;
                }
            }
            this.setState({
                ioDate:ioDate
            })
        }
    }

    saveLocationChange =()=> {
        console.log("=======saveMaterialInventoryIn");
        let toastOpts;
        if (!this.state.selLocationAreaId) {
            toastOpts = getFailToastOpts("请选择库区");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selLocationId) {
            toastOpts = getFailToastOpts("请选择库位");
            WToast.show(toastOpts)
            return;
        }
        if (this.state.selLocationId === this.state.oldLocationId) {
            toastOpts = getFailToastOpts("不能同库位调换,请选择新库位");
            WToast.show(toastOpts);
            return;
        }
        let url= "/biz/material/inventory/modifyLocation";
        let requestParams={
            "inventoryId": this.state.inventoryItem.inventoryId,
            "ioDate": this.state.ioDate,
            "operator": constants.loginUser.userName,
            "locationId":this.state.selLocationId,
            "currentAuditUserId":this.state.reviewerId,
        };
        console.log("requestParams===",requestParams)
        httpPost(url, requestParams, this.saveLocationChangeCallBack);
    }
    
    // 保存回调函数
    saveLocationChangeCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 库区
    renderLocationAreaRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    selLocationAreaId:item.locationAreaId
                }) 
                this.loadStorageLocationByAreaId(item.locationAreaId);
            }}>
                <View key={item.locationAreaId} style={[item.locationAreaId===this.state.selLocationAreaId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.locationAreaId===this.state.selLocationAreaId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationAreaName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 库位
    renderLocationRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (item.locationId === this.state.oldLocationId) {
                    let toastOpts = getFailToastOpts("不能同库位调换,请选择新库位");
                    WToast.show(toastOpts);
                    return;
                }
                this.setState({
                    selLocationId:item.locationId
                })
            }}>
                <View key={item.locationId} style={[item.locationId===this.state.selLocationId? [CommonStyle.selectedBlockItemViewStyle, item.locationId===this.state.oldLocationId ? CommonStyle.disableViewStyle : null] : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='库位调换'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    {/* <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>原料类别：{this.state.inventoryItem?.classifyName}</Text>
                    </View>
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>原料名称：{this.state.inventoryItem?.classifyName}</Text>
                    </View> */}
                    {/* <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>重量(吨)：{this.state.inventoryItem?.weight}</Text>
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>原料类别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            editable={false}
                            // //keyboardType='text'
                            style={[styles.inputRightText,{backgroundColor:'#00000010',width:screenWidth - (leftLabWidth + 50)}]}
                            placeholder={'请输入原料类别'}
                        >
                            {this.state.inventoryItem?.parentClassifyName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>原料名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            editable={false}
                            // //keyboardType='text'
                            style={[styles.inputRightText,{backgroundColor:'#00000010',width:screenWidth - (leftLabWidth + 50)}]}
                            placeholder={'请输入原料名称'}
                        >
                            {this.state.inventoryItem?.classifyName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>库区</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationAreaDataSource && this.state.storageLocationAreaDataSource.length > 0) 
                            ? 
                            this.state.storageLocationAreaDataSource.map((item, index)=>{
                                return this.renderLocationAreaRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>库位</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationDataSource && this.state.storageLocationDataSource.length > 0) 
                            ? 
                            this.state.storageLocationDataSource.map((item, index)=>{
                                return this.renderLocationRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>重量(吨)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            editable={false}
                            keyboardType='numeric'
                            style={[styles.inputRightText,{backgroundColor:'#00000010',width:screenWidth - (leftLabWidth + 50)}]}
                            placeholder={'请输入重量'}
                        >
                            {this.state.inventoryItem?.weight}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>入库时间</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openIoDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 50)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.ioDate ? "请选择入库时间" : this.state.ioDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    {
                        this.state.reviewerName?
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>审核人</Text>
                            </View>
                            <TextInput 
                                editable={false}
                                style={[styles.inputRightText,{backgroundColor:'#00000010',width:screenWidth - (leftLabWidth + 50)}]}
                                placeholder={'请输入审核人'}
                                onChangeText={(text) => this.setState({reviewerName:text})}
                            >
                                {this.state.reviewerName}
                            </TextInput>
                        </View>
                        :
                        <View/>
                    }
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>附件{this.state.compressFileList && this.state.compressFileList.length > 0 ? null : "：无"}</Text>
                        </View>
                    </View>
                    <View>
                        {
                            this.state.compressFileList && this.state.compressFileList.length > 0 ?
                            (
                                <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                    {
                                        this.state.compressFileList.map((item,index) =>{
                                            return(
                                                <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex'}]}>
                                                    <TouchableOpacity onPress={() => {
                                                        this.setState({
                                                            isShowImage:true,
                                                            pictureIndex:index
                                                        })
                                                    }}>
                                                        <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />                                                    
                                                                
                                                    </TouchableOpacity>
                                                    <Modal visible={this.state.isShowImage} transparent={true}>
                                                        <ImageViewer onClick={()=>{this.setState({isShowImage:false})}}  index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                            onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} 
                                                            onSave={() => {
                                                            saveImage( this.state.urls[this.state.pictureIndex].url)
                                                            }}/>
                                                    </Modal>
                                            </View>
                                            )
                                            
                                        })
                                    }
                                </View>
                            )
                            :null
                        }
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{flexDirection:'row',width:130,height:40,marginLeft:35,marginTop:15}]} >
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveLocationChange.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row',width:130,height:40,marginRight:35,marginTop:15}]}>
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                <BottomScrollSelect 
                        ref={'SelectIoDate'} 
                        callBackDateValue={this.callBackSelectIoDateValue.bind(this)}
                    />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },

    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
});