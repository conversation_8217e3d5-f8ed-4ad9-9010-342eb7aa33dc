import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class InventoryQuery extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:6,
            currentPage:1,
            totalPage:1,
            totalRecord:1
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadInventoryList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/order/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/order/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            // var dataAll = [...dataNew];
            this.setState({
                dataSource:response.data.dataList,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadInventoryList();
    }

    loadInventoryList=()=>{
        let url= "/biz/order/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize
        };
        httpPost(url, loadRequest, this.loadInventoryListCallBack);
    }

    loadInventoryListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            // var dataAll = dataOld.concat(dataNew.filter(v => !dataOld.includes(v)))
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.orderId} style={styles.innerViewStyle}>
                {
                    index == 0 ?
                        <View style={{ width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF', borderBottomWidth: 10, borderBottomColor: '#F4F7F9' }}>
                        </View>
                        :
                        <View></View>
                }
                <View style={CommonStyle.titleViewStyleSpecial}>
                    <Text style={CommonStyle.titleTextStyleSpecial}>{item.orderName}</Text>
                    {/* <Text style={styles.titleTextStyle}>订单名称：{item.orderName}</Text> */}
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>客户名称：{item.customerName}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>砖型：{item.brickTypeName}</Text>
                </View> */}
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>烧结总数：{item.sinteringAmount}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>成品总数：{item.storageInAmount}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>废品总数：{item.sinteringAmount - item.storageInAmount}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>成品率：{item.sinteringAmount != 0 ? (item.storageInAmount/item.sinteringAmount * 100).toFixed(2) : 100} %</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={[CommonStyle.titleTextStyle,{marginBottom:10}]}>废品率：{100 - (item.sinteringAmount != 0 ? (item.storageInAmount/item.sinteringAmount * 100).toFixed(2) : 100)} %</Text>
                </View>
                {/* <View style={[CommonStyle.itemBottomBtnStyle]}>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("InventoryAdjust", 
                            {
                                // 传递参数
                                brickTypeId:item.brickTypeId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle
                        ]}>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>库存调整</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0',marginHorizontal:16}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFFstyle={[{marginBottom:1.5}]}ilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                }}  >
                    {/* <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
                </TouchableOpacity>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='成品率查询'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        ItemSeparatorComponent={this.space}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        // borderColor:"#F4F4F4",
        // borderWidth:8,
        // marginLeft:15
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:3,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    // itemContentViewStyle:{
    //     flexDirection:'row',
    //     justifyContent:'space-between',
    //     marginLeft:25
    // },
    itemContentChildViewStyle:{
        justifyContent:'space-between',
        flexDirection:'row',
    },
});