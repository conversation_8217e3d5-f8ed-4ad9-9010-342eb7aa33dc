import React,{ Component } from 'react';
import {View, Text, StyleSheet,Dimensions} from 'react-native';
var CommonStyle = require('../assets/css/CommonStyle');
var screenHeight = Dimensions.get('window').height;
class EmptyListComponent extends Component {
    render(){
        return(
            <View style={[CommonStyle.contentViewStyle, {alignItems: 'center', justifyContent: 'center'}]}>
                <Text style={styles.contentTextStyle}>暂无数据</Text>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     alignItems: 'center',
    //     justifyContent: 'center'
    // },
    contentTextStyle:{
        color:'#A0A0A0',
        fontSize:25
    }
})
module.exports = EmptyListComponent;