import React,{Component} from 'react';
import {View, Text, StyleSheet, Image, FlatList,Dimensions, ScrollView, TouchableOpacity} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
// import TopScreen from '../../component/TopScreen';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';

// 引入公共样式
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
class FireRecord extends Component{
    constructor(props) {
        super(props);
        this.state = {
            dataSource: []
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadKilnCarFireRecordList();
    }

    // 查询烧制状态的窑车
    loadKilnCarFireRecordList=()=>{
        let url= "/biz/kiln/car/list";
        let loadRequest={
            "kilnCarState":"W",
            "currentPage": 1,
            "pageSize": 200,
        };
        httpPost(url, loadRequest, this.callBackLoadKilnCarFireRecordList);
    }

    callBackLoadKilnCarFireRecordList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                dataSource:response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }


    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.goBack()
                }}>
                <Text style={CommonStyle.headRightText}>返回</Text>
            </TouchableOpacity>
        )
    }

    renderItemRow=(item)=>{                
        return (
            <View>
                
            </View>
        );
    }
    renderRow=(item)=>{
        return (
            <View key={item.checkId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>窑车-{item.kilnCarName}</Text>
                    <Text style={styles.titleTextStyle}>{item.kilnCarStateName}</Text>
                </View>
                <View style={styles.itemContentStyle}>
                    <View style={{width:100, height:100, borderRadius:100, backgroundColor:'#DFDFFC', flexDirection:'column', alignItems:'center',justifyContent:'center'}}>
                        <Text style={{color:'#666666',fontSize:34}}>{item.kilnCarName}</Text>
                    </View>
                    <View>
                        <View style={styles.itemContentChildCol1ViewStyle}>
                            <Text style={styles.itemContentChildTextStyle}>砖块总数：{item.totalBrickAmount}块</Text>
                        </View>
                        <View style={styles.itemContentChildCol1ViewStyle}>
                            <Text style={styles.itemContentChildTextStyle}>砖块总重量：{item.totalWeight}Kg</Text>
                        </View>
                        <View style={styles.itemContentChildCol1ViewStyle}>
                            <Text style={styles.itemContentChildTextStyle}>进窑烧制时间：{item.intoTheKilnTime}</Text>
                        </View>
                        <View style={styles.itemContentChildCol1ViewStyle}>
                            <Text style={styles.itemContentChildTextStyle}>预计出窑时间：{item.estimatedTimeOfKilnDischarge}</Text>
                        </View>
                    </View>
                </View>
                <View style={styles.itemBottomBtnStyle}>
                    <TouchableOpacity onPress={()=>{
                        WToast.show({data:'详情功能完善中'});
                    }}>
                        <View style={styles.itemBottomDeleteBtnViewStyle}>
                            <Text style={styles.itemBottomDeleteBtnTextStyle}>详情</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='烧制记录' 
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                <FlatList 
                    data={this.state.dataSource}
                    ItemSeparatorComponent={this.space}
                    renderItem={({item}) => this.renderRow(item)}
                    ListEmptyComponent={this.emptyComponent}
                    />
                </ScrollView>
               
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFF'
    // },
    innerViewStyle:{
        marginTop:10
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10
    },
    titleTextStyle:{
        fontSize:23
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center',
        marginLeft:15,
        paddingTop:5
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'row'
    },
    itemContentChildCol1ViewStyle:{
        marginLeft:20,
        marginTop:15
    },
    itemContentChildCol2ViewStyle:{
        marginLeft:40,
        marginTop:15
    },
    itemContentChildTextStyle:{
        fontSize:15
    },
    itemBottomBtnStyle:{
        flexDirection:'row',
        justifyContent:'flex-end'
    },
    itemBottomDeleteBtnViewStyle:{
        fontSize:16,
        width:100,
        height:30,
        borderWidth:1,
        borderColor:'#A0A0A0',
        justifyContent:'center',
        alignItems:'center',
        margin:10,
        borderRadius:4
    },
    itemBottomEditBtnViewStyle:{
        fontSize:16,
        width:100,
        height:30,
        justifyContent:'center',
        alignItems:'center',
        margin:10,
        backgroundColor:"#CB4139",
        borderRadius:4
    },
    itemBottomEditBtnTextStyle:{
        color:'#F0F0F0'
    }
});
module.exports = FireRecord;
