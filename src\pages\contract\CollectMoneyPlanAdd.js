import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,
    TouchableOpacity,Dimensions,Image,KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 160;

export default class CollectMoneyPointAdd extends Component {
    constructor(){
        super()
        this.state = {
            operate:"",
            planId:"",
            // 节点编号
            pointId:"",
            // 节点名称
            pointName:"",
            // 计划收款比例
            planProportion:"",
            // 计划收款金额
            planAmount:"",
            // 计划收款日期
            planDate:"",
            selectCollectMoneyPlanDate:[],
            // 收款节点数据集合
            pointDateSource: [],
            // 勾选的收款节点
            selPointId:"",
            // 合同编号
            contractId:"",
            contractAmount:"",
            planProportionSum:"",
            planInvoiceAmount:"",
            diff:0,
            // 记录比例和金额哪个最后输入，P-比例、A-金额
            lastInput:"P"
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            console.log(route.params);
            const { planId, contractId, contractAmount, dataSource} = route.params;
            if(contractId){
                this.setState({
                    contractId: contractId
                })
            }
            if(contractAmount){
                this.setState({
                    contractAmount: contractAmount
                })
            }
            if (planId) {
                this.setState({
                    planId: planId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/contract/collect/money/plan/get";
                loadRequest={'planId': planId};
                httpPost(loadTypeUrl, loadRequest, (response)=>{
                    if (response.code == 200 && response.data) {
                        console.log(response)
                        var planDate;
                        if (response.data.planDate) {
                            planDate = response.data.planDate.split("-");
                        }
                        this.setState({
                            planId: response.data.planId,
                            selPointId: response.data.pointId,
                            pintId: response.data.pointId,
                            pointName: response.data.pointName,
                            planProportion: response.data.planProportion ? response.data.planProportion : parseFloat((response.data.planAmount / contractAmount * 100).toFixed(2)),
                            planAmount: response.data.planAmount ? response.data.planAmount : parseFloat((response.data.planProportion * contractAmount / 100).toFixed(2)),
                            contractId: response.data.contractId,
                            planDate:response.data.planDate,
                            selectCollectMoneyPlanDate: planDate,
                            diff:response.data.planProportion ? response.data.planProportion : parseFloat((response.data.planAmount / contractAmount * 100).toFixed(2)),
                            planInvoiceAmount:response.data.planInvoiceAmount,
                            lastInput:response.data.planProportion ? 'P' : 'A'
                        })
                        console.log(this.state.selectCollectMoneyPlanDate)
                    }
                });
            }
            else {
                this.setState({
                    operate:"新增",
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectCollectMoneyPlanDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                })      
                if(constants.loginUser.tenantId != 66){
                    this.setState({
                        planDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                    })
                }      
            }
            
        }
        this.loadContractMoneyPointList();
    }



    // 获取合同收款节点列表
    loadContractMoneyPointList=()=>{
        console.log("获取节点列表");
        let url= "/biz/contract/collect/money/point/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 30
        };
        httpPost(url, loadRequest, this.loadContractMoneyPointListCallBack);
    }

    loadContractMoneyPointListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log(response.data)
            var dataNew = response.data.dataList;
            var dataOld = this.state.pointDateSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                pointDateSource:dataAll,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 收款节点单项渲染
    renderPlanPointRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selPointId:item.pointId
            }) }}>
                <View key={item.pointId} style={[item.pointId===this.state.selPointId ? 
                    // CommonStyle.selectedBlockItemViewStyle 
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    : 
                    // CommonStyle.blockItemViewStyle 
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginLeft:16,
                        // marginRight: 4,
                        // marginTop: 8,
                        marginBottom: 8,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 40,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                    ]}>
                    <Text style={[item.pointId===this.state.selPointId ? 
                        // CommonStyle.selectedBlockItemTextStyle16 
                        {
                            color: '#1E6EFA'
                        }
                        : 
                        // CommonStyle.blockItemTextStyle16 
                        {
                            color: '#404956'
                        }
                        ,
                        {
                            fontSize: 16, textAlign : 'center'
                        } 
                        ]}>
                        {item.pointName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("CollectMoneyPlanList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>计划管理</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    {/* <Text style={{color:'#FFFFFF'}}>计划管理</Text> */}
                </TouchableOpacity>
            </View>
        )
    }

    getProportionSum =()=> {
        console.log("=======getProportionSum");
        let toastOpts;
        if (!this.state.selPointId) {
            toastOpts = getFailToastOpts("请选择收款节点");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.planProportion) {
            toastOpts = getFailToastOpts("请输入计划收款比例");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.planAmount) {
        //     toastOpts = getFailToastOpts("请输入计划收款金额");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if(constants.loginUser.tenantId != 66) {
            if (!this.state.planDate) {
                toastOpts = getFailToastOpts("请选择计划收款日期");
                WToast.show(toastOpts)
                return;
            }    
        }


        let url0 = "/biz/contract/collect/money/plan/sum";
        let params={
            contractId:this.state.contractId
        }
        httpPost(url0, params, this.getProportionSumCallBack);
        
    }

     // 获取比列总和的回调函数
     getProportionSumCallBack=(response)=>{
        if (response.code == 200) {
            console.log(response)
            var sum;
            sum = response.data
            // this.setState({
            //     planProportionSum:sum
            // })
            console.log("比例综合为："+sum);
            this.saveCollectMoneyPlan(sum);
        }
    }

    saveCollectMoneyPlan=(sum)=>{
        console.log("=======saveCollectMoneyPlan");
        var proportionSum;
        let url= "/biz/contract/collect/money/plan/add";
        proportionSum = 100 - sum;
        console.log(sum);
        console.log(proportionSum + this.state.diff);
        if (this.state.planId) {
            console.log("=========Edit===planId", this.state.planId)
            proportionSum = proportionSum + this.state.diff
            url= "/biz/contract/collect/money/plan/modify";
        }
        let requestParams={
            planId: this.state.planId,
            pointId:this.state.selPointId,
            planProportion: this.state.lastInput == 'P' ? this.state.planProportion : null,
            planAmount:  this.state.lastInput == 'A' ? this.state.planAmount : null,
            planDate: this.state.planDate,
            contractId:this.state.contractId,
            planInvoiceAmount:this.state.planInvoiceAmount,
        };
        console.log("===requestParams",requestParams);
        if(proportionSum >= this.state.planProportion){
            httpPost(url, requestParams, this.saveContractCallBack);
        }else{
            let toastOpts;
            toastOpts = getFailToastOpts("比例超过100，请重新输入比例");
            WToast.show(toastOpts)
        }
    }

    // 保存回调函数
    saveContractCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack();
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
   
    openCollectMoneyPlanDate(){
        this.refs.SelectCollectMoneyPlanDate.showDate(this.state.selectCollectMoneyPlanDate)
    }
    
    callBackSelectSelectCollectMoneyPlanDate(value){
        console.log("==========计划收款时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectCollectMoneyPlanDate:value
        })
        if (value && value.length) {
            var planDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    planDate += vartime;
                }
                else{
                    planDate += "-" + vartime;
                }
            }
            this.setState({
                planDate:planDate
            })
        }
    }
    
    render(){
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                           <Text style={styles.leftLabNameTextStyle}>收款节点</Text>
                           {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.pointDateSource && this.state.pointDateSource.length > 0) 
                            ? 
                            this.state.pointDateSource.map((item, index)=>{
                                return this.renderPlanPointRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                           <Text style={styles.leftLabNameTextStyle}>计划收款比例</Text>
                           {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60)}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({
                                planProportion:text,
                                planAmount:parseFloat((text*this.state.contractAmount/100).toFixed(2)),
                                lastInput:"P"
                            })}
                        >
                            {this.state.planProportion}
                        </TextInput>
                        <Text style={styles.percent}>%</Text>    
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                           <Text style={styles.leftLabNameTextStyle}>计划开票金额</Text>
                           {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60)}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({planInvoiceAmount:text})}
                        >
                            {this.state.planInvoiceAmount}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                           <Text style={styles.leftLabNameTextStyle}>计划收款金额</Text>
                           {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60)}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({
                                planAmount:text,
                                planProportion: parseFloat((text * 100 / this.state.contractAmount).toFixed(2)),
                                lastInput:"A"
                            })}
                        >
                            {this.state.planAmount}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           {
                               (constants.loginUser.tenantId != 66)?
                               <Text style={styles.leftLabRedTextStyle}>*</Text>
                                :
                               <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                           }
                            <Text style={styles.leftLabNameTextStyle}>计划收款日期</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openCollectMoneyPlanDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{marginLeft:5,width:screenWidth - (leftLabWidth+60),borderWidth:0}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.planDate ? "请选择计划收款日期" : this.state.planDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    <View style={{height:ifIphoneXContentViewHeight()-270-229, backgroundColor:'#F2F5FC'}}>
                    </View>

                    <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => this.getProportionSum()}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                <BottomScrollSelect 
                    ref={'SelectCollectMoneyPlanDate'} 
                    callBackDateValue={this.callBackSelectSelectCollectMoneyPlanDate.bind(this)}
                />
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
  itemViewStyle:{
      margin:10,  
      padding:15, 
      borderRadius:2,
      backgroundColor:'#FFFFFF'
  },
  selectedItemViewStyle:{
      margin:10,  
      padding:15, 
      borderRadius:2,
      backgroundColor:"#CB4139"
  },
  itemTextStyle:{
      color:'#000000'
  },
  selectedItemTextStyle:{
      color:'#FFFFFF'
  },
  inputRowStyle:{
      height:45,
      flexDirection:'row',
      marginTop:4,
      marginBottom:4,
  },

  rowLabView:{
      height:45,
      flexDirection:'row',
      alignItems:'center',
      paddingLeft:10,
  },
  leftLabView:{
      width:leftLabWidth,
      height:45,
      flexDirection:'row',
      alignItems:'center',
      paddingLeft:10,
  },
  leftLabNameTextStyle:{
      fontSize:18,
  },
  leftLabRedTextStyle:{
      color:'red',
      marginLeft:6,
      marginRight:5
  },
  leftLabWhiteTextStyle:{
    color:'#FFFFFF',
    marginLeft:6,
    marginRight:5
},
  inputRightText:{
      width:screenWidth - (leftLabWidth+10),
    //   borderRadius:5,
    //   borderColor:'#F1F1F1',
    //   borderWidth:1,
      marginLeft:5,
      marginRight:5,
      color:'#A0A0A0',
      fontSize:15,
      paddingLeft:10,
      paddingRight:10
  },
  rightLabView:{
    width:20,
    height:45,
    flexDirection:'row',
    alignItems:'center',
    paddingLeft:10,
  },
  percent:{
    fontSize:20,
    marginTop:6
    }
})