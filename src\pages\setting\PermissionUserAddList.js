import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class PermissionUserAddList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            permissionCode:null,
            permissionName:"",
            selUserIdList:[],
            oldSelUserIdList:[],
            searchKeyWord:null,
            topBlockLayoutHeight:0
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            const { permissionCode, permissionName, jobAmount } = route.params;
            if (permissionCode) {
                this.setState({
                    permissionCode:permissionCode,
                })
                console.log("=============permissionCode:" + permissionCode + "");
                this.loadJobStaffList(permissionCode);
                this.loadSelectedJobStaffList(permissionCode);
            }
            if (permissionName) {
                this.setState({
                    permissionName:permissionName
                })
            }
        }
    }

    loadJobStaffList=(permissionCode)=>{
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "permissionCode": permissionCode ? permissionCode : this.state.permissionCode,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this.loadJobStaffListCallBack);
    }

    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadJobStaffList();
    }

    loadJobStaffListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // this.setState({
            //     dataSource:response.data.dataList
            // })
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadSelectedJobStaffList=(permissionCode)=>{
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 10000,
            "permissionCode": permissionCode ? permissionCode : this.state.permissionCode,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this.loadSelectedJobStaffListCallBack);
    }

    loadSelectedJobStaffListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var userDTO;
            var selUserIdList = [];
            for(var index = 0; index < response.data.dataList.length; index ++) {
                userDTO = response.data.dataList[index];
                if (userDTO && userDTO.permissionSelectedUser === "Y") {
                    selUserIdList = selUserIdList.concat(userDTO.userId)
                }
            }
            this.setState({
                selUserIdList:selUserIdList,
                oldSelUserIdList:copyArr(selUserIdList),
            })
            console.log("=========oldSelUserIdList:", selUserIdList);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "permissionCode": this.state.permissionCode,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.userId}  >
                <TouchableOpacity onPress={()=>{
                    var selUserIdList = this.state.selUserIdList;
                    if (item.permissionSelectedUser && item.permissionSelectedUser == "Y") {
                        item.permissionSelectedUser = "N";
                        arrayRemoveItem(selUserIdList, item.userId);
                    }
                    else {
                        item.permissionSelectedUser = "Y";
                        selUserIdList = selUserIdList.concat(item.userId)
                    }
                    this.setState({
                        selUserIdList:selUserIdList,
                    })
                    WToast.show({data:'点击了' + item.userName});
                    console.log("======selUserIdList:", selUserIdList)
                }}>
                    <View key={item.userId} style={[styles.innerViewStyle,(item.permissionSelectedUser && item.permissionSelectedUser === 'Y') ? {backgroundColor:'rgba(255,0,0,0.4)',borderRadius:20,hight:80}:{} ]}>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>用户姓名：{item.userName}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>联系电话：{item.userNbr ? item.userNbr : "无"}</Text>
                        </View>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                let requestUrl= "/biz/permission/add";
                let requestParams={
                    "permissionCode":this.state.permissionCode,
                    "selUserIdList":this.state.selUserIdList,
                    "oldSelUserIdList":this.state.oldSelUserIdList,
                    "operatorUserId":constants.loginUser.userId
                };
                console.log("=======save==requestParams:", requestParams);
                httpPost(requestUrl, requestParams, (response)=>{
                    let toastOpts;
                    if (response && response.code === 200) {
                        if (this.props.route.params.refresh) {
                            this.props.route.params.refresh();
                        }
                        toastOpts = getSuccessToastOpts('保存完成');
                        WToast.show(toastOpts);
                        this.props.navigation.goBack()
                        // this.props.navigation.navigate("JobStaffMgrList", 
                        // {
                        //     // 传递回调函数
                        //     refresh: this.callBackFunction 
                        // })
                    }
                    else {
                        toastOpts = getFailToastOpts(response.message);
                        WToast.show({data:response.message})
                    }
                });
            }}>
                <Image  style={{width:28, height:28,marginRight:5}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                {/* <Text style={CommonStyle.headRightText}>保存</Text> */}
            </TouchableOpacity>
        )
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    searchByKeyWord = ()=>{
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "permissionCode": this.state.permissionCode,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='新增用户'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0, index:1000}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle,{marginLeft:10, fontWeight:'bold', marginRight:100}]}>权限名称：{this.state.permissionName}</Text>
                    </View>
                    <View style={{marginTop:10}}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput 
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'姓名或电话'}
                                onChangeText={(text) => this.setState({searchKeyWord:text})}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        paddingBottom:5
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    searchInputText:{
        width:screenWidth / 2,
        borderColor:'#000000',
        // borderBottomWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:16,
        marginLeft:10,
        paddingLeft:10,
        paddingRight:10,
        paddingBottom:0,
        paddingTop:0
    },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
        borderBottomWidth:5,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },

});