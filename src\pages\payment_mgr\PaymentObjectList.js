import React,{Component} from 'react';
import {View, Text, StyleSheet, Image, FlatList,RefreshControl
    ,Dimensions, ScrollView, TouchableOpacity, Alert,TextInput} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';

// 公共组件及样式
import EmptyListComponent from '../../component/EmptyListComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import CommonHeadScreen from '../../component/CommonHeadScreen';

import {WToast} from 'react-native-smart-tip';

// 引入公共样式
// import CommonStyle from '../../assets/css/CommonStyle';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class PaymentObjectList extends Component{
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            searchKeyWord:"",
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadCustomerList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/tenant/customer/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "customerType":"P",
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/tenant/customer/list";
        let data={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "customerType":"P",
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, data, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadCustomerList();
    }

    loadCustomerList=()=>{
        let url= "/biz/tenant/customer/list";
        let data={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "customerType":"P",
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, data, this.callBackLoadCustomerList);
    }

    callBackLoadCustomerList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteCustomer =(customerId)=> {
        console.log("=======delete=customerId", customerId);
        let url= "/biz/tenant/customer/delete";
        let requestParams={'customerId':customerId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"成功删除"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }
    renderRow=(customerItem,index)=>{
        return (
            <View key={customerItem.customerId} style={styles.innerViewStyle}>
                {
                    index == 0 ?
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>
                        :
                        <View></View>
                }
                <View style={CommonStyle.titleViewStyleSpecial}>
                    {/* <Text style={styles.titleTextStyle}>支付对象：{customerItem.customerName}</Text> */}
                    <Text style={CommonStyle.titleTextStyleSpecial}>{customerItem.customerName}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>联系人：{customerItem.customerConcat ? customerItem.customerConcat : "无"}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>联系电话：{customerItem.customerTel ? customerItem.customerTel : "无"}</Text>
                </View>
                <View style={[CommonStyle.blockTwoEditDelStyle,{marginRight:15}]}>
                    <TouchableOpacity onPress={()=>{
                        if (dateDiffHours(constants.nowDateTime, customerItem.gmtCreated) > constants.editDeleteTimeLimit) {
                            return;
                        }
                        Alert.alert('确认','您确定要删除该支付对象吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteCustomer(customerItem.customerId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.btnTwoDeleteBtnView,dateDiffHours(constants.nowDateTime, customerItem.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""]}>
                            <Image  style={CommonStyle.btnTwoDeleteBtnImage} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.btnTwoDeleteBtnText}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>this.props.navigation.navigate("PaymentObjectAdd",
                    {
                        customerId:customerItem.customerId,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })}>
                        <View style={[CommonStyle.btnTwoEditBtnView]}>
                            <Image  style={CommonStyle.btnTwoEditBtnImage} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.btnTwoEditBtnText}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                    
                </View>
            </View>
        )
    }
    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }


    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("PaymentObjectAdd", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     });
            //     }}>
            //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("PaymentObjectAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    });
                }}  >
                    <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    searchByKeyWord=()=>{
        let url= "/biz/tenant/customer/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "customerType":"P",
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='支付对象'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.headViewStyle, { borderLeftWidth: 0, borderRightWidth: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={CommonStyle.singleSearchBox}>
                        <View style={CommonStyle.searchBoxWithoutOthers}>
                            {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                            <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            <TextInput
                                style={{color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 5, paddingTop: 0, paddingBottom: 0, paddingRight: 0, paddingLeft: 0, width:'100%' }}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'支付对象'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> */}
                        <FlatList 
                            data={this.state.dataSource}
                            ItemSeparatorComponent={this.space}
                            ListEmptyComponent={this.emptyComponent}
                            renderItem={({item,index}) => this.renderRow(item,index)}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                            />
                    {/* </ScrollView> */}
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewSearchStyle:{
        // marginLeft:16
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
    },
    innerViewStyle: {
        // marginLeft:16
        marginTop:10,
        // borderColor:"#F4F4F4",
        // borderWidth:8,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        // marginBottom: 5,
        marginTop: 5,
    },
    titleViewStyleSpecial: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 10,
    },
    titleTextStyle: {
        fontSize: 16
    },
    titleTextStyleSpecial: {
        width: 200,
        height: 24,
        // fontFamily: 'PingFangSC',
        fontWeight: 'bold',
        fontSize: 20,
        color: '#404956',
        lineHeight: 24,
        textAlign: 'left',
        fontStyle: 'normal',
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
})