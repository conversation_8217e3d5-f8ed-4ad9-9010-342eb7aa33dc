import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,Modal,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import ProductEmptyRowViewComponent from '../../../component/ProductEmptyRowViewComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
var CommonStyle = require('../../../assets/css/CommonStyle');
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../../utils/CameraRollUtils';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class MyProductSaleRelease extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态',
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            display:"N",
            compressFileList:[],
            urls:[],
            isShowImage: false,
            pictureIndex:0,
            productTypeList:[
                {
                    "typeId":0,
                    "typeName":"耐火制品",
                    "typeCode":"P"
                },
                {
                    "typeId":1,
                    "typeName":"耐火原料",
                    "typeCode":"M"
                }
            ],
            productType:"P",
            searchKeyWord:null,
            topBlockLayoutHeight:0,
            auditDataSource:[],
            selAuditType:"all"
        }
    }

    getNowTime(){
        var date = new Date();
        //获取当前时间的毫秒数
        var nowMilliSeconds = date.getTime();
        // 用获取毫秒数 加上30天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        var nowTime = new Date(nowMilliSeconds + ( 8 * 3600000));
        //年 getFullYear()：四位数字返回年份
        var year = nowTime.getFullYear();  //getFullYear()代替getYear()
        //月 getMonth()：0 ~ 11
        var month = nowTime.getMonth() + 1;
        //日 getDate()：(1 ~ 31)
        var day = nowTime.getDate();
        //时 getHours()：(0 ~ 23)
        var hour = nowTime.getHours();
        //分 getMinutes()： (0 ~ 59)
        var minute = nowTime.getMinutes();
        //秒 getSeconds()：(0 ~ 59)
        var second = nowTime.getSeconds();

        var time =  year + '-' + this.addZero(month) + '-' + this.addZero(day) + ' ' + this.addZero(hour) + ':' + this.addZero(minute) + ':' + this.addZero(second);
        return time;
    }

    addZero(s){
        return s < 10 ? ('0'+s):s;
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let auditDataSource = [
            {
                auditType: 'all',
                auditTypeName: '全部',
            },
            {
                auditType: '1',
                auditTypeName: "未发起",
            },
            {
                auditType: '2',
                auditTypeName: "待审核",
            },
            {
                auditType: '3',
                auditTypeName: "通过",
            },
            {
                auditType: '4',
                auditTypeName: "驳回"
            }
        ]
        this.setState({
            auditDataSource: auditDataSource,
        })
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        this.loadMyProductSaleReleaseList()
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": "R",
            "userId":constants.loginUser.userId,
            "productType":this.state.productType,
            "searchKeyWord":this.state.searchKeyWord,
            "auditState":this.state.selAuditType,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": "R",
            "userId":constants.loginUser.userId,
            "productType":this.state.productType,
            "searchKeyWord":this.state.searchKeyWord,
            "auditState":this.state.selAuditType,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadMyProductSaleReleaseList();
    }

    loadMyProductSaleReleaseList=()=>{
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "releaseType": "R",
            "userId":constants.loginUser.userId,
            "productType":this.state.productType,
            "searchKeyWord":this.state.searchKeyWord,
            "auditState":this.state.selAuditType,
        };
        httpPost(url, loadRequest, this.loadMyProductSaleReleaseListCallBack);
    }


    loadMyProductSaleReleaseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            console.log(dataNew)
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteRelease =(releaseId)=> {
        console.log("=======delete=releaseId", releaseId);
        let url= "/biz/product/release/delete";
        let requestParams={'releaseId':releaseId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    operateProduct=(item)=>{
        console.log("=======operateProduct=item", item);
        let url= "/biz/product/release/modify";
        let requestParams={
            'releaseId':item.releaseId,
            'productState':item.productState=="U"?"D":"U",
            "updateProductState":"Y"
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                WToast.show({data:item.productState=="U"?"下架成功":"上架成功"});
                var dataSource = this.state.dataSource;
                dataSource.map(obj=>{
                    if(obj.releaseId == item.releaseId){
                        obj.productState = item.productState=="U"?"D":"U"
                    }
                })
                this.setState({
                    dataSource:dataSource
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({data:response.message});
            }
        });
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("MyProductSaleReleaseAdd",
                {
                    // 传递回调函数
                    refresh: this.callBackFunction
                })
            }}>
                <Text style={CommonStyle.headRightText}>发布</Text>
            </TouchableOpacity>
        )
    }


    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }

    emptyComponent(height) {
        return <ProductEmptyRowViewComponent height={height} />
    }

    myIsNaN=(value)=> {
        return parseFloat(value).toString() != "NaN";
    }

    startAudit=(item)=>{
        if(item.auditState != '1'){
            return;
        }
        else{
            let url= "/biz/product/release/modify";
            let requestParams={
                releaseId:item.releaseId,
                auditState:"2",
                compressFileList:item.compressFileList
            };
            httpPost(url, requestParams, this.startAuditCallBack);
        }
        console.log("审核状态：：：：："+item.auditState)
    }

    // 保存回调函数
    startAuditCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"发起审核成功"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }


    renderRow=(item, index)=>{
        return (
            <View key={item.kilnCarId} style={styles.innerViewStyle}>
                <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                    <Text style={styles.titleTextStyle}>{item.productName}</Text>
                    {
                        this.state.productType == "P" ?
                            <Text style={[styles.greyTextStyle,{marginLeft:30}]}>型号：{item.productModel}</Text>
                        : <View/>
                    }
                </View>
                <View style={{position:'absolute',right:-10,top:10}}>
                    {
                        item.auditMarking == '1' ?
                        <View style={{position:'absolute',right:0,top:0}}>
                            <Image style={{width:85,height:25}} source={require('../../../assets/image/recommend.png')}/>
                        </View>
                        :
                        (
                            item.auditMarking == '2' ?
                            <View style={{position:'absolute',right:0,top:0}}>
                                <Image style={{width:50,height:23}} source={require('../../../assets/image/selfSupport.png')}/>
                            </View>
                            :
                            null
                        )
                    }
                    {
                        item.spotFlag == 'Y' ?
                        (
                            item.auditMarking == '1' || item.auditMarking == '2'?
                            <View  style={{position:'absolute',right:0,top:30}}>
                                <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/>
                                <Text style={{position:'absolute',right:3,color:"#f0f0f0"}}>现货</Text>
                            </View>
                            :
                            <View  style={{position:'absolute',right:0,top:0}}>
                                <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/>
                                <Text style={{position:'absolute',right:3,color:"#f0f0f0"}}>现货</Text>
                            </View>
                        )
                        :
                        null
                    }
                    
                </View>
                <View style={{flexDirection:'row',display:'flex'}}>
                    <View >
                        {
                            item.compressFileList && item.compressFileList.length > 0 ?
                            <View>
                                <TouchableOpacity onPress={() => {
                                    var urls = [];
                                    var url = {
                                        url:constants.image_addr + '/' +  item.compressFileList[0].compressFile
                                    }
                                    urls=urls.concat(url)
                                    console.log(url)

                                    this.setState({
                                        urls:urls
                                    })
                                    this.setState({
                                        isShowImage:true,
                                    })

                                }}>
                                    <Image source={{ uri: (constants.image_addr + '/' + item.compressFileList[0].compressFile) }} style={[{height:85, width:85},this.state.productType=='P'?{marginTop:20}:{marginTop:15}]} />
                                </TouchableOpacity>
                                <Modal visible={this.state.isShowImage} transparent={true}>
                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}}
                                    enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                    onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls}
                                    onSave={()=>{
                                        saveImage( this.state.urls[0].url)
                                    }}/>
                                </Modal>
                            </View>
                            :
                            <View style={{display:'flex',justifyContent:'center',alignItems:'center'}}>
                                <Image  style={[{height:85, width:85},this.state.productType=='P'?{marginTop:20}:{marginTop:15}]} source={require('../../../assets/icon/iconfont/emptyPicture.png')}></Image>
                            </View>
                        }
                    </View>
                    <View style={{flexDirection:'column',marginLeft:20,marginTop:10}}>
                        {
                           this.state.productType == "P" ?
                            <View style={{marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>材质：{item.productMaterial}</Text>
                            </View>
                            : <View/>
                        }
                        <View style={{marginBottom:5}}>
                            <Text style={styles.blackTextStyle}>数量：{item.productWeight}吨</Text>
                        </View>
                        {
                            item.productPrice ?
                            <View style={{flexDirection:'row',marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>价格：
                                    <Text style={styles.redTextStyle}>{item.productPrice}</Text>
                                    {
                                        this.myIsNaN(item.productPrice) ?
                                        <Text style={styles.blackTextStyle}> 元/吨</Text>
                                        :
                                        null
                                    }
                                </Text>
                            </View> :
                            <View style={{flexDirection:'row',marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>价格：
                                    <Text style={styles.redTextStyle}>面议</Text>
                                </Text>
                            </View>
                        }
                        <View style={{marginBottom:5}}>
                            <Text style={styles.blackTextStyle}>产地：{item.productionAddr}</Text>
                        </View>
                        <View style={{marginBottom:5}}>
                            <Text style={[styles.blackTextStyle]}>产品状态：<Text style={[item.productState=="U"?{color:'#3ab240'}:{color:'#333333'}]}>{item.productState=="U"?"上架":"下架"}</Text></Text>
                        </View>
                    </View>
                </View>
                <View style={{marginTop:5}}>
                    <Text style={styles.blackTextStyle}>说明：{item.productExplain?item.productExplain:"无"}</Text>
                </View>
                <View style={{marginTop:5}}>
                    <Text style={styles.blackTextStyle}>联系人：{item.contactPerson}</Text>
                </View>
                <View style={{marginTop:5}}>
                    <Text style={styles.blackTextStyle}>联系电话：{item.contactTel}</Text>
                </View>
                <View style={{marginTop:5}}>
                    <Text style={styles.blackTextStyle}>发布时间：{item.releaseDate}</Text>
                </View>
                <View style={{marginTop:5}}>
                    <Text style={styles.blackTextStyle}>失效时间：<Text style={[item.expDate?{color:'#CB4139'}:{}]}>{item.expDate?item.expDate.split(" ")[0].replace(/-/g,"/"):"无"} {item.expDate <= this.getNowTime() ? "[过期]" : ""}</Text></Text>
                </View>
                {
                    item.auditState == '3' || item.auditState == '4' ?
                    <View>
                        <View style={{marginTop:5}}>
                            <Text style={styles.blackTextStyle}>审核人：{item.auditUserName?item.auditUserName:"无"}</Text>
                        </View>
                        <View style={{marginTop:5}}>
                            <Text style={styles.blackTextStyle}>审核日期：{item.auditDate?item.auditDate:"无"}</Text>
                        </View>
                    </View>
                    :
                    null
                }
                {
                    item.auditState != "1" ?
                    <View style={{marginTop:5}}>
                        <Text style={[styles.blackTextStyle]}>审核结果：<Text style={{color:item.auditState == '3' ? "#3ab240" : (item.auditState == '4' ? '#CB4139' : '#559ff3')}}>{item.auditState == '3'? '通过' : (item.auditState == '4' ? '驳回' : '待审核')}</Text></Text>
                    </View>
                    :
                    null
                }

                {/* <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>是否现货：{item.spotFlag=="Y"?"是":"否"}</Text>
                </View> */}
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    {
                        item.auditState == "1" ?
                        <TouchableOpacity onPress={() => {
                            Alert.alert('确认','您确定要发起审核?',[
                                {
                                    text:"取消", onPress:()=>{
                                    WToast.show({data:'点击了取消'});
                                    }
                                },
                                {
                                    text:"确定", onPress:()=>{
                                        WToast.show({data:'点击了确定'});
                                        this.startAudit(item)
                                    }
                                }
                            ]);
                        }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {width:70,flexDirection:'row',backgroundColor:'#FFB800'}]}>
                                <Text style={[CommonStyle.itemBottomDetailBtnTextStyle,{fontSize:14}]}>发起审核</Text>
                            </View>
                        </TouchableOpacity>
                        :
                        null
                    }

                    <TouchableOpacity onPress={() => {
                        Alert.alert('确认',item.productState=="U"?'您确定要下架该产品吗?':'您确定要上架该产品吗?',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.operateProduct(item)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {width:70,flexDirection:'row'},item.productState=="U"?{backgroundColor:'#559ff3'}:{backgroundColor:'#FC783D'}]}>
                            <Text style={[CommonStyle.itemBottomDetailBtnTextStyle,{fontSize:14}]}>{item.productState=="U"?"下架":"上架"}</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            if(item.auditState == "1" || item.auditState == "4"  || item.expDate <= this.getNowTime()){
                                // 只有未发起审核、被驳回、过期的数据可以编辑
                            }
                            else {
                                WToast.show({data:'审核通过或正在审核，不可编辑！'});
                                return;
                            }
                            this.props.navigation.navigate("MyProductSaleReleaseAdd",
                            {
                                // 传递参数
                                releaseId:item.releaseId,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,(item.auditState == "1" || item.auditState == "4"  || item.expDate <= this.getNowTime()) ? "" : CommonStyle.disableViewStyle,{backgroundColor:'#559ff3',width:70,flexDirection:"row",marginLeft:0}]}>
                            <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{fontSize:14}]}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该产品吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteRelease(item.releaseId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{borderColor:'#33333330',width:70,flexDirection:"row",marginLeft:0}]}>
                            {/* <Image  style={{width:20, height:20,marginRight:5}} source={require('../../../assets/icon/iconfont/delete.png')}></Image> */}
                            <Text style={[CommonStyle.itemBottomDeleteBtnTextStyle,{fontSize:14,color:'#33333360'}]}>删除</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    productTypeRow=(item, index)=>{
        return (
            <View key={item.typeId} >
                <TouchableOpacity onPress={()=>{
                    var typeCode = item.typeCode;
                    this.setState({
                        productType:typeCode
                    })

                    let url= "/biz/product/release/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "releaseType": "R",
                        "userId":constants.loginUser.userId,
                        "productType":typeCode,
                        "searchKeyWord":this.state.searchKeyWord,
                        "auditState":this.state.selAuditType,
                    };
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.typeCode} style={[item.typeCode===this.state.productType? [CommonStyle.selectedBlockItemViewStyle,{backgroundColor:'#FFF',borderBottomWidth:2,borderBottomColor:'#255BDA',borderRadius:0,paddingBottom:0,marginBottom:0}] : CommonStyle.blockItemViewStyle,{paddingLeft:8,backgroundColor:'#FFF',paddingBottom:0,marginBottom:0.8}]}>
                        <Text style={[item.typeCode===this.state.productType? styles.selectedBlockItemTextStyle : styles.blockItemTextStyle]}>
                            {item.typeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }
    searchByKeyWord = () => {
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": "R",
            "userId":constants.loginUser.userId,
            "productType":this.state.productType,
            "searchKeyWord":this.state.searchKeyWord,
            "auditState":this.state.selAuditType,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    auditTypeChooseStateRow = (item, index) => {
        return (
            <View key={item.auditType} >
                <TouchableOpacity onPress={() => {
                    var selAuditType = item.auditType;
                    console.log("selAuditType",selAuditType);
                    this.setState({
                        selAuditType: selAuditType
                    })

                    let url= "/biz/product/release/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "releaseType": "R",
                        "userId":constants.loginUser.userId,
                        "productType":this.state.productType,
                        "searchKeyWord":this.state.searchKeyWord,
                        "auditState":selAuditType,
                    };

                    // console.log("selYearsChooseName+1:"+ this.addOneYear(selYearsChooseName))
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.auditType} style={[item.auditType === this.state.selAuditType ? [CommonStyle.selectedBlockItemViewStyle,{backgroundColor:"#FC783D"}] : [CommonStyle.blockItemViewStyle,{}], { paddingLeft:4, paddingRight:4 ,width:screenWidth/6,borderRadius:0}]}>
                        <Text style={[item.auditType === this.state.selAuditType ? [{color:"#ffffff",fontSize:17,textAlign:'center'}] : [{color:"#000000",fontSize:17,textAlign:'center'}], { fontWeight: 'bold' }]}>
                            {item.auditTypeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='我的产品'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                 <View style={[{marginTop:0,backgroundColor:'#FFF'}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop:5,backgroundColor:'#FFF',marginLeft:10}}>
                        <Image  style={[{height:100 ,borderRadius:10,width:screenWidth - 20}]} source={require('../../../assets/image/myProductBanner.jpg')} />
                    </View>

                    <View style={{backgroundColor:'#ffffff',height:50,marginTop:3,marginBottom:3}}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Image  style={{width:25, height:25}} source={require('../../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'搜索名称/型号/材质/产地'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                            {/* | {this.state.currentPage} | {this.state.totalPage} | {this.state.totalRecord} | {this.state.refreshing ? "Y":"N"} | {(this.state.currentPage) < this.state.totalPage ? "YY":"NN"} */}
                        </View>
                    </View>
                    <View style={{ index: 1000, flexWrap: 'wrap', flexDirection: 'row' ,borderBottomColor:'#33333333',borderBottomWidth:2,alignItems:'flex-end',borderTopWidth:7,borderTopColor:'#F4F7F9'}}>
                        {
                            (this.state.productTypeList && this.state.productTypeList.length > 0)
                                ?
                                this.state.productTypeList.map((item, index) => {
                                    return this.productTypeRow(item)
                                })
                                : <View />
                        }
                    </View>
                    <View style={[styles.innerViewStyle, { marginLeft:0,marginTop:0,marginBottom:0,paddingBottom:0,borderBottomWidth:0 }]}>
                        <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                            {
                                (this.state.auditDataSource && this.state.auditDataSource.length > 0)
                                    ?
                                    this.state.auditDataSource.map((item, index) => {
                                        return this.auditTypeChooseStateRow(item)
                                    })
                                    :
                                <View />
                            }
                        </View>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent(ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight))}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />

                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        height: 40,
        width:screenWidth -40,
        marginTop:5,
        marginLeft:20,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#F2F5FC",
        backgroundColor:"#F2F5FC",
        borderRadius:5,
    },

    leftLabNameTextStyle: {
        fontSize: 18,
    },
    selectedBlockItemTextStyle: {
        fontSize: 17,
        // fontFamily: "PingFangSC-Medium,PingFang SC",
        fontWeight: 'bold',
        color: '#255BDA',
        marginBottom:5
    },
    blockItemTextStyle: {
        fontSize:17,
        color:'#000000',
        marginBottom:5
    },
    greyTextStyle: {
        fontSize:14,
        color:'#33333399',
        backgroundColor:'#FFF',
    },
    blackTextStyle: {
        fontSize:15,
        color:'#333333',
    },
    redTextStyle: {
        fontSize:16,
        color:'#E41F00',
        fontWeight:'bold',
    },
    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft:15
    },
    searchInputText: {
        width: screenWidth -100,
        color: '#33333399',
        fontSize: 16,
        marginLeft: 10,
        paddingBottom: 0,
        paddingTop:4
    },
    innerViewStyle:{
        width:screenWidth,
        alignItems: 'center',
        // marginLeft:10,
        // paddingLeft:10,
        paddingTop:10,
        marginTop:10,
        backgroundColor:'#FFF',
        borderBottomColor:'#33333315',
        borderBottomWidth:0.8,
        paddingBottom:10
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize: 18,
        color:'#333333',
        backgroundColor:'#FFF',
        fontWeight:'bold',
        marginLeft:0,
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },

});
