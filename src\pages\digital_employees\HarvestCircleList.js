import React, {Component} from 'react';
import {
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  ImageBackground,
  Linking,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import MessageInputModal from '../../component/MessageInputModal';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class HarvestCircleList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: 'Y',
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,

      showSearchItemBlock: false,

      departmentDataSource: null,

      selDepartmentId: null,
      selDepartmentName: null,
      selDepartmentStaffList: null,
      selStaffId: null,
      selStaffName: null,

      qryStartTime: null,
      selectedQryStartDate: [],
      searchKeyWord: '',
      messageModal: false,
      exportPdfModal: false,
      messageFkId: '',
      parentMessageId: '',
      messageContent: '',
    };
  }

  initqryStartTime = () => {
    // 当前时间
    var currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 1);
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _qryStartTime =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      selectedQryStartDate: [
        currentDate.getFullYear(),
        currentDateMonth,
        currentDateDay,
      ],
      qryStartTime: _qryStartTime,
    });
    return _qryStartTime;
  };

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    var _qryStartTime = this.initqryStartTime();
    console.log('componentWillMount==_qryStartTime', _qryStartTime);
    let loadTypeUrl = '/biz/department/list_for_tenant';
    let loadRequest = {qryAll_NoPower: 'Y', currentPage: 1, pageSize: 1000};
    httpPost(loadTypeUrl, loadRequest, (response) => {
      if (response.code == 200 && response.data) {
        this.setState({
          departmentDataSource: response.data,
        });
      }
    });

    this.loadHarvestList(_qryStartTime);
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/harvest/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      visible: this.state.visible,
      departmentId: this.state.selDepartmentId,
      userId: this.state.selStaffId,
      qryStartTime: this.state.qryStartTime,
      searchKeyWord: this.state.searchKeyWord,
      harvestState: '0AA',
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/harvest/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      visible: this.state.visible,
      departmentId: this.state.selDepartmentId,
      userId: this.state.selStaffId,
      qryStartTime: this.state.qryStartTime,
      searchKeyWord: this.state.searchKeyWord,
      harvestState: '0AA',
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    if (this.state.refreshing) {
      WToast.show({data: 'loading...'});
      return;
    }
    this.setState({ refreshing: true }, () => {
        console.log('refreshing 已更新:', this.state.refreshing);
        // 在这里执行后续操作
        this.loadHarvestList();
    });
  };

  loadHarvestList = (_qryStartTime) => {
    let url = '/biz/harvest/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      visible: this.state.visible,
      departmentId: this.state.selDepartmentId,
      qryStartTime: _qryStartTime ? _qryStartTime : this.state.qryStartTime,
      userId: this.state.selStaffId,
      searchKeyWord: this.state.searchKeyWord,
      harvestState: '0AA',
    };
    httpPost(url, loadRequest, this.loadHarvestListCallBack);
  };

  loadHarvestListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteHarvest = (harvestId) => {
    console.log('=======delete=harvestId', harvestId);
    let url = '/biz/harvest/delete';
    let requestParams = {harvestId: harvestId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  // 保存留言
  saveMessage = () => {
    console.log('=======saveMessage');
    let toastOpts;
    if (!this.state.messageContent) {
      toastOpts = getFailToastOpts('请输入留言内容');
      WToast.show(toastOpts);
      return;
    }
    let url = '/biz/portal/message/board/add';
    let requestParams = {
      messageContent: this.state.messageContent,
      messageFkId: this.state.harvestItem.harvestId,
      parentMessageId: this.state.parentMessageId,
      messageFkType: 'C',
    };
    httpPost(url, requestParams, this.saveMessageCallBack);
  };

  // 保存留言的回调函数
  saveMessageCallBack = (response) => {
    this.setState({
      messageContent: '',
    });
    let toastOpts;
    switch (response.code) {
      case 200:
        WToast.show({data: '留言发送成功'});
        this.callBackFunction();
        break;
      default:
        toastOpts = getFailToastOpts(response.message);
        WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('HarvestDetail', {
            // 传递参数
            harvestId: item.harvestId,
            userName: item.userName,
            // 传递回调函数
            refresh: this.callBackFunction,
          });
        }}>
        <View key={item.harvestId} style={[CommonStyle.innerViewStyle]}>
          {/* 成果顶部信息 */}
          <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
            <View
              style={{
                height: 48,
                width: 48,
                borderRadius: 20,
                marginTop: 4,
                paddingTop: 3,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#1E6EFA',
              }}>
              {this.state.userPhoto ? (
                <Image
                  source={{uri: this.state.userPhotoUrl}}
                  style={{height: 48, width: 48, borderRadius: 50}}
                />
              ) : (
                <ImageBackground
                  source={require('../../assets/icon/iconfont/profilePicture.png')}
                  style={{width: 48, height: 48}}>
                  <View
                    style={{
                      height: 48,
                      width: 48,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    {item.userName.length <= 2 ? (
                      <Text
                        style={{
                          color: '#FFFFFF',
                          fontSize: 17,
                          fontWeight: 'normal',
                          textAlign: 'center',
                          lineHeight: 22,
                        }}>
                        {item.userName}
                      </Text>
                    ) : (
                      <Text
                        style={{
                          color: '#FFFFFF',
                          fontSize: 17,
                          fontWeight: 'normal',
                          textAlign: 'center',
                          lineHeight: 22,
                        }}>
                        {item.userName.slice(-2)}
                      </Text>
                    )}
                  </View>
                </ImageBackground>
              )}
            </View>
            <View style={{marginLeft: 11, flexDirection: 'column'}}>
              <View style={{flexDirection: 'row', marginTop: 4}}>
                <View style={{flexDirection: 'row'}}>
                  <Text style={{fontSize: 16}}>{item.userName}的成果</Text>
                </View>
                {/* 草稿,优秀,私密,无 */}
                {item.harvestState === '0BB' ? (
                  <View
                    style={{
                      width: 38,
                      height: 20,
                      marginLeft: 7,
                      borderRadius: 2,
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: '#E63633',
                    }}>
                    <Text style={{fontSize: 13, color: '#FFFFFF'}}>草稿</Text>
                  </View>
                ) : (
                  <View></View>
                )}
                {item.harvestState === '0AA' && item.visible === 'N' ? (
                  <View
                    style={{
                      width: 38,
                      height: 20,
                      marginLeft: 7,
                      borderRadius: 2,
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: '#B0B9BF',
                    }}>
                    <Text style={{fontSize: 13, color: '#FFFFFF'}}>私密</Text>
                  </View>
                ) : (
                  <View></View>
                )}
                {item.harvestState === '0AA' &&
                item.visible === 'Y' &&
                item.score === 1 ? (
                  <View
                    style={{
                      width: 58,
                      height: 20,
                      marginLeft: 7,
                      borderRadius: 2,
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: '#E63633',
                    }}>
                    <Image
                      style={{
                        width: 16,
                        height: 18,
                        marginRight: 2,
                        marginRight: 3,
                      }}
                      source={require('../../assets/icon/good.png')}></Image>
                    <Text style={{fontSize: 13, color: '#FFFFFF'}}>优秀</Text>
                  </View>
                ) : (
                  <View></View>
                )}
              </View>
              <View style={{flexDirection: 'row'}}>
                <Image
                  style={{
                    height: 13,
                    width: 12,
                    marginTop: 5,
                    marginLeft: 1,
                    marginRight: 5,
                  }}
                  source={require('../../assets/icon/iconfont/clock.png')}></Image>
                {item.gmtModified == null ? (
                  <View style={{marginTop: 4, marginBottom: 3, marginRight: 4}}>
                    <Text
                      style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)'}]}>
                      {item.gmtCreated.slice(0, 10)} 提交
                    </Text>
                  </View>
                ) : (
                  <View style={{marginTop: 4, marginBottom: 3, marginRight: 4}}>
                    <Text
                      style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)'}]}>
                      {item.gmtModified.slice(0, 10)} 提交
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>
          {/* 分隔线 */}
          <View style={styles.lineViewStyle} />
          {/* <View style={styles.titleViewStyle}>
              <Text style={{fontSize:16}}>标题</Text>
          </View> */}
          <View style={styles.itemContentTextStyle}>
            <Text
              style={[
                styles.itemContentStyle,
                {
                  marginLeft: 15,
                  marginTop: 10,
                  fontWeight: 'bold',
                  fontSize: 20,
                  color: '#404956',
                },
              ]}>
              {item.harvestTitle}
            </Text>
          </View>
          {/* <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>内容</Text>
          </View> */}
          <View style={styles.itemContentTextStyle}>
            <Text
              style={[
                styles.itemContentStyle,
                {fontSize: 16, marginLeft: 15, marginTop: 5},
              ]}>
              {item.harvestContent}
            </Text>
          </View>
          {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>提交人：</Text>
                    <Text style={styles.itemContentStyle}>{item.userName}</Text>
                </View> */}
          {/* {
                    item.gmtModified == null ?
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>提交时间：</Text>
                            <Text style={styles.itemContentStyle}>{item.gmtCreated}</Text>
                        </View>
                        :
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>提交时间：</Text>
                            <Text style={styles.itemContentStyle}>{item.gmtModified}</Text>
                        </View>
                } */}
          {/* <View style={{
                    width: 40, height: 40, backgroundColor: 'rgba(255,0,0,0.0)', position: 'absolute',
                    alignItems: 'center', justifyContent: 'center', right: 10, top: 5,
                }}>
                    <TouchableOpacity onPress={() => {
                        // this.setGood(item)
                    }}>
                        {
                            item.score ?
                                <Image style={{ width: 50, height: 50 }} source={require('../../assets/icon/iconfont/good-red.png')}></Image>
                                :
                                <View />
                        }
                    </TouchableOpacity>
                </View> */}
          {/* <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                    <TouchableOpacity onPress={() => {
                        this.props.navigation.navigate("HarvestDiscussList",
                            {
                                // 传递参数
                                harvestId: item.harvestId,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }}>
                        <View style={[{
                            width: 64,
                            height: 28,
                            flexDirection: "row",
                            // justifyContent: 'center',
                            alignItems: 'center',
                            margin: 10,
                            marginRight: 16,
                            borderColor: 'rgba(30, 110, 250, 1)',
                            borderWidth: 0.85,
                            borderRadius: 6
                        }]}>
                            <Image style={{ width: 24, height: 24, marginRight: 6, marginLeft: 5 }} source={require('../../assets/icon/iconfont/newMessageBlack.png')}></Image>
                            <Text style={[{ color: 'rgba(83, 106, 247, 1)', fontSize: 14 }]}>{item.messageNum}</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}
          {/* 留言 按钮*/}
          <View
            style={[
              CommonStyle.itemBottomBtnStyle,
              {flexWrap: 'wrap', marginLeft: 12, marginRight: 16},
            ]}>
            {item.harvestState === '0BB' ? (
              <View></View>
            ) : (
              <View
                style={[CommonStyle.itemBottomBtnStyle, {flexWrap: 'wrap'}]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      messageModal: true,
                      harvestItem: item,
                    });
                  }}>
                  <View
                    style={[
                      {
                        width: 78,
                        height: 28,
                        flexDirection: 'row',
                        alignItems: 'center',
                        margin: 10,
                        marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                      },
                    ]}>
                    <Image
                      style={{
                        width: 20,
                        height: 20,
                        marginRight: 8,
                        marginLeft: 12,
                      }}
                      source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                    <Text
                      style={[
                        {
                          color: 'rgba(0, 10, 32, 0.65)',
                          fontSize: 14,
                          lineHeight: 24,
                        },
                      ]}>
                      留言
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            )}
          </View>
          {/* 留言 */}
          {item.messageList && item.messageList.length > 0 ? (
            <View
              style={{
                backgroundColor: 'rgba(242, 245, 252, 0.5)',
                borderRadius: 10,
                width: screenWidth - 24,
                marginLeft: 12,
                marginRight: 12,
                paddingTop: 5,
                marginBottom: 5,
              }}>
              {item.messageList.slice(0, 3).map((item, index) => {
                return (
                  <View
                    key={item.messageId}
                    style={{
                      flexDirection: 'row',
                      marginLeft: 10,
                      marginTop: 10,
                      marginBottom: 10,
                    }}>
                    {item.operatorPhoto ? (
                      <Image
                        source={{
                          uri: constants.image_addr + '/' + item.operatorPhoto,
                        }}
                        style={{height: 36, width: 36, borderRadius: 50}}
                      />
                    ) : (
                      <ImageBackground
                        source={require('../../assets/icon/iconfont/profilePicture.png')}
                        style={{height: 36, width: 36}}>
                        <View
                          style={{
                            height: 36,
                            width: 36,
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          {item.operatorName <= 2 ? (
                            <Text
                              style={{
                                color: '#FFFFFF',
                                fontSize: 13,
                                fontWeight: 'normal',
                                textAlign: 'center',
                                lineHeight: 20,
                              }}>
                              {item.operatorName}
                            </Text>
                          ) : (
                            <Text
                              style={{
                                color: '#FFFFFF',
                                fontSize: 13,
                                fontWeight: 'normal',
                                textAlign: 'center',
                                lineHeight: 20,
                              }}>
                              {item.operatorName.slice(-2)}
                            </Text>
                          )}
                        </View>
                      </ImageBackground>
                    )}

                    <View
                      style={{
                        flexDirection: 'column',
                        marginLeft: 10,
                        flex: 1,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'flex-start',
                          alignItems: 'center',
                          paddingTop: 4,
                        }}>
                        <View style={{flexDirection: 'row'}}>
                          <Text style={{fontSize: 16}}>
                            {item.operatorName}
                          </Text>
                        </View>
                        <View style={{flexDirection: 'row', marginLeft: 6}}>
                          <Text
                            style={[
                              {fontSize: 12, color: 'rgba(0,10,32,0.45)'},
                            ]}>
                            {item.gmtCreated.slice(0, 16)}
                          </Text>
                        </View>
                      </View>

                      {item.parentMessageId ? (
                        <View
                          style={[
                            {
                              flexDirection: 'column',
                              justifyContent: 'flex-start',
                            },
                          ]}>
                          <View
                            style={[
                              {
                                flexDirection: 'row',
                                justifyContent: 'flex-start',
                                alignItems: 'flex-start',
                                marginLeft: 9,
                                marginTop: 11,
                              },
                            ]}>
                            <Text
                              style={[
                                styles.itemContentStyle,
                                {color: 'rgba(0,10,32,0.45)'},
                              ]}>
                              {'回复 ' +
                                item.parentUserName +
                                ': ' +
                                item.parentMessageContent}
                            </Text>
                          </View>
                          <View
                            style={[
                              {
                                flexDirection: 'row',
                                justifyContent: 'flex-start',
                                alignItems: 'flex-start',
                                marginTop: 8,
                              },
                            ]}>
                            <Text style={styles.itemContentStyle}>
                              {item.messageContent}
                            </Text>
                          </View>
                        </View>
                      ) : (
                        <View
                          style={[
                            {
                              flexDirection: 'row',
                              justifyContent: 'flex-start',
                              alignItems: 'flex-start',
                              marginTop: 10,
                            },
                          ]}>
                          <Text style={styles.itemContentStyle}>
                            {item.messageContent}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>
                );
              })}
            </View>
          ) : (
            <View />
          )}
          {/* 留言输入框弹窗 */}
          <MessageInputModal
            visible={this.state.messageModal}
            onClose={() =>
              this.setState({
                messageModal: false,
                messageContent: '',
              })
            }
            onSend={() => {
              if (!this.state.messageContent) {
                return;
              }
              this.setState({messageModal: false});
              this.saveMessage();
            }}
            messageContent={this.state.messageContent}
            onChangeMessageContent={(text) =>
              this.setState({messageContent: text})
            }
          />
          {/* 导出pdf弹窗 */}
          <Modal
            animationType="fade"
            transparent={true}
            visible={this.state.exportPdfModal}
            //  onShow={this.onShow.bind(this)}
            onRequestClose={() => console.log('onRequestClose...')}>
            <View
              style={[
                CommonStyle.fullScreenKeepOut,
                {backgroundColor: 'rgba(0,0,0,0.08)'},
              ]}>
              <View
                style={{
                  width: 291,
                  height: 156,
                  bottom: screenHeight / 2 - 80,
                  position: 'absolute',
                  backgroundColor: '#FFFFFF',
                  borderRadius: 10,
                }}>
                <View
                  style={{
                    height: 50,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: 10,
                  }}>
                  <Text style={{fontSize: 18}}>确认导出成果？</Text>
                </View>
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: 24,
                  }}>
                  <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                    导出地址已复制到粘贴板，使用浏览器打开
                  </Text>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    width: 291,
                    height: 56,
                    marginTop: 15,
                    borderTopWidth: 1,
                    borderColor: '#DFE3E8',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      this.setState({
                        exportPdfModal: false,
                      });
                      WToast.show({data: '点击了不打开'});
                    }}>
                    <View
                      style={{
                        width: 145,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          fontSize: 17,
                          fontWeight: '400',
                          color: '#000A20',
                        }}>
                        不打开
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => {
                      WToast.show({data: '点击了打开'});
                      this.setState({
                        exportPdfModal: false,
                      });
                      this.exportPdfFile();
                    }}>
                    <View
                      style={{
                        width: 145,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderLeftWidth: 1,
                        borderColor: '#DFE3E8',
                      }}>
                      <Text
                        style={{
                          fontSize: 17,
                          fontWeight: '400',
                          color: '#1E6EFA',
                        }}>
                        打开
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
        </View>
      </TouchableOpacity>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 部门
  renderDepartmentRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selDepartmentId: item.departmentId,
            selDepartmentName: item.departmentName,
            selDepartmentStaffDataSource: item.departmentUserDTOList,
            selStaffId: null,
            selStaffName: null,
          });
        }}>
        <View
          key={'department_' + item.departmentId}
          style={[
            item.departmentId === this.state.selDepartmentId
              ? CommonStyle.choseToSearchItemsSelectedViewColor
              : CommonStyle.choseToSearchItemsViewColor,
            CommonStyle.choseToSearchItemsViewSize,
          ]}>
          <Text
            style={[
              item.departmentId === this.state.selDepartmentId
                ? CommonStyle.choseToSearchItemsSelectedTextStyle
                : CommonStyle.choseToSearchItemsTextStyle,
            ]}>
            {item.departmentName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderDepartmentStaffRow = (item, index) => {
    return (
      <View key={item.jobUserId}>
        <TouchableOpacity
          onPress={() => {
            this.setState({
              selStaffId: item.userId,
              selStaffName: item.staffName,
            });
          }}>
          <View
            key={'jobuser_' + item.jobUserId}
            style={[
              item.userId === this.state.selStaffId
                ? CommonStyle.choseToSearchItemsSelectedViewColor
                : CommonStyle.choseToSearchItemsViewColor,
              CommonStyle.choseToSearchItemsViewSize,
            ]}>
            <Text
              style={[
                item.userId === this.state.selStaffId
                  ? CommonStyle.choseToSearchItemsSelectedTextStyle
                  : CommonStyle.choseToSearchItemsTextStyle,
              ]}>
              {item.staffName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          // 触发-导出弹窗Modal
          this.setState({
            exportPdfModal: true,
          });
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 23, height: 23}}
          source={require('../../assets/icon/iconfont/newExport.png')}></Image>
      </TouchableOpacity>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  // 显示搜索项目
  showSearchItemSelect() {
    if (
      !this.state.departmentDataSource ||
      this.state.departmentDataSource.length < 1
    ) {
      WToast.show({data: '请先添加部门'});
      return;
    }
    this.setState({
      showSearchItemBlock: true,
    });
  }

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/harvest_circle';

    // if(this.state.selDepartmentId != null  && this.state.selStaffId == null) {

    //     return ;
    // }
    // if(this.state.selStaffId == null) {
    //     WToast.show({data:'请选择提交人'});
    //     return ;
    // }
    let requestParams = {
      // "userId": constants.loginUser.userId,
      // "userId":this.state.selStaffId,
      qryStartTime: this.state.qryStartTime,
      currentPage: 1,
      visible: 'Y',
      pageSize: 1000,
      departmentId: this.state.selDepartmentId,
      userId: this.state.selStaffId,
      harvestState: '0AA',
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        // WToast.show({ data: "导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data });
        // 直接打开外网链接
        Linking.openURL(response.data);
      }
    });
  };

  openQryStartDate() {
    this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate);
  }

  callBackSelectQryStartDateValue(value) {
    console.log('==========提交时间选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectedQryStartDate: value,
    });
    if (value && value.length) {
      var qryStartTime = '';
      var vartime;
      for (var index = 0; index < value.length; index++) {
        vartime = value[index];
        if (index === 0) {
          qryStartTime += vartime;
        } else {
          qryStartTime += '-' + vartime;
        }
      }
      this.setState({
        qryStartTime: qryStartTime,
      });

      let loadUrl = '/biz/harvest/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        visible: this.state.visible,
        departmentId: this.state.selDepartmentId,
        userId: this.state.selStaffId,
        qryStartTime: qryStartTime,
        searchKeyWord: this.state.searchKeyWord,
        harvestState: '0AA',
      };
      httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }
  }
  searchByKeyWord = () => {
    let loadUrl = '/biz/harvest/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      // "gmtCreated": this.state.gmtCreated,
      visible: this.state.visible,
      departmentId: this.state.selDepartmentId,
      qryStartTime: this.state.qryStartTime,
      userId: this.state.selStaffId,
      harvestState: '0AA',
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="成果圈"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />

        <View
          style={[
            CommonStyle.headViewStyle,
            {
              flexDirection: 'row', // 添加这一行以使子元素水平排列
              alignItems: 'center', // 添加这一行以使子元素垂直居中对齐
              borderWidth: 0,
            },
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={{
              flexDirection: 'row', // 添加这一行以使子元素水平排列
              alignItems: 'center', // 添加这一行以使子元素垂直居中对齐
            }}>
            <View
              style={[
                {
                  backgroundColor: '#ffffff',
                  flexDirection: 'row',
                  width: screenWidth / 1.75,
                  justifyContent: 'flex-start',
                },
              ]}>
              <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                {this.state.showSearchItemBlock ? (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                      {this.state.selDepartmentId &&
                      this.state.selDepartmentName
                        ? this.state.selDepartmentName +
                          (this.state.selStaffId && this.state.selStaffName
                            ? ' - ' + this.state.selStaffName
                            : '')
                        : '选择部门'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchClosedIconSize]}
                      source={require('../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                  </View>
                ) : (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                      {this.state.selDepartmentId &&
                      this.state.selDepartmentName
                        ? this.state.selDepartmentName +
                          (this.state.selStaffId && this.state.selStaffName
                            ? ' - ' + this.state.selStaffName
                            : '')
                        : '选择部门'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchOpenedIconSize]}
                      source={require('../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                  </View>
                )}
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={{
                width: screenWidth / 3,
                flexDirection: 'row',
                justifyContent: 'flex-end',
                marginLeft: 10,
              }}
              onPress={() => this.openQryStartDate()}>
              <View
                style={[
                  {
                    height: 32,
                    paddingLeft: 10,
                    paddingRight: 10,
                    opacity: 0.6,
                    borderRadius: 8,
                    backgroundColor: 'rgba(242, 245, 252, 1)',
                    justifyContent: 'center',
                    alignItems: 'center',
                  },
                ]}>
                <Text style={{color: 'rgba(0,10,32,0.85)', fontSize: 14}}>
                  {!this.state.qryStartTime ? '时间' : this.state.qryStartTime}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        <View>
          {this.state.showSearchItemBlock ? (
            <View
              style={[
                CommonStyle.choseToSearchBigBoxViewStyle,
                {
                  height: ifIphoneXContentViewDynamicHeight(
                    this.state.topBlockLayoutHeight,
                  ),
                },
              ]}>
              <View style={CommonStyle.heightLimited}>
                <ScrollView>
                  <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                    <View
                      style={[
                        {backgroundColor: 'rgba(255,255,255,1)'},
                        CommonStyle.choseToSearchItemsViewSize,
                      ]}>
                      <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                        部门：
                      </Text>
                    </View>
                    {this.state.departmentDataSource &&
                    this.state.departmentDataSource.length > 0
                      ? this.state.departmentDataSource.map((item, index) => {
                          return this.renderDepartmentRow(item);
                        })
                      : null}
                  </View>
                  {this.state.selDepartmentStaffDataSource &&
                  this.state.selDepartmentStaffDataSource.length > 0 ? (
                    <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                      <View
                        style={[
                          {backgroundColor: 'rgba(255,255,255,1)'},
                          CommonStyle.choseToSearchItemsViewSize,
                        ]}>
                        <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                          提交人：
                        </Text>
                      </View>
                      {this.state.selDepartmentStaffDataSource.map(
                        (item, index) => {
                          return this.renderDepartmentStaffRow(item);
                        },
                      )}
                    </View>
                  ) : null}
                </ScrollView>
              </View>
              <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]}>
                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    let loadUrl = '/biz/harvest/list';
                    let loadRequest = {
                      currentPage: 1,
                      pageSize: this.state.pageSize,
                      departmentId: this.state.selDepartmentId,
                      userId: this.state.selStaffId,
                      qryStartTime: this.state.qryStartTime,
                    };
                    console.log('loadRequest:', loadRequest);
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>
                      确定搜索
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}

          <View
            style={[
              CommonStyle.headViewStyle,
              {
                borderLeftWidth: 0,
                borderRightWidth: 0,
              },
            ]}
            onLayout={this.topBlockLayout.bind(this)}>
            <View style={CommonStyle.singleSearchBox}>
              <View
                style={[
                  CommonStyle.searchBoxWithoutOthers,
                  {justifyContent: 'center'},
                ]}>
                <Image
                  style={{width: 16, height: 16, marginRight: 6}}
                  source={require('../../assets/icon/iconfont/search.png')}></Image>
                <TextInput
                  style={{
                    color: 'rgba(rgba(0, 10, 32, 0.45))',
                    fontSize: 14,
                    paddingTop: 0,
                    paddingBottom: 0,
                    paddingRight: 0,
                    paddingLeft: 0,
                  }}
                  returnKeyType="search"
                  returnKeyLabel="搜索"
                  onSubmitEditing={(e) => {
                    this.searchByKeyWord();
                  }}
                  placeholder={'请输入要搜索的内容'}
                  onChangeText={(text) => this.setState({searchKeyWord: text})}>
                  {this.state.searchKeyWord}
                </TextInput>
              </View>
            </View>
          </View>

          <View
            style={[
              CommonStyle.contentViewStyle,
              {
                height: ifIphoneXContentViewDynamicHeight(
                  this.state.topBlockLayoutHeight,
                ),
              },
            ]}>
            <FlatList
              data={this.state.dataSource}
              keyExtractor={(item) => item.harvestId}
              renderItem={({item, index}) => this.renderRow(item, index)}
              ListEmptyComponent={this.emptyComponent}
              // 自定义下拉刷新
              refreshControl={
                <RefreshControl
                  tintColor="#FF0000"
                  title="loading"
                  colors={['#FF0000', '#00FF00', '#0000FF']}
                  progressBackgroundColor="#FFFF00"
                  refreshing={this.state.refreshing}
                  onRefresh={() => {
                    this._loadFreshData();
                  }}
                />
              }
              // 底部加载
              ListFooterComponent={() => this.flatListFooterComponent()}
              onEndReached={() => this._loadNextData()}
            />
          </View>
          <BottomScrollSelect
            ref={'SelectQryStartDate'}
            callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
          />
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  searchInputText: {
    width: screenWidth / 2,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
  },
  leftLabView: {
    height: 40,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  innerViewStyle: {
    // marginTop:10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
  itemContentStyle: {
    fontSize: 14,
    lineHeight: 24,
    textAlign: 'left',
    textAlignVertical: 'top',
    color: 'rgba(0, 10, 32, 0.65)',
  },
  itemContentTextStyle: {
    marginLeft: 12,
    marginRight: 16,
    marginTop: 3,
    lineHeight: 24,
  },
  titleViewStyle: {
    flexDirection: 'row',
    marginLeft: 12,
    marginRight: 16,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  lineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    marginBottom: 6,
    borderBottomWidth: 0.5,
    borderColor: '#E8E9EC',
  },
});
