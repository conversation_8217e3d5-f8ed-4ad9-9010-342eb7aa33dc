import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import EmptyListComponent from '../../../component/EmptyListComponent';
var CommonStyle = require('../../../assets/css/CommonStyle');
const {
  ifIphoneXContentViewDynamicHeight,
} = require('../../../utils/ScreenUtil');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class CourseLevelStaffMgrAdd extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      courseLevelId: null,
      courseLevelName: '',
      selStaffIdList: [],
      oldSelStaffIdList: [],
      searchKeyWord: null,
    };
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {courseLevelId, courseLevelName} = route.params;
      if (courseLevelName) {
        this.setState({
          courseLevelName: courseLevelName,
        });
      }
      if (courseLevelId) {
        this.setState({
          courseLevelId: courseLevelId,
        });

        this.loadCourseLevelStaffList(courseLevelId);
        this.loadSelectCourseLevelStaffList(courseLevelId);
      }
    }
  }

  loadCourseLevelStaffList = (courseLevelId) => {
    let url = '/biz/course/level/user/tenant_staff';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      courseLevelId: courseLevelId ? courseLevelId : this.state.courseLevelId,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this.loadCourseLevelStaffListCallBack);
  };

  loadCourseLevelStaffListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      // this.setState({
      //     dataSource:response.data.dataList
      // })
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  loadSelectCourseLevelStaffList = (courseLevelId) => {
    let url = '/biz/course/level/user/tenant_staff';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: 10000,
      courseLevelId: courseLevelId ? courseLevelId : this.state.courseLevelId,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this.loadSelectCourseLevelStaffListCallBack);
  };

  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadCourseLevelStaffList();
  };

  loadSelectCourseLevelStaffListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      // this.setState({
      //     dataSource:response.data.dataList
      // })
      // var dataNew = response.data.dataList;
      // var dataOld = this.state.dataSource;
      // // dataOld.unshift(dataNew);
      // var dataAll = [...dataOld, ...dataNew];
      // this.setState({
      //     dataSource: dataAll,
      //     currentPage: response.data.currentPage + 1,
      //     totalPage: response.data.totalPage,
      //     totalRecord: response.data.totalRecord,
      //     refreshing: false
      // })
      var staffDTO;
      var selStaffIdList = [];
      for (var index = 0; index < response.data.dataList.length; index++) {
        staffDTO = response.data.dataList[index];
        if (staffDTO && staffDTO.jobSelectedStaff === 'Y') {
          selStaffIdList = selStaffIdList.concat(staffDTO.userId);
        }
      }
      this.setState({
        selStaffIdList: selStaffIdList,
        oldSelStaffIdList: copyArr(selStaffIdList),
      });
      console.log('=========oldSelStaffIdList:', selStaffIdList);
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      return;
    }
    let url = '/biz/course/level/user/tenant_staff';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      courseLevelId: this.state.courseLevelId,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.userId}>
        <TouchableOpacity
          onPress={() => {
            var selStaffIdList = this.state.selStaffIdList;
            if (item.jobSelectedStaff && item.jobSelectedStaff == 'Y') {
              item.jobSelectedStaff = 'N';
              arrayRemoveItem(selStaffIdList, item.userId);
            } else {
              item.jobSelectedStaff = 'Y';
              selStaffIdList = selStaffIdList.concat(item.userId);
            }
            this.setState({
              selStaffIdList: selStaffIdList,
            });
            WToast.show({data: '点击了' + item.userName});
            console.log('======selStaffIdList:', selStaffIdList);
          }}>
          <View
            key={item.userId}
            style={[
              styles.inputRowStyle,
              {
                paddingLeft: 12,
                height: 70,
                borderBottomWidth: 1,
                borderBottomColor: '#E8E9EC',
              },
              item.jobSelectedStaff && item.jobSelectedStaff === 'Y'
                ? {backgroundColor: 'rgba(248, 250, 251, 1)'}
                : {},
            ]}>
            <View
              style={[
                {
                  width: screenWidth - (leftLabWidth + 25),
                  paddingTop: 5,
                  position: 'relative',
                },
              ]}>
              <View
                style={{
                  height: 48,
                  width: 48,
                  borderRadius: 20,
                  marginTop: 4,
                  paddingTop: 3,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: '#1E6EFA',
                }}>
                {item.userName.length <= 2 ? (
                  <Text
                    style={{
                      color: 'rgba(255,255,255,1)',
                      fontSize: 17,
                      fontFamily: 'PingFangSC-Regular',
                      fontWeight: 'normal',
                      textAlign: 'center',
                      lineHeight: 20,
                    }}>
                    {item.userName}
                  </Text>
                ) : (
                  <Text
                    style={{
                      color: 'rgba(255,255,255,1)',
                      fontSize: 17,
                      fontFamily: 'PingFangSC-Regular',
                      fontWeight: 'normal',
                      textAlign: 'center',
                      lineHeight: 20,
                    }}>
                    {item.userName.slice(-2)}
                  </Text>
                )}
              </View>

              <View
                style={{
                  justifyContent: 'flex-start',
                  width: screenWidth - 15,
                  flexDirection: 'row',
                  position: 'absolute',
                  paddingTop: 13,
                }}>
                <View style={{flexDirection: 'row', marginLeft: 58}}>
                  <Text style={{fontSize: 16}}>{item.userName}</Text>
                </View>
              </View>

              <View
                style={[
                  {
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    width: screenWidth - 15,
                    flexDirection: 'row',
                    paddingLeft: 58,
                    position: 'absolute',
                    paddingTop: 36,
                  },
                ]}>
                <Image
                  style={{height: 13, width: 12, marginTop: 2, marginRight: 5}}
                  source={require('../../../assets/icon/iconfont/telephone.png')}></Image>
                <Text style={{fontSize: 12}}>{item.userNbr}</Text>
              </View>
            </View>
            {item.jobSelectedStaff && item.jobSelectedStaff === 'Y' ? (
              <View style={{marginLeft: 100, marginTop: 25}}>
                <Image
                  style={{height: 20, width: 20}}
                  source={require('../../../assets/icon/iconfont/staffSelected.png')}></Image>
              </View>
            ) : (
              <View></View>
            )}

            {/* <View style={styles.lineViewStyle}></View> */}
          </View>
        </TouchableOpacity>
      </View>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
      </TouchableOpacity>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          let requestUrl = '/biz/course/level/user/add';
          let requestParams = {
            courseLevelId: this.state.courseLevelId,
            selStaffIdList: this.state.selStaffIdList,
            oldSelStaffIdList: this.state.oldSelStaffIdList,
          };
          httpPost(requestUrl, requestParams, (response) => {
            let toastOpts;
            if (response && response.code === 200) {
              if (this.props.route.params.refresh) {
                this.props.route.params.refresh();
              }
              toastOpts = getSuccessToastOpts('保存完成');
              WToast.show(toastOpts);
              this.props.navigation.goBack();
              // this.props.navigation.navigate("JobStaffMgrList",
              // {
              //     // 传递回调函数
              //     refresh: this.callBackFunction
              // })
            } else {
              toastOpts = getFailToastOpts(response.message);
              WToast.show({data: response.message});
            }
          });
        }}>
        <Image
          style={{width: 30, height: 30}}
          source={require('../../../assets/icon/iconfont/ok1.png')}></Image>
        {/* <Text style={CommonStyle.headRightText}>保存</Text> */}
      </TouchableOpacity>
    );
  }
  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };

  searchByKeyWord = () => {
    let url = '/biz/course/level/user/tenant_staff';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      courseLevelId: this.state.courseLevelId,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  render() {
    return (
      <View style={{backgroundColor: 'rgba(242, 245, 252, 1)'}}>
        <CommonHeadScreen
          title="新增人员"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={{
            marginTop: 0,
            index: 1000,
            backgroundColor: 'rgba(255, 255, 255, 1)',
          }}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginLeft: 10,
              marginRight: 10,
              marginBottom: 5,
              marginTop: 5,
              backgroundColor: 'rgba(255, 255, 255, 1)',
            }}>
            <Text
              style={{
                marginLeft: 10,
                fontWeight: '400',
                fontSize: 16,
                color: '#2B333F',
                marginLeft: 10,
              }}>
              职级名称：{this.state.courseLevelName}
            </Text>
            {/* <Text style={{ marginLeft: 10, fontWeight: '400',fontSize:16,color:'#2B333F', marginLeft:10, }}>职位个数：{this.state.jobAmount}</Text> */}
          </View>
          <View
            style={[
              CommonStyle.singleSearchBox,
              {
                marginTop: 10,
                height: 42,
                borderBottomColor: '#E8E9EC',
                borderBottomWidth: 1,
              },
            ]}>
            <View style={CommonStyle.searchBoxWithoutOthers}>
              <View>
                <Image
                  style={{width: 16, height: 16, marginLeft: 7}}
                  source={require('../../../assets/icon/iconfont/search.png')}></Image>
              </View>
              <TextInput
                style={{
                  color: 'rgba(rgba(0, 10, 32, 0.45))',
                  fontSize: 14,
                  marginLeft: 5,
                  paddingTop: 0,
                  paddingBottom: 0,
                  paddingRight: 0,
                  paddingLeft: 0,
                  width: '100%',
                }}
                returnKeyType="search"
                returnKeyLabel="搜索"
                onSubmitEditing={(e) => {
                  this.searchByKeyWord();
                }}
                placeholder={'员工姓名或联系电话'}
                onChangeText={(text) => this.setState({searchKeyWord: text})}>
                {this.state.searchKeyWord}
              </TextInput>
            </View>
          </View>
        </View>
        <View
          style={{
            backgroundColor: 'rgba(242, 245, 252, 1)',
            height: ifIphoneXContentViewDynamicHeight(
              this.state.topBlockLayoutHeight,
            ),
          }}>
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            ListEmptyComponent={this.emptyComponent}
            initialNumToRender={15}
            windowSize={15}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
          {/* {this.state.dataSource.map((item, key)=>{
                        return(
                            <TouchableOpacity onPress={()=>{
                                var selStaffIdList = this.state.selStaffIdList;
                                if (item.jobSelectedStaff && item.jobSelectedStaff == "Y") {
                                    item.jobSelectedStaff = "N";
                                    arrayRemoveItem(selStaffIdList, item.userId);
                                }
                                else {
                                    item.jobSelectedStaff = "Y";
                                    selStaffIdList = selStaffIdList.concat(item.userId)
                                }
                                this.setState({
                                    selStaffIdList:selStaffIdList,
                                })
                                WToast.show({data:'点击了' + item.userName});
                                console.log("======selStaffIdList:", selStaffIdList)
                            }}>
                                <View key={item.userId} style={[styles.innerViewStyle,(item.jobSelectedStaff && item.jobSelectedStaff === 'Y') ? {backgroundColor:'rgba(255,0,0,0.4)',borderRadius:20,hight:80}:{} ]}>
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>员工姓名：{item.userName}</Text>
                                    </View>
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>联系电话：{item.userNbr ? item.userNbr : "无"}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                            
                        )
                    })} */}
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
  },

  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
    paddingBottom: 5,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
  },
  searchInputText: {
    width: screenWidth / 2,
    borderColor: '#000000',
    // borderBottomWidth:1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },
  innerViewStyle: {
    borderColor: '#F4F4F4',
    borderBottomWidth: 2,
    paddingBottom: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
  lineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    // marginTop: 5,
    // marginBottom: 2,
    borderBottomWidth: 5,
    // borderColor:'#E8E9EC',
    borderBottomColor: '#E8E9EC',
    // backgroundColor:'rgba(255, 255, 255, 1)'
  },
});
