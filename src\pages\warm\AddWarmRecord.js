import React,{ Component } from 'react';
import {Alert, Confirm ,View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'

import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';

var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var cols = 4;
var cellWH = screenWidth / cols;
var vMargin = (screenWidth - cellWH * cols) / (cols + 1);

class AddWarmRecord extends Component{
    constructor(props){
        super(props);
        this.state = {
            kilnCarDataSource:[],
            selKilnCarId:0,
            warmAreaDataSource:[],
            selKilnCarWarmRecordList:[]
        }
    }
    
    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadKilnCarList();
        // 
        this.loadWarmArea();
    }

    loadKilnCarList=()=>{
        // 完成装窑的窑车【等待烧制，对应状态为R】
        let url= "/biz/kiln/car/list";
        let loadRequest={
            "kilnCarState":"R",
            "currentPage": 1,
            "pageSize": 200,
        };
        httpPost(url, loadRequest, this.callBackLoadEncastageKilnCarList);
    }

    callBackLoadEncastageKilnCarList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                kilnCarDataSource:response.data.dataList,
                selKilnCarId:response.data.dataList[0] ? response.data.dataList[0].kilnCarId : 0
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadWarmArea=()=>{
        // 租户设置的温区
        let url= "/biz/warm/area/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 200,
        };
        httpPost(url, loadRequest, this.callBackLoadWarmArea);
    }

    callBackLoadWarmArea=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                warmAreaDataSource:response.data.dataList
            })

            // 清空数组
            this.state.selKilnCarWarmRecordList.length = 0;
            var kilnCarWarmRecordDTO;
            var selKilnCarWarmRecordDTO;
            for(var index=0; index < response.data.dataList.length; index++) {
                kilnCarWarmRecordDTO = response.data.dataList[index];
                console.log("==========kilnCarWarmRecordDTO:", kilnCarWarmRecordDTO);
                selKilnCarWarmRecordDTO = {
                    "warmAreaId": kilnCarWarmRecordDTO.warmAreaId,
                    "warmAreaName": kilnCarWarmRecordDTO.warmAreaName,
                    "warmValue": 0
                }
                this.state.selKilnCarWarmRecordList.push(selKilnCarWarmRecordDTO);
            }
            console.log("==========selKilnCarWarmRecordList:", this.state.selKilnCarWarmRecordList);

        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }


    // componentDidMount(){
    //     if (kilnCarJsonData.code == 200 && kilnCarJsonData.data && kilnCarJsonData.data.dataList) {
    //         this.setState({
    //             kilnCarDataSource:kilnCarJsonData.data.dataList,
    //             selKilnCarId:kilnCarJsonData.data.dataList[0] ? kilnCarJsonData.data.dataList[0].kilnCarId : 0
    //         })
    //     }

    //     if (warmAreaJsonData.code == 200 && warmAreaJsonData.data && warmAreaJsonData.data.dataList) {
    //         this.setState({
    //             warmAreaDataSource:warmAreaJsonData.data.dataList,
    //         })
    //     }
        
    // }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0',marginBottom:10,marginTop:10}}/>)
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("WarmRecordList")
            }}>
                <Text style={CommonStyle.headRightText}>温度管理</Text>
            </TouchableOpacity>
        )
    }

    // 窑车单项渲染
    renderKilnCarRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selKilnCarId:item.kilnCarId
            }) }}>
                <View key={item.kilnCarId} style={item.kilnCarId===this.state.selKilnCarId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.kilnCarId===this.state.selKilnCarId ? CommonStyle.selectedBlockItemTextStyle : CommonStyle.blockItemTextStyle }>
                        {item.kilnCarName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 温区单项渲染
    renderAreaWarm=(areaWarmItem)=>{
        return(
            <View key={areaWarmItem.warmAreaId} style={styles.warmAreaItemStyle}>
                <View style={styles.warmAreaNameItemStyle}>
                    <Text style={styles.warmAreaNameTextItemStyle}>{areaWarmItem.warmAreaName}</Text>
                </View>
                <View
                style={{backgroundColor:'#EFF0F1', padding:15, borderRadius:4}}>
                    <TextInput 
                        keyboardType='numeric'
                        placeholder={'温度值'}
                        style={{backgroundColor:'#FFF', width:60, height:40, alignItems:'center', alignContent:'center'}}
                        onChangeText={(text) => {
                            const newText = text.replace(/[^\d]+/, '');
                            // 温度记录DTO
                            var selKilnCarWarmRecordDTO;
                            for(var index=0; index<this.state.selKilnCarWarmRecordList.length;index++){
                                selKilnCarWarmRecordDTO = this.state.selKilnCarWarmRecordList[index];
                                if (areaWarmItem.warmAreaId === selKilnCarWarmRecordDTO.warmAreaId) {
                                    selKilnCarWarmRecordDTO.warmValue = parseInt(newText);
                                    this.state.selKilnCarWarmRecordList[index] = selKilnCarWarmRecordDTO;
                                }
                            }
                        }}
                    >
                        {areaWarmItem.warmValue}
                    </TextInput>
                </View>
            </View>
        )
    }

    selectCancel=()=>{
        console.log("点击了取消");
        this.props.navigation.goBack()
        // WToast.show({data:'点击了No'});
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title='新增温度'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}/>
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>窑车</Text>
                    </View>
                    <View>
                        <FlatList 
                            numColumns = {5}
                            data={this.state.kilnCarDataSource}
                            // ItemSeparatorComponent={this.space}
                            renderItem={({item}) => this.renderKilnCarRow(item)}
                            ListEmptyComponent={EmptyRowViewComponent}
                            />
                    </View>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>温度</Text>
                    </View>
                    <View 
                        // style={{alignItems:'center'}}
                    >
                        <FlatList 
                            numColumns = {4}
                            data={this.state.warmAreaDataSource}
                            ItemSeparatorComponent={this.space}
                            ListEmptyComponent={EmptyRowViewComponent}
                            renderItem={({item}) => this.renderAreaWarm(item)}
                            />
                    </View>

                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={()=>{
                            Alert.alert('确认','您确定要取消吗？',[
                                {text:"取消", onPress:()=>{
                                    WToast.show({data:'点击了取消'});
                                    // this在这里可用，传到方法里还有问题
                                    // this.props.navigation.goBack();
                                }},
                                {text:"确定", onPress:()=>{this.props.navigation.goBack()}}
                            ]);
                        }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                            Alert.alert('确认','您确定要提交保存吗？',[
                                {
                                    text:"取消", onPress:()=>{
                                    WToast.show({data:'点击了取消'});
                                    // this在这里可用，传到方法里还有问题
                                    // this.props.navigation.goBack();
                                    }
                                },
                                {
                                    text:"确定", onPress:()=>{
                                        // WToast.show({data:'点击了确定'});
                                        var tempDTO;
                                        for(var i=0;i<this.state.selKilnCarWarmRecordList.length;i++){
                                            tempDTO = this.state.selKilnCarWarmRecordList[i];
                                            // console.log("===tempDTO:", tempDTO);
                                            if (!tempDTO) {
                                                WToast.show({data:'温度对象不合法'});
                                                return;
                                            }
                                            if (tempDTO.warmValue === 0) {
                                                WToast.show({data: tempDTO.warmAreaName + '，温度值不能为0'});
                                                return;
                                            }
                                        }
                                        // console.log("===this.state.selKilnCarWarmRecordList:", this.state.selKilnCarWarmRecordList);
                                        let url= "/biz/warm/record/add";
                                        let requestParams={
                                            "kilnCarId": this.state.selKilnCarId,
                                            "warmEaraRecordJson": JSON.stringify(this.state.selKilnCarWarmRecordList)
                                        };
                                        console.log("=========url:", url)
                                        console.log("=========requestParams:", requestParams)
                                        httpPost(url, requestParams, (response)=>{
                                            let toastOpts;
                                            switch (response.code) {
                                                case 200:
                                                    toastOpts = getSuccessToastOpts('窑车温度记录完成');
                                                    WToast.show(toastOpts)
                                                    // this.props.navigation.navigate("MainPage");
                                                    this.props.navigation.goBack()
                                                    break;
                                                default:
                                                    toastOpts = getFailToastOpts(response.message);
                                                    WToast.show({data:response.message})
                                            }
                                        });
                                    }
                                }
                            ]);
                        }}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                        
                        
                    </View>

                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({

    armAreaViewStyle:{
        backgroundColor:'red',
        flexDirection:'column',
        justifyContent:'center',
        marginLeft:10,
        marginRight:10
    },
    warmAreaItemStyle:{
        width:cellWH,
        marginLeft:vMargin,
        alignItems:'center'
    },
    warmAreaNameItemStyle:{
        padding:10
    },
})
module.exports = AddWarmRecord;