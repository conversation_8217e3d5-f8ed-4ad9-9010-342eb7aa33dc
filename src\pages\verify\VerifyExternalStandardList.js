import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,ScrollView,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class VerifyExternalStandardList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            standardType:"E",
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            moreModal: false,
            deleteModal: false,
            dailyItem: {},
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadVerifyStandardList();
        
    }

    // 回调函数
    callBackFunction=()=>{
        let loadTypeUrl= "/biz/verify/standard/list";
        let loadRequest={
            "standardType": this.state.standardType,
            "currentPage": 1,
            "pageSize": this.state.pageSize
        };
        httpPost(loadTypeUrl, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let loadTypeUrl= "/biz/verify/standard/list";
        let loadRequest={
            "standardType": this.state.standardType,
            "currentPage": 1,
            "pageSize": this.state.pageSize
        };
        httpPost(loadTypeUrl, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadVerifyStandardList();
    }

    loadVerifyStandardList=()=>{
        let loadTypeUrl;
        let loadRequest;
        loadTypeUrl= "/biz/verify/standard/list";
        loadRequest={
            "standardType": this.state.standardType,
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize
        };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadVerifyStandardList);
    }

    callBackLoadVerifyStandardList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            if (dataAll.length > response.data.totalRecord) {
                this.setState({
                    refreshing:false
                })
                console.log("=====数据错误了========" + dataAll.length + "/" + response.data.totalRecord);
                return;
            }
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteVerifyStandard =(standardId)=> {
        console.log("=======delete=standardId", standardId);
        let loadTypeUrl= "/biz/verify/standard/delete";
        let requestParams={'standardId':standardId};
        httpDelete(loadTypeUrl, requestParams, this.deleteVerifyStandardCallBack);
    }

    // 删除操作的回调操作
    deleteVerifyStandardCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.standardId} style={styles.innerViewStyle}>
                {
                    index == 0 ?
                        <View style={{ width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF', borderBottomWidth: 10, borderBottomColor: '#F4F7F9' }}>
                        </View>
                        :
                        <View></View>
                }
                {/* <View style={{ position:'absolute', right: 13, top: 0}}>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                moreModal: true,
                                dailyItem: item
                            })
                        }}>
                            <View style={[{width: 35, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                                <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                            </View>
                        </TouchableOpacity>
                </View> */}
                <View style={CommonStyle.titleViewStyleSpecial}>
                    <Text style={CommonStyle.titleTextStyleSpecial}>{item.orderName}</Text>
                    {/* <Text style={styles.titleTextStyle}>订单名称：{item.orderName}</Text> */}
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>客户名称：{item.customerName}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>砖型：{item.seriesName}-{item.brickTypeName}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>创建时间：{item.gmtCreated}</Text>
                </View>
                <View style={[CommonStyle.titleViewStyle,{flexDirection:'row', flexWrap:'wrap'}]}>
                {/* <View style={[CommonStyle.titleViewStyle,{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.95, justifyContent:'flex-start'}]}> */}
                    {item.spVerifyItemDTOList.map((item, key)=>{
                        return(
                                // <Text style={[CommonStyle.titleTextStyle,{height:30, margin:5, backgroundColor:'#F5F5F5', borderRadius:4, padding:5}]}>{item.itemName}：[{item.itemRefValue}]</Text>
                                <Text style={[CommonStyle.titleTextStyle,{height:28}]}>{item.itemName}：[{item.itemRefValue}]</Text>
                        )
                    })}
                </View>
                
                <View style={[CommonStyle.blockTwoEditDelStyle, {marginRight:15}]}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该条检验标准吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteVerifyStandard(item.standardId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.btnTwoDeleteBtnView]}>
                            <Image  style={CommonStyle.btnTwoDeleteBtnImage} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.btnTwoDeleteBtnText}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            if ("K" == item.orderState || dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                                return;
                            }
                            this.props.navigation.navigate("VerifyExternalStandardAdd", 
                            {
                                // 传递参数
                                standardId:item.standardId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.btnTwoEditBtnView
                        ,("K" === item.orderState || dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""
                        ]}>
                            <Image  style={CommonStyle.btnTwoEditBtnImage} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.btnTwoEditBtnText}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                    
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("VerifyExternalStandardAdd", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            //     {/* <Text style={CommonStyle.headRightText}>新增标准</Text> */}
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row-reverse', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("VerifyExternalStandardAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    });
                }}  >
                    <Image style={{ width:22, height:22, marginVertical: 2}} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }
    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={false} />
        )
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title={'检验标准管理'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}> */}
                        {/* <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>  */}
                        <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            ItemSeparatorComponent={this.space}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                            />
                    {/* </ScrollView> */}
                </View>
                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false,
                                    })
                                    if ("K" == this.state.dailyItem.orderState || dateDiffHours(constants.nowDateTime, this.state.dailyItem.gmtCreated) > constants.editDeleteTimeLimit) {
                                        return;
                                    }
                                    this.props.navigation.navigate("VerifyExternalStandardAdd",
                                        {
                                            // 传递参数
                                            standardId: this.state.dailyItem.standardId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        ]}>
                                        {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => {
                                    console.log("dailyItem=================",this.state.dailyItem)
                                    // if (this.state.dailyItem.dailyState != "0BB" && this.state.dailyItem.auditScore) {
                                    //     WToast.show({ data: '日报已审核不可删除' });
                                    //     return;
                                    // }
                                    // if (this.state.dailyItem.dailyState != "0BB" && dateDiffHours(this.state.currentTime, this.state.dailyItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                    //     WToast.show({ data: '日报已超出删除时限' });
                                    //     return;
                                    // }
                                    // 删除弹窗Modal
                                    this.setState({
                                        moreModal: false,
                                        deleteModal: true
                                    })
                                    
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        ]}>
                                        {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>{'您确定要删除该条检验标准吗？'}</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17,  fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteVerifyStandard(this.state.dailyItem.standardId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        // marginBottom:,
        // borderColor:"#F4F4F4",
        // borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});