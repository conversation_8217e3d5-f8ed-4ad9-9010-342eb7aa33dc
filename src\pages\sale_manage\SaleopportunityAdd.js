import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Modal,
    FlatList,RefreshControl,Image,ScrollView,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

const leftLabWidth = 130;
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class SaleopportunityAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            opportunityId:"",
            opportunityName:"",
            leadId:"",
            selLeadId:"",
            selCustomerName:"",
            customerName:"",
            contactPerson:"",
            contactTel:"",
            jobTitle:"",
            searchKeyWord: null,
            modal: false,
            visitFollowedList:[],
            selectCustomer: [],
            customerDataSource:[],
            _customerDataSource: [],
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 加载拜访结果为“跟进”状态的客户
        this.loadVisitFollowedList();

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { opportunityId } = route.params;
            if (opportunityId) {
                console.log("=============opportunityId" + opportunityId + "");
                this.setState({
                    opportunityId:opportunityId,
                    operate:"编辑"
                })
                loadTypeUrl = "/biz/sale/opportunity/get";
                loadRequest = { 'opportunityId': opportunityId };
                httpPost(loadTypeUrl, loadRequest, this.loadSaleOpportunityCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }

    loadSaleOpportunityCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                opportunityId: response.data.opportunityId,
                opportunityName: response.data.opportunityName,
                contactPerson: response.data.contactPerson,
                jobTitle: response.data.jobTitle,
                contactTel: response.data.contactTel,
                leadId: response.data.leadId,
                selLeadId:response.data.leadId,
                selCustomerName:response.data.customerName,
                customerName:response.data.customerName,
            })
        }
    }

    //拜访结果为“跟进”状态的客户
    loadVisitFollowedList=()=>{
        let url= "/biz/lead/operate/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            "display":"Y",
            "operateType":"V",
            "operateResult":"F",
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this.loadVisitFollowedListCallBack);
    }

    loadVisitFollowedListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataAll = response.data.dataList;
            this.setState({
                visitFollowedList:dataAll,
                customerDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    callBackLoadCustomerDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                customerName: response.data.customerName,
                leadId: response.data.leadId,
                contactPerson: response.data.contactPerson,
                jobTitle:response.data.jobTitle,
                contactTel: response.data.contactTel,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.opportunityId) {
                    return;
                }
                this.setState({
                    selLeadId: item.leadId,
                    selCustomerName: item.customerName,
                    contactPerson: item.contactPerson,
                    jobTitle: item.jobTitle,
                    contactTel:item.contactTel
                })
            }}>
                <View key={item.leadId} style={[item.leadId === this.state.selLeadId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.leadId === this.state.selLeadId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.customerName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    saveSaleOpportunity=()=>{
        console.log("=======saveSaleOpportunity");
        let toastOpts;
        if (!this.state.opportunityName) {
            toastOpts = getFailToastOpts("请输入商机名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selLeadId || this.state.selLeadId === 0) {
            toastOpts = getFailToastOpts("请选择客户");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/sale/opportunity/add";
        if (this.state.opportunityId) {
            console.log("=========Edit===opportunityId", this.state.opportunityId)
            url = "/biz/sale/opportunity/modify";
        }
        let requestParams = {
            opportunityId: this.state.opportunityId,
            opportunityName:this.state.opportunityName,
            leadId: this.state.selLeadId,
            customerName: this.state.customerName,
            contactPerson: this.state.contactPerson,
            contactTel: this.state.contactTel,
            jobTitle: this.state.jobTitle,
            userId: constants.loginUser.userId
        };
        httpPost(url, requestParams, this.saveSaleOpportunityCallBack);
    }

    saveSaleOpportunityCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("SaleopportunityList", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>商机管理</Text>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '商机'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>商机名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入商机名称'}
                            onChangeText={(text) => this.setState({ opportunityName: text })}
                        >
                            {this.state.opportunityName}
                        </TextInput>
                    </View>
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>客户名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openCustomerSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5) }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.customerName ? "请选择客户" : this.state.customerName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>客户名称</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>

                            <View style={[{flexWrap:'wrap'}, this.state.opportunityId? CommonStyle.disableViewStyle : null]}>
                                <TouchableOpacity onPress={() => {
                                    if (this.state.opportunityId) {
                                        return;
                                    }
                                    this.setState({
                                        modal: true,
                                        searchKeyWord: ""
                                    })
        
                                    // if (!this.state.customerDataSource || this.state.customerDataSource.length === 0) {
                                    //     let errorMsg = '暂无客户';
                                    //     Alert.alert('确认', errorMsg, [
                                    //         {
                                    //             text: "确定", onPress: () => {
                                    //                 WToast.show({ data: '点击了确定' });
                                    //             }
                                    //         }
                                    //     ]);
                                    //     return;
                                    // }
                                    // if (this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                                    //     this.setState({
                                    //         _customerDataSource: copyArr(this.state.customerDataSource),
                                    //     })
                                    // }

                                    if (!this.state.selLeadId && this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                                        this.setState({
                                            selLeadId: this.state.customerDataSource[0].leadId,
                                            selCustomerName: this.state.customerDataSource[0].customerName,
                                            contactPerson: this.state.customerDataSource[0].contactPerson,
                                            jobTitle: this.state.customerDataSource[0].jobTitle,
                                            contactTel: this.state.customerDataSource[0].contactTel,
                                        })
                                    }
                                }}>
                                    <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, {height:40, flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                        {/* <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                            选择客户{this.state.selLeadId && this.state.selCustomerName ? ("：" + this.state.selCustomerName) : null}
                                        </Text> */}
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.selLeadId && this.state.selCustomerName ? this.state.selCustomerName : "选择客户"}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <Modal
                                animationType={'slide'}
                                transparent={true}
                                onRequestClose={() => console.log('onRequestClose...')}
                                visible={this.state.modal}>
                                <View style={CommonStyle.fullScreenKeepOut}>
                                    <View style={CommonStyle.modalContentViewStyle}>
                                        <View style={CommonStyle.rowLabView}>
                                        <TextInput
                                            style={[CommonStyle.modalSearchInputText]}
                                            placeholder={'请输入查询关键字'}
                                            onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                        >
                                            {this.state.searchKeyWord}
                                        </TextInput>
                                        <TouchableOpacity onPress={() => {
                                            this.loadVisitFollowedList();
                                        }}>
                                            <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                                <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                            </View>
                                        </TouchableOpacity>
                                        </View>
                                        <ScrollView style={{}}>
                                            <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                                {
                                                    (this.state.customerDataSource && this.state.customerDataSource.length > 0)
                                                        ?
                                                        this.state.customerDataSource.map((item, index) => {
                                                            if (index < 1000) {
                                                                return this.renderRow(item)
                                                            }
                                                        })
                                                        : <EmptyRowViewComponent />
                                                }
                                            </View>
                                        </ScrollView>
                                        <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    modal: false,
                                                })
                                            }}>
                                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                                <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                            </View>
                                            </TouchableOpacity>
                                            <TouchableOpacity onPress={() => {
                                                if (!this.state.selLeadId) {
                                                    let toastOpts = getFailToastOpts("您还没有选择客户");
                                                    WToast.show(toastOpts);
                                                    return;
                                                }
                                                this.setState({
                                                    modal: false,
                                                })
                                            }}>
                                                <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                                    <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                                    <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                                </View>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>
                            </Modal>
                        </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            editable={false}
                            style={styles.inputRightText}
                            placeholder={'请选择客户'}
                            onChangeText={(text) => this.setState({ contactPerson: text })}
                        >
                            {this.state.contactPerson}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>职务</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            editable={false}
                            style={styles.inputRightText}
                            placeholder={'请选择客户'}
                            onChangeText={(text) => this.setState({ jobTitle: text })}
                        >
                            {this.state.jobTitle}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            editable={false}
                            style={styles.inputRightText}
                            placeholder={'请选择客户'}
                            onChangeText={(text) => this.setState({ contactTel: text })}
                        >
                            {this.state.contactTel}
                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveSaleOpportunity.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row', width: 130, height: 40, marginRight: 35, marginTop: 15 }]}>
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    {/* <BottomScrollSelect
                        ref={'SelectCustomer'}
                        callBackCustomerValue={this.callBackCustomerValue.bind(this)}
                    /> */}
                </ScrollView>

            </View>
        )
    }
}
const styles = StyleSheet.create({
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
});