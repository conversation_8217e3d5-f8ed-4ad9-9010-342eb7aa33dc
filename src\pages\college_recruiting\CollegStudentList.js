import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl,TextInput,Image,Clipboard
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class CollegStudentList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            // adjustFactor:"",
            searchKeyWord: "",
            classId:null,
            topBlockLayoutHeight: 0,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { staffId ,classId} = route.params;
            if (staffId) {
                console.log("=============staffId" + this.state.staffId + "");
            }
            if (classId) {
                console.log("=============classId" + classId + "");
                this.setState({
                    classId:classId
                })
                this.loadStudentList(classId);
            }
            else{
                this.loadStudentList();
            }
        }
        else{
            // 获取租户下调节系数
            // this.loadAdjustFactor();
            this.loadStudentList();
        }
    }

    // loadAdjustFactor=()=>{
    //     let url = "/biz/tenant/get";
    //     let loadRequest = {
    //         "operateTenantId":constants.loginUser.tenantId
    //     };
    //     httpPost(url, loadRequest, this.loadAdjustFactorCallBack);
    // }

    // loadAdjustFactorCallBack=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             adjustFactor: response.data.adjustFactor,
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

     // 回调函数
     callBackFunction=()=>{
        let url= "/biz/cr/staff/student/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "classId":this.state.classId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/cr/staff/student/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "classId": this.state.classId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadStudentList();
    }

    loadStudentList=(classId)=>{
        let url= "/biz/cr/staff/student/list";
        let data={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "classId":classId?classId:this.state.classId,
        };
        httpPost(url, data, this.callBackLoadStudentList);
    }

    callBackLoadStudentList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteStaff =(staffId)=> {
        console.log("=======delete=staffId", staffId);
        let url= "/biz/cr/staff/delete";
        let requestParams={'staffId':staffId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除成功"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    staffDisplaySetting = (item, index) => {
        console.log("=======staffDisplaySetting=staffId", item.staffId);
        let requestUrl = "/biz/cr/staff/update_staff_resumeDisplay";
        let requestParams = {
            'staffId': item.staffId,
            'resumeDisplay': item.resumeDisplay === 'Y' ? 'N' : 'Y'
        };
        httpPost(requestUrl, requestParams, (response) => {
            if (response.code == 200) {
                // 更新页面上订单状态
                item.resumeDisplay = (item.resumeDisplay === 'Y' ? 'N' : 'Y');
                WToast.show({ data: (item.resumeDisplay === 'Y' ? '显示' : '隐藏') + "设置完成" });
                let staffDataSource = this.state.dataSource;
                // JS 数组遍历
                staffDataSource.forEach((staffObj) => {
                    if (staffObj.staffId === item.staffId) {
                        staffObj.resumeDisplay = item.resumeDisplay;
                    }
                })
                this.setState({
                    dataSource: staffDataSource,
                })
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }

    renderRow=(staffItem)=>{
        return (
            <View key={staffItem.staffId} style={styles.innerViewStyle}>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>学生姓名：{staffItem.staffName}</Text>
                    {
                        staffItem.resumeDisplay==="Y"?
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, borderRadius:12, backgroundColor:'#FF8C2860', color:'#FFFFFF'}}>显示</Text>
                        :
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, borderRadius:12, backgroundColor:'rgba(173,172,173,0.4)', color:'#FFFFFF'}}>隐藏</Text>
                    }
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>联系电话：{staffItem.staffTel}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>所属班级：{staffItem.className}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>专业绩点：{staffItem.gradePoint/100}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>实践绩点：{staffItem.comprehensiveAbility/100}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>综合绩点：{staffItem.comprehensivePoint/100}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={[CommonStyle.bodyTextStyle,{fontWeight:'bold'}]}>个人荣誉</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>{staffItem.personalHonor ? staffItem.personalHonor : "无"}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={[CommonStyle.bodyTextStyle,{fontWeight:'bold'}]}>学院评价</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>{staffItem.collegeEvaluation ? staffItem.collegeEvaluation : "无"}</Text>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    <TouchableOpacity onPress={() => {
                        let message = '您确定要' + (staffItem.resumeDisplay === 'Y' ? '隐藏' : '显示') + '该简历吗？';
                        Alert.alert('确认', message, [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.staffDisplaySetting(staffItem)
                                }
                            }
                        ]);
                    }}>
                        <View style={[staffItem.resumeDisplay === 'Y' ? CommonStyle.itemBottomDeleteGreyBtnViewStyle : [CommonStyle.itemBottomEditBlueBtnViewStyle, { backgroundColor: "#FF8C28" }] 
                            , { width: 75, flexDirection: "row", marginLeft: 10 }
                        ]}>
                            {/* {
                                staffItem.resumeDisplay === 'Y' ?
                                <Image style={{ width: 25, height: 30, marginRight: 5 }} source={require('../../assets/icon/iconfont/hide.png')}></Image>
                                :
                                <Image style={{ width: 25, height: 30, marginRight: 5 }} source={require('../../assets/icon/iconfont/show.png')}></Image>
                            } */}
                            <Text style={staffItem.resumeDisplay === 'Y' ? 
                            [CommonStyle.itemBottomDeleteBtnTextStyle,{color: 'rgba(145, 147, 152, 1)',fontSize: 14, lineHeight: 20 }] 
                            :
                            [CommonStyle.itemBottomDetailBtnTextStyle,{fontSize: 14, lineHeight: 20 }]}>
                                {staffItem.resumeDisplay === 'Y' ? '设为隐藏' : '设为显示'}
                            </Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{this.props.navigation.navigate("StudentMyInterViewPreview", 
                        {
                            // 传递回调函数
                            staffId: staffItem.staffId,
                            // userPhotoUrl:constants.image_addr + '/' + item.electronicPhotos,
                            refresh: this.callBackFunction,
                            
                        })}}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {height: 28,backgroundColor:'rgba(27, 188, 130, 1)',marginRight:0, width: 75 ,flexDirection:"row"}]}>
                            <Image  style={{width:17, height:17,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                            <Text style={[CommonStyle.itemBottomDetailBtnTextStyle,{fontSize: 14, lineHeight: 20 }]}>简历</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该学生吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteStaff(staffItem.staffId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle,{width:75,flexDirection:"row"}]}>
                            <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>this.props.navigation.navigate("CollegStudentAdd",
                    {
                        staffId:staffItem.staffId,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle,{width:75,marginRight:10,flexDirection:"row"}]}>
                            <Image style={{ width: 17, height: 17, marginRight: 5 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20}}>编辑</Text>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity onPress={()=>{
                        console.log('===loadUserPWD:', staffItem.userId);
                        let loadTypeUrl= "/biz/portal/user/send_user_pwd";
                        let loadRequest={tenantId: constants.loginUser.tenantId, userId:staffItem.userId};
                        httpPost(loadTypeUrl, loadRequest, (response)=>{
                            if (response.code == 200 && response.data) {
                                WToast.show({data:"发送成功"});
                            }
                            else {
                                WToast.show({data:response.message});
                            }
                        });
                    }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { height:28,width:105,backgroundColor:"#5DD421",flexDirection:"row"}]}>
                            <Image style={{width:16, height:16,marginRight:5}} source={require('../../assets/icon/iconfont/sendPwd.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>发送密码</Text>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity onPress={()=>{
                        console.log('===loadUserPWD:', staffItem.userId);
                        let loadTypeUrl= "/biz/portal/user/get_pwd";
                        let loadRequest={tenantId: constants.loginUser.tenantId, userId:staffItem.userId};;
                        httpPost(loadTypeUrl, loadRequest, (response)=>{
                            if (response.code == 200 && response.data) {
                                let userPwd = response.data;
                                Clipboard.setString(userPwd);
                                WToast.show({data:"复制成功"});
                            }
                            else {
                                WToast.show({data:response.message});
                            }
                            this.setState({
                                userPwd: response.data,
                            }) 
                        });
                    }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { height:28,width:105,marginRight:10,backgroundColor:"#FF8C28",flexDirection:"row"}]}>
                            <Image style={{width:16, height:16,marginRight:5}} source={require('../../assets/icon/iconfont/copyPwd.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>复制密码</Text>
                        </View>
                    </TouchableOpacity>
                </View>

                {/* <View style={CommonStyle.itemBottomBtnStyle}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该学生吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteStaff(staffItem.staffId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:80,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>this.props.navigation.navigate("CollegStudentAdd",
                    {
                        staffId:staffItem.staffId,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                    
                </View> */}
            </View>
        )
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("CollegStudentAdd",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/cr/staff/student/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord":this.state.searchKeyWord,
            "classId":this.state.classId,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='学生管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{}}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                this.searchByKeyWord();
                                }}
                                placeholder={'搜索姓名/班级'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                            {/*<TouchableOpacity onPress={() => {
                                this.searchByKeyWord();
                            }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 70,backgroundColor:'#DEB887',borderColor:'#DEB887' }]}>
                                    <Text style={[CommonStyle.itemBottomDeleteBtnTextStyle,{color:'#FFFFFF'}]}>查询</Text>
                                </View>
                        </TouchableOpacity>*/}
                        </View>
                        {/* <View style={{ height: 5, backgroundColor: '#FFFFFF' }}></View> */}
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList 
                        data={this.state.dataSource}
                        ItemSeparatorComponent={this.space}
                        ListEmptyComponent={this.emptyComponent}
                        renderItem={({item}) => this.renderRow(item)}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle:{
        // marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10
    },
    titleTextStyle:{
        fontSize:23
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyRowLeftView:{
        width:screenWidth/2-40, 
        flexDirection:'row'
    },
    bodyRowRightView:{
        flexDirection:'row', 
        alignItems:'flex-start',
        paddingLeft:10,
        marginRight:5, 
        justifyContent:'flex-start',
        alignContent:'flex-start'
    }
})
module.exports = CollegStudentList;