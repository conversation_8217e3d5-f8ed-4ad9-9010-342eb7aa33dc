import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Image,Dimensions,KeyboardAvoidingView} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class SuggestionFeedbackAdd extends Component {
    constructor(){
        super()
        this.state = {
            operate:"",
            feedbackId: "",
            feedbackContent: "",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
    
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { feedbackId } = route.params;
            if (feedbackId) {
                console.log("========Edit==feedbackId:", feedbackId);
                this.setState({
                    feedbackId:feedbackId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/suggest/feedback/get";
                loadRequest={'feedbackId':feedbackId};
                httpPost(loadTypeUrl, loadRequest, this.loadSuggestionFeedbackDataCallBack);
            }
            else{
                this.setState({
                    operate:""
                })
            }
        }
    }
    loadSuggestionFeedbackDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                feedbackId:response.data.feedbackId,
                feedbackContent:response.data.feedbackContent,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("SuggestionFeedbackList")
            }}>
                <Text style={CommonStyle.headRightText}>查看反馈</Text>
            </TouchableOpacity>
        )
    }

    saveSuggestionFeedback =()=> {
        console.log("=======saveSuggestionFeedback");
        let toastOpts;
        if (!this.state.feedbackContent) {
            toastOpts = getFailToastOpts("请输入意见反馈");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/suggest/feedback/add";
        if (this.state.feedbackId) {
            console.log("=========Edit===feedbackId", this.state.feedbackId)
            url= "/biz/suggest/feedback/modify";
        }
        let requestParams={
            feedbackId:this.state.feedbackId,
            feedbackContent: this.state.feedbackContent,
        };
        httpPost(url, requestParams, this.saveSuggestionFeedbackCallBack);
    }
    
    // 保存回调函数
    saveSuggestionFeedbackCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('提交完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '意见反馈'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>意见反馈</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>

                    <View style={[styles.inputRowStyle,{height:255}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:255}]}
                            placeholder={'请输入意见反馈'}
                            onChangeText={(text) => this.setState({feedbackContent:text})}
                        >
                            {this.state.feedbackContent}
                        </TextInput>
                    </View>
                    
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveSuggestionFeedback.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>提交</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    selectViewItem:{
        width:100, justifyContent:'center', alignItems:'center'
    },
    selectTextItem:{
        fontSize:18,
        fontWeight:'bold'
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})