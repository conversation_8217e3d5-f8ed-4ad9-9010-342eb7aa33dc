import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  Linking,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class StorageInList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      initGmtCreated: null,
      gmtCreated: null,
      selectGmtCreated: null,
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 6,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      brickTypeDataSource: [],
      brickTypeName: '',
      brickTypeId: '',
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  initGmtCreated = () => {
    // 当前时间
    var currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth());
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _gmtCreated =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      selectGmtCreated: [
        currentDate.getFullYear(),
        currentDateMonth,
        currentDateDay,
      ],
      gmtCreated: _gmtCreated,
      initGmtCreated: _gmtCreated,
    });
    return _gmtCreated;
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');

    var _gmtCreated = this.initGmtCreated();

    this.loadStorageInList(_gmtCreated);

    // 加载砖型
    var loadTypeUrl = '/biz/brick/series/type/effBrickTreeCatalog';
    var loadRequest = {currentPage: 1, pageSize: 10000};
    httpPost(loadTypeUrl, loadRequest, (response) => {
      if (response.code == 200 && response.data && response.data) {
        this.setState({
          brickTypeDataSource: response.data,
        });
      } else if (response.code == 401) {
        WToast.show({data: response.message});
        this.props.navigation.navigate('LoginView');
      }
    });
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/storage/in/list';
    let loadRequest = {
      gmtCreated: this.state.gmtCreated,
      currentPage: 1,
      pageSize: this.state.pageSize,
      brickTypeId: this.state.brickTypeId ? this.state.brickTypeId : null,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      (this.state.currentPage == 1 ||
        this.state.totalRecord <= this.state.pageSize) &&
      this.state.brickTypeId == null &&
      this.state.gmtCreated === this.state.initGmtCreated
    ) {
      console.log('==========不刷新=====');
      return;
    }
    var _gmtCreated = this.initGmtCreated();
    this.setState({
      gmtCreated: _gmtCreated,
      brickTypeName: '',
      brickTypeId: '',
    });
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/storage/in/list';
    let loadRequest = {
      gmtCreated: _gmtCreated,
      currentPage: 1,
      pageSize: this.state.pageSize,
      brickTypeId: this.state.brickTypeId ? this.state.brickTypeId : null,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========第一页即是最后一页，不加载=====');
      return;
    }
    this.loadStorageInList();
  };

  loadStorageInList = (_gmtCreated) => {
    let url = '/biz/storage/in/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      gmtCreated: _gmtCreated ? _gmtCreated : this.state.gmtCreated,
      brickTypeId: this.state.brickTypeId ? this.state.brickTypeId : null,
    };
    httpPost(url, loadRequest, this.loadStorageInListCallBack);
  };

  loadStorageInListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteStorageIn = (storageInId) => {
    console.log('=======delete=storageInId', storageInId);
    let url = '/biz/storage/in/delete';
    let requestParams = {storageInId: storageInId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.storageInId} style={styles.innerViewStyle}>
        {index == 0 ? (
          <View
            style={{
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#FFFFFF',
              borderBottomWidth: 10,
              borderBottomColor: '#F4F7F9',
            }}></View>
        ) : (
          <View></View>
        )}
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>客户：{item.customerName}</Text>
                </View> */}
        {item.storageInTime ? (
          <View style={styles.titleViewStyle}>
            <Text style={styles.titleTextStyle}>
              入库日期：{item.storageInTime}
            </Text>
            {item.orderId ? (
              <Text
                style={{
                  paddingTop: 3,
                  paddingBottom: 3,
                  paddingLeft: 5,
                  paddingRight: 5,
                  height: 23,
                  borderRadius: 12,
                  backgroundColor: 'rgb(72,201,176)',
                  color: '#FFFFFF',
                }}>
                成品入库
              </Text>
            ) : (
              <Text
                style={{
                  paddingTop: 3,
                  paddingBottom: 3,
                  paddingLeft: 5,
                  paddingRight: 5,
                  height: 23,
                  borderRadius: 12,
                  backgroundColor: 'rgba(255,0,0,0.4)',
                  color: '#FFFFFF',
                }}>
                库存品入库
              </Text>
            )}
          </View>
        ) : (
          <View />
        )}

        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            砖型：
            {item.orderName
              ? item.orderName
              : item.seriesName + '-' + item.brickTypeName}
          </Text>
        </View>
        <View style={[styles.titleViewStyle, item.standardSize ? '' : '']}>
          <Text style={styles.titleTextStyle}>
            尺寸：{item.standardSize ? item.standardSize : '无'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            件数：{item.packageAmount ? item.packageAmount : '无'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>入库数量：{item.inAmount}</Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            实际单重(Kg)：
            {item.actualSingleWeight ? item.actualSingleWeight : '无'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            实际总重(吨)：
            {item.actualTotalWeight
              ? (item.actualTotalWeight / 1000).toFixed(2)
              : '无'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            库区：{item.locationAreaName}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>库位：{item.locationName}</Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            备注：{item.location ? item.location : '无'}
          </Text>
        </View>

        <View style={[CommonStyle.itemBottomBtnStyle, {marginRight: 15}]}>
          <TouchableOpacity
            onPress={() => {
              if (
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
              ) {
                return;
              }
              Alert.alert('确认', '您确定要删除该条入库记录吗？', [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                    // this在这里可用，传到方法里还有问题
                    // this.props.navigation.goBack();
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.deleteStorageIn(item.storageInId);
                  },
                },
              ]);
            }}>
            <View
              style={[
                CommonStyle.itemBottomDeleteBtnViewStyle,
                {width: 80, flexDirection: 'row'},
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
                  ? CommonStyle.disableViewStyle
                  : '',
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 5}}
                source={require('../../assets/icon/iconfont/delete.png')}></Image>
              <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
              ) {
                return;
              }
              if (item.orderId) {
                this.props.navigation.navigate('StorageInAdd', {
                  // 传递参数
                  storageInId: item.storageInId,
                  locationAreaId: item.locationAreaId,
                  // 传递回调函数
                  productionLineId: item.productionLineId,
                  refresh: this.callBackFunction,
                });
              } else {
                this.props.navigation.navigate('InventoryStorageAdd', {
                  // 传递参数
                  storageInId: item.storageInId,
                  locationAreaId: item.locationAreaId,
                  // 传递回调函数
                  refresh: this.callBackFunction,
                });
              }
            }}>
            <View
              style={[
                CommonStyle.itemBottomEditBtnViewStyle,
                {width: 80, flexDirection: 'row'},
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
                  ? CommonStyle.disableViewStyle
                  : '',
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 5}}
                source={require('../../assets/icon/iconfont/edit.png')}></Image>
              <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/back.png')}></Image>
      </TouchableOpacity>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <View style={{flexDirection: 'row'}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('InventoryStorageAdd', {
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Text style={CommonStyle.headRightText}>库存品入库</Text>
        </TouchableOpacity>
        <View style={{width: 10}}></View>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('StorageInAdd', {
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Text style={CommonStyle.headRightText}>成品入库</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // 渲染砖型底部滚动数据
  openBrickTypeSelect() {
    if (
      !this.state.brickTypeDataSource ||
      this.state.brickTypeDataSource.length < 1
    ) {
      WToast.show({data: '请先添加砖型'});
      return;
    }
    this.refs.SelectBrickType.showBrickType(
      this.state.selectBrirck,
      this.state.brickTypeDataSource,
    );
  }

  callBackBrickTypeValue(value) {
    console.log('==========砖型选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectBrirck: value,
    });
    // 取选定的砖型ID
    if (value.length == 2) {
      // 加载砖型
      let loadTypeUrl = '/biz/brick/series/type/getBrickByName';
      let loadRequest = {
        brickTypeName: value[1],
        seriesName: value[0],
      };
      httpPost(loadTypeUrl, loadRequest, this._callBackLoadBrickTypeData);
    } else {
      console.log('======选择砖型返回数据不合法', value);
    }
  }

  _callBackLoadBrickTypeData = (response) => {
    if (response.code == 200 && response.data) {
      this.setState({
        brickTypeName: response.data.brickTypeName,
        brickTypeId: response.data.brickTypeId,
      });
      let url = '/biz/storage/in/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        brickTypeId: response.data.brickTypeId,
        gmtCreated: this.state.gmtCreated,
      };
      httpPost(url, loadRequest, this._loadFreshDataCallBack);
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
      this.setState({
        brickTypeName: '',
        brickTypeId: '',
      });
    }
  };

  openGmtCreated() {
    this.refs.SelectGmtCreated.showDate(this.state.selectGmtCreated);
  }
  callBackSelectGmtCreatedValue(value) {
    console.log('==========时间选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectGmtCreated: value,
    });
    if (this.state.selectGmtCreated && this.state.selectGmtCreated.length) {
      var _gmtCreated = '';
      var vartime;
      for (var index = 0; index < this.state.selectGmtCreated.length; index++) {
        vartime = this.state.selectGmtCreated[index];
        if (index === 0) {
          _gmtCreated += vartime;
        } else if (index < 3) {
          _gmtCreated += '-' + vartime;
        } else if (index === 3) {
          _gmtCreated += ' ' + vartime;
        } else {
          _gmtCreated += ':' + vartime;
        }
      }
      this.setState({
        currentPage: 1,
        gmtCreated: _gmtCreated,
      });

      let url = '/biz/storage/in/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        brickTypeId: this.state.brickTypeId ? this.state.brickTypeId : null,
        gmtCreated: _gmtCreated,
      };
      httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
  }

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/storage_in';
    let requestParams = {
      currentPage: 1,
      pageSize: 1000,
      brickTypeId: this.state.brickTypeId ? this.state.brickTypeId : null,
      gmtCreated: this.state.gmtCreated,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      } else if (response.code == 400) {
        WToast.show({data: '所选日期当月没有入库数据！'});
      }
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="入库管理"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />

        <View style={[CommonStyle.rightAbsoluteButtonContainer]}>
          <View style={[CommonStyle.rightAbsoluteButtonView]}>
            <TouchableOpacity onPress={() => this.openGmtCreated()}>
              <Text style={CommonStyle.rightAbsoluteButtonTextView}>
                {!this.state.gmtCreated ? '时间' : this.state.gmtCreated}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={[CommonStyle.rightAbsoluteButtonView]}>
            <TouchableOpacity onPress={() => this.openBrickTypeSelect()}>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                style={[CommonStyle.rightAbsoluteButtonTextView]}>
                {!this.state.brickTypeName ? '砖型' : this.state.brickTypeName}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={[CommonStyle.rightAbsoluteButtonView, {width: 90}]}>
            <TouchableOpacity
              onPress={() => {
                Alert.alert('确认', '您确定要导出PDF文件吗？', [
                  {
                    text: '取消',
                    onPress: () => {
                      WToast.show({data: '点击了取消'});
                    },
                  },
                  {
                    text: '确定',
                    onPress: () => {
                      WToast.show({data: '点击了确定'});
                      this.exportPdfFile();
                    },
                  },
                ]);
              }}>
              <View style={[CommonStyle.rightAbsoluteButtonBoxView]}>
                <Image
                  style={[CommonStyle.rightAbsoluteButtonIconView]}
                  source={require('../../assets/icon/iconfont/output.png')}></Image>
                <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>
                  导出
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <View style={CommonStyle.contentViewStyle}>
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            ListEmptyComponent={this.emptyComponent}
            keyExtractor={(item) => item.storageInId}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
        <BottomScrollSelect
          ref={'SelectBrickType'}
          callBackBrickTypeValue={this.callBackBrickTypeValue.bind(this)}
        />
        <BottomScrollSelect
          ref={'SelectGmtCreated'}
          callBackDateValue={this.callBackSelectGmtCreatedValue.bind(this)}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    // marginTop: 10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
  newrightTop50FloatingBlockView: {
    zIndex: 100,
    position: 'absolute',
    right: 15,
    backgroundColor: '#F2C16D',
    width: 80,
    height: 30,
    opacity: 0.9,
    alignItems: 'center',
    borderRadius: 50,
    justifyContent: 'center',
  },
});
