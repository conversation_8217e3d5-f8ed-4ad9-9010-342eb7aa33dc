import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import EmptyListComponent from '../../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../../utils/ScreenUtil';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;

export default class CourseTypeMgrList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      searchKeyWord: '',
      topBlockLayoutHeight: 0,
      moreModal: false,
      modalItem: {},
      deleteModal: false,
    };
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {tenantId} = route.params;
      if (tenantId) {
        console.log('=============tenantId' + tenantId + '');
      }
      this.loadCourseTypeList();
    }
  }
  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 回调函数
  callBackFunction = () => {
    let url = '/biz/course/type/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };
  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/course/type/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };
  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadCourseTypeList();
  };

  loadCourseTypeList = () => {
    let url = '/biz/course/type/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
    };
    httpPost(url, loadRequest, this.loadCourseTypeListCallBack);
  };
  loadCourseTypeListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      var dataAll = [...dataOld, ...dataNew];
      console.log(response);
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteCourseType = (courseTypeId) => {
    console.log('=======delete=courseTypeId', courseTypeId);
    let url = '/biz/course/type/delete';
    let requestParams = {courseTypeId: courseTypeId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };
  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除成功'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 22, height: 22}}
          source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('CourseTypeMgrAdd', {
            refresh: this.callBackFunction,
          });
        }}>
        <Image
          style={{width: 27, height: 27}}
          source={require('../../../assets/icon/iconfont/add.png')}></Image>
      </TouchableOpacity>
    );
  }
  renderRow = (item, index) => {
    return (
      <View
        key={item.courseTypeId}
        style={{backgroundColor: 'rgba(255, 255, 255, 1)'}}>
        <TouchableOpacity
          style={[
            {
              position: 'absolute',
              right: 13,
              top: 0,
              zIndex: 999,
            },
          ]}
          onPress={() => {
            this.setState({
              moreModal: true,
              modalItem: item,
            });
          }}>
          <View
            style={[
              {
                width: 35,
                height: 35,
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              },
            ]}>
            <Image
              style={{width: 28, height: 28}}
              source={require('../../../assets/icon/iconfont/more.png')}></Image>
          </View>
        </TouchableOpacity>

        <View style={[styles.titleViewStyle]}>
          <Text style={styles.titleTextStyle}>实习类型：</Text>
          <Text style={styles.itemContentStyle}>{item.courseTypeName}</Text>
        </View>
        {/* <View style={[styles.titleViewStyle, { marginTop: 5 }]}>
                    <Text style={styles.titleTextStyle}>类型说明：</Text>
                    <Text style={styles.itemContentStyle}>{item.courseTypeDesc ? item.courseTypeDesc : "无"}</Text>
                </View> */}
        <View style={[styles.titleViewStyle]}>
          <Text style={styles.titleTextStyle}>排序：</Text>
          <Text style={styles.itemContentStyle}>{item.courseTypeSort}</Text>
        </View>
        <View style={styles.lineViewStyle} />
      </View>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  searchByKeyWord = () => {
    let loadUrl = '/biz/course/type/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="实习类型"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.headViewStyle,
            {borderLeftWidth: 0, borderRightWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View style={CommonStyle.singleSearchBox}>
            <View style={CommonStyle.searchBoxWithoutOthers}>
              <Image
                style={{width: 16, height: 16, marginLeft: 7}}
                source={require('../../../assets/icon/iconfont/search.png')}></Image>
              <TextInput
                style={{
                  color: 'rgba(rgba(0, 10, 32, 0.45))',
                  fontSize: 14,
                  marginLeft: 15,
                  paddingTop: 0,
                  paddingBottom: 0,
                  paddingRight: 0,
                  paddingLeft: 0,
                }}
                returnKeyType="search"
                returnKeyLabel="搜索"
                onSubmitEditing={(e) => {
                  this.searchByKeyWord();
                }}
                placeholder={'实习类型'}
                onChangeText={(text) => this.setState({searchKeyWord: text})}>
                {this.state.searchKeyWord}
              </TextInput>
            </View>
          </View>
        </View>
        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.courseTypeId}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
        {/* 更多操作弹窗Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.moreModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      moreModal: false,
                    });
                    this.props.navigation.navigate('CourseTypeMgrAdd', {
                      // 传递参数
                      courseTypeId: this.state.modalItem.courseTypeId,
                      // 传递回调函数
                      refresh: this.callBackFunction,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                    ]}>
                    {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                    <Text
                      style={{
                        color: 'rgba(0, 10, 32, 0.85)',
                        fontSize: 18,
                        lineHeight: 52,
                      }}>
                      编辑
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              <View>
                <TouchableOpacity
                  onPress={() => {
                    if (this.state.modalItem.courseNum) {
                      WToast.show({data: '实习类型已存在关联实习,无法删除'});
                      return;
                    }
                    this.setState({
                      moreModal: false,
                      deleteModal: true,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                      this.state.modalItem.courseNum
                        ? CommonStyle.disableViewStyle
                        : '',
                    ]}>
                    {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                    <Text
                      style={[
                        {
                          color: 'rgba(0, 10, 32, 0.85)',
                          fontSize: 18,
                          lineHeight: 52,
                        },
                      ]}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: 291,
                  height: 50,
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      moreModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 105,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 删除弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.deleteModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 292,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认删除该日报?</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  删除后数据不可恢复，请谨慎操作
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 292,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了确定'});
                    this.deleteCourseType(this.state.modalItem.courseTypeId);
                  }}>
                  <View
                    style={[
                      {
                        width: 146,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  titleViewStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    marginLeft: 12,
    marginRight: 16,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    fontSize: 14,
    color: 'rgba(0, 10, 32, 0.65)',
  },
  lineViewStyle: {
    // height:1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    // marginBottom: 6,
    borderBottomWidth: 1,
    borderColor: '#E8E9EC',
  },
});
