import React, {Component} from 'react';
import {
  <PERSON>ert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  Linking,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class WorkDaily extends Component {
  constructor(props) {
    super(props);
    this.state = {
      operate: '',
      standardType: 'E',
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 5,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,

      showSearchItemBlock: false,

      departmentDataSource: null,

      selDepartmentId: null,
      selDepartmentName: null,
      selDepartmentStaffList: null,
      selStaffId: null,
      selStaffName: null,

      qryStartTime: null,
      selectedQryStartDate: [],
      dailyState: '',
      jobUserDataSource: null,
      isBottomHidden: false,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  initqryStartTime = () => {
    // 当前时间
    var currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 1);
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _qryStartTime =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      selectedQryStartDate: [
        currentDate.getFullYear(),
        currentDateMonth,
        currentDateDay,
      ],
      qryStartTime: _qryStartTime,
    });
    return _qryStartTime;
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    var _qryStartTime = this.initqryStartTime();
    console.log(
      'componentWillMount==这是工作日报的页面_qryStartTime',
      _qryStartTime,
    );

    // 部门
    // let loadTypeUrl= "/biz/department/list_for_tenant";
    // let loadRequest={"qryAll":"Y", "currentPage": 1, "pageSize": 1000};
    // httpPost(loadTypeUrl, loadRequest, (response)=>{
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             departmentDataSource:response.data,
    //         })
    //     }
    // });
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {selStaffId} = route.params;
      if (selStaffId) {
        console.log('=============selStaffId' + selStaffId + '');
        this.setState({
          selStaffId: selStaffId,
          operate: '工作日报',
          isBottomHidden: true,
        });
        this.loadDailyList(_qryStartTime, selStaffId);
      } else {
        this.setState({
          operate: '日报查询',
        });
        this.loadDailyList(_qryStartTime);
      }
    } else {
      this.setState({
        operate: '日报查询',
      });
      this.loadDailyList(_qryStartTime);
    }
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/daily/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      departmentId: this.state.selDepartmentId,
      userId: this.state.selStaffId,
      qryStartTime: this.state.qryStartTime,
      dailyState: '0AA',
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    var _qryStartTime = this.initqryStartTime();
    this.setState({
      qryStartTime: _qryStartTime,
      currentPage: 1,
    });
    let loadTypeUrl = '/biz/daily/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      departmentId: this.state.selDepartmentId,
      userId: this.state.selStaffId,
      qryStartTime: _qryStartTime,
      dailyState: '0AA',
    };
    httpPost(loadTypeUrl, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    if (this.state.refreshing) {
      WToast.show({data: 'loading...'});
      return;
    }
    this.setState({ refreshing: true }, () => {
          console.log('refreshing 已更新:', this.state.refreshing);
          // 在这里执行后续操作
          this.loadDailyList();
    });
  };

  loadDailyList = (_qryStartTime, selStaffId) => {
    let url = '/biz/daily/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      departmentId: this.state.selDepartmentId,
      qryStartTime: _qryStartTime ? _qryStartTime : this.state.qryStartTime,
      userId: selStaffId ? selStaffId : this.state.selStaffId,
      dailyState: '0AA',
    };
    httpPost(url, loadRequest, this.loadDailyListCallBack);
  };

  loadDailyListCallBack = (response) => {
    let toastOpts;
    console.log('++++', response.code);
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      toastOpts = getFailToastOpts(response.message);
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
      this._loadFreshData();
    }
  };

  deleteDaily = (dailyId) => {
    console.log('=======delete=dailyId', dailyId);
    let url = '/biz/daily/delete';
    let requestParams = {dailyId: dailyId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.dailyId} style={[CommonStyle.innerViewStyle]}>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>提交人：{item.userName}</Text>
          {
            item.auditScore !== null || item.auditScore == 0 ? (
              <View
                style={[
                  styles.titleViewStyle,
                  {
                    position: 'absolute',
                    right: 0,
                    top: 0,
                    flexDirection: 'column',
                  },
                ]}>
                <Text style={[styles.titleTextStyle, {color: '#CB4139'}]}>
                  {item.auditScore}
                </Text>
                <Image
                  style={{width: 28, height: 28}}
                  source={require('../../assets/icon/iconfont/scoreLine.png')}></Image>
              </View>
            ) : (
              <Text style={{color: 'rgba(253, 66, 70, 1)'}}>未审核</Text>
            )
            // <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>未审核</Text>
          }
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>提交日期：{item.dailyDate}</Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>完成的工作：</Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>{item.finishedWork}</Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>工作计划：</Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>{item.workPlan}</Text>
        </View>
        {item.unfinishedWork ? (
          <View>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>未完成工作：</Text>
            </View>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>{item.unfinishedWork}</Text>
            </View>
          </View>
        ) : (
          <View></View>
        )}
        {item.requiresCoordinationWork ? (
          <View>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>需协调工作：</Text>
            </View>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>
                {item.requiresCoordinationWork}
              </Text>
            </View>
          </View>
        ) : (
          <View></View>
        )}
        {item.auditScore !== null || item.auditScore == 0 ? (
          <View>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>
                审核得分：{item.auditScore}
              </Text>
            </View>
            {item.auditOpinion !== null && item.auditOpinion !== '无' ? (
              <View style={styles.titleViewStyle}>
                <Text style={styles.titleTextStyle}>
                  审核意见：{item.auditOpinion}
                </Text>
                {/* <Text style={[styles.titleTextStyle,{color:"#CB4139"}]}>审核意见：{item.auditOpinion}</Text> */}
              </View>
            ) : (
              <View></View>
            )}
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>
                审核人：{item.auditOperator}
              </Text>
            </View>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>
                审核时间：{item.auditTime}
              </Text>
            </View>
          </View>
        ) : (
          <View></View>
        )}
        {item.gmtModified == null ? (
          <View style={styles.titleViewStyle}>
            <Text style={styles.titleTextStyle}>
              创建时间：{item.gmtCreated}
            </Text>
          </View>
        ) : (
          <View style={styles.titleViewStyle}>
            <Text style={styles.titleTextStyle}>
              创建时间：{item.gmtModified}
            </Text>
          </View>
        )}
        {this.state.operate == '日报查询' ? (
          <View style={[CommonStyle.itemBottomBtnStyle, {flexWrap: 'wrap'}]}>
            <TouchableOpacity
              onPress={() => {
                this.props.navigation.navigate('DailyMessageList', {
                  // 传递参数
                  messageFkId: item.dailyId,
                  // 传递回调函数
                  refresh: this.callBackFunction,
                });
              }}>
              <View
                style={[
                  CommonStyle.itemBottomDeleteBtnViewStyle,
                  {width: 70, flexDirection: 'row'},
                ]}>
                <Image
                  style={{width: 20, height: 20, marginRight: 5}}
                  source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>
                  {item.messageNum}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        ) : (
          <View />
        )}
      </View>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 部门
  renderDepartmentRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selDepartmentId: item.departmentId,
            selDepartmentName: item.departmentName,
            selDepartmentStaffDataSource: item.departmentUserDTOList,
            selStaffId: null,
            selStaffName: null,
          });
        }}>
        <View
          key={'department_' + item.departmentId}
          style={[
            item.departmentId === this.state.selDepartmentId
              ? CommonStyle.choseToSearchItemsSelectedViewColor
              : CommonStyle.choseToSearchItemsViewColor,
            CommonStyle.choseToSearchItemsViewSize,
          ]}>
          <Text
            style={[
              item.departmentId === this.state.selDepartmentId
                ? CommonStyle.choseToSearchItemsSelectedTextStyle
                : CommonStyle.choseToSearchItemsTextStyle,
            ]}>
            {item.departmentName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderDepartmentStaffRow = (item, index) => {
    return (
      <View key={item.jobUserId}>
        <TouchableOpacity
          onPress={() => {
            this.setState({
              selStaffId: item.userId,
              selStaffName: item.staffName,
            });
          }}>
          <View
            key={'jobuser_' + item.jobUserId}
            style={[
              item.userId === this.state.selStaffId
                ? CommonStyle.choseToSearchItemsSelectedViewColor
                : CommonStyle.choseToSearchItemsViewColor,
              CommonStyle.choseToSearchItemsViewSize,
            ]}>
            <Text
              style={[
                item.userId === this.state.selStaffId
                  ? CommonStyle.choseToSearchItemsSelectedTextStyle
                  : CommonStyle.choseToSearchItemsTextStyle,
              ]}>
              {item.staffName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          Alert.alert('确认', '您确定要导出PDF文件吗？', [
            {
              text: '取消',
              onPress: () => {
                WToast.show({data: '点击了取消'});
              },
            },
            {
              text: '确定',
              onPress: () => {
                WToast.show({data: '点击了确定'});
                this.exportPdfFile();
              },
            },
          ]);
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 23, height: 23}}
          source={require('../../assets/icon/iconfont/outputBlack.png')}></Image>
      </TouchableOpacity>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  // 显示搜索项目
  showSearchItemSelect() {
    if (
      !this.state.departmentDataSource ||
      this.state.departmentDataSource.length < 1
    ) {
      WToast.show({data: '请先添加部门'});
      return;
    }
    this.setState({
      showSearchItemBlock: true,
    });
  }

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/daily_query';

    // if(this.state.selDepartmentId != null  && this.state.selStaffId == null) {

    //     return ;
    // }
    // if(this.state.selStaffId == null) {
    //     WToast.show({data:'请选择提交人'});
    //     return ;
    // }
    let requestParams = {
      // "userId": constants.loginUser.userId,
      qryStartTime: this.state.qryStartTime,
      currentPage: 1,
      pageSize: 1000,
      departmentId: this.state.selDepartmentId,
      userId: this.state.selStaffId,
      dailyState: '0AA',
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  openQryStartDate() {
    this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate);
  }

  callBackSelectQryStartDateValue(value) {
    console.log('==========提交时间选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectedQryStartDate: value,
    });
    if (value && value.length) {
      var qryStartTime = '';
      var vartime;
      for (var index = 0; index < value.length; index++) {
        vartime = value[index];
        if (index === 0) {
          qryStartTime += vartime;
        } else {
          qryStartTime += '-' + vartime;
        }
      }
      this.setState({
        qryStartTime: qryStartTime,
      });

      let loadUrl = '/biz/daily/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        departmentId: this.state.selDepartmentId,
        userId: this.state.selStaffId,
        qryStartTime: qryStartTime,
        dailyState: '0AA',
      };
      httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }
  }

  render() {
    return (
      <View
        style={{
          height: this.state.isBottomHidden ? screenHeight : null,
          backgroundColor: '#ffffff',
        }}>
        <CommonHeadScreen
          title={this.state.operate}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.rightTop50FloatingBlockView,
            {
              height: 32,
              width: 110,
              opacity: 0.6,
              borderRadius: 8,
              backgroundColor: 'rgba(242, 245, 252, 1)',
            },
          ]}>
          <TouchableOpacity onPress={() => this.openQryStartDate()}>
            <Text style={{color: 'rgba(0,10,32,0.85)', fontSize: 14}}>
              {!this.state.qryStartTime ? '时间' : this.state.qryStartTime}
            </Text>
          </TouchableOpacity>
        </View>

        {this.state.operate == '日报查询' ? (
          <View
            style={[CommonStyle.headViewStyle]}
            onLayout={this.topBlockLayout.bind(this)}>
            <View
              style={{width: '100%', flexWrap: 'wrap', flexDirection: 'row'}}>
              <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                {this.state.showSearchItemBlock ? (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                      {this.state.selDepartmentId &&
                      this.state.selDepartmentName
                        ? this.state.selDepartmentName
                        : '选择部门'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchClosedIconSize]}
                      source={require('../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                  </View>
                ) : (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                      {this.state.selDepartmentId &&
                      this.state.selDepartmentName
                        ? this.state.selDepartmentName
                        : '选择部门'}
                    </Text>
                    <Image
                      style={[CommonStyle.choseToSearchOpenedIconSize]}
                      source={require('../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                  </View>
                )}
              </TouchableOpacity>
              {this.state.selStaffId && this.state.selStaffName ? (
                <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                  {this.state.showSearchItemBlock ? (
                    <View style={[CommonStyle.choseToSearchViewStyle]}>
                      <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                        {this.state.selStaffName}
                      </Text>
                    </View>
                  ) : (
                    <View style={[CommonStyle.choseToSearchViewStyle]}>
                      <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                        {this.state.selStaffName}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              ) : null}
            </View>
          </View>
        ) : (
          <View />
        )}
        <View>
          {this.state.showSearchItemBlock ? (
            <View
              style={[
                CommonStyle.choseToSearchBigBoxViewStyle,
                {
                  height: ifIphoneXContentViewDynamicHeight(
                    this.state.topBlockLayoutHeight,
                  ),
                },
              ]}>
              <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                <View
                  style={[
                    {backgroundColor: 'rgba(255,255,255,1)'},
                    CommonStyle.choseToSearchItemsViewSize,
                  ]}>
                  <Text style={{fontSize: 16, fontWeight: 'bold'}}>部门：</Text>
                </View>
                {this.state.departmentDataSource &&
                this.state.departmentDataSource.length > 0
                  ? this.state.departmentDataSource.map((item, index) => {
                      return this.renderDepartmentRow(item);
                    })
                  : null}
              </View>
              {this.state.selDepartmentStaffDataSource &&
              this.state.selDepartmentStaffDataSource.length > 0 ? (
                <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                  <View
                    style={[
                      {backgroundColor: 'rgba(255,255,255,1)'},
                      CommonStyle.choseToSearchItemsViewSize,
                    ]}>
                    <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                      提交人：
                    </Text>
                  </View>
                  {this.state.selDepartmentStaffDataSource.map(
                    (item, index) => {
                      return this.renderDepartmentStaffRow(item);
                    },
                  )}
                </View>
              ) : null}
              <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]}>
                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    let loadUrl = '/biz/daily/list';
                    let loadRequest = {
                      currentPage: 1,
                      pageSize: this.state.pageSize,
                      departmentId: this.state.selDepartmentId,
                      userId: this.state.selStaffId,
                      qryStartTime: this.state.qryStartTime,
                      dailyState: '0AA',
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>
                      确定搜索
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}
          <View
            style={[
              CommonStyle.contentViewStyle,
              {
                height: ifIphoneXContentViewDynamicHeight(
                  this.state.topBlockLayoutHeight,
                ),
              },
            ]}>
            <FlatList
              data={this.state.dataSource}
              renderItem={({item, index}) => this.renderRow(item, index)}
              ListEmptyComponent={this.emptyComponent}
              // 自定义下拉刷新
              refreshControl={
                <RefreshControl
                  tintColor="#FF0000"
                  title="loading"
                  colors={['#FF0000', '#00FF00', '#0000FF']}
                  progressBackgroundColor="#FFFF00"
                  refreshing={this.state.refreshing}
                  onRefresh={() => {
                    this._loadFreshData();
                  }}
                />
              }
              // 底部加载
              ListFooterComponent={() => this.flatListFooterComponent()}
              onEndReached={() => this._loadNextData()}
            />
          </View>
        </View>
        <BottomScrollSelect
          ref={'SelectQryStartDate'}
          callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    // marginTop:10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
});
