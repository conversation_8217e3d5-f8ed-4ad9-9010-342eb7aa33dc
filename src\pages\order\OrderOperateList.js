import React, {Component} from 'react';
import {
  Alert,
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';

// 公共组件及样式
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';

// 引入公共样式
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

class OrderOperateList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      searchKeyWord: '',
      topBlockLayoutHeight: 0,
      moreModal: false,
      hideModal: false,
      shutModal: false,
      dailyItem: {},
    };
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    this.loadOrderList();
  }

  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadOrderList();
  };

  loadOrderList = () => {
    let url = '/biz/order/list';
    let data = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      excludeOrderStateList: ['A'],
    };
    httpPost(url, data, this.callBackLoadOrder);
  };

  callBackLoadOrder = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/order/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      excludeOrderStateList: ['A'],
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  componentDidMount() {
    console.log('componentDidMount');
  }

  UNSAFE_componentWillReceiveProps() {
    console.log('componentWillReceiveProps');
  }

  shouldComponentUpdate(nextProps, nextState) {
    console.log('shouldComponentUpdate');
    console.log('nextProps->', nextProps);
    console.log('nextState->', nextState);
    return true;
  }

  UNSAFE_componentWillUpdate() {
    console.log('componentWillUpdate');
  }

  componentDidUpdate() {
    console.log('componentDidUpdate');
  }

  componentWillUnmount() {
    console.log('componentWillUnmount');
  }

  orderDisplaySetting = (orderItem, index) => {
    console.log('=======orderDisplaySetting=orderId', orderItem.orderId);
    let requestUrl = '/biz/order/update_order_display';
    let requestParams = {
      orderId: orderItem.orderId,
      display: orderItem.display === 'Y' ? 'N' : 'Y',
    };
    httpPost(requestUrl, requestParams, (response) => {
      if (response.code == 200) {
        // 更新页面上订单状态
        orderItem.display = orderItem.display === 'Y' ? 'N' : 'Y';
        WToast.show({
          data: (orderItem.display === 'Y' ? '显示' : '隐藏') + '设置完成',
        });
        let orderDataSource = this.state.dataSource;
        // JS 数组遍历
        orderDataSource.forEach((orderObj) => {
          if (orderObj.orderId === orderItem.orderId) {
            orderObj.display = orderItem.display;
          }
        });
        this.setState({
          dataSource: orderDataSource,
        });
      } else {
        WToast.show({data: response.message});
      }
    });
  };

  finishOrder = (orderItem) => {
    console.log('=======finishOrder=orderId', orderItem.orderId);
    let requestUrl = '/biz/order/finish_order';
    let requestParams = {
      orderId: orderItem.orderId,
      orderState: orderItem.orderState === 'K' ? 'J' : 'K',
    };
    httpPost(requestUrl, requestParams, (response) => {
      if (response.code == 200) {
        // 更新页面上订单状态
        orderItem.orderState = orderItem.orderState === 'K' ? 'J' : 'K';
        WToast.show({
          data: (orderItem.orderState === 'K' ? '关闭' : '重启') + '订单完成',
        });
        let orderDataSource = this.state.dataSource;
        // JS 数组遍历
        orderDataSource.forEach((orderObj) => {
          if (orderObj.orderId === orderItem.orderId) {
            orderObj.orderState = orderItem.orderState;
            if (orderObj.orderState === 'K') {
              orderObj.orderStateName = '订单关闭';
            }
            if (orderObj.orderState === 'J') {
              orderObj.orderStateName = '开始装车发运';
            }
          }
        });
        this.setState({
          dataSource: orderDataSource,
        });
      } else {
        WToast.show({data: response.message});
      }
    });
  };

  renderRow = (orderItem, index) => {
    return (
      <View key={orderItem.orderId} style={styles.innerViewStyle}>
        {index == 0 ? (
          <View style={CommonStyle.lineListHeadRenderRowStyle}></View>
        ) : (
          <View></View>
        )}
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{orderItem.orderName}</Text>
                </View> */}
        {/* <View style={{ position:'absolute', right: 13, top: 0}}>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                moreModal: true,
                                dailyItem: orderItem
                            })
                        }}>
                            <View style={[{width: 35, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                                <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                            </View>
                        </TouchableOpacity>
                </View> */}
        <View style={CommonStyle.titleViewStyleSpecial}>
          <Text style={CommonStyle.titleTextStyleSpecial}>
            {orderItem.orderName}
          </Text>
          {/* <Text style={CommonStyle.bodyTextStyle}>订单名称：{orderItem.orderName}</Text> */}
        </View>
        {/* <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>所属合同：{orderItem.contractName}</Text>
                </View> */}
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            客户名称：{orderItem.customerName}
          </Text>
        </View>

        {/* <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>订单号：{orderItem.orderCode}</Text>
                </View> */}

        {/* <View style={styles.bodyViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={CommonStyle.bodyTextStyle}>合同：{orderItem.contractName}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={CommonStyle.bodyTextStyle}>订单时间：{orderItem.gmtCreated}</Text>
                    </View>
                </View> */}
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            订单时间：{orderItem.gmtCreated}
          </Text>
        </View>

        {/* <View style={styles.bodyViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={CommonStyle.bodyTextStyle}>砖型：{orderItem.brickTypeName}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={CommonStyle.bodyTextStyle}>数量：{orderItem.brickAmount}</Text>
                    </View>
                </View> */}

        {/* <View style={styles.bodyViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={CommonStyle.bodyTextStyle}>联系人：{orderItem.orderContact}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={CommonStyle.bodyTextStyle}>联系电话：{orderItem.orderContactTel}</Text>
                    </View>
                </View> */}

        {/* <View style={styles.bodyViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={CommonStyle.bodyTextStyle}>订单金额：{orderItem.contractAmountUnit}{orderItem.contractAmont}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={CommonStyle.bodyTextStyle}>订单状态：{orderItem.orderStateName}</Text>
                    </View>
                </View> */}
        {/* <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>是否显示：{orderItem.display === 'Y' ? "显示" : "不显示"}</Text>
                </View> */}
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            订单状态：{orderItem.orderStateName}
          </Text>
        </View>
        <View style={CommonStyle.itemBottomBtnStyle}>
          <TouchableOpacity
            onPress={() => {
              let message =
                '您确定要' +
                (orderItem.display === 'Y' ? '隐藏' : '显示') +
                '该订单吗？';
              Alert.alert('确认', message, [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.orderDisplaySetting(orderItem);
                  },
                },
              ]);
            }}>
            <View
              style={[
                orderItem.display === 'Y'
                  ? CommonStyle.itemBottomDeleteBtnViewStyle
                  : [
                      CommonStyle.itemBottomDetailBtnViewStyle,
                      {backgroundColor: '#FFB800'},
                    ],
                {width: 80, flexDirection: 'row', marginLeft: 0},
              ]}>
              {orderItem.display === 'Y' ? (
                <Image
                  style={{width: 25, height: 30, marginRight: 5}}
                  source={require('../../assets/icon/iconfont/hide.png')}></Image>
              ) : (
                <Image
                  style={{width: 25, height: 30, marginRight: 5}}
                  source={require('../../assets/icon/iconfont/show.png')}></Image>
              )}
              <Text
                style={
                  orderItem.display === 'Y'
                    ? CommonStyle.itemBottomDeleteBtnTextStyle
                    : CommonStyle.itemBottomDetailBtnTextStyle
                }>
                {orderItem.display === 'Y' ? '隐藏' : '显示'}
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              if (orderItem.orderState === 'K') {
                WToast.show({data: '请联系系统业务支撑人员重启订单'});
                return;
              }
              let message =
                '您确定要' +
                (orderItem.orderState === 'K' ? '重启' : '关闭') +
                '该订单吗？';
              Alert.alert('确认', message, [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.finishOrder(orderItem);
                  },
                },
              ]);
            }}>
            <View
              style={[
                orderItem.orderState === 'K'
                  ? CommonStyle.itemBottomDeleteBtnViewStyle
                  : CommonStyle.itemBottomEditBtnViewStyle,
                {width: 80, flexDirection: 'row', marginLeft: 0},
              ]}>
              {orderItem.orderState === 'K' ? (
                <Image
                  style={{width: 18, height: 18, marginRight: 5}}
                  source={require('../../assets/icon/iconfont/restartBlack.png')}></Image>
              ) : (
                <Image
                  style={{width: 20, height: 20, marginRight: 5}}
                  source={require('../../assets/icon/iconfont/close.png')}></Image>
              )}
              <Text
                style={
                  orderItem.orderState === 'K'
                    ? CommonStyle.itemBottomDeleteBtnTextStyle
                    : CommonStyle.itemBottomEditBtnTextStyle
                }>
                {orderItem.orderState === 'K' ? '重启' : '关闭'}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  // 分隔线
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      // </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[{flexDirection: 'row', alignItems: 'center'}]}>
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={{color: '#3C6CDE', fontWeight: 'bold'}}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity onPress={() => {}}>
          {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
          <Text style={{color: '#FFFFFF'}}>订单状态</Text>
          {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
        </TouchableOpacity>
      </View>
    );
  }

  searchByKeyWord = () => {
    let toastOpts;
    if (!this.state.searchKeyWord) {
      toastOpts = getFailToastOpts('请输入客户、合同或订单名称');
      WToast.show(toastOpts);
      return;
    }
    let loadUrl = '/biz/order/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      excludeOrderStateList: ['A'],
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };
  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="订单状态"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />

        <View
          style={[
            CommonStyle.headViewStyle,
            {borderLeftWidth: 0, borderRightWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View style={CommonStyle.singleSearchBox}>
            <View style={CommonStyle.searchBoxWithoutOthers}>
              {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
              <Image
                style={{width: 16, height: 16, marginLeft: 7}}
                source={require('../../assets/icon/iconfont/search.png')}></Image>
              <TextInput
                style={{
                  color: 'rgba(rgba(0, 10, 32, 0.45))',
                  fontSize: 14,
                  marginLeft: 5,
                  paddingTop: 0,
                  paddingBottom: 0,
                  paddingRight: 0,
                  paddingLeft: 0,
                  width: '100%',
                }}
                returnKeyType="search"
                returnKeyLabel="搜索"
                onSubmitEditing={(e) => {
                  this.searchByKeyWord();
                }}
                placeholder={'客户/合同/订单'}
                onChangeText={(text) => this.setState({searchKeyWord: text})}>
                {this.state.searchKeyWord}
              </TextInput>
            </View>
          </View>
        </View>

        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>  */}
          <FlatList
            data={this.state.dataSource}
            ItemSeparatorComponent={this.space}
            ListEmptyComponent={this.emptyComponent}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.orderId}
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
          {/* </ScrollView> */}
        </View>
        {/* 更多操作弹窗Modal */}
        {/* <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {

                                    this.setState({
                                        moreModal: false,
                                        hideModal: true,
                                    })
                                }}>
                                    <View style={[this.state.dailyItem.display === 'Y' ? {width: 145, height: 50, paddingLeft: 30, marginTop: 5} : {width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , { width: 80, flexDirection: "row", marginLeft: 0 }
                                    ]}>

                                        <Text style={this.state.dailyItem.display === 'Y' ? { color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 } :{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>
                                            {this.state.dailyItem.display === 'Y' ? '隐藏' : '显示'}
                                        </Text>
                                    </View>

                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => { 

                                    if (this.state.dailyItem.orderState === "K") {
                                        WToast.show({ data: '请联系系统业务支撑人员重启订单' });
                                        return;
                                    } 
                                    // 弹窗Modal
                                    this.setState({
                                        moreModal: false,
                                        shutModal: true,
                                        // deleteModal: true
                                    });   
                                }}>

                                    <View style={[this.state.dailyItem.orderState === 'K' ? CommonStyle.itemBottomDeleteBtnViewStyle : {width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , { width: 80, flexDirection: "row", marginLeft: 0 }]}>
                                        <Text style={this.state.dailyItem.orderState === 'K' ? CommonStyle.itemBottomDeleteBtnTextStyle : { color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>
                                            {this.state.dailyItem.orderState === 'K' ? '重启' : '关闭'}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal> */}
        {/* 隐藏弹窗 */}
        {/* <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.hideModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                            <Text style={ {fontSize: 18} }>
                                            {this.state.dailyItem.display === 'Y' ? '您确定要隐藏该订单吗？' : '您确定要显示该订单吗？'}
                            </Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>{this.state.dailyItem.display === 'Y' ? '隐藏可恢复显示' : '显示可重新隐藏'}</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        hideModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17,  fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        hideModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.orderDisplaySetting(this.state.dailyItem)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA'}}>确定</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal> */}
        {/* 关闭弹窗 */}
        {/* <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.shutModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                            <Text style={ {fontSize: 18} }>
                                            {this.state.dailyItem.orderState === 'K' ? '您确定要重启该订单吗？' : '您确定要关闭该订单吗？'}
                            </Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>{this.state.dailyItem.display === 'Y' ? '隐藏可恢复显示' : '关闭后需联系管理员重启'}</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        shutModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17,  fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        shutModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.finishOrder(this.state.dailyItem)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA'}}>确定</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal> */}
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 140,
  //     backgroundColor:'#FFF'
  // },
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
  },

  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
    paddingLeft: 0,
  },
  searchInputText: {
    width: screenWidth - 100,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    marginTop: 5,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },
  innerViewStyle: {
    marginTop: 10,
    // borderColor:"#F4F4F4",
    // borderWidth:8,
    marginBottom: 10,
  },
  innerViewStyleSearch: {
    // marginTop: 10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
  },
  titleTextStyle: {
    fontSize: 23,
  },
  bodyViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginLeft: 10,
    marginRight: 10,
    marginBottom: 8,
    marginTop: 8,
  },
  bodyRowLeftView: {
    width: screenWidth / 2 - 40,
    flexDirection: 'row',
  },
  bodyRowRightView: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingLeft: 10,
    marginRight: 5,
    justifyContent: 'flex-start',
    alignContent: 'flex-start',
  },
});
module.exports = OrderOperateList;
