import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Linking,
    FlatList,RefreshControl,ScrollView,ImageBackground,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import NaicaiGuidBugHeadScreen from '../../../component/NaicaiGuidBugHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import { color } from 'react-native-reanimated';
var CommonStyle = require('../../../assets/css/CommonStyle');
const { fullScreenIfIphoneXContentViewHeight, ifIphoneXContentViewHeight, ifIphoneXBodyViewHeight, isIphoneX, ifIphoneXHeaderHeight } = require('../../../utils/ScreenUtil');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class ProductDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,

            auditMarking:'',
            releaseId:'',
            compressFile:'',
            productPhotoUrl:'',
            contactPerson:'',
            contactTel:'',
            currentAuditUserId:'',
            enterpriseName:'',
            expDate:'',
            gmtCreated:'',
            productExplain:'',
            productMaterial:'',
            productModel:'',
            productName:'',
            productPrice:'',
            productState:'',
            productStateTime:'',
            productType:'',
            productWeight:'',
            productionAddr:'',
            releaseDate:'',
            releaseId:'',
            releaseType:'',
            salePersonName:'',
            salePersonTel:'',
            spotFlag:'',
            tenantAbbreviation:'',
            updateProductState:'',
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { releaseId , salePersonTel } = route.params;
            if (releaseId) {
                console.log("=============releaseId" + releaseId + "");
                this.setState({
                    releaseId:releaseId,
                    salePersonTel:salePersonTel
                })
                loadTypeUrl = "/biz/product/release/get";
                loadRequest = { 'releaseId': releaseId };
                httpPost(loadTypeUrl, loadRequest, this.loadProductDataCallBack);
            }
            else {
                console.log("=============releaseId ： 空");
            }
        }
    }

    loadProductDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            console.log('response.data=========================', response.data)
            this.setState({
                auditMarking:response.data.auditMarking,
                compressFile:response.data.compressFileList[0].compressFile,
                // productPhotoUrl:constants.image_addr + '/' + response.data.compressFile,
                contactPerson:response.data.contactPerson,
                contactTel:response.data.contactTel,
                currentAuditUserId:response.data.currentAuditUserId,
                enterpriseName:response.data.enterpriseName,
                expDate:response.data.expDate,
                gmtCreated:response.data.gmtCreated,
                productExplain:response.data.productExplain,
                productMaterial:response.data.productMaterial,
                productModel:response.data.productModel,
                productName:response.data.productName,
                productPrice:response.data.productPrice,
                productState:response.data.productState,
                productStateTime:response.data.productStateTime,
                productType:response.data.productType,
                productWeight:response.data.productWeight,
                productionAddr:response.data.productionAddr,
                releaseDate:response.data.releaseDate,
                releaseId:response.data.releaseId,
                releaseType:response.data.releaseType,
                salePersonName:response.data.salePersonName,
                // salePersonTel:response.data.salePersonTel,
                spotFlag:response.data.spotFlag,
                tenantAbbreviation:response.data.tenantAbbreviation,
                updateProductState:response.data.updateProductState,
            })

        }
    }

    myIsNaN=(value)=> {    
        return parseFloat(value).toString() != "NaN";
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                <View style={{flexDirection: 'row',width:32}}>
                    <Image  style={{width:22, height:22,transform:[{ rotate: '-90deg' }]}} source={require('../../../assets/icon/iconfont/arrow-up.png')}></Image>
                    <Text style={[CommonStyle.naicaiIndexHeadLeftText,{color:'#255BDA'}]}>返回</Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                constants.loginUser != null?
                this.props.navigation.navigate("MyProductSaleReleaseAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
                :
                this.login();
            }}>
                <Text style={[CommonStyle.headRightText,{color:'#255BDA',fontSize:16}]}>退出</Text>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <NaicaiGuidBugHeadScreen title='耐材市场'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                {/* <View style={CommonStyle.fullScreenContentViewStyle}>
                    <Text>
                    接收的参数值：{this.state.releaseId}
                    </Text>
                </View> */}
                
                <ScrollView style={[CommonStyle.formContentViewStyle]} >
                    
                        <View  style={{  width: screenWidth,}}>
                            <ImageBackground source={{ uri: (constants.image_addr + '/' + this.state.compressFile) }} style={{ height: screenHeight*0.34, width: screenWidth,}} />
                            <View style={{
                                    justifyContent: 'space-around', flexWrap: 'wrap', width: screenWidth-32,
                                    backgroundColor: "#FFFFff", marginTop: -34, marginLeft: 16, marginRight: 16, borderRadius: 12 , borderColor:'rgba(0, 0, 0, 0.1)' , borderWidth:2,
                                }}>
                                <View style={{marginTop: 20, marginLeft:20}}>
                                    <View style={{flexDirection:'row',alignItems:'center'}}>
                                    {
                                        this.state.auditMarking == '1' ?
                                        <View  style={{backgroundColor:'#FA653A',width: 70, height: 20,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                            {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                            <Text style={{fontSize: 13,color:"#ffffff"}}>精选推荐</Text>
                                        </View>
                                        :
                                        (
                                            this.state.auditMarking == '2' ?
                                            <View  style={{backgroundColor:'#FA653A',width: 38, height: 20,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                            {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                            <Text style={{fontSize: 13,color:"#ffffff"}}>自营</Text>
                                        </View>
                                            :
                                            null
                                        )
                                    }
                                        <View style={{marginLeft:2}}>
                                            <Text style={{color: '#111111',fontSize: 20}}>{this.state.productName}</Text>
                                        </View>
                                    </View>
                                    <View style={{flexDirection: 'row',marginTop:10,width:screenWidth-50}}>
                                    {
                                        this.state.spotFlag == 'Y' ?
                                        (
                                            this.state.auditMarking == '1' || this.state.auditMarking == '2'?
                                            <View  style={{backgroundColor:'#ECEEF2',width: 38, height: 30,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                                {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                                <Text style={{color:"#846020"}}>现货</Text>
                                            </View>
                                            :
                                            <View  style={{backgroundColor:'#ECEEF2',width: 38, height: 30,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                                {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                                <Text style={{color:"#846020"}}>现货</Text>
                                            </View>
                                        )
                                        :
                                        null
                                    }
                                    {
                                        this.state.productPrice ?
                                        <View style={{flexDirection:'row',height:30,backgroundColor:'#ffffff',position:'absolute',right:16}}>
                                            <Text style={styles.redTextStyle}>￥
                                                <Text style={styles.redTextStyle}>{this.state.productPrice}</Text>
                                                {
                                                    this.myIsNaN(this.state.productPrice) ?
                                                    <Text style={styles.redTextStyle}> 元/吨</Text>
                                                    :
                                                    null
                                                }
                                            </Text>
                                        </View> :
                                        <View style={{flexDirection:'row',marginBottom:5,backgroundColor:'#ffffff',position:'absolute',right:16}}>
                                            <Text style={styles.greyTextStyle}>价格：
                                                <Text style={styles.redTextStyle}>面议</Text>
                                            </Text>
                                        </View>
                                    }
                                    </View>
                                </View>

                                {/* 分隔线 */}
                                {/* <View style={styles.columnLineViewStyle}></View> */}
                                
                                <View style={{marginTop: 20}}>
                                    <View style={{flexDirection: 'row',marginLeft:16,marginTop:10}}>
                                        <View style={{width:(screenWidth-64)/2}}>
                                            <View style={{flexDirection: 'row'}}>
                                                <Image source={require('../../../assets/image/weight.png')} style={{width:20,height:20}}></Image>
                                                <Text style={[styles.greyTextStyle,{marginLeft:6}]}>重量</Text>
                                            </View>
                                            <View>
                                                <Text style={[styles.blackTextStyle,{marginLeft:26}]}>{this.state.productWeight}</Text>
                                            </View>
                                        </View>
                                        <View style={{width:(screenWidth-64)/2}}>
                                            <View style={{flexDirection: 'row'}}>
                                                <Image source={require('../../../assets/image/address.png')} style={{width:20,height:20}}></Image>
                                                <Text style={[styles.greyTextStyle,{marginLeft:6}]}>产地</Text>
                                            </View>
                                            <View>
                                                <Text style={[styles.blackTextStyle,{marginLeft:26}]}>{this.state.productionAddr}</Text>
                                            </View>
                                        </View>
                                    </View>
                                    <View style={{flexDirection: 'row',marginLeft:16,marginTop:20}}>
                                        <View style={{width:(screenWidth-64)/2}}>
                                            <View style={{flexDirection: 'row'}}>
                                                <Image source={require('../../../assets/image/gmtCreate.png')} style={{width:20,height:20}}></Image>
                                                <Text style={[styles.greyTextStyle,{marginLeft:6}]}>发布时间</Text>
                                            </View>
                                            <View>
                                                <Text style={[styles.blackTextStyle,{marginLeft:26}]}>{this.state.gmtCreated.slice(0,10)}</Text>
                                            </View>
                                        </View>
                                        <View style={{width:(screenWidth-64)/2}}>
                                            <View style={{flexDirection: 'row'}}>
                                                <Image source={require('../../../assets/image/contectPersion.png')} style={{width:20,height:20}}></Image>
                                                <Text style={[styles.greyTextStyle,{marginLeft:6}]}>联系人</Text>
                                            </View>
                                            <View>
                                                <Text style={[styles.blackTextStyle,{marginLeft:26}]}>{this.state.contactPerson}</Text>
                                            </View>
                                        </View>
                                    </View>
                                    <View style={{flexDirection: 'row',marginLeft:16,marginTop:20,marginBottom:20}}>
                                        <View style={{width:screenWidth-64}}>
                                            <View style={{flexDirection: 'row'}}>
                                                <Image source={require('../../../assets/image/sustainTime.png')} style={{width:20,height:20}}></Image>
                                                <Text style={[styles.greyTextStyle,{marginLeft:6}]}>有效时间</Text>
                                            </View>
                                            <View>
                                                <Text style={[styles.blackTextStyle,{marginLeft:26}]}>{this.state.gmtCreated.slice(0,10)}至{this.state.expDate.slice(0,10)}</Text>
                                            </View>
                                        </View>
                                    </View>
                                </View>

                                {/* 分隔线
                                <View style={styles.columnLineViewStyle}></View> */}
                                
                            </View>
                        </View>
                        <View>
                            <View style={{marginLeft:32,marginTop:10,height:46,width:67,borderBottomWidth:1,borderBottomColor:'blue',justifyContent:'center'}}>
                                <Text style={[styles.blackTextStyle,{color:'#255BDA'}]}>详细说明</Text>
                            </View>
                            {/* 分隔线 */}
                                <View style={styles.columnLineViewStyle}></View>
                            <View style={{marginTop:20,marginLeft:16,marginRight:16}}>
                                <Text style={[styles.blackTextStyle]}>{'\t'}{'\t'}{'\t'}{'\t'}{'\t'}{'\t'}{this.state.productExplain?this.state.productExplain:"无"}</Text>
                            </View>
                        </View>
                        <View style={{flexDirection:'row',width:screenWidth,marginTop:20,borderTopWidth:11,borderTopColor:'#F4F7F9'}}>
                            <View style={{flexDirection:'row',marginLeft:16}}>
                                <View style={[{ width: 60, paddingTop: 5, position: 'relative' }]}>
                                    <Image source={require('../../../assets/image/logo_new.png')} style={{width:60,height:60}}></Image>
                                </View>
                                <View style={{marginLeft:20, flexDirection: 'column', paddingTop: 7}}>
                                    <View style={{flexDirection: 'row', marginTop: 5 }}>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={{ fontSize: 20, color: "#000000" }}>{this.state.enterpriseName?this.state.enterpriseName:"无"}</Text>
                                        </View>

                                    </View>

                                    <View style={{flexDirection: 'row'}}>
                                        <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 ,flexDirection:'row'}}>
                                            <Text style={[{fontSize: 14, color: "#000000" ,opacity:0.6}]}>联系人</Text>
                                            <Text style={[{fontSize: 14, color: "#000000" ,fontWeight:'500',marginLeft:4}]}>{this.state.contactPerson}</Text>
                                        </View>
                                    </View>
                                </View>
                               
                            </View>

                            {/* 点击拨号 */}
                            <View style={{position:'absolute',right:16,alignItems:'center',top:14}}>
                                <TouchableOpacity  onPress={() => {
                                    let phone = this.state.salePersonTel;
                                    if(phone == null){
                                        WToast.show({data:'暂未添加联系人电话，请联系管理员！'});
                                        return;
                                    }
                                    const url = `tel:${phone}`;
                                    Linking.canOpenURL(url)
                                    .then(supported => {
                                        if (!supported) {
                                        return Alert.alert('提示', `您的设备不支持该功能，请手动拨打 ${phone}`, [
                                            { text: '确定' }
                                        ]);
                                        }
                                        return Linking.openURL(url);
                                    })
                                    .catch(err => WToast.show({data:`出错了：${err}`}));
                                }}>
                                    <Image  style={{width:40, height:40}} source={require('../../../assets/icon/iconfont/tel.png')}></Image>
                                </TouchableOpacity>
                            </View>
                            
                        </View>

                        {/* 进入店铺、收藏店铺 */}
                        {/* <View style={{flexDirection:'row'}}>
                                <View style={{width:80 }}>
                                    <TouchableOpacity onPress={()=>{
                                        // this.props.navigation.navigate("MyBacklogDailyList", 
                                        // {
                                        //     // 传递回调函数
                                        //     refresh: this.callBackFunction 
                                        // })
                                    }}>
                                        <View style={[{width: 80, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                                            <Text style={styles.blackTextStyle}>进入店铺</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <View style={{width:80 }}>
                                    <TouchableOpacity onPress={()=>{
                                        // this.props.navigation.navigate("MyBacklogDailyList", 
                                        // {
                                        //     // 传递回调函数
                                        //     refresh: this.callBackFunction 
                                        // })
                                    }}>
                                        <View style={[{width: 80, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                                            <Text style={styles.blackTextStyle}>收藏店铺</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View> */}
                    
                    
                    
                </ScrollView>

                {/* 底部导航栏 */}
                {/* <View style={{width:screenWidth,height:64,flexDirection:'row',backgroundColor:'#ffffff',marginBottom:10,alignItems:'center'}}>
                    <TouchableOpacity>
                        <View style={styles.bottomIconStyle}>
                            <View style={{justifyContent:'center'}}>
                                <Image source={require('../../../assets/icon/bottom_navigate/naicai_index.png')} style={{width:21,height:21,}}></Image>
                            </View>
                            <View>
                                <Text style={{font:12}}>店铺</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity>
                        <View style={styles.bottomIconStyle}>
                            <View >
                                <Image source={require('../../../assets/icon/bottom_navigate/card.png')} style={{width:21,height:21,alignItems:'center'}}></Image>
                            </View>
                            <View>
                                <Text style={{font:12}}>交换名片</Text>
                            </View>
                        </View>    
                    </TouchableOpacity>
                    <TouchableOpacity>
                        <View style={styles.bottomIconStyle}>
                            <View>
                                <Image source={require('../../../assets/icon/bottom_navigate/card.png')} style={{width:21,height:21,}}></Image>
                            </View>
                            <View>
                                <Text style={{font:12}}>收藏</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity>
                        <View style={{width:screenWidth*0.4,height:48,backgroundColor:'#255BDA',alignItems:'center',justifyContent:'center',borderRadius:4}}>
                            <Text style={{fontSize:20}}>在线交流</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}
            </View>
        )
    }
}
const styles = StyleSheet.create({
    columnLineViewStyle:{
        // width:100,
        // marginTop:17,
        // marginLeft:32,
        flexDirection:'row',
        justifyContent:'center',
        borderBottomWidth:1,
        borderColor:'#DFE3E8'
    },
    blackTextStyle: {
        fontSize:16,
        color:'#000000',
        fontWeight:'500',
    },
    redTextStyle: {
        fontSize:18,
        color:'#E41F00',
        fontWeight:'bold',
    },
    greyTextStyle: {
        fontSize:14,
        color:'#808DA1',
        backgroundColor:'#FFF',
    },
    bottomIconStyle: {
        width:0.14*screenWidth,
        justifyContent:'center',
    },
    topBox: {
        flexDirection: 'row',
        margin: 10,
        borderColor: "#F4F4F4",
        elevation: 2,
    },

    innerViewStyleList:{
        // marginTop: 10,
        borderColor: "rgba(255, 255, 255, 1)",
        // borderColor: "#FFFFFF",
        // borderWidth: 2
    },
});