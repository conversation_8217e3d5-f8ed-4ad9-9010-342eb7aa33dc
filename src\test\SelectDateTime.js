import React from 'react'
import {
    Text,
    View,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Dimensions,
    Image,
} from 'react-native';
import DatePicker from 'react-native-datepicker'

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

// https://www.npmjs.com/package/react-native-datepicker
export default class SelectDateTime extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            time: "21:12",
            datetime: "2021-12-13 21:12",
            date:"2021-12-13"
        }
    }
    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
    }

    render() {
        return (
            <ScrollView style={{ width: screenWidth, height: screenHeight, overflow: 'scroll', flexDirection: 'column', backgroundColor: '#fff000' }}>
                <View style={{ alignItems: 'center', justifyContent: 'center', backgroundColor:'red'}}>
                    <View style={{ flexDirection: 'column' }}>
                        <Text style={styles.instructions}>date: {this.state.date}</Text>
                        <DatePicker
                            style={{ width: 200 }}
                            date={this.state.date}
                            mode="date"
                            format="YYYY-MM-DD"
                            confirmBtnText="确定"
                            cancelBtnText="取消"
                            showIcon={false}
                            onDateChange={(date) => { this.setState({ date: date }); }}
                        />
                        <Text style={styles.instructions}>time: {this.state.time}</Text>
                        <DatePicker
                            style={{ width: 200 }}
                            date={this.state.time}
                            mode="time"
                            androidMode="spinner"
                            format="HH:mm"
                            confirmBtnText="确定"
                            cancelBtnText="取消"
                            showIcon={false}
                            onDateChange={(time) => { this.setState({ time: time }); }}
                        />
                        <Text style={styles.instructions}>datetime: {this.state.datetime}</Text>
                        <DatePicker
                            style={{ width: 200 }}
                            date={this.state.datetime}
                            mode="datetime"
                            androidMode="spinner"
                            format="YYYY-MM-DD HH:mm"
                            is24Hour="true"
                            confirmBtnText="确定"
                            cancelBtnText="取消"
                            customStyles={{
                                dateIcon: {
                                    position: 'absolute',
                                    left: 0,
                                    top: 4,
                                    marginLeft: 0
                                },
                                dateInput: {
                                    marginLeft: 36
                                }
                            }}
                            minuteInterval={10}
                            onDateChange={(datetime) => { this.setState({ datetime: datetime }); }}
                        />
                        
                    </View>
                </View>
            </ScrollView>
        )
    }

}
const styles = StyleSheet.create({
    instructions: {
        fontSize: 20,
    },
});