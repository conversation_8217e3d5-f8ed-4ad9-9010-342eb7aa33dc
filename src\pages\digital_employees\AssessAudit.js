import React,{Component} from 'react';
import {
    View, ScrollView, Text, TextInput, StyleSheet,KeyboardAvoidingView,FlatList,TouchableOpacity,Dimensions,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class AssessAudit extends Component {
    constructor() {
        super();
        this.state = {
            assessRecordId:"",
            applyDate:"",
            applyUserId:null,
            applyUserName:"",
            assessTitle:"",
            assessContent:"",
            assessClassName:"",
            assessUserName: "",
            expectAssessDate:"",
            actualAssessDate:"",
            selectedActualAssessDate:[],
            assessOpinion:"",

            assessDifficultyName:"",
            assessDifficultyCode:"",
            selectedAssessDifficulty:[],
            assessDifficultyDataSource:[],

            assessResultName:"",
            assessResultCode:"",
            selectedAssessResult:[],
            assessResultDataSource:[],

        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        //加载考核难度
        this.loadAssessDifficultyList();

        //加载考核结果
        this.loadAssessResultList();

        const { route, navigation } = this.props;
        if (route && route.params) {
            const {assessRecordId,operate } = route.params;
            if (operate) {
                if ("edit_audit" === operate) {
                    this.setState({ 
                        operate:"重新审批",
                    })
                }
                else {
                    this.setState({ 
                        operate:"审批",
                    })
                }
            }
            if (assessRecordId) {
                console.log("========Edit==assessRecordId:", assessRecordId);
                this.setState({
                    assessRecordId:assessRecordId,
                })
                let url= "/biz/assess/record/get";
                let loadRequest={'assessRecordId':assessRecordId};
                httpPost(url, loadRequest, this.loadAssessAuditCallBack);
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectedActualAssessDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    actualAssessDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
            
        }
    }

    loadAssessAuditCallBack=(response) => {
        if (response.code == 200 && response.data) {
            if (response.data.actualAssessDate) {
                var selectedActualAssessDate = response.data.actualAssessDate.split("-");
                this.setState({
                    assessRecordId:response.data.assessRecordId,
                    applyDate:response.data.applyDate,
                    applyUserId:response.data.applyUserId,
                    applyUserName:response.data.applyUserName,
                    assessTitle:response.data.assessTitle,
                    assessContent:response.data.assessContent,
                    expectAssessDate:response.data.expectAssessDate,
                    assessClassName:response.data.assessClassName,
                    assessUserName:response.data.assessUserName,
                    assessDifficultyName:response.data.assessDifficultyName,
                    assessDifficultyCode:response.data.assessDifficulty,
                    selectedAssessDifficulty:[response.data.assessDifficultyName],
                    assessResultName:response.data.assessResultName,
                    assessResultCode:response.data.assessResult,
                    selectedAssessResult:[response.data.assessResultName],
                    assessOpinion:response.data.assessOpinion,
                    actualAssessDate: response.data.actualAssessDate,
                    selectedActualAssessDate:selectedActualAssessDate
                })
            }
            else {
                this.setState({
                    assessRecordId:response.data.assessRecordId,
                    applyDate:response.data.applyDate,
                    applyUserId:response.data.applyUserId,
                    applyUserName:response.data.applyUserName,
                    assessTitle:response.data.assessTitle,
                    assessContent:response.data.assessContent,
                    expectAssessDate:response.data.expectAssessDate,
                    assessClassName:response.data.assessClassName,
                    assessUserName:response.data.assessUserName,  
                    assessDifficultyName:response.data.assessDifficultyName,
                    assessDifficultyCode:response.data.assessDifficulty,
                    selectedAssessDifficulty:[response.data.assessDifficultyName],    
                    assessResultName:response.data.assessResultName,
                    assessResultCode:response.data.assessResult,
                    selectedAssessResult:[response.data.assessResultName],
                    assessOpinion:response.data.assessOpinion
                })
            }            
        }
    }

    loadAssessDifficultyList=()=>{
        let loadDifficultyUrl = "/biz/assess/record/assessDifficultyList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(loadDifficultyUrl, loadRequest, this.callBackAssessDifficultyData);
    }

    callBackAssessDifficultyData=(response)=> {
        if (response.code == 200 && response.data) {
            this.setState({
                assessDifficultyDataSource: response.data
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadAssessResultList=()=>{
        let loadResultUrl = "/biz/assess/record/assessResultList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(loadResultUrl, loadRequest, this.callBackAssessResultData);
    }

    callBackAssessResultData=(response)=> {
        if (response.code == 200 && response.data) {
            this.setState({
                assessResultDataSource: response.data
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    openAssessDifficultySelect(){           
        if (!this.state.assessDifficultyDataSource || this.state.assessDifficultyDataSource.length < 1) {
            WToast.show({data:"暂无数据"});
            return
        }
        
        this.refs.SelectAssessDifficulty.showAssessDifficulty(this.state.selectedAssessDifficulty,this.state.assessDifficultyDataSource)
    }

    callBackSelectAssessDifficultyValue(value){
        console.log("==========考核难度选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedAssessDifficulty:value
        })
        var assessDifficultyName = value.toString();
        this.setState({
            assessDifficultyName:assessDifficultyName
        })
        let url= "/biz/assess/record/assessDifficultyByName";
        let loadRequest={"assessDifficultyName":assessDifficultyName};
        httpPost(url, loadRequest, this.loadAssessDifficultyCallBack);
    }

    loadAssessDifficultyCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                assessDifficultyCode:response.data.assessDifficulty
            })
        }
    }

    openAssessResultSelect(){           
        if (!this.state.assessResultDataSource || this.state.assessResultDataSource.length < 1) {
            WToast.show({data:"暂无数据"});
            return
        }
        
        this.refs.SelectAssessResult.showAssessResult(this.state.selectedAssessResult,this.state.assessResultDataSource)
    }

    callBackSelectAssessResultValue(value){
        console.log("==========考核难度选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedAssessResult:value
        })
        var assessResultName = value.toString();
        this.setState({
            assessResultName:assessResultName
        })
        let url= "/biz/assess/record/assessResultByName";
        let loadRequest={"assessResultName":assessResultName};
        httpPost(url, loadRequest, this.loadAssessResultCallBack);
    }

    loadAssessResultCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                assessResultCode:response.data.assessResult
            })
        }
    }

    openActualAssessDate(){
        this.refs.SelectActualAssessDate.showDate(this.state.selectedActualAssessDate)
    }
    
    callBackSelectSelectActualAssessDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedActualAssessDate:value
        })
        if (value && value.length) {
            var actualAssessDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    actualAssessDate += vartime;
                }
                else{
                    actualAssessDate += "-" + vartime;
                }
            }
            this.setState({
                actualAssessDate:actualAssessDate
            })
        }
    }

    saveAssessAudit = () => {
        console.log("=======saveAuditConfig");
        let toastOpts;

        if (!this.state.assessDifficultyName) {
            toastOpts = getFailToastOpts("请选择考核难度");
            WToast.show(toastOpts)
            return;
        }

        if (!this.state.actualAssessDate) {
            toastOpts = getFailToastOpts("请选择实际考核时间");
            WToast.show(toastOpts)
            return;
        }

        if (!this.state.assessResultName) {
            toastOpts = getFailToastOpts("请选择考核结果");
            WToast.show(toastOpts)
            return;
        }

        let url = "/biz/assess/record/modify";
        let requestParams = {
            applyUserId:this.state.applyUserId,
            assessRecordId:this.state.assessRecordId,
            assessDifficulty:this.state.assessDifficultyCode,
            assessResult:this.state.assessResultCode,
            actualAssessDate:this.state.actualAssessDate,
            assessOpinion:this.state.assessOpinion,            
            assessRecordState:"0AB",
        };
        httpPost(url, requestParams, this.saveAssessAuditCallBack);
    }

    // 保存回调函数
    saveAssessAuditCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('审批完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }


    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>申请日期</Text>
                        </View>
                        <TextInput
                            editable={false} 
                            style={styles.inputRightText}
                            placeholder={'请输入申请日期'}
                            onChangeText={(text) => this.setState({applyDate:text})}
                        >
                            {this.state.applyDate}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>申请人</Text>
                        </View>
                        <TextInput 
                            editable={false} 
                            style={styles.inputRightText}
                            placeholder={'请输入申请人'}
                            onChangeText={(text) => this.setState({applyUserName:text})}
                        >
                            {this.state.applyUserName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核标题</Text>
                        </View>
                        <TextInput
                            editable={false} 
                            style={styles.inputRightText}
                            placeholder={'请输入考核标题'}
                            onChangeText={(text) => this.setState({assessTitle:text})}
                        >
                            {this.state.assessTitle}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核内容</Text>
                        </View>
                        {/* <TextInput
                            editable={false} 
                            style={styles.inputRightText}
                            placeholder={'请输入考核内容'}
                            onChangeText={(text) => this.setState({assessContent:text})}
                        >
                            {this.state.assessContent}
                        </TextInput> */}
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <TextInput 
                            multiline={true}
                            editable={false}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                            placeholder={'请输入考核内容'}
                            onChangeText={(text) => this.setState({assessContent:text})}
                        >
                            {this.state.assessContent}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核类别</Text>
                        </View>
                        <TextInput
                            editable={false} 
                            style={styles.inputRightText}
                            placeholder={'请输入考核类别'}
                            onChangeText={(text) => this.setState({assessClassName:text})}
                        >
                            {this.state.assessClassName}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核难度</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openAssessDifficultySelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5) }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.assessDifficultyName ? "请选择考核难度" : this.state.assessDifficultyName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            editable={false} 
                            style={styles.inputRightText}
                            placeholder={'请输入考核人'}
                            onChangeText={(text) => this.setState({assessUserName:text})}
                        >
                            {this.state.assessUserName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>预计考核日期</Text>
                        </View>
                        <TextInput
                            editable={false} 
                            style={styles.inputRightText}
                            placeholder={'请输入预计考核日期'}
                            onChangeText={(text) => this.setState({expectAssessDate:text})}
                        >
                            {this.state.expectAssessDate}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>实际考核日期</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openActualAssessDate()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.actualAssessDate ? "请选择实际考核日期" : this.state.actualAssessDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>                    

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核结果</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openAssessResultSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5) }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.assessResultName ? "请选择考核结果" : this.state.assessResultName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>                                                      

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考核意见</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                            placeholder={'请输入考核意见'}
                            onChangeText={(text) => this.setState({assessOpinion:text})}
                        >
                            {this.state.assessOpinion}
                        </TextInput>
                    </View>

                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveAssessAudit.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    
                    <BottomScrollSelect 
                        ref={'SelectAssessDifficulty'} 
                        callBackAssessDifficultyValue={this.callBackSelectAssessDifficultyValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectAssessResult'} 
                        callBackAssessResultValue={this.callBackSelectAssessResultValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectActualAssessDate'} 
                        callBackDateValue={this.callBackSelectSelectActualAssessDateValue.bind(this)}
                    />
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:18
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputTextStyleTextStyle:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        height:45,
        justifyContent:'center'
    }
});