import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyDepartmentComponent from '../../component/EmptyDepartmentComponent';
const {ifIphoneXContentViewHeight} = require('../../utils/ScreenUtil');

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class Department extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      parentDepartmentId: null,
      parentDepartmentName: null,
      topBlockLayoutHeight: 0,
      searchKeyWord: '',
      moreModal: false,
      modalItem: {},
      deleteModal: false,
      newDepartmentId: null,
      departmentDataScore: [],
      _departmentDataScore: [],
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    console.log(
      '-====++++++++111',
      constants.loginUser.tenantExtAttrJSON.menuTypes,
    );
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {parentDepartmentId, parentDepartmentName} = route.params;
      if (parentDepartmentName) {
        this.setState({
          parentDepartmentName: parentDepartmentName,
        });
      }
      if (parentDepartmentId) {
        this.setState({
          parentDepartmentId: parentDepartmentId,
        });
        this.loadDepartmentList(parentDepartmentId);
        return;
      }
    }
    this.loadDepartmentList();
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/department/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      parentDepartmentId: this.state.parentDepartmentId
        ? this.state.parentDepartmentId
        : null,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/department/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      parentDepartmentId: this.state.parentDepartmentId
        ? this.state.parentDepartmentId
        : null,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
      if (this.state.dataSource && this.state.dataSource.length > 0) {
        this.setState({
          departmentDataScore: copyArr(this.state.dataSource),
        });
      }
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    if (this.state.refreshing) {
      WToast.show({data: 'loading...'});
      return;
    }
    this.setState({ refreshing: true }, () => {
      console.log('refreshing 已更新:', this.state.refreshing);
          // 在这里执行后续操作
          this.loadDepartmentList();
    });
  };

  loadDepartmentList = (parentDepartmentId) => {
    let url = '/biz/department/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      parentDepartmentId: parentDepartmentId
        ? parentDepartmentId
        : this.state.parentDepartmentId,
    };
    httpPost(url, loadRequest, this.loadDepartmentListCallBack);
  };

  loadDepartmentListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteDepartment = (departmentId) => {
    console.log('=======delete=departmentId', departmentId);
    let url = '/biz/department/delete';
    let requestParams = {departmentId: departmentId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.departmentId} style={styles.innerViewStyle}>
        {index == 0 ? (
          <View
            style={{
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#FFFFFF',
              borderBottomWidth: 10,
              borderBottomColor: '#F4F7F9',
            }}></View>
        ) : (
          <View></View>
        )}
        <View style={{position: 'absolute', right: 0, top: 0, marginRight: 15}}>
          <TouchableOpacity
            onPress={() => {
              this.setState({
                moreModal: true,
                modalItem: item,
              });
            }}>
            <View
              style={[
                {
                  width: 35,
                  height: 35,
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <Image
                style={{width: 28, height: 28}}
                source={require('../../assets/icon/iconfont/more.png')}></Image>
            </View>
          </TouchableOpacity>
        </View>
        <View style={styles.bodyViewStyleSpecial}>
          <Text style={styles.texRowSpecial}>{item.departmentName}</Text>
        </View>
        <View style={[styles.bodyViewStyleCommon]}>
          <Text style={styles.texRowCommon}>排序：{item.departmentSort}</Text>
          {/* <Text style={styles.itemContentStyle}>{item.departmentSort}</Text> */}
        </View>
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>最近更新时间：{item.gmtModified ? item.gmtModified: item.gmtCreated }</Text>
                </View> */}
        <View style={[CommonStyle.itemBottomBtnStyle, {marginRight: 8}]}>
          {/* <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("DepartmentStaffMgrList", 
                            {
                                // 传递参数
                                departmentId:item.departmentId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle
                        ]}>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>员工管理</Text>
                        </View>
                    </TouchableOpacity> */}
          {/* <TouchableOpacity onPress={()=>{
                            this.props.navigation.push("DepartmentList", 
                            {
                                // 传递参数
                                parentDepartmentId:item.departmentId,
                                parentDepartmentName:item.departmentName,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle
                        ]}>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>子部门管理</Text>
                        </View>
                    </TouchableOpacity> */}
          <TouchableOpacity
            onPress={() => {
              this.props.navigation.navigate('JobMgrList', {
                // 传递参数
                departmentId: item.departmentId,
                // 传递回调函数
                refresh: this.callBackFunction,
              });
            }}>
            <View
              style={[
                styles.itemEditBtnViewStyle,
                {width: 80, flexDirection: 'row', marginRight: 0},
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 5}}
                source={require('../../assets/icon/iconfont/position.png')}></Image>
              <Text style={[CommonStyle.itemBottomEditBtnTextStyle]}>职位</Text>
            </View>
          </TouchableOpacity>
          {constants.loginUser &&
          constants.loginUser?.tenantExtAttrJSON?.menuTypes.includes('M') ? (
            <TouchableOpacity
              onPress={() => {
                this.props.navigation.navigate('EquipmentMgrList', {
                  // 传递参数
                  departmentId: item.departmentId,
                  // 传递回调函数
                  refresh: this.callBackFunction,
                });
              }}>
              <View
                style={[
                  styles.itemEditBtnViewStyle,
                  {
                    width: 80,
                    flexDirection: 'row',
                    marginRight: 15,
                    backgroundColor: '#C49E00',
                    borderColor: '#C49E00',
                  },
                ]}>
                <Image
                  style={{width: 23, height: 23, marginRight: 2}}
                  source={require('../../assets/icon/iconfont/equip.png')}></Image>
                <Text style={[CommonStyle.itemBottomEditBtnTextStyle]}>
                  设备
                </Text>
              </View>
            </TouchableOpacity>
          ) : (
            <View />
          )}

          {/* <TouchableOpacity onPress={() => {
                        // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                        //     return;
                        // }
                        Alert.alert('确认', '您确定要删除该部门吗？', [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                    // this在这里可用，传到方法里还有问题
                                    // this.props.navigation.goBack();
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteDepartment(item.departmentId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle, { width: 64 }]}>
                            <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                        </View>
                    </TouchableOpacity> */}
          {/* <TouchableOpacity onPress={() => {
                        // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                        //     return;
                        // }
                        this.props.navigation.navigate("DepartmentAdd",
                            {
                                // 传递参数
                                departmentId: item.departmentId,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64, marginRight: 16 }]}>
                            <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }}>编辑</Text>
                        </View>
                    </TouchableOpacity> */}
        </View>
        {/* <View style={styles.lineViewStyle}/> */}
      </View>
    );
  };
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyDepartmentComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      //     <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      // </TouchableOpacity>
      <View style={CommonStyle.viewListLeftViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[CommonStyle.btnListLeftBtn]}>
          <Image
            style={CommonStyle.btnListLeftBtnImage}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={CommonStyle.btnListLeftBtnText}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      // <TouchableOpacity onPress={() => {
      //     this.props.navigation.navigate("DepartmentAdd",
      //         {
      //             parentDepartmentId: this.state.parentDepartmentId,
      //             // 传递回调函数
      //             refresh: this.callBackFunction
      //         })
      // }}>
      //     <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>

      // </TouchableOpacity>
      <View style={CommonStyle.viewListRightViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('DepartmentAdd', {
              parentDepartmentId: this.state.parentDepartmentId,
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Image
            style={CommonStyle.btnListRightBtnImage}
            source={require('../../assets/icon/iconfont/add.png')}></Image>
        </TouchableOpacity>
      </View>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  searchByKeyWord = () => {
    let loadUrl = '/biz/department/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      // "departmentName": this.state.searchKeyWord,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title={(this.state.parentDepartmentName ? '子' : '') + '部门管理'}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.headViewStyle,
            {borderLeftWidth: 0, borderRightWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View style={CommonStyle.singleSearchBox}>
            <View style={CommonStyle.searchBoxWithoutOthers}>
              <Image
                style={{width: 16, height: 16, marginLeft: 7}}
                source={require('../../assets/icon/iconfont/search.png')}></Image>
              <TextInput
                style={{
                  color: 'rgba(rgba(0, 10, 32, 0.45))',
                  fontSize: 14,
                  marginLeft: 5,
                  paddingTop: 0,
                  paddingBottom: 0,
                  paddingRight: 0,
                  paddingLeft: 0,
                  width: '100%',
                }}
                returnKeyType="search"
                returnKeyLabel="搜索"
                onSubmitEditing={(e) => {
                  this.searchByKeyWord();
                }}
                placeholder={'搜索'}
                onChangeText={(text) => this.setState({searchKeyWord: text})}>
                {this.state.searchKeyWord}
              </TextInput>
            </View>
          </View>
        </View>
        <View
          style={{
            backgroundColor: '#FFFFFF',
            height: ifIphoneXContentViewHeight(),
          }}>
          <FlatList
            data={this.state.dataSource}
            keyExtractor={(item) => item.departmentId}
            ListEmptyComponent={this.emptyComponent}
            renderItem={({item, index}) => this.renderRow(item, index)}
            ItemSeparatorComponent={this.space}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>

        {/* 更多操作弹窗Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.moreModal}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0, 0, 0, 0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    this.props.navigation.navigate('DepartmentAdd', {
                      // 传递参数
                      departmentId: this.state.modalItem.departmentId,
                      // 传递回调函数
                      refresh: this.callBackFunction,
                    });
                    this.setState({
                      moreModal: false,
                    });
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 50,
                      paddingLeft: 30,
                      marginTop: 5,
                    }}>
                    <Text
                      style={{
                        color: 'rgba(0, 10, 32, 0.85)',
                        fontSize: 18,
                        lineHeight: 52,
                      }}>
                      编辑
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      moreModal: false,
                      deleteModal: true,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                    ]}>
                    {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                    <Text
                      style={[
                        {
                          color: 'rgba(0, 10, 32, 0.85)',
                          fontSize: 18,
                          lineHeight: 52,
                        },
                      ]}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: 291,
                  height: 50,
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      moreModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 105,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 删除弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.deleteModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 292,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认删除该部门?</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  删除后数据不可恢复，请谨慎操作
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 292,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了确定'});
                    this.deleteDepartment(this.state.modalItem.departmentId);
                  }}>
                  <View
                    style={[
                      {
                        width: 146,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    marginTop: 10,
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
  itemContentStyle: {
    fontSize: 14,
    lineHeight: 24,
    textAlign: 'left',
    textAlignVertical: 'top',
    color: 'rgba(0, 10, 32, 0.65)',
  },
  itemContentTextStyle: {
    marginLeft: 12,
    marginRight: 16,
    marginTop: 3,
    lineHeight: 24,
  },
  titleViewStyle: {
    flexDirection: 'row',
    marginLeft: 12,
    marginRight: 16,
    marginTop: 5,
  },
  lineViewStyle: {
    // height:1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    // marginBottom: 6,
    borderBottomWidth: 1,
    borderColor: '#E8E9EC',
  },
  itemEditBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 1,
    borderColor: '#255BDA',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    borderRadius: 6,
    backgroundColor: '#255BDA',
  },
  bodyViewStyleSpecial: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 40,
    marginRight: 10,
    marginBottom: 8,
    marginTop: 8,
  },
  bodyViewStyleCommon: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 40,
    marginRight: 10,
    marginBottom: 0,
    marginTop: 0,
  },
  texRowSpecial: {
    width: 200,
    height: 24,
    fontWeight: 'bold',
    fontSize: 20,
    color: '#404956',
    lineHeight: 24,
    textAlign: 'left',
    fontStyle: 'normal',
  },
  texRowCommon: {
    width: 220,
    height: 24,
    fontWeight: '400',
    fontSize: 14,
    color: 'rgba(0,10,32,0.65)',
    lineHeight: 24,
    textAlign: 'left',
    fontStyle: 'normal',
  },
  itemEditBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 1,
    borderColor: '#255BDA',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    borderRadius: 6,
    backgroundColor: '#255BDA',
  },
});
