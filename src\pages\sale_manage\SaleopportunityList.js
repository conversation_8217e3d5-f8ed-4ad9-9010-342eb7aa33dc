import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Modal,KeyboardAvoidingView,
    FlatList,RefreshControl,Image,TextInput,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class SaleopportunityList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight: 0,
            selResultFlag: "all",
            searchKeyWord: "",
            resultFlagChooseDataSource:[],
            modal:false,

            trackId:"",
            trackContactPerson:"",
            trackJobTitle:"",
            trackContactTel:"",
            trackExplain:"",
            opportunityId:"",

            errorMsg:"",
            opportunityTrackList:[],
            detailModal:false,
            gmtCreated:null,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
        this._updateState('正在刷新......', true);
             //5秒后结束刷新
             setTimeout(() => {
             this._updateState('结束状态', false)
             }, 2000)
        } 
     }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let resultFlagChooseDataSource = [
            {
                resultFlag: 'all',
                resultFlagName: '全部',
            },
            {
                resultFlag: "F",
                resultFlagName: "跟进",
            },
           
            {
                resultFlag: "C",
                resultFlagName: "关闭"
            },
            {
                resultFlag: "D",
                resultFlagName: "待认领"
            }

        ]
        this.setState({
            resultFlagChooseDataSource: resultFlagChooseDataSource,
        })
        var _gmtCreated = this.initGmtCreated();
        this.loadOpportunityList(_gmtCreated);
    }

    initGmtCreated=()=>{
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 3);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectGmtCreated: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            gmtCreated: _gmtCreated,
            initGmtCreated: _gmtCreated,
        })
        return _gmtCreated;
    }

     // 上拉触底加载下一页
     _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadOpportunityList();
    }
    loadOpportunityTrackList =()=>{
        let url = "/biz/sale/opportunity/track/list";
            let loadRequest = {
               "currentPage": 1,
               "pageSize": this.state.pageSize,
               "opportunityId": this.state.opportunityId
        };
        httpPost(url, loadRequest, this.loadOpportunityTrackListCallBack);
    }
    loadOpportunityTrackListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    loadOpportunityList = (_gmtCreated) => {
        let url = "/biz/sale/opportunity/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "resultFlag": this.state.selResultFlag === 'all' ? null : this.state.selResultFlag,
            "gmtCreated": _gmtCreated ? _gmtCreated : this.state.gmtCreated,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this.loadOpportunityListCallBack);
    }

    loadOpportunityListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.gmtCreated === this.state.initGmtCreated) {
            return;
        }
        var _gmtCreated = this.initGmtCreated();
        this.setState({
            gmtCreated: _gmtCreated,
            currentPage: 1
        })
        let url = "/biz/sale/opportunity/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "resultFlag": this.state.selResultFlag === 'all' ? null : this.state.selResultFlag,
            "gmtCreated": _gmtCreated,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 回调函数
    callBackFunction = () => {
        let url = "/biz/sale/opportunity/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "opportunityId": constants.loginUser.opportunityId,
            "resultFlag": this.state.selResultFlag === "all" ? null : this.state.selResultFlag,
            "searchKeyWord": this.state.searchKeyWord,
            "gmtCreated":this.state.gmtCreated
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    searchByKeyWord = () => {
        let loadUrl = "/biz/sale/opportunity/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "resultFlag": this.state.selResultFlag === 'all' ? null : this.state.selResultFlag,
            "searchKeyWord": this.state.searchKeyWord,
            "gmtCreated":this.state.gmtCreated
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }
    deleteOpportunity = (opportunityId) => {
        console.log("=======delete=opportunityId", opportunityId);
        let url = "/biz/sale/opportunity/delete";
        let requestParams = { 'opportunityId': opportunityId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("SaleopportunityAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }
    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    resultFlagChooseStateRow=(item, index)=>{
        return (
            <View key={item.resultFlag} >
                <TouchableOpacity onPress={()=>{
                    var selResultFlag = item.resultFlag;
                    this.setState({
                        selResultFlag:selResultFlag
                    })

                    let loadUrl= "/biz/sale/opportunity/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "resultFlag": selResultFlag === 'all' ? null : selResultFlag,
                        "searchKeyWord": this.state.searchKeyWord,
                        "gmtCreated":this.state.gmtCreated
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.resultFlag} style={[item.resultFlag===this.state.selResultFlag? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle,{paddingLeft:8,paddingRight:8}]}>
                        <Text style={[item.resultFlag===this.state.selResultFlag? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16,{fontWeight:'bold',textAlign:'center'}]}>
                            {item.resultFlagName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }
    modifyOpportunityState = (opportunityId, resultFlag) => {
        console.log("=======delete=opportunityId", opportunityId);
        let url = "/biz/sale/opportunity/modify";
        let requestParams = { 'opportunityId': opportunityId, 'resultFlag': resultFlag };
        httpPost(url, requestParams, this.modifyOpportunityStateCallBack);
    }
    // 修改状态操作的回调操作
    modifyOpportunityStateCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "状态修改完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    saveOpportunityTrack=()=>{
        console.log("=======saveOpportunityTrack");
        if (!this.state.trackContactPerson) {
            this.setState({
                errorMsg:"请输入联系人"
            })
            return;
        }
        if (!this.state.trackJobTitle) {
            this.setState({
                errorMsg:"请输入职务"
            })
            return;
        }
        if (!this.state.trackContactTel) {
            this.setState({
                errorMsg:"请输入联系电话"
            })
            return;
        }
        if (!this.state.trackExplain) {
            this.setState({
                errorMsg:"请输入进展说明"
            })
            return;
        }
        
        let url = "/biz/sale/opportunity/track/add";
        // if (this.state.leadId) {
        //     console.log("=========Edit===leadId", this.state.leadId)
        //     url = "/biz/customer/lead/modify";
        // }
        let requestParams = {
            trackId: this.state.trackId,
            contactPerson: this.state.trackContactPerson,
            contactTel: this.state.trackContactTel,
            jobTitle: this.state.trackJobTitle,
            trackExplain: this.state.trackExplain,
            userId: constants.loginUser.userId,
            opportunityId:this.state.opportunityId
        };
        httpPost(url, requestParams, this.saveOpportunityTrackCallBack);
    }

    saveOpportunityTrackCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                this.setState({
                    modal:false
                })
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
        }
    }

    renderRow = (item, index) => {
        return (
            <View key={item.leadId} style={styles.innerViewStyle}>
                  <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,{width:screenWidth - 110}]}>商机名称：{item.opportunityName}</Text>
                    {
                        item.resultFlag ? 
                          item.resultFlag==='F' ?
                           <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                            {"跟进中"}
                           </Text>
                          :
                          <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(134,134,134,0.4)', color:'#FFFFFF'}}>
                          {item.userId === constants.loginUser.userId ? "已关闭" : "待认领"}
                          </Text>

                        :
                        null
                    }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>客户名称：{item.customerName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系人：{item.contactPerson}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>职务：{item.jobTitle}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>联系电话：{item.contactTel}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>提交人：{item.userName}</Text>
                </View>
                {
                    item.claimUserName
                    ?
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>认领人：{item.claimUserName}</Text>
                    </View>
                    :<View/>
                }
                
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.claimTime?"认领时间":"提交时间"}：{item.claimTime?item.claimTime:item.gmtCreated}</Text>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>  
                        {/* {
                        <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}> */}
                                {
                                    item.userId != constants.loginUser.userId && null == item.claimUserId && item.resultFlag === "C"
                                    ?
                                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                        <TouchableOpacity onPress={()=>{
                                                let loadUrl = "/biz/sale/opportunity/opportunityClaim";
                                                let loadRequest = {
                                                    "opportunityId": item.opportunityId,
                                                    'claimUserId':constants.loginUser.userId,
                                                    'leadId':item.leadId
                                                };
                                                httpPost(loadUrl, loadRequest, (response)=>{
                                                    if (response.code == 200 && response.data) {
                                                        WToast.show({ data: '认领成功' });
                                                        this.callBackFunction()
                                                    }
                                                    else{
                                                        WToast.show({ data: response.message });
                                                        this.props.navigation.navigate("LoginView");
                                                    }
                                            
                                                });
                                            }}>
                                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,backgroundColor:'#FFB800',flexDirection:'row'}]}>
                                                <Image style={{width:17, height:17,marginRight:2}} source={require('../../assets/icon/iconfont/add.png')}></Image>
                                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>认领</Text>
                                            </View>
                                        </TouchableOpacity>

                                        <TouchableOpacity onPress={()=>{
                                            let loadUrl = "/biz/sale/opportunity/track/list";
                                            let loadRequest = {
                                                "opportunityId": item.opportunityId,
                                            };
                                            httpPost(loadUrl, loadRequest, (response)=>{
                                                if (response.code == 200 && response.data && response.data.dataList) {
                                                    this.setState({
                                                        opportunityTrackList:response.data.dataList,
                                                        detailModal:true
                                                    })
                                                }
                                            });
                                        }}>
                                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { backgroundColor:"#3ab240",marginLeft:0,width:70,flexDirection:"row"}]}>
                                        <Image  style={{width:25, height:25,marginRight:2}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                        </View>
                                    </TouchableOpacity>
                                    </View>
                                    :
                                    (
                                        (item.userId != constants.loginUser.userId && item.claimUserId != constants.loginUser.userId) || (item.userId === constants.loginUser.userId && item.claimUserId != null)
                                        ?
                                        <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                            <TouchableOpacity onPress={()=>{
                                                    let loadUrl = "/biz/sale/opportunity/track/list";
                                                    let loadRequest = {
                                                        "opportunityId": item.opportunityId,
                                                    };
                                                    httpPost(loadUrl, loadRequest, (response)=>{
                                                        if (response.code == 200 && response.data && response.data.dataList) {
                                                            this.setState({
                                                                opportunityTrackList:response.data.dataList,
                                                                detailModal:true
                                                            })
                                                        }
                                                    });
                                                }}>
                                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { backgroundColor:"#3ab240",marginLeft:0,width:70,flexDirection:"row"}]}>
                                                <Image  style={{width:25, height:25,marginRight:2}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                                    <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                                </View>
                                            </TouchableOpacity>
                                        </View>

                                        :
                                        <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                            <TouchableOpacity onPress={()=>{
                                                this.setState({
                                                    modal:true,
                                                    opportunityId:item.opportunityId,
                                                    errorMsg:"",
                                                    trackContactPerson:"",
                                                    trackContactTel:"",
                                                    trackJobTitle:"",
                                                    trackExplain:""
                                                })
                                            }}>
                                                <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,backgroundColor:'#FFB800',flexDirection:'row'}]}>
                                                    <Image style={{width:17, height:17,marginRight:2}} source={require('../../assets/icon/iconfont/add.png')}></Image>
                                                    <Text style={CommonStyle.itemBottomEditBtnTextStyle}>进展</Text>
                                                </View>
                                            </TouchableOpacity>

                                            <TouchableOpacity onPress={()=>{
                                                let loadUrl = "/biz/sale/opportunity/track/list";
                                                let loadRequest = {
                                                    "opportunityId": item.opportunityId,
                                                };
                                                httpPost(loadUrl, loadRequest, (response)=>{
                                                    if (response.code == 200 && response.data && response.data.dataList) {
                                                        this.setState({
                                                            opportunityTrackList:response.data.dataList,
                                                            detailModal:true
                                                        })
                                                    }
                                                });
                                            }}>
                                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { backgroundColor:"#3ab240",marginLeft:0,width:70,flexDirection:"row"}]}>
                                            <Image  style={{width:25, height:25,marginRight:2}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                            </View>
                                        </TouchableOpacity>
                                            <TouchableOpacity onPress={()=>{
                                                    this.props.navigation.navigate("SaleopportunityAdd", 
                                                    {
                                                        // 传递参数
                                                        opportunityId:item.opportunityId,
                                                        // 传递回调函数
                                                        refresh: this.callBackFunction 
                                                    })
                                                }}>
                                                <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,marginLeft:0,flexDirection:'row'}]}>
                                                    <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                                    <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                                </View>
                                            </TouchableOpacity>
                                            <TouchableOpacity onPress={()=>{
                                                Alert.alert('确认','您确定要删除该商机吗？',[
                                                    {
                                                        text:"取消", onPress:()=>{
                                                        WToast.show({data:'点击了取消'});
                                                        // this在这里可用，传到方法里还有问题
                                                        // this.props.navigation.goBack();
                                                        }
                                                    },
                                                    {
                                                        text:"确定", onPress:()=>{
                                                            WToast.show({data:'点击了确定'});
                                                            this.deleteOpportunity(item.opportunityId)
                                                        }
                                                    }
                                                ]);
                                            }}>
                                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:70,marginLeft:0,flexDirection:'row'}]}>
                                                    <Image style={{width:20, height:20,marginRight:3}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                                </View>
                                            </TouchableOpacity>
                                            <TouchableOpacity onPress={() => {
                                                let operate = item.resultFlag === "F" ? "关闭" : "跟进";
                                                Alert.alert('确认', '您确定要' + operate + '吗？', [
                                                    {
                                                        text: "取消", onPress: () => {
                                                            WToast.show({ data: '点击了取消' });
                                                            // this在这里可用，传到方法里还有问题
                                                            // this.props.navigation.goBack();
                                                        }
                                                    },
                                                    {
                                                        text: "确定", onPress: () => {
                                                            WToast.show({ data: '点击了确定' });
                                                            this.modifyOpportunityState(item.opportunityId, item.resultFlag === "F" ? "C" : "F")
                                                        }
                                                    }
                                                ]);
                                            }}>
                                                <View style={[item.resultFlag === "F" ? [CommonStyle.itemBottomEditBtnViewStyle,{borderColor:"#bfbfbf",borderWidth:1,backgroundColor:'rgba(0,0,0,0)'}] 
                                                : [CommonStyle.itemBottomDetailBtnViewStyle,{borderColor:'rgba(255,0,0,0.4)',borderWidth:1,backgroundColor:'rgba(0,0,0,0)'}], { width:70,flexDirection:"row",marginLeft:0}
                                                ]}>
                                                    {
                                                        item.resultFlag === "F" ?
                                                        <Image  style={{width:25, height:25,marginRight:1}} source={require('../../assets/icon/iconfont/closeGrey.png')}></Image>
                                                        :
                                                        <Image  style={{width:18, height:18,marginRight:3}} source={require('../../assets/icon/iconfont/restartAlpha.png')}></Image>
                                                    }
                                                    <Text style={item.resultFlag === "F" ? [CommonStyle.itemBottomEditBtnTextStyle,{color:'#bfbfbf'}] 
                                                    : [CommonStyle.itemBottomEditBtnTextStyle,{color:'rgba(255,0,0,0.4)'}]}>
                                                        {item.resultFlag === "F" ? "关闭" : "跟进"}
                                                    </Text>
                                                </View>
                                            </TouchableOpacity>
                                        </View>

                                    )
                                }
                    </View>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    openGmtCreated(){
        this.refs.SelectGmtCreated.showDate(this.state.selectGmtCreated)
    }

    callBackSelectGmtCreatedValue(value){
        console.log("==========时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectGmtCreated:value
        })
        if (this.state.selectGmtCreated && this.state.selectGmtCreated.length) {
            var _gmtCreated = "";
            var vartime;
            for(var index=0;index<this.state.selectGmtCreated.length;index++) {
                vartime = this.state.selectGmtCreated[index];
                if (index===0) {
                    _gmtCreated += vartime;
                }
                else if (index < 3){
                    _gmtCreated += "-" + vartime;
                }
                else if (index===3){
                    _gmtCreated += " " + vartime;
                }
                else {
                    _gmtCreated += ":" + vartime;
                }
            }
            this.setState({
                currentPage: 1,
                gmtCreated:_gmtCreated
            })
            let url= "/biz/sale/opportunity/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "searchKeyWord": this.state.searchKeyWord,
                "gmtCreated": _gmtCreated,
                "resultFlag": this.state.selResultFlag === 'all' ? null : this.state.selResultFlag,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='商机管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.rightTop50FloatingBlockView,this.state.gmtCreated 
                    ? {borderRadius:3, width:null,height: 40, marginTop: 50,paddingLeft:15, paddingRight:15, opacity:0.5} : {}]}>
                    <TouchableOpacity onPress={()=>this.openGmtCreated()}>
                        <Text style={CommonStyle.rightTop50FloatingBlockText}>
                        {!this.state.gmtCreated ? "时间" : this.state.gmtCreated}
                        </Text>
                    </TouchableOpacity>
                </View>
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>

                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.resultFlagChooseDataSource && this.state.resultFlagChooseDataSource.length > 0)
                                ?
                                this.state.resultFlagChooseDataSource.map((item, index) => {
                                    return this.resultFlagChooseStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                    
                    <View style={{}}>
                        <View style={styles.inputOutsideText}>
                            <View style={styles.inputInsideText}>
                                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                                <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'客户名称/提交人'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>
            </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}//
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.modal}
                >
                    <KeyboardAvoidingView style={CommonStyle.fullScreenKeepOut} behavior="padding">
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle,{height:40, backgroundColor:'#FFB800',borderRadius:5, justifyContent:'center',alignItems:'center',marginTop:10}]}>
                                <Text style={[styles.titleTextStyle,{fontSize:18,fontWeight:'bold',color:"#ffffff"}]}>新增进展</Text>
                            </View>
                            <ScrollView style={[CommonStyle.formContentViewStyle]}>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    </View>                                
                                    <TextInput
                                        style={styles.inputRightText}
                                        placeholder={'请输入联系人'}
                                        onChangeText={(text) => this.setState({trackContactPerson:text})}
                                    >
                                        {this.state.trackContactPerson}
                                    </TextInput>
                                </View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>职务</Text>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    </View>                                
                                    <TextInput 
                                        style={styles.inputRightText}
                                        placeholder={'请输入职务'}
                                        onChangeText={(text) => this.setState({trackJobTitle:text})}
                                    >
                                        {this.state.trackJobTitle}
                                    </TextInput>
                                </View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    </View>                                
                                    <TextInput 
                                        style={styles.inputRightText}
                                        placeholder={'请输联系电话'}
                                        onChangeText={(text) => this.setState({trackContactTel:text})}
                                    >
                                        {this.state.trackContactTel}
                                    </TextInput>
                                </View>   
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>进展说明</Text>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    </View>                                
                                </View>
                                <View style={[styles.inputRowStyle,{height:180}]}>
                                    <TextInput 
                                        multiline={true}
                                        textAlignVertical="top"
                                        style={[CommonStyle.inputRowText,{height:180,width:screenWidth - 70}]}
                                        placeholder={'请输入进展说明'}
                                        onChangeText={(text) => this.setState({trackExplain:text})}
                                    >
                                        {this.state.trackExplain}
                                    </TextInput>
                                </View>
                            </ScrollView>
                            <View style={CommonStyle.alignCenterStyle}>
                                <Text style={[CommonStyle.rowLabRedTextStyle, CommonStyle.boldTextStyle]}>{this.state.errorMsg}</Text>
                            </View>
                            <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                <TouchableOpacity onPress={() => { 
                                    this.setState({
                                        modal:false,
                                    }) 
                                }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                        <Image  style={{width:22, height:22,marginRight:10}} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={
                                    this.saveOpportunityTrack.bind(this)
                                    }>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                        <Image  style={{width:25, height:25,marginRight:10}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                        <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>保存</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>      
                    </KeyboardAvoidingView>
                </Modal>
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.detailModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle,{height:40, backgroundColor:'#3ab240',borderRadius:5, justifyContent:'center',alignItems:'center',marginBottom:10,marginTop:10}]}>
                                <Text style={[styles.titleTextStyle,{fontSize:18,fontWeight:'bold',color:"#ffffff"}]}>进展详情</Text>
                            </View>
                            <ScrollView>
                                {
                                    this.state.opportunityTrackList && this.state.opportunityTrackList.length > 0
                                    ?
                                    (
                                        this.state.opportunityTrackList.map((trackItem)=>{
                                            return(
                                                <View>
                                                {/* <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}></Text>
                                                </View> */}
                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{trackItem.contactPerson}</Text>
                                                </View>
                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职务：{trackItem.jobTitle}</Text>
                                                </View>
                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{trackItem.contactTel}</Text>
                                                </View>
                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>进展说明：{trackItem.trackExplain}</Text>
                                                </View>
                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{trackItem.userName}</Text>
                                                </View>
                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{trackItem.gmtCreated}</Text>
                                                </View>
                                                <View style={{height:20}}>
                                                </View>
                                            </View>
                                            )
                                        })
                                    )
                                    :
                                    <View style={[{alignItems: 'center', justifyContent: 'center',marginTop:20}]}>
                                        <Text style={{color:'#A0A0A0',fontSize:25}}>暂无数据</Text>
                                    </View>
                                }
                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    
                                    this.setState({
                                        detailModal:false
                                    })
                                }}>
                                <View style={[styles.btnRowLeftCancelBtnView]} >
                                {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                    <Text  style={[styles.titleTextStyle,{ fontWeight: 'bold',fontSize:18,color:'#a1a1a1'}]}>返       回</Text>
                                </View>
                                </TouchableOpacity>  
                            </View>
                        </View>
                    </View>
                </Modal>
                <BottomScrollSelect 
                    ref={'SelectGmtCreated'} 
                    callBackDateValue={this.callBackSelectGmtCreatedValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 65),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabView: {
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    innerViewStyle: {
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
    },
    btnRowLeftCancelBtnView:{
        flexDirection:'row',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
        alignItems:'center',
        justifyContent:'center',
        borderWidth:1,
        borderColor:'#a1a1a1',
        borderRadius:5,
        height:40,        
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    fullScreenKeepOut:{
        height:screenHeight, 
        width:screenWidth,
        backgroundColor:'rgba(169,169,169,0.95)',
        alignItems:'center',
        justifyContent:'center',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputOutsideText:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },
    inputInsideText:{
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        // width:screenWidth - 180,
        marginLeft:20
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});