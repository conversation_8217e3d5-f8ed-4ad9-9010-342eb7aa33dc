import React,{Component} from 'react';
import {
    Alert,Modal,
    View, 
    ScrollView, 
    Text, 
    TextInput, 
    StyleSheet, 
    KeyboardAvoidingView ,
    TouchableOpacity,
    Dimensions,
    Image
} from 'react-native';

import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadMultiImageLibrary } from '../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

const leftLabWidth = 130;
export default class BlockWorkStorageInMgrAdd extends Component {
    constructor(props) {
        super(props);
        this.state ={
            storageInId:"",
            operate:"",
            //准备预砌入库的订单
            completeOrderTypeDataSource:[],
            //勾选的订单
            selOrderId: null,
            selOrderName:"",
            //准备预砌入库的订单统计集合
            selOrderTypeList:[],
            orderTime:"",
            errorMsg: null,
            //订单数据集
            orderDataSource: [],
            _orderDataSource:[],
            customerDataSource:[],
            _customerDataSource:[],
            positionDataSource:[],
            _positionDataSource:[],
            customerId:"",
            selCustomerId:"",
            selCustomerName:"",
            contractId:"",
            contractName:"",
            positionId:"",
            positionName:"",
            completedSetNumber:"",
            blockworkWorker:"",
            contractDataSource:[],
            selectContract:[],

            modal:false,
            positionModal:false,
            orderModal:false,
            searchKeyWord:"",
            searchPositionKeyWord: "",
            searchOrderKeyWord:""
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadCustomerData();
        // 加载订单部位
        
        let loadTypeUrl;
        let loadRequest;
        loadTypeUrl= "/biz/order/position/list";
        loadRequest={
        "currentPage":1,
        "pageSize":10000
        };
        httpPost(loadTypeUrl, loadRequest, this.loadOrderPositionListCallBack);
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageInId } = route.params;
            if (storageInId) {
                console.log("=============storageInId" + storageInId + "");
                this.setState({
                    operate:"编辑"
                })
                loadRequest={"storageInId": storageInId};
                loadTypeUrl= "/biz/blockwork/storage/in/get";
                httpPost(loadTypeUrl, loadRequest, this.loadEditStorageInDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
        
    }

    loadEditStorageInDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                storageInId: response.data.storageInId,
                selCustomerId:response.data.customerId,
                selCustomerName:response.data.customerName,
                contractId:response.data.contractId,
                contractName:response.data.contractName,
                selectContract:[response.data.contractName],
                positionId:response.data.positionId,
                positionName:response.data.positionName,
                selOrderTypeList:response.data.spBlockworkStorageInDetailDTOList,
                completedSetNumber:response.data.completedSetNumber,
                blockworkWorker:response.data.blockworkWorker,
            })
            let loadUrl = "/biz/contract/list";
            let loadRequest = {
                "currentPage":1,
                "pageSize":1000,
                "partyA": response.data.customerId,
            };
            httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadOrderPositionListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                positionDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadCustomerData=()=>{
        let loadUrl= "/biz/tenant/customer/list";
        let loadRequest={'currentPage':1,'pageSize':100};
        httpPost(loadUrl, loadRequest, this.callBackLoadCustomerData);
    }

    callBackLoadCustomerData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                customerDataSource:response.data.dataList,
                customerId:response.data.customerId,
            })
            // this.loadContractList(this.state.selCustomerId);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadContractListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                contractDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    openContractSelect() {
        if (this.state.selOrderTypeList && this.state.selOrderTypeList.length > 0) {
            WToast.show({ data: "请先将产品删除再进行合同的选择" });
            return;
        }
        if (this.state.selCustomerId) {
            if (!this.state.contractDataSource || this.state.contractDataSource.length < 1) {
                WToast.show({ data: "请先添加合同" });
                return;
            }
            this.setState({
                orderDataSource: []
            })
            this.refs.SelectContract.showContract(this.state.selectContract, this.state.contractDataSource)
        }
        else {
            WToast.show({ data: "请先选择客户名称"});
            return;
        }
    }

    callBackContractValue(value) {
        console.log("==========合同选择的结果：", value)
        if (!value) {
            return;
        }
        var contractName = value.toString();
        this.setState({
            selectContract: value,
            contractName: contractName,
            _orderDataSource:[]
        })
        // this.loadOrderList(contractName);
        let loadUrl= "/biz/contract/getContractByName";
        let loadRequest={
            "contractName":contractName
        };
        httpPost(loadUrl, loadRequest, this.loadContractDetailCallBack);
    }

    loadContractDetailCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                contractId : response.data.contractId,
            })
            this.loadOrderListByContractId(response.data.contractId);
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadOrderListByContractId=(contractId)=>{
        let loadUrl = "/biz/order/list";
        let loadRequest = {
            "currentPage":1,
            "pageSize":1000,
            "contractId": contractId,
            "qryContent":"order",
            // "display": "Y",
            // "excludeOrderStateList": [
            //     "A","B", "K"
            // ],
        };
        httpPost(loadUrl, loadRequest, this.loadOrderListByContractIdCallBack);
    }

    loadOrderListByContractIdCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                orderDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadOrder = () => {
        var _orderDataSource = copyArr(this.state.orderDataSource);
        if (this.state.searchOrderKeyWord && this.state.searchOrderKeyWord.length > 0) {
            _orderDataSource = _orderDataSource.filter(item => item.orderName.indexOf(this.state.searchOrderKeyWord) > -1);
        }
        this.setState({
            _orderDataSource: _orderDataSource,
        })
    }

    loadCustomer = () => {
        var _customerDataSource = copyArr(this.state.customerDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _customerDataSource = _customerDataSource.filter(item => item.customerName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _customerDataSource: _customerDataSource,
        })
    }

    renderCustomerRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.storageOutId) {
                    return;
                }
                this.setState({
                    selCustomerId: item.customerId,
                    selCustomerName: item.customerName,
                    // selectContract:[],
                    // contractId:"",
                    // contractName:"",
                })
                // this.loadContractList()
                // let loadUrl = "/biz/contract/list";
                // let loadRequest = {
                //     "currentPage":1,
                //     "pageSize":1000,
                //     "customerId": item.customerId,
                // };
                // httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
            }}>
                <View key={item.customerId} style={[item.customerId === this.state.selCustomerId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.customerId === this.state.selCustomerId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.customerName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    loadPosition = () => {
        var _positionDataSource = copyArr(this.state.positionDataSource);
        if (this.state.searchPositionKeyWord && this.state.searchPositionKeyWord.length > 0) {
            _positionDataSource = _positionDataSource.filter(item => item.positionName.indexOf(this.state.searchPositionKeyWord) > -1);
        }
        this.setState({
            _positionDataSource: _positionDataSource,
        })
    }

    renderPositionRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.storageOutId) {
                    return;
                }
                this.setState({
                    positionId: item.positionId,
                    positionName: item.positionName,
                })
            }}>
                <View key={item.positionId} style={[item.positionId === this.state.positionId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.positionId === this.state.positionId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.positionName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }


    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} >
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("BlockWorkStorageInMgrList") 
            // }}>
            //     <Text style={CommonStyle.headRightText}>预砌入库</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>预砌入库</Text>
                </TouchableOpacity>
            </View>
        )
    }

    saveBlockStorageIn = () => {
        console.log("=======saveBlockStorageIn==this.state.selOrderTypeList", this.state.selOrderTypeList);
        let toastOpts;
        if (!this.state.selCustomerId || this.state.selCustomerId === 0) {
            toastOpts = getFailToastOpts("请选择客户名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contractId || this.state.contractId === 0) {
            toastOpts = getFailToastOpts("请选择合同名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.positionId || this.state.positionName === 0) {
            toastOpts = getFailToastOpts("请选择使用部位");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selOrderTypeList || this.state.selOrderTypeList.length === 0) {
            toastOpts = getFailToastOpts("至少指定一种产品");
            WToast.show(toastOpts)
            return;
        }
        let brickAmountVal = true;
        this.state.selOrderTypeList.map((elem, index) => {
            // console.log("======elem:", elem)
            if (!elem || !elem.brickAmount || elem.brickAmount <= 0) {
                brickAmountVal = false;
                return;
            }
        })
        if (!brickAmountVal) {
            toastOpts = getFailToastOpts("订单的数量不合法");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.completedSetNumber || this.state.completedSetNumber === 0) {
            toastOpts = getFailToastOpts("请输入完成套数");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.blockworkWorker || this.state.blockworkWorker === 0) {
            toastOpts = getFailToastOpts("请输入预砌工人");
            WToast.show(toastOpts)
            return;
        }
        // 新增、编辑接口都走添加
        let url = "/biz/blockwork/storage/in/add";
        let _spOrderDetailDTOList = [];
        this.state.selOrderTypeList.map((elem, index) => {
            var storageInDetailDTO = {
                "orderId": elem.orderId,
                "brickAmount": elem.brickAmount,
            }
            _spOrderDetailDTOList.push(storageInDetailDTO);
        })
        let requestParams = {
            "storageInId": this.state.storageInId,
            "customerId": this.state.selCustomerId,
            "contractId": this.state.contractId,
            "positionId": this.state.positionId,
            "spBlockworkStorageInDetailDTOList": _spOrderDetailDTOList,
            "completedSetNumber":this.state.completedSetNumber,
            "blockworkWorker":this.state.blockworkWorker,
            "userId":constants.loginUser.userId
        };
        console.log("requestParams==========",requestParams)
        httpPost(url, requestParams, this.saveBlockStorageInCallBack);
    }

    // 保存回调函数
    saveBlockStorageInCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('预砌入库完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    renderOrderItem = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selBrickTypeId: item.brickTypeId,
                    selBrickTypeName: item.brickTypeName,
                    selOrderId: item.orderId,
                    selOrderName: item.orderName,
                    // brickAmount:item.brickAmount,
                })
            }}>
                <View key={item.orderId} style={item.orderId === this.state.selOrderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.orderId === this.state.selOrderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        //动态显示订单类型数据
        var pages = [];
        for (var i = 0; i < this.state.selOrderTypeList.length; i++) {
            const _orderDataSource = _.cloneDeep(this.state.orderDataSource);
            _orderDataSource.map((elem, index) => {
                elem._index = i;
                return elem;
            })
            pages.push(
                <View key={"view_" + this.state.selOrderTypeList[i].orderId + "_" + i}>

                    <View style={[{ flexWrap: 'wrap' }, this.state.storageInId ? CommonStyle.disableViewStyle : null]}>
                        <TouchableOpacity onPress={(event) => {
                           
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: '#F2F5FC', padding: 10, margin: 5 }]}>
                                <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: '#404956' }]}>
                                    选择产品
                                    {this.state.selOrderTypeList[i].orderId && this.state.selOrderTypeList[i].orderName ? ("：" + this.state.selOrderTypeList[i].orderName) : null}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    
                    <View style={CommonStyle.rowLabView}>
                        <View style={CommonStyle.rowLabLeftView}>
                            <Text style={CommonStyle.rowLabTextStyle}>数量</Text>
                            <Text style={CommonStyle.rowLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            placeholder={'请输入产品的数量'}
                            style={CommonStyle.rowRightTextInput}
                            orderId={this.state.selOrderTypeList[i].orderId}
                            onChange={(event) => {
                                // 通过回调事件查看控件属性
                                var orderId = event._dispatchInstances.memoizedProps.orderId;
                                var text = event.nativeEvent.text;
                                console.log("=====isNumber:", isNumber(text));
                                var varselOrderType;
                                for (var index = 0; index < this.state.selOrderTypeList.length; index++) {
                                    varselOrderType = this.state.selOrderTypeList[index];
                                    if (orderId === varselOrderType.orderId) {
                                        varselOrderType.brickAmount = text;
                                        this.state.selOrderTypeList[index] = varselOrderType;
                                        console.log("==数据更新==this.state.selOrderTypeList", this.state.selOrderTypeList);
                                    }
                                }
                            }}
                        >
                            {this.state.selOrderTypeList[i].brickAmount}
                        </TextInput>
                    </View>
                </View>
            );
        }

        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate+'预砌入库'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: -2 }} />

                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>客户名称</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[(!this.state.customerDataSource || this.state.customerDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (this.state.selOrderTypeList && this.state.selOrderTypeList.length > 0) {
                                    WToast.show({ data: "请先将产品删除再进行客户的选择" });
                                    return;
                                }
                                if (this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                                    this.setState({
                                        _customerDataSource: copyArr(this.state.customerDataSource),
                                    })
                                }
                                this.setState({
                                    modal: true,
                                    searchKeyWord: ""
                                })

                                if (!this.state.selCustomerId && this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                                    this.setState({
                                        selCustomerId: this.state.customerDataSource[0].customerId,
                                        selCustomerName: this.state.customerDataSource[0].customerName,
                                        // contractId:"",
                                        // contractName:"",
                                        // selectContract:[],
                                    })
                                }
                            }}>
                                <View style={[this.state.selCustomerId && this.state.selCustomerName ?
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    :
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        paddingLeft:6,
                                        paddingRight:6,
                                        // width: (screenWidth - 54)/2,
                                        borderRadius: 4,
                                    }
                                ]}>
                                    <Text style={[this.state.selCustomerId && this.state.selCustomerName ?
                                        {
                                            color: '#1E6EFA'
                                        }
                                        :
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>
                                        {this.state.selCustomerId && this.state.selCustomerName ? ( this.state.selCustomerName) : '选择客户名称'}
                                    </Text>
                                </View>
                                {/* <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, {height:40, flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                    {this.state.selCustomerId && this.state.selCustomerName ? 
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.selCustomerName}
                                        </Text>
                                        :
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        选择客户名称
                                        </Text>
                                    }
                                </View> */}
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15, }} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>合同名称</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={() => {this.openContractSelect()}}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.contractName ? "请选择" : this.state.contractName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadCustomer();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._customerDataSource && this.state._customerDataSource.length > 0)
                                                ?
                                                this.state._customerDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderCustomerRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selCustomerId) {
                                            let toastOpts = getFailToastOpts("您还没有选择需货单位");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        let loadUrl = "/biz/contract/list";
                                        let loadRequest = {
                                            "currentPage":1,
                                            "pageSize":1000,
                                            "partyA": this.state.selCustomerId,
                                        };
                                        httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
                                        this.setState({
                                            modal: false,
                                            contractId:"",
                                            contractName:"",
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>使用部位</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[(!this.state.positionDataSource || this.state.positionDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (this.state.positionDataSource && this.state.positionDataSource.length > 0) {
                                    this.setState({
                                        _positionDataSource: copyArr(this.state.positionDataSource),
                                    })
                                }
                                this.setState({
                                    positionModal: true,
                                    searchPositionKeyWord: ""
                                })

                                if (!this.state.positionId && this.state.positionDataSource && this.state.positionDataSource.length > 0) {
                                    this.setState({
                                        positionId: this.state.positionDataSource[0].positionId,
                                        positionName: this.state.positionDataSource[0].positionName,
                                    })
                                }
                            }}>
                                <View style={[this.state.positionId && this.state.positionName ?
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    :
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        paddingLeft:6,
                                        paddingRight:6,
                                        // width: (screenWidth - 54)/2,
                                        borderRadius: 4,
                                    }
                                ]}>
                                    <Text style={[this.state.positionId && this.state.positionName ?
                                        {
                                            color: '#1E6EFA'
                                        }
                                        :
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>
                                        {this.state.positionId && this.state.positionName ? ( this.state.positionName) : '选择使用部位'}
                                    </Text>
                                </View>
                                {/* <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, {height:40, flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                    {this.state.positionId && this.state.positionName ? 
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.positionName}
                                        </Text>
                                        :
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        选择使用部位
                                        </Text>
                                    }
                                </View> */}
                            </TouchableOpacity>
                        </View>
                    </View>

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.positionModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchPositionKeyWord: text })}
                                    >
                                        {this.state.searchPositionKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadPosition();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._positionDataSource && this.state._positionDataSource.length > 0)
                                                ?
                                                this.state._positionDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderPositionRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            positionModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.positionId) {
                                            let toastOpts = getFailToastOpts("您还没有选择使用部位");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            positionModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <View style={[styles.inputRowStyle,{ width: screenWidth, height: 50, justifyContent: 'flex-start', alignItems: 'center', backgroundColor: '#F6F9FA',paddingLeft:11}]}>
                    {/* <View style={CommonStyle.addItemSplitRowView}> */}
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>产品</Text>

                        {/* <Text style={[CommonStyle.addItemSplitRowText]}>产品</Text> */}
                        {/* <Text style={CommonStyle.rowLabRedTextStyle}>*</Text> */}
                    </View>
                    <View>
                        {
                            pages.map((elem, index) => {
                                return elem;
                            })
                        }
                    </View>

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.orderModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.alignCenterStyle}>
                                    <Text style={[CommonStyle.rowLabRedTextStyle, CommonStyle.boldTextStyle]}>{this.state.errorMsg}</Text>
                                </View>
        
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchOrderKeyWord: text })}
                                    >
                                        {this.state.searchOrderKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadOrder();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._orderDataSource && this.state._orderDataSource.length > 0)
                                                ?
                                                this.state._orderDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderOrderItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={CommonStyle.rowLabView}>
                                    <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>数量</Text>
                                        <Text style={CommonStyle.rowLabRedTextStyle}>*</Text>
                                    </View>
                                    <TextInput
                                        keyboardType='numeric'
                                        placeholder={'请输入产品的数量'}
                                        style={[CommonStyle.rowRightTextInput, { width: screenWidth - 150 }]}
                                        onChangeText={(text) => this.setState({ brickAmount: text })}
                                    >
                                        {this.state.brickAmount}
                                    </TextInput>
                                </View>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            orderModal: false,
                                            selOrderId: null,
                                            selOrderName: null,
                                            brickAmount: null,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selOrderId) {
                                            let errorMsg = "您还没有选择产品";
                                            this.setState({
                                                errorMsg: errorMsg,
                                            })
                                            let toastOpts = getFailToastOpts(errorMsg);
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        if (!this.state.brickAmount) {
                                            let errorMsg = "请输入产品的数量";
                                            this.setState({
                                                errorMsg: errorMsg,
                                            })
                                            let toastOpts = getFailToastOpts(errorMsg);
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        if (this.state.orderDataSource.length > 0) {
                                            var existAdd = false;
                                            let selOrderTypeList = this.state.selOrderTypeList;
                                            // JS 数组遍历
                                            selOrderTypeList.forEach((obj) => {
                                                console.log("==========obj.orderId:", obj.orderId, "==========this.state.orderId:", this.state.orderId)
                                                if (obj.orderId === this.state.selOrderId) {
                                                    // obj.brickAmount = parseInt(obj.brickAmount) + parseInt(this.state.brickAmount);
                                                    obj.brickAmount = parseInt(this.state.brickAmount);
                                                    existAdd = true;
                                                }
                                            })
                                            if (existAdd) {
                                                this.setState({
                                                    selOrderTypeList: selOrderTypeList,
                                                })
                                            }
                                            else {
                                                var varBrickType = {
                                                    orderId: this.state.selOrderId,
                                                    orderName: this.state.selOrderName,
                                                    brickAmount: this.state.brickAmount,
                                                };
                                                this.setState({
                                                    selOrderTypeList: this.state.selOrderTypeList.concat(varBrickType)
                                                })
                                            }

                                        }

                                        this.setState({
                                            orderModal: false,
                                            selOrderId: null,
                                            selOrderName: null,
                                            brickAmount: null,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>

                        </View>
                        <View>

                        </View>
                    </Modal>

                    <View style={styles.btnRowView}>
                        <TouchableOpacity onPress={() => {   
                            if (!this.state.contractId) {
                                let toastOpts = getFailToastOpts("您还没有选择合同名称");
                                WToast.show(toastOpts);
                                return;
                            }     
                            if (this.state.orderDataSource && this.state.orderDataSource.length > 0) {
                                this.setState({
                                    _orderDataSource: copyArr(this.state.orderDataSource),
                                })
                            }                   
                            this.setState({
                                errorMsg: null,
                                orderModal: true,
                                selOrderId:"",
                                selOrderName:""
                            })      
                            if (!this.state.selOrderId && this.state.orderDataSource && this.state.orderDataSource.length > 0) {
                                this.setState({
                                    selOrderId: this.state.orderDataSource[0].orderId,
                                    selOrderName: this.state.orderDataSource[0].orderName,
                                })
                            }                     
                        }}>
                            <View style={[styles.btnAddView,{marginBottom:5,backgroundColor: '#F2F5FC'}]}>
                                <Text style={[styles.btnAddText,{color: '#404956'}]}>+新增产品</Text>
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity onPress={() => {
                            if (!this.state.selOrderTypeList) {
                                WToast.show({ data: "没有可删除的，请先指定产品" });
                                return;
                            }
                            this.setState({
                                selOrderTypeList: this.state.selOrderTypeList.slice(0, -1)
                            })
                        }}>
                            <View style={[styles.btnDeleteView,{marginBottom:5}]}>
                                <Text style={styles.btnDeleteText}>-删除</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>完成套数</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText,{borderWidth:0,}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ completedSetNumber: text })}
                        >
                            {this.state.completedSetNumber}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>预砌工人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={[styles.inputRightText ,{borderWidth:0}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ blockworkWorker: text })}
                        >
                            {this.state.blockworkWorker}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View style={{height:ifIphoneXContentViewHeight()-359-105, backgroundColor:'#F2F5FC'}}>
                        {/* <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                        >
                        </TextInput> */}
                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle, { marginTop: 0 ,height:60}]}>
                        <TouchableOpacity onPress={() => {
                            this.props.navigation.goBack()
                        }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveBlockStorageIn.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>


                <BottomScrollSelect
                ref={'SelectContract'}
                callBackContractValue={this.callBackContractValue.bind(this)}
                />
            </KeyboardAvoidingView>
        )
    }
}

const styles = StyleSheet.create({
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    // inputRowStyle:{
    //     height:45,
    //     flexDirection:'row',
    //     marginTop:10,
    //     // flex: 1,
    //     // justifyContent: 'space-between',
    //     // alignContent:'center'
    //     // backgroundColor:'#000FFF',
    //     // width:screenWidth,
    //     // alignContent:'space-between',
    //     // justifyContent:'center'
    // },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#1E6EFA', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})