import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class MaterialPurchaseAdd extends Component {
    constructor(){
        super()
        this.state = {
            purchaseId: "",
            classifyId: "",
            amount: "",
            supplier:"",
            budget: "",
            operator: "",
            operate:"",
            materialClassifyDataSource:[],
            materialNameDataSource:[],
            reviewerName:"",
            reviewerId:"",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadreviewer();
        let loadTypeUrl;
        let loadRequest;
        // 原料类别
        loadTypeUrl= "/biz/material/classify/list";
        loadRequest={
            'currentPage':1,
            'pageSize':100,
            "parentClassifyId": null
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    materialClassifyDataSource:response.data.dataList,
                })
            }
        });

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { purchaseId, classifyId } = route.params;
            if (classifyId) {
                this.setState({
                    classifyId:classifyId,
                })
            }
            if (purchaseId) {
                console.log("========Edit==purchaseId:", purchaseId);
                this.setState({
                    purchaseId:purchaseId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/material/purchase/get";
                loadRequest={'purchaseId':purchaseId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditPurchaseDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }

        

        
    }

    loadreviewer = ()=>{
        let loadTypeUrl= "/biz/material/purchase/reviewer";
        let loadRequest={
            "operaterId": constants.loginUser.userId
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    reviewerName:response.data.userName,
                    reviewerId:response.data.userId,
                })
            }
        });
    }

    loadEditPurchaseDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                purchaseId:response.data.purchaseId,
                classifyId: response.data.classifyId,
                amount: response.data.amount,
                supplier: response.data.supplier,
                budget: response.data.budget,
                operator: response.data.operator,
                parentClassifyId:response.data.parentClassifyId,
            })
            // 调接口查询原料名称
            let loadTypeUrl;
            let loadRequest;
            loadTypeUrl= "/biz/material/classify/list";
            loadRequest={
                'currentPage':1,
                'pageSize':100,
                "parentClassifyId":response.data.parentClassifyId
            };
            httpPost(loadTypeUrl, loadRequest, this.materialNameLoadCallBack);
        }
    }

    materialNameLoadCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                materialNameDataSource:response.data.dataList,
            })
            console.log("=============this.state.materialNameDataSource:", this.state.materialNameDataSource);
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>新增采购</Text>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.checkId) {
                    return;
                }
                    this.setState({
                        classifyId:item.classifyId,
                    })
                }}>
                <View key={item.orderId} style={[item.classifyId===this.state.classifyId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.classifyId===this.state.classifyId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.classifyName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }


    saveContract =()=> {
        console.log("=======saveContract");
        let toastOpts;
        if (!this.state.classifyId) {
            toastOpts = getFailToastOpts("请选择原料名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.amount) {
            toastOpts = getFailToastOpts("请输入数量");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.supplier) {
        //     toastOpts = getFailToastOpts("请输入供应商");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url= "/biz/material/purchase/add";
        if (this.state.purchaseId) {
            console.log("=========Edit===purchaseId", this.state.purchaseId)
            url= "/biz/material/purchase/modify";
        }
        let requestParams={
            "purchaseId": this.state.purchaseId,
            "classifyId": this.state.classifyId,
            "amount": this.state.amount,
            "supplier": this.state.supplier,
            "budget": this.state.budget,
            "operator": constants.loginUser.userName,
            "operatorId":constants.loginUser.userId,
            "currentAuditUserId": this.state.reviewerId,
        };
        httpPost(url, requestParams, this.savePurchaseCallBack);

    }
    
    // 保存回调函数
    savePurchaseCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '采购'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    {/* <View style={styles.rowLabView}>
                        <Text style={styles.leftLabNameTextStyle}>原料大类</Text>
                    </View> */}
                    
                    {/* <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.materialClassifyDataSource && this.state.materialClassifyDataSource.length > 0) 
                            ? 
                            this.state.materialClassifyDataSource.map((item, index)=>{
                                return this.renderRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View> */}
                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>原料类别</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>

                    <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.95, justifyContent:'flex-start'}]}>
                    {this.state.materialClassifyDataSource.map((item, key)=>{
                        return(
                            <TouchableOpacity onPress={()=>{
                                this.setState({
                                    parentClassifyId:item.classifyId,
                                    classifyId:null,
                                })
                                WToast.show({data:'点击了' + item.classifyName});
                                // 调接口查询原料名称
                                let loadTypeUrl;
                                let loadRequest;
                                loadTypeUrl= "/biz/material/classify/list";
                                loadRequest={
                                    'currentPage':1,
                                    'pageSize':100,
                                    "parentClassifyId": item.classifyId
                                };
                                httpPost(loadTypeUrl, loadRequest, (response)=>{
                                    if (response.code == 200 && response.data && response.data.dataList) {
                                        this.setState({
                                            materialNameDataSource:response.data.dataList,
                                        })
                                    }
                                });

                            }}>
                                <View key={item.classifyId} style={[item.classifyId===this.state.parentClassifyId ? 
                                    // CommonStyle.selectedBlockItemViewStyle 
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    : 
                                    // CommonStyle.blockItemViewStyle
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginLeft:16,
                                        // marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 40,
                                        //width: (screenWidth - 54)/3,
                                        borderRadius: 4
                                    }
                                    ] }>
                                    <Text style={[item.classifyId===this.state.parentClassifyId ? 
                                        // CommonStyle.selectedBlockItemTextStyle16 
                                        {
                                            color: '#1E6EFA'
                                        }
                                        : 
                                        // CommonStyle.blockItemTextStyle16 
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                        {
                                            fontSize: 16, textAlign : 'center'
                                        },
                                        {
                                            paddingLeft:5,paddingRight:5
                                        }
                                        ]}>
                                        {item.classifyName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                            
                        )
                    })}
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>原料名称</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    
                    <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.90, justifyContent:'flex-start'}]}>
                        {!this.state.materialNameDataSource || this.state.materialNameDataSource.length < 1 ? 
                        <View style={{width:screenWidth}}><EmptyRowViewComponent/></View> 
                        : <Text></Text>} 
                    {this.state.materialNameDataSource.map((item, key)=>{
                        return(
                            <TouchableOpacity onPress={()=>{
                                this.setState({
                                    classifyId:item.classifyId,
                                    classifyName:item.classifyName,
                                })
                                WToast.show({data:'点击了' + item.classifyName});
                            }}>
                                <View key={item.classifyId} style={[item.classifyId===this.state.classifyId ? 
                                    // CommonStyle.selectedBlockItemViewStyle
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1} 
                                    : 
                                    // CommonStyle.blockItemViewStyle
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginLeft:16,
                                        // marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        //width: (screenWidth - 54)/3,
                                        borderRadius: 4
                                    }
                                    ] }>
                                    <Text style={[item.classifyId===this.state.classifyId ? 
                                        // CommonStyle.selectedBlockItemTextStyle16 
                                        {
                                            color: '#1E6EFA'
                                        }                                        
                                        : 
                                        // CommonStyle.blockItemTextStyle16 
                                        {
                                            color: '#404956'
                                        }
                                        ,                                        
                                        {
                                            fontSize: 16, textAlign : 'center'
                                        },
                                        {
                                            paddingLeft:5,paddingRight:5
                                        }
                                        ]}>
                                        {item.classifyName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                            
                        )
                    })}
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>重量(吨)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({amount:text})}
                        >
                            {this.state.amount}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>供应商</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入供应商'}
                            onChangeText={(text) => this.setState({supplier:text})}
                        >
                            {this.state.supplier}
                        </TextInput>
                    </View> */}
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>预计费用</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入预计费用'}
                            onChangeText={(text) => this.setState({budget:text})}
                        >
                            {this.state.budget}
                        </TextInput>
                    </View> */}
                    {
                        this.state.reviewerName?
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>审核人</Text>
                            </View>
                            <TextInput 
                                editable={false}
                                style={styles.inputRightText}
                                placeholder={'请输入'}
                                onChangeText={(text) => this.setState({reviewerName:text})}
                            >
                                {this.state.reviewerName}
                            </TextInput>
                        </View>
                        :
                        <View/>
                    }
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    <View style={{height:ifIphoneXContentViewHeight()-225-86-140, backgroundColor:'#F2F5FC'}}>

                    </View>
                    
                    <View style={[CommonStyle.blockAddCancelSaveStyle, { marginTop: 0}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveContract.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:5,
        marginRight:5,
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})