import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,ScrollView,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 160;

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class ProductionQtyQuery extends Component {
    constructor(props) {
        super(props);
        this.state = {
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight: 0,
            selCategoryChooseName:"全部",
            selCategoryChooseCode:"",
            qryStartTime:null,
            selectedQryStartDate:[],
            qryEndTime:null,
            selectedQryEndDate:[],
            semiFinishedList:[],
            encastageList:[],
            goodsList:[],
            wasteList:[],
            semiFinishedTotalWeight:"",
            encastageTotalWeight:"",
            goodsTotalWeight:"",
            wasteTotalWeight:"",
        }
    }

    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }

        

        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var qryEndTime = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectedQryEndDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            qryEndTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })

        var dateString = this.state.qryEndTime +' 00:00:01';
        dateString = dateString.substring(0,19);    
        dateString = dateString.replace(/-/g,'/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var SevenDaysLast = new Date(dateStringTimestamp);
        //获取当前时间的毫秒数
        var nowMilliSeconds = currentDate.getTime();
        // 用获取毫秒数 加上七天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        SevenDaysLast.setTime(nowMilliSeconds-(8*86400000));
        //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
        //格式化月，如果小于9，前面补0  
        var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0  
        var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
        var qryStartTime = SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay;
        this.setState({
            selectedQryStartDate:[SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
            qryStartTime:SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
        })

        let categoryChooseDataSource = [
            {
                chooseCode:'all',
                chooseName:'全部',
            },
            {
                chooseCode:'semifinished',
                chooseName:'成型',
            },
            {
                chooseCode:'encastage',
                chooseName:'入窑',
            },
            {
                chooseCode:'goods',
                chooseName:'正品',
            },
            {
                chooseCode:'waste',
                chooseName:'废品',
            },
        ]

        this.setState({
            categoryChooseDataSource:categoryChooseDataSource,
        })


        this.loadOutputList(qryStartTime,qryEndTime);
    }

    loadOutputQryList = () => {
        let url = "/biz/output/get";
        let loadRequest = {
            "qryStartDate": this.state.qryStartTime,
            "qryEndDate": this.state.qryEndTime,
            "category":(this.state.selCategoryChooseName == '全部') ? null : this.state.selCategoryChooseCode,
        };
        httpPost(url, loadRequest, this.loadOutputListCallBack);
    }

    loadOutputList = (qryStartTime,qryEndTime) => {
        let url = "/biz/output/get";
        let loadRequest = {
            "qryStartDate": qryStartTime,
            "qryEndDate": qryEndTime,
            "category":(this.state.selCategoryChooseName == '全部') ? null : this.state.selCategoryChooseCode,
        };
        httpPost(url, loadRequest, this.loadOutputListCallBack);
    }

    loadOutputListCallBack = (response) => {
        if (response.code == 200 && response.data) {
            console.log("========" + response.data.semiFinishedList)
            this.setState({
                semiFinishedList:response.data.semiFinishedList,
                encastageList:response.data.encastageList,
                goodsList:response.data.goodsList,
                wasteList:response.data.wasteList,
                semiFinishedTotalWeight:response.data.semiFinishedTotalWeight,
                encastageTotalWeight:response.data.encastageTotalWeight,
                goodsTotalWeight:response.data.goodsTotalWeight,
                wasteTotalWeight:response.data.wasteTotalWeight,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 

                }}  >
                    {/* <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
                </TouchableOpacity>
            </View>
        )
    }

    openQryStartDate(){
        this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate)
    }

    openQryEndDate(){
        this.refs.SelectQryEndDate.showDate(this.state.selectedQryEndDate)
    }

    callBackSelectQryStartDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryStartDate:value
        })
        if (value && value.length) {
            var qryStartTime = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    qryStartTime += vartime;
                }
                else{
                    qryStartTime += "-" + vartime;
                }
            }
            this.setState({
                qryStartTime:qryStartTime
            })
        }
    }

    callBackSelectQryEndDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryEndDate:value
        })
        if (value && value.length) {
            var qryEndTime = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    qryEndTime += vartime;
                }
                else{
                    qryEndTime += "-" + vartime;
                }
            }
            this.setState({
                qryEndTime:qryEndTime
            })
        }
    }

    categoryChooseStateRow=(item, index)=>{
        return (
            <View key={item.chooseCode} >
                <TouchableOpacity onPress={()=>{
                    var selCategoryChooseName = item.chooseName;
                    var selCategoryChooseCode = item.chooseCode;
                    console.log("=========" + selCategoryChooseCode);
                    this.setState({
                        selCategoryChooseName:selCategoryChooseName,
                        selCategoryChooseCode:selCategoryChooseCode,
                    })

                    let loadUrl= "/biz/output/get";
                    let loadRequest={
                        "qryStartDate": this.state.qryStartTime,
                        "qryEndDate": this.state.qryEndTime,
                        "category":(selCategoryChooseName === '全部') ? null : selCategoryChooseCode,
                    };
                    httpPost(loadUrl, loadRequest, this.loadOutputListCallBack);
                }}>
                    <View key={item.chooseCode} style={[{width: screenWidth/5 , height: 49, flexDirection: 'row', justifyContent: 'center'}
                    // ,item.stateCode === this.state.selCompletionStateCode ?
                    //     [styles.selectedBlockItemViewStyle]
                    //     :
                    //     [styles.blockItemViewStyle],
                    ]}>
                        <Text style={[item.chooseName === this.state.selCategoryChooseName ?
                            { color: "#255BDA", fontSize: 16, fontWeight: '500', lineHeight: 49, textAlign: 'center', borderColor: "#255BDA", borderBottomWidth: 2, paddingLeft: 5, paddingRight: 5 }
                            :
                            { color: "#2B333F", fontSize: 16, fontWeight: '500', lineHeight: 49, textAlign: 'center'},
                        ]}>
                            {item.chooseName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    // _loadFreshDataCallBack = (response) => {
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             semiFinishedList:response.data.semiFinishedList,
    //             encastageList:response.data.encastageList,
    //             goodsList:response.data.goodsList,
    //             wasteList:response.data.wasteList,
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }

    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadOutputList();
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // _loadFreshData = () => {
    //     if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
    //         return;
    //     }
    //     this.setState({
    //         currentPage: 1
    //     })
    //     let url = "/biz/output/get";
    //     let loadRequest = {
    //         "qryStartDate": this.state.qryStartTime,
    //         "qryEndDate": this.state.qryEndTime,
    //     };
    //     httpPost(url, loadRequest, this.loadOutputListCallBack);
    // }


    render(){
        return(
            <View>
                <CommonHeadScreen title='产量查询'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <View style={[styles.innerViewStyle,{marginTop:0,paddingBottom:5}]} onLayout={this.topBlockLayout.bind(this)}>

                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.categoryChooseDataSource && this.state.categoryChooseDataSource.length > 0)
                                ?
                                this.state.categoryChooseDataSource.map((item, index) => {
                                    return this.categoryChooseStateRow(item)
                                })
                                : <View />
                        }
                    </View>

                    <View style={[{marginTop:5, index:1000, flexWrap:'wrap', flexDirection:'row'}]}>
                        {/* <View style={[CommonStyle.inputTextStyleViewStyle,{marginRight:0}]}>
                            <Text style={CommonStyle.blockItemTextStyle16}>日期:</Text>
                        </View> */}
                        <TouchableOpacity onPress={()=>this.openQryStartDate()}>
                            <View style={[CommonStyle.inputTextStyleViewStyle,
                                {marginRight:0,marginLeft:0,paddingLeft:5,paddingRight:5,alignItems:'center',width: screenWidth/3, backgroundColor:'#F2F5FC',borderWidth: 0,}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.qryStartTime ? "起始日期" : this.state.qryStartTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                        <View style={[CommonStyle.inputTextStyleViewStyle,{marginRight:0,marginLeft:0,borderWidth: 0}]}>
                            <Text style={CommonStyle.blockItemTextStyle16}>--</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openQryEndDate()}>
                            <View style={[CommonStyle.inputTextStyleViewStyle,
                                {marginRight:0,marginLeft:0,paddingLeft:5,paddingRight:5,alignItems:'center',width:screenWidth/3, backgroundColor:'#F2F5FC',borderWidth: 0}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.qryEndTime ? "结束日期" : this.state.qryEndTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{this.loadOutputQryList()}}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/5,marginTop: 0, marginRight: 0,height:40,marginLeft:13}]}>
                                <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontSize:17}]}>查询</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>

                <ScrollView style={[CommonStyle.contentViewStyle,{height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    
                    {
                        (this.state.semiFinishedList && this.state.semiFinishedList.length > 0) 
                        ? 
                        <View>
                            <View style={[CommonStyle.addItemSplitRowView,{justifyContent:"center",marginTop:0}]}>
                                <Text style={[CommonStyle.addItemSplitRowText,{color:'#237a28'}]}>成型产量</Text>
                            </View>
                            <View style={[styles.itemContentViewStyle,{  }]}>
                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>合同名称</Text>
                                </View>
                                <View style={styles.itemContentRightChildViewStyle}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>产量</Text>
                                </View>
                            </View>
                            <View style={[CommonStyle.lineBorderBottomStyle,{ marginLeft:0,marginTop:20}]} />

                            {
                                this.state.semiFinishedList.map((semiFinishedItem, index)=>{
                                    return(
                                        
                                        <View key={semiFinishedItem.contractName} style={styles._innerViewStyle}>
                                            <View style={[styles.itemContentViewStyle,{marginTop:5}]}>
                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>{semiFinishedItem.contractName}</Text>
                                                </View>
                                                <View style={styles.itemContentRightChildViewStyle}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>{semiFinishedItem.produceWeight}吨</Text>
                                                </View>
                                            </View>
                                        </View>
                                    )                           
                                })
                            }
                            <View style={[CommonStyle.lineBorderBottomStyle,{ marginLeft:0,marginTop:30}]} />

                            <View style={[styles.itemContentViewStyle,{ marginTop:15 ,marginBottom:20}]}>
                                <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>合计</Text>
                                </View>
                                <View style={styles.itemContentRightChildViewStyle}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>{this.state.semiFinishedTotalWeight}吨</Text>
                                </View>
                            </View>
                        </View>
                        
                        : <View/>
                        
                    }


                    {/* 入窑产量 */}

                {
                    (this.state.encastageList && this.state.encastageList.length > 0) 
                    ? 

                    <View>
                         <View style={[CommonStyle.addItemSplitRowView,{justifyContent:"center"}]}>
                                <Text style={[CommonStyle.addItemSplitRowText,{color:'#237a28'}]}>入窑产量</Text>
                            </View>
                        <View style={[styles.itemContentViewStyle,{  }]}>
                            <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>合同名称</Text>
                            </View>
                            <View style={styles.itemContentRightChildViewStyle}>
                                <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>产量</Text>
                            </View>
                        </View>
                        <View style={[CommonStyle.lineBorderBottomStyle,{ marginLeft:0,marginTop:20}]} />

                         {
                            this.state.encastageList.map((encastageItem, index)=>{
                                return(
                                    <View key={encastageItem.contractName} style={styles._innerViewStyle}>
                                        <View style={[styles.itemContentViewStyle,{marginTop:5}]}>
                                            <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                                <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>{encastageItem.contractName}</Text>
                                            </View>
                                            <View style={styles.itemContentRightChildViewStyle}>
                                                <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>{encastageItem.produceWeight}吨</Text>
                                            </View>
                                        </View>
                                    </View>
                                )                           
                            })
                        }
                        <View style={[CommonStyle.lineBorderBottomStyle,{ marginLeft:0,marginTop:30}]} />

                        <View style={[styles.itemContentViewStyle,{ marginTop:15 }]}>
                            <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>合计</Text>
                            </View>
                            <View style={styles.itemContentRightChildViewStyle}>
                                <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>{this.state.encastageTotalWeight}吨</Text>
                            </View>
                        </View>

                    </View>
                    : <View/>
                    }

                    {/* 正品产量 */}

                    {
                        (this.state.goodsList && this.state.goodsList.length > 0) 
                        ? 
                        <View>
                            <View style={[CommonStyle.addItemSplitRowView,{justifyContent:"center"}]}>
                                <Text style={[CommonStyle.addItemSplitRowText,{color:'#237a28'}]}>正品产量</Text>
                            </View>
                            <View style={[styles.itemContentViewStyle,{  }]}>
                                <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>合同名称</Text>
                                </View>
                                <View style={styles.itemContentRightChildViewStyle}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>产量</Text>
                                </View>
                            </View>
                            {
                                this.state.goodsList.map((goodsItem, index)=>{
                                    return(
                                        
                                        <View key={goodsItem.contractName} style={styles._innerViewStyle}>
                                            <View style={[styles.itemContentViewStyle,{marginTop:5}]}>
                                                <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>{goodsItem.contractName}</Text>
                                                </View>
                                                <View style={styles.itemContentRightChildViewStyle}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>{goodsItem.produceWeight}吨</Text>
                                                </View>
                                            </View>
                                        </View>
                                    )                           
                                })
                            }
                            <View style={[styles.itemContentViewStyle,{ marginTop:15 }]}>
                                <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>合计</Text>
                                </View>
                                <View style={styles.itemContentRightChildViewStyle}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>{this.state.goodsTotalWeight}吨</Text>
                                </View>
                            </View>
                        </View>
                        
                        : <View/>
                        
                    }

                    {/* 废品产量 */}

                    {
                        (this.state.wasteList && this.state.wasteList.length > 0) 
                        ? 
                        <View>
                            <View style={[CommonStyle.addItemSplitRowView,{justifyContent:"center"}]}>
                                <Text style={[CommonStyle.addItemSplitRowText,{color:'#237a28'}]}>废品产量</Text>
                            </View>
                            <View style={[styles.itemContentViewStyle,{  }]}>
                                <View style={[styles.itemContentLeftChildViewStyle]}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>合同名称</Text>
                                </View>
                                <View style={styles.itemContentRightChildViewStyle}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>产量</Text>
                                </View>
                            </View>
                            {
                                this.state.wasteList.map((wasteItem, index)=>{
                                    return(
                                        
                                        <View key={wasteItem.contractName} style={styles._innerViewStyle}>
                                            <View style={[styles.itemContentViewStyle,{marginTop:5}]}>
                                                <View style={[styles.itemContentLeftChildViewStyle]}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>{wasteItem.contractName}</Text>
                                                </View>
                                                <View style={styles.itemContentRightChildViewStyle}>
                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>{wasteItem.produceWeight}吨</Text>
                                                </View>
                                            </View>
                                        </View>
                                    )                           
                                })
                            }
                            <View style={[styles.itemContentViewStyle,{ marginTop:15, marginBottom:10 }]}>
                                <View style={[styles.itemContentLeftChildViewStyle]}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>合计</Text>
                                </View>
                                <View style={styles.itemContentRightChildViewStyle}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>{this.state.wasteTotalWeight}吨</Text>
                                </View>
                            </View>
                        </View>
                        
                        : <View/>
                        
                    }
                    
                </ScrollView>

                <BottomScrollSelect 
                    ref={'SelectQryStartDate'} 
                    callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectQryEndDate'} 
                    callBackDateValue={this.callBackSelectQryEndDateValue.bind(this)}
                />

            </View>
        )
    }
}
const styles = StyleSheet.create({

    _innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:0,
    },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0
    },
    innerViewStyle: {
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        // borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    // itemContentViewStyle: {
    //     flexDirection: 'row',
    //     justifyContent: 'space-between',
    //     marginLeft: 25
    // },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginTop:10
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:screenWidth - 120,
    },
    itemContentRightChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:120,
    },
    // itemContentChildTextStyle:{
    //     // marginLeft:10,
    //     marginBottom:10,
    //     fontSize:16
    // },

});