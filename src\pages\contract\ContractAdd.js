import React, { Component } from 'react';
import { View, ScrollView, StatusBar, Text, TextInput, StyleSheet, FlatList, TouchableOpacity,Modal, Dimensions, Image, KeyboardAvoidingView} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { saveImage } from '../../utils/CameraRollUtils';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

import { uploadMultiImageLibrary } from '../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';

export default class ContractAdd extends Component {
    constructor() {
        super()
        this.state = {
            contractId: "",
            contractName: "",
            contractCode: "",
            partyA: "",
            customerName: "",
            partyB: constants.loginUser.tenantName,
            contractAmount: "",
            contractWeight: "",
            partyAContact: "",
            partyATel: "",
            partyBContact: "",
            partyBTel: "",
            signingTime: "",
            remark: "",
            operate: "",
            selectSigningTime: [],
            customerDataSource: [],
            selectCustomer: [],
            selectDeliveryDate: [],
            deliveryDate: "",
            actualWeight: "",
            actualAmount: "",
            enterpriseDataSource:[],
            selectEnterprise:[],
            enterpriseName:"",
            enterpriseId:"",
            compressFileList:[],
            modal:false,
            cusModal:false,
            _customerDataSource: [],
            selCustomerId:"",
            selCustomerName:"",
            selUserId:"",
            selUserName:"",
            searchKeyWord:"",
            userList:[],
            pictureIndex:0,
            isShowImage: false,     //  显示弹窗组件
            urls:[
                // {
                //     url:'http://10.162.210.158/image_a/10/2021-12/rn_image_picker_lib_temp_a45ead39-8c25-4fb5-b388-12be9d46cf62_200052052_small.jpg'
                // }
            ]
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        // 加载公司
        this.loadEnterpriseList();
        // 加载员工
        this.loadUserList();
        // 加载客户
        this.loadCustomerData();

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { contractId } = route.params;
            if (contractId) {
                console.log("========Edit==contractId:", contractId);
                this.setState({
                    contractId: contractId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/contract/get";
                loadRequest = { 'contractId': contractId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditContractDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectSigningTime: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    signingTime: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                })
                var dateString = this.state.signingTime + ' 00:00:01';
                dateString = dateString.substring(0, 19);
                dateString = dateString.replace(/-/g, '/');
                var dateStringTimestamp = new Date(dateString).getTime();
                // 根据毫秒数构建 Date 对象
                var SevenDaysLast = new Date(dateStringTimestamp);
                //获取当前时间的毫秒数
                var nowMilliSeconds = currentDate.getTime();
                // 用获取毫秒数 加上30天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
                SevenDaysLast.setTime(nowMilliSeconds + (30 * 86400000));
                //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
                //格式化月，如果小于9，前面补0
                var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
                //格式化日，如果小于9，前面补0
                var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
                this.setState({
                    selectDeliveryDate: [SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
                    deliveryDate: SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
                })
            }
        }
    }
    loadCustomerData=()=>{
        let loadTypeUrl = "/biz/tenant/customer/list";
        let loadRequest = { 'currentPage': 1, 'pageSize': 1000 };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadCustomerData);
    }
    callBackLoadCustomerData = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                customerDataSource: response.data.dataList,
                customerId:response.data.customerId,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    loadCustomer = () => {
        var _customerDataSource = copyArr(this.state.customerDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _customerDataSource = _customerDataSource.filter(item => item.customerName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _customerDataSource: _customerDataSource,
        })
    }

    loadUserList=()=>{
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this.loadUserListCallBack);
    }
    loadUserListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            this.setState({
                userList:data
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEnterpriseList=()=>{
        let url= "/biz/enterprise/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadEnterpriseListCallBack);
    }

    loadEnterpriseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            this.setState({
                enterpriseDataSource:data
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditContractDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectSigningTime = response.data.signingTime.split("-");
            var selectDeliveryDate = [];
            if (response.data.deliveryDate) {
                selectDeliveryDate = response.data.deliveryDate.split("-");
            }
            console.log("===========" + response.data.contractWeight)
            this.setState({
                contractId: response.data.contractId,
                contractName: response.data.contractName,
                contractCode: response.data.contractCode,
                partyA: response.data.partyA,
                customerName: response.data.customerName,
                enterpriseId:response.data.enterpriseId,
                // partyB: response.data.partyB ? response.data.partyB : constants.loginUser.tenantName,
                enterpriseName:response.data.enterpriseName ,
                selectEnterprise:[response.data.enterpriseName],
                signingTime: response.data.signingTime,
                contractAmount: response.data.contractAmount,
                contractWeight: response.data.contractWeight,
                partyAContact: response.data.partyAContact,
                partyATel: response.data.partyATel,
                selUserId: response.data.userName?response.data.partyBContact * 1:response.data.partyBContact,
                selUserName:response.data.userName,
                // partyBContact: response.data.partyBContact,
                partyBTel: response.data.partyBTel,
                remark: response.data.remark,
                selectSigningTime: selectSigningTime,
                deliveryDate: response.data.deliveryDate,
                actualWeight: response.data.actualWeight,
                actualAmount: response.data.actualAmount,
                selectDeliveryDate: selectDeliveryDate,
                compressFileList:response.data.compressFileList,
            })
            var urls = [];
            if(response.data.compressFileList && response.data.compressFileList.length > 0){
                for(var i=0;i<response.data.compressFileList.length;i++){
                    var url = {
                        url:constants.image_addr + '/' +  response.data.compressFileList[i].compressFile
                    }
                    urls=urls.concat(url)
                    console.log(url)
                }
            }
            this.setState({
                urls:urls
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={{ marginBottom: 1.5, flexDirection: 'row', alignItems: 'center'}}>
                    <Image style={{ width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')} />
                    <Text style={{ color: '#3C6CDE', marginLeft: 3, fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
                        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                        {/* <Text style={{ color: '#3C6CDE', marginLeft: 3, fontWeight:'bold'}}>返回</Text> */}
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("ContractList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>合同管理</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {}}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>合同管理</Text>
                        {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
                </TouchableOpacity>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    // 渲染客户底部滚动数据
    openCustomerSelect() {
        if (!this.state.customerDataSource || this.state.customerDataSource.length < 1) {
            WToast.show({ data: "请先添加客户" });
            return
        }
        this.refs.SelectCustomer.showCustomer(this.state.selectCustomer, this.state.customerDataSource)
    }
    callBackCustomerValue(value) {
        console.log("==========客户选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectCustomer: value
        })
        var customerName = value.toString();
        let loadUrl = "/biz/tenant/customer/getCustomerByName";
        let loadRequest = {
            "customerName": customerName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadCustomerDetailData);
    }
    callBackLoadCustomerDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                customerName: response.data.customerName,
                partyA: response.data.customerId,
                partyAContact: response.data.customerConcat,
                partyATel: response.data.customerTel,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    openSigningTime() {
        this.refs.SelectSigningTime.showDate(this.state.selectSigningTime)
    }
    callBackSelectSigningTimeValue(value) {
        console.log("==========签订时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectSigningTime: value
        })
        if (this.state.selectSigningTime && this.state.selectSigningTime.length) {
            var signingTime = "";
            var vartime;
            for (var index = 0; index < this.state.selectSigningTime.length; index++) {
                vartime = this.state.selectSigningTime[index];
                if (index === 0) {
                    signingTime += vartime;
                }
                else {
                    signingTime += "-" + vartime;
                }
            }
            this.setState({
                signingTime: signingTime
            })
        }
        var dateString = this.state.signingTime + ' 00:00:01';
        dateString = dateString.substring(0, 19);
        dateString = dateString.replace(/-/g, '/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var SevenDaysLast = new Date(dateStringTimestamp);
        // 用获取毫秒数 加上30天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        SevenDaysLast.setTime(dateStringTimestamp + (30 * 86400000));
        //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
        //格式化月，如果小于9，前面补0
        var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0
        var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
        this.setState({
            selectDeliveryDate: [SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
            deliveryDate: SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
        })
        if (this.state.selectDeliveryDate && this.state.selectDeliveryDate.length) {
            var deliveryDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectDeliveryDate.length; index++) {
                vartime = this.state.selectDeliveryDate[index];
                if (index === 0) {
                    deliveryDate += vartime;
                }
                else {
                    deliveryDate += "-" + vartime;
                }
            }
            this.setState({
                deliveryDate: deliveryDate
            })
        }
    }
    openDeliveryDate() {
        this.refs.SelectDeliveryDate.showDate(this.state.selectDeliveryDate)
    }
    callBackSelectDeliveryDateValue(value) {
        console.log("==========交付时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectDeliveryDate: value
        })
        if (this.state.selectDeliveryDate && this.state.selectDeliveryDate.length) {
            var deliveryDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectDeliveryDate.length; index++) {
                vartime = this.state.selectDeliveryDate[index];
                if (index === 0) {
                    deliveryDate += vartime;
                }
                else {
                    deliveryDate += "-" + vartime;
                }
            }
            this.setState({
                deliveryDate: deliveryDate
            })
        }
    }

    // 渲染公司底部滚动数据
    openEnterpriseSelect() {
        if (!this.state.enterpriseDataSource || this.state.enterpriseDataSource.length < 1) {
            WToast.show({ data: "请先添加公司" });
            return
        }
        this.refs.SelectEnterprise.showEnterprise(this.state.selectEnterprise, this.state.enterpriseDataSource)
    }

    callBackEnterpriseValue(value) {
        console.log("==========公司选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectEnterprise: value
        })
        var enterpriseName = value.toString();
        // this.setState({
        //     enterpriseName:enterpriseName
        // })
        let loadUrl = "/biz/enterprise/getEnterpriseByName";
        let loadRequest = {
            "enterpriseName": enterpriseName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadEnterpriseDetailData);
    }

    callBackLoadEnterpriseDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                enterpriseName:response.data.enterpriseName,
                enterpriseId: response.data.enterpriseId,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    saveContract = () => {
        console.log("=======saveContract");
        let toastOpts;
        if (!this.state.contractName) {
            toastOpts = getFailToastOpts("请输入合同名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.partyA) {
            toastOpts = getFailToastOpts("请选择甲方");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.enterpriseName) {
            toastOpts = getFailToastOpts("请选择乙方");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.partyAContact) {
        //     toastOpts = getFailToastOpts("请输入甲方联系人");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.partyATel) {
        //     toastOpts = getFailToastOpts("请输入甲方联系电话");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.selUserId) {
            toastOpts = getFailToastOpts("请选择乙方联系人");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.partyBTel) {
            toastOpts = getFailToastOpts("请输入乙方联系电话");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.actualWeight) {
        //     toastOpts = getFailToastOpts("请输入实结重量");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url = "/biz/contract/add";
        if (this.state.contractId) {
            console.log("=========Edit===contractId", this.state.contractId)
            url = "/biz/contract/modify";
        }
        let requestParams = {
            contractId: this.state.contractId,
            contractName: this.state.contractName,
            contractCode: this.state.contractCode,
            contractAmount: this.state.contractAmount,
            contractWeight: this.state.contractWeight,
            signingTime: this.state.signingTime,
            partyA: this.state.partyA,
            partyB: this.state.enterpriseId,
            partyAContact: this.state.partyAContact,
            partyATel: this.state.partyATel,
            partyBContact: this.state.selUserId,
            partyBTel: this.state.partyBTel,
            remark: this.state.remark,
            deliveryDate: this.state.deliveryDate,
            actualWeight: this.state.actualWeight,
            actualAmount: this.state.actualAmount,
            compressFileList:this.state.compressFileList,
        };
        // console.log("=========requestParams", requestParams)
        httpPost(url, requestParams, this.saveContractCallBack);
    }

    // 保存回调函数
    saveContractCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                if (this.state.contractId) {
                    toastOpts = getSuccessToastOpts('保存成功，请确认合同实际回款数据是否需要调整');
                }
                else {
                    toastOpts = getSuccessToastOpts('保存完成');
                }
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    renderRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                // if (this.state.contractId) {
                //     return;
                // }
                this.setState({
                    selUserId: item.userId,
                    selUserName:item.userName,
                    partyBTel: item.userNbr,
                })
            }}>
                <View key={item.userId} style={[item.userId === this.state.selUserId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.userId === this.state.selUserId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.userName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    renderCustomerRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.contractId) {
                    return;
                }
                this.setState({
                    partyA: item.customerId,
                    customerName: item.customerName,
                    partyAContact:item.customerConcat,
                    partyATel:item.customerTel,
                })
            }}>
                <View key={item.customerId} style={[item.customerId === this.state.partyA ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.customerId === this.state.partyA ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.customerName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]}  behavior="padding">
                {/* <StatusBar
                    // animated={true} //指定状态栏的变化是否应以动画形式呈现。目前支持这几种样式：backgroundColor, barStyle和hidden
                    hidden={false}  //是否隐藏状态栏。
                    backgroundColor={'#FFFFFF'} //状态栏的背景色
                    // translucent={true}//指定状态栏是否透明。设置为true时，应用会在状态栏之下绘制（即所谓“沉浸式”——被状态栏遮住一部分）。常和带有半透明背景色的状态栏搭配使用。
                    barStyle={'dark-content'} // enum('default', 'light-content', 'dark-content')
                >
                </StatusBar> */}
                <CommonHeadScreen title={this.state.operate + '合同'} titleStyle={{color: 'rgba(43, 51, 63, 1)', fontSize: 20, fontWeight:'bold'}}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: -2 }} />
                <ScrollView style={[CommonStyle.formContentViewStyle]}>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>合同名称</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ contractName: text })}
                        >
                            {this.state.contractName}
                        </TextInput>
                    </View>
                    <View style={[CommonStyle.viewAddLineStyle,{marginTop:0}]} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>合同编号</Text>
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ contractCode: text })}
                        >
                            {this.state.contractCode}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>客户</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[(!this.state.customerDataSource || this.state.customerDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                // if (!this.state.labelEnable) {
                                //     return;
                                // }
                                if (this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                                    this.setState({
                                        _customerDataSource: copyArr(this.state.customerDataSource),
                                    })
                                }
                                this.setState({
                                    cusModal: true,
                                    searchKeyWord: ""
                                })

                                if (!this.state.partyA && this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                                    this.setState({
                                        partyA: this.state.customerDataSource[0].customerId,
                                        customerName: this.state.customerDataSource[0].customerName,
                                        partyAContact: this.state.customerDataSource[0].customerConcat,
                                        partyATel: this.state.customerDataSource[0].customerTel,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 10), flexWrap: 'wrap', borderWidth: 0}]}>
                                    {
                                        this.state.partyA && this.state.customerName ?
                                        <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>{this.state.customerName}</Text>
                                            :
                                        <Text style={[{ color: '#A0A0A0', fontSize: 15,}]}>选择客户</Text>
                                    }
                                    <Image style={{ width: 22, height: 22, position:'absolute', right: 10, top: 11 }} source={require('../../assets/icon/iconfont/arrowRight.png')}></Image>
                                </View>

                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                    {/* <View style={CommonStyle.rowSplitViewStyle}></View> */}
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.cusModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadCustomer();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._customerDataSource && this.state._customerDataSource.length > 0)
                                                ?
                                                this.state._customerDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderCustomerRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            cusModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.partyA) {
                                            let toastOpts = getFailToastOpts("您还没有选择客户");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            cusModal: false,
                                            // partyA: this.state.selCustomerId,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>乙方</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={() => this.openEnterpriseSelect()}>
                            <View style={[styles.inputInsideTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5) }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.enterpriseName ? "请选择" : this.state.enterpriseName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15 }} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>合同金额</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ contractAmount: text })}
                        >
                            {this.state.contractAmount}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>合同重量(吨)</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ contractWeight: text })}
                        >
                            {this.state.contractWeight}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>交付日期</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openDeliveryDate()}>
                            <View style={styles.inputInsideTextStyleTextStyle}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.deliveryDate ? "请选择交付时间" : this.state.deliveryDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>签订时间</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openSigningTime()}>
                            <View style={styles.inputInsideTextStyleTextStyle}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.signingTime ? "请选择签订时间" : this.state.signingTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>客户联系人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ partyAContact: text })}
                        >
                            {this.state.partyAContact}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>客户联系电话</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ partyATel: text })}
                        >
                            {this.state.partyATel}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>乙方联系人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[{flexWrap:'wrap'}, this.state.contractId? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                // if (this.state.contractId) {
                                //     return;
                                // }
                                this.setState({
                                    modal: true,
                                })

                                if (!this.state.selUserId && this.state.userList && this.state.userList.length > 0) {
                                    this.setState({
                                        selUserId: this.state.userList[0].userId,
                                        selUserName: this.state.userList[0].selUserName,
                                        partyBTel: this.state.userList[0].userNbr,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 10), flexWrap: 'wrap', borderWidth: 0}]}>
                                    {
                                        this.state.selUserId && this.state.selUserName ?
                                        <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold'}]}>{this.state.selUserName}</Text>
                                            :
                                        <Text style={[{ color: '#A0A0A0', fontSize: 15,}]}>选择乙方联系人</Text>
                                    }
                                    <Image style={{ width: 22, height: 22, position:'absolute', right: 10, top: 11 }} source={require('../../assets/icon/iconfont/arrowRight.png')}></Image>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <Modal
                            animationType={'slide'}
                            transparent={true}
                            onRequestClose={() => console.log('onRequestClose...')}
                            visible={this.state.modal}>
                            <View style={CommonStyle.fullScreenKeepOut}>
                                <View style={CommonStyle.modalContentViewStyle}>
                                    <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadUserList();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                    </View>
                                    <ScrollView style={{}}>
                                        <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                            {
                                                (this.state.userList && this.state.userList.length > 0)
                                                    ?
                                                    this.state.userList.map((item, index) => {
                                                        if (index < 1000) {
                                                            return this.renderRow(item)
                                                        }
                                                    })
                                                    : <EmptyRowViewComponent />
                                            }
                                        </View>
                                    </ScrollView>
                                    <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                        <TouchableOpacity onPress={() => {
                                            this.setState({
                                                modal: false,
                                            })
                                        }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                        </TouchableOpacity>
                                        <TouchableOpacity onPress={() => {
                                            if (!this.state.selUserId) {
                                                let toastOpts = getFailToastOpts("您还没有选择乙方联系人");
                                                WToast.show(toastOpts);
                                                return;
                                            }
                                            this.setState({
                                                modal: false,
                                            })
                                        }}>
                                            <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                                <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                                <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>
                        </Modal>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15, }} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>乙方联系电话</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ partyBTel: text })}
                        >
                            {this.state.partyBTel}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>实结重量(吨)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ actualWeight: text })}
                        >
                            {this.state.actualWeight}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>实结金额</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ actualAmount: text })}
                        >
                            {this.state.actualAmount}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />


                    <View style={[styles.inputRowStyle, { height: 100 }]}>
                    {/* <View style={[ styles.inputRowStyle ]}> */}
                        <View style={[styles.leftLabView, {alignSelf:'center'}]}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={[styles.leftLabNameTextStyle ]}>备注</Text>
                        </View>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[styles.inputRightText, { height: 100, textAlignVertical: 'center' }]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ remark: text })}
                        >
                            {this.state.remark}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15 }} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>附件</Text>
                        </View>
                    </View>

                        <View style={{marginLeft:15}}>

                            {
                                this.state.compressFileList && this.state.compressFileList.length > 0 ?
                                (
                                    <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                        {
                                            this.state.compressFileList.map((item,index) =>{
                                                return(
                                                    <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex'}]}>
                                                    <TouchableOpacity
                                                        style={{position:'absolute',left:110,top:-10,zIndex:1000}}
                                                        onPress={() => {
                                                            console.log("========deletePhoto")
                                                            var urls = this.state.urls;
                                                            var compressFileList = this.state.compressFileList;

                                                            urls.splice(index,1);
                                                            compressFileList.splice(index,1);
                                                            console.log(urls)
                                                            console.log(this.state.compressFileList)

                                                            this.setState({
                                                                urls:urls,
                                                                compressFileList:compressFileList
                                                            })
                                                        }}
                                                    >
                                                        <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/deleteRed.png')}></Image>

                                                    </TouchableOpacity>
                                                    <TouchableOpacity onPress={() => {
                                                        this.setState({
                                                            isShowImage:true,
                                                            pictureIndex:index
                                                        })
                                                        // uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                                        //     console.log("========imageUploadResponse", imageUploadResponse)
                                                        //     if (imageUploadResponse.code === 200) {
                                                        //         WToast.show({ data: "上传成功" });
                                                        //         let compressFileList = imageUploadResponse.data
                                                        //         this.setState({
                                                        //             compressFileList: compressFileList
                                                        //         })
                                                        //     }
                                                        //     else {
                                                        //         WToast.show({ data: imageUploadResponse.message });
                                                        //     }
                                                        // });

                                                    }}>
                                                        <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />

                                                    </TouchableOpacity>
                                                    <Modal visible={this.state.isShowImage} transparent={true}>
                                                        <ImageViewer onClick={()=>{this.setState({isShowImage:false})}}  index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                         onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls}
                                                         onSave={() => {
                                                            saveImage( this.state.urls[this.state.pictureIndex].url)
                                                         }}/>
                                                    </Modal>
                                                </View>
                                                )

                                            })
                                        }
                                        <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                            <TouchableOpacity onPress={() => {
                                                uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                                    console.log("========imageUploadResponse", imageUploadResponse)
                                                    if (imageUploadResponse.code === 200) {
                                                        WToast.show({ data: "上传成功" });
                                                        let compressFileList = imageUploadResponse.data
                                                        this.setState({
                                                            compressFileList: this.state.compressFileList.concat(compressFileList)
                                                        })
                                                        var urls = this.state.urls;
                                                        if(compressFileList && compressFileList.length > 0){
                                                            for(var i=0;i<compressFileList.length;i++){
                                                                var url = {
                                                                    url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                                }
                                                                urls=urls.concat(url)
                                                                console.log(url)
                                                            }
                                                        }
                                                        this.setState({
                                                            urls:urls
                                                        })
                                                    }
                                                    else {
                                                        WToast.show({ data: imageUploadResponse.message });
                                                    }
                                                });

                                            }}>
                                                <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                                    <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                                </View>
                                            </TouchableOpacity>
                                        </View>
                                    </View>

                                )

                                :
                                <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                <TouchableOpacity onPress={() => {
                                        uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                            console.log("========imageUploadResponse", imageUploadResponse)
                                            if (imageUploadResponse.code === 200) {
                                                WToast.show({ data: "上传成功" });
                                                let compressFileList = imageUploadResponse.data
                                                this.setState({
                                                    compressFileList: compressFileList
                                                })
                                                var urls = [];
                                                if(compressFileList && compressFileList.length > 0){
                                                    for(var i=0;i<compressFileList.length;i++){
                                                        var url = {
                                                            url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                        }
                                                        urls=urls.concat(url)
                                                        console.log(url)
                                                    }
                                                }
                                                this.setState({
                                                    urls:urls
                                                })
                                            }
                                            else {
                                                WToast.show({ data: imageUploadResponse.message });
                                            }
                                        });

                                    }}>
                                        <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                                <Image style={{height: 66, width:66}}  source={require('../../assets/icon/iconfont/addPhoto.png')} ></Image>
                                            {/* <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image> */}
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            }

                        </View>

                    <View style={[styles.blockAddCancelSaveStyle,{marginBottom:10}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveContract.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>


                    {/* <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0, marginTop: screenHeight-305,backgroundColor:'rgba(255, 255, 255, 1)',height:66,}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginLeft: 20,marginTop:8, width: (screenWidth - 56)/2}]} >
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveDepartment.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, {marginRight: 20, marginTop:8, width: (screenWidth - 56)/2}]}>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                        </View>
                    </TouchableOpacity>
                    </View> */}

                    <BottomScrollSelect
                        ref={'SelectSigningTime'}
                        callBackDateValue={this.callBackSelectSigningTimeValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectDeliveryDate'}
                        callBackDateValue={this.callBackSelectDeliveryDateValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectCustomer'}
                        callBackCustomerValue={this.callBackCustomerValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectEnterprise'}
                        callBackEnterpriseValue={this.callBackEnterpriseValue.bind(this)}
                    />
                </ScrollView>
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 4,
        marginBottom:4,
        marginLeft:15,
        // backgroundColor:'red'
        // borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        // paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        // borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputInsideTextStyleTextStyle: {
        width: screenWidth - (leftLabWidth + 5),
        // borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    },
    blockAddCancelSaveStyle:{
        flexDirection: 'row',
        marginTop: 10,
        // marginHorizontal: -1,
        marginBottom:10,
        justifyContent: 'space-between',
        width: screenWidth, 
        marginLeft: 0, 
        // marginTop: screenHeight-330,
        backgroundColor:'rgba(255, 255, 255, 1)',
        height:66,
    },
})
