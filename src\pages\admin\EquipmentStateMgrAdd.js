import React,{ Component } from 'react';
import {View, ScrollView,Image, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class EquipmentStateMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            equipmentId:"",
            equipmentName:null,
            equipmentState:"0AA",
            equipmentStateName:null,
            remark:"",
            pic:"",
            operate:"",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { recordId, equipmentId, equipmentName, equipmentStateName } = route.params;
            if (equipmentId) {
                this.setState({
                    equipmentId:equipmentId,
                })
            }
            if (equipmentName) {
                this.setState({
                    equipmentName:equipmentName,
                })
            }
            if (equipmentStateName) {
                this.setState({
                    equipmentStateName:equipmentStateName,
                })
            }
            if (recordId) {
                console.log("========Edit==recordId:", recordId);
                this.setState({
                    recordId:recordId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/equipment/record/get";
                loadRequest={'recordId':recordId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditEquipmentStateDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    loadEditEquipmentStateDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                equipmentState:response.data.equipmentState,
                remark:response.data.remark,
                pic:response.data.pic,
                equipmentId:response.data.equipmentId,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={{ marginBottom: 1.5, flexDirection: 'row', alignItems: 'center'}}>
                    <Image style={{ width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')} />
                    <Text style={{ color: '#3C6CDE', marginLeft: 3, fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
                        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                        {/* <Text style={{ color: '#3C6CDE', marginLeft: 3, fontWeight:'bold'}}>返回</Text> */}
            </View>
        )
    }
    
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     // this.props.navigation.navigate("EquipmentStateMgrList")
            //     this.props.navigation.navigate("EquipmentStateMgrList", 
            //     {
            //         // 传递参数
            //         equipmentId:this.state.equipmentId,
            //         equipmentName:this.state.equipmentName,
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Text style={CommonStyle.headRightText}>明细管理</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { 
                    // this.props.navigation.navigate("EquipmentStateMgrList")
                    // this.props.navigation.navigate("EquipmentStateMgrList", 
                    // {
                    //     // 传递参数
                    //     equipmentId:this.state.equipmentId,
                    //     equipmentName:this.state.equipmentName,
                    //     // 传递回调函数
                    //     refresh: this.callBackFunction 
                    // })
                }}>
                    <Text style={{color:'#FFFFFF'}}>明细管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveEquipmentState =()=> {
        console.log("=======saveEquipmentState");
        let toastOpts;
        if (!this.state.equipmentState) {
            toastOpts = getFailToastOpts("请勾选设备状态");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.remark) {
        //     toastOpts = getFailToastOpts("请输入描述");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url= "/biz/equipment/record/add";
        if (this.state.recordId) {
            console.log("=========Edit===recordId", this.state.recordId)
            url= "/biz/equipment/record/modify";
        }
        let requestParams={
            "equipmentId":this.state.equipmentId,
            "recordId":this.state.recordId,
            "equipmentState":this.state.equipmentState,
            "remark":this.state.remark
        };
        httpPost(url, requestParams, this.saveEquipmentStateCallBack);
    }
    
    // 保存回调函数
    saveEquipmentStateCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '明细'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>设备状态</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[styles.selectViewItem, (this.state.equipmentState === '0AA') ? 
                        {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                        : 
                        // ""
                        {backgroundColor: '#F2F5FC'}
                        ,
                        {
                            marginRight: 8,
                            marginTop: 4,
                            marginBottom: 4,
                            borderRadius: 4,
                            justifyContent: 'center',
                            alignContent: 'center',
                            height: 36,
                            paddingLeft:6,
                            paddingRight:6,
                            // width: (screenWidth - 54)/2,
                            borderRadius: 4,
                        }
                        ]}>
                            <TouchableOpacity onPress={()=>{
                                this.setState({
                                    equipmentState:"0AA",
                                })
                            }}>
                                <View style={styles.selectViewItem}>
                                    <Text style={[styles.selectTextItem,(this.state.equipmentState === '0AA') ? 
                                    {
                                        color: '#1E6EFA'
                                    }                                    
                                    : 
                                    {
                                        color: '#404956'
                                    } 
                                    ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }                                  
                                    ]}>正常</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={[styles.selectViewItem, (this.state.equipmentState === '0AB') ? 
                        {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                        : 
                        {backgroundColor: '#F2F5FC'}
                        ,
                        {
                            marginRight: 8,
                            marginTop: 4,
                            marginBottom: 4,
                            borderRadius: 4,
                            justifyContent: 'center',
                            alignContent: 'center',
                            height: 36,
                            paddingLeft:6,
                            paddingRight:6,
                            // width: (screenWidth - 54)/2,
                            borderRadius: 4,
                        }
                        ]}>
                            <TouchableOpacity onPress={()=>{
                                this.setState({
                                    equipmentState:"0AB",
                                })
                            }}>
                                <View style={styles.selectViewItem}>
                                    <Text style={[styles.selectTextItem,(this.state.equipmentState === '0AB') ? 
                                    {
                                        color: '#1E6EFA'
                                    }
                                    : 
                                    {
                                        color: '#404956'
                                    }                                    
                                    ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>维修</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={[styles.selectViewItem, (this.state.equipmentState === '0AC') ? 
                        // {backgroundColor:'red'} 
                        {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                        : 
                        {backgroundColor: '#F2F5FC'}
                        ,
                        {
                            marginRight: 8,
                            marginTop: 4,
                            marginBottom: 4,
                            borderRadius: 4,
                            justifyContent: 'center',
                            alignContent: 'center',
                            height: 36,
                            paddingLeft:6,
                            paddingRight:6,
                            // width: (screenWidth - 54)/2,
                            borderRadius: 4,
                        }
                        ]}>
                            <TouchableOpacity onPress={()=>{
                                this.setState({
                                    equipmentState:"0AC",
                                })
                            }}>
                                <View style={styles.selectViewItem}>
                                    <Text style={[styles.selectTextItem,(this.state.equipmentState === '0AC') ? 
                                    {
                                        color: '#1E6EFA'
                                    }                                    
                                    : 
                                    {
                                        color: '#404956'
                                    }
                                    ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>保养</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={[styles.inputRowStyle,{height:100,textAlignVertical:'center'}]}>
                        <View style={[styles.leftLabView,{height:100}]}>
                            <Text style={[styles.leftLabWhiteTextStyle]}>*</Text>
                            <Text style={[styles.leftLabNameTextStyle]}>状态说明</Text>
                        </View>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[styles.inputRightText,{height:100,textAlignVertical:'center'}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({remark:text})}
                        >
                            {this.state.remark}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    <View style={{height:ifIphoneXContentViewHeight()-154-80, backgroundColor:'#F2F5FC'}}>
                        {/* <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                        >
                        </TextInput> */}
                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveEquipmentState.bind(this)}>
                        <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },

    selectViewItem:{
        width:60, justifyContent:'center', alignItems:'center'
    },
    selectTextItem:{
        fontSize:18,
        fontWeight:'bold'
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        // marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:6,
        marginRight:5
    },
    leftLabWhiteTextStyle: {
        color: 'white',
        marginLeft: 6,
        marginRight: 5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})