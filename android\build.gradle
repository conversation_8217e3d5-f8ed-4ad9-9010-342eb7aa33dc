// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "30.0.3"
        minSdkVersion = 21
        compileSdkVersion = 30
        targetSdkVersion = 30
    }
    repositories {
        google()
        // jcenter()
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        jcenter { url 'http://maven.aliyun.com/nexus/content/repositories/jcenter' }
    }
    dependencies {
        // classpath("com.android.tools.build:gradle:3.5.3")
        classpath("com.android.tools.build:gradle:3.6.3")
        // classpath("com.android.tools.build:gradle:7.0.3")
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        mavenLocal()
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }

        google()
        // jcenter()
        jcenter { url 'http://maven.aliyun.com/nexus/content/repositories/jcenter' }
        maven { url 'http://maven.aliyun.com/nexus/content/repositories/releases/'}
        maven { url 'https://developer.huawei.com/repo/'}
        maven { url 'https://www.jitpack.io' }
    }
}
