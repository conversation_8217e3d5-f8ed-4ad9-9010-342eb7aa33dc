/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 * @flow
 */

import React, { Component } from 'react';

import {
    AppRegistry,
    StyleSheet,
    View
} from 'react-native';
import ScrollRefreshControl from "./ScrollRefreshControl";
import FlatListRefreshControl from "./FlatListRefreshControl";


export default class ReactNativeDemo extends Component {

    render() {
        return (
            <View style={[styles.flex,styles.bgColor]}>
                <FlatListRefreshControl/>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    flex: {
        flex: 1
    },
    bgColor: {
      backgroundColor: 'white'
    }
});