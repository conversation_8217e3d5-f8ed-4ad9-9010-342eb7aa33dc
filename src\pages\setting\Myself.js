import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import CrHeadScreen from '../../component/CrHeadScreen'

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
export default class Myself extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            personalHonor:"",
            collegeEvaluation:"",
            selExtList:[],
            staffId:"",
            staffName: "",
            staffSex: "",
            staffTel: "",
            staffBirthday: "",
            nationality: "",
            height: "",
            politicsStatus: "",
            englishLevelName: "",
            className: "",
            nativePlace: "",
            address: "",
            email: "",
            electronicPhotos:"",
            comprehensivePoint: "",
            education: "",
            professionalName:  "",
            graduateInstitutions:"",
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        this.loadStudentInterViewList(constants.loginUser.staffId);
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
            
        }
    }

    loadStudentInterViewList=(staffId) =>{

        let url = "/biz/cr/staff/get";
        let loadRequest = {
           "staffId" : staffId
        };
        httpPost(url, loadRequest, this._loadStudentInterViewListcallback);

    }

    _loadStudentInterViewListcallback=(response) =>{
        if (response.code == 200 && response.data) {
            this.setState({
            staffId: response.data.staffId,
            staffName: response.data.staffName,
            staffSex: response.data.staffSex == 'M' ? "男" : (response.data.staffSex == 'L' ? "女" : null),
            staffTel: response.data.staffTel,
            staffBirthday: response.data.staffBirthday,
            nationality: response.data.nationality,
            height: response.data.height,
            politicsStatus: response.data.politicsStatus,
            englishLevelName: response.data.englishLevelName,
            className: response.data.className,
            nativePlace: response.data.nativePlace,
            address: response.data.address,
            email: response.data.email,
            electronicPhotos: response.data.electronicPhotos,
            comprehensivePoint: response.data.comprehensivePoint/100,
            education: response.data.education,
            professionalName:  response.data.professionalName,
            personalHonor: response.data.personalHonor,
            collegeEvaluation: response.data.collegeEvaluation,
            selExtList:response.data.crStaffExtDTOList,
            graduateInstitutions:response.data.graduateInstitutions,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("TemplateMgrAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>新增模版</Text>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View style={{backgroundColor:'#F7FBFC'}}>
                {/* <CommonHeadScreen title='模版管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                /> */}
                <View style={CommonStyle.contentViewStyle,{backgroundColor:'#F7FBFC',height:screenHeight}}>

                <View style={[styles.inputRowStyle]}>
                        {
                            this.state.userPhotoUrl ?
                            <View>
                                <Image source={{ uri: this.state.userPhotoUrl }} style={{ height: 70, width:70,borderRadius:50}} />
                                    {
                                        this.state.staffSex == '男' ? 
                                        <View style={[styles.sexIcon]}>
                                            <Image source ={require('../../assets/icon/iconfont/man.png')} style={{ height: 15, width:15}}></Image>
                                        </View>
                                        :
                                        <View style={[styles.sexIcon]}>
                                            <Image source ={require('../../assets/icon/iconfont/woman.png')} style={{ height: 15, width:15}}></Image>
                                        </View>
                                    }       
                            </View>
                                      
                            :
                            <View>
                                <Image source ={require('../../assets/icon/iconfont/headPortrait.png')} style ={{width:70,height:70,borderRadius:50}}></Image>
                                {
                                        this.state.staffSex == '男' ? 
                                        <View style={[styles.sexIcon]}>
                                            <Image source ={require('../../assets/icon/iconfont/man.png')} style={{ height: 15, width:15}}></Image>
                                        </View>
                                        :
                                        <View style={[styles.sexIcon]}>
                                            <Image source ={require('../../assets/icon/iconfont/woman.png')} style={{ height: 15, width:15}}></Image>
                                        </View>
                                    }
                            </View>
                            
                        }

                        <View style={{marginLeft:20,flexDirection:'column'}}>
                            <View style={{flexDirection:'row',alignItems:'center',justifyContent:'center'}}>
                                <Text style={[styles.titleTextStyle,{fontSize:28,marginTop:0,color:'#000000D9'}]}>{this.state.staffName}</Text>
                                <TouchableOpacity style={{position:'absolute',left:screenWidth / 2.3,marginTop:-5}}
                                        onPress={() => {
                                            this.props.navigation.navigate("StudentMyInterViewPreview", 
                                            {

                                                // 传递回调函数  
                                                // refresh: this.callBackFunction
                                                staffId:constants.loginUser.staffId,  
                                            })
                                        }} 
                                        >
                                    <Text style={[styles.titleTextStyle,{color:'#000000A6',marginTop:0}]}>修改简历</Text>
                                    <Image source ={require('../../assets/icon/iconfont/crArrow.png')} style={{ position:'absolute',height: 30, width:30,right:-30,marginTop:-5}}></Image>
                                </TouchableOpacity>
                            </View>
                            <View style={{justifyContent:'center',marginTop:5}}>
                                <Text style={[styles.titleTextStyle,{color:'#000000A6',marginTop:0}]}>{this.state.graduateInstitutions}·{this.state.professionalName}</Text>
                            </View>
                        </View>
                    </View>
                    <View style={{backgroundColor:'#FFF',width:screenWidth}}>
                        <View style={styles.contentViewStyle}>
                            <TouchableOpacity style={{flexDirection:'row'}}
                                onPress={() => {
                                    this.props.navigation.navigate("ChangePassword", 
                                    {

                                        // 传递回调函数  
                                        // refresh: this.callBackFunction
                                        // staffId:constants.loginUser.staffId,  
                                    })
                                }} >
                                <Image source ={require('../../assets/icon/iconfont/crKey.png')} style={{height:18, width:18,marginTop:2,marginLeft:5}}></Image>
                                <Text style={styles.contentTextStyle}>修改密码</Text>
                                <Image source ={require('../../assets/icon/iconfont/crArrow.png')} style={{ position:'absolute',height: 35, width:35,right:20,marginTop:-5}}></Image>
                            </TouchableOpacity>
                        </View>
                    </View>
                    
                    <View style={{backgroundColor:'#FFF',width:screenWidth}}>
                        <View style={[styles.contentViewStyle,{marginTop:0}]}>
                            <TouchableOpacity style={{flexDirection:'row'}}
                                onPress={() => {
                                    this.props.navigation.navigate("Feedback", 
                                    {

                                        // 传递回调函数  
                                        // refresh: this.callBackFunction
                                        // staffId:constants.loginUser.staffId,  
                                    })
                                }} >
                                <Image source ={require('../../assets/icon/iconfont/crOpinion.png')} style={{height:18, width:18,marginTop:2,marginLeft:5}}></Image>
                                <Text style={styles.contentTextStyle}>意见反馈</Text>
                                <Image source ={require('../../assets/icon/iconfont/crArrow.png')} style={{ position:'absolute',height: 35, width:35,right:20,marginTop:-5}}></Image>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={CommonStyle.btnRowStyle,{display:'flex',justifyContent:'center',alignItems:'center',bottom:80,position:'absolute',zIndex:100,left:40}}>
                        <TouchableOpacity 
                        onPress={() => {
                                this.props.navigation.navigate("LoginView", 
                                {

                                    // 传递回调函数  
                                    // refresh: this.callBackFunction
                                    // staffId:constants.loginUser.staffId,  
                                })
                            }}>
                            <View style={[styles.btnRowRightSaveBtnView1, {width:screenWidth - 200,height:45,borderRadius:40,opacity:0.8 }]}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText,{color:'#FF0000',fontSize:15}}>退出登录</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    
                </View>

                
            </View>
        )
    }
}
const styles = StyleSheet.create({
    contentViewStyle:{
        width:screenWidth - 40,
        marginLeft:20,
        backgroundColor:'#FFF',
        borderBottomColor:'#EEE',
        borderBottomWidth:1,
        marginTop:20,
        height:60,
        // alignItems:'center',
        justifyContent:'center',
    },
    contentTextStyle:{
        color:'#000000D9',
        fontSize:15,
        marginLeft:10
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        backgroundColor:'#FFF',
        width:screenWidth,
        height:45,
        flexDirection:'row',
        paddingTop:35,
        height: 120 ,
        alignItems:'flex-start',
        justifyContent:'flex-start',
        marginLeft:20,
        borderBottomColor:'#EEE',
        borderTopColor:'#EEE',
        borderTopWidth:1,
        borderBottomWidth:1,
    },
    sexIcon:{
       position:'absolute',
       width:25,
       height:25,
       left:50,
       top:0,
       borderRadius:50,
       backgroundColor:'#1D80FF',
       borderColor:'#FFFFFF',
       borderWidth:1,
       zIndex:1000,
       justifyContent:'center',
       alignItems:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', width:100, alignItems:'center', alignContent:'flex-end', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:14,
        color:'#000000A6',
        marginTop:15
    },
    titleViewStyle:{
        width:screenWidth - 40,
        flexDirection:'column',
        justifyContent:'flex-start',
        marginLeft:20,
        marginRight:10,
        marginTop:10,
        marginBottom:5,
        borderTopColor:'#EEEEEE',
        borderTopWidth:1,
    },
    underTitleTextStyle:{
        width:screenWidth - 40,
        justifyContent:'flex-start',
        fontSize:15,
        marginTop:15
    },
    innerViewStyle:{
        marginTop:0,
    },
    photos:{
        width:150,
        height:200,
        borderWidth:0,
    },
    btnRowRightSaveBtnView1:{
        backgroundColor:'#FFFFFF',
        alignItems:'center',
        // alignContent:'center',
        justifyContent:'center',
        borderRadius:30,
        borderColor:'#EEE',
        borderWidth:1,
        flexDirection:'row',
        marginLeft:60,
        marginTop:15
    },
});