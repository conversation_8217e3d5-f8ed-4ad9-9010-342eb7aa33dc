import React,{Component} from 'react';
import {View, Text, StyleSheet, Image, FlatList,RefreshControl
    ,Dimensions, ScrollView, TouchableOpacity, Alert} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';

// 公共组件及样式
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import CommonHeadScreen from '../../component/CommonHeadScreen';

import {WToast} from 'react-native-smart-tip';

// 引入公共样式
// import CommonStyle from '../../assets/css/CommonStyle';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

class CustomerList extends Component{
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadCustomerList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/tenant/customer/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/tenant/customer/list";
        let data={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadCustomerList();
    }

    loadCustomerList=()=>{
        let url= "/biz/tenant/customer/list";
        let data={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, data, this.callBackLoadCustomerList);
    }

    callBackLoadCustomerList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            // dataAll[0]
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
            
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteCustomer =(customerId)=> {
        console.log("=======delete=customerId", customerId);
        let url= "/biz/tenant/customer/delete";
        let requestParams={'customerId':customerId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"成功删除"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }
    renderRow=(customerItem,index)=>{
        return (
            <View key={customerItem.customerId} style={styles.innerViewStyle}>
                {/* 
                    {
                        customerItem.flag!=null && customerItem.flag=='true'? 
                            <View/>
                            :
                            <View/>
                    }
                */}
                {
                    index == 0 ?
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>
                        :
                        <View></View>
                }
                <View style={styles.bodyViewStyleSpecial}>
                    <Text style={styles.texRowSpecial}>{customerItem.customerName}</Text>
                </View>
                <View style={styles.bodyViewStyleCommon}>
                    <Text style={styles.texRowCommon}>联系人：{customerItem.customerConcat ? customerItem.customerConcat : "无"}</Text>
                </View>
                <View style={styles.bodyViewStyleCommon}>
                    <Text style={styles.texRowCommon}>联系电话：{customerItem.customerTel ? customerItem.customerTel : "无"}</Text>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle,{marginRight:15}]}>
                    <TouchableOpacity onPress={()=>{
                        if (dateDiffHours(constants.nowDateTime, customerItem.gmtCreated) > constants.editDeleteTimeLimit) {
                            return;
                        }
                        Alert.alert('确认','您确定要删除该客户吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteCustomer(customerItem.customerId)
                                }
                            }
                        ]);
                        // console.log("@@@",JSON.stringify(this.state.dataSource,null,6))
                        // this.state.dataSource[0].flag=true
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle
                            ,{width:80,flexDirection:'row'},dateDiffHours(constants.nowDateTime, customerItem.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""]}>
                            <Image  style={{width:20, height:20,marginRight:5, tintColor: 'rgba(145, 147, 152, 1)'}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={{  color: 'rgba(145, 147, 152, 1)', fontSize: 16}}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>this.props.navigation.navigate("CustomerAdd",
                    {
                        customerId:customerItem.customerId,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })}>
                        <View style={[styles.itemEditBtnViewStyle,{width:80,flexDirection:'row'}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70 }}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View style={{ flexDirection: 'row-reverse', alignItems: 'center', width:70 }}>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("CustomerAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    });
                    }}>
                    <Image style={{ width:22, height:22, marginVertical: 2}} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }


    render(){
        return(
            <View>
                <View style={{justifyContent: 'center', alignItems: 'center' }}>
                    <CommonHeadScreen title='客户管理'  
                        leftItem={() => this.renderLeftItem()}
                        rightItem={() => this.renderRightItem()}
                    />
                </View>
                <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF'}}>
                {/* <View style={styles.searchTimeBoxWithExport11}>
                            <TouchableOpacity onPress={() => {}}>
                                <View style={{ alignItems: 'center', flexDirection: 'row', width: screenWidth / 1.9, borderRadius: 80}}>
                                    <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                    <Text style={{ color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 15 }}>
                                        搜索名称/型号/材质
                                    </Text>
                                </View>
                            </TouchableOpacity>
                </View> */}
                </View>
                <View style={CommonStyle.contentViewStyle}>
                    {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> */}
                        <FlatList 
                            data={this.state.dataSource}
                            ListEmptyComponent={this.emptyComponent}
                            renderItem={({item,index}) => this.renderRow(item,index)}
                            ItemSeparatorComponent={this.space}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                        />
                    {/* </ScrollView> */}
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10
    },
    titleTextStyle:{
        fontSize:23
    },
    bodyViewStyleSpecial:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:40,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyViewStyleCommon:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:40,
        marginRight:10,
        marginBottom:0,
        marginTop:0
    },
    bodyRowLeftView:{
        width:screenWidth/2-40, 
        flexDirection:'row'
    },
    bodyRowRightView:{
        flexDirection:'row', 
        alignItems:'flex-start',
        paddingLeft:10,
        marginRight:5, 
        justifyContent:'flex-start',
        alignContent:'flex-start'
    },
    searchTimeBoxWithExport11: {
        justifyContent: "space-between",
        flexDirection: 'row',
        alignItems: 'center',
        margin: 0,
        marginTop: 5,
        marginLeft: 0,
        marginBottom: 12,
        width: screenWidth / 1.2,
        backgroundColor: '#F2F5FC',
        // backgroundColor: '#4169E1',
        paddingLeft: 5,
        borderWidth: 0,
        borderColor: '#F2F5FC',
        height: 38,
        borderRadius: 4
    },
    texRowSpecial: {
        width: 400,
        height: 24,
        // fontFamily: 'PingFangSC',
        fontWeight: 'bold',
        fontSize: 20,
        color: '#404956',
        lineHeight: 24,
        textAlign: 'left',
        fontStyle: 'normal',
      },
      texRowCommon: {
        width:220,
        height: 24,
        // fontFamily: 'PingFangSC',
        fontWeight: '400',
        fontSize: 14,
        color: 'rgba(0,10,32,0.65)',
        lineHeight: 24,
        textAlign: 'left',
        fontStyle: 'normal',
      },
      itemEditBtnViewStyle: {
        fontSize: 16,
        width: 100,
        height: 30,
        borderWidth: 1,
        borderColor: '#255BDA',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        borderRadius: 6,
        backgroundColor:'#255BDA'
    },
})
module.exports = CustomerList;