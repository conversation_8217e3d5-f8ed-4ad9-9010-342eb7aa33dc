import React,{Component} from 'react';
import {View,Text,StyleSheet,Dimensions,TouchableOpacity,TextInput,Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
// var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class AuditConfigMgrAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            auditConfigId:"",
            topBlockLayoutHeight:0,
            auditConfigId:"",
            auditConfigName:"",
            auditConfigSort:0,
            auditTypeName:"",
            auditTypeCode:"",
            auditTypeDataSource:[],
            selectAuditType:[]
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        //加载申请类型
        this.loadAuditTypeList();

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { auditConfigId } = route.params;
            if (auditConfigId) {
                console.log("=============auditConfigId" + auditConfigId + "");
                this.setState({
                    auditConfigId:auditConfigId,
                    operate:"编辑"
                })
                let loadTypeUrl;
                let loadRequest;
                loadTypeUrl = "/biz/audit/config/get";
                loadRequest = { 'auditConfigId': auditConfigId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditAuditConfigDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }

    loadAuditTypeList=()=>{
        let loadTypeUrl = "/biz/audit/type/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(loadTypeUrl, loadRequest, this.callBackAuditTypeData);
    }

    callBackAuditTypeData=(response)=> {
        if (response.code == 200 && response.data) {
            this.setState({
                auditTypeDataSource: response.data
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    callBackAuditTypeValue(value) {
        console.log("=======申请类型选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectAuditType: value
        })
        var auditTypeName = value.toString();
        let loadUrl = "/biz/audit/type/getAuditTypeByName";
        let loadRequest = {
            "auditTypeName": auditTypeName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadAuditTypeDetailData);
    }

    callBackLoadAuditTypeDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                auditTypeName: response.data.auditTypeName,
                auditTypeCode: response.data.auditTypeCode,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    openAuditTypeSelect() {
        if (!this.state.auditTypeDataSource || this.state.auditTypeDataSource.length < 1) {
            WToast.show({ data: "请先设置申请类型" });
            return
        }
        this.refs.SelectAuditTypeList.showAuditType(this.state.selectAuditType, this.state.auditTypeDataSource)
    }

    loadEditAuditConfigDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                auditConfigId: response.data.auditConfigId,
                auditConfigName: response.data.auditConfigName,
                auditConfigSort: response.data.auditConfigSort,
                auditTypeName: response.data.auditTypeName,
                auditTypeCode: response.data.auditTypeCode,
                selectAuditType:[response.data.auditTypeName]
            })
        }
    }

    saveAuditConfig = () => {
        console.log("=======saveAuditConfig");
        let toastOpts;
        if (!this.state.auditConfigName) {
            toastOpts = getFailToastOpts("请输入审核名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.auditTypeName) {
            toastOpts = getFailToastOpts("请选择审核类型");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/audit/config/add";
        if (this.state.auditConfigId) {
            console.log("=========Edit===auditConfigId", this.state.auditConfigId)
            url = "/biz/audit/config/modify";
        }
        let requestParams = {
            auditConfigId: this.state.auditConfigId,
            auditConfigName: this.state.auditConfigName,
            auditConfigSort: this.state.auditConfigSort,
            auditTypeCode: this.state.auditTypeCode
        };
        httpPost(url, requestParams, this.saveAuditConfigCallBack);
    }

    // 保存回调函数
    saveAuditConfigCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("AuditConfigMgrList", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Text style={CommonStyle.headRightText}>审核配置</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>审核配置</Text>
                </TouchableOpacity>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '配置'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <View style={CommonStyle.contentViewStyle}>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>审核名称</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({auditConfigName:text})}
                        >
                            {this.state.auditConfigName}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>申请类型</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openAuditTypeSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5), borderWidth:0 }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.auditTypeName ? "请选择" : this.state.auditTypeName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'0'}
                            onChangeText={(text) => this.setState({auditConfigSort:text})}
                        >
                            {this.state.auditConfigSort}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    <View style={{height:ifIphoneXContentViewHeight()-159-90, backgroundColor:'#F2F5FC'}}>
                        
                    </View>
                    <View style={[ CommonStyle.blockAddCancelSaveStyle,{ marginTop: 0} ]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnAddCancelBtnView} >
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveAuditConfig.bind(this)}>
                            <View style={CommonStyle.btnAddSaveBtnView}>
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    {/* <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveAuditConfig.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                </View>
                <BottomScrollSelect
                        ref={'SelectAuditTypeList'}
                        callBackAuditTypeValue={this.callBackAuditTypeValue.bind(this)}
                    />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:6,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#FFFFFF',
        // borderWidth:1,
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
    },
    inputTextStyleTextStyle:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        height:45,
        justifyContent:'center'
    }
});