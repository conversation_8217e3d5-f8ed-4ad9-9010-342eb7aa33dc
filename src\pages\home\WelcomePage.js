import React,{Component} from 'react';
import {View, Text, StyleSheet, Image, FlatList,RefreshControl,TextInput
    ,Dimensions, ScrollView, TouchableOpacity, Alert,Animated} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';

// 公共组件及样式
import EmptyListComponent from '../../component/EmptyListComponent';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

import Swiper from 'react-native-swiper';
// import StickyHeader from 'react-native-StickyHeader';

import {WToast} from 'react-native-smart-tip';
// 引入公共样式
// import CommonStyle from '../../assets/css/CommonStyle';


var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
var cols = 3;
var cellWH = 100;
var vMargin = (screenWidth - cellWH * cols) / (cols + 1);
var hMargin = 10;


class WelcomePage extends Component{

    // static defaultProps = {
    //     stickyHeaderY: -1,
    //     stickyScrollY: new Animated.Value(0)
    //   }

    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight: 0,
            crTopBlockLayoutHeight:0,
            searchKeyWord:"",
            swiperDataSource:[],

   
            collegeRecruitingMenuDataSource:[
                {
                    "code":"cr_student_my_interview",
                    "icon": require('../../assets/icon/workbench/workbench_cr_student_my_interview.png'),
                    "title": "个人简历",
                    "component":"StudentMyInterView"
                },
                {
                    "code":"cr_student_interview_invited",
                    "icon": require('../../assets/icon/workbench/workbench_cr_student_interview_invited.png'),
                    "title": "面试邀请",
                    "component":"StudentInterViewInvited"
                },
                {
                    "code":"cr_student_my_chance",
                    "icon": require('../../assets/icon/workbench/workbench_cr_student_my_chance.png'),
                    "title": "我的机会",
                    "component":"StudentMyChance"
                },
            ]

            // scrollY: new Animated.Value(0),
            // stickyLayoutY: 0,
        }
        
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        // const translateY = ScrollY.interpolate({
        //     inputRange: [-1, 0, headerHeight, headerHeight + 1],
        //     outputRange: [0, 0, 0, 1],
        //   });

        this.loadswiperDataSource();
        this.loadPositionList();




        // var homeMenuDTO;
        // var homeMenuList;
        // for(var j = 0,len=constants.roleInfo.menuDTOList.length; j < len; j++) {
        //     homeMenuDTO = constants.roleInfo.menuDTOList[j];
        //     if (homeMenuDTO && homeMenuDTO.menuCode && "HomeStackScreen" === homeMenuDTO.menuCode) {
        //         homeMenuList =homeMenuDTO.menuDTOList;
        //         console.log("===HomeStackScreen==homeMenuDTO===", homeMenuDTO);
        //         break;
        //     }
        // }
        // if (!homeMenuList || homeMenuList.length <= 0) {
        //     console.log("=========首页子菜单是空的");
        //     return;
        // }
        // console.log("=========homeMenuList.length==", homeMenuList.length);
        // console.log("=========homeMenuList==", homeMenuList);
        // var roleHomeOneLevelMenuDTO;
        // for(var index = 0; index < homeMenuList.length; index++) {
        //     roleHomeOneLevelMenuDTO = homeMenuList[index];
        //     console.log("=========roleHomeOneLevelMenuDTO.menuCode==", roleHomeOneLevelMenuDTO.menuCode);
        //     if (!roleHomeOneLevelMenuDTO || !roleHomeOneLevelMenuDTO.menuDTOList || roleHomeOneLevelMenuDTO.menuDTOList.length <= 0) {
        //         console.log("========roleHomeSonMenuDTO是空的", roleHomeOneLevelMenuDTO);
        //         continue;
        //     }
        //     var roleSecondLevelMenuDTO;
        //     var homeSecondLevelMenuList = [];
        //     var sonMenuDTO;
        //     for(var secondLevelIndex = 0, len = roleHomeOneLevelMenuDTO.menuDTOList.length; secondLevelIndex < len; secondLevelIndex++) {
        //         roleSecondLevelMenuDTO = roleHomeOneLevelMenuDTO.menuDTOList[secondLevelIndex];
        //         sonMenuDTO = {
        //             "code":roleSecondLevelMenuDTO.menuCode,
        //             "icon": {uri:roleSecondLevelMenuDTO.menuIcon.replace("../../assets/icon", "http://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/react-native-network-app-images/icon")},
        //             "title": roleSecondLevelMenuDTO.menuName,
        //             "component":roleSecondLevelMenuDTO.menuUrl
        //         }
        //         homeSecondLevelMenuList = homeSecondLevelMenuList.concat(sonMenuDTO);
        //     }
        //     console.log("=====homeSecondLevelMenuList==", homeSecondLevelMenuList);

        //     if (roleHomeOneLevelMenuDTO.menuCode === "collegeRecruitingMenuDataSource") {
        //         this.setState({
        //             collegeRecruitingMenuDataSource:homeSecondLevelMenuList,
        //         })
        //     }


    // }
}

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    loadswiperDataSource = ()=>{
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10,
        };
        httpPost(url, loadRequest, this.loadswiperDataSourceCallBack);
    }

    loadswiperDataSourceCallBack = (response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                swiperDataSource:[...response.data.dataList],
            })
            var swiper = this.refs.swiper;

            swiper.scrollBy(0, false)
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }


    logout=()=>{
        console.log("===logout");
        let url = "/biz/user/logout?a=123&b=234"
        httpGet(url, this.logout_call_back);
    }

    logout_call_back=(response)=>{
        console.log("=====logout_call_back:", response);
        this.props.navigation.navigate('LoginView');
    }

        // 回调函数
    callBackFunction = () => {
        let url = "/biz/hiring/position/tenantAllPositionList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "staffId":constants.loginUser.staffId,
            "searchKeyWordHiringPosition":this.state.searchKeyWord,
            "positionState":"0AA",
            "positionType":this.state.selPositionType === "all" ? null : this.state.selPositionType
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
        
    }

    searchByKeyWord = () => {
        let url = "/biz/hiring/position/tenantAllPositionList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "staffId":constants.loginUser.staffId,
            "searchKeyWordHiringPosition":this.state.searchKeyWord,
            "positionState":"0AA",
            "positionType":this.state.selPositionType === "all" ? null : this.state.selPositionType
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);        
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/hiring/position/tenantAllPositionList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "staffId":constants.loginUser.staffId,
            "searchKeyWordHiringPosition":this.state.searchKeyWord,
            "positionState":"0AA",
            "positionType":this.state.selPositionType === "all" ? null : this.state.selPositionType
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

        // 上拉触底加载下一页
        _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            console.log("=====")
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadPositionList();
    }

    loadPositionList=()=>{
        let url = "/biz/hiring/position/tenantAllPositionList";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "staffId":constants.loginUser.staffId,
            "searchKeyWordHiringPosition":this.state.searchKeyWord,
            "positionState":"0AA",
            "positionType":this.state.selPositionType === "all" ? null : this.state.selPositionType
        };
        httpPost(url, loadRequest, this.loadPositionListCallBack);
    }

    loadPositionListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }


    applyInterview=(item)=>{
        let url = "/biz/collection/position/add";
        let loadRequest = {
            "staffId":constants.loginUser.staffId,
            "positionId":item.positionId,
            "applyPosition":item.applyState == 'Y'?"N":"Y"
        };
        httpPost(url, loadRequest, this.applyInterviewCallBack);
    }

    applyInterviewCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({ data: response.data.applyPosition == 'Y'?"申请面试成功":"取消申请成功" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={this.logout.bind(this)}>
                <Text style={CommonStyle.headRightText}>登出</Text>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    crTopBlockLayout = (event) => {
        this.setState({
            crTopBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    // // 兼容代码，防止没有传头部高度
    // _onLayout = (event) => {
    //     this.setState({
    //     stickyLayoutY: event.nativeEvent.layout.y,
    //     });
    // }

    renderRow=(item)=>{
        return (
            <View key={item.staffId} style={styles.crInnerViewStyle}>

                <TouchableOpacity onPress={()=>{
                    this.props.navigation.navigate("EnterprisecrHiringPositionDetail",
                    {
                        // 传递参数
                        positionId: item.positionId,
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
                }}>

                <View style={[{flexDirection:'row',flexWrap:'wrap',marginTop:5}]}>
                    <View style={{}}>
                        <Text style={styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18,color:'#000000D9'}}>{item.positionName}</Text>
                    </View>
                    <View style={{position:'absolute',right:40}}>
                        <Text style={styles.itemContentChildTextStyle,{color:'#1D80FF', fontSize:14}}>{item.positionSalaryFrom}k - {item.positionSalaryTo}k</Text>
                    </View>
                    
                </View>

                <View style={[{flexDirection:'row',flexWrap:'wrap',marginTop:5}]}>
                    <View style={{}}>
                        <Text style={styles.itemContentChildTextStyle,{color:'#000000A6', fontSize:12,backgroundColor:'#EFF6F9' }}>{item.positionTypeName}</Text>
                    </View>
                    <View style={{}}>
                        <Text style={styles.itemContentChildTextStyle,{color:'#000000A6', fontSize:12,backgroundColor:'#EFF6F9',marginLeft:10}}>{item.enterpriseArea}</Text>
                    </View>
                    
                </View>

                <View style={[{flexDirection:'row',flexWrap:'wrap',marginTop:5,marginBottom:10}]}>
                    <View style={{}}>
                        <Image style={styles.enterpriseLogoStyle} source={{uri:item.enterpriseLogo}}></Image>
                    </View>
                    <View style={{flexDirection:'column'}}>
                        <Text style={styles.itemContentChildTextStyle,{color:'#000000A6', fontSize:14,marginTop:15,marginLeft:12}}>{item.enterpriseName}</Text>
                        <Text style={styles.itemContentChildTextStyle,{color:'#2C2C2CA6', fontSize:12,marginTop:12,marginLeft:12}}>招{item.hiringNumber}人</Text>
                    </View>
                    
                </View>

                </TouchableOpacity>         
                
            </View>
        )
    }

    renderOrderRow=(item)=>{
        if ("hidden" ===item.visibility) {
            return;
        }
        // 登出
        if ("logout" === item.code) {
            return (
                <TouchableOpacity key={item.code} onPress={this.logout.bind(this)}>
                    <View key={item.code} style={styles.innerViewStyle}>
                        <Image style={styles.innerViewImageStyle} source={item.icon}></Image>
                        <Text style={CommonStyle.bodyTextStyle}>{item.title}</Text>
                    </View>
                </TouchableOpacity>
            )
        }
        return (
            <TouchableOpacity key={item.code} onPress={this._pressJump.bind(this, item)} >
                <View style={styles.innerOrderViewStyle}>
                    <Image style={styles.innerViewImageStyle} source={item.icon}></Image>
                    <Text style={CommonStyle.bodyTextStyle}>{item.title}</Text>
                </View>
            </TouchableOpacity>
            
        )
    }

    _pressJump(item) {
        const { navigation } = this.props;
        if(navigation && item.component != null) {
            navigation.navigate(item.component, {
                // 测试参数
                itemId: 1000000, 
                code:item.code,
                title: item.title
            })
        }
    }

    render(){
        // const { stickyHeaderY, stickyScrollY, children, style } = this.props
        // const { stickyLayoutY } = this.state
        // let y = stickyHeaderY != -1 ? stickyHeaderY : stickyLayoutY;
        // const translateY = stickyScrollY.interpolate({
        //     inputRange: [-1, 0, y, y + 1],
        //     outputRange: [0, 0, 0, 1],
        // });
        return(
            <View style={{backgroundColor:'#F7FBFC',alignItems:'center'}}>
 
                <View style={[styles.innerViewStyle,{marginTop:0,justifyContent:'center',alignItems:'center'}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{width:screenWidth - 30}}>
                        <View style={[styles.inputRowStyle,{backgroundColor:'#F4F6F8',borderRadius:20 }]}>
                            <View style={styles.leftLabView}>
                            <Image  style={{width:18, height:18}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                    returnKeyType="search"
                                    placeholderTextColor={'#28354C40'}
                                    returnKeyLabel="搜索"
                                    onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                    }}
                                placeholder={'请输入'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>
                </View>

                {/* <Animated.ScrollView
                    style={{ flex: 1 }}
                    onScroll={
                        Animated.event(
                            [{
                                nativeEvent: { contentOffset: { y: this.state.scrollY } } // 记录滑动距离
                            }],
                            { useNativeDriver: true }) // 使用原生动画驱动
                    }
                    scrollEventThrottle={1}
                > */}

                <ScrollView
                 showsVerticalScrollIndicator={false}
                 style={{zIndex:1000}}
                 stickyHeaderIndices={[2]}
                 
                >
                
                <View style={{alignItems:'center'}} >
                    <View style={[CommonStyle.contentViewStyle,{height:200,width:screenWidth,borderColor:'#FFFFFF'}]}>
                    {
                        this.state.swiperDataSource && this.state.swiperDataSource.length > 0
                        ?
                        <Swiper style={[{justifyContent:'center'}]}
                            ref="swiper"    
                            key={this.state.swiperDataSource.length}
                            height={300} 
                            // horizontal={false} 
                            autoplay={true} //自动轮播
                            autoplayTimeout={3.5} //每隔2秒切换
                        >
                            {
                                this.state.swiperDataSource && this.state.swiperDataSource.length > 0?
                                    (
                                    this.state.swiperDataSource.map((item, index) => {
                                        return (
                                            <Image  style={[{width:screenWidth,height:200}]} source={{uri:item.advertisingImage}} />                                
                                        );
                                    })
                                    )
                                :
                                <View></View>
                            }
                            
                            
                        </Swiper>
                        :
                        <Swiper style={[styles.wrapper,{justifyContent:'center',alignItems:'center'}]}
                            ref="swiper"
                            height={300} 
                            // horizontal={false} 
                            autoplay={false} //自动轮播
                            // autoplayTimeout={3.5} //每隔2秒切换
                            // data={this.state.swiperDataSource}
                            // renderItem={({ item, index }) => this.renderPictureRow(item, index)}
                        >
                            <View></View>
                            
                            
                        </Swiper>

                    }
                    
                    </View>
                </View>

                {/* <StickyHeader
                    stickyHeaderY={this.state.headHeight} // 把头部高度传入
                    stickyScrollY={this.state.scrollY}    // 把滑动距离传入
                > */}
                <View style={[styles.classViewStyle,this.state.collegeRecruitingMenuDataSource && this.state.collegeRecruitingMenuDataSource.length > 0 ? "" : CommonStyle.hiddenViewStyle]}
                        onLayout={this.crTopBlockLayout.bind(this)}
                ></View>
                    <View style = {{backgroundColor:'#FFFFFF'}}>
                        <FlatList 
                            numColumns = {3}
                            data={this.state.collegeRecruitingMenuDataSource}
                            renderItem={({item}) => this.renderOrderRow(item)}
                            // onLayout={this.topBlockLayout.bind(this)}
                        />
                    </View>
                {/* </StickyHeader> */}

                
                
                <View style={{marginBottom:75,alignItems:'center',marginLeft:20}}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
                </ScrollView>
                {/* </Animated.ScrollView> */}
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        borderColor: "#FFFFFF",
        borderWidth: 14,
    },
    crInnerViewStyle:{
        marginTop:20,
        backgroundColor:'#FFF',
        width:screenWidth - 40,
        borderRadius:20
    },
    innerOrderViewStyle:{
        width:cellWH, 
        height:cellWH, 
        marginLeft:vMargin,
        marginTop:hMargin,
        alignItems:'center', 
        justifyContent:'center',
        backgroundColor:'#FFFFFF'
    },
     titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyRowLeftView:{
        width:screenWidth/2-40, 
        flexDirection:'row'
    },
    bodyRowRightView:{
        flexDirection:'row', 
        alignItems:'flex-start',
        paddingLeft:10,
        marginRight:5, 
        justifyContent:'flex-start',
        alignContent:'flex-start'
    },
    searchInputText: {
        width: screenWidth / 1.5,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    inputRowStyle: {
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft:15
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginTop: 15,
        fontSize: 16
    },
    classViewStyle:{
        backgroundColor:'#FFFFFF',
        height:0,
        alignItems:'flex-start',
        justifyContent:'center',
        // borderBottomWidth:1,
        // borderBottomColor:'#E0E0E0'
        position:'absolute',
        top:0
    },
    classTextStyle:{
        color:'#000',
        fontSize:16,
        fontWeight:'bold',
        marginLeft:15
    },
    innerViewImageStyle:{
        width:cellWH-50,
        height:cellWH-50
    },
    wrapper: {
        justifyContent:'center'
    },
    slide: {
        // flex:1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#9DD6EB',
        width:350,
        height:200,
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
    },
    itemContentRightChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
    },
    enterpriseLogoStyle:{
        borderRadius:10,
        width:50,
        height:50,
        marginTop:15
    }
})
module.exports = WelcomePage;