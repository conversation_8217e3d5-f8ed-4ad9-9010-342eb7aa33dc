import React, {Component} from 'react';
import {
  Alert,
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class DryKoleWasteCauseMgrList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    this.loadUngradedCauseList();
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/ungraded/cause/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      causeType: 'D',
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/ungraded/cause/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      causeType: 'D',
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };

  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadUngradedCauseList();
  };

  loadUngradedCauseList = () => {
    let url = '/biz/ungraded/cause/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      causeType: 'D',
    };
    httpPost(url, loadRequest, this.loadUngradedCauseListCallBack);
  };

  loadUngradedCauseListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteUngradedCause = (causeId) => {
    console.log('=======delete=causeId', causeId);
    let url = '/biz/ungraded/cause/delete';
    let requestParams = {causeId: causeId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.causeId} style={styles.innerViewStyle}>
        {index == 0 ? (
          <View
            style={{
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#FFFFFF',
              borderBottomWidth: 10,
              borderBottomColor: '#F4F7F9',
            }}></View>
        ) : (
          <View></View>
        )}
        <View style={styles.bodyViewStyleSpecial}>
          <Text style={styles.texRowSpecial}>{item.causeTitle}</Text>
        </View>
        <View style={styles.bodyViewStyleCommon}>
          <Text style={styles.texRowCommon}>废品原因：{item.causeTitle}</Text>
        </View>
        <View style={styles.bodyViewStyleCommon}>
          <Text style={styles.texRowCommon}>
            原因描述：{item.causeDescribe}
          </Text>
        </View>
        <View style={styles.bodyViewStyleCommon}>
          <Text style={styles.texRowCommon}>排序：{item.causeSort}</Text>
        </View>
        <View style={styles.bodyViewStyleCommon}>
          <Text style={[styles.texRowCommon, {width: null}]}>
            最近更新时间：{item.gmtModified}
          </Text>
        </View>

        <View style={[CommonStyle.itemBottomBtnStyle, {marginRight: 15}]}>
          <TouchableOpacity
            onPress={() => {
              if (
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
              ) {
                return;
              }
              Alert.alert('确认', '您确定要删除该废品原因吗？', [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                    // this在这里可用，传到方法里还有问题
                    // this.props.navigation.goBack();
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.deleteUngradedCause(item.causeId);
                  },
                },
              ]);
            }}>
            <View
              style={[
                CommonStyle.btnTwoDeleteBtnView,
                {marginRight: 0},
                ,
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
                  ? CommonStyle.disableViewStyle
                  : '',
              ]}>
              <Image
                style={[CommonStyle.btnTwoDeleteBtnImage]}
                source={require('../../assets/icon/iconfont/delete.png')}></Image>
              <Text style={CommonStyle.btnTwoDeleteBtnText}>删除</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
              ) {
                return;
              }
              this.props.navigation.navigate('DryKoleWasteCauseMgrAdd', {
                // 传递参数
                causeId: item.causeId,
                // 传递回调函数
                refresh: this.callBackFunction,
              });
            }}>
            <View
              style={[
                CommonStyle.btnTwoEditBtnView,
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
                  ? CommonStyle.disableViewStyle
                  : '',
              ]}>
              <Image
                style={CommonStyle.btnTwoEditBtnImage}
                source={require('../../assets/icon/iconfont/edit.png')}></Image>
              <Text style={CommonStyle.btnTwoEditBtnText}>编辑</Text>
            </View>
            {/* <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:"row"}
                        ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View> */}
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 头部左侧
  renderLeftItem() {
    return (
      <View style={CommonStyle.viewListLeftViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[CommonStyle.btnListLeftBtn]}>
          <Image
            style={CommonStyle.btnListLeftBtnImage}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={CommonStyle.btnListLeftBtnText}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <View style={CommonStyle.viewListRightViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('DryKoleWasteCauseMgrAdd', {
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Image
            style={CommonStyle.btnListRightBtnImage}
            source={require('../../assets/icon/iconfont/add.png')}></Image>
        </TouchableOpacity>
      </View>
      // <TouchableOpacity onPress={() => {
      //     this.props.navigation.navigate("DryKoleWasteCauseMgrAdd",
      //     {
      //         // 传递回调函数
      //         refresh: this.callBackFunction
      //     })
      // }}>
      //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
      //     {/* <Text style={CommonStyle.headRightText}>新增</Text> */}
      // </TouchableOpacity>
    );
  }

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="废品原因"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View style={CommonStyle.contentViewStyle}>
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.causeId}
            ListEmptyComponent={this.emptyComponent}
            ItemSeparatorComponent={this.space}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  innerViewStyle: {
    marginTop: 10,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
  texRowSpecial: {
    width: 200,
    height: 24,
    // fontFamily: 'PingFangSC',
    fontWeight: 'bold',
    fontSize: 20,
    color: '#404956',
    lineHeight: 24,
    textAlign: 'left',
    fontStyle: 'normal',
  },
  texRowCommon: {
    width: 220,
    height: 24,
    // fontFamily: 'PingFangSC',
    fontWeight: '400',
    fontSize: 14,
    color: 'rgba(0,10,32,0.65)',
    lineHeight: 24,
    textAlign: 'left',
    fontStyle: 'normal',
  },
  bodyViewStyleSpecial: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 40,
    marginRight: 10,
    marginBottom: 8,
    marginTop: 8,
  },
  bodyViewStyleCommon: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 40,
    marginRight: 10,
    marginBottom: 0,
    marginTop: 0,
  },
});
