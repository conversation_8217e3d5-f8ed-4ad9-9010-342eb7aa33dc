import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class HLTransferModeAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            transferModeId:"",
            transferModeName:"",
            transferModeCode:"",
            transferModeSort:0,
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { transferModeId } = route.params;
            if (transferModeId) {
                console.log("=============transferModeId" + transferModeId + "");
                this.setState({
                    transferModeId:transferModeId,
                    operate:"编辑",
                })
                let loadTypeUrl = "/biz/hl/transfer/mode/get";
                let loadRequest = { 'transferModeId': transferModeId };
                httpPost(loadTypeUrl, loadRequest, this.loadHLTransferModeCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    loadHLTransferModeCallBack = (response) => {
        if (response.code == 200 && response.data) {

            this.setState({
                transferModeName: response.data.transferModeName,
                transferModeCode: response.data.transferModeCode,
                transferModeSort: response.data.transferModeSort,
            })
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLTransforModeList")
            }}>
                <Text style={CommonStyle.headRightText}>流转方式</Text>
            </TouchableOpacity>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveHLTransferMode = () => {
        console.log("=======saveHLTransferMode");
        let toastOpts;
        if (!this.state.transferModeName) {
            toastOpts = getFailToastOpts("请填写流转方式");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.transferModeCode) {
        //     toastOpts = getFailToastOpts("请输入外系统关联标识");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.transferModeSort) {
        //     toastOpts = getFailToastOpts("请输入排序");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url = "/biz/hl/transfer/mode/add";
        if (this.state.transferModeId) {
            console.log("=========Edit===transferModeId", this.state.transferModeId)
            url = "/biz/hl/transfer/mode/modify";
        }

        let requestParams = {
            transferModeId: this.state.transferModeId,
            transferModeName: this.state.transferModeName,
            transferModeCode: this.state.transferModeCode,
            transferModeSort: this.state.transferModeSort,
        };
        httpPost(url, requestParams, this.saveHLTransferModeCallBack);
    }

    // 保存回调函数
    saveHLTransferModeCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }


    render(){
        return(
            <ScrollView style={[CommonStyle.contentViewStyle]}>
                <CommonHeadScreen title={this.state.operate + '流转方式'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>流转方式</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput
                        //keyboardType='text'
                        style={styles.inputRightText}
                        placeholder={'请填写流转方式名称'}
                        onChangeText={(text) => this.setState({transferModeName: text })}
                    >
                        {this.state.transferModeName}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>关联标识</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <TextInput 
                        style={styles.inputRightText}
                        placeholder={'请输入外系统关联标识'}
                        onChangeText={(text) => this.setState({transferModeCode:text})}
                    >
                        {this.state.transferModeCode}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>
                        排序
                        </Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput 
                        keyboardType='numeric'
                        style={styles.inputRightText}
                        placeholder={'请输入排序'}
                        onChangeText={(text) => this.setState({transferModeSort:text})}
                    >
                        {this.state.transferModeSort}
                    </TextInput>
                </View>
                <View style={CommonStyle.btnRowStyle}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveHLTransferMode.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row', width: 130, height: 40, marginRight: 35, marginTop: 15 }]}>
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        )
    }
}
const styles = StyleSheet.create({
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        // marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth +30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})