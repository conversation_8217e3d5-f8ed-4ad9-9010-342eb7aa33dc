import React, {Component} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';

class HomeDetail extends React.Component{
    
    constructor(props) {
        super(props);
        console.log("========HomeDetail=props==2:", props);
        this.state = {
            code:'init'
        };
    }

    _pressButton() {
        const { navigator } = this.props;
        if(navigator) {
            //很熟悉吧，入栈出栈~ 把当前的页面pop掉，这里就返回到了上一个页面:FirstPageComponent了
            navigation.goBack();
        }
    }

    render (){
        return (
            <View>
                <Text>Home-Detail</Text>
                <Text>传递过来的参数值=code{this.props.code}</Text>
                <TouchableOpacity onPress={this._pressButton.bind(this)}>
                    <View style={styles.loginBtnStyle}>
                        <Text style={{color:'white'}}>点我跳回去</Text>
                    </View>
                    
                </TouchableOpacity>
            </View>
        );
    }

}

const styles = StyleSheet.create({
    loginBtnStyle:{
        height:35,
        width:350,
        backgroundColor:'blue',
        marginTop:30,
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8
    }
})

module.exports = HomeDetail;