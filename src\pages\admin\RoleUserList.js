import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,Modal,Clipboard,
    FlatList,RefreshControl,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class RoleUserList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            roleId:"",
            roleName:"",
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            searchKeyWord:null,
            modal:false,
            userPwd:null,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { roleId, roleName } = route.params;
            if (roleId) {
                console.log("========Edit==roleId:", roleId);
                this.setState({
                    roleId:roleId
                })
                this.loadRoleUserList(roleId);
            }
            if (roleName) {
                this.setState({
                    roleName:roleName
                })
            }
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/role/role_user_list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "roleId":this.state.roleId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/role/role_user_list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "roleId":this.state.roleId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            list.map((item, index) => {
                    item.modal = false;
                })
            console.log(list);
            this.setState({
                dataSource:list,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadRoleUserList();
    }

    loadRoleUserList=(roleId)=>{
        let url= "/biz/role/role_user_list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "roleId":(roleId ? roleId : this.state.roleId)
        };
        httpPost(url, loadRequest, this.loadRoleUserListCallBack);
    }

    loadRoleUserListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            list.map((item, index) => {
                    item.modal = false;
                })
            console.log(list);
            this.setState({
                dataSource:list,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteRoleUser =(userId)=> {
        console.log("=======delete=userId", userId);
        let url= "/biz/role/role_user_delete";
        let requestParams={'userId':userId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.roleId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>员工姓名：{item.userName}</Text>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>联系电话：{item.userCode}</Text>
                </View>
                
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>

                    <TouchableOpacity onPress={()=>{

                            //WToast.show({data:'点击了确定' + item.userId});
                            console.log('===loadUserPWD:', item.userName);
                            let loadTypeUrl= "/biz/portal/user/send_user_pwd";
                            let loadRequest={tenantId: constants.loginUser.tenantId, userId:item.userId};;
                            
                            httpPost(loadTypeUrl, loadRequest, (response)=>{
                                if (response.code == 200 && response.data) {
                                    WToast.show({data:"发送成功"});
                                }

                                else {
                                    WToast.show({data:response.message});
                                }
                                
                            });
                        
                        }
                        }>
                        {/* <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:100,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>发送密码</Text>
                        </View> */}
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { height:28,width:95,backgroundColor:"#5DD421",flexDirection:"row"}]}>
                            <Image style={{width:16, height:16,marginRight:5}} source={require('../../assets/icon/iconfont/sendPwd.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>发送密码</Text>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity onPress={()=>{

                            console.log('===loadUserPWD:', constants.loginUser.userId);
                            let loadTypeUrl= "/biz/portal/user/get_pwd";
                            let loadRequest={tenantId: constants.loginUser.tenantId, userId:item.userId};;
                            
                            httpPost(loadTypeUrl, loadRequest, (response)=>{
                                if (response.code == 200 && response.data) {
                                    let userPwd = response.data;
                                    Clipboard.setString(userPwd);
                                    WToast.show({data:"复制成功"});
                                }

                                else {
                                    WToast.show({data:response.message});
                                    //WToast.show({data:"复制失败"});
                                }

                                this.setState({
                                    userPwd: response.data,
                                }) 
    
                            });

                           

                            // var list = this.state.dataSource;
                            // list.map((elem)=>{
                            //         if(elem.roleId === item.roleId && elem.userCode === item.userCode){
                            //             elem.modal = true
                            //         }  
                            //     })
                            // this.setState({
                            //     dataSource:list
                            // })
                        
                    }}>
                        {/* <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:100,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>查看密码</Text>
                        </View> */}
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { height:28,width:95,backgroundColor:"#FF8C28",flexDirection:"row"}]}>
                            <Image style={{width:16, height:16,marginRight:5}} source={require('../../assets/icon/iconfont/copyPwd.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>复制密码</Text>
                        </View>
                    </TouchableOpacity>
                        
                    {/* <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={item.modal}>
                        <View style={[CommonStyle.fullScreenKeepOut,]}>
                            <View style={CommonStyle.modalContentViewStyle}>

                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>本用户密码：{this.state.userPwd ? this.state.userPwd :"无"}</Text>
                            </View>
                                
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        var list = this.state.dataSource;
                                        list.map((elem)=>{
                                                if(elem.roleId === item.roleId && elem.userCode === item.userCode){
                                                    elem.modal = false
                                                }  
                                            })
                                        this.setState({
                                            dataSource:list
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal> */}
                    
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该用户吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定' + item.userId});
                                    this.deleteRoleUser(item.userId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:70,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("RoleUserAdd", 
                            {
                                userId:item.userId,
                                // 传递参数
                                roleId:item.roleId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,marginLeft:0,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("RoleUserAdd", 
                {
                    roleId:this.state.roleId,
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
               <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
                {/* <Text style={CommonStyle.headRightText}>添加员工</Text> */}
            </TouchableOpacity>
        )
    }
    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    searchByKeyWord=()=>{
        let url= "/biz/role/role_user_list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "roleId":this.state.roleId,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={'【' + this.state.roleName + '】员工管理'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0}]}>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Image  style={{width:25, height:25,marginBottom:5}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput 
                            style={[styles.searchInputText, {}]}
                            placeholder={'员工姓名或联系电话'}
                            onSubmitEditing={e => {
                                this.searchByKeyWord();
                                }}
                            onChangeText={(text) => this.setState({searchKeyWord:text})}
                            >
                            {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    
                </View>
                
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },

    leftLabView:{
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    searchInputText:{
        width: screenWidth / 1.5,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});