import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class StorageLocationAdd extends Component {
    constructor(){
        super()
        this.state = {
            locationId:"",
            locationName:"",
            locationSort:0,
            locationAreaId:"",
            selLocationAreaId:0,
            storageLocationAreaDataSource:[],
            locationType:"",
            selTypeId:1,
            selTypeName:"",
            locationTypeDataSource:[
                {
                    typeId:1,
                    typeName:"成品库位",
                    locationType:"P"
                },
                {
                    typeId:2,
                    typeName:"原料库位",
                    locationType:"M"
                }
            ],
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { locationId, locationAreaId} = route.params;
            if (locationId) {
                console.log("========Edit==locationId:", locationId);
                this.setState({
                    locationId:locationId
                })
                loadTypeUrl= "/biz/storage/location/get";
                loadRequest={'locationId':locationId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditLocationDataCallBack);
            }
            if (locationAreaId) {
                this.setState({
                    locationAreaId:locationAreaId
                })
            }
        }
        this.loadLocationAreaList();
    }

    loadLocationAreaList=()=>{
        let url= "/biz/storage/location/area/list";
        let loadRequest={'currentPage':1,'pageSize':1000};
        httpPost(url, loadRequest, this.callBackLoadStorageLocationArea);
    }

    loadEditLocationDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                locationId:response.data.locationId,
                locationName:response.data.locationName,
                locationSort:response.data.locationSort,
                locationAreaId:response.data.locationAreaId,
                // selTypeName:response.data.locationType == 'P' ? "成品库位" : "原料库位",
                selTypeId:response.data.locationType == 'M' ? 2 : 1,
            })
        }
    }

    // 库区回调加载
    callBackLoadStorageLocationArea=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库区");
                WToast.show(toastOpts);
                return;
            }
            this.setState({
                storageLocationAreaDataSource:response.data.dataList,
            })
            const { route, navigation } = this.props;
            if (route && route.params) {
                const {locationAreaId} = route.params;
                if (locationAreaId) {
                    this.setState({
                        locationAreaId:locationAreaId
                    })
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("StorageLocationList")
            }}>
                <Text style={CommonStyle.headRightText}>库位管理</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveLocation =()=> {
        console.log("=======saveLocation");
        let toastOpts;
        if (!this.state.locationName) {
            toastOpts = getFailToastOpts("请输入库位名称");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/storage/location/add";
        if (this.state.locationId) {
            console.log("=========Edit===locationId", this.state.locationId)
            url= "/biz/storage/location/modify";
        }
        let requestParams={
            "locationId":this.state.locationId,
            "locationName":this.state.locationName,
            "locationSort":this.state.locationSort,
            "locationAreaId":this.state.locationAreaId,
            "locationType": this.state.locationTypeDataSource[this.state.selTypeId-1].locationType,
        };
        httpPost(url, requestParams, this.saveLocationCallBack);
    }
    
    // 保存回调函数
    saveLocationCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 库区
    renderLocationAreaRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    locationAreaId:item.locationAreaId
                }) 
            }}>
                <View key={item.locationAreaId} style={item.locationAreaId===this.state.locationAreaId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.locationAreaId===this.state.locationAreaId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationAreaName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

     //库位类型列表展示
     renderLocationTypeRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selTypeId:item.typeId,
                        locationType:item.locationType,
                        selTypeName:item.typeName,
                    })
                }}>
                <View key={item.typeId} style={[item.typeId===this.state.selTypeId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.typeId===this.state.selTypeId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.typeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title='新增库位'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>库区</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationAreaDataSource && this.state.storageLocationAreaDataSource.length > 0) 
                            ? 
                            this.state.storageLocationAreaDataSource.map((item, index)=>{
                                return this.renderLocationAreaRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>库位名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入库位名称'}
                            onChangeText={(text) => this.setState({locationName:text})}
                        >
                            {this.state.locationName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={[styles.rowLabView,{marginRight:20}]}>
                            <Text style={styles.leftLabNameTextStyle}>库位类型</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row',marginLeft:5}}>
                            {
                                (this.state.locationTypeDataSource && this.state.locationTypeDataSource.length > 0) 
                                ? 
                                this.state.locationTypeDataSource.map((item, index)=>{
                                    return this.renderLocationTypeRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'0'}
                            onChangeText={(text) => this.setState({locationSort:text})}
                        >
                            {this.state.locationSort}
                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{flexDirection:'row'}]} >
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/cancel2.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveLocation.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})