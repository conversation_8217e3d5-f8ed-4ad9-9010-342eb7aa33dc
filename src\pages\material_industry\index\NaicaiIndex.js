import React,{Component} from 'react';
import {View, Text, StyleSheet, Image, FlatList,RefreshControl,TextInput,Linking,StatusBar
    ,Dimensions, ScrollView, TouchableOpacity, Alert,Animated,Modal,CameraRoll,DeviceEventEmitter
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';

// 公共组件及样式
import EmptyListComponent from '../../../component/EmptyListComponent';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import { ifIphoneX, ifIphoneXHeaderHeight } from '../../../utils/ScreenUtil';
import { naicaiIndexIfIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
import ProductEmptyRowViewComponent from '../../../component/ProductEmptyRowViewComponent';
import UserPrivacyComponent from '../../../component/UserPrivacyComponent';

var CommonStyle = require('../../../assets/css/CommonStyle');
import AsyncStorage from '@react-native-community/async-storage';

import { isIphoneX } from '../../../utils/ScreenUtil';
import Swiper from 'react-native-swiper';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../../utils/CameraRollUtils';
// import StickyHeader from 'react-native-StickyHeader';

import {WToast} from 'react-native-smart-tip';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
var cols = 3;
var cellWH = 100;
var vMargin = (screenWidth - cellWH * cols) / (cols + 1);
var hMargin = 10;
//未登录首页
export default class NaicaiIndex extends Component {
    constructor(props) {
        super(props);
        this.state = {
            appFirstStartPopup:false,
            dataSource:[],
            text: '初始状态',
            refreshing: false,
            pageSize:6,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight: 0,
            crTopBlockLayoutHeight:0,
            searchKeyWord:"",
            managerTenantId:null,
            swiperDataSource:[],
            refractoryProductDataSource:[],
            productAskForPurchaseDataSource:[],
            materialAskForPurchaseDataSource:[],
            refractoryMaterialDataSource:[],
            isShowImage:false,
            urls:"",
            productTypeList:[
                {
                    "typeId":0,
                    "typeName":"耐火制品",
                    "typeCode":"P",
                    "releaseType":"R"
                },
                {
                    "typeId":1,
                    "typeName":"耐火原料",
                    "typeCode":"M",
                    "releaseType":"R"
                },
                {
                    "typeId":2,
                    "typeName":"制品求购",
                    "typeCode":"P",
                    "releaseType":"P"
                },
                {
                    "typeId":3,
                    "typeName":"原料求购",
                    "typeCode":"M",
                    "releaseType":"P"
                }
            ],
            selReleaseType:"R",
            selProductType:"P"
        }

    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    getNowTime(){
        var date = new Date();
        //获取当前时间的毫秒数
        var nowMilliSeconds = date.getTime();
        // 用获取毫秒数 加上30天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        var nowTime = new Date(nowMilliSeconds + ( 8 * 3600000));
        //年 getFullYear()：四位数字返回年份
        var year = nowTime.getFullYear();  //getFullYear()代替getYear()
        //月 getMonth()：0 ~ 11
        var month = nowTime.getMonth() + 1;
        //日 getDate()：(1 ~ 31)
        var day = nowTime.getDate();
        //时 getHours()：(0 ~ 23)
        var hour = nowTime.getHours();
        //分 getMinutes()： (0 ~ 59)
        var minute = nowTime.getMinutes();
        //秒 getSeconds()：(0 ~ 59)
        var second = nowTime.getSeconds();

        var time =  year + '-' + this.addZero(month) + '-' + this.addZero(day) + ' ' + this.addZero(hour) + ':' + this.addZero(minute) + ':' + this.addZero(second);
        return time;
    }

    addZero(s){
        return s < 10 ? ('0'+s):s;
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        this.listener = DeviceEventEmitter.addListener('action',(appFirstStartPopup, triggerTime)=>{//（）中为携带参数，可以为空
            //调接口……刷新页面
            console.log("============同步到了=XX==NaicaiIndex=======", appFirstStartPopup, triggerTime)
            this.setState(({
                appFirstStartPopup:appFirstStartPopup
            }))
        });


        AsyncStorage.getItem("managerTenantId",(err,param)=>{
            console.log(param)
            this.setState({managerTenantId:Number(param)})
        })

        const { route, navigation } = this.props;
        if (route && route.params) {
        }
        this.loadswiperDataSource();
        this.loadDataList();
    }

    loadswiperDataSource = ()=>{
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10,
            "searchAllTenant":"Y"
        };
        httpPost(url, loadRequest, this.loadswiperDataSourceCallBack);
    }

    loadswiperDataSourceCallBack = (response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                swiperDataSource:[...response.data.dataList],
            })
            
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadDataList=()=>{
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "releaseType": this.state.selReleaseType,
            "productState":this.state.selReleaseType == "R" ? "U" : null,
            "searchKeyWord":this.state.searchKeyWord,
            "productType":this.state.selProductType,
            "searchAllTenant":"Y",
            "auditState":"3",
            "expDate":this.getNowTime(),
            "includeAuditMarking":["1","2"]
        };
        this.httpPost_addManagerTenantId(url, loadRequest, this.loadDataListCallBack);
    }

    loadDataListCallBack=(response)=>{
        this.setState({
            refreshing:false
        })
        if (response.code == 200 && response.data) {
            var dataNew = response.data.dataList;
            console.log(dataNew)
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadDataList();
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": this.state.selReleaseType,
            "productState":this.state.selReleaseType == "R" ? "U" : null,
            "searchKeyWord":this.state.searchKeyWord,
            "productType":this.state.selProductType,
            "searchAllTenant":"Y",
            "auditState":"3",
            "expDate":this.getNowTime(),
            "includeAuditMarking":["1","2"]
        };
        this.httpPost_addManagerTenantId(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        this.setState({
            refreshing:false
        })
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/product/release/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": this.state.selReleaseType,
            "productState":this.state.selReleaseType == "R" ? "U" : null,
            "searchKeyWord":this.state.searchKeyWord,
            "productType":this.state.selProductType,
            "searchAllTenant":"Y",
            "auditState":"3",
            "expDate":this.getNowTime(),
            "includeAuditMarking":["1","2"]
        };
        this.httpPost_addManagerTenantId(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }

    emptyComponent() {
        return <ProductEmptyRowViewComponent height={350} />
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage) < this.state.totalPage} />
        )
    }


    // logout=()=>{
    //     console.log("===logout");
    //     let url = "/biz/user/logout?a=123&b=234"
    //     httpGet(url, this.logout_call_back);
    // }

    // logout_call_back=(response)=>{
    //     console.log("=====logout_call_back:", response);
    //     this.props.navigation.navigate('LoginView');
    // }




    // _pressJump(item) {
    //     const { navigation } = this.props;
    //     if(navigation && item.component != null) {
    //         navigation.navigate(item.component, {
    //             // 测试参数
    //             itemId: 1000000, 
    //             code:item.code,
    //             title: item.title
    //         })
    //     }
    // }

    myIsNaN=(value)=> {
        return parseFloat(value).toString() != "NaN";
    }

    httpPost_addManagerTenantId=(url,loadRequest,callback)=>{
        AsyncStorage.getItem("managerTenantId",(err,param)=>{
            console.log(param,Number(param))
            this.setState({managerTenantId:Number(param)})
            loadRequest.managerTenantId=Number(param)?Number(param):null
            console.log(loadRequest)
            httpPost(url,loadRequest,callback)         
        })
    }
    productTypeRow=(item, index)=>{
        return (
            <View key={item.typeId} style={{width:screenWidth/4}}>
                <TouchableOpacity onPress={()=>{

                    this.setState({
                        selProductType:item.typeCode,
                        selReleaseType:item.releaseType,
                        currentPage:1,
                        totalRecord:0,
                        totalPage:1,
                        dataSource:[],
                    })

                    let url= "/biz/product/release/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "releaseType": item.releaseType,
                        "productState":item.releaseType == "R" ? "U" : null,
                        "searchKeyWord":this.state.searchKeyWord,
                        "productType":item.typeCode,
                        "searchAllTenant":"Y",
                        "auditState":"3",
                        "expDate":this.getNowTime(),
                        "includeAuditMarking":["1","2"]
                    };
                    this.httpPost_addManagerTenantId(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.typeCode} style={[(item.releaseType===this.state.selReleaseType && item.typeCode===this.state.selProductType) ? [CommonStyle.selectedBlockItemViewStyle,{backgroundColor:'#FFF',borderBottomWidth:2,borderBottomColor:'#255BDA',borderRadius:0,paddingBottom:0,marginBottom:0}] : [CommonStyle.blockItemViewStyle,{backgroundColor:'#FFF',paddingBottom:0,marginBottom:0.8,marginLeft:0,marginRight:0}],{alignItems:'center', height:35, paddingLeft:0, paddingRight:0}]}>
                        <Text style={[(item.releaseType===this.state.selReleaseType && item.typeCode===this.state.selProductType)? styles.selectedBlockItemTextStyle : styles.blockItemTextStyle]}>
                            {item.typeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    renderReleaseRow=(item, index)=>{
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("ProductDetail", {
                    // 传递参数
                    releaseId: item.releaseId,
                    // userName: item.userName,
                    // 传递回调函数
                    refresh: this.callBackFunction
                })
            }}>
            <View key={item.releaseId} style={[styles.innerViewStyle,{flexDirection:'row'}]}>
                {/* <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                    <Text style={styles.titleTextStyle}>{item.productName}</Text>
                    {
                        item.productModel ?
                        <Text style={[styles.greyTextStyle,{marginLeft:30}]}>型号：{item.productModel}</Text>
                        : <View/>
                    }
                    
                </View> */}

                <View >
                    {
                        item.compressFileList && item.compressFileList.length > 0 ?
                        <View>
                            <TouchableOpacity onPress={() => {
                                var urls = [];
                                var url = {
                                    url:constants.image_addr + '/' +  item.compressFileList[0].compressFile
                                }
                                urls=urls.concat(url) 
                                console.log(url)

                                this.setState({
                                    urls:urls
                                })
                                this.setState({
                                    isShowImage:true,
                                })

                            }}>
                                <Image source={{ uri: (constants.image_addr + '/' + item.compressFileList[0].compressFile) }} style={[{height:120, width:120,marginTop:10,borderRadius:3}]} />                                                    
                            </TouchableOpacity>
                            <Modal visible={this.state.isShowImage} transparent={true}>
                                <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} 
                                enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}  
                                onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} 
                                onSave={()=>{
                                    saveImage( this.state.urls[0].url)
                                }}/>
                            </Modal>
                        </View>
                        :
                        <View style={{display:'flex',justifyContent:'center',alignItems:'center'}}>
                            <Image  style={{height:120, width:120,marginTop:10,borderRadius:3}} source={require('../../../assets/icon/iconfont/emptyPicture.png')}></Image>
                        </View>
                    }
                </View>
                {/* <View style={{position:'absolute',right:-10,top:10}}>
                    {
                        item.auditMarking == '1' ?
                        <View style={{position:'absolute',right:0,top:0}}>
                            <Image style={{width:85,height:25}} source={require('../../../assets/image/recommend.png')}/>
                        </View>
                        :
                        (
                            item.auditMarking == '2' ?
                            <View style={{position:'absolute',right:0,top:0}}>
                                <Image style={{width:50,height:23}} source={require('../../../assets/image/selfSupport.png')}/>
                            </View>
                            :
                            null
                        )
                    }
                    {
                        item.spotFlag == 'Y' ?
                        (
                            item.auditMarking == '1' || item.auditMarking == '2'?
                            <View  style={{position:'absolute',right:0,top:30}}>
                                <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/>
                                <Text style={{position:'absolute',right:3,color:"#f0f0f0"}}>现货</Text>
                            </View>
                            :
                            <View  style={{position:'absolute',right:0,top:0}}>
                                <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/>
                                <Text style={{position:'absolute',right:3,color:"#f0f0f0"}}>现货</Text>
                            </View>
                        )
                        :
                        null
                    }
                    
                </View> */}


                <View style={{flexDirection:'row',display:'flex'}}>
                    

                    <View style={{flexDirection:'column',marginLeft:10,marginTop:7}}>
                        
                        <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                            {
                                item.auditMarking == '1' ?
                                <View  style={{backgroundColor:'#FA653A',width: 70, height: 20,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                    {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                    <Text style={{fontSize: 13,color:"#ffffff"}}>精选推荐</Text>
                                </View>
                                :
                                (
                                    item.auditMarking == '2' ?
                                    <View  style={{backgroundColor:'#FA653A',width: 38, height: 20,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                    {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                    <Text style={{fontSize: 13,color:"#ffffff"}}>自营</Text>
                                </View>
                                    :
                                    null
                                )
                            }
                            <View>
                                <Text style={[styles.titleTextStyle,{marginLeft:3}]}>{item.productName}</Text>
                            </View>
                            
                            {/* {
                                item.productModel ?
                                <Text style={[styles.greyTextStyle,{marginLeft:30}]}>型号：{item.productModel}</Text>
                                : <View/>
                            } */}
                            
                        </View>
                        {/* {
                            item.productMaterial ?
                            <View style={{marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>材质：{item.productMaterial}</Text>
                            </View>
                            : <View/>
                        } */}
                        
                        <View style={{marginBottom:5}}>
                            <Text style={styles.blackTextStyle}>重量：{item.productWeight}吨</Text>
                        </View>
                        <View>
                            <Text style={styles.blackTextStyle}>产地：{item.productionAddr}</Text>
                        </View>
                        {
                            item.productPrice ?
                            <View style={{flexDirection:'row',marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>价格：
                                    <Text style={styles.redTextStyle}>{item.productPrice}</Text>
                                    {
                                        this.myIsNaN(item.productPrice) ?
                                        <Text style={styles.greyTextStyle}> 元/吨</Text>
                                        :
                                        null
                                    }
                                </Text>
                            </View> :
                            <View style={{flexDirection:'row',marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>价格：
                                    <Text style={styles.redTextStyle}>面议</Text>
                                </Text>
                            </View>
                        }
                        {
                            item.spotFlag == 'Y' ?
                            (
                                item.auditMarking == '1' || item.auditMarking == '2'?
                                <View  style={{backgroundColor:'#FFF4DE',width: 38, height: 20,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                    {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                    <Text style={{color:"#846020"}}>现货</Text>
                                </View>
                                :
                                <View  style={{backgroundColor:'#FFF4DE',width: 38, height: 20,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                    {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                    <Text style={{color:"#846020"}}>现货</Text>
                                </View>
                            )
                            :
                            null
                        }
                        
                    </View>

                    
                </View>
                <View style={{position:'absolute',right:10,alignItems:'center',top:50}}>
                        <TouchableOpacity  onPress={() => {
                            let phone = item.salePersonTel;
                            if(phone == null){
                                WToast.show({data:'暂未添加联系人电话，请联系管理员！'});
                                return;
                            }
                            const url = `tel:${phone}`;
                            Linking.canOpenURL(url)
                            .then(supported => {
                                if (!supported) {
                                return Alert.alert('提示', `您的设备不支持该功能，请手动拨打 ${phone}`, [
                                    { text: '确定' }
                                ]);
                                }
                                return Linking.openURL(url);
                            })
                            .catch(err => WToast.show({data:`出错了：${err}`}));
                        }}>
                            <Image  style={{width:40, height:40}} source={require('../../../assets/icon/iconfont/tel.png')}></Image>
                        </TouchableOpacity>
                    </View>
                {/* <View style={[item.productType == 'P' ? {marginTop:5} : {marginTop:10}]}>
                    <Text style={styles.greyTextStyle}>说明：{item.productExplain?item.productExplain:"无"}</Text>
                </View> */}

                {/* <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>发布日期：{item.releaseDate}</Text>
                </View> */}

                {/* <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>联系人：{item.contactPerson}</Text>
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>联系电话：{item.contactTel}</Text>
                </View> */}
            </View>
            </TouchableOpacity>
        )
    }

    renderPurchaseRow=(item, index)=>{
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("ProductDetail", {
                    // 传递参数
                    releaseId: item.releaseId,
                    // userName: item.userName,
                    // 传递回调函数
                    refresh: this.callBackFunction
                })
            }}>
            <View key={item.releaseId} style={[styles.innerViewStyle,{flexDirection:'row'}]}>
                {/* <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                    <Text style={styles.titleTextStyle}>{item.productName}</Text>
                    {
                        item.productModel ?
                        <Text style={[styles.greyTextStyle,{marginLeft:30}]}>型号：{item.productModel}</Text>
                        : <View/>
                    }
                    
                </View> */}

                <View >
                    {
                        item.compressFileList && item.compressFileList.length > 0 ?
                        <View>
                            <TouchableOpacity onPress={() => {
                                var urls = [];
                                var url = {
                                    url:constants.image_addr + '/' +  item.compressFileList[0].compressFile
                                }
                                urls=urls.concat(url) 
                                console.log(url)

                                this.setState({
                                    urls:urls
                                })
                                this.setState({
                                    isShowImage:true,
                                })

                            }}>
                                <Image source={{ uri: (constants.image_addr + '/' + item.compressFileList[0].compressFile) }} style={[{height:120, width:120,marginTop:10,borderRadius:3}]} />                                                    
                            </TouchableOpacity>
                            <Modal visible={this.state.isShowImage} transparent={true}>
                                <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} 
                                enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}  
                                onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} 
                                onSave={()=>{
                                    saveImage( this.state.urls[0].url)
                                }}/>
                            </Modal>
                        </View>
                        :
                        <View style={{display:'flex',justifyContent:'center',alignItems:'center'}}>
                            <Image  style={{height:120, width:120,marginTop:10,borderRadius:3}} source={require('../../../assets/icon/iconfont/emptyPicture.png')}></Image>
                        </View>
                    }
                </View>
                {/* <View style={{position:'absolute',right:-10,top:10}}>
                    {
                        item.auditMarking == '1' ?
                        <View style={{position:'absolute',right:0,top:0}}>
                            <Image style={{width:85,height:25}} source={require('../../../assets/image/recommend.png')}/>
                        </View>
                        :
                        (
                            item.auditMarking == '2' ?
                            <View style={{position:'absolute',right:0,top:0}}>
                                <Image style={{width:50,height:23}} source={require('../../../assets/image/selfSupport.png')}/>
                            </View>
                            :
                            null
                        )
                    }
                    {
                        item.spotFlag == 'Y' ?
                        (
                            item.auditMarking == '1' || item.auditMarking == '2'?
                            <View  style={{position:'absolute',right:0,top:30}}>
                                <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/>
                                <Text style={{position:'absolute',right:3,color:"#f0f0f0"}}>现货</Text>
                            </View>
                            :
                            <View  style={{position:'absolute',right:0,top:0}}>
                                <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/>
                                <Text style={{position:'absolute',right:3,color:"#f0f0f0"}}>现货</Text>
                            </View>
                        )
                        :
                        null
                    }
                    
                </View> */}


                <View style={{flexDirection:'row',display:'flex'}}>
                    

                    <View style={{flexDirection:'column',marginLeft:10,marginTop:7}}>
                        
                        <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                            {
                                item.auditMarking == '1' ?
                                <View  style={{backgroundColor:'#FA653A',width: 70, height: 20,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                    {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                    <Text style={{fontSize: 13,color:"#ffffff"}}>精选推荐</Text>
                                </View>
                                :
                                (
                                    item.auditMarking == '2' ?
                                    <View  style={{backgroundColor:'#FA653A',width: 38, height: 20,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                    {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                    <Text style={{fontSize: 13,color:"#ffffff"}}>自营</Text>
                                </View>
                                    :
                                    null
                                )
                            }
                            <View>
                                <Text style={[styles.titleTextStyle,{marginLeft:3}]}>{item.productName}</Text>
                            </View>
                            
                            {/* {
                                item.productModel ?
                                <Text style={[styles.greyTextStyle,{marginLeft:30}]}>型号：{item.productModel}</Text>
                                : <View/>
                            } */}
                            
                        </View>
                        {/* {
                            item.productMaterial ?
                            <View style={{marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>材质：{item.productMaterial}</Text>
                            </View>
                            : <View/>
                        } */}
                        
                        <View style={{marginBottom:5}}>
                            <Text style={styles.blackTextStyle}>重量：{item.productWeight}吨</Text>
                        </View>
                        <View>
                            <Text style={styles.blackTextStyle}>期望产地：{item.productionAddr}</Text>
                        </View>
                        {
                            item.productPrice ?
                            <View style={{flexDirection:'row',marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>价格：
                                    <Text style={styles.redTextStyle}>{item.productPrice}</Text>
                                    {
                                        this.myIsNaN(item.productPrice) ?
                                        <Text style={styles.greyTextStyle}> 元/吨</Text>
                                        :
                                        null
                                    }
                                </Text>
                            </View> :
                            <View style={{flexDirection:'row',marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>价格：
                                    <Text style={styles.redTextStyle}>面议</Text>
                                </Text>
                            </View>
                        }
                        {
                            item.spotFlag == 'Y' ?
                            (
                                item.auditMarking == '1' || item.auditMarking == '2'?
                                <View  style={{backgroundColor:'#FFF4DE',width: 38, height: 20,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                    {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                    <Text style={{color:"#846020"}}>现货</Text>
                                </View>
                                :
                                <View  style={{backgroundColor:'#FFF4DE',width: 38, height: 20,  borderRadius: 7, flexDirection: 'row', justifyContent:'center', alignItems: 'center',}}>
                                    {/* <Image style={{width:45,height:20}} source={require('../../../assets/image/productLabel.png')}/> */}
                                    <Text style={{color:"#846020"}}>现货</Text>
                                </View>
                            )
                            :
                            null
                        }
                        
                    </View>

                    
                </View>
                <View style={{position:'absolute',right:10,alignItems:'center',top:50}}>
                    <TouchableOpacity  onPress={() => {
                        let phone = item.salePersonTel;
                        if(phone == null){
                            WToast.show({data:'暂未添加联系人电话，请联系管理员！'});
                            return;
                        }
                        const url = `tel:${phone}`;
                        Linking.canOpenURL(url)
                        .then(supported => {
                            if (!supported) {
                            return Alert.alert('提示', `您的设备不支持该功能，请手动拨打 ${phone}`, [
                                { text: '确定' }
                            ]);
                            }
                            return Linking.openURL(url);
                        })
                        .catch(err => WToast.show({data:`出错了：${err}`}));
                    }}>
                        <Image  style={{width:40, height:40}} source={require('../../../assets/icon/iconfont/tel.png')}></Image>
                    </TouchableOpacity>
                </View>
                {/* <View style={[item.productType == 'P' ? {marginTop:5} : {marginTop:10}]}>
                    <Text style={styles.greyTextStyle}>说明：{item.productExplain?item.productExplain:"无"}</Text>
                </View> */}

                {/* <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>发布日期：{item.releaseDate}</Text>
                </View> */}

                {/* <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>联系人：{item.contactPerson}</Text>
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>联系电话：{item.contactTel}</Text>
                </View> */}
            </View>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height  - 75
        })

    }

    render(){
        return(
            <View style={{backgroundColor:'#FFFFFF '}}>
                {this.state.appFirstStartPopup ? null : <UserPrivacyComponent />} 
                <StatusBar hidden={!isIphoneX()} barStyle='dark-content' />
                <View style={[{marginTop:0,backgroundColor:'#FFF'}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{display:'flex'}} >
                        <View style={[CommonStyle.contentViewStyle,{height:200,width:screenWidth,backgroundColor:'#FFFFFF',justifyContent:'center',marginTop:ifIphoneXHeaderHeight() - 40}]}>
                        {
                            this.state.swiperDataSource && this.state.swiperDataSource.length > 0
                            ?
                                <Swiper
                                    ref="swiper"    
                                    key={this.state.swiperDataSource.length}
                                    height={200} 
                                    // horizontal={false} 
                                    autoplay={true} //自动轮播
                                    autoplayTimeout={3.5} //每隔2秒切换
                                >
                                    {
                                        this.state.swiperDataSource && this.state.swiperDataSource.length > 0?
                                            (
                                            this.state.swiperDataSource.map((item, index) => {
                                                return (
                                                    <View style={{width:screenWidth - 20,marginLeft:10,marginTop:5}}>
                                                        <Image  style={[{height:180 ,borderRadius:10}]} source={{uri:item.advertisingImage}} />                                
                                                    </View>
                                                
                                                );
                                            })
                                            )
                                        :
                                        <View></View>
                                    }
                                    
                                    
                                </Swiper>
                            
                            :
                            <Swiper style={[styles.wrapper,{justifyContent:'center',alignItems:'center'}]}
                                ref="swiper"
                                height={200} 
                                // horizontal={false} 
                                autoplay={false} //自动轮播
                                // autoplayTimeout={3.5} //每隔2秒切换
                                // data={this.state.swiperDataSource}
                                // renderItem={({ item, index }) => this.renderPictureRow(item, index)}
                            >
                                <View></View>
                            </Swiper>
                        }
                        </View>
                    </View>

                    <View style={[CommonStyle.searchBoxExternalViewStyle,{marginTop:0}]}>
                        <View style={CommonStyle.searchBoxInternalViewStyle}>
                            <View style={CommonStyle.searchBoxImageViewStyle}>
                                <Image  style={{width:25, height:25}} source={require('../../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'搜索名称/型号/材质'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                            {/* | {this.state.currentPage} | {this.state.totalPage} | {this.state.totalRecord} | {this.state.refreshing ? "Y":"N"} | {(this.state.currentPage) < this.state.totalPage ? "YY":"NN"} */}
                        </View>
                    </View>
                    <View style={{backgroundColor:'#ffffff', index: 1000, flexWrap: 'wrap', flexDirection: 'row',alignItems:'flex-end',borderBottomColor:'#33333333',borderBottomWidth:2,borderTopWidth:11,borderTopColor:'#F4F7F9'}}>
                        {
                            (this.state.productTypeList && this.state.productTypeList.length > 0)
                                ?
                                this.state.productTypeList.map((item, index) => {
                                    return this.productTypeRow(item)
                                })
                                : <View />
                        }
                    </View>
                </View>

                <View style={[CommonStyle.contentViewStyle, { height: naicaiIndexIfIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={
                            ({item,index}) => {
                                if(this.state.selReleaseType == "R"){
                                    return this.renderReleaseRow(item, index)
                                }
                                else{
                                    return this.renderPurchaseRow(item, index)
                                }
                            }
                        }
                        ListEmptyComponent={this.emptyComponent()}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />

                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({

    innerViewStyle:{
        width:screenWidth - 20,
        marginLeft:10,
        // paddingLeft:10,
        // paddingTop:10,
        // marginTop:10,
        backgroundColor:'#FFF',
        borderBottomColor:'#33333333',
        borderBottomWidth:0.8,
        paddingBottom:10
    },
    crInnerViewStyle:{
        marginTop:20,
        backgroundColor:'#FFF',
        width:screenWidth - 40,
        borderRadius:20
    },
    innerOrderViewStyle:{
        width:cellWH,
        height:cellWH,
        marginLeft:vMargin,
        marginTop:hMargin,
        alignItems:'center',
        justifyContent:'center',
        backgroundColor:'#FFFFFF'
    },
     titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 18,
        color:'#333333',
        backgroundColor:'#FFF',
        fontWeight:'bold',
        marginLeft:0,
    },
    greyTextStyle: {
        fontSize:14,
        color:'#33333399',
        backgroundColor:'#FFF',
    },
    blackTextStyle: {
        fontSize:15,
        color:'#333333',
    },
    redTextStyle: {
        fontSize:16,
        color:'#E41F00',
        fontWeight:'bold',
    },

    pictureTextStyle: {
        fontSize: 16,
        color:'#AAA'
    },

    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyRowLeftView:{
        width:screenWidth/2-40,
        flexDirection:'row'
    },
    bodyRowRightView:{
        flexDirection:'row',
        alignItems:'flex-start',
        paddingLeft:10,
        marginRight:5,
        justifyContent:'flex-start',
        alignContent:'flex-start'
    },
    searchInputText: {
        width: screenWidth / 1.5,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    inputRowStyle: {
        height: 40,
        width:screenWidth -40,
        marginTop:5,
        marginLeft:20,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#F2F5FC",
        backgroundColor:"#F2F5FC",
        borderRadius:20
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft:15
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginTop: 15,
        fontSize: 16
    },
    classViewStyle:{
        backgroundColor:'#FFFFFF',
        height:0,
        alignItems:'flex-start',
        justifyContent:'center',
        // borderBottomWidth:1,
        // borderBottomColor:'#E0E0E0'
        position:'absolute',
        top:0
    },
    classTextStyle:{
        color:'#000',
        fontSize:16,
        fontWeight:'bold',
        marginLeft:15
    },
    innerViewImageStyle:{
        width:cellWH-50,
        height:cellWH-50
    },
    wrapper: {
        justifyContent:'center'
    },
    slide: {
        // flex:1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#9DD6EB',
        width:350,
        height:200,
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
    },
    itemContentRightChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
    },
    enterpriseLogoStyle:{
        borderRadius:10,
        width:50,
        height:50,
        marginTop:15
    },
    selectedBlockItemTextStyle: {
        fontSize: 17,
        // fontFamily: "PingFangSC-Medium,PingFang SC",
        fontWeight: 'bold',
        color: '#255BDA',
        marginBottom:5
    },
    blockItemTextStyle: {
        fontSize:17,
        color:'#000000',
        marginBottom:5
    }

})
module.exports = NaicaiIndex;
