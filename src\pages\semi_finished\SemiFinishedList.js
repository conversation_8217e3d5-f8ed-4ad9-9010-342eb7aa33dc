import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  Linking,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';

import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
class SemiFinishedList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      productionTime: null,
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 10,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      // 不显示相关字段的租户列表
      excludeTenantIdList: [59, 66, 67],
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  initProductionTime = () => {
    // 当前时间
    var currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth());
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _productionTime =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      selectProductionTime: [
        currentDate.getFullYear(),
        currentDateMonth,
        currentDateDay,
      ],
      productionTime: _productionTime,
    });
    return _productionTime;
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');

    var _productionTime = this.initProductionTime();
    console.log('componentWillMount==_productionTime', _productionTime);

    this.loadSemiFinishedList(_productionTime);
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/semi/finished/check/list';
    let loadRequest = {
      checkState: '0AA',
      currentPage: 1,
      productionTime: this.state.productionTime,
      pageSize: this.state.pageSize,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      (this.state.currentPage == 1 ||
        this.state.totalRecord <= this.state.pageSize) &&
      this.state.productionTime == null
    ) {
      console.log('==========不刷新=====');
      return;
    }
    var _productionTime = this.initProductionTime();
    this.setState({
      productionTime: _productionTime,
      currentPage: 1,
    });
    let url = '/biz/semi/finished/check/list';
    let loadRequest = {
      checkState: '0AA',
      currentPage: 1,
      pageSize: this.state.pageSize,
      productionTime: _productionTime,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadSemiFinishedList();
  };

  loadSemiFinishedList = (_productionTime) => {
    let url = '/biz/semi/finished/check/list';
    let loadRequest = {
      checkState: '0AA',
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      productionTime: _productionTime
        ? _productionTime
        : this.state.productionTime,
    };
    httpPost(url, loadRequest, this.callBackLoadSemiFinishedList);
  };

  callBackLoadSemiFinishedList = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      if (dataAll.length > response.data.totalRecord) {
        this.setState({
          refreshing: false,
        });
        console.log(
          '=====数据错误了========' +
            dataAll.length +
            '/' +
            response.data.totalRecord,
        );
        return;
      }
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteSemiFinished = (checkId) => {
    console.log('=======delete=checkId', checkId);
    let url = '/biz/semi/finished/check/delete';
    let requestParams = {checkId: checkId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.checkId} style={styles.innerViewStyle}>
        {index == 0 ? (
          <View style={CommonStyle.lineListHeadRenderRowStyle}></View>
        ) : (
          <View></View>
        )}
        <View style={CommonStyle.titleViewStyleSpecial}>
          <Text style={CommonStyle.titleTextStyleSpecial}>
            机台：{item.machineName}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            生产时间：{item.productionTime}
          </Text>
        </View>
        <View style={[CommonStyle.newTitleViewStyle]}>
          <View>
            <Text
              style={[CommonStyle.newTitleTextStyle, {width: null}]}
              numberOfLines={2}>
              砖型：
            </Text>
          </View>
          <View>
            <Text style={[CommonStyle.newTitleTextStyle]} numberOfLines={2}>
              {item.orderName}
            </Text>
          </View>
        </View>

        {/* <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>砖型：{item.orderName}</Text>
                </View> */}

        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            记录时间：{item.gmtCreated}
          </Text>
        </View>
        <View style={[CommonStyle.titleViewStyle, {}]}>
          <View style={[styles.itemContentLeftChildViewStyle, {}]}>
            <Text style={CommonStyle.titleTextStyle}>
              数量(块)：{item.brickAmount}
            </Text>
            <Text style={CommonStyle.titleTextStyle}>
              重量(吨)：
              {item.unitWeight
                ? ((item.unitWeight * item.brickAmount * 0.96) / 1000).toFixed(
                    2,
                  )
                : '无'}
            </Text>
            <Text style={CommonStyle.titleTextStyle}>
              员工：
              {item.staffNameList
                ? item.staffNameList.map((item) => item + ' ')
                : '无'}
            </Text>
            <Text style={CommonStyle.titleTextStyle}>
              累计块数：{item.completeBrickAmount}
            </Text>
            <Text style={CommonStyle.titleTextStyle}>
              废品数：{item.wasteNumber ? item.wasteNumber : '无'}
            </Text>
            {!this.state.excludeTenantIdList.includes(
              constants.loginUser.tenantId,
            ) ? (
              <View>
                <Text style={CommonStyle.titleTextStyle}>
                  锤数：{item.hammerNumber ? item.hammerNumber : '无'}
                </Text>
              </View>
            ) : (
              <View />
            )}
          </View>
          <View style={styles.itemContentRightChildViewStyle}>
            <Text style={CommonStyle.titleTextStyle}>
              单重(Kg)：{item.unitWeight ? item.unitWeight : '无'}
            </Text>
            <Text style={CommonStyle.titleTextStyle}>
              班次：{item.shiftName ? item.shiftName : '无'}
            </Text>
            <Text style={CommonStyle.titleTextStyle}>
              代班：{item.standIn ? item.standIn : '无'}
            </Text>
            {!this.state.excludeTenantIdList.includes(
              constants.loginUser.tenantId,
            ) ? (
              <View>
                <Text style={CommonStyle.titleTextStyle}>
                  抽检尺寸：{item.samplingSize ? item.samplingSize : '无'}
                </Text>
                <Text style={CommonStyle.titleTextStyle}>
                  单重(湿重)：{item.singleWeight ? item.singleWeight : '无'}
                </Text>
              </View>
            ) : (
              <View />
            )}
          </View>
        </View>
        <View
          style={[
            CommonStyle.blockTwoEditDelStyle,
            {marginRight: 15, marginBottom: -10},
          ]}>
          <TouchableOpacity
            onPress={() => {
              if (
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
              ) {
                return;
              }
              Alert.alert('确认', '您确定要删除该条点验吗？', [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                    // this在这里可用，传到方法里还有问题
                    // this.props.navigation.goBack();
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.deleteSemiFinished(item.checkId);
                  },
                },
              ]);
            }}>
            <View
              style={[
                CommonStyle.btnTwoDeleteBtnView,
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
                  ? CommonStyle.disableViewStyle
                  : '',
              ]}>
              <Image
                style={CommonStyle.btnTwoDeleteBtnImage}
                source={require('../../assets/icon/iconfont/delete.png')}></Image>
              <Text style={CommonStyle.btnTwoDeleteBtnText}>删除</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
              ) {
                return;
              }
              this.props.navigation.navigate('SemiFinishedAdd', {
                // 传递参数
                checkId: item.checkId,
                // 班次名称
                shiftName: item.shiftName,
                // staffName:item.staffName,
                shiftId: item.shiftId,
                productionLineId: item.productionLineId,
                // 传递回调函数
                refresh: this.callBackFunction,
              });
            }}>
            <View
              style={[
                CommonStyle.btnTwoEditBtnView,
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
                  ? CommonStyle.disableViewStyle
                  : '',
              ]}>
              <Image
                style={CommonStyle.btnTwoEditBtnImage}
                source={require('../../assets/icon/iconfont/edit.png')}></Image>
              <Text style={CommonStyle.btnTwoEditBtnText}>编辑</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
      // </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[{flexDirection: 'row', alignItems: 'center'}]}>
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={{color: '#3C6CDE', fontWeight: 'bold'}}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      // <TouchableOpacity onPress={() => {
      //     this.props.navigation.navigate("SemiFinishedAdd",
      //     {
      //         // 传递回调函数
      //         refresh: this.callBackFunction
      //     })
      // }}>
      //     {/* <Text style={CommonStyle.headRightText}>半成品点验</Text> */}
      //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
      // </TouchableOpacity>
      <View
        style={{flexDirection: 'row-reverse', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('SemiFinishedAdd', {
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Image
            style={{width: 22, height: 22, marginVertical: 2}}
            source={require('../../assets/icon/iconfont/add.png')}></Image>
        </TouchableOpacity>
      </View>
    );
  }
  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/semi_finished_check';
    let requestParams = {
      checkState: '0AA',
      productionTime: this.state.productionTime,
      currentPage: 1,
      pageSize: 1000,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  openProductionTime() {
    this.refs.SelectProductionTime.showDate(this.state.selectProductionTime);
  }
  callBackSelectProductionTimeValue(value) {
    console.log('==========生产时间选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectProductionTime: value,
    });
    if (
      this.state.selectProductionTime &&
      this.state.selectProductionTime.length
    ) {
      var productionTime = '';
      var vartime;
      for (
        var index = 0;
        index < this.state.selectProductionTime.length;
        index++
      ) {
        vartime = this.state.selectProductionTime[index];
        if (index === 0) {
          productionTime += vartime;
        } else if (index < 3) {
          productionTime += '-' + vartime;
        } else if (index === 3) {
          productionTime += ' ' + vartime;
        } else {
          productionTime += ':' + vartime;
        }
      }
      this.setState({
        currentPage: 1,
        productionTime: productionTime,
      });

      let url = '/biz/semi/finished/check/list';
      let loadRequest = {
        checkState: '0AA',
        currentPage: 1,
        productionTime: productionTime,
        pageSize: this.state.pageSize,
      };
      httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
  }

  _callBackLoadBrickTypeData = (response) => {
    if (response.code == 200 && response.data) {
      this.setState({
        brickTypeName: response.data.brickTypeName,
        brickTypeId: response.data.brickTypeId,
      });
      let url = '/biz/brick/series/type/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        isInventoryQuery: true,
        brickTypeId: response.data.brickTypeId,
      };
      httpPost(url, loadRequest, this._loadFreshDataCallBack);
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
      this.setState({
        brickTypeName: '',
        brickTypeId: '',
      });
    }
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="半成品管理"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View style={[CommonStyle.rightAbsoluteButtonContainer]}>
          <View style={[CommonStyle.rightAbsoluteButtonView]}>
            <TouchableOpacity onPress={() => this.openProductionTime()}>
              <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>
                {!this.state.productionTime
                  ? '时间'
                  : this.state.productionTime}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={[CommonStyle.rightAbsoluteButtonView, {width: 90}]}>
            <TouchableOpacity
              onPress={() => {
                Alert.alert('确认', '您确定要导出PDF文件吗？', [
                  {
                    text: '取消',
                    onPress: () => {
                      WToast.show({data: '点击了取消'});
                    },
                  },
                  {
                    text: '确定',
                    onPress: () => {
                      WToast.show({data: '点击了确定'});
                      this.exportPdfFile();
                    },
                  },
                ]);
              }}>
              <View style={[CommonStyle.rightAbsoluteButtonBoxView]}>
                <Image
                  style={[CommonStyle.rightAbsoluteButtonIconView]}
                  source={require('../../assets/icon/iconfont/output.png')}></Image>
                <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>
                  导出
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <View style={CommonStyle.contentViewStyle}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>  */}
          <FlatList
            data={this.state.dataSource}
            ItemSeparatorComponent={this.space}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.checkId}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
          {/* </ScrollView> */}
        </View>
        <BottomScrollSelect
          ref={'SelectProductionTime'}
          callBackDateValue={this.callBackSelectProductionTimeValue.bind(this)}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    marginTop: 10,
    marginBottom: 10,
    // borderColor:"#F4F4F4",
    // borderWidth:8
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
  },
  titleTextStyle: {
    fontSize: 23,
  },
  itemContentStyle: {
    flexDirection: 'row',
    // alignItems:'center',
    justifyContent: 'space-between',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginTop: 10,
  },
  itemContentLeftChildViewStyle: {
    flexDirection: 'column',
    // alignContent:'flex-start',
    // justifyContent:'flex-start',
    // alignItems:'flex-start',
    width: screenWidth * 0.4,
  },
  itemContentRightChildViewStyle: {
    flexDirection: 'column',
    // alignContent:'flex-start',
    // justifyContent:'flex-start',
    // alignItems:'flex-start',
    width: screenWidth * 0.5,
  },
  itemContentChildTextStyle: {
    // marginLeft:10,
    marginBottom: 10,
    fontSize: 16,
  },
  itemBottomBtnStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  itemBottomDeleteBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 1,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    borderRadius: 4,
  },
  itemBottomEditBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: '#CB4139',
    borderRadius: 4,
  },
  itemBottomEditBtnTextStyle: {
    color: '#F0F0F0',
  },
});
module.exports = SemiFinishedList;
