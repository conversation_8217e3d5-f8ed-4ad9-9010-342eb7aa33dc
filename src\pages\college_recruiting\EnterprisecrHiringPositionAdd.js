import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,KeyboardAvoidingView,
    FlatList,RefreshControl,ScrollView,TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class EnterprisecrHiringPositionAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            positionId:"",
            positionName:"",
            positionTreatment:"",
            positionDescribe:"",
            positionType:"",
            workingPlace:"",
            positionRequirements:"",
            positionTypeId:"",
            positionTypeName:"",
            positionTypeDataSource:[],
            selectPositionType:[],
            positionSalaryFrom:"",
            positionSalaryTo:"",
            hiringNumber:"",
            enterpriseId:"",
            defaultEnterpriseName:"",
            enterpriseName:"",
            enterpriseDataSource:[]
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 加载岗位类型列表
        this.loadPositionTypeList();
        // 加载企业
        this.loadEnterpriseList();
        if(constants && constants.loginUser){
            if (constants.loginUser.enterpriseId) {
                console.log("=============enterpriseId" + constants.loginUser.enterpriseId + "")
                this.setState({
                    enterpriseId:constants.loginUser.enterpriseId
                }) 
                let loadTypeUrl= "/biz/enterprise/get";
                let loadRequest={'enterpriseId':constants.loginUser.enterpriseId};
                httpPost(loadTypeUrl, loadRequest, this.loadEnterpriseDataCallBack);
            }
        }
        
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { positionId } = route.params;
            if (positionId) {
                console.log("=============positionId" + positionId + "");
                this.setState({
                    positionId:positionId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/hiring/position/get";
                loadRequest={'positionId':positionId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditHiringPositionDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }

    loadEnterpriseDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            console.log("=============defaultEnterpriseName" + response.data.enterpriseName)
            this.setState({
                defaultEnterpriseName:response.data.enterpriseName
            })
        }
    }

    loadEditHiringPositionDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                positionName:response.data.positionName,
                positionTreatment:response.data.positionTreatment,
                positionDescribe:response.data.positionDescribe,
                workingPlace:response.data.workingPlace,
                positionRequirements:response.data.positionRequirements,
                positionType:response.data.positionType,
                positionTypeName:response.data.positionTypeName,
                selectPositionType:[response.data.positionTypeName],
                positionSalaryFrom:response.data.positionSalaryFrom,
                positionSalaryTo:response.data.positionSalaryTo,
                hiringNumber:response.data.hiringNumber,
                enterpriseName:response.data.enterpriseName ,
                selectEnterprise:[response.data.enterpriseName]
            })
        }
    }

    loadPositionTypeList=()=>{
        let url = "/biz/hiring/position/positionTypeList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadPositionTypeListCallBack);
    }

    loadPositionTypeListCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                positionTypeDataSource: response.data,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEnterpriseList=()=>{
        let url= "/biz/enterprise/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadEnterpriseListCallBack);
    }

    loadEnterpriseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            this.setState({
                enterpriseDataSource:data
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 渲染企业底部滚动数据
    openEnterpriseSelect() {
        if (!this.state.enterpriseDataSource || this.state.enterpriseDataSource.length < 1) {
            WToast.show({ data: "请先添加企业" });
            return
        }
        this.refs.SelectEnterprise.showEnterprise(this.state.selectEnterprise, this.state.enterpriseDataSource)
    }

    callBackEnterpriseValue(value) {
        console.log("==========企业选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectEnterprise: value
        })
        var enterpriseName = value.toString();
        // this.setState({
        //     enterpriseName:enterpriseName
        // })
        let loadUrl = "/biz/enterprise/getEnterpriseByName";
        let loadRequest = {
            "enterpriseName": enterpriseName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadEnterpriseDetailData);
    }

    callBackLoadEnterpriseDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                enterpriseName:response.data.enterpriseName,
                enterpriseId: response.data.enterpriseId,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    saveHiringPosition =()=> {
        console.log("=======saveHiringPosition");
        let toastOpts;
        if (!this.state.defaultEnterpriseName && !this.state.enterpriseName) {
            toastOpts = getFailToastOpts("请选择所属企业");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.positionName) {
            toastOpts = getFailToastOpts("请输入岗位名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.positionType) {
            toastOpts = getFailToastOpts("请选择岗位类型");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/hiring/position/add";
        if (this.state.positionId) {
            console.log("=========Edit===positionId", this.state.positionId)
            url= "/biz/hiring/position/modify";
        }
        let requestParams={
            "positionId":this.state.positionId,
            "positionName":this.state.positionName,
            "positionTreatment":this.state.positionTreatment,
            "positionDescribe":this.state.positionDescribe,
            "enterpriseId":constants.loginUser.enterpriseId ? constants.loginUser.enterpriseId : this.state.enterpriseId,
            "positionType":this.state.positionType,
            "workingPlace":this.state.workingPlace,
            "positionRequirements":this.state.positionRequirements,
            "positionSalaryFrom":this.state.positionSalaryFrom,
            "positionSalaryTo":this.state.positionSalaryTo,
            "hiringNumber":this.state.hiringNumber
        };
        httpPost(url, requestParams, this.saveHiringPositionCallBack);
    }
    
    // 保存回调函数
    saveHiringPositionCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("EnterprisecrHiringPositionList", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>招聘岗位</Text>
            </TouchableOpacity>
        )
    }

    openPositionType() {
        if(!this.state.positionTypeDataSource || this.state.positionTypeDataSource.length == 0){
            console.log("=============无数据")
            return;
        }
        this.refs.SelectPositionType.showPositionType(this.state.selectPositionType, this.state.positionTypeDataSource);
    }

    callBackPositionTypeValue(value) {
        console.log("==========岗位类型选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectPositionType: value
        })
        var positionTypeName = value.toString();
        this.setState({
            positionTypeName: positionTypeName
        })
        let loadUrl = "/biz/hiring/position/positionTypeByName";
        let loadRequest = {
            "positionTypeName": positionTypeName
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    positionType: response.data.positionType,
                })
            }
            else if (response.code == 401) {
                WToast.show({ data: response.message });
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }


    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '岗位'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.formContentViewStyle]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>所属企业</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        {
                            this.state.defaultEnterpriseName ?
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>{this.state.defaultEnterpriseName}</Text>
                            </View>
                            :
                            <TouchableOpacity onPress={()=>this.openEnterpriseSelect()}>
                                <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 5) }]}>
                                    <Text style={{color:'#A0A0A0', fontSize:15}}>
                                        {!this.state.enterpriseName ? "请选择所属企业" : this.state.enterpriseName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        }
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>岗位名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入岗位名称'}
                            onChangeText={(text) => this.setState({positionName:text})}
                        >
                            {this.state.positionName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>岗位类型</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openPositionType()}>
                                <View style={CommonStyle.inputTextStyleTextStyle}>
                                    <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                        {!this.state.positionTypeName ? "请选择岗位类型" : this.state.positionTypeName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>工作地点</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入工作地点'}
                            onChangeText={(text) => this.setState({workingPlace:text})}
                        >
                            {this.state.workingPlace}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>岗位描述</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:150}]}
                            placeholder={'请输入岗位描述'}
                            onChangeText={(text) => this.setState({positionDescribe:text})}
                        >
                            {this.state.positionDescribe}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>任职要求</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:150}]}
                            placeholder={'请输入任职要求'}
                            onChangeText={(text) => this.setState({positionRequirements:text})}
                        >
                            {this.state.positionRequirements}
                        </TextInput>
                    </View>
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>最低薪资</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入最低薪资'}
                            onChangeText={(text) => this.setState({positionSalaryFrom:text})}
                        >
                            {this.state.positionSalaryFrom}
                        </TextInput>
                    </View> */}
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>最高薪资</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入最高薪资'}
                            onChangeText={(text) => this.setState({positionSalaryTo:text})}
                        >
                            {this.state.positionSalaryTo}
                        </TextInput>
                    </View> */}
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>招聘人数</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入招聘人数'}
                            onChangeText={(text) => this.setState({hiringNumber:text})}
                        >
                            {this.state.hiringNumber}
                        </TextInput>
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>薪资待遇</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:150}]}
                            placeholder={'请输入薪资待遇'}
                            onChangeText={(text) => this.setState({positionTreatment:text})}
                        >
                            {this.state.positionTreatment}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveHiringPosition.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect
                        ref={'SelectPositionType'}
                        callBackPositionTypeValue={this.callBackPositionTypeValue.bind(this)}
                    />
                    <BottomScrollSelect
                        ref={'SelectEnterprise'}
                        callBackEnterpriseValue={this.callBackEnterpriseValue.bind(this)}
                    />
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
});