import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,ImageFlatList,Image,TouchableOpacity,Dimensions,number} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;


var screenHeight = Dimensions.get('window').height;
export default class NaturalGasPurchaseAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            flowId:"",
            flowValue:"",
            operate:"",
            //接班数字
            flowStartValue:"",
            //交班数字
            flowEndValue:"",
            addDate:"",
            selectAddDate:[],
            //供应商
            supplier:"",
            // 剩余流量
            leftValue:"",
            finalLeftValue:"",
            consumeValue:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        const { flowId } = this.props;
        if (route && route.params) {
            const { flowId , shiftName, staffName, productionLineId,shiftId} = route.params;
            if (flowId) {
                this.setState({
                    operate:"编辑",
                    flowId:flowId
                })
                loadTypeUrl= "/biz/natural/gas/flow/get";
                loadRequest={'flowId':flowId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditNaturalGasPurchaseCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                var addDate = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
                this.setState({
                    selectAddDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    addDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })   
                this.loadLeftValue(addDate);
                this.loadConsumeValue(addDate);
            }
        }
    }
    
    loadLeftValue=(addTime)=>{
        let url= "/biz/natural/gas/flow/leftValue";
        let loadRequest={
            "flowType":"O",
            "qryConsumeTime":addTime
        };
        httpPost(url, loadRequest, this.callBackLoadLeftValue);
    }

    callBackLoadLeftValue=(response)=>{
        if (response.code == 200) {
            this.setState({
                leftValue:response.data,
                finalLeftValue:response.data,
            })
        }
    }

    loadConsumeValue=(addTime)=>{
        let url= "/biz/natural/gas/flow/consumeValue";
        let loadRequest={
            "flowType":"O",
            "qryConsumeTime":addTime
        };
        httpPost(url, loadRequest, this.callBackLoadConsumeValue);
    }

    callBackLoadConsumeValue=(response)=>{
        if (response.code == 200) {
            this.setState({
                consumeValue:response.data,
            })
        }
    }

    loadEditNaturalGasPurchaseCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            var addDate;
            if (response.data.addTime) {
                addDate = response.data.addTime.split("-");
            }
            this.setState({
                addDate:response.data.addTime,
                selectAddDate:addDate,
                // selKilnRoadId:response.data.productionLineId,
                // workingShiftId:response.data.shiftId,
                // staffId:response.data.staffId,
                // flowStartValue:response.data.flowStartValue,
                // flowEndValue:response.data.flowEndValue,
                flowValue:response.data.flowValue,
                supplier:response.data.supplier,
                
            })
            let url= "/biz/natural/gas/flow/leftValue";
            let loadRequest={
                "flowType":"O",
                "qryCreatedTime":response.data.gmtCreated
            };
            httpPost(url, loadRequest, this.callBackLoadLeftValue);
            this.loadConsumeValue(response.data.addTime);
        }
    }

    openAddDate(){
        if(this.state.flowId) {
            return;
        }
        this.refs.SelectAddDate.showDate(this.state.selectAddDate)
    }
    
    callBackSelectAddDate(value){
        console.log("==========计划收款时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectAddDate:value
        })
        if (value && value.length) {
            var addDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    addDate += vartime;
                }
                else{
                    addDate += "-" + vartime;
                }
            }
            this.setState({
                addDate:addDate
            })
            this.loadLeftValue(addDate);
            this.loadConsumeValue(addDate);
        }
    }


    emptyComponent() {
        return <EmptyRowViewComponent/>
    }


    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("NaturalGasPurchaseList", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Text style={CommonStyle.headRightText}>天燃气购进</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>新增天然气购进</Text>
                </TouchableOpacity>
            </View>

        )
    }

    // 保存回调函数
    saveNaturalGasPurchaseCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }


    saveNaturalGasPurchase =()=> {
        console.log("=======saveNaturalGasFlow");
        let toastOpts;
        if (!this.state.addDate) {
            toastOpts = getFailToastOpts("请选择日期");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.flowValue) {
            toastOpts = getFailToastOpts("请输入购进量");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.supplier) {
            toastOpts = getFailToastOpts("请输入供应商");
            WToast.show(toastOpts)
            return;
        }
        
        let url= "/biz/natural/gas/flow/add";
        if (this.state.flowId) {
            console.log("=========Edit===flowId", this.state.flowId)
            url= "/biz/natural/gas/flow/modify";
        }
        let requestParams={
            flowId:this.state.flowId,
            addTime:this.state.addDate,
            flowValue:this.state.flowValue,
            supplier:this.state.supplier,
            flowType:"I",
            operator:constants.loginUser.userName
        };
        httpPost(url, requestParams, this.saveNaturalGasPurchaseCallBack);
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '购进'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>购进日期</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={()=>this.openAddDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth+60),borderWidth:0}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.addDate ? "请选择日期" : this.state.addDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>购进量(m³)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='number'//此处数据类型可能不妥
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60)}]}
                            placeholder={'请输入'}
                            onChangeText={(number) => this.setState({flowValue:number,finalLeftValue:this.state.leftValue * 1 + number * 1})}
                        >
                            {this.state.flowValue}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>供应商</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60)}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({supplier:text})}
                        >
                            {this.state.supplier}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>剩余量(m³)</Text>
                            
                        </View>
                        <TextInput
                            editable={false}
                            //keyboardType='text'
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60)}]}
                            placeholder={'0'}
                        >
                            {this.state.leftValue * 1 + this.state.flowValue * 1}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>消耗量(m³)</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            editable={false}
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60)}]}
                            placeholder={'0'}
                        >
                            {this.state.consumeValue}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    <View style={{height:ifIphoneXContentViewHeight()-270-86, backgroundColor:'#F2F5FC'}}>
                        {/* <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                        >
                        </TextInput> */}
                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{ marginTop: 0 }]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                            {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveNaturalGasPurchase.bind(this)}
                        >
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                            {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect 
                        ref={'SelectAddDate'} 
                        callBackDateValue={this.callBackSelectAddDate.bind(this)}
                    />
                </ScrollView>    
            </View>
        )
    }
}
const styles = StyleSheet.create({
// contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:6,
        marginRight:5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:5,
        marginRight:5,
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }

});