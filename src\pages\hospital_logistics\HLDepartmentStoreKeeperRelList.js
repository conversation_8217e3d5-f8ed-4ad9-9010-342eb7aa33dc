import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class HLDepartmentStoreKeeperRelList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            departmentId:"",
            departmentName:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { departmentId ,departmentName} = route.params;
            if (departmentId) {
                console.log("=============departmentId" + departmentId + "");
                this.setState({
                    departmentId:departmentId,
                })
                this.loadDepartmentStoreKeeperRelList(departmentId);                
            }
            if (departmentName) {
                console.log("=============departmentName" + departmentName + "");
                this.setState({
                    departmentName:departmentName,
                })               
            }
        }

    }

    loadDepartmentStoreKeeperRelList=(departmentId)=>{
        let url = "/biz/hl/department/store/keeper/rel/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "departmentId":departmentId ? departmentId : this.state.departmentId
        };
        httpPost(url, loadRequest, this.loadDepartmentStoreKeeperRelListCallBack);
    }

    loadDepartmentStoreKeeperRelListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }

    }
    deleteUser =(departmentUserRelId)=> {
        console.log("=======delete=departmentUserRelId", departmentUserRelId);
        let url= "/biz/hl/department/store/keeper/rel/delete";
        let requestParams={'departmentUserRelId':departmentUserRelId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }
    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            let toastOpts;
            toastOpts = getSuccessToastOpts('删除完成');
            WToast.show(toastOpts);
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }
    // 回调函数
    callBackFunction = () => {
        let url = "/biz/hl/department/store/keeper/rel/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId":this.state.departmentId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    
    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadDepartmentStoreKeeperRelList;
    }
    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "departmentId":departmentId ? departmentId : this.state.departmentId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    renderRow=(item, index)=>{
        return (
            <View key={item.jobUserId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>员工姓名：{item.userName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系电话：{item.userNbr}</Text>
                </View> 
                {/*<View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>：{item.gmtCreated }</Text>
                </View>*/}
                
                    <View style={[{width: 40, height: 40, 
                        backgroundColor: 'rgba(255,0,0,0.0)', 
                        position:'absolute', 
                        alignItems:'center',
                        justifyContent:'center',
                        right: 20,bottom:10
                        }]}>
                            <TouchableOpacity onPress={()=>{
                                Alert.alert('确认','确定将该员工移除吗？',[
                                    {
                                        text:"取消", onPress:()=>{
                                            let toastOpts;
                                            toastOpts = getSuccessToastOpts('点击了取消');
                                            WToast.show(toastOpts);
                                        }
                                    },
                                    {
                                        text:"确定", onPress:()=>{
                                            let toastOpts;
                                            toastOpts = getSuccessToastOpts('点击了确定');
                                            WToast.show(toastOpts);
                                            this.deleteUser(item.departmentUserRelId)
                                        }
                                    }
                                ]);
                            }}>
                                {/* <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>离岗</Text> */}
                                <Image style={{width:70, height:35}} source={require('../../assets/icon/iconfont/out.png')}></Image>
                            </TouchableOpacity>
                    </View>
            </View>
        )
    }
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLDepartmentStoreKeeperRelAdd", 
                {
                    departmentName:this.state.departmentName,
                    departmentId:this.state.departmentId,
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 分隔线
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title='库管员设置'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle,{marginLeft:10, fontWeight:'bold', marginRight:100}]}>科室名称：{this.state.departmentName}</Text>
                    </View>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />

                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle: {
        // marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});