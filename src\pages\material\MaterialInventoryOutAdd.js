import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,
    TouchableOpacity,Dimensions,Modal,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class MaterialInventoryOutAdd extends Component {
    constructor(){
        super()
        this.state = {
            inventoryId: "",
            classifyId: "",
            classifyName: null,
            io:'O',
            weight: "",
            ioDate:"",
            operate:"",
            selectIoDate:[],
            selOrderId:null,
            selOrderName:"",
            selSeriesId:null,
            selSeriesName:"",
            materialClassifyDataSource:[],
            materialNameDataSource:[],
            modal:false,
            searchKeyWord:null,
            seriesDataSource:[],
            materialTypeList:[
                {
                    typeId:0,
                    type:"Y",
                    typeName:"定型"
                },
                {
                    typeId:1,
                    type:"N",
                    typeName:"不定型"
                }
            ],
            selMaterialType:"Y",
            reviewerName:"",
            reviewerId:"",
            storageLocationDataSource:[],
            storageLocationAreaDataSource:[],
            selBrickTypeId:"",
            selLocationId:"",
            selLocationAreaId:0,
            selLocationCurrentInventory:0
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadreviewer();
        this.loadInitData();
        let loadTypeUrl;
        let loadRequest;

        // 原料类别
        loadTypeUrl= "/biz/material/classify/list";
        loadRequest={
            'currentPage':1,
            'pageSize':100,
            "parentClassifyId": null
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    materialClassifyDataSource:response.data.dataList,
                })
            }
        });

        // 加载排产状态的订单，显示砖型
        this.loadOrder();
        
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { inventoryId, classifyId, classifyName } = route.params;
            if (classifyId) {
                this.setState({
                    classifyId:classifyId,
                })
            }
            if (classifyName) {
                this.setState({
                    classifyName:classifyName,
                })
            }
            if (inventoryId) {
                console.log("========Edit==inventoryId:", inventoryId);
                this.setState({
                    inventoryId:inventoryId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/material/inventory/get";
                loadRequest={'inventoryId':inventoryId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditPurchaseDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectIoDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    ioDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
    }

    // 加载对应库区的库位信息
    loadStorageLocationByAreaId=(locationAreaId)=>{
        let url= "/biz/storage/location/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':1000,
            'locationAreaId':locationAreaId,
            "locationType":'M'
        };
        httpPost(url, loadRequest, this.callBackLoadStorageLocationByAreaId);
    }

    // 库位信息回调
    // callBackLoadStorageLocationByAreaId=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         if (response.data.dataList.length <= 0) {
    //             let toastOpts = getFailToastOpts("请联系管理员添加库位");
    //             WToast.show(toastOpts);
    //             this.setState({
    //                 storageLocationDataSource:[]
    //             })
    //             return;
    //         }
    //         this.setState({
    //             storageLocationDataSource:response.data.dataList,
    //         })
    //         if (this.state.inventoryId && this.state.selLocationId != 0) {
    //             this.setState({
    //                 selLocationId:this.state.selLocationId,
    //             })
    //         }
    //         else if (response.data.dataList.length > 0) {
    //             this.setState({
    //                 selLocationId:response.data.dataList[0].locationId
    //             })
    //         }
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }


    loadInitData=()=>{
        // 加载库区列表
        let url= "/biz/storage/location/area/list";
        let request={'currentPage':1,'pageSize':1000};
        httpPost(url, request, this.callBackLoadStorageLocationArea);
    }

    // 库区回调加载
    callBackLoadStorageLocationArea=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库区");
                WToast.show(toastOpts);
                return;
            }
            this.setState({
                storageLocationAreaDataSource:response.data.dataList,
            })
            const { route, navigation } = this.props;
            if (route && route.params) {
                const { inventoryId,locationAreaId } = route.params;
                if(locationAreaId){
                    this.setState({
                        selLocationAreaId:locationAreaId,
                    })
                    // // 库位库存信息查询
                    // let loadTypeUrl;
                    // let loadRequest;
                    // loadTypeUrl= "/biz/material/inventory/locationInventoryList";
                    // loadRequest={
                    //     "classifyId": this.state.classifyId,
                    //     "locationAreaId":locationAreaId,
                    // };
                    // httpPost(loadTypeUrl, loadRequest, this.loadLocationInventoryListCallBack);
                    // this.loadStorageLocationByAreaId(locationAreaId)
                }
                else if (response.data.dataList.length > 0) {
                    this.setState({
                        selLocationAreaId:response.data.dataList[0].locationAreaId
                    })
                    // 库位库存信息查询
                    let loadTypeUrl;
                    let loadRequest;
                    loadTypeUrl= "/biz/material/inventory/locationInventoryList";
                    loadRequest={
                        "classifyId": this.state.classifyId,
                        "locationAreaId":response.data.dataList[0].locationAreaId
                    };
                    httpPost(loadTypeUrl, loadRequest, this.loadLocationInventoryListCallBack);

                    // this.loadStorageLocationByAreaId(response.data.dataList[0].locationAreaId);
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadreviewer = ()=>{
        let loadTypeUrl= "/biz/material/inventory/reviewer";
        let loadRequest={
            "io":this.state.io,
            "operaterId": constants.loginUser.userId
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    reviewerName:response.data.userName,
                    reviewerId:response.data.userId,
                })
            }
        });
    }

    loadEditPurchaseDataCallBack=(response)=>{

        if (response.code == 200 && response.data) {
            var selectIoDate;
            if (response.data.ioDate) {
                selectIoDate = response.data.ioDate.split("-");
            }
            else {
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                selectIoDate = [currentDate.getFullYear(), currentDateMonth, currentDateDay];
                this.setState({
                    selectIoDate:selectIoDate,
                })
            }
            console.log("=========response.data=========:", response.data);
            this.setState({
                inventoryId:response.data.inventoryId,
                classifyId: response.data.classifyId,
                io: response.data.io,
                weight: response.data.weight,
                ioDate: response.data.ioDate,
                selSeriesId:response.data.seriesId,
                selSeriesName:response.data.seriesName,
                selOrderId: response.data.orderId,
                selOrderName:response.data.orderName,
                selectIoDate:selectIoDate,
                parentClassifyId:response.data.parentClassifyId,
                selMaterialType:response.data.materialType,
                selLocationAreaId:response.data.locationAreaId,
                selLocationId:response.data.locationId,
            })
            

            // console.log("目前的两个Id" + response.data.locationAreaId)
            // 调接口查询原料名称
            let loadTypeUrl;
            let loadRequest;
            loadTypeUrl= "/biz/material/classify/list";
            loadRequest={
                'currentPage':1,
                'pageSize':100,
                "parentClassifyId":response.data.parentClassifyId
            };
            httpPost(loadTypeUrl, loadRequest, this.materialNameLoadCallBack);

            loadTypeUrl= "/biz/material/inventory/locationInventoryList";
            loadRequest={
                "classifyId": this.state.classifyId,
                "locationAreaId":response.data.locationAreaId
            };
            httpPost(loadTypeUrl, loadRequest, this.loadLocationInventoryListCallBack);
        }
    }

    materialNameLoadCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                materialNameDataSource:response.data.dataList,
            })
            console.log("=============this.state.materialNameDataSource:", this.state.materialNameDataSource);
        }
    }

    loadOrder=()=>{
        var currentTime = new Date();
        var qryEndTime = new Date(currentTime.getTime() + 8 * 3600000);
        var qryStartTime = new Date(qryEndTime.getTime() - 15 * 86400000);
        console.log("qryEndTime===",qryEndTime);
        console.log("qryStartTime===",qryStartTime);
        let loadTypeUrl= "/biz/order/seriesList";
        let loadRequest={
            'currentPage':1,
            'pageSize':1000,
            "display":"Y",
            "qryContent":"order",
            "searchSeriesKeyWord":this.state.searchKeyWord,
            "excludeOrderStateList":[
                "A","K"
            ],
            "startTime":this.state.selMaterialType == 'Y'?qryStartTime:null,
            "endTime":this.state.selMaterialType == 'Y'?qryEndTime:null,
            "materialOut":"Y",
            "productionLineId":constants.loginUser.tenantId == 66 ? (this.state.selMaterialType == 'Y' ? 52 :(this.state.selMaterialType == 'N' ? 73 : null)) : null,
        };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadOrder);
    }

    // 订单回调加载
    callBackLoadOrder=(response)=>{
        if (response.code == 200 && response.data) {
            if (response.data.length <= 0) {
                WToast.show({data:"没有可以出库的产品"});
                this.setState({
                    seriesDataSource:[],
                })
                return;
            }
            this.setState({
                seriesDataSource:response.data,
            })
            // if (!this.state.inventoryId) {
            //     this.setState({
            //         selBrickTypeId:response.data.dataList[0] ? response.data.dataList[0].brickTypeId : 0,
            //         selOrderId:response.data.dataList[0] ? response.data.dataList[0].orderId : 0,
            //     })
            // }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("MaterialInventoryOutList")
            }}>
                <Text style={CommonStyle.headRightText}>原料出库</Text>
            </TouchableOpacity>
        )
    }

    openIoDate(){
        this.refs.SelectIoDate.showDate(this.state.selectIoDate)
    }
    callBackSelectIoDateValue(value){
        console.log("==========出库时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectIoDate:value
        })
        if (this.state.selectIoDate && this.state.selectIoDate.length) {
            var ioDate = "";
            var vartime;
            for(var index=0;index<this.state.selectIoDate.length;index++) {
                vartime = this.state.selectIoDate[index];
                if (index===0) {
                    ioDate += vartime;
                }
                else{
                    ioDate += "-" + vartime;
                }
            }
            this.setState({
                ioDate:ioDate
            })
        }
    }

    saveMaterialInventoryIn =()=> {
        console.log("=======saveMaterialInventoryIn");
        let toastOpts;
        if (!this.state.classifyId) {
            toastOpts = getFailToastOpts("请选择原料名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selSeriesId && !this.state.selOrderId) {
            toastOpts = getFailToastOpts("没有需求产品,不能出库");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selLocationId) {
            toastOpts = getFailToastOpts("请选择库位");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.weight) {
            toastOpts = getFailToastOpts("请输入重量");
            WToast.show(toastOpts)
            return;
        }
        if(constants.loginUser.tenantId == 66) {
            if (!this.state.selMaterialType) {
                toastOpts = getFailToastOpts("请选择用料类型");
                WToast.show(toastOpts)
                return;
            }
        }
        if (this.state.weight  > this.state.selLocationCurrentInventory) {
            WToast.show({data:"出库重量不能大于当前库位的库存重量[" + this.state.selLocationCurrentInventory + "吨]"});
            return;
        }
        let url= "/biz/material/inventory/add";
        if (this.state.inventoryId) {
            console.log("=========Edit=save===inventoryId", this.state.inventoryId)
            url= "/biz/material/inventory/modify";
        }
        let requestParams={
            "inventoryId": this.state.inventoryId,
            "io": this.state.io,
            "classifyId": this.state.classifyId,
            "weight": this.state.weight,
            "ioDate": this.state.ioDate,
            "seriesId": this.state.selSeriesId,
            "materialType":this.state.selMaterialType,
            "currentAuditUserId": this.state.reviewerId,
            "locationId":this.state.selLocationId,
        };
        httpPost(url, requestParams, this.saveStorageCallBack);
    }
    
    // 保存回调函数
    saveStorageCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    loadLocationInventoryListCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            var storageLocationDataSource = response.data;
            if(storageLocationDataSource && storageLocationDataSource.length > 0) {
                
            }
            else {
                storageLocationDataSource = [{"locationId":0,"locationName":"库存","inventoryInWeight":0,"inventoryOutWeight":0}];
            }

            this.setState({
                storageLocationDataSource:storageLocationDataSource,
            })
        }
    }

    // renderOrderRow=(item)=>{
    //     return (
    //         <TouchableOpacity onPress={() => {
    //             // if (this.state.inventoryId) {
    //             //     return;
    //             // }
    //                 this.setState({
    //                     selBrickTypeId:item.brickTypeId,
    //                     selOrderId:item.orderId,
    //                 })
    //             }}>
    //                 {/* , this.state.inventoryId ? CommonStyle.disableViewStyle : '' */}
    //             <View key={item.orderId} style={[item.orderId===this.state.selOrderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
    //                 <Text style={item.orderId===this.state.selOrderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
    //                     {item.orderName}
    //                 </Text>
    //             </View>
    //         </TouchableOpacity>
    //     )
    // }

    renderSeriesItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.inventoryId) {
                    return;
                }
                this.setState({
                    selSeriesId:item.seriesId,
                    selSeriesName:item.seriesName
                })
            }}>
                <View key={item.seriesId} style={item.seriesId===this.state.selSeriesId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.seriesId===this.state.selSeriesId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.seriesName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 用料产品类型渲染
    renderMaterialTypeRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    selMaterialType:item.type,
                    selSeriesId:"",
                    selSeriesName:""
                }) 
                var currentTime = new Date();
                var qryEndTime = new Date(currentTime.getTime() + 8 * 3600000);
                var qryStartTime = new Date(qryEndTime.getTime() - 15 * 86400000);
                console.log("qryEndTime===",qryEndTime);
                console.log("qryStartTime===",qryStartTime);
                let loadTypeUrl= "/biz/order/seriesList";
                let loadRequest={
                    'currentPage':1,
                    'pageSize':1000,
                    "display":"Y",
                    "qryContent":"order",
                    "searchSeriesKeyWord":this.state.searchKeyWord,
                    "excludeOrderStateList":[
                        "A","K"
                    ],
                    "materialOut":"Y",
                    "startTime":item.type == 'Y'?qryStartTime:null,
                    "endTime":item.type == 'Y'?qryEndTime:null,
                    "productionLineId":item.type == 'Y' ? 52 :(item.type == 'N' ? 73 : null)
                };
                httpPost(loadTypeUrl, loadRequest, this.callBackLoadOrder);
            }}>
                <View key={item.typeId} style={item.type===this.state.selMaterialType ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.type===this.state.selMaterialType ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.typeName}
                    </Text>
                </View>
    
            </TouchableOpacity>
        )
    }

     // 库区
     renderLocationAreaRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.inventoryId) {
                    let toastOpts = getFailToastOpts('不能编辑');
                    WToast.show(toastOpts);
                    return;
                }
                this.setState({
                    selLocationAreaId:item.locationAreaId
                }) 
                let loadTypeUrl;
                let loadRequest;
                loadTypeUrl= "/biz/material/inventory/locationInventoryList";
                loadRequest={
                    "classifyId": this.state.classifyId,
                    "locationAreaId":item.locationAreaId
                };
                httpPost(loadTypeUrl, loadRequest, this.loadLocationInventoryListCallBack);
                // this.loadStorageLocationByAreaId(item.locationAreaId);
            }}>
                <View key={item.locationAreaId} style={[this.state.inventoryId ? CommonStyle.disableViewStyle : null, item.locationAreaId===this.state.selLocationAreaId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.locationAreaId===this.state.selLocationAreaId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationAreaName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 库位
    renderLocationRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.inventoryId) {
                    let toastOpts = getFailToastOpts('不能编辑');
                    WToast.show(toastOpts);
                    return;
                }
                this.setState({
                    selLocationId:item.locationId,
                    selLocationCurrentInventory:item.inventoryInWeight - item.inventoryOutWeight
                })
            }}>
                <View key={item.locationId} style={[this.state.inventoryId ? CommonStyle.disableViewStyle : null, item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle ]}>
                    <Text style={item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationName}[{double2StringFormat(item.inventoryInWeight - item.inventoryOutWeight)}吨]
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '出库'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                {/* <View style={[styles.innerViewStyle,{marginTop:0, index:1000}]}>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, fontWeight:'bold', marginRight:10}]}>
                         原料名称：{this.state.classifyName}
                     </Text>
                </View> */}
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabNameTextStyle}>原料类别</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    
                    <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.95, justifyContent:'flex-start'}]}>
                    {this.state.materialClassifyDataSource.map((item, key)=>{
                        return(
                            <TouchableOpacity onPress={()=>{
                                this.setState({
                                    parentClassifyId:item.classifyId,
                                    classifyId:null,
                                })
                                WToast.show({data:'点击了' + item.classifyName});
                                // 调接口查询原料名称
                                let loadTypeUrl;
                                let loadRequest;
                                loadTypeUrl= "/biz/material/classify/list";
                                loadRequest={
                                    'currentPage':1,
                                    'pageSize':100,
                                    "parentClassifyId": item.classifyId
                                };
                                httpPost(loadTypeUrl, loadRequest, (response)=>{
                                    if (response.code == 200 && response.data && response.data.dataList) {
                                        this.setState({
                                            materialNameDataSource:response.data.dataList,
                                        })
                                    }
                                });

                            }}>
                                <View key={item.classifyId} style={[item.classifyId===this.state.parentClassifyId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                                    <Text style={item.classifyId===this.state.parentClassifyId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                                        {item.classifyName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                            
                        )
                    })}
                    </View>

                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabNameTextStyle}>原料名称</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    
                    <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.95, justifyContent:'flex-start'}]}>
                        {!this.state.materialNameDataSource || this.state.materialNameDataSource.length < 1 ? 
                        <View style={{width:screenWidth}}><EmptyRowViewComponent/></View> 
                        : <Text></Text>} 
                    {this.state.materialNameDataSource.map((item, key)=>{
                        return(
                            <TouchableOpacity onPress={()=>{
                                this.setState({
                                    classifyId:item.classifyId,
                                    classifyName:item.classifyName,
                                })
                                WToast.show({data:'点击了' + item.classifyName});
                                 // 库位库存信息查询
                                 let loadTypeUrl;
                                 let loadRequest;
                                 loadTypeUrl= "/biz/material/inventory/locationInventoryList";
                                 loadRequest={
                                     "classifyId": item.classifyId,
                                     "locationAreaId":this.state.selLocationAreaId,
                                 };
                                 httpPost(loadTypeUrl, loadRequest, this.loadLocationInventoryListCallBack);
                            }}>
                                <View key={item.classifyId} style={[item.classifyId===this.state.classifyId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                                    <Text style={item.classifyId===this.state.classifyId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                                        {item.classifyName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                            
                        )
                    })}
                    </View>
                    {
                        (constants.loginUser.tenantId == 66)?
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={CommonStyle.rowLabTextStyle}>用料类型</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <View style={{flexWrap:'wrap', flexDirection:'row'}}>
                                {
                                    (this.state.materialTypeList && this.state.materialTypeList.length > 0) 
                                    ? 
                                    this.state.materialTypeList.map((item, index)=>{
                                        return this.renderMaterialTypeRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                        </View>
                        :
                        <View/>
                    }
                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabNameTextStyle}>需求产品</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <View style={[{flexWrap:'wrap'}, this.state.inventoryId? CommonStyle.disableViewStyle : null]}>
                        <TouchableOpacity onPress={()=>{
                            if(this.state.inventoryId) {
                                return;
                            }
                            this.setState({ 
                                modal:true,
                            })

                            if (!this.state.selSeriesId && this.state.seriesDataSource && this.state.seriesDataSource.length > 0) {
                                this.setState({
                                    selSeriesId:this.state.seriesDataSource[0].seriesId,
                                    selSeriesName:this.state.seriesDataSource[0].seriesName,
                                })
                            }
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}]}>
                                <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                                    选择需求产品
                                    {this.state.selSeriesId && this.state.selSeriesName ? ("：" + this.state.selSeriesName) : (this.state.selOrderName ? ("：" + this.state.selOrderName):null)}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput 
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({searchKeyWord:text})}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={()=>{
                                        this.loadOrder();
                                        }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{flexDirection:'row', flexWrap:'wrap', overflow:'scroll'}}>
                                    {
                                        (this.state.seriesDataSource && this.state.seriesDataSource.length > 0) 
                                        ? 
                                        this.state.seriesDataSource.map((item, index)=>{
                                            if (index < 1000) {
                                                return this.renderSeriesItem(item)
                                            }
                                        })
                                        : <EmptyRowViewComponent/> 
                                    }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                    <TouchableOpacity onPress={() => { 
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selSeriesId) {
                                            let toastOpts = getFailToastOpts("您还没有选择需求产品");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            
                        </View>
                        <View>

                        </View>
                    </Modal>
                    
                    {/* <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.seriesDataSource && this.state.seriesDataSource.length > 0) 
                            ? 
                            this.state.seriesDataSource.map((item, index)=>{
                                return this.renderOrderRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View> */}

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>库区</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationAreaDataSource && this.state.storageLocationAreaDataSource.length > 0) 
                            ? 
                            this.state.storageLocationAreaDataSource.map((item, index)=>{
                                return this.renderLocationAreaRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>库位</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationDataSource && this.state.storageLocationDataSource.length > 0) 
                            ? 
                            this.state.storageLocationDataSource.map((item, index)=>{
                                return this.renderLocationRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>重量(吨)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入重量'}
                            onChangeText={(text) => this.setState({weight:text})}
                        >
                            {this.state.weight}
                        </TextInput>
                    </View>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库时间</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openIoDate()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.ioDate ? "请选择出库时间" : this.state.ioDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    {
                        this.state.reviewerName?
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>审核人</Text>
                            </View>
                            <TextInput 
                                editable={false}
                                style={styles.inputRightText}
                                placeholder={'请输入审核人'}
                                onChangeText={(text) => this.setState({reviewerName:text})}
                            >
                                {this.state.reviewerName}
                            </TextInput>
                        </View>
                        :
                        <View/>
                    }
                    
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{flexDirection:'row',width:130,height:40,marginLeft:35,marginTop:15}]} >
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveMaterialInventoryIn.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row',width:130,height:40,marginRight:35,marginTop:15}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <BottomScrollSelect 
                        ref={'SelectIoDate'} 
                        callBackDateValue={this.callBackSelectIoDateValue.bind(this)}
                    />
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },

    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})