import React, {Component} from 'react';
import {
  Dimensions,
  Image,
  KeyboardAvoidingView,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import {uploadImageLibraryWithCrop} from '../../utils/UploadImageUtils';
import CourseMgrCoverCrop from './CourseMgrCoverCrop';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class CourseMgrAdd extends Component {
  constructor() {
    super();
    this.state = {
      operate: '',

      courseId: '',
      courseName: '',
      courseContent: '',
      courseSort: 1,
      orCourseSort: '',
      courseLevel: null,
      courseLevelName: '',
      courseLevelId: '',
      scene: '',
      coursePhoto: '',
      oldFile: '',
      courseDuration: null,
      courseTypeId: '',
      courseTypeName: '',
      courseLevelName: '',
      courseLevelId: '',
      taskNum: null,

      courseLevelDataSource: [],
      selCourseLevelCode: null,
      courseTypeIdDataSource: [],
      selectedCourseType: [],
      courseLevelIdDataSource: [],
      selectedCourseLevel: [],
      courseDataNum: null,

      moreModal: false,
      deleteModal: false,
      photo: null,
      srcPhotoData: null,
    };
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    this.loadCourseTypeList();
    this.loadCourseLevelList();

    let loadTypeUrl;
    let loadRequest;
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {dataItem, operate} = route.params;
      this.setState({...dataItem, orCourseSort: dataItem.courseSort});

      console.log(
        '=====scene=====',
        JSON.stringify(this.props.route.params, null, 6),
      );
      if (operate == '编辑') {
        console.log('========Edit==courseId:', dataItem.courseId);
        this.setState({
          operate: '编辑',
        });
        loadTypeUrl = '/biz/course/join/task/get';
        loadRequest = {courseId: dataItem.courseId};
        httpPost(loadTypeUrl, loadRequest, this.loadCourseDataCallBack);
      } else if (operate == '新增') {
        this.setState({
          operate: '新增',
        });
      } else if (operate == '详情') {
        this.setState({
          operate: '详情',
        });
        loadTypeUrl = '/biz/course/join/task/get';
        loadRequest = {courseId: dataItem.courseId};
        httpPost(loadTypeUrl, loadRequest, this.loadCourseDataCallBack);
      }
    }
    // 实习总数
    let loadCourseUrl = '/biz/course/join/task/list';
    let loadCourseRequest = {currentPage: 1, pageSize: 1000};
    httpPost(loadCourseUrl, loadCourseRequest, (response) => {
      if (
        response.code == 200 &&
        response.data.dataList &&
        response.data.dataList.length > 0
      ) {
        this.state.courseDataNum = response.data.dataList.length;
      } else {
        this.state.courseDataNum = 0;
      }
      console.log('实习总数===' + this.state.courseDataNum);
    });
  }
  loadCourseDataCallBack = (response) => {
    if (response.code == 200 && response.data) {
      this.setState({
        courseId: response.data.courseId,
        courseName: response.data.courseName,
        courseContent: response.data.courseContent,
        courseSort: response.data.courseSort,
        selectedCourseType: [response.data.courseTypeName],
        courseTypeId: response.data.courseTypeId,
        courseTypeName: response.data.courseTypeName,
        selectedCourseLevel: [response.data.courseLevelName],
        courseLevelId: response.data.courseLevelId,
        courseLevelName: response.data.courseLevelName,
        courseDuration: response.data.courseDuration,
        coursePhoto: response.data.coursePhoto,
        oldFile: response.data.coursePhoto,
        // selCourseLevelCode:response.data.courseLevel + "",
        planContent: response.data.planContent,
      });
    }
  };
  deleteCourse = (courseId) => {
    console.log('=======delete=courseId', courseId);
    // let url = "/biz/course/delete";
    let url = '/biz/course/join/task/delete';
    let requestParams = {courseId: courseId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      if (this.props.route.params.refresh) {
        this.props.route.params.refresh();
      }
      this.props.navigation.goBack();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };
  loadCourseTypeList = () => {
    let url = '/biz/course/type/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: 1000,
    };
    httpPost(url, loadRequest, this.loadCourseTypeListCallBack);
  };
  loadCourseTypeListCallBack = (response) => {
    if (response.code == 200 && response.data.dataList) {
      this.setState({
        courseTypeIdDataSource: response.data.dataList,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };
  loadCourseLevelList = () => {
    let url = '/biz/course/level/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: 1000,
    };
    httpPost(url, loadRequest, this.loadCourseLevelListCallBack);
  };
  loadCourseLevelListCallBack = (response) => {
    if (response.code == 200 && response.data.dataList) {
      this.setState({
        courseLevelIdDataSource: response.data.dataList,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('CourseList');
        }}>
        {this.state.operate == '详情' ? (
          <TouchableOpacity
            onPress={() => {
              this.setState({
                moreModal: true,
              });
            }}>
            <View
              style={[
                {
                  width: 35,
                  height: 35,
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <Image
                style={{width: 28, height: 28}}
                source={require('../../assets/icon/iconfont/more.png')}></Image>
            </View>
          </TouchableOpacity>
        ) : (
          <View></View>
        )}
      </TouchableOpacity>
    );
  }

  saveCourse = () => {
    console.log('=======saveCourse');
    let toastOpts;
    if (!this.state.courseSort || this.state.courseSort == 0) {
      toastOpts = getFailToastOpts('请输入实习编号');
      WToast.show(toastOpts);
      return;
    }

    // if (Math.floor(this.state.courseSort)!=this.state.courseSort) {
    // if (Number.isInteger(this.state.courseSort)) {
    // if (parseInt(this.state.courseSort) !== this.state.courseSort) {
    if (parseInt(this.state.courseSort) != this.state.courseSort) {
      toastOpts = getFailToastOpts('编号为正整数');
      WToast.show(toastOpts);
      return;
    }
    //编辑实习：1<=编号<=实习总数
    //新增实习：1<=编号<=实习总数+1
    if (this.state.courseId) {
      if (this.state.courseSort > this.state.courseDataNum) {
        toastOpts = getFailToastOpts(
          '当前编号最多支持' + this.state.courseDataNum,
        );
        WToast.show(toastOpts);
        return;
      }
    } else {
      if (this.state.courseSort > this.state.courseDataNum + 1) {
        toastOpts = getFailToastOpts(
          '当前编号最多支持' + (this.state.courseDataNum + 1),
        );
        WToast.show(toastOpts);
        return;
      }
    }
    if (!this.state.courseName) {
      toastOpts = getFailToastOpts('请输实习名称');
      WToast.show(toastOpts);
      return;
    }
    if (this.state.scene === 'D') {
      if (!this.state.courseTypeId) {
        toastOpts = getFailToastOpts('请选择实习类型');
        WToast.show(toastOpts);
        return;
      }
      if (!this.state.courseLevelId) {
        toastOpts = getFailToastOpts('请选择实习所属职级');
        WToast.show(toastOpts);
        return;
      }
      if (!this.state.courseDuration) {
        toastOpts = getFailToastOpts('请输入实习时长');
        if (parseInt(this.state.courseDuration) === 0) {
          toastOpts = getFailToastOpts('实习时长不能为0');
        }
        WToast.show(toastOpts);
        return;
      }
      if (parseInt(this.state.courseDuration) != this.state.courseDuration) {
        toastOpts = getFailToastOpts('实习时长为正整数');
        WToast.show(toastOpts);
        return;
      }
    }

    let url = '/biz/course/join/task/add';
    if (this.state.courseId) {
      console.log('=========Edit===courseId', this.state.courseId);
      url = '/biz/course/join/task/modify';
    }
    let requestParams = {
      courseId: this.state.courseId,
      courseName: this.state.courseName,
      courseContent: this.state.courseContent,
      courseSort: parseInt(this.state.courseSort),
      orCourseSort: this.state.orCourseSort,
      courseTypeId: this.state.courseTypeId,
      courseLevelId: this.state.courseLevelId,
      courseDuration: parseInt(this.state.courseDuration),
      coursePhoto: this.state.coursePhoto,
      // courseLevel: this.state.scene==="D"?this.state.selCourseLevelCode:null,
      courseLevel: 1,
      scene: this.state.scene,
    };
    httpPost(url, requestParams, this.saveCourseCallBack);
  };

  // 保存回调函数
  saveCourseCallBack = (response) => {
    let toastOpts;
    switch (response.code) {
      case 200:
        if (this.props.route.params.refresh) {
          this.props.route.params.refresh();
        }
        toastOpts = getSuccessToastOpts('保存完成');
        WToast.show(toastOpts);
        this.props.navigation.goBack();
        break;
      default:
        toastOpts = getFailToastOpts(response.message);
        WToast.show({data: response.message});
    }
  };

  // 渲染底部积分类别选择器
  openCourseTypeSelect() {
    if (
      !this.state.courseTypeIdDataSource ||
      this.state.courseTypeIdDataSource.length < 1
    ) {
      WToast.show({data: '请先添加实习类型'});
      return;
    }
    this.refs.SelectCourseTypeId.showCourseTypeId(
      this.state.selectedCourseType,
      this.state.courseTypeIdDataSource,
    );
  }
  callBackCourseTypeIdValue(value) {
    console.log('==========实习类型选择的结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectedCourseType: value,
    });
    var courseTypeName = value.toString();
    let loadUrl = '/biz/course/type/getCourseTypeByName';
    let loadRequest = {
      courseTypeName: courseTypeName,
    };
    httpPost(loadUrl, loadRequest, this.callBackLoadCourseTypeData);
  }
  callBackLoadCourseTypeData = (response) => {
    if (response.code == 200 && response.data) {
      this.setState({
        courseTypeName: response.data.courseTypeName,
        courseTypeId: response.data.courseTypeId,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 回调函数
  callBackFunction = (scene) => {
    loadTypeUrl = '/biz/course/join/task/get';
    loadRequest = {courseId: this.state.courseId};
    httpPost(loadTypeUrl, loadRequest, this.loadCourseDataCallBack);
  };

  photoCallBack = (res) => {
    console.log('========imageUploadResponse', res);
    if (res.code === 200) {
      WToast.show({data: '成功上传'});
      let {cropFile} = res.data;
      this.setState({
        coursePhoto: cropFile,
        oldFile: cropFile,
      });
    } else {
      WToast.show({data: res.message});
    }
  };

  // 渲染底部 所属职级选择器
  openCourseLevelSelect() {
    if (
      !this.state.courseLevelIdDataSource ||
      this.state.courseLevelIdDataSource.length < 1
    ) {
      WToast.show({data: '请先添加实习类型'});
      return;
    }
    this.refs.SelectCourseLevelId.showCourseLevelId(
      this.state.selectedCourseLevel,
      this.state.courseLevelIdDataSource,
    );
  }
  callBackCourseLevelIdValue(value) {
    console.log('==========所属职级选择的结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectedCourseLevel: value,
    });
    var courseLevelName = value.toString();
    let loadUrl = '/biz/course/level/getCourseLevelByName';
    let loadRequest = {
      courseLevelName: courseLevelName,
    };
    httpPost(loadUrl, loadRequest, this.callBackLoadCourseLevelData);
  }
  callBackLoadCourseLevelData = (response) => {
    if (response.code == 200 && response.data) {
      this.setState({
        courseLevelName: response.data.courseLevelName,
        courseLevelId: response.data.courseLevelId,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };
  renderTitleItem = () => {
    return (
      <Text style={styles.headCenterTitleText}>
        {this.state.operate == '详情'
          ? '实习详情'
          : this.state.operate == '编辑'
          ? '编辑实习'
          : '新增实习'}
      </Text>
    );
  };
  render() {
    return (
      <KeyboardAvoidingView
        style={[CommonStyle.formContentViewStyle]}
        behavior="padding">
        <CommonHeadScreen
          titleItem={() => this.renderTitleItem()}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />

        {this.state.operate == '详情' ? (
          <ScrollView style={CommonStyle.contentViewStyle}>
            <View style={[styles.inputRowStyle]}>
              <View style={styles.leftLabView}>
                <Text style={styles.leftLabRedTextStyle}>*</Text>
                <Text style={styles.leftLabNameTextStyle}>编号（整数）</Text>
              </View>
              <View
                style={[
                  CommonStyle.inputTextStyleTextStyle,
                  {borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                ]}>
                <Text
                  style={{
                    color: '#A0A0A0',
                    fontSize: 15,
                    width: screenWidth / 2,
                  }}>
                  {this.state.courseSort}
                </Text>
              </View>
            </View>
            <View style={styles.inputLineViewStyle} />

            <View style={[styles.inputRowStyle]}>
              <View style={styles.leftLabView}>
                <Text style={styles.leftLabRedTextStyle}>*</Text>
                <Text style={styles.leftLabNameTextStyle}>实习名称</Text>
              </View>
              <View
                style={[
                  CommonStyle.inputTextStyleTextStyle,
                  {borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                ]}>
                <Text
                  style={{
                    color: '#A0A0A0',
                    fontSize: 15,
                    width: screenWidth / 2,
                  }}
                  numberOfLines={2}
                  ellipsizeMode="tail">
                  {this.state.courseName}
                </Text>
              </View>
            </View>
            <View style={styles.inputLineViewStyle} />
            {this.state.scene == 'D' ? (
              <View>
                <View style={styles.inputRowStyle}>
                  <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>实习类型</Text>
                  </View>
                  <View
                    style={[
                      CommonStyle.inputTextStyleTextStyle,
                      {borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                    ]}>
                    <Text
                      style={{
                        color: '#A0A0A0',
                        fontSize: 15,
                        width: screenWidth / 2,
                      }}>
                      {!this.state.courseTypeName
                        ? '请选择实习类型'
                        : this.state.courseTypeName}
                    </Text>
                  </View>
                </View>
                <View style={styles.inputLineViewStyle} />

                <View style={styles.inputRowStyle}>
                  <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>所属职级</Text>
                  </View>
                  <View
                    style={[
                      CommonStyle.inputTextStyleTextStyle,
                      {borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                    ]}>
                    <Text
                      style={{
                        color: '#A0A0A0',
                        fontSize: 15,
                        width: screenWidth / 2,
                      }}>
                      {!this.state.courseLevelName
                        ? '请选择所属职级'
                        : this.state.courseLevelName}
                    </Text>
                  </View>
                </View>
                <View style={styles.inputLineViewStyle} />

                <View style={[styles.inputRowStyle]}>
                  <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>时长（天）</Text>
                  </View>
                  <View
                    style={[
                      CommonStyle.inputTextStyleTextStyle,
                      {borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                    ]}>
                    <Text
                      style={{
                        color: '#A0A0A0',
                        fontSize: 15,
                        width: screenWidth / 2,
                      }}>
                      {this.state.courseDuration}
                    </Text>
                  </View>
                </View>
                <View style={styles.inputLineViewStyle} />
              </View>
            ) : (
              <View />
            )}
            <View
              style={[
                styles.inputRowStyle,
                {height: 86, alignItems: 'center'},
              ]}>
              <View style={[styles.leftLabView, {height: 86}]}>
                <Text style={styles.leftLabNameTextStyle}>封面</Text>
              </View>
              {this.state.coursePhoto ? (
                <Image
                  source={{
                    uri: constants.image_addr + '/' + this.state.coursePhoto,
                  }}
                  style={{height: 66, width: 66 * 1.57}}
                />
              ) : (
                <Image
                  style={{height: 66, width: 66}}
                  source={require('../../assets/icon/addCover.png')}></Image>
              )}
            </View>
            <View style={styles.inputLineViewStyle} />

            <View style={[styles.inputRowStyle]}>
              <View style={styles.leftLabView}>
                <Text style={styles.leftLabNameTextStyle}>实习内容</Text>
              </View>
            </View>
            <View style={[styles.inputRowStyle, {height: 136}]}>
              <Text
                style={[
                  CommonStyle.inputRowText,
                  {height: 136, borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                ]}>
                {this.state.courseContent}
              </Text>
            </View>
            <View style={styles.inputLineViewStyle} />

            <View
              style={[
                CommonStyle.btnRowStyle,
                {
                  width: screenWidth,
                  marginLeft: 0,
                  backgroundColor: 'rgba(255, 255, 255, 1)',
                  height: 66,
                },
              ]}>
              <TouchableOpacity
                onPress={() => {
                  this.props.navigation.goBack();
                }}>
                <View
                  style={[
                    CommonStyle.btnRowLeftCancelBtnView,
                    {marginLeft: 20, marginTop: 8, width: screenWidth - 40},
                  ]}>
                  <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                </View>
              </TouchableOpacity>
            </View>
          </ScrollView>
        ) : (
          <ScrollView style={CommonStyle.contentViewStyle}>
            <View style={[styles.inputRowStyle]}>
              <View style={styles.leftLabView}>
                <Text style={styles.leftLabRedTextStyle}>*</Text>
                <Text style={styles.leftLabNameTextStyle}>编号（整数）</Text>
              </View>
              <TextInput
                keyboardType="numeric"
                style={[
                  CommonStyle.inputTextStyleTextStyle,
                  {borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                ]}
                placeholder={'请输入实习编号'}
                onChangeText={(text) => this.setState({courseSort: text})}>
                {this.state.courseSort}
              </TextInput>
            </View>
            <View style={styles.inputLineViewStyle} />

            <View style={[styles.inputRowStyle, {height: 60}]}>
              <View style={[styles.leftLabView, {height: 60}]}>
                <Text style={styles.leftLabRedTextStyle}>*</Text>
                <Text style={styles.leftLabNameTextStyle}>实习名称</Text>
              </View>
              <TextInput
                style={[
                  CommonStyle.inputTextStyleTextStyleNoWidth,
                  {
                    borderWidth: 0,
                    color: '#A0A0A0',
                    fontSize: 15,
                    width: screenWidth / 2,
                    height: 60,
                  },
                ]}
                placeholder={'请输入实习名称'}
                multiline={true}
                onChangeText={(text) => this.setState({courseName: text})}>
                {this.state.courseName}
              </TextInput>
            </View>
            <View style={styles.inputLineViewStyle} />
            {this.state.scene == 'D' ? (
              <View>
                <View style={styles.inputRowStyle}>
                  <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>实习类型</Text>
                  </View>
                  <TouchableOpacity onPress={() => this.openCourseTypeSelect()}>
                    <View
                      style={[
                        CommonStyle.inputTextStyleTextStyle,
                        {borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                      ]}>
                      <Text style={{color: '#A0A0A0', fontSize: 15}}>
                        {!this.state.courseTypeName
                          ? '请选择实习类型'
                          : this.state.courseTypeName}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View style={styles.inputLineViewStyle} />

                <View style={styles.inputRowStyle}>
                  <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>所属职级</Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => this.openCourseLevelSelect()}>
                    <View
                      style={[
                        CommonStyle.inputTextStyleTextStyle,
                        {borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                      ]}>
                      <Text style={{color: '#A0A0A0', fontSize: 15}}>
                        {!this.state.courseLevelName
                          ? '请选择所属职级'
                          : this.state.courseLevelName}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View style={styles.inputLineViewStyle} />

                <View style={[styles.inputRowStyle]}>
                  <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>时长（天）</Text>
                  </View>
                  <TextInput
                    keyboardType="numeric"
                    style={[
                      CommonStyle.inputTextStyleTextStyle,
                      {borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                    ]}
                    placeholder={'请输入时长（整数）'}
                    onChangeText={(text) =>
                      this.setState({courseDuration: text})
                    }>
                    {this.state.courseDuration}
                  </TextInput>
                </View>
                <View style={styles.inputLineViewStyle} />
              </View>
            ) : (
              <View />
            )}
            <View
              style={[
                styles.inputRowStyle,
                {height: 86, alignItems: 'center'},
              ]}>
              <View style={[styles.leftLabView, {height: 86}]}>
                <Text style={styles.leftLabNameTextStyle}>封面</Text>
              </View>
              <TouchableOpacity
                onPress={() => {
                  this.state.cropModal = true;
                  uploadImageLibraryWithCrop('course_photo', (res) => {
                    console.log(
                      'uploadImageLibraryWithCrop',
                      JSON.stringify(res, null, 6),
                    );
                    if (res && res.assets) {
                      this.setState({
                        srcPhotoData: res,
                      });
                      res = {...res, oldFile: this.state.oldFile};

                      this.refs.courseMgrCoverCrop.openCropModal(res);
                    }
                  });
                }}>
                {this.state.coursePhoto ? (
                  <View style={{}}>
                    <Image
                      source={{
                        uri:
                          constants.image_addr + '/' + this.state.coursePhoto,
                      }}
                      style={{height: 66, width: 66 * 1.57}}
                    />
                  </View>
                ) : (
                  <Image
                    style={{height: 66, width: 66}}
                    source={require('../../assets/icon/addCover.png')}></Image>
                )}
              </TouchableOpacity>
              {this.state.coursePhoto ? (
                <TouchableOpacity
                  style={{
                    position: 'relative',
                    left: -10,
                    top: -30,
                    zIndex: 1000,
                  }}
                  onPress={() => {
                    console.log('========deletePhoto');
                    this.setState({
                      coursePhoto: null,
                    });
                  }}>
                  <Image
                    style={{width: 22, height: 22}}
                    source={require('../../assets/icon/iconfont/deleteRed.png')}></Image>
                </TouchableOpacity>
              ) : null}
            </View>
            <View style={styles.inputLineViewStyle} />

            <View style={[styles.inputRowStyle]}>
              <View style={styles.leftLabView}>
                <Text style={styles.leftLabNameTextStyle}>实习内容</Text>
              </View>
            </View>
            <View style={[styles.inputRowStyle, {height: 136}]}>
              <TextInput
                multiline={true}
                textAlignVertical="top"
                style={[
                  CommonStyle.inputRowText,
                  {height: 136, borderWidth: 0, color: '#A0A0A0', fontSize: 15},
                ]}
                placeholder={'请输入实习内容'}
                onChangeText={(text) => this.setState({courseContent: text})}>
                {this.state.courseContent}
              </TextInput>
            </View>
            <View style={styles.inputLineViewStyle} />
            <View
              style={[
                CommonStyle.btnRowStyle,
                {
                  width: screenWidth,
                  marginLeft: 0,
                  backgroundColor: 'rgba(255, 255, 255, 1)',
                  height: 66,
                },
              ]}>
              <TouchableOpacity
                onPress={() => {
                  this.props.navigation.goBack();
                }}>
                <View
                  style={[
                    CommonStyle.btnRowLeftCancelBtnView,
                    {
                      marginLeft: 20,
                      marginTop: 8,
                      width: (screenWidth - 56) / 2,
                    },
                  ]}>
                  <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity onPress={this.saveCourse.bind(this)}>
                <View
                  style={[
                    CommonStyle.btnRowRightSaveBtnView,
                    {
                      marginRight: 20,
                      marginTop: 8,
                      width: (screenWidth - 56) / 2,
                    },
                  ]}>
                  <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                </View>
              </TouchableOpacity>
            </View>
            <BottomScrollSelect
              ref={'SelectCourseTypeId'}
              callBackCourseTypeIdValue={this.callBackCourseTypeIdValue.bind(
                this,
              )}
            />
            <BottomScrollSelect
              ref={'SelectCourseLevelId'}
              callBackCourseLevelIdValue={this.callBackCourseLevelIdValue.bind(
                this,
              )}
            />
          </ScrollView>
        )}

        {/* 更多操作弹窗Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.moreModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    console.log('============');
                    this.setState({
                      moreModal: false,
                      operate: '编辑',
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                    ]}>
                    <Text
                      style={{
                        color: 'rgba(0, 10, 32, 0.85)',
                        fontSize: 18,
                        lineHeight: 52,
                      }}>
                      编辑
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              <View>
                <TouchableOpacity
                  onPress={() => {
                    if (this.state.taskNum) {
                      WToast.show({data: '实习任务已开始，无法删除'});
                      return;
                    }
                    this.setState({
                      moreModal: false,
                      deleteModal: true,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                      this.state.taskNum ? CommonStyle.disableViewStyle : '',
                    ]}>
                    <Text
                      style={[
                        {
                          color: 'rgba(0, 10, 32, 0.85)',
                          fontSize: 18,
                          lineHeight: 52,
                        },
                      ]}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              <View
                style={{
                  width: 291,
                  height: 50,
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      moreModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 105,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 删除弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.deleteModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.08)'},
            ]}>
            <View
              style={{
                width: 292,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认删除该实习?</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  删除后数据不可恢复，请谨慎操作
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 292,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了确定'});
                    this.deleteCourse(this.state.courseId);
                  }}>
                  <View
                    style={[
                      {
                        width: 146,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        <CourseMgrCoverCrop
          ref={'courseMgrCoverCrop'}
          callBack={this.photoCallBack.bind(this)}
        />
      </KeyboardAvoidingView>
    );
  }
}

let styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 140,
  //     backgroundColor:'#FFFFFF'
  // },
  itemViewStyle: {
    margin: 10,
    padding: 15,
    borderRadius: 2,
    backgroundColor: '#FFFFFF',
  },
  selectedItemViewStyle: {
    margin: 10,
    padding: 15,
    borderRadius: 2,
    backgroundColor: '#CB4139',
  },
  itemTextStyle: {
    color: '#000000',
  },
  selectedItemTextStyle: {
    color: '#FFFFFF',
  },
  inputRowStyle: {
    height: 45,
    flexDirection: 'row',
    marginTop: 10,
  },

  rowLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabView: {
    width: leftLabWidth,
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
  },
  leftLabRedTextStyle: {
    color: 'red',
    marginLeft: 5,
    marginRight: 5,
  },
  inputRightText: {
    width: screenWidth - (leftLabWidth + 5),
    borderRadius: 5,
    borderColor: '#F1F1F1',
    borderWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 15,
    paddingLeft: 10,
    paddingRight: 10,
  },
  inputLineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    borderBottomWidth: 0.5,
    borderColor: '#E8E9EC',
  },
  headCenterTitleText: {
    fontSize: 20,
    color: '#000000',
    fontWeight: '600',
  },
});
