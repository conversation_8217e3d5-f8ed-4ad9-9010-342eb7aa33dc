import React, {Component} from 'react';
import {
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import {
  ifIphoneXBodyViewHeight,
  ifIphoneXContentViewHeight,
} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class DepartmentAdd extends Component {
  constructor() {
    super();
    this.state = {
      departmentId: '',
      departmentName: '',
      departmentSort: 0,
      operate: '',
      parentDepartmentId: null,
    };
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    let loadTypeUrl;
    let loadRequest;
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {departmentId, parentDepartmentId} = route.params;
      if (parentDepartmentId) {
        this.setState({
          parentDepartmentId: parentDepartmentId,
        });
      }
      if (departmentId) {
        console.log('========Edit==departmentId:', departmentId);
        this.setState({
          departmentId: departmentId,
          operate: '编辑',
        });
        loadTypeUrl = '/biz/department/get';
        loadRequest = {departmentId: departmentId};
        httpPost(loadTypeUrl, loadRequest, this.loadEditMachineDataCallBack);
      } else {
        this.setState({
          operate: '新增',
        });
      }
    }
  }
  loadEditMachineDataCallBack = (response) => {
    if (response.code == 200 && response.data) {
      this.setState({
        departmentId: response.data.departmentId,
        departmentName: response.data.departmentName,
        departmentSort: response.data.departmentSort,
      });
    }
  };

  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      //     <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      // </TouchableOpacity>
      <View style={CommonStyle.viewAddLeftViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[CommonStyle.btnAddLeftBtn]}>
          <Image
            style={CommonStyle.btnAddLeftBtnView}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={CommonStyle.btnAddLeftBtnText}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      //     <TouchableOpacity onPress={() => {
      //         this.props.navigation.navigate("DepartmentList")
      //     }}>
      //         <Text style={CommonStyle.headRightText}>部门管理</Text>
      //     </TouchableOpacity>
      <View style={CommonStyle.viewAddRightViewStyle}>
        <TouchableOpacity onPress={() => {}}>
          {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
          <Text style={CommonStyle.btnAddRightBtnText}>部门管理</Text>
        </TouchableOpacity>
      </View>
    );
  }

  emptyComponent() {
    return <EmptyRowViewComponent />;
  }

  saveDepartment = () => {
    console.log('=======saveBrickClassify');
    let toastOpts;
    if (!this.state.departmentName) {
      toastOpts = getFailToastOpts('请输入部门名称');
      WToast.show(toastOpts);
      return;
    }
    let url = '/biz/department/add';
    if (this.state.departmentId) {
      console.log('=========Edit===departmentId', this.state.departmentId);
      url = '/biz/department/modify';
    }
    let requestParams = {
      departmentId: this.state.departmentId,
      departmentName: this.state.departmentName,
      departmentSort: this.state.departmentSort,
      parentDepartmentId: this.state.parentDepartmentId,
    };
    httpPost(url, requestParams, this.saveDepartmentCallBack);
  };

  // 保存回调函数
  saveDepartmentCallBack = (response) => {
    let toastOpts;
    switch (response.code) {
      case 200:
        if (this.props.route.params.refresh) {
          this.props.route.params.refresh();
        }
        toastOpts = getSuccessToastOpts('保存完成');
        WToast.show(toastOpts);
        this.props.navigation.goBack();
        break;
      default:
        toastOpts = getFailToastOpts(response.message);
        WToast.show({data: response.message});
    }
  };
  render() {
    return (
      <View style={{backgroundColor: 'rgba(242, 245, 252, 1)'}}>
        <CommonHeadScreen
          title={this.state.operate + '部门'}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <ScrollView
          style={{
            backgroundColor: '#FFFFFF',
            borderBottomWidth: 1,
            borderBottomColor: '#E8E9EC',
            height: ifIphoneXBodyViewHeight(),
          }}>
          <View
            style={{
              borderBottomWidth: 1,
              borderBottomColor: '#F1F1F1',
              width: '100%',
              marginTop: 0,
              marginLeft: 15,
            }}
          />
          <View style={styles.inputRowStyle}>
            <View style={styles.leftLabView}>
              <Text style={styles.leftLabRedTextStyle}>*</Text>
              <Text style={styles.leftLabNameTextStyle}>部门名称</Text>
            </View>
            <TextInput
              style={styles.inputRightText}
              placeholder={'请输入'}
              onChangeText={(text) => this.setState({departmentName: text})}>
              {this.state.departmentName}
            </TextInput>
          </View>
          <View
            style={{
              borderBottomWidth: 1,
              borderBottomColor: '#F1F1F1',
              width: '100%',
              marginTop: 0,
              marginLeft: 15,
            }}
          />
          <View style={styles.inputRowStyle}>
            <View style={styles.leftLabView}>
              <Text style={[styles.leftLabRedTextStyle, {color: 'white'}]}>
                *
              </Text>
              <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
            </View>
            <TextInput
              keyboardType="numeric"
              style={styles.inputRightText}
              placeholder={'请输入'}
              onChangeText={(text) => this.setState({departmentSort: text})}>
              {this.state.departmentSort}
            </TextInput>
          </View>
          <View
            style={{
              borderBottomWidth: 1,
              borderBottomColor: '#F1F1F1',
              width: '100%',
              marginTop: 0,
              marginLeft: 15,
            }}
          />
          <View
            style={{
              height: ifIphoneXContentViewHeight() - 108 - 87,
              backgroundColor: '#F2F5FC',
            }}>
            {/* <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                        >
                        </TextInput> */}
          </View>
          <View style={[CommonStyle.blockAddCancelSaveStyle, {marginTop: 0}]}>
            <TouchableOpacity
              onPress={() => {
                this.props.navigation.goBack();
              }}>
              <View style={CommonStyle.btnAddCancelBtnView}>
                {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity onPress={this.saveDepartment.bind(this)}>
              <View style={CommonStyle.btnAddSaveBtnView}>
                {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
              </View>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    );
  }
}

let styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 140,
  //     backgroundColor:'#FFFFFF'
  // },
  itemViewStyle: {
    margin: 10,
    padding: 15,
    borderRadius: 2,
    backgroundColor: '#FFFFFF',
  },
  selectedItemViewStyle: {
    margin: 10,
    padding: 15,
    borderRadius: 2,
    backgroundColor: '#CB4139',
  },
  itemTextStyle: {
    color: '#000000',
  },
  selectedItemTextStyle: {
    color: '#FFFFFF',
  },
  inputRowStyle: {
    height: 45,
    flexDirection: 'row',
    marginTop: 4,
    marginBottom: 4,
    // flex: 1,
    // justifyContent: 'space-between',
    // alignContent:'center'
    // backgroundColor:'#000FFF',
    // width:screenWidth,
    // alignContent:'space-between',
    // justifyContent:'center'
  },

  rowLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
    // alignContent:'flex-start',
    // justifyContent:'center',
    // backgroundColor:'yellow',
  },
  leftLabView: {
    width: leftLabWidth,
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
    // alignContent:'flex-start',
    // justifyContent:'center',
    // backgroundColor:'yellow',
  },
  leftLabNameTextStyle: {
    fontSize: 18,
    // color:'red',
    // borderColor:'#000',
    // borderWidth:1,
    // justifyContent:'center',
    // alignContent:'center',
    // backgroundColor:'yellow',
  },
  leftLabRedTextStyle: {
    color: 'red',
    marginLeft: 5,
    marginRight: 5,
  },
  inputRightText: {
    width: screenWidth - (leftLabWidth + 5),
    // borderRadius: 5,
    borderColor: '#F1F1F1',
    // borderWidth: 1,
    marginRight: 5,
    // color: '#A0A0A0',
    color: 'rgba(43, 51, 63, 1)',
    fontSize: 15,
    paddingLeft: 10,
    paddingRight: 10,
  },
  lineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    marginBottom: 6,
    borderBottomWidth: 0.5,
    borderColor: '#E8E9EC',
  },
  textCertain: {
    // width: 34,
    // height: 24,
    // fontFamily: 'PingFangSC',
    // fontWeight: '400',
    fontSize: 18,
    color: '#FFFFFF',
    lineHeight: 24,
    marginTop: 10,
    textAlign: 'center',
    // fontStyle: 'normal',
  },
  textCancel: {
    // width: 34,
    // height: 24,
    // fontFamily: 'PingFangSC',
    // fontWeight: '400',
    fontSize: 18,
    color: '#404956',
    lineHeight: 24,
    marginTop: 10,
    textAlign: 'center',
    // fontStyle: 'normal',
  },
  textContainerCertain: {
    width: 180,
    height: 48,
    marginRight: 8,
    backgroundColor: '#255BDA',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#DFE3E8',
  },
  textContainerCancel: {
    width: 180,
    height: 48,
    marginLeft: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#DFE3E8',
  },
});
