import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Clipboard,Linking,Image,Modal,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class InventoryDetailList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            initGmtCreated:null,
            gmtCreated:null,
            selectGmtCreated:null,
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:6,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            brickTypeDataSource:[],
            seriesName:"",
            brickTypeName:"",
            brickTypeId:"",
            brickTypeNameLayoutHeight:0,
            isOutsourcingInventoryDetail:"N",

            outsourcingTenantId: null,
            outsourceingTenantName: "",
            outsourceingTenantModal:false,
            outsourceingTenantSearchKeyWord:"",
            outsourceingTenantDataSource:[],

            selOutsourcingTenantId:null,
            selOutsourcingTenantName:"",
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    initGmtCreated=()=>{
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth()-3);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectGmtCreated:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            gmtCreated:_gmtCreated,
            initGmtCreated:_gmtCreated,
        })
        return _gmtCreated;
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        const { route, navigation } = this.props;
        if (route && route.params) {
            const {seriesName, brickTypeId, brickTypeName, standardSize, standardWeight, isOutsourcingInventoryDetail } = route.params;
            if (seriesName) {
                this.setState({
                    seriesName:seriesName
                })
            }
            if (brickTypeId) {
                this.setState({
                    brickTypeId:brickTypeId
                })
            }
            if (brickTypeName) {
                this.setState({
                    brickTypeName:brickTypeName
                })
            }
            if (standardSize) {
                this.setState({
                    standardSize:standardSize
                })
            }
            if (standardWeight) {
                this.setState({
                    standardWeight:standardWeight
                })
            }
            if (isOutsourcingInventoryDetail) {
                this.setState({
                    isOutsourcingInventoryDetail:isOutsourcingInventoryDetail
                })

                this.loadInventoryDetailList(brickTypeId, isOutsourcingInventoryDetail, null);
            }
            else {
                this.loadInventoryDetailList(brickTypeId, null, null);
            }

            if ("Y" === isOutsourcingInventoryDetail) {
                // 加载外协生产单位
                this.loadOutsourceingTenantList();
            }
        }
        else {
            this.loadInventoryDetailList(brickTypeId, null, null);
        }

        // 加载砖型
        var loadTypeUrl= "/biz/brick/series/type/effBrickTreeCatalog";
        var loadRequest={'currentPage':1,'pageSize':10000};
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data) {
                this.setState({
                    brickTypeDataSource:response.data
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });

    }


    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.brickTypeId == null && this.state.gmtCreated === this.state.initGmtCreated) {
            console.log("==========不刷新=====");
            return;
        }
        var _gmtCreated = this.initGmtCreated();
        this.setState({
            gmtCreated:_gmtCreated,
            brickTypeName:"",
            brickTypeId:"",
        })
        this.setState({
            currentPage:1
        })
        let url= "/biz/inventory/detail";
        let loadRequest={
            "gmtCreated":_gmtCreated,
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "brickTypeId":this.state.brickTypeId ? this.state.brickTypeId : null,
            "operateTypeListString":"A,B,C"
        };
        if ("Y" === this.state.isOutsourcingInventoryDetail) {
            if (outsourcingTenantId) {
                loadRequest={
                    "gmtCreated":_gmtCreated,
                    "currentPage": 1,
                    "pageSize": this.state.pageSize,
                    "brickTypeId":this.state.brickTypeId ? this.state.brickTypeId : null,
                    // 是否为外协管理里的查询库存明细
                    "isOutsourcingInventoryDetail":this.state.isOutsourcingInventoryDetail,
                    "outsourcingTenantId":outsourcingTenantId,
                };
                httpPost(url, loadRequest, this._loadFreshDataCallBack)
            }
        }
        else {
            httpPost(url, loadRequest, this._loadFreshDataCallBack)
        }
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 加载外协生产单位
    loadOutsourceingTenantList = () => {
        loadTypeUrl = "/biz/tenant/outsourcing/list";
        loadRequest = { 'currentPage': 1, 'pageSize': 1000 };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    outsourceingTenantDataSource: response.data.dataList,
                })
            }
        });
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========第一页即是最后一页，不加载=====");
            return;
        }
        // this.loadInventoryDetailList();
    }

    loadInventoryDetailList=(brickTypeId, isOutsourcingInventoryDetail, outsourcingTenantId)=>{
        this.setState({
            dataSource:[]
        })
        
        let url= "/biz/inventory/detail";
        let loadRequest={
            "brickTypeId":brickTypeId ? brickTypeId : this.state.brickTypeId,
            "operateTypeListString":"A,B,C"
        };

        if ("Y" === isOutsourcingInventoryDetail) {
            if (outsourcingTenantId) {
                loadRequest={
                    "brickTypeId":brickTypeId ? brickTypeId : this.state.brickTypeId,
                    // 是否为外协管理里的查询库存明细
                    "isOutsourcingInventoryDetail":this.state.isOutsourcingInventoryDetail,
                    "outsourcingTenantId":outsourcingTenantId,
                };
                httpPost(url, loadRequest, this.loadDataListCallBack);
            }
        }
        else{
            httpPost(url, loadRequest, this.loadDataListCallBack)
        } 
    }

    loadDataListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }



    renderRow=(item, index)=>{
        return (
            <View key={item.storageInId} style={[styles.innerViewStyle,item.storageInAmount ? {backgroundColor:'rgba(255,0,0,0.2)'} : {backgroundColor:'rgba(0,255,0,0.2)'}]}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>库存数量：{item.beforeInventoryAmount}</Text>
                    {
                        (null != item.tenantName && item.tenantName != constants.loginUser.tenantName) ? 
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                            {item.tenantAbbreviation ? item.tenantAbbreviation : item.tenantName}
                        </Text>
                        :
                        null
                    }
                </View>
                
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.storageInAmount ? "入库数量" : "出库数量"}
                    ：
                    {item.storageInAmount ? (item.storageInAmount) : item.storageOutAmount}</Text>
                    <Text style={{color:'#F0F0F0'}}>
                        {
                            (item.operateType === "B" & item.storageInAmount < 0) ? "库位调拨（出）":null
                        }
                        {
                            (item.operateType === "B" & item.storageInAmount > 0) ? "库位调拨（入）":null
                        }
                        {
                            (item.operateType === "C" & item.storageInAmount < 0) ? "库存调整（减）":null
                        }
                        {
                            (item.operateType === "C" & item.storageInAmount > 0) ? "库存调整（加）":null
                        }
                    </Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>结余库存：{item.afterInventoryAmount}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>库区：{item.locationAreaName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>库位：{item.locationName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>库存总重(吨)：{this.state.standardWeight ? (item.afterInventoryAmount * this.state.standardWeight / 1000).toFixed(2) : "-"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>操作人：{item.operatorName}</Text>
                </View>
                {
                    item.customerName ? 
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>客户名称：{item.customerName}</Text>
                    </View> 
                    : 
                    <View/>
                }
                
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>更新时间：{item.gmtCreated}</Text>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0',marginleft:15}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} >
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70 }}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={{ flexDirection: 'row-reverse', alignItems: 'center', width:70 }}>
                <TouchableOpacity onPress={() => { 
                    // this.props.navigation.navigate("CustomerAdd", 
                    // {
                    //     // 传递回调函数
                    //     refresh: this.callBackFunction 
                    // });
                    }}>
                    {/* <Image style={{ width:22, height:22, marginVertical: 2}} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
                </TouchableOpacity>
            </View>
        )
    }


    brickTypeNameLayout=(event)=> {
        this.setState({
            brickTypeNameLayoutHeight: event.nativeEvent.layout.height
        })

    }

    renderOutsourceingTenantItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    selOutsourcingTenantId:item.outsourcingTenantId,
                    selOutsourcingTenantName:(item.outsourcingAbbreviation ? item.outsourcingAbbreviation : item.outsourcingName)
                })
            }}>
                <View key={item.outsourcingTenantId} style={item.outsourcingTenantId===this.state.selOutsourcingTenantId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.outsourcingTenantId===this.state.selOutsourcingTenantId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {(item.outsourcingAbbreviation ? item.outsourcingAbbreviation : item.outsourcingName)}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={"Y" == this.state.isOutsourcingInventoryDetail ? "外协库存明细" : "库存明细"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                 <View style={[styles.innerViewStyle,{marginTop:0, index:1000}]} onLayout={this.brickTypeNameLayout.bind(this)}>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, marginRight:100}]}>产品全称：{'[' + this.state.seriesName + ']-' +  this.state.brickTypeName}</Text>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, marginRight:100}]}>尺寸：{this.state.standardSize ? this.state.standardSize : "-"}      单重(Kg)：{this.state.standardWeight ? this.state.standardWeight : "-"}</Text>
                     {
                        ("Y" === this.state.isOutsourcingInventoryDetail) ?
                        <TouchableOpacity onPress={()=>{
                            this.setState({ 
                                outsourceingTenantModal:true,
                            })
                        }}>
                            <Text style={[CommonStyle.blockItemTextStyle16, {fontWeight:'bold',marginLeft:10, marginRight:100}]}>外协生产单位：{this.state.outsourceingTenantName ? this.state.outsourceingTenantName : "请选择"}</Text>
                        </TouchableOpacity>

                    
                        : null
                     }
                     
                    <View style={{position:'absolute', backgroundColor:'rgba(255,0,0,0.2)', right:50, top:-5, height:30, padding:5}}>
                         <Text>入库</Text>
                     </View>
                     <View style={{position:'absolute', backgroundColor:'rgba(0,255,0,0.2)', right:10, top:-5, height:30, padding:5}}>
                         <Text>出库</Text>
                     </View>

                     <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.outsourceingTenantModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                {/* <View style={CommonStyle.rowLabView}>
                                    <TextInput 
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({outsourceingTenantSearchKeyWord:text})}
                                    >
                                        {this.state.outsourceingTenantSearchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={()=>{
                                        this.searchOutsourceingTenantSearchKeyWord();
                                        }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View> */}
                                <ScrollView style={{}}>
                                    <View style={{flexDirection:'row', flexWrap:'wrap', overflow:'scroll'}}>
                                    {
                                        (this.state.outsourceingTenantDataSource && this.state.outsourceingTenantDataSource.length > 0) 
                                        ? 
                                        this.state.outsourceingTenantDataSource.map((item, index)=>{
                                            if (index < 1000) {
                                                return this.renderOutsourceingTenantItem(item)
                                            }
                                        })
                                        : <EmptyRowViewComponent/> 
                                    }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                    <TouchableOpacity onPress={() => { 
                                        this.setState({
                                            outsourceingTenantModal:false,
                                            outsourceingTenantSearchKeyWord:"",                         
                                            // selOutsourcingTenantId: null,
                                            // selOutsourcingTenantName: null,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            outsourceingTenantModal:false,
                                            outsourcingTenantId: this.state.selOutsourcingTenantId,
                                            outsourceingTenantName: this.state.selOutsourcingTenantName,
                                            dataSource:[]
                                        })
                                        this.loadInventoryDetailList(this.state.brickTypeId, this.state.isOutsourcingInventoryDetail, this.state.selOutsourcingTenantId);
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                </View>

                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.brickTypeNameLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        ItemSeparatorComponent={this.space}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
                
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});