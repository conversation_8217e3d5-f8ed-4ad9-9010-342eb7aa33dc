import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, KeyboardAvoidingView, TouchableOpacity, Dimensions, Modal, Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import _ from 'lodash'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class ProductCheckMgrAdd extends Component {
    constructor() {
        super()
        this.state = {
            operate: "",
            userId: "",
            userCode: "",
            userPwd: "",
            // 检选id
            checkId: "",
            // 订单列表
            ordersDataSource: [
                // {
                //     "orderId":91,
                //     "orderName":'砖型1'
                // },
                // {
                //     "orderId":90,
                //     "orderName":'砖型2'
                // },
                // {
                //     "orderId":80,
                //     "orderName":'砖型3'
                // },
                // {
                //     "orderId":79,
                //     "orderName":'砖型4'
                // }
            ],
            // 勾选的砖型（订单名称）
            selOrderId: 0,
            selOrderName: "",
            // 废品原因列表
            wasteCauseDataSource: [],
            // 准备新增的废品原因列表
            selWasteCauseList: [],
            pieceWeight: "",
            goodsNumber: "",
            temperingNumber: "",
            secondLevelNumber: "",
            productionLineDataSource: [],
            selProductionLineId: null,
            modal: false,
            searchKeyWord: null,
            // 不显示回火、二级品数量的租户列表
            excludeTemAndSecTenantIdList: [59,66],
            // 不显示正品数量的租户列表
            excludeGoodsTenantIdList: [],
            checkTime: "",
            selectedCheckDate: []
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');

        // this.loadOrderList();
        this.loadWasteCauseList();
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { checkId, productionLineId} = route.params;
            if (checkId) {
                this.setState({
                    operate: "编辑",
                    checkId: checkId,
                    selProductionLineId: productionLineId,
                })
                loadTypeUrl = "/biz/product/check/get";
                loadRequest = { 'checkId': checkId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditProductCheckDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增",
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectedCheckDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    checkTime: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }

        let url = "/biz/production/line/list";
        let loadRequest1 = { 'currentPage': 1, 'pageSize': 1000 };
        httpPost(url, loadRequest1, this.callBackLoadProductionLine);
    }

    callBackLoadProductionLine = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            let productionLineDataSource = response.data.dataList;
            let selProductionLineId = response.data.dataList[0].productionLineId;
            // console.log("======现在的iD" + this.state.selProductionLineId);

            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                selProductionLineId = constants.loginUser.spUserExtDTO.productionLineId;
            }
            if (this.state.selProductionLineId) {
                selProductionLineId = this.state.selProductionLineId;
            }
            // console.log("===-=设置之前的id===" + this.state.selproductionLineId);
            this.setState({
                productionLineDataSource: productionLineDataSource,
                selProductionLineId: selProductionLineId,
            })
            // console.log("设置后的sel=" + selProductionLineId);
            // console.log("===-=设置之后的id===" + this.state.selProductionLineId);
            this.loadOrder();
        }
    }

    loadOrder = () => {
        var currentTime = new Date();
        var qryEndTime = new Date(currentTime.getTime() + 8 * 3600000);
        var qryStartTime = new Date(qryEndTime.getTime() - 15 * 86400000);
        console.log("qryEndTime===",qryEndTime);
        console.log("qryStartTime===",qryStartTime);
        let url = "/biz/order/list";
        let loadRequest = {
            'currentPage': 1,
            'pageSize': 1000,
            "display": "Y",
            "excludeOrderStateList": [
                "A", "B", "C", "K"
            ],
            'productionLineId': this.state.selProductionLineId,
            "qryContent": "order",
            "searchKeyWord": this.state.searchKeyWord,
            "qryStartTime":qryStartTime,
            "qryEndTime":qryEndTime,
            'encastage':"Y"
        };
        httpPost(url, loadRequest, this.callBackLoadOrder);

    }

    loadEditProductCheckDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectedCheckDate = response.data.checkTime.split("-");
            this.setState({
                selOrderId: response.data.orderId,
                selOrderName: response.data.orderName,
                checkTime:response.data.checkTime,
                selectedCheckDate:selectedCheckDate
            })

            // var varEditOrder={
            //     orderId:response.data.orderId,
            //     orderName:response.data.orderName,
            // };
            // console.log("=====varEditOrder:", varEditOrder)
            // this.setState({
            //     ordersDataSource:this.state.ordersDataSource.concat(varEditOrder)
            // })
            this.setState({
                pieceWeight: response.data.pieceWeight,
                goodsNumber: response.data.goodsNumber,
                temperingNumber: response.data.temperingNumber,
                secondLevelNumber: response.data.secondLevelNumber,
            })

            if (response.data.spProductCheckWasteDetailDTOList && response.data.spProductCheckWasteDetailDTOList.length > 0) {
                // 遍历装窑详情
                response.data.spProductCheckWasteDetailDTOList.forEach((item) => {
                    var varWasteCause = {
                        causeId: item.causeId,
                        causeTitle: item.causeTitle,
                        wasteNumber: item.wasteNumber,
                    };
                    this.setState({
                        selWasteCauseList: this.state.selWasteCauseList.concat(varWasteCause)
                    })
                })
            }
            console.log(this.state.selWasteCauseList)
        }
    }

    // 加载砖型（订单名称）列表
    // loadOrderList=()=>{
    //     // 加载排产状态的订单，显示砖型
    //     let url= "/biz/order/list";
    //     let loadRequest={
    //         'currentPage':1,
    //         'pageSize':100,
    //         "display":"Y",
    //         "excludeOrderStateList":[
    //             "A","K"
    //         ]
    //     };
    //     httpPost(url, loadRequest, this.callBackLoadOrder);
    // }

    callBackLoadOrder = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log(response.data.dataList);
            this.setState({
                ordersDataSource: response.data.dataList
            })
            // if (this.state.checkId && this.state.selOrderId != 0) {
            //     // 读编辑数据
            // }
            // else {
            //     this.setState({
            //         selOrderId:response.data.dataList[0] ? response.data.dataList[0].orderId : 0,
            //     })
            // }
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadWasteCauseList = () => {
        let url = "/biz/ungraded/cause/list";
        let loadRequest = {
            'currentPage': 1,
            'pageSize': 100,
            "causeType": "P"
        };
        httpPost(url, loadRequest, this.loadWasteCauseListCallBack);
    }

    loadWasteCauseListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log("response.data.dataList");
            this.setState({
                wasteCauseDataSource: response.data.dataList,
            })
            if (this.state.checkId) {
                // 走编辑
            }
            else {
                if (response.data.dataList.length > 0) {
                    var varWasteCause = {
                        index: 0,
                        causeId: response.data.dataList[0].causeId,
                        causeTitle: response.data.dataList[0].causeTitle,
                        wasteNumber: ""
                        // brickAmount:response.data.dataList[0].brickAmount
                    };
                    this.setState({
                        selWasteCauseList: this.state.selWasteCauseList.concat(varWasteCause)
                    })

                }
            }
            console.log(this.state.selWasteCauseList)
        }
    }

    // 成品检选保存函数
    saveProductCheck = () => {
        console.log(this.state.selWasteCauseList);
        console.log(this.state.pieceWeight);
        console.log(this.state.goodsNumber);
        console.log(this.state.temperingNumber);
        let toastOpts;
        if (!this.state.selOrderId || this.state.selOrderId === 0) {
            toastOpts = getFailToastOpts("请选择砖型");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.pieceWeight) {
        //     toastOpts = getFailToastOpts("请输入单重");
        //     WToast.show(toastOpts)
        //     return;
        // }
        var goodsNumber = 0;
        if (this.state.goodsNumber != null && this.state.goodsNumber != "") {
            goodsNumber = this.state.goodsNumber
        }

        let loadTypeUrl = "/biz/product/check/compareEncastage";
        let loadRequest = {
            'checkId':this.state.checkId,
            'orderId': this.state.selOrderId,
            'goodsAmount':goodsNumber
        };
        httpPost(loadTypeUrl, loadRequest,(response)=>{
            if(response.code == 200){
                let url = "/biz/product/check/add";
                let _spWasteCauseDetailDTOList = [];
                this.state.selWasteCauseList.map((elem, index) => {
                    var wasteCauseDetailDTO = {
                        "causeId": elem.causeId,
                        "wasteNumber": elem.wasteNumber,
                    }
                    _spWasteCauseDetailDTOList.push(wasteCauseDetailDTO);
                })
                let requestParams = {
                    "checkId": this.state.checkId,
                    "orderId": this.state.selOrderId,
                    "pieceWeight": this.state.pieceWeight,
                    "goodsNumber": this.state.goodsNumber,
                    "temperingNumber": this.state.temperingNumber,
                    "secondLevelNumber": this.state.secondLevelNumber,
                    "spProductCheckWasteDetailDTOList": _spWasteCauseDetailDTOList,
                    "userId": constants.loginUser.userId,
                    "checkTime": this.state.checkTime
                };
                console.log("=======requestParams", requestParams);
                httpPost(url, requestParams, this.saveProductCheckCallBack);
            }
            else{
                WToast.show({data: response.message})
                return;
            }
        });
        

        // let url = "/biz/product/check/add";
        // let _spWasteCauseDetailDTOList = [];
        // this.state.selWasteCauseList.map((elem, index) => {
        //     var wasteCauseDetailDTO = {
        //         "causeId": elem.causeId,
        //         "wasteNumber": elem.wasteNumber,
        //     }
        //     _spWasteCauseDetailDTOList.push(wasteCauseDetailDTO);
        // })
        // let requestParams = {
        //     "checkId": this.state.checkId,
        //     "orderId": this.state.selOrderId,
        //     "pieceWeight": this.state.pieceWeight,
        //     "goodsNumber": this.state.goodsNumber,
        //     "temperingNumber": this.state.temperingNumber,
        //     "secondLevelNumber": this.state.secondLevelNumber,
        //     "spProductCheckWasteDetailDTOList": _spWasteCauseDetailDTOList,
        //     "userId": constants.loginUser.userId,
        //     "checkTime": this.state.checkTime
        // };
        // console.log("=======requestParams", requestParams);
        // httpPost(url, requestParams, this.saveProductCheckCallBack);
    }

    // 保存回调函数
    saveProductCheckCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('检选完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    // 砖型单项渲染
    renderOrderRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.checkId) {
                    return;
                }
                this.setState({
                    selOrderId: item.orderId,
                    selOrderName: item.orderName
                })
            }}>
                <View key={item.orderId} style={[item.orderId === this.state.selOrderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle, this.state.checkId ? CommonStyle.disableViewStyle : '']}>
                    <Text style={item.orderId === this.state.selOrderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    renderProductLineRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.checkId) {
                    return;
                }
                // var selMachineId = null;
                // 切换生产车间时，下面的机台也要跟着变，机台默认选择第一个
                this.setState({
                    selProductionLineId: item.productionLineId,
                    selOrderId: "",
                    selOrderName: "",
                    searchKeyWord: ""
                    // machineDataSource:item.machineDTOList,
                })
                var currentTime = new Date();
                var qryEndTime = new Date(currentTime.getTime() + 8 * 3600000);
                var qryStartTime = new Date(qryEndTime.getTime() - 15 * 86400000);
                console.log("qryEndTime===",qryEndTime);
                console.log("qryStartTime===",qryStartTime);
                let url = "/biz/order/list";
                let loadRequest = {
                    'currentPage': 1,
                    'pageSize': 1000,
                    "display": "Y",
                    "excludeOrderStateList": [
                        "A", "B", "C", "K"
                    ],
                    'productionLineId': item.productionLineId,
                    "qryContent": "order",
                    "qryStartTime":qryStartTime,
                    "qryEndTime":qryEndTime,
                    'encastage':"Y"
                };
                httpPost(url, loadRequest, this.callBackLoadOrder);
                // if (item.machineDTOList && item.machineDTOList.length > 0) {
                //     selMachineId = item.machineDTOList[0].machineId;
                // }
                // this.setState({
                //     selMachineId:selMachineId,
                // })
            }}>
                <View key={item.productionLineId} style={[this.state.checkId ? CommonStyle.disableViewStyle : '',item.productionLineId === this.state.selProductionLineId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/2,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.productionLineId === this.state.selProductionLineId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.productionLineName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/*<Text style={CommonStyle.headLeftText}>返回</Text>*/}
            //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("ProductCheckMgrList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>成品检选</Text>
            // </TouchableOpacity>

            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>新增检选</Text>
                </TouchableOpacity>
            </View>
        )
    }

    openCheckDate() {
        if (this.state.checkId) {
            return;
        }
        this.refs.SelectCheckDate.showDate(this.state.selectedCheckDate)
    }

    callBackSelectCheckDateValue(value) {
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedCheckDate: value
        })
        if (value && value.length) {
            var checkTime = "";
            var vartime;
            for (var index = 0; index < value.length; index++) {
                vartime = value[index];
                if (index === 0) {
                    checkTime += vartime;
                }
                else {
                    checkTime += "-" + vartime;
                }
            }
            this.setState({
                checkTime: checkTime
            })
        }
    }
    render() {
        // 动态显示废品原因数据
        var pages = [];
        for (var i = 0; i < this.state.selWasteCauseList.length; i++) {
            const _wasteCauseDataSource = _.cloneDeep(this.state.wasteCauseDataSource);
            _wasteCauseDataSource.map((elem, index) => {
                elem._index = i;
                return elem;
            })
            pages.push(
                <View key={"view_" + this.state.selWasteCauseList[i].causeId + "_" + i}>
                    <View style={[CommonStyle.addItemSplitRowView,{marginLeft:5}]}>
                        <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                        <Text style={CommonStyle.addItemSplitRowText}>废品原因</Text>
                    </View>
                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', marginLeft:25 }}>
                        {
                            _wasteCauseDataSource.map((elem, index) => {
                                return (
                                    <TouchableOpacity key={elem.causeId} onPress={(event) => {
                                        // console.log("=======event", event);
                                        var tempSelWasteCauseList = this.state.selWasteCauseList;
                                        tempSelWasteCauseList[elem._index] = {
                                            index: elem.index,
                                            causeId: elem.causeId,
                                            causeTitle: elem.causeTitle,
                                            wasteNumber: tempSelWasteCauseList[elem._index].wasteNumber
                                        }
                                        this.setState({
                                            selWasteCauseList: tempSelWasteCauseList
                                        })
                                    }} >
                                        <View  style={[this.state.selWasteCauseList[i].causeId === elem.causeId ?
                                            {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                            :
                                            {backgroundColor: '#F2F5FC'}
                                            ,
                                            {
                                                marginRight: 8,
                                                marginTop: 8,
                                                marginBottom: 4,
                                                borderRadius: 4,
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                                height: 36,
                                                width: (screenWidth - 54)/3,
                                                borderRadius: 4
                                            }
                                        ]}>
                                            <Text style={[this.state.selWasteCauseList[i].causeId === elem.causeId ?
                                                {
                                                    color: '#1E6EFA'
                                                }
                                                :
                                                {
                                                    color: '#404956'
                                                }
                                                ,
                                            {
                                                fontSize: 16, textAlign : 'center'
                                            }
                                            ]}>
                                                {elem.causeTitle}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                );
                            })
                        }
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View style={CommonStyle.rowLabView}>
                        <View style={[CommonStyle.rowLabLeftView,{marginleft:0}]}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={CommonStyle.rowLabTextStyle}>数量</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            placeholder={'请输入'}
                            style={[CommonStyle.rowRightTextInput,{borderWidth:0}]}
                            causeId={this.state.selWasteCauseList[i].causeId}
                            onChange={(event) => {
                                // 通过回调事件查看控件属性
                                // var orderId = event.target._internalFiberInstanceHandleDEV.memoizedProps.orderId;
                                var causeId = event._dispatchInstances.memoizedProps.causeId;
                                var text = event.nativeEvent.text;
                                console.log("=====isNumber:", isNumber(text));
                                var varselWasteCause;
                                for (var index = 0; index < this.state.selWasteCauseList.length; index++) {
                                    varselWasteCause = this.state.selWasteCauseList[index];
                                    if (causeId === varselWasteCause.causeId) {
                                        varselWasteCause.wasteNumber = text;
                                        this.state.selWasteCauseList[index] = varselWasteCause;
                                        console.log("==数据更新==this.state.selWasteCauseList", this.state.selWasteCauseList);
                                    }
                                }
                            }}
                        >
                            {this.state.selWasteCauseList[i].wasteNumber}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                </View>
                
            );
        }
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + "检选"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: -2 }} />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>检选日期</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openCheckDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, {borderWidth:0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.checkTime ? "请选择成品检选日期" : this.state.checkTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View style={{ width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15 }} />
                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>生产车间</Text>
                    </View>
                    <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row', marginLeft:15}}>
                        {
                            (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0)
                                ?
                                this.state.productionLineDataSource.map((item, index) => {
                                    return this.renderProductLineRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>砖型</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[{ flexWrap: 'wrap' , marginLeft:25}, this.state.checkId ? CommonStyle.disableViewStyle : null]}>
                        <TouchableOpacity onPress={() => {
                            if (this.state.checkId) {
                                return;
                            }
                            this.setState({
                                modal: true,
                            })

                            if (!this.state.selOrderId && this.state.ordersDataSource && this.state.ordersDataSource.length > 0) {
                                this.setState({
                                    // selBrickTypeId:this.state.ordersDataSource[0].brickTypeId,
                                    selOrderId: this.state.ordersDataSource[0].orderId,
                                    selOrderName: this.state.ordersDataSource[0].orderName,
                                })
                            }
                        }}>
                            <View style={[this.state.selOrderId && this.state.selOrderName ?
                                {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                :
                                {backgroundColor: '#F2F5FC'}
                                ,
                                {
                                    marginRight: 8,
                                    marginTop: 8,
                                    marginBottom: 4,
                                    borderRadius: 4,
                                    justifyContent: 'center',
                                    alignContent: 'center',
                                    height: 36,
                                    paddingLeft:6,
                                    paddingRight:6,
                                    // width: (screenWidth - 54)/2,
                                    borderRadius: 4,
                                }
                            ]}>
                                <Text style={[this.state.selOrderId && this.state.selOrderName ?
                                    {
                                        color: '#1E6EFA'
                                    }
                                    :
                                    {
                                        color: '#404956'
                                    }
                                    ,
                                {
                                    fontSize: 16, textAlign : 'center'
                                }
                                ]}>
                                    选择砖型
                                    {this.state.selOrderId && this.state.selOrderName ? ("：" + this.state.selOrderName) : null}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadOrder();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state.ordersDataSource && this.state.ordersDataSource.length > 0)
                                                ?
                                                this.state.ordersDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderOrderRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selOrderId) {
                                            let toastOpts = getFailToastOpts("您还没有选择砖型");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>

                        </View>
                        <View>

                        </View>
                    </Modal>
                    {/* <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.ordersDataSource && this.state.ordersDataSource.length > 0) 
                            ? 
                            this.state.ordersDataSource.map((item, index)=>{
                                return this.renderPlanPointRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View> */}
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>单重（Kg）</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ pieceWeight: text })}
                        >
                            {this.state.pieceWeight}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    {
                        !this.state.excludeGoodsTenantIdList.includes(constants.loginUser.tenantId) ?
                            <View>
                                <View style={[styles.inputRowStyle]}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                        <Text style={styles.leftLabNameTextStyle}>正品数量</Text>
                                    </View>
                                    <TextInput
                                        keyboardType='numeric'
                                        style={styles.inputRightText}
                                        placeholder={'请输入'}
                                        onChangeText={(text) => this.setState({ goodsNumber: text })}
                                    >
                                        {this.state.goodsNumber}
                                    </TextInput>
                                </View>
                            </View>
                            :
                            <View />
                    }
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    {/* <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           <Text style={styles.leftLabNameTextStyle}>正品数量</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入正品数量'}
                            onChangeText={(text) => this.setState({goodsNumber:text})}
                        >
                            {this.state.goodsNumber}
                        </TextInput>
                    </View> */}
                    {
                        !this.state.excludeTemAndSecTenantIdList.includes(constants.loginUser.tenantId) ?
                            <View>
                                <View style={[styles.inputRowStyle]}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                        <Text style={styles.leftLabNameTextStyle}>回火数量</Text>
                                    </View>
                                    <TextInput
                                        keyboardType='numeric'
                                        style={styles.inputRightText}
                                        placeholder={'请输入'}
                                        onChangeText={(text) => this.setState({ temperingNumber: text })}
                                    >
                                        {this.state.temperingNumber}
                                    </TextInput>
                                </View>
                                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                                <View style={[styles.inputRowStyle]}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                        <Text style={styles.leftLabNameTextStyle}>二级品数量</Text>
                                    </View>
                                    <TextInput
                                        keyboardType='numeric'
                                        style={styles.inputRightText}
                                        placeholder={'请输入'}
                                        onChangeText={(text) => this.setState({ secondLevelNumber: text })}
                                    >
                                        {this.state.secondLevelNumber}
                                    </TextInput>
                                </View>
                                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                            </View>
                            :
                            <View />
                    }
                    {/* <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           <Text style={styles.leftLabNameTextStyle}>回火数量</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入回火数量'}
                            onChangeText={(text) => this.setState({temperingNumber:text})}
                        >
                            {this.state.temperingNumber}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           <Text style={styles.leftLabNameTextStyle}>二级品数量</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入二级品数量'}
                            onChangeText={(text) => this.setState({secondLevelNumber:text})}
                        >
                            {this.state.secondLevelNumber}
                        </TextInput>
                    </View> */}


                    <View>
                        {
                            pages.map((elem, index) => {
                                return elem;
                            })
                        }
                    </View>

                    <View style={styles.btnRowView}>
                        <TouchableOpacity onPress={() => {
                            // console.log("==========this.state.brickTypeDataSource.length:", this.state.wasteCauseDataSource.length);
                            if (this.state.selWasteCauseList.length >= this.state.wasteCauseDataSource.length) {
                                WToast.show({ data: "废品数量达到上限，不能再添加了" });
                                return;
                            }
                            if (this.state.wasteCauseDataSource.length > 0) {
                                var selIndex = this.state.selWasteCauseList.length;
                                var varBrickType = {
                                    index: selIndex,
                                    causeId: this.state.wasteCauseDataSource[selIndex].causeId,
                                    causeTitle: this.state.wasteCauseDataSource[selIndex].causeTitle,
                                    wasteNumber: ""
                                };
                                this.setState({
                                    selWasteCauseList: this.state.selWasteCauseList.concat(varBrickType)
                                })
                                // console.log("xxxxxx======selWasteCauseList:", this.state.selWasteCauseList)
                            }
                        }}>
                            <View style={styles.btnAddView}>
                                <Text style={styles.btnAddText}>+新增废品</Text>
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity onPress={() => {
                            if (!this.state.selWasteCauseList) {
                                WToast.show({ data: "没有可删除的，请先指定废品原因" });
                                return;
                            }
                            // if (this.state.selWasteCauseList.length <= 1) {
                            //     WToast.show({data:"至少要指定一种砖型"});
                            //     return;
                            // }
                            this.setState({
                                selWasteCauseList: this.state.selWasteCauseList.slice(0, -1)
                            })
                        }}>
                            <View style={styles.btnDeleteView}>
                                <Text style={styles.btnDeleteText}>-删除</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={[CommonStyle.blockAddCancelSaveStyle]}>
                        <TouchableOpacity onPress={() => {
                            this.props.navigation.goBack()
                        }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveProductCheck.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                <BottomScrollSelect
                    ref={'SelectCheckDate'}
                    callBackDateValue={this.callBackSelectCheckDateValue.bind(this)}
                />

            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        borderRadius: 6,backgroundColor: '#1E6EFA', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        borderRadius: 6,backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 4,
        marginBottom:4,
        marginLeft:15
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft:15,
        // paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        // paddingLeft: 15,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:5,
        marginRight:5,
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        // borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
})