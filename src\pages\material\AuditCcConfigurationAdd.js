import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class AuditCcConfigurationAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:10,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            auditConfigId:"",
            selUserIdList:[],
            oldSelUserIdList:[],
            searchKeyWord:null,
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { auditConfigId } = route.params;
            if (auditConfigId) {
                console.log("=============auditConfigId:" + auditConfigId + "");
                this.setState({
                    auditConfigId:auditConfigId,
                })
                this.loadUserList(auditConfigId);
                this.loadUserIdList(auditConfigId);
            }
        }
    }

    loadUserList=(auditConfigId)=>{
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "auditConfigId": auditConfigId ? auditConfigId : this.state.auditConfigId,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this.loadUserListCallBack);
    }

    loadUserListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
            // var staffDTO;
            // var selUserIdList = [];
            // var oldSelUserIdList = [];
            // for(var index = 0; index < dataAll.length; index ++) {
            //     staffDTO = dataAll[index];
            //     if (staffDTO && staffDTO.ccConfigSelectedUser === "Y") {
            //         selUserIdList = selUserIdList.concat(staffDTO.userId)
            //     }
            // }
            // for(var index = 0; index < dataNew.length; index ++) {
            //     staffDTO = dataNew[index];
            //     if (staffDTO && staffDTO.ccConfigSelectedUser === "Y") {
            //         oldSelUserIdList = this.state.oldSelUserIdList.concat(staffDTO.userId)
            //     }
            // }
            // this.setState({
            //     selUserIdList:selUserIdList,
            //     // oldSelUserIdList:copyArr(selUserIdList),
            //     oldSelUserIdList:oldSelUserIdList,
            // })
            // console.log("=========oldSelUserIdList:", oldSelUserIdList);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadUserIdList=(auditConfigId)=>{
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
            "auditConfigId": auditConfigId ? auditConfigId : this.state.auditConfigId,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this.loadUserIdListCallBack);
    }

    loadUserIdListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataAll = response.data.dataList;
            var staffDTO;
            var selUserIdList = [];
            // var oldSelUserIdList = [];
            for(var index = 0; index < dataAll.length; index ++) {
                staffDTO = dataAll[index];
                if (staffDTO && staffDTO.ccConfigSelectedUser === "Y") {
                    selUserIdList = selUserIdList.concat(staffDTO.userId)
                }
            }
            this.setState({
                selUserIdList:selUserIdList,
                oldSelUserIdList:copyArr(selUserIdList),
                // oldSelUserIdList:oldSelUserIdList,
            })
            console.log("=========oldSelUserIdList:", selUserIdList);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadUserList();
    }

    searchByKeyWord=()=>{
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditConfigId": this.state.auditConfigId,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);

    }

     // 下拉触顶刷新到第一页
     _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditConfigId": this.state.auditConfigId,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
        <View style={ CommonStyle.viewListRightViewStyle }>
            <TouchableOpacity onPress={() => {
                let requestUrl= "/biz/audit/cc/config/add";
                let requestParams={
                    "auditConfigId":this.state.auditConfigId,
                    "selUserIdList":this.state.selUserIdList,
                    "oldSelUserIdList":this.state.oldSelUserIdList,
                };
                httpPost(requestUrl, requestParams, (response)=>{
                    let toastOpts;
                    if (response && response.code === 200) {
                        if (this.props.route.params.refresh) {
                            this.props.route.params.refresh();
                        }
                        toastOpts = getSuccessToastOpts('保存完成');
                        WToast.show(toastOpts);
                        this.props.navigation.goBack()
                        // this.props.navigation.navigate("JobStaffMgrList", 
                        // {
                        //     // 传递回调函数
                        //     refresh: this.callBackFunction 
                        // })
                    }
                    else {
                        toastOpts = getFailToastOpts(response.message);
                        WToast.show({data:response.message})
                    }
                });
            }}>
                <Text style={CommonStyle.headRightText}>保存</Text>
            </TouchableOpacity>
        </View>
        )
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    renderRow = (item, index) => {
        return (
            <View>
                <TouchableOpacity onPress={()=>{
                    var selUserIdList = this.state.selUserIdList;
                    if (item.ccConfigSelectedUser && item.ccConfigSelectedUser == "Y") {
                        item.ccConfigSelectedUser = "N";
                        arrayRemoveItem(selUserIdList, item.userId);
                    }
                    else {
                        item.ccConfigSelectedUser = "Y";
                        selUserIdList = selUserIdList.concat(item.userId)
                    }
                    this.setState({
                        selUserIdList:selUserIdList,
                    })
                    WToast.show({data:'点击了' + item.userName});
                    console.log("======selUserIdList:", selUserIdList)
                }}>
                    <View key={item.userId} style={[styles.innerViewStyle,(item.ccConfigSelectedUser && item.ccConfigSelectedUser === 'Y') ? {backgroundColor:'rgba(255,0,0,0.4)',borderRadius:20,hight:80}:{} ]}>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>员工姓名：{item.userName}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>联系电话：{item.userNbr ? item.userNbr : "无"}</Text>
                        </View>
                    </View>
                </TouchableOpacity>

            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={'选择抄送人'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.headViewStyle, { borderLeftWidth: 0, borderRightWidth: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={CommonStyle.singleSearchBox}>
                        <View style={CommonStyle.searchBoxWithoutOthers}>
                            {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                            <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            <TextInput
                                style={{color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 5, paddingTop: 0, paddingBottom: 0, paddingRight: 0, paddingLeft: 0 }}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'姓名/电话'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>
                </View>
                {/* <ScrollView style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    {this.state.dataSource.map((item, key)=>{
                        return(
                            <TouchableOpacity onPress={()=>{
                                var selUserIdList = this.state.selUserIdList;
                                if (item.ccConfigSelectedUser && item.ccConfigSelectedUser == "Y") {
                                    item.ccConfigSelectedUser = "N";
                                    arrayRemoveItem(selUserIdList, item.userId);
                                }
                                else {
                                    item.ccConfigSelectedUser = "Y";
                                    selUserIdList = selUserIdList.concat(item.userId)
                                }
                                this.setState({
                                    selUserIdList:selUserIdList,
                                })
                                WToast.show({data:'点击了' + item.userName});
                                console.log("======selUserIdList:", selUserIdList)
                            }}>
                                <View key={item.userId} style={[styles.innerViewStyle,(item.ccConfigSelectedUser && item.ccConfigSelectedUser === 'Y') ? {backgroundColor:'rgba(255,0,0,0.4)',borderRadius:20,hight:80}:{} ]}>
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>员工姓名：{item.userName}</Text>
                                    </View>
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>联系电话：{item.userNbr}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                            
                        )
                    })}
                </ScrollView> */}
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        paddingBottom:5
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    searchInputText:{
        width:screenWidth / 2,
        borderColor:'#000000',
        // borderBottomWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:16,
        marginLeft:10,
        paddingLeft:10,
        paddingRight:10,
        paddingBottom:0,
        paddingTop:0
    },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
        borderBottomWidth:5,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },

});