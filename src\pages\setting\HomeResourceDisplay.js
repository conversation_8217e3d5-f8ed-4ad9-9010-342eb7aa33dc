import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Image, TextInput,Modal, ImageBackground
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
const {ifIphoneXContentViewHeight} = require('../../utils/ScreenUtil');

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class HomeResourceDisplay extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 10,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            searchKeyWord: null,
            paramCode:"",
            paramValue:"",
            tenantIdList: [],
            tenantIdListNew: [],
            configDataList: [],
            // 判断首页资源是否已配置
            homeResourceDisplayFlag: false
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { paramCode, homeResourceDisplayFlag } = route.params;
            console.log("paramCode....", paramCode)
            console.log("homeResourceDisplayFlag....", homeResourceDisplayFlag)
            if (paramCode) {
                this.setState({
                    paramCode: paramCode
                })   
            }
            if (homeResourceDisplayFlag) {
                this.setState({
                    homeResourceDisplayFlag: homeResourceDisplayFlag
                })   
            }
        }
        this.loadHomeResourceDisplayConfig();
        this.loadTenantList();
    }

    loadHomeResourceDisplayConfig=()=>{
        let url= "/biz/tenant/config/homeResourceDisplaylist";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10000,
            "paramCode": "MEMBER_SHARE_WEBSITE_HOME_RESOURCE_DISPLAY",
            "tenantId": constants.loginUser.tenantId,
        };
        httpPost(url, loadRequest, this.loadHomeResourceDisplayConfigCallBack);
    }

    loadHomeResourceDisplayConfigCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length == 1) {
                var paramCode = response.data.dataList[0].paramCode;
                var val = response.data.dataList[0].paramValue;
                if (val) {
                    var tenantIdList = val.split(",")
                    tenantIdList = tenantIdList.map(Number)
                    console.log("====tenantIdList====", tenantIdList)
                    this.setState({
                        paramValue: val,
                        tenantIdList: tenantIdList,
                    })
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/cr/member/management/tenant/list/all";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/cr/member/management/tenant/list/all";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadTenantList();
    }

    loadTenantList = () => {
        let url = "/biz/cr/member/management/tenant/list/all";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this.loadTenantListCallBack);
    }

    loadTenantListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log('response.data.dataList...........', response.data.dataList)
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord = () => {
        let url = "/biz/cr/member/management/tenant/list/all";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    renderRow = (item, index) => {
        return (
            <View key={item.tenantId} style={{backgroundColor:'rgba(255, 255, 255, 1)'}}>
                <TouchableOpacity onPress={() => {
                    var selTenantIdList = this.state.tenantIdList;
                    if (selTenantIdList.includes(item.tenantId)) {
                        arrayRemoveItem(selTenantIdList, item.tenantId);
                    }
                    else {
                        selTenantIdList = selTenantIdList.concat(item.tenantId)
                    }
                    this.setState({
                        tenantIdList: selTenantIdList,
                    })
                    WToast.show({ data: '点击了' + item.tenantName });
                    console.log("======selTenantIdList:", selTenantIdList)
                }}>

                    <View style={[styles.inputRowStyle, { paddingLeft: 12, height: 80, paddingTop: 10, paddingBottom: 10}]}>
                        <View style={[{ width: screenWidth - 80, flexDirection: "row"}]}>
                            {
                                item.tenantLogo ?
                                <Image source={{ uri: (constants.image_addr + '/' + item.tenantLogo)}} style={{height: 48, width: 48, borderRadius: 50, marginTop:5}} />
                                :
                                <Image source={require('../../assets/icon/iconfont/tenantLogo.png')} style={{height: 48, width: 48, borderRadius: 50, marginTop:5}} /> 
                            }
                            
                            <View style={{width: screenWidth - 140, marginLeft: 10 , flexDirection: 'column', justifyContent: "center" }}>
                                <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16,fontWeight:'500', lineHeight: 24 }}>{item.tenantName}</Text>
                            </View>
                        </View>

                        {
                            (this.state.tenantIdList.includes(item.tenantId)) ?
                            <View style={[styles.titleViewStyle, { position: 'absolute', right: 15, top: 25}]}>
                                <Image style={{ width: 20, height: 20}} source={require('../../assets/icon/iconfont/staffSelected.png')}></Image>
                            </View>
                            :
                            <View/>
                        }
                        
                    </View>
                    
                </TouchableOpacity>

                <View style={styles.lineViewStyle}/> 
            </View>
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={this.saveDisplayTenantList.bind(this)
            }>
                <Text style={CommonStyle.headRightText}>保存</Text>
            </TouchableOpacity>
        )
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    saveDisplayTenantList = () => {
        console.log("=======saveDisplayTenantList");
        let url = "/biz/tenant/config/add";
        if (this.state.homeResourceDisplayFlag) {
            url = "/biz/tenant/config/modify";
        }
        console.log("url================", url)
        let requestParams = {
            "paramCode": this.state.paramCode,
            "paramValue": this.state.tenantIdList.toString(),
        };
        console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.saveDisplayTenantListCallBack);
    }

    saveDisplayTenantListCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('更新完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    render() {
        return (
            <View style={{backgroundColor: 'rgba(242, 245, 252, 1)'}}>
                <CommonHeadScreen title='首页资源展示'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <View style={{ marginTop: 0, index: 1000, backgroundColor: 'rgba(255, 255, 255, 1)', }} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={[CommonStyle.singleSearchBox,{marginTop:10,height:42}]}>
                        <View style={CommonStyle.searchBoxWithoutOthers}>
                            <View>
                                <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={{ color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, width: screenWidth - 80, marginLeft: 5, paddingTop: 0, paddingBottom: 0 }}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                placeholderTextColor= "rgba(0, 10, 32, 0.5)"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'租户名称'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>
                    <View style={[styles.itemContentTextStyle, {borderBottomColor:'#E8E9EC',borderBottomWidth:1}]}>
                        <Text style={styles.itemContentStyle}>请勾选在私域平台首页展示的公司/组织</Text>
                    </View>
                </View>

                <View style={[CommonStyle.contentViewStyle, { backgroundColor: 'rgba(242, 245, 252, 1)', height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        // borderWidth: 1,
        // borderColor: "#FFFFFF",
        backgroundColor: "#FFFFFF",
        borderRadius: 5
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        paddingBottom: 5
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth:1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop: 0
    },

    // innerViewStyle: {
    //     // marginTop:10,
    //     borderColor: "#F4F4F4",
    //     borderWidth: 8,
    // },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    itemContentTextStyle: {
        paddingLeft: 14,
        paddingRight: 16,
        lineHeight: 24,
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        // marginTop: 15,
        // marginBottom: 6,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC',
    },
});