import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,ScrollView,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class InventoryAuditBacklogDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            inventoryId:"",
            auditState:"",
            ccUserDataSource:[],
            auditConfigId:"",
            auditTypeCode:"",
            nodeIdList:[],
            inventoryItem:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId ,auditItemId,auditTypeCode,inventoryItem} = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
            if (inventoryItem) {
                this.setState({
                    inventoryItem:inventoryItem
                })
            }

            if (auditItemId && auditTypeCode) {
                console.log("=============auditItemId" + auditItemId + "");
                let url= "/biz/audit/node/record/auditListByItemId";
                let loadRequest={
                    "auditItemId": auditItemId,
                    "excludeAuditResult":"I",
                    "auditTypeCode":auditTypeCode
                };
                httpPost(url, loadRequest, this.loadAuditListByItemIdCallBack);

                this.setState({
                    inventoryId:auditItemId,
                    auditTypeCode:auditTypeCode
                })
                let loadUrl= "/biz/material/inventory/get";
                let request={'inventoryId':auditItemId};
                httpPost(loadUrl, request, this.loadEditPurchaseDataCallBack);
            }
        }
    }

    loadAuditListByItemIdCallBack=(response) => {
        if (response.code == 200 && response.data) {
            var dataSource = response.data.reverse()
            this.setState({
                dataSource:dataSource,
            })
            var nodeIdList = []
            dataSource.map(item=>{
                nodeIdList.push(item.nodeId)
            })
            console.log("nodeIdList",nodeIdList)
            this.setState({
                nodeIdList:nodeIdList
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditPurchaseDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                auditState:response.data.auditState,
                auditConfigId:response.data.auditConfigId,
            })
            if(null != response.data.auditConfigId) {
                this.loadAuditCcConfigList(response.data.auditConfigId)
                this.loadAuditNodeList(response.data.auditConfigId)
            }
        }
    }

    loadAuditNodeList=(auditConfigId)=>{
        let url = "/biz/audit/node/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
            "auditConfigId":auditConfigId ? auditConfigId : this.state.auditConfigId
        };
        httpPost(url, loadRequest, this.loadAuditNodeListCallBack);
    }

    loadAuditNodeListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                auditNodeDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }

    }

    loadAuditCcConfigList=(auditConfigId)=>{
        let url = "/biz/audit/cc/config/list";
        console.log("auditConfigId ++++===",auditConfigId);
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
            "auditConfigId":auditConfigId ? auditConfigId : this.state.auditConfigId
        };
        httpPost(url, loadRequest, this.loadAuditCcConfigListCallBack);
    }

    loadAuditCcConfigListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                ccUserDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }

    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity>
                {/* <Text style={CommonStyle.headRightText}>新增</Text> */}
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='审核详情'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                {
                    this.state.auditNodeDataSource && this.state.auditNodeDataSource.length > 0 ?
                    <View>
                        {this.state.auditNodeDataSource.map((item, index)=>{
                            return(
                                <View key={item.recordId} >
                                    {/* 判断审核节点是否直接跳过记录，例如一开始的审核节点是提交审核的用户 */}
                                    {
                                        (item.userName == this.state.inventoryItem.operator)?
                                        <View style={styles._innerViewStyle}>
                                            <View style={styles.dotStyle}>
                                                <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/finishDot.png')}></Image>
                                                <View style={{width:12,marginLeft:12,marginTop:15,borderLeftWidth:2,borderLeftColor:'#09c119',height:100}}></View>
                                                <Image style={{width:20, height:20,marginLeft:2.5,marginTop:-10}} source={require('../../assets/icon/iconfont/downArrow.png')}></Image>
                                            </View>
                                            <View>
                                                <View style={[styles._titleViewStyle]}>
                                                    <Text style={[styles._titleTextStyle,{fontWeight:'bold',fontSize:18}]}>{item.nodeName}</Text>
                                                </View>
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>审核人：{item.userName}</Text>
                                                </View>
                                                    <View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>审核批复：
                                                                <Text style={[{color:'green'}]}>
                                                                    同意
                                                                </Text>
                                                            </Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>审核意见：无</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>审核时间：{this.state.purchaseItem?this.state.purchaseItem.gmtCreated:"无"}</Text>
                                                        </View>
                                                    </View>
                                            </View>
                                        </View>
                                        :
                                        <View style={styles._innerViewStyle}>
                                            {
                                                this.state.nodeIdList.includes(item.nodeId)?
                                                <View style={styles.dotStyle}>
                                                    {
                                                        this.state.dataSource.filter(obj => obj.nodeId == item.nodeId)[0].auditResult === 'Y'?
                                                        <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/finishDot.png')}></Image>
                                                        :
                                                        <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/refuseDot.png')}></Image>
                                                    }
                                                    {
                                                        index == this.state.auditNodeDataSource.length - 1?
                                                        null
                                                        :
                                                        (
                                                            this.state.dataSource.filter(obj => obj.nodeId == item.nodeId)[0].auditResult === 'Y'?
                                                            <View>
                                                                <View style={{width:12,marginLeft:12,marginTop:15,borderLeftWidth:2,borderLeftColor:'#09c119',height:100}}></View>
                                                                <Image style={{width:20, height:20,marginLeft:2.5,marginTop:-10}} source={require('../../assets/icon/iconfont/downArrow.png')}></Image>
                                                            </View>
                                                            :
                                                            <View>
                                                                <View style={{width:12,marginLeft:12,marginTop:15,borderLeftWidth:2,borderLeftColor:'#d81e06',height:100}}></View>
                                                                <Image style={{width:20, height:20,marginLeft:2.5,marginTop:-10}} source={require('../../assets/icon/iconfont/downArrowRed.png')}></Image>
                                                            </View>
                                                        )
                                                    }
                                                </View>
                                                :
                                                <View style={styles.dotStyle}>
                                                    <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/unfinishedDot.png')}></Image>
                                                    {
                                                        index == this.state.auditNodeDataSource.length - 1?
                                                        null
                                                        :
                                                        <View>
                                                            <View style={{width:12,marginLeft:12,marginTop:15,borderLeftWidth:2,borderLeftColor:'#ea9518',height:15}}></View>
                                                            <Image style={{width:20, height:20,marginLeft:2.5,marginTop:-10}} source={require('../../assets/icon/iconfont/downArrowOrange.png')}></Image>
                                                        </View>
                                                    }
                                                </View>
                                            }
                                            <View>
                                                <View style={[styles._titleViewStyle]}>
                                                    <Text style={[styles._titleTextStyle,{fontWeight:'bold',fontSize:18}]}>{item.nodeName}</Text>
                                                </View>
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>审核人：{item.userName}</Text>
                                                </View>
                                                {
                                                    this.state.nodeIdList.includes(item.nodeId)
                                                    ?
                                                    <View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>审核批复：
                                                                <Text style={[(this.state.dataSource.filter(obj => obj.nodeId == item.nodeId)[0].auditResult === 'Y' ? {color:'green'} : {color:'red'})]}>
                                                                {this.state.dataSource.filter(obj => obj.nodeId == item.nodeId)[0].auditResult === 'Y' ? "同意" : "驳回"}
                                                                </Text>
                                                            </Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>审核意见：
                                                                {this.state.dataSource.filter(obj => obj.nodeId == item.nodeId)[0].auditOpinion?this.state.dataSource.filter(obj => obj.nodeId == item.nodeId)[0].auditOpinion:"无"}
                                                            </Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>审核时间：{this.state.dataSource.filter(obj => obj.nodeId == item.nodeId)[0].gmtModified ? this.state.dataSource.filter(obj => obj.nodeId == item.nodeId)[0].gmtModified : this.state.dataSource.filter(obj => obj.nodeId == item.nodeId)[0].gmtCreated}</Text>
                                                        </View>
                                                    </View>
                                                    :null
                                                }
                                                
                                            </View>
                                        </View>
                                    }
                                    
                                    
                                </View>
                            )
                                
                            })
                        }
                        {/* {
                            this.state.auditState == '3' || this.state.auditState == '4' ?
                            <View style={styles._innerViewStyle}>
                                <View style={[styles._titleViewStyle,{justifyContent:"flex-start"}]}>
                                    <Text style={[styles._titleTextStyle,{fontWeight:'bold',fontSize:18}]}>抄送人：</Text>
                                    {
                                    this.state.ccUserDataSource.map((item, index)=>{
                                        return(
                                            <View key={item.recordId}>
                                                <Text style={styles._titleTextStyle}>{item.userName + ((index == this.state.ccUserDataSource.length - 1)?"":"、")}</Text>
                                            </View>
                                        )
                                    })
                                }
                                </View>
                                
                            </View>
                            :
                            <View/>
                        } */}
                        <View style={styles._innerViewStyle}>
                            <View style={[styles._titleViewStyle,{justifyContent:"flex-start"}]}>
                                <Text style={[styles._titleTextStyle,{fontWeight:'bold',fontSize:18}]}>抄送人：</Text>
                                {
                                this.state.ccUserDataSource && this.state.ccUserDataSource.length > 0 ?
                                (
                                    this.state.ccUserDataSource.map((item, index)=>{
                                        return(
                                            <View key={item.recordId}>
                                                <Text style={styles._titleTextStyle}>{item.userName + ((index == this.state.ccUserDataSource.length - 1)?"":"、")}</Text>
                                            </View>
                                        )
                                    })
                                )
                                :
                                <Text style={[styles._titleTextStyle,{fontWeight:'bold',fontSize:18}]}>无</Text>
                                }
                                
                            </View>
                            
                        </View>
                            
                    </View> 
                    :
                    <EmptyListComponent/>
                    
                }

                
                
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    _innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:0,
        display:'flex',
        flexDirection:'row'
    },
    _titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    _titleTextStyle:{
        fontSize:16
    },
    dotStyle:{
        marginLeft:10,
        marginRight:10,
        marginTop:5,
        display:'flex',
        flexDirection:'column'
    }
});