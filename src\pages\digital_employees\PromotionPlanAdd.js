import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image, Modal } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class PromotionPlanAdd extends Component {
    constructor() {
        super()
        this.state = {
            operate: "",
            planId: "",
            planTitle: "",
            planContent: "",
            checkInUserId: "",
            checkInUserName: "",
            selCheckInUserId: "",
            selCheckInUserName: "",
            belongClassDataSource: [],
            selBelongClassCode: null,
            selectedPlannedCompletionTime: [],
            plannedCompletionTime: null,
            //员工相关数据
            userModal: false,
            userSearchKeyWord: "",
            userId: "",
            userName: "",
            userDataSource: [],
            _userDataSource: [],
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        // 属性类别
        this.loadCheckInUserList();
        let belongClassDataSource = [
            {
                belongClassCode: '1', belongClassName: '5W2H',
            },
            {
                belongClassCode: '2', belongClassName: '工作闭环',
            },
            {
                belongClassCode: '3', belongClassName: '工作计划',
            },
            {
                belongClassCode: '4', belongClassName: '时间管理',
            },
            {
                belongClassCode: '5', belongClassName: '情绪管理',
            },
            {
                belongClassCode: '6', belongClassName: '个人软实力',
            },
            {
                belongClassCode: '8', belongClassName: '专业技能',
            },
            {
                belongClassCode: '7', belongClassName: '其它',
            },
        ]
        this.setState({
            belongClassDataSource: belongClassDataSource,
        })

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { planId } = route.params;
            if (planId) {
                console.log("========Edit==planId:", planId);
                this.setState({
                    planId: planId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/promotion/plan/get";
                loadRequest = { 'planId': planId };
                httpPost(loadTypeUrl, loadRequest, this.loadPromotionPlanDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectedPlannedCompletionTime: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    plannedCompletionTime: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
    }

    loadCheckInUserList = () => {
        let url = "/biz/portal/user/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadCheckInUserListCallBack);
    }

    loadCheckInUserListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataSource = response.data.dataList;
            // dataSource = dataSource.filter(item => item.userId != constants.loginUser.userId)
            this.setState({
                userDataSource: dataSource
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    loadUser = () => {
        var _userDataSource = copyArr(this.state.userDataSource);
        if (this.state.userSearchKeyWord && this.state.userSearchKeyWord.length > 0) {
            _userDataSource = _userDataSource.filter(item => item.userName.indexOf(this.state.userSearchKeyWord) > -1);
        }
        this.setState({
            _userDataSource: _userDataSource,
        })
    }
    loadPromotionPlanDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectedPlannedCompletionTime;
            if (response.data.plannedCompletionTime) {
                selectedPlannedCompletionTime = response.data.plannedCompletionTime.split("-");
            }
            this.setState({
                planId: response.data.planId,
                planTitle: response.data.planTitle,
                selBelongClassCode: response.data.belongClass + "",
                planContent: response.data.planContent,
                plannedCompletionTime: response.data.plannedCompletionTime,
                selectedPlannedCompletionTime: selectedPlannedCompletionTime,
                selCheckInUserId: response.data.checkInUserId,
                selCheckInUserName: response.data.checkInUserName,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("PromotionPlanList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>任务管理</Text>
            // </TouchableOpacity>
            <View/>
        )
    }

    savePromotionPlan = () => {
        console.log("=======savePromotionPlan");
        let toastOpts;
        if (!this.state.planTitle) {
            toastOpts = getFailToastOpts("请输入标题");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selBelongClassCode) {
            toastOpts = getFailToastOpts("请选择所属类别");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.planContent) {
            toastOpts = getFailToastOpts("请输入工作内容");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selCheckInUserId) {
            toastOpts = getFailToastOpts("请选择关闭人");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/promotion/plan/add";
        if (this.state.planId) {
            console.log("=========Edit===planId", this.state.planId)
            url = "/biz/promotion/plan/modify";
        }
        let requestParams = {
            planId: this.state.planId,
            planTitle: this.state.planTitle,
            belongClass: this.state.selBelongClassCode,
            planContent: this.state.planContent,
            plannedCompletionTime: this.state.plannedCompletionTime,
            "checkInUserId": this.state.selCheckInUserId,
        };
        httpPost(url, requestParams, this.savePromotionPlanCallBack);
    }

    //员工显示组件
    renderUserRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selCheckInUserId: item.userId,
                    selCheckInUserName: item.userName,
                })

            }}>
                <View key={item.userId} style={[item.userId === this.state.selCheckInUserId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.userId === this.state.selCheckInUserId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.userName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 保存回调函数
    savePromotionPlanCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }



    // 属性类别
    renderBelongClassRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selBelongClassCode: item.belongClassCode
                })
            }}>
                {/* <View key={item.belongClassCode} style={item.belongClassCode === this.state.selBelongClassCode ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.belongClassCode === this.state.selBelongClassCode ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.belongClassName}
                    </Text>
                </View> */}
                <View key={item.belongClassCode} style={[item.belongClassCode === this.state.selBelongClassCode ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.belongClassCode === this.state.selBelongClassCode ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.belongClassName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    openPlannedCompletionTime() {
        this.refs.SelectPlannedCompletionTime.showDate(this.state.selectedPlannedCompletionTime)
    }
    callBackSelectPlannedCompletionTimeValue(value) {
        console.log("==========计划完成时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedPlannedCompletionTime: value
        })
        if (value && value.length) {
            var plannedCompletionTime = "";
            var vartime;
            for (var index = 0; index < value.length; index++) {
                vartime = value[index];
                if (index === 0) {
                    plannedCompletionTime += vartime;
                }
                else {
                    plannedCompletionTime += "-" + vartime;
                }
            }
            this.setState({
                plannedCompletionTime: plannedCompletionTime
            })
        }
    }

    // 选择关闭人回调函数
    callBackCheckInUserFunction = (selUserId, selUserName) => {
        this.setState({
            selCheckInUserId: selUserId,
            selCheckInUserName: selUserName
        })
    }

    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '任务'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.formContentViewStyle, {marginBottom: 0}]}>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>标题</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <TextInput
                            style={[CommonStyle.inputRowText, {borderWidth: 0 }]}
                            placeholder={'请输入标题'}
                            onChangeText={(text) => this.setState({ planTitle: text })}
                        >
                            {this.state.planTitle}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>内容</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle, { height: 120 }]}>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText, { height: 120, borderWidth: 0 }]}
                            placeholder={'请输入内容'}
                            onChangeText={(text) => this.setState({ planContent: text })}
                        >
                            {this.state.planContent}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>所属类别</Text>
                        </View>
                    </View>
                    <View style={{ width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15 }}>
                        {
                            (this.state.belongClassDataSource && this.state.belongClassDataSource.length > 0)
                                ?
                                this.state.belongClassDataSource.map((item, index) => {
                                    return this.renderBelongClassRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>
                    <View style={[styles.inputLineViewStyle, {marginTop: 15}]}/>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>计划完成时间</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openPlannedCompletionTime()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, {borderWidth: 0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15,marginLeft:15 }}>
                                    {!this.state.plannedCompletionTime ? "请选择计划完成时间" : this.state.plannedCompletionTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    <View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>关闭人</Text>
                            </View>
                            <View style={[{ flexWrap: 'wrap' }, this.state.planId ? CommonStyle.disableViewStyle : null]}>
                                <TouchableOpacity onPress={() => {
                                    // if(this.state.planId){
                                    //    return ;
                                    // }
                                    this.props.navigation.navigate("PromotionPlanSelUser",
                                        {
                                            // 传递参数
                                            selCheckInUserId: this.state.selCheckInUserId,
                                            selCheckInUserName: this.state.selCheckInUserName,
                                            refresh: this.callBackCheckInUserFunction
                                        })
                                }}>
                                    <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 10), flexWrap: 'wrap', borderWidth: 0}]}>
                                        {
                                            this.state.selCheckInUserId && this.state.selCheckInUserName ?
                                                <Text style={{color: '#000000', fontSize: 15,marginLeft:15}}>{this.state.selCheckInUserName}</Text>
                                                :
                                                <Text style={[{ color: '#A0A0A0', fontSize: 15,marginLeft:15 }]}>请选择关闭人</Text>
                                        }
                                        <Image style={{ width: 22, height: 22, position:'absolute', right: 10, top: 11 }} source={require('../../assets/icon/iconfont/arrowRight.png')}></Image>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {/* <View style={[{ flexWrap: 'wrap' }, this.state.planId ? CommonStyle.disableViewStyle : null]}>
                                <TouchableOpacity onPress={() => {
                                     if(this.state.planId){
                                        return ;
                                     }

                                    if (this.state.userDataSource && this.state.userDataSource.length > 0) {
                                        this.setState({
                                            _userDataSource: copyArr(this.state.userDataSource)
                                        })
                                    }
                                    this.setState({
                                        userModal: true,
                                        userSearchKeyWord: ""  //部门的搜索关键字
                                    })
                                    if (!this.state.selCheckInUserId && this.state._userDataSource && this.state._userDataSource.length > 0) {
                                        this.setState({
                                            selCheckInUserId: this.state._userDataSource[0].userId,
                                            selCheckInUserName: this.state._userDataSource[0].userName,


                                        })
                                    }
                                }}>
                                    <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 10), flexWrap: 'wrap', borderWidth: 0}]}>
                                        {
                                            this.state.selCheckInUserId && this.state.selCheckInUserName ?
                                                <Text style={{color: '#000000', fontSize: 15}}>{this.state.selCheckInUserName}</Text>
                                                :
                                                <Text style={[{ color: '#A0A0A0', fontSize: 15 }]}>选择关闭人</Text>
                                        }
                                        <Image style={{ width: 22, height: 22, position:'absolute', right: 10, top: 11 }} source={require('../../assets/icon/iconfont/arrowRight.png')}></Image>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <Modal
                                animationType={'slide'}
                                transparent={true}
                                onRequestClose={() => console.log('onRequestClose...')}
                                visible={this.state.userModal}>
                                <View style={CommonStyle.fullScreenKeepOut}>
                                    <View style={CommonStyle.modalContentViewStyle}>
                                        <View style={CommonStyle.rowLabView}>
                                            <View style={styles.leftLabViewImage}>
                                                <Image style={{ width: 25, height: 25 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                                <TextInput
                                                    style={[styles.searchInputText]}
                                                    returnKeyType="search"
                                                    returnKeyLabel="搜索"
                                                    onSubmitEditing={e => {
                                                        this.loadUser();
                                                    }}
                                                    placeholder={'输入关闭人姓名'}
                                                    onChangeText={(text) => this.setState({ userSearchKeyWord: text })}
                                                >
                                                    {this.state.userSearchKeyWord}
                                                </TextInput>
                                            </View>
                                        </View>
                                        <ScrollView style={{}}>
                                            <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                                {
                                                    (this.state._userDataSource && this.state._userDataSource.length > 0)
                                                        ?
                                                        this.state._userDataSource.map((item, index) => {
                                                            if (index < 1000) {
                                                                return this.renderUserRow(item)
                                                            }
                                                        })
                                                        : <EmptyRowViewComponent />
                                                }
                                            </View>
                                        </ScrollView>
                                        <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    userModal: false,
                                                })
                                            }}>
                                                <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                                    <Image style={{ width: 25, height: 25, marginRight: 5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                                    <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                                </View>
                                            </TouchableOpacity>
                                            <TouchableOpacity onPress={() => {
                                                if (!this.state.selCheckInUserId) {
                                                    let toastOpts = getFailToastOpts("您还没有选择关闭人");
                                                    WToast.show(toastOpts);
                                                    return;
                                                }

                                                this.setState({
                                                    userModal: false,
                                                })
                                            }}>
                                                <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                                    <Image style={{ width: 30, height: 30, marginRight: 5 }} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                                    <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                                </View>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>
                            </Modal> */}
                        </View>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    {/* <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0, marginTop: 6}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginLeft: 20, width: (screenWidth - 56)/2}]} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.savePromotionPlan.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView, {marginRight: 20, width: (screenWidth - 56)/2}]}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>提交</Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                </ScrollView>

                <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginTop: 0, marginLeft: 20, width: (screenWidth - 56)/2}]} >
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.savePromotionPlan.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, {marginTop: 0, marginRight: 20, width: (screenWidth - 56)/2}]}>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>提交</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                <BottomScrollSelect
                    ref={'SelectPlannedCompletionTime'}
                    callBackDateValue={this.callBackSelectPlannedCompletionTimeValue.bind(this)}
                />
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },
    searchInputText: {
        width: screenWidth - 100,
        // borderColor: '#000000',
        // borderBottomWidth: 1,
        // marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        // marginLeft: 0,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop: 0
    },
    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabViewImage: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        borderWidth: 1,
        borderColor: "#E4E4E4",
        borderRadius: 5,
        marginTop: 5,
        // marginRight:5
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: '#FD6645',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputLineViewStyle: {
        height:1,
        marginLeft: 13,
        marginRight: 13,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
})