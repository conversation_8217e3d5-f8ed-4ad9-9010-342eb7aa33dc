import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CrHeadScreen from '../../component/CrHeadScreen'
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import ImageViewer from 'react-native-image-zoom-viewer';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class EnterprisecrHiringPositionDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            positionId:"",
            positionName:"",
            positionTreatment:"",
            positionDescribe:"",
            positionTypeName:"",
            workingPlace:"",
            positionRequirements:"",
            applyState:"",
            enterpriseName:"",
            enterpriseArea:"",
            enterpriseLogo:"",
            hiringNumber:"",
            enterpriseId:"",
            enterpriseIntroduction:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        console.log("1",route.params)
        if (route && route.params) {
            const { positionId,enterpriseId} = route.params;
            if (positionId && enterpriseId) {
                console.log("positionId +++++ enterpriseId",positionId,enterpriseId)
                this.setState({
                    positionId:positionId,
                    enterpriseId:enterpriseId
                })
                let loadTypeUrl= "/biz/hiring/position/get";
                let loadRequest={'positionId':positionId};
                httpPost(loadTypeUrl, loadRequest, this.loadHiringPositionDataCallBack);
                 loadTypeUrl= "/biz/enterprise/get";
                 loadRequest={'enterpriseId':enterpriseId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditEnterpriseDataCallBack);
            }
        }
    }

    loadHiringPositionDataCallBack = (response) => {
        if (response.code == 200 && response.data) {

            this.setState({
                positionName:response.data.positionName,
                positionTreatment:response.data.positionTreatment,
                positionDescribe:response.data.positionDescribe,
                positionTypeName:response.data.positionTypeName,
                workingPlace:response.data.workingPlace,
                positionRequirements:response.data.positionRequirements,
                applyState:response.data.applyPosition,
                enterpriseName:response.data.enterpriseName,
                enterpriseArea:response.data.enterpriseArea,
                enterpriseLogo:response.data.enterpriseLogo,
                hiringNumber:response.data.hiringNumber,
            })
        }
    }
    loadEditEnterpriseDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                enterpriseIntroduction:response.data.enterpriseIntroduction,
                enterpriseLogo:response.data.enterpriseLogo
            })
        }
    }
    // applyInterview=()=>{
    //     let url = "/biz/collection/position/add";
    //     let loadRequest = {
    //         "staffId":constants.loginUser.staffId,
    //         "positionId":this.state.positionId,
    //         "applyPosition":this.state.applyState == 'Y'?"N":"Y"
    //     };
    //     httpPost(url, loadRequest, this.applyInterviewCallBack);
    // }

    // applyInterviewCallBack=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         WToast.show({ data: response.data.applyPosition == 'Y'?"申请面试成功":"取消申请成功" });
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:24, height:24}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    render(){
        return(

            <View>
                <CommonHeadScreen title='岗位详情'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle]}>企业名称：{this.state.enterpriseName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle]}>企业log：</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <View style={{borderStyle:'dashed',justifyContent:'center',alignItems:'center'}}>
                        <View>
                            {
                                this.state.enterpriseLogo ?
                                <Image source={{ uri: (constants.image_addr + '/' + this.state.enterpriseLogo) }} style={{width:75,height:100,justifyContent:'center',alignItems:'center'}} />                                                    
                                :
                                <Image source ={require('../../assets/image/enterprise.png')} style ={{width:75,height:100,justifyContent:'center',alignItems:'center'}}></Image>
                            }  
                        </View>
                    </View>                
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle]}>企业简介</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{this.state.enterpriseIntroduction?this.state.enterpriseIntroduction: "暂无信息"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle]}>岗位名称：{this.state.positionName}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{this.state.positionName}</Text>
                </View> */}
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle]}>岗位类型：{this.state.positionTypeName}</Text>
                </View>
  
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle]}>工作地点：{this.state.workingPlace ? this.state.workingPlace : "暂无信息"}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{this.state.positionTypeName}</Text>
                </View> */}
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,{fontWeight:'bold'}]}>薪资待遇</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{this.state.positionTreatment ? this.state.positionTreatment : "暂无信息"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,{fontWeight:'bold'}]}>岗位介绍</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{this.state.positionDescribe ? this.state.positionDescribe : "暂无信息"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,{fontWeight:'bold'}]}>任职要求</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{this.state.positionRequirements ? this.state.positionRequirements : "暂无信息"}</Text>
                </View>

             
                
             
                {/* {
                    constants.loginUser.staffId != null ?
                    <View style={CommonStyle.itemBottomBtnStyle}>
                        <TouchableOpacity onPress={()=>{this.applyInterview()}}>
                            <View style={[this.state.applyState == 'Y'? [CommonStyle.itemBottomEditBtnViewStyle,{backgroundColor:'#F2C16D'}]:[CommonStyle.itemBottomEditBtnViewStyle],{width:85}]}>
                                <Text style={this.state.applyState == 'Y'? CommonStyle.itemBottomEditBtnTextStyle :CommonStyle.itemBottomDetailBtnTextStyle}>{this.state.applyState == 'Y'?"取消申请":"申请面试"}</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    :
                    <View/>
                } */}
                </ScrollView>
            </View>

            // <View>
            //     <CrHeadScreen 
            //         title=''
            //         leftItem={() => this.renderLeftItem()}
            //         rightItem={() => this.renderRightItem()}
            //     />
            //     <ScrollView style={[CommonStyle.contentViewStyle]}>
            //     <View style={styles.titleViewStyle}>
            //         <Text style={[styles.titleTextStyle],{fontSize:21,color:'#333333',marginTop:10}}>{this.state.positionName}</Text>
            //     </View>

            //     <View style={styles.titleViewStyle}>
            //         <Text style={[styles.titleTextStyle],{color:'#7F7F7F', fontSize:14}}>{this.state.positionTypeName}  {this.state.enterpriseArea}</Text>
            //     </View>

            //     <View style={[styles.itemContentViewStyle,{  }]}>
            //         <View style={[styles.itemContentLeftChildViewStyle,{width:screenWidth - 320}]}>
            //         <Image style={styles.enterpriseLogoStyle} source={{uri:this.state.enterpriseLogo}}></Image>
            //         </View>
            //         <View style={styles.itemContentRightChildViewStyle,{marginLeft:0,marginTop:15,width:320}}>
            //             <Text style={styles.itemContentChildTextStyle,{color:'#333333', fontSize:18}}>{this.state.enterpriseName}</Text>
            //             <Text style={styles.itemContentChildTextStyle,{color:'#7F7F7F', fontSize:14,marginTop:12}}>招{this.state.hiringNumber}人</Text>
            //         </View>
            //     </View>

            //     {/* <View style={styles.titleViewStyle}>
            //         <Text style={[styles.titleTextStyle]}>岗位名称：{this.state.positionName}</Text>
            //     </View> */}
            //     {/* <View style={styles.titleViewStyle}>
            //         <Text style={styles.titleTextStyle}>{this.state.positionName}</Text>
            //     </View> */}
                
  
                
            //     {/* <View style={styles.titleViewStyle}>
            //         <Text style={styles.titleTextStyle}>{this.state.positionTypeName}</Text>
            //     </View> */}
            //     <View style={styles.titleViewStyle}>
            //         <Text style={[styles.titleTextStyle,{fontSize:18,marginTop:15,color:'#333333'}]}>福利待遇</Text>
            //     </View>
            //     <View style={styles.titleViewStyle}>
            //         <Text style={styles.titleTextStyle,{lineHeight:30,fontSize:14}}>{this.state.positionTreatment ? this.state.positionTreatment : "暂无信息"}</Text>
            //     </View>
            //     <View style={styles.titleViewStyle}>
            //         <Text style={[styles.titleTextStyle,{fontSize:18,color:'#333333'}]}>岗位介绍</Text>
            //     </View>
            //     <View style={styles.titleViewStyle}>
            //         <Text style={styles.titleTextStyle,{lineHeight:30}}>{this.state.positionDescribe ? this.state.positionDescribe : "暂无信息"}</Text>
            //     </View>
            //     <View style={styles.titleViewStyle}>
            //         <Text style={[styles.titleTextStyle,{fontSize:18,color:'#333333'}]}>任职要求</Text>
            //     </View>
            //     <View style={styles.titleViewStyle}>
            //         <Text style={styles.titleTextStyle,{lineHeight:30}}>{this.state.positionRequirements ? this.state.positionRequirements : "暂无信息"}</Text>
            //     </View>
            //     <View style={styles.titleViewStyle}>
            //         <Text style={[styles.titleTextStyle,{fontSize:18,color:'#333333'}]}>工作地点</Text>
            //     </View>
            //     <View style={styles.titleViewStyle}>
            //         <Text style={styles.titleTextStyle,{lineHeight:30}}>{this.state.workingPlace ? this.state.workingPlace : "暂无信息"}</Text>
            //     </View>
            //     {/* {
            //         constants.loginUser.staffId != null ?
            //         <View style={CommonStyle.itemBottomBtnStyle}>
            //             <TouchableOpacity onPress={()=>{this.applyInterview()}}>
            //                 <View style={[this.state.applyState == 'Y'? [CommonStyle.itemBottomEditBtnViewStyle,{backgroundColor:'#F2C16D'}]:[CommonStyle.itemBottomEditBtnViewStyle],{width:85}]}>
            //                     <Text style={this.state.applyState == 'Y'? CommonStyle.itemBottomEditBtnTextStyle :CommonStyle.itemBottomDetailBtnTextStyle}>{this.state.applyState == 'Y'?"取消申请":"申请面试"}</Text>
            //                 </View>
            //             </TouchableOpacity>
            //         </View>
            //         :
            //         <View/>
            //     } */}
            //     </ScrollView>
            // </View>
        )
    }
}
const styles = StyleSheet.create({
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', width:100, alignItems:'center', alignContent:'flex-end', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginTop:5,
        marginBottom:5,
    },
    innerViewStyle:{
        marginTop:0,
    },
    photos:{
        width:150,
        height:200,
        // borderRadius:50,
        borderWidth:0,
        // marginTop:80,
        // marginBottom:30
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:screenWidth - 120,
    },
    itemContentRightChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:120,
    },
    enterpriseLogoStyle:{
        borderRadius:10,
        width:65,
        height:65,
        marginTop:15
    }
});