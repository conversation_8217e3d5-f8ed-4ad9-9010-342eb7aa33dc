import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Image
} from 'react-native';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
import { WebView } from 'react-native-webview';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class ViewDocument extends Component {
    constructor(){
        super();
        this.state = {
            title: "预览",
            documentId: null
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let toastOpts = requiredReminder('正在加载中，请稍候...');
        WToast.show(toastOpts)
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { documentId,documentName } = route.params;
            if (documentId) {
                console.log("========view==documentId:", documentId);
                this.setState({
                    documentId: documentId
                })
            }
            if (documentName) {
                console.log("========view==documentName:", documentName);
                this.setState({
                    title:documentName
                })
            }
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

  render() {
    return (
        <View>
            <CommonHeadScreen title={this.state.title}
                leftItem={() => this.renderLeftItem()}
                rightItem={() => this.renderRightItem()}
            />
            <View style={CommonStyle.contentViewStyle}>
                <WebView 
                    source={{uri:constants.service_addr + '/biz/view/document?fileId=' + this.state.documentId}}
                    scalesPageToFit={true}
                    style={{width:'100%',height:'100%'}} 
                    
                />
            </View>
        </View>
    )
  }
}
