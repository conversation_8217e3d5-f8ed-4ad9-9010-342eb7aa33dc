import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, Alert, TouchableOpacity, Dimensions, Image, KeyboardAvoidingView } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
var screenHeight = Dimensions.get('window').height;

export default class InformationConfigAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate: "",
            currentPage: 1,
            advertisingId: "",
            advertisingTitle: "",
            advertisingImage: "",
            advertisingContent: "",
            advertisingState: "",
            advertisingLink: "",
            advertisingType: "",
            advertisingTypeDataSource: [],
        }
    }


    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { advertisingId } = route.params;
            if (advertisingId) {
                console.log("=============advertisingId" + advertisingId + "");
                this.setState({
                    advertisingId: advertisingId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/portal/advertising/get";
                loadRequest = { 'advertisingId': advertisingId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditInformationDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
        }
        let advertisingTypeDataSource = [
            {
                typeCode: 'D',
                typeName: '极致学社首页轮播图',
            },
            {
                typeCode: 'N',
                typeName: '极致学社首页资讯',
            },

        ]
        this.setState({
            advertisingTypeDataSource: advertisingTypeDataSource,

        })
    }


    loadEditInformationDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                advertisingTitle: response.data.advertisingTitle,
                advertisingImage: response.data.advertisingImage,
                advertisingContent: response.data.advertisingContent,
                advertisingLink: response.data.advertisingLink,
                advertisingType: response.data.advertisingType,
            })
        }
    }

    //资讯类型单项渲染
    renderAdvertisingTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    advertisingType: item.typeCode
                })
                console.log("======advertisingTypeChoose==", this.state.advertisingType, ",", item.typeName, ",", item.typeCode)
            }}>
                <View key={item.typeCode} style={item.typeCode === this.state.advertisingType ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.typeCode === this.state.advertisingType ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.typeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("InformationConfigList",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Text style={CommonStyle.headRightText}>资讯配置</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    saveInformation = () => {
        console.log("=======saveInformation");
        let toastOpts;
        if (!this.state.advertisingTitle) {
            toastOpts = getFailToastOpts("请填写资讯标题");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.advertisingImage) {
        //     toastOpts = getFailToastOpts("请上传图片");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url = "/biz/portal/advertising/add";
        if (this.state.advertisingId) {
            console.log("=========Edit===advertisingId", this.state.advertisingId)
            url = "/biz/portal/advertising/modify";
        }
        let requestParams = {
            advertisingId: this.state.advertisingId,
            advertisingTitle: this.state.advertisingTitle,
            advertisingImage: this.state.advertisingImage,
            advertisingContent: this.state.advertisingContent,
            advertisingLink: this.state.advertisingLink,
            advertisingType: this.state.advertisingType,
        };
        httpPost(url, requestParams, this.saveInformationCallBack);
    }

    // 保存回调函数
    saveInformationCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }


    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '资讯'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.formContentViewStyle]}>
                    <View>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>上传资讯图片</Text>
                        </View>
                        <View style={[{
                            width: 120, height: 150, marginLeft: 10, marginBottom: 10, display: 'flex', justifyContent: 'center',
                            alignItems: 'center'
                        }, { borderColor: '#AAAAAA', borderWidth: 1, borderStyle: 'dashed', borderRadius: 5 }]}>
                            <TouchableOpacity
                                style={{ width: 120, height: 150, alignItems: 'center', justifyContent: 'center' }}
                                onPress={() => {
                                    uploadImageLibrary(this.state.advertisingImageUrl, "user_header", (imageUploadResponse) => {
                                        console.log("========imageUploadResponse", imageUploadResponse)
                                        if (imageUploadResponse.code === 200) {
                                            WToast.show({ data: "成功上传" });
                                            let { compressFile } = imageUploadResponse.data
                                            this.setState({
                                                advertisingImage: compressFile,
                                            })
                                        }
                                        else {
                                            WToast.show({ data: imageUploadResponse.message });
                                        }
                                    });
                                }}
                            >
                                {
                                    this.state.advertisingImage ?
                                        <Image source={{ uri: (constants.image_addr + '/' + this.state.advertisingImage) }} style={{ width: 120, height: 150, justifyContent: 'center', alignItems: 'center' }} />
                                        :
                                        <Image source={require('../../assets/icon/iconfont/addPhoto.png')} style={{ width: 24, height: 24 }}></Image>
                                }
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>资讯主标题</Text>
                        </View>

                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <TextInput
                            style={[CommonStyle.inputRowText]}
                            placeholder={'请输入资讯主标题'}
                            onChangeText={(text) => this.setState({ advertisingTitle: text })}
                        >
                            {this.state.advertisingTitle}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>资讯副标题</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle, { height: 100 }]}>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText, { height: 100 }]}
                            placeholder={'请输入资讯副标题'}
                            onChangeText={(text) => this.setState({ advertisingContent: text })}
                        >
                            {this.state.advertisingContent}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>资讯类型</Text>
                        </View>
                    </View>
                    <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.advertisingTypeDataSource && this.state.advertisingTypeDataSource.length > 0)
                                ?
                                this.state.advertisingTypeDataSource.map((item, index) => {
                                    return this.renderAdvertisingTypeRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>资讯链接</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle, { height: 100 }]}>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText, { height: 100 }]}
                            placeholder={'请输入资讯链接'}
                            onChangeText={(text) => this.setState({ advertisingLink: text })}
                        >
                            {this.state.advertisingLink}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveInformation.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },
    holdbtnView: {
        fontSize: 16, width: 60, height: 30,
        borderWidth: 1,
        borderColor: 'rgba(30, 110, 250, 1)',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 5,
        borderRadius: 4,
        flexDirection: 'row'
    },
    holdBtnText: {
        color: 'rgba(83, 106, 247, 1)', fontSize: 16
    },

    // inputRowText:{
    //     width:screenWidth - 10,
    //     borderRadius:5,
    //     borderColor:'#F1F1F1',
    //     borderWidth:1,
    //     marginRight:5,
    //     marginLeft:5,
    //     color:'#A0A0A0',
    //     fontSize:15,
    //     paddingLeft:10,
    //     paddingRight:10
    // }
})