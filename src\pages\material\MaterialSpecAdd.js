import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class MaterialSpecAdd extends Component {
    constructor(){
        super()
        this.state = {
            classifyId: "",
            classifyName: "",
            classifyType: "",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { classifyId,classifyType } = route.params;
            if (classifyId) {
                console.log("========Edit==classifyId:", classifyId);
                this.setState({
                    classifyId:classifyId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/material/classify/get";
                loadRequest={'classifyId':classifyId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditClassifyDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
            if (classifyType) {
                this.setState({
                    classifyType:classifyType,
                })
            }
        }
    }
    loadEditClassifyDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                classifyId:response.data.classifyId,
                classifyName: response.data.classifyName,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("MaterialSpecList")
            }}>
                <Text style={CommonStyle.headRightText}>原料规格</Text>
            </TouchableOpacity>
        )
    }

    saveClassify =()=> {
        console.log("=======saveClassify");
        let toastOpts;
        if (!this.state.classifyName) {
            toastOpts = getFailToastOpts("请输入规格名称");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/material/classify/add";
        if (this.state.classifyId) {
            console.log("=========Edit===classifyId", this.state.classifyId)
            url= "/biz/material/classify/modify";
        }
        let requestParams={
            classifyId:this.state.classifyId,
            classifyName: this.state.classifyName,
            classifyType: this.state.classifyType,
        };
        httpPost(url, requestParams, this.saveBigClassCallBack);
    }
    
    // 保存回调函数
    saveBigClassCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '规格'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>原料规格</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入原料规格'}
                            onChangeText={(text) => this.setState({classifyName:text})}
                        >
                            {this.state.classifyName}
                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveClassify.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})