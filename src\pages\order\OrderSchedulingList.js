import React, {Component} from 'react';
import {
  Alert,
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';

// 公共组件及样式
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';

// 引入公共样式
// import CommonStyle from '../../assets/css/CommonStyle';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

class OrderSchedulingList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      searchKeyWord: '',
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      // productionLineId:""
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    this.loadOrderList();
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/order/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      excludeOrderState: 'A',
      qry_scheduling_order_list: 'Y',
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/order/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      excludeOrderState: 'A',
      qry_scheduling_order_list: 'Y',
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadOrderList();
  };

  loadOrderList = () => {
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      excludeOrderState: 'A',
      qry_scheduling_order_list: 'Y',
      searchKeyWord: this.state.searchKeyWord,
    };
    let url = '/biz/order/list';
    httpPost(url, loadRequest, this.callBackLoadOrder);
  };

  callBackLoadOrder = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteOrder = (orderId) => {
    console.log('=======delete=orderId', orderId);
    let url = '/biz/order/delete';
    let requestParams = {orderId: orderId};
    httpPost(url, requestParams, this.callBackDeleteOrder);
  };
  callBackDeleteOrder = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '成功删除'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };
  searchByKeyWord = () => {
    let toastOpts;
    if (!this.state.searchKeyWord) {
      toastOpts = getFailToastOpts('请输入客户、合同或订单名称');
      WToast.show(toastOpts);
      return;
    }
    let loadUrl = '/biz/order/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      excludeOrderState: 'A',
      qry_scheduling_order_list: 'Y',
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  // _loadFreshDataCallBack=(response)=>{
  //     if (response.code == 200 && response.data && response.data.dataList) {
  //         // var dataNew = response.data.dataList;
  //         // dataOld.unshift(dataNew);
  //         // var dataAll = [...dataNew];
  //         console.log("==========执行了查询的回调函数==============");
  //         this.setState({
  //             dataSource:response.data.dataList,
  //             // currentPage:response.data.currentPage + 1,
  //             // totalPage:response.data.totalPage,
  //             // totalRecord:response.data.totalRecord,
  //             // refreshing:false,
  //         })
  //     }
  //     else if (response.code == 401) {
  //         WToast.show({data:response.message});
  //         this.props.navigation.navigate("LoginView");
  //     }
  // }

  renderRow = (orderSchedulingItem) => {
    return (
      <View key={orderSchedulingItem.orderId} style={styles.innerViewStyle}>
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{orderSchedulingItem.orderName}</Text>
                </View> */}
        <View style={styles.bodyViewStyle}>
          <Text style={CommonStyle.bodyTextStyle}>
            订单名称：{orderSchedulingItem.orderName}
          </Text>
        </View>
        {/* <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>订单号：{orderSchedulingItem.orderCode}</Text>
                </View> */}
        {/* <View style={styles.bodyViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={CommonStyle.bodyTextStyle}>客户名称：{orderSchedulingItem.customerName}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={CommonStyle.bodyTextStyle}>排产时间：{orderSchedulingItem.schedulingTime}</Text>
                    </View>
                </View> */}
        {/* <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>所属合同：{orderSchedulingItem.contractName}</Text>
                </View> */}
        <View style={styles.bodyViewStyle}>
          <Text style={CommonStyle.bodyTextStyle}>
            客户名称：{orderSchedulingItem.customerName}
          </Text>
        </View>
        <View style={styles.bodyViewStyle}>
          <Text style={CommonStyle.bodyTextStyle}>
            排产时间：{orderSchedulingItem.schedulingTime}
          </Text>
        </View>

        {/* <View style={styles.bodyViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={CommonStyle.bodyTextStyle}>砖型：{orderSchedulingItem.brickTypeName}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={CommonStyle.bodyTextStyle}>数量(吨/块)：{orderSchedulingItem.brickAmount}</Text>
                    </View>
                </View> */}

        {/* <View style={styles.bodyViewStyle}>
                    <View style={styles.bodyRowLeftView} >
                        <Text style={CommonStyle.bodyTextStyle}>生产时间：{orderSchedulingItem.schedulingProductionTime}</Text>
                    </View>
                    <View style={styles.bodyRowRightView}>
                        <Text style={CommonStyle.bodyTextStyle}>预计完成时间：{orderSchedulingItem.schedulingCompletedTime}</Text>
                    </View>
                </View> */}

        <View style={styles.bodyViewStyle}>
          <Text style={CommonStyle.bodyTextStyle}>
            预计生产时间：{orderSchedulingItem.schedulingProductionTime}
          </Text>
        </View>
        <View style={styles.bodyViewStyle}>
          <Text style={CommonStyle.bodyTextStyle}>
            预计完成时间：{orderSchedulingItem.schedulingCompletedTime}
          </Text>
        </View>

        <View style={CommonStyle.itemBottomBtnStyle}>
          <TouchableOpacity
            onPress={() => {
              Alert.alert('确认', '您确定要删除该订单吗？', [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                    // this在这里可用，传到方法里还有问题
                    // this.props.navigation.goBack();
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.deleteOrder(orderSchedulingItem.orderId);
                  },
                },
              ]);
              // this.deleteOrder(orderSchedulingItem.orderId)
            }}>
            {/* <View style={CommonStyle.itemBottomDeleteBtnViewStyle}>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View> */}
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              // if (orderSchedulingItem.orderState!="B") {
              //     // 只有在排产，未开始生产的状态下才可以编辑
              //     return;
              // }
              if (
                dateDiffHours(
                  constants.nowDateTime,
                  orderSchedulingItem.schedulingTime,
                ) > constants.editDeleteTimeLimit
              ) {
                return;
              }
              // WToast.show({data:'排产的订单不能编译'});
              // console.log("==========" + orderSchedulingItem.contractName + "======");
              this.props.navigation.navigate('OrderScheduling', {
                productionLineId: orderSchedulingItem.productionLineId,
                orderId: orderSchedulingItem.orderId,
                customerName: orderSchedulingItem.customerName,
                contractName: orderSchedulingItem.contractName,
                // 传递回调函数
                refresh: this.callBackFunction,
              });
            }}>
            <View
              style={[
                CommonStyle.itemBottomEditBtnViewStyle,
                {width: 80, flexDirection: 'row'},
                dateDiffHours(
                  constants.nowDateTime,
                  orderSchedulingItem.schedulingTime,
                ) > constants.editDeleteTimeLimit
                  ? CommonStyle.disableViewStyle
                  : '',
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 5}}
                source={require('../../assets/icon/iconfont/edit.png')}></Image>
              <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 分隔线
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/backnew.png')}></Image>
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    console.log('========LIST=>ADD');
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('OrderScheduling', {
            // 传递回调函数
            // productionLineId:orderSchedulingItem.productionLineId,
            refresh: this.callBackFunction(),
          });
        }}>
        {/* <Text style={CommonStyle.headRightText}>新增排产</Text> */}
        <Image
          style={{width: 27, height: 27}}
          source={require('../../assets/icon/iconfont/add.png')}></Image>
      </TouchableOpacity>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="排产管理"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.headViewStyle,
            {borderLeftWidth: 0, borderRightWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View style={CommonStyle.singleSearchBox}>
            <View style={CommonStyle.searchBoxWithoutOthers}>
              {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
              <Image
                style={{width: 16, height: 16, marginLeft: 7}}
                source={require('../../assets/icon/iconfont/search.png')}></Image>
              <TextInput
                style={{
                  color: 'rgba(rgba(0, 10, 32, 0.45))',
                  fontSize: 14,
                  marginLeft: 5,
                  paddingTop: 0,
                  paddingBottom: 0,
                  paddingRight: 0,
                  paddingLeft: 0,
                  width: '100%',
                }}
                returnKeyType="search"
                returnKeyLabel="搜索"
                onSubmitEditing={(e) => {
                  this.searchByKeyWord();
                }}
                placeholder={'客户/合同/订单'}
                onChangeText={(text) => this.setState({searchKeyWord: text})}>
                {this.state.searchKeyWord}
              </TextInput>
            </View>
          </View>
        </View>

        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.orderId}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 140,
  //     backgroundColor:'#FFF'
  // },
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
  },

  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
    paddingLeft: 0,
  },
  searchInputText: {
    width: screenWidth - 100,
    borderColor: '#000000',
    // borderBottomWidth:1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },

  innerViewStyle: {
    // marginTop:10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
  },
  titleTextStyle: {
    fontSize: 23,
  },
  bodyViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 8,
    marginTop: 8,
  },
  bodyRowLeftView: {
    width: screenWidth / 2 - 40,
    flexDirection: 'row',
  },
  bodyRowRightView: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingLeft: 10,
    marginRight: 5,
    justifyContent: 'flex-start',
    alignContent: 'flex-start',
  },
});
module.exports = OrderSchedulingList;
