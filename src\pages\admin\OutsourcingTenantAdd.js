import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList,Image, TouchableOpacity, Dimensions } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class OutsourcingTenantAdd extends Component {
    constructor() {
        super()
        this.state = {
            outsourcingId:"",
            operateTenantId: "",
            outsourcingName: "",
            outsourcingTenantId:"",
            tenantList:[],
            operate: "",
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const {outsourcingId, outsourcingTenantId, operateTenantId } = route.params;
            if(operateTenantId){
                console.log("========Edit==operateTenantId:", operateTenantId);
                this.setState({
                    operateTenantId: operateTenantId
                })
            }
            if(outsourcingTenantId){
                this.setState({
                    outsourcingTenantId: outsourcingTenantId
                })
            }
            if (outsourcingId) {
                console.log("========Edit==outsourcingId:", operateTenantId);
                this.setState({
                    outsourcingId:outsourcingId,
                    operate: "编辑"
                })
                loadTypeUrl= "/biz/tenant/outsourcing/get";
                loadRequest={'operateTenantId':operateTenantId};
                httpPost(loadTypeUrl, loadRequest, this.loadOutsourcingTenantCallBack);

            }
            else {
                this.setState({
                    operate: "新增",
                })
            }
        }
        this.loadOutsourcingTenantList();
    }

    loadOutsourcingTenantList(){
        let loadTypeUrl;
        let loadRequest;
        loadTypeUrl = "/biz/tenant/list";
        loadRequest={
            "currentPage":1,
            "pageSize":100
        }
        httpPost(loadTypeUrl, loadRequest, this.loadOutsourcingTenantListCallBack);
    }

    loadOutsourcingTenantListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.tenantList;
            var dataAll = [...dataOld,...dataNew];
            console.log(dataAll)
            this.setState({
                tenantList:dataAll,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadOutsourcingTenantCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                outsourcingName:response.data.outsourcingName,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("OutsourcingTenantList")
            }}>
                <Text style={CommonStyle.headRightText}>外协管理</Text>
            </TouchableOpacity>
        )
    }

    saveOutsourcing = () => {
        console.log("=======saveOutsourcing");
        let toastOpts;
        if (!this.state.outsourcingName) {
            toastOpts = getFailToastOpts("请输入外协单位");
            WToast.show(toastOpts)
            return;
        }

        let url = "/biz/tenant/outsourcing/add";
        if (this.state.outsourcingId) {
            console.log("=========Edit===outsourcingId", this.state.outsourcingId)
            url = "/biz/tenant/outsourcing/modify";
        }
        let requestParams = {
            // outsourcingTenantId: this.state.outsourcingName,
            // outsourcingName: this.state.outsourcingName,
            operateTenantId:this.state.operateTenantId
        };
        httpPost(url, requestParams, this.saveOutsourcing_call_back);

    }

    // 保存回调函数
    saveOutsourcing_call_back = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }


    render() {
        return (
            <View>
                <CommonHeadScreen title={this.state.operate}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                
                <ScrollView style={CommonStyle.contentViewStyle}>
                <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.95, justifyContent:'flex-start'}]}>
                    {this.state.tenantList.map((item, key)=>{
                        return(
                            <TouchableOpacity onPress={()=>{
                                // var selMenuIdList = this.state.selMenuIdList;
                                if (item.selected && item.selected == "Y") {
                                    item.selected = "N";
                                    // arrayRemoveItem(selMenuIdList, item.menuId);
                                    // arrayRemoveItem(selMenuIdList, item.menuParentId);
                                }
                                else {
                                    item.selected = "Y";
                                    // selMenuIdList = selMenuIdList.concat(item.menuId).concat(item.menuParentId)
                                }
                                // this.setState({
                                //     selMenuIdList:selMenuIdList,
                                // })
                                WToast.show({data:'点击了' + item.tenantName});
                                // console.log("======selMenuIdList:", selMenuIdList)
                                // console.log("======oldselMenuIdList:", this.state.oldselMenuIdList)
                            }}>
                                <View style={[{margin:10, borderRadius:4, padding:10, height:40, backgroundColor:'#F5F5F5'}, (item.selected && item.selected === 'Y') ? {backgroundColor:'red'} : ""]}>
                                    <Text style={[styles.titleTextStyle, (item.selected && item.selected === 'Y') ? {color:'#FFFFFF'} : {color:'#000000'}]}>{item.tenantName}</Text>
                                </View>
                            </TouchableOpacity>
                            
                        )
                    })}
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveOutsourcing.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                
            </View>
        )
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
})