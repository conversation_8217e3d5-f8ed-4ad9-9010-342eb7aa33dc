import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,TextInput,Image,KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';

import BottomScrollSelect from '../../component/BottomScrollSelect';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;

export default class CustomerLeadAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            leadId:"",
            customerName:"",
            contactPerson:"",
            contactTel:"",
            jobTitle:"",
            customerSummary:"",
            remark:"",
            leadSort:"",
            leadState:"",
            userId:"",
            auditFlag:"",
            gmtCreated:"",
            gmtModified:"",
            version:"",
            tenantId:"",
            operate:"",
            selectedCustomerLeadDate:[],
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { leadId } = route.params;
            if (leadId) {
                console.log("=============leadId" + leadId + "");
                this.setState({
                    leadId:leadId,
                    operate:"编辑"
                })
                loadTypeUrl = "/biz/customer/lead/get";
                loadRequest = { 'leadId': leadId };
                httpPost(loadTypeUrl, loadRequest, this.loadCustomerLeadCallBack);
            }
            else {
                this.setState({
                    operate:"新增",
                    userId: constants.loginUser.userId
                })
                // // 当前时间
                // var currentDate = new Date();
                // var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                // var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                // this.setState({
                //     selectedCustomerLeadDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                //     gmtCreated:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDayastOfMonth + "-" + SevenDaysLastOfDay
                // })
            }
        }
    }

    loadCustomerLeadCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                leadId: response.data.leadId,
                customerName: response.data.customerName,
                contactPerson: response.data.contactPerson,
                jobTitle: response.data.jobTitle,
                contactTel: response.data.contactTel,
                customerSummary:response.data.customerSummary,
                remark: response.data.remark,
                userId: response.data.userId,
            })
        }
    }          

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("CustomerLeadList")
            }}>
                <Text style={CommonStyle.headRightText}>客户线索</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    saveCustomerLead = () => {
        console.log("=======saveCustomerLead");
        let toastOpts;
        if (!this.state.customerName) {
            toastOpts = getFailToastOpts("请输入客户名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contactPerson) {
            toastOpts = getFailToastOpts("请输入联系人名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contactTel) {
            toastOpts = getFailToastOpts("请输入联系人电话");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.jobTitle) {
            toastOpts = getFailToastOpts("请输入职务");
            WToast.show(toastOpts)
            return;
        }
        
        let url = "/biz/customer/lead/add";
        if (this.state.leadId) {
            console.log("=========Edit===leadId", this.state.leadId)
            url = "/biz/customer/lead/modify";
        }
        let requestParams = {
            leadId: this.state.leadId,
            customerName: this.state.customerName,
            contactPerson: this.state.contactPerson,
            jobTitle: this.state.jobTitle,
            contactTel: this.state.contactTel,
            customerSummary: this.state.customerSummary,
            remark: this.state.remark,
            userId: this.state.userId,
        };
        httpPost(url, requestParams, this.saveCustomerLeadCallBack);
    }

    // 保存回调函数
    saveCustomerLeadCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // openCustomerLeadDate(){
    //     this.refs.SelectCustomerLeadDate.showDate(this.state.selectedCustomerLeadDate)
    // }
    
    // callBackSelectCustomerLeadDateValue(value){
    //     console.log("==========提交时间选择结果：", value)
    //     if (!value) {
    //         return;
    //     }
    //     this.setState({
    //         selectedCustomerLeadDate:value
    //     })
    //     if (value && value.length) {
    //         var gmtCreated = "";
    //         var vartime;
    //         for(var index=0;index<value.length;index++) {
    //             vartime = value[index];
    //             if (index===0) {
    //                 gmtCreated += vartime;
    //             }
    //             else{
    //                 gmtCreated += "-" + vartime;
    //             }
    //         }
    //         this.setState({
    //             gmtCreated:gmtCreated
    //         })
    //     }
    // }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]}  behavior="padding">
                <CommonHeadScreen title={this.state.operate + '线索'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>客户名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入客户名称'}
                            onChangeText={(text) => this.setState({ customerName: text })}
                        >
                            {this.state.customerName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入联系人'}
                            onChangeText={(text) => this.setState({ contactPerson: text })}
                        >
                            {this.state.contactPerson}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入联系电话'}
                            onChangeText={(text) => this.setState({ contactTel: text })}
                        >
                            {this.state.contactTel}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>职务</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入职务'}
                            onChangeText={(text) => this.setState({ jobTitle: text })}
                        >
                            {this.state.jobTitle}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>客户简介</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:150}]}
                            placeholder={'请输入客户简介'}
                            onChangeText={(text) => this.setState({customerSummary:text})}
                        >
                            {this.state.customerSummary}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>备注说明</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                            placeholder={'请输入备注说明'}
                            onChangeText={(text) => this.setState({remark:text})}
                        >
                            {this.state.remark}
                        </TextInput>
                    </View>

                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveCustomerLead.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row', width: 130, height: 40, marginRight: 35, marginTop: 15 }]}>
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                {/* <BottomScrollSelect 
                    ref={'SelectCustomerLeadDate'} 
                    callBackDateValue={this.callBackSelectCustomerLeadDateValue.bind(this)}
                /> */}
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
});