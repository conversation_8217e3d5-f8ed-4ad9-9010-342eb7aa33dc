jznc-v1.0
https://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/ipa/sp_mobile_front.ipa
https://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/ipa/logo_gaitubao_57x57.png
https://lmz-beijing.oss-cn-beijing.aliyuncs.com/liminshan/ipa/logo_gaitubao_512x512.png
=========================================
AAA
maxHeight:500 - 样式可用

2021.07.23
合同管理-完成
1、合同编号可输入
2、加一个交付日期字段

订单管理
1、加一个部位->完成
2、加个生产单位字段
3、编辑、删除、外厂订单显示控制

排产管理
1、生产单位根据订单关连出来，只是显示
2、机台管理做关联，加一个车间管理按钮（二级菜单），页面读的方式有点变化（车间块状显示，涉及到半成品点验、装窑、烧结三 个功能），存到数据的关联不变加三个字段，即计划生产数量、理论单重、理论总重（算出来可修改）
3、车间管理（【车间设置】我们分配账号使用），窑车管理

个人设置-完成
1、加一个【所属车间】

合同回款
（tenantadmin/123456789）

登录账号：test/1234567
1、外协助单位---SP_TENANT_OUTSOURCING--租户管理页面---OutsourcingTenantList.js
2、收款点---SP_CONTRACT_COLLECT_MONEY_POINT---工作台-合同管理-收款点---CollectMoneyPointList.js
3、收款计划管理---SP_CONTRACT_COLLECT_MONEY_PLAN---工作台-合同管理---CollectMoneyPlanList.js
4、实际收款管理---SP_CONTRACT_COLLECT_MONEY_ACTUAL--工作台-合同管理---CollectMoneyActualList.js
5、车间管理---SP_PRODUCTION_LINE--租户管理页面---ProductionLineMgrList.js
6、天然气流量---SP_NATURAL_GAS_FLOW--工作台-原料管理---NaturalGasFlowMgrList.js
7、废品原因---SP_UNGRADED_CAUSE---工作台-系统设置-废品原因---UngradedCauseMgrList.js

1、成品检选-SP_PRODUCT_CHECK、SP_PRODUCT_CHECK_WASTE_DETAIL--工作台-库存管理--ProductCheckMgrList.js

2、到货验收-分成两个模块（工程接收、工程退货），GoodsAcceptanceMgrList.js（删了）
工程接收--SP_GOODS_ACCEPTANCE--工作台-库存管理--EngineeringAcceptanceMgrList.js
工程退货--SP_GOODS_ACCEPTANCE--工作台-库存管理--EngineeringBackMgrList.js

3、订单部位--SP_ORDER_POSITION--工作台-系统设置--OrderPositionMgrList.js
4、班次管理-SP_WORKING_SHIFT--工作台-系统设置--OrderPositionMgrList.js

================================

1、新增一个部门设置，部门设置放在课程管理前面√
2、系统设置移到数字员工前面√
3、提升计划、提升查询提到收获查询后面√
4、成果圈放到我的已办后面√
5、日报查询、成果圈、优秀成果包一个底部菜单


日报管理->我的日报√，新增日报查询
收获管理->我的收获√，新增收获查询
新增我的成绩
提升计划->查的内容是自己的，新增提升查询


1、新增出库时检验出库总数，点新增时检验

出库时硅莫红砖-622库位没有数据，应该是有数据的

未升级-20210319
update sp_material_classify set CLASSIFY_STATE = '0XX', GMT_MODIFIED = NOW() 
WHERE CLASSIFY_STATE = '0AA' AND TENANT_ID = 10;
update portal_menu set MENU_NAME = '原料类别' where MENU_ID = 112 AND MENU_NAME ='原料大类';
update portal_menu set MENU_URL = 'MaterialPurchaseList' where MENU_ID = 113 AND MENU_NAME ='原料采购';
update portal_menu set MENU_URL = 'MaterialInventoryInList' where MENU_ID = 114 AND MENU_NAME ='原料入库';
update portal_menu set MENU_URL = 'MaterialInventoryOutList' where MENU_ID = 115 AND MENU_NAME ='原料出库';
update sp_material_purchase set PURCHASE_STATE = '0XX', GMT_MODIFIED=NOW() WHERE PURCHASE_STATE='0AA';

select * from portal_menu where MENU_NAME in ('原料小类','原料规格','分选入库','分选出库','规格入库','规格出库') AND MENU_PARENT_ID = 111 order by MENU_ID desc;
update portal_menu set MENU_STATE = '0XX', GMT_MODIFIED = NOW() where MENU_NAME in ('原料小类','原料规格','分选入库','分选出库','规格入库','规格出库') AND MENU_PARENT_ID = 111;

INSERT INTO `portal_menu`(`MENU_ID`, `MENU_NAME`, `MENU_CODE`, `MENU_TYPE`, `MENU_URL`, `MENU_ICON`, `MENU_LIGHTED_ICON`, `MENU_SORT`, `MENU_STATE`, `MENU_LEVEL`, `MENU_PARENT_ID`, `GMT_CREATED`, `GMT_MODIFIED`, `VERSION`) VALUES (152, '原料库存', 'check_material_inventory_list', 'M', 'CheckClassifyMaterialInventoryList', '../../assets/icon/workbench/workbench_inventory_storage_list.png', '', 2.43, '0AA', NULL, 111, '2021-03-21 14:36:52', NULL, 0);


========================

select * from portal_user where USER_CODE = '13626109969';

select a.* from sp_storage_out_detail a 
left join sp_storage_out b on a.STORAGE_OUT_ID = b.STORAGE_OUT_ID
where b.TENANT_ID = 59 and a.BRICK_TYPE_ID = 90 order by a.DETAIL_ID desc;


2021.02.23（未改）
装窑删除-订单跟踪完成数量没有变

2021.02.02
删除：#DBDBDB->,#DADADA
编辑\关闭合同：#CB483E
跳转类：#383A46
置闲\合同：#59B968

2021.01.25

2021.01.10
1、排产管理、订单状态 没有做分页


已完成：合同管理（合同详情）、入库管理（成品入库）
未完成：订单页面、订单跟踪、库存查询、出库管理

20210131
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_verify_internal_result_list.png' where MENU_ICON='../../assets/icon/workbench/workbench_inventory_storage_list.png' and MENU_ID=130;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_verify_external_standard_list.png' where MENU_ICON='../../assets/icon/workbench/workbench_inventory_storage_list.png' and MENU_ID=129;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_verify_internal_standard_list.png' where MENU_ICON='../../assets/icon/workbench/workbench_inventory_storage_list.png' and MENU_ID=128;

update portal_menu set MENU_NAME='检验标准' where MENU_NAME='检验标准(外)' and MENU_ID=129;
update portal_menu set MENU_NAME='自检标准' where MENU_NAME='检验标准(内)' and MENU_ID=128;

20201215
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_semi_add.png' where MENU_ICON='../../assets/icon/main/halfcheck.png' and MENU_ID=56;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_semi_list.png' where MENU_ICON='../../assets/icon/main/halfquery.png' and MENU_ID=57;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_order_add.png' where MENU_ICON='../../assets/icon/main/orderadd.png' and MENU_ID=42;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_order_list.png' where MENU_ICON='../../assets/icon/main/orderquery.png' and MENU_ID=43;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_order_state_mgr.png' where MENU_ICON='../../assets/icon/main/timequery.png' and MENU_ID=46;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_custmoer_add.png' where MENU_ICON='../../assets/icon/main/customeradd.png' and MENU_ID=48;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_custmoer_list.png' where MENU_ICON='../../assets/icon/main/customerquery.png' and MENU_ID=49;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_contract_add.png' where MENU_ICON='../../assets/icon/main/orderadd.png' and MENU_ID=108;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_contract_list.png' where MENU_ICON='../../assets/icon/main/orderquery.png' and MENU_ID=109;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_scheduling_add.png' where MENU_ICON='../../assets/icon/main/timeadd.png' and MENU_ID=44;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_scheduling_list.png' where MENU_ICON='../../assets/icon/main/timequery.png' and MENU_ID=45;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_material_big_classify.png' where MENU_ICON='../../assets/icon/main/classify.png' and MENU_ID=112;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_material_small_classify.png' where MENU_ICON='../../assets/icon/main/classify.png' and MENU_ID=123;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_material_spec.png' where MENU_ICON='../../assets/icon/main/classify.png' and MENU_ID=124;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_material_spec_in.png' where MENU_ICON='../../assets/icon/main/storein.png' and MENU_ID=118;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_material_spec_out.png' where MENU_ICON='../../assets/icon/main/storeout.png' and MENU_ID=119;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_material_sorting_in.png' where MENU_ICON='../../assets/icon/main/storein.png' and MENU_ID=116;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_material_sorting_out.png' where MENU_ICON='../../assets/icon/main/storeout.png' and MENU_ID=117;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_material_in.png' where MENU_ICON='../../assets/icon/main/storein.png' and MENU_ID=114;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_material_out.png' where MENU_ICON='../../assets/icon/main/storeout.png' and MENU_ID=115;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_material_puchase.png' where MENU_ICON='../../assets/icon/main/kilncar.png' and MENU_ID=113;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_encastage_add.png' where MENU_ICON='../../assets/icon/main/enadd.png' and MENU_ID=61;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_sintering_add.png' where MENU_ICON='../../assets/icon/main/zoneadd.png' and MENU_ID=83;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_sintering_list.png' where MENU_ICON='../../assets/icon/main/zonequery.png' and MENU_ID=84;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_package_store_out_add.png' where MENU_ICON='../../assets/icon/main/storeout.png' and MENU_ID=86;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_package_store_out_list.png' where MENU_ICON='../../assets/icon/main/storeout.png' and MENU_ID=81;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_inventory_storage_add.png' where MENU_ICON='../../assets/icon/main/storein.png' and MENU_ID=120;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_inventory_storage_list.png' where MENU_ICON='../../assets/icon/main/storequery.png' and MENU_ID=121;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_yield_query.png' where MENU_ICON='../../assets/icon/main/storequery.png' and MENU_ID=122;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_logout.png' where MENU_ICON='../../assets/icon/main/logout.png' and MENU_ID=73;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_modify_pwd.png' where MENU_ICON='../../assets/icon/main/pwdset.png' and MENU_ID=71;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_user_info.png' where MENU_ICON='../../assets/icon/main/userinfo.png' and MENU_ID=70;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_brick_classify.png' where MENU_ICON='../../assets/icon/main/classify.png' and MENU_ID=90;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_machine_list.png' where MENU_ICON='../../assets/icon/main/machinery.png' and MENU_ID=89;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_kiln_car_list.png' where MENU_ICON='../../assets/icon/main/kilncar.png' and MENU_ID=88;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_package_store_in_add.png' where MENU_ICON='../../assets/icon/main/storein.png' and MENU_ID=85;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_package_store_in_list.png' where MENU_ICON='../../assets/icon/main/storein.png' and MENU_ID=80;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_tenant_add.png' where MENU_ICON='../../assets/icon/main/customeradd.png' and MENU_ID=105;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_tenant_list.png' where MENU_ICON='../../assets/icon/main/customerquery.png' and MENU_ID=106;
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_role_mgr.png' where MENU_ICON='../../assets/icon/main/classify.png' and MENU_ID=102;


// 缺少的
--装窑管理
update portal_menu set MENU_ICON='../../assets/icon/workbench/workbench_encastage_list.png' where MENU_ICON='../../assets/icon/main/enquery.png' and MENU_ID=62;

==================================

20201213
半成品管理页面样式要调整
装窑管理
0、圆图不显示
1、窑车：
2、装窑详情（砖型[订单名称]、数量）
3、记录时间
订单页面出现多加载的情况
update portal_menu set MENU_NAME='原料大类' where MENU_NAME='原料大类管理' and MENU_ID=112;
update portal_menu set MENU_NAME='砖料出入库' where MENU_NAME='砖料出入库管理' and MENU_ID=54;
update portal_menu set MENU_NAME='客户信息' where MENU_NAME='客户信息管理' and MENU_ID=47;
update portal_menu set MENU_NAME='客户信息' where MENU_NAME='客户信息管理' and MENU_ID=47;
update portal_menu set MENU_NAME='订单状态' where MENU_NAME='订单状态管理' and MENU_ID=46;
update portal_menu set MENU_NAME='砖型分类' where MENU_NAME='砖型分类管理' and MENU_ID=90;
update portal_menu set MENU_NAME='砖型分类' where MENU_NAME='砖型分类管理' and MENU_ID=100;
update portal_menu set MENU_NAME='原料采购' where MENU_NAME='原料采购管理' and MENU_ID=113;
update portal_menu set MENU_NAME='原料入库' where MENU_NAME='原料入库管理' and MENU_ID=114;
update portal_menu set MENU_NAME='原料出库' where MENU_NAME='原料出库管理' and MENU_ID=115;
update portal_menu set MENU_NAME='分选入库' where MENU_NAME='分选入库管理' and MENU_ID=116;
update portal_menu set MENU_NAME='分选出库' where MENU_NAME='分选出库管理' and MENU_ID=117;
update portal_menu set MENU_NAME='规格入库' where MENU_NAME='规格入库管理' and MENU_ID=118;
update portal_menu set MENU_NAME='规格出库' where MENU_NAME='规格出库管理' and MENU_ID=119;
update portal_menu set MENU_NAME='原料小类' where MENU_NAME='原料小类管理' and MENU_ID=123;
update portal_menu set MENU_NAME='原料规格' where MENU_NAME='原料规格管理' and MENU_ID=124;

2020.11.26
首页->工作台
新增清点，清点管理
出库管理（详）
库存查询->同入库管理（砖型、入库总数、出库总数、库存量）

超级管理员
原料采购、原料入库，在数量后面加供应商（输入框），必填
库存查询->库存数量->库存总数
InventoryQuery
成品率查询
合同跟踪
->进展说明、提交人、提交时间（倒序）
新增进展
->进展说明、保存、取消
订单关联合同

订单管理
数量、合同金额-不必填
增加重量字段

订单名称、客户名称、砖型、烧结总数、成品总数、废品（烧结总数-成品总数）、成品率（成品总数/烧结总数）
入库总数、出库总数、现有库存s


