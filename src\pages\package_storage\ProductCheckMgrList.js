import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  Linking,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class ProductCheckMgrList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      gmtCreated: null,
      selectGmtCreated: null,
      productionLineDataSource: [],
      selProductionLineId: null,
      // 不显示回火、二级品数量的租户列表
      excludeTemAndSecTenantIdList: [59, 66],
      // 不显示正品信息的租户列表
      excludeGoodsTenantIdList: [],
      brickTypeDataSource: [],
      brickTypeName: '',
      brickTypeId: null,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/product/check/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  initGmtCreated = () => {
    // 当前时间
    var currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth());
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _gmtCreated =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      selectGmtCreated: [
        currentDate.getFullYear(),
        currentDateMonth,
        currentDateDay,
      ],
      gmtCreated: _gmtCreated,
      initGmtCreated: _gmtCreated,
    });
    return _gmtCreated;
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    const {route, navigation} = this.props;
    // if (route && route.params) {
    //     const { tenantId } = route.params;
    //     if (tenantId) {
    //         console.log("=============tenantId" + tenantId + "");
    //     }
    // }
    var _gmtCreated = this.initGmtCreated();
    this.loadProductCheckList(_gmtCreated);
    // 加载砖型
    var loadTypeUrl = '/biz/brick/series/type/effBrickTreeCatalog';
    var loadRequest = {currentPage: 1, pageSize: 10000};
    httpPost(loadTypeUrl, loadRequest, (response) => {
      if (response.code == 200 && response.data && response.data) {
        this.setState({
          brickTypeDataSource: response.data,
        });
      } else if (response.code == 401) {
        WToast.show({data: response.message});
        this.props.navigation.navigate('LoginView');
      }
    });
  }

  // 加载成品检选列表
  loadProductCheckList = (_gmtCreated) => {
    let url = '/biz/product/check/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      gmtCreated: _gmtCreated ? _gmtCreated : this.state.gmtCreated,
      brickTypeId: this.state.brickTypeId,
    };
    httpPost(url, loadRequest, this.loadProductCheckListCallBack);
  };

  loadProductCheckListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      console.log(dataNew);
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      if (dataAll.length > response.data.totalRecord) {
        this.setState({
          refreshing: false,
        });
        console.log(
          '=====数据错误了========' +
            dataAll.length +
            '/' +
            response.data.totalRecord,
        );
        return;
      }
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // loadWasteCauseList = () => {
  //     let url = "/biz/ungraded/cause/list";
  //     let loadRequest = {
  //         'currentPage': 1,
  //         'pageSize': 100,
  //         "causeType": "D"
  //     };
  //     httpPost(url, loadRequest, this.loadWasteCauseListCallBack);
  // }

  // loadWasteCauseListCallBack = (response) => {
  //     if (response.code == 200 && response.data && response.data.dataList) {
  //         console.log("response.data.dataList");
  //         this.setState({
  //             wasteCauseDataSource: response.data.dataList,
  //         })
  //         if (this.state.checkId) {
  //             // 走编辑
  //         }
  //         else {
  //             if (response.data.dataList.length > 0) {
  //                 var varWasteCause = {
  //                     index: 0,
  //                     causeId: response.data.dataList[0].causeId,
  //                     causeTitle: response.data.dataList[0].causeTitle,
  //                     wasteNumber: ""
  //                     // brickAmount:response.data.dataList[0].brickAmount
  //                 };
  //                 this.setState({
  //                     selWasteCauseList: this.state.selWasteCauseList.concat(varWasteCause)
  //                 })

  //             }
  //         }
  //         console.log(this.state.selWasteCauseList)
  //     }
  // }

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      (this.state.currentPage == 1 ||
        this.state.totalRecord <= this.state.pageSize) &&
      this.state.gmtCreated == this.state.initGmtCreated
    ) {
      console.log('==========不刷新=====');
      return;
    }
    var _gmtCreated = this.initGmtCreated();
    this.setState({
      gmtCreated: _gmtCreated,
      currentPage: 1,
      brickTypeId: null,
    });
    let url = '/biz/product/check/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      gmtCreated: _gmtCreated,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========第一页即是最后一页，不加载=====');
      return;
    }
    this.loadProductCheckList();
  };

  deleteProductCheck = (checkId) => {
    console.log('=======delete=checkId', checkId);
    let url = '/biz/product/check/delete';
    let requestParams = {checkId: checkId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/product_check';
    let requestParams = {
      gmtCreated: this.state.gmtCreated,
      currentPage: 1,
      pageSize: 1000,
      brickTypeId: this.state.brickTypeId,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  // 分隔线
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  space1() {
    return (
      <View
        style={{
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
          borderBottomWidth: 10,
          borderBottomColor: '#F4F7F9',
        }}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  openGmtCreated() {
    this.refs.SelectGmtCreated.showDate(this.state.selectGmtCreated);
  }

  callBackSelectGmtCreatedValue(value) {
    console.log('==========时间选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectGmtCreated: value,
    });
    if (this.state.selectGmtCreated && this.state.selectGmtCreated.length) {
      var _gmtCreated = '';
      var vartime;
      for (var index = 0; index < this.state.selectGmtCreated.length; index++) {
        vartime = this.state.selectGmtCreated[index];
        if (index === 0) {
          _gmtCreated += vartime;
        } else if (index < 3) {
          _gmtCreated += '-' + vartime;
        } else if (index === 3) {
          _gmtCreated += ' ' + vartime;
        } else {
          _gmtCreated += ':' + vartime;
        }
      }
      this.setState({
        currentPage: 1,
        gmtCreated: _gmtCreated,
      });

      let url = '/biz/product/check/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        gmtCreated: _gmtCreated,
        brickTypeId: this.state.brickTypeId,
      };
      httpPost(url, loadRequest, this._loadFreshDataCallBack);

      // this.loadEncastageList(_gmtCreated);
    }
  }

  // 渲染砖型底部滚动数据
  openBrickTypeSelect() {
    if (
      !this.state.brickTypeDataSource ||
      this.state.brickTypeDataSource.length < 1
    ) {
      WToast.show({data: '请先添加砖型'});
      return;
    }
    this.refs.SelectBrickType.showBrickType(
      this.state.selectBrirck,
      this.state.brickTypeDataSource,
    );
  }

  callBackBrickTypeValue(value) {
    console.log('==========砖型选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectBrirck: value,
    });
    // 取选定的砖型ID
    if (value.length == 2) {
      // 加载砖型
      let loadTypeUrl = '/biz/brick/series/type/getBrickByName';
      let loadRequest = {
        brickTypeName: value[1],
        seriesName: value[0],
      };
      httpPost(loadTypeUrl, loadRequest, this._callBackLoadBrickTypeData);
    } else {
      console.log('======选择砖型返回数据不合法', value);
    }
  }

  _callBackLoadBrickTypeData = (response) => {
    if (response.code == 200 && response.data) {
      this.setState({
        brickTypeName: response.data.brickTypeName,
        brickTypeId: response.data.brickTypeId,
      });
      let url = '/biz/product/check/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        brickTypeId: response.data.brickTypeId,
      };
      httpPost(url, loadRequest, this._loadFreshDataCallBack);
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
      this.setState({
        brickTypeName: '',
        brickTypeId: '',
      });
    }
  };

  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
      //     {/*<Text style={CommonStyle.headLeftText}>返回</Text>*/}
      //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      // </TouchableOpacity>
      <View style={CommonStyle.viewListLeftViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[CommonStyle.btnListLeftBtn]}>
          <Image
            style={CommonStyle.btnListLeftBtnImage}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={CommonStyle.btnListLeftBtnText}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      // <TouchableOpacity onPress={() => {
      //     this.props.navigation.navigate("ProductCheckMgrAdd",
      //         {
      //             // 传递回调函数
      //             refresh: this.callBackFunction
      //         })
      // }}>
      //      <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
      // </TouchableOpacity>
      <View style={CommonStyle.viewListRightViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('ProductCheckMgrAdd', {
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Image
            style={CommonStyle.btnListRightBtnImage}
            source={require('../../assets/icon/iconfont/add.png')}></Image>
        </TouchableOpacity>
      </View>
    );
  }

  renderRowNew = (item, index) => {
    return (
      <View key={item.checkId} style={styles.innerViewStyle}>
        {index == 0 ? (
          <View
            style={{
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#FFFFFF',
              borderBottomWidth: 10,
              borderBottomColor: '#F4F7F9',
            }}></View>
        ) : (
          <View></View>
        )}
        <View style={CommonStyle.titleViewStyleSpecial}>
          <Text style={CommonStyle.titleTextStyleSpecial}>
            {item.checkTime ? item.checkTime : '无'}
          </Text>
          {/* <Text style={styles.titleTextStyle}>检选日期：{item.checkTime ? item.checkTime : "无"}</Text> */}
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>砖型：{item.orderName}</Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            单重：{item.pieceWeight ? item.pieceWeight + 'Kg' : '无'}
          </Text>
        </View>
        {!this.state.excludeGoodsTenantIdList.includes(
          constants.loginUser.tenantId,
        ) ? (
          <View>
            <View
              style={[
                CommonStyle.titleViewStyle,
                {alignItems: 'center', flexDirection: 'column'},
              ]}>
              <View
                style={{flexDirection: 'row', width: screenWidth - 134 + 90}}>
                <View style={{width: screenWidth - 230 + 20, marginLeft: 2}}>
                  <Text style={CommonStyle.titleTextStyle}>
                    正品数量：{item.goodsNumber ? item.goodsNumber : '无'}
                  </Text>
                </View>
                <View
                  style={{
                    width: 90 + 70,
                    marginLeft: 5,
                    justifyContent: 'flex-start',
                  }}>
                  <Text style={CommonStyle.titleTextStyle}>
                    累计正品数量：
                    {item.accumulateGoodsNumber
                      ? item.accumulateGoodsNumber
                      : '无'}
                  </Text>
                </View>
              </View>
            </View>
            <View
              style={[
                CommonStyle.titleViewStyle,
                {alignItems: 'center', flexDirection: 'column'},
              ]}>
              <View
                style={{flexDirection: 'row', width: screenWidth - 134 + 90}}>
                <View style={{width: screenWidth - 230 + 20, marginLeft: 2}}>
                  <Text style={CommonStyle.titleTextStyle}>
                    正品重量：{item.goodsWeight}吨
                  </Text>
                </View>
                <View
                  style={{
                    width: 90 + 70,
                    marginLeft: 5,
                    justifyContent: 'flex-start',
                  }}>
                  <Text style={CommonStyle.titleTextStyle}>
                    下欠数量：
                    {item.owedAmount || item.owedAmount === 0
                      ? item.owedAmount
                      : '无'}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        ) : (
          <View />
        )}
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>正品数量：{item.goodsNumber?item.goodsNumber:"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>正品重量：{item.goodsWeight}吨</Text>
                </View> */}
        <View style={[CommonStyle.titleViewStyle, {alignItems: 'center'}]}>
          <View style={{flexDirection: 'column'}}>
            {item.spProductCheckWasteDetailDTOList.map((elem, index) => {
              return (
                <View
                  key={elem.detailId}
                  style={{
                    flexDirection: 'row',
                    width: screenWidth - 134 + 90,
                    marginBottom: 5,
                  }}>
                  <View style={{width: screenWidth - 230 + 25}}>
                    <Text style={CommonStyle.titleTextStyle}>
                      废品原因：{elem.causeTitle}
                    </Text>
                  </View>
                  <View
                    style={{
                      width: 90 + 10,
                      marginLeft: 0,
                      justifyContent: 'center',
                    }}>
                    <Text style={CommonStyle.titleTextStyle}>
                      数量：{elem.wasteNumber}
                    </Text>
                  </View>
                </View>
              );
            })}
          </View>
        </View>

        {!this.state.excludeTemAndSecTenantIdList.includes(
          constants.loginUser.tenantId,
        ) ? (
          <View>
            <View style={CommonStyle.titleViewStyle}>
              <Text style={CommonStyle.titleTextStyle}>
                回火数量：{item.temperingNumber ? item.temperingNumber : '无'}
              </Text>
            </View>
            <View style={CommonStyle.titleViewStyle}>
              <Text style={CommonStyle.titleTextStyle}>
                二级品数量：
                {item.secondLevelNumber ? item.secondLevelNumber : '无'}
              </Text>
            </View>
          </View>
        ) : (
          <View />
        )}

        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>回火数量：{item.temperingNumber?item.temperingNumber:"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>二级品数量：{item.secondLevelNumber?item.secondLevelNumber:"无"}</Text>
                </View> */}

        <View style={[CommonStyle.blockTwoEditDelStyle, {marginRight: 15}]}>
          <TouchableOpacity
            onPress={() => {
              // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
              //     return;
              // }
              Alert.alert('确认', '您确定要删除该条装窑记录吗？', [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                    // this在这里可用，传到方法里还有问题
                    // this.props.navigation.goBack();
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.deleteProductCheck(item.checkId);
                  },
                },
              ]);
            }}>
            <View style={[CommonStyle.btnTwoDeleteBtnView]}>
              <Image
                style={CommonStyle.btnTwoDeleteBtnImage}
                source={require('../../assets/icon/iconfont/delete.png')}></Image>
              <Text style={CommonStyle.btnTwoDeleteBtnText}>删除</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
              //     return;
              // }
              this.props.navigation.navigate('ProductCheckMgrAdd', {
                checkId: item.checkId,
                // 传递回调函数
                productionLineId: item.productionLineId,
                refresh: this.callBackFunction,
              });
            }}>
            <View style={[CommonStyle.btnTwoEditBtnView]}>
              <Image
                style={CommonStyle.btnTwoEditBtnImage}
                source={require('../../assets/icon/iconfont/edit.png')}></Image>
              <Text style={CommonStyle.btnTwoEditBtnText}>编辑</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="成品检选"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View style={[CommonStyle.rightAbsoluteButtonContainer]}>
          <View style={[CommonStyle.rightAbsoluteButtonView]}>
            <TouchableOpacity onPress={() => this.openGmtCreated()}>
              <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>
                {!this.state.gmtCreated ? '时间' : this.state.gmtCreated}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={[CommonStyle.rightAbsoluteButtonView, {width: 90}]}>
            <TouchableOpacity onPress={() => this.openBrickTypeSelect()}>
              <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>
                {!this.state.brickTypeName ? '砖型' : this.state.brickTypeName}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={[CommonStyle.rightAbsoluteButtonView, {width: 90}]}>
            <TouchableOpacity
              onPress={() => {
                Alert.alert('确认', '您确定要导出PDF文件吗？', [
                  {
                    text: '取消',
                    onPress: () => {
                      WToast.show({data: '点击了取消'});
                    },
                  },
                  {
                    text: '确定',
                    onPress: () => {
                      WToast.show({data: '点击了确定'});
                      this.exportPdfFile();
                    },
                  },
                ]);
              }}>
              <View style={[CommonStyle.rightAbsoluteButtonBoxView]}>
                <Image
                  style={[CommonStyle.rightAbsoluteButtonIconView]}
                  source={require('../../assets/icon/iconfont/output.png')}></Image>
                <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>
                  导出
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <View style={CommonStyle.contentViewStyle}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}> */}
          {/* <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> */}
          <FlatList
            data={this.state.dataSource}
            ItemSeparatorComponent={this.space}
            renderItem={({item, index}) => this.renderRowNew(item, index)}
            keyExtractor={(item) => item.checkId}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
          {/* </ScrollView> */}
        </View>
        <BottomScrollSelect
          ref={'SelectGmtCreated'}
          callBackDateValue={this.callBackSelectGmtCreatedValue.bind(this)}
        />
        <BottomScrollSelect
          ref={'SelectBrickType'}
          callBackBrickTypeValue={this.callBackBrickTypeValue.bind(this)}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    marginTop: 10,
    // borderColor: "#F4F4F4",
    // borderWidth: 14,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    // marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 15,
    paddingTop: 5,
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'row',
  },
  itemContentChildCol1ViewStyle: {
    marginLeft: 20,
    marginTop: 15,
  },
  itemContentChildCol2ViewStyle: {
    marginLeft: 40,
    marginTop: 15,
  },
  itemContentChildTextStyle: {
    fontSize: 15,
  },
  itemBottomBtnStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  itemBottomDeleteBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 1,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    borderRadius: 4,
  },
  itemBottomEditBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: '#CB4139',
    borderRadius: 4,
  },
  itemBottomEditBtnTextStyle: {
    color: '#F0F0F0',
  },
});
