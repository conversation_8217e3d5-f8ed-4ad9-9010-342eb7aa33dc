import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, KeyboardAvoidingView, TouchableOpacity, Dimensions, Modal,Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import _ from 'lodash'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
const leftLabWidth = 130;

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
class EncastageAdd extends Component {
    constructor() {
        super()
        this.state = {
            encastageId: '',
            // 窑车数据集合
            kilnCarDataSource: [],
            // 勾选的窑车
            selKilnCarId: null,

            ordersDataSource: [],
            // 点验完待装车的砖
            completeCheckBrickTypeDataSource: [],
            // 准备装窑的砖统计集合
            selBrickTypeList: [],

            productionLineDataSource: [],
            kilnRoadDataSource: [],
            selProductionLineId: null,
            selKilnRoadId: "",

            selBrickTypeId: null,
            selBrickTypeName: null,
            selOrderId: null,
            selOrderName: null,

            modal: false,
            searchKeyWord: null,
            errorMsg: null,
            selectedEncastageDate:[],
            encastageTime:""
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        this.loadProductLineAndFireingKilnCarList();
        // this.loadFireingKilnCarList();
        // this.loadCompleteCheckBrickTypeList();
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { encastageId } = route.params;
            if (encastageId) {
                console.log("========Edit==encastageId:", encastageId);
                this.setState({
                    encastageId: encastageId
                }) 
                loadTypeUrl = "/biz/encastage/record/get";
                loadRequest = { 'encastageId': encastageId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditEncastageDataCallBack);
            }
        }
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectedEncastageDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            encastageTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        }) 

        
    }

    loadEditEncastageDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectedEncastageDate = response.data.encastageTime.split("-");
            this.setState({
                selKilnCarId: response.data.kilnCarId,
                encastageTime:response.data.encastageTime,
                selectedEncastageDate:selectedEncastageDate
            })

            var varEditKilnCar = {
                kilnCarId: response.data.kilnCarId,
                kilnCarName: response.data.kilnCarName,
            };
            console.log("=====varEditKilnCar:", varEditKilnCar)
            this.setState({
                kilnCarDataSource: this.state.kilnCarDataSource.concat(varEditKilnCar)
            })
            if (response.data.spEncastageDetailDTOList && response.data.spEncastageDetailDTOList.length > 0) {
                // 遍历装窑详情
                response.data.spEncastageDetailDTOList.forEach((item) => {
                    var varBrickType = {
                        brickTypeId: item.brickTypeId,
                        brickTypeName: item.brickTypeName,
                        orderId: item.orderId,
                        brickAmount: item.brickAmount,
                    };
                    this.setState({
                        selBrickTypeList: this.state.selBrickTypeList.concat(varBrickType)
                    })
                })
            }
        }
    }

    // 生产车间 & 窑车
    loadProductLineAndFireingKilnCarList = () => {
        let loadurl = "/biz/production/line/list"
        let url = "/biz/production/line/list";
        let loadRequest = {
            "kilnCarState": "F",
            "currentPage": 1,
            "pageSize": 200,
            "qryIncludeContent": "kilnCar",
        };
        httpPost(url, loadRequest, this.callBackLoadProductLineAndFireingKilnCarList);
    }



    callBackLoadProductLineAndFireingKilnCarList = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            let productionLineDataSource = response.data.dataList;
            //获取窑道数据
            let kilnRoadDataSource = [];
            var i = 0;
            for (i; i < productionLineDataSource.length; i++) {
                if (productionLineDataSource[i].kilnRoadName) {
                    kilnRoadDataSource = kilnRoadDataSource.concat(productionLineDataSource[i])
                }
            }
            this.setState({
                kilnRoadDataSource: kilnRoadDataSource
            })

            // 生产车间
            let selProductionLineId = response.data.dataList[0].productionLineId;
            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                selProductionLineId = constants.loginUser.spUserExtDTO.productionLineId;
            }
            this.setState({
                productionLineDataSource: productionLineDataSource,
                selProductionLineId: selProductionLineId,
            })

            //判断所在车间是否设置窑道名称，若有则选中该窑道，没有则选中第一个窑道
            if (kilnRoadDataSource && kilnRoadDataSource.length > 0) {
                kilnRoadDataSource.forEach(element => {
                    if (element.productionLineId == selProductionLineId) {
                        this.setState({
                            // 窑道
                            selKilnRoadId: selProductionLineId
                        })
                    }
                });
                if (!this.state.selKilnRoadId) {
                    this.setState({
                        // 窑道
                        selKilnRoadId: kilnRoadDataSource[0].productionLineId
                    })
                }
            }
            else {
                this.setState({
                    // 窑道
                    selKilnRoadId: selProductionLineId
                })
            }


            if (productionLineDataSource.length == 1) {
                this.setState({
                    kilnCarDataSource: productionLineDataSource[0].kilnCarDTOList,
                })
                if (this.state.encastageId && this.state.selKilnCarId != 0) {
                    this.setState({
                        selKilnCarId: this.state.selKilnCarId,
                    })
                }
                else if (productionLineDataSource[0].kilnCarDTOList.length > 1) {
                    this.setState({
                        selKilnCarId: productionLineDataSource[0].kilnCarDTOList[0].kilnCarId
                    })
                }
            }
            else {
                if (kilnRoadDataSource && kilnRoadDataSource.length > 0) {
                    // JS 数组遍历
                    productionLineDataSource.forEach((obj) => {
                        if (selProductionLineId && obj.kilnCarDTOList && obj.productionLineId === selProductionLineId) {
                            this.setState({
                                kilnCarDataSource: obj.kilnCarDTOList
                            })
                        }
                    })
                }


            }

            this.loadOrder(selProductionLineId);

            // let url1= "/biz/order/list";
            // let loadRequest1={
            //     'currentPage':1,
            //     'pageSize':100,
            //     "display":"Y",
            //     "excludeOrderStateList":[
            //         "A","K"
            //     ],
            //     'productionLineId':this.state.selProductionLineId
            // };
            // httpPost(url1, loadRequest1, this.callBackLoadCompleteCheckBrickTypeList);
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadOrder = (productionLineId) => {
        var currentTime = new Date();
        var qryEndTime = new Date(currentTime.getTime() + 8 * 3600000);
        var qryStartTime = new Date(qryEndTime.getTime() - 15 * 86400000);
        console.log("qryEndTime===",qryEndTime);
        console.log("qryStartTime===",qryStartTime);

        let loadUrl = "/biz/order/list";
        let loadRequest = {
            'currentPage': 1,
            'pageSize': 1000,
            "display": "Y",
            "qryContent": "order",
            "searchKeyWord": this.state.searchKeyWord,
            "excludeOrderStateList": [
                "A","B", "K"
            ],
            "qryStartTime":qryStartTime,
            "qryEndTime":qryEndTime,
            'semiCheck':"Y"
            // 'productionLineId':productionLineId ? productionLineId : this.state.selProductionLineId
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadCompleteCheckBrickTypeList);
    }


    // loadFireingKilnCarList=()=>{
    //     let url= "/biz/kiln/car/list";
    //     let loadRequest={
    //         "kilnCarState":"F",
    //         "currentPage": 1,
    //         "pageSize": 200,
    //     };
    //     httpPost(url, loadRequest, this.callBackLoadFreeKilnCarList);
    // }

    // callBackLoadFreeKilnCarList=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         if (response.data.dataList.length <= 0) {
    //             let toastOpts = getFailToastOpts("请联系管理员配置窑车");
    //             WToast.show(toastOpts);
    //             return;
    //         }
    //         this.setState({
    //             kilnCarDataSource:response.data.dataList,
    //             // encastageDetailDataSource: response.data.dataList[0] ? response.data.dataList[0].spEncastageDetailDTOList : []
    //         })
    //         if (this.state.encastageId && this.state.selKilnCarId != 0) {
    //             // 读编辑数据
    //         }
    //         else {
    //             this.setState({
    //                 selKilnCarId:response.data.dataList[0] ? response.data.dataList[0].kilnCarId : 0,
    //             })
    //         }
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // select a.brick_type_id, a.order_id, sum(a.brick_amount) from sp_semi_finished_check a where a.BRICK_TYPE_ID=20 GROUP BY a.brick_type_id, a.order_id order by a.order_id desc;
    // 载入完成点验的砖型
    // loadCompleteCheckBrickTypeList=()=>{
    //     // 加载排产状态的订单，显示砖型
    //     let url= "/biz/order/list";
    //     let loadRequest={
    //         'currentPage':1,
    //         'pageSize':100,
    //         "display":"Y",
    //         "excludeOrderStateList":[
    //             "A","K"
    //         ]
    //     };
    //     httpPost(url, loadRequest, this.callBackLoadCompleteCheckBrickTypeList);
    // }

    loadOrderCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                completeCheckBrickTypeDataSource: response.data.dataList,
                selBrickTypeList: []
            })
            if (response.data.dataList.length > 0) {
                var varBrickType = {
                    index: 0,
                    checkId: response.data.dataList[0].checkId,
                    brickTypeId: response.data.dataList[0].brickTypeId,
                    brickTypeName: response.data.dataList[0].brickTypeName,
                    orderId: response.data.dataList[0].orderId,
                    brickAmount: ""
                    // brickAmount:response.data.dataList[0].brickAmount
                };
                this.setState({
                    selBrickTypeList: this.state.selBrickTypeList.concat(varBrickType)
                })

            }
        }
    }

    // 载入完成点验的砖型-回调函数
    callBackLoadCompleteCheckBrickTypeList = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                completeCheckBrickTypeDataSource: response.data.dataList,
                ordersDataSource: response.data.dataList,
            })
            if (this.state.encastageId) {
                // 走编辑
            }
            else {
                // if (response.data.dataList.length>0) {
                //     var varBrickType={
                //         // index:0,
                //         // checkId:response.data.dataList[0].checkId,
                //         brickTypeId:response.data.dataList[0].brickTypeId,
                //         brickTypeName:response.data.dataList[0].brickTypeName,
                //         orderId:response.data.dataList[0].orderId,
                //         orderName:response.data.dataList[0].orderName,
                //         brickAmount:""
                //         // brickAmount:response.data.dataList[0].brickAmount
                //     };
                //     this.setState({
                //         selBrickTypeList:this.state.selBrickTypeList.concat(varBrickType)
                //     })

                // }
            }
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("EncastageList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>装窑管理</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>装窑管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    // 窑道名称
    renderKilnRoadNameRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                var selKilnCarId = null;
                // 切换窑道时，下面的窑车也要跟着变，默认选择第一个
                this.setState({
                    selKilnRoadId: item.productionLineId,
                    kilnCarDataSource: item.kilnCarDTOList,
                })
                if (item.kilnCarDTOList && item.kilnCarDTOList.length > 0) {
                    selKilnCarId = item.kilnCarDTOList[0].kilnCarId;
                }
                this.setState({
                    selKilnCarId: selKilnCarId,
                })
            }}>
                <View key={item.productionLineId} style={[item.productionLineId === this.state.selKilnRoadId ? 
                    // CommonStyle.selectedBlockItemViewStyle 
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    : 
                    // CommonStyle.blockItemViewStyle
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 10,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                    ]}>
                    <Text style={[item.productionLineId === this.state.selKilnRoadId ? 
                    // CommonStyle.selectedBlockItemTextStyle16 
                    {
                        color: '#1E6EFA'
                    }
                    : 
                    // CommonStyle.blockItemTextStyle16
                    {
                        color: '#404956'
                    }
                    ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.kilnRoadName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 生产车间 
    renderProductLineRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                // var selKilnCarId = null;
                // 切换生产车间时，下面的机台也要跟着变，机台默认选择第一个
                this.setState({
                    selProductionLineId: item.productionLineId,
                    // kilnCarDataSource:item.kilnCarDTOList,
                })
                // if (item.kilnCarDTOList && item.kilnCarDTOList.length > 0) {
                //     selKilnCarId = item.kilnCarDTOList[0].kilnCarId;
                // }
                // this.setState({
                //     selKilnCarId:selKilnCarId,
                // })

                // let url= "/biz/order/list";
                // let loadRequest={
                //     'currentPage':1,
                //     'pageSize':100,
                //     "display":"Y",
                //     "excludeOrderStateList":[
                //         "A","K"
                //     ],
                //     'productionLineId': item.productionLineId
                // };
                // httpPost(url, loadRequest, this.loadOrderCallBack);
                this.loadOrder(item.productionLineId);
            }}>
                <View key={item.productionLineId} style={item.productionLineId === this.state.selProductionLineId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.productionLineId === this.state.selProductionLineId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.productionLineName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 窑车单项渲染
    renderKilnCarRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selKilnCarId: item.kilnCarId
                })
            }}>
                <View key={item.kilnCarId} style={[item.kilnCarId === this.state.selKilnCarId ? 
                    // CommonStyle.selectedBlockItemViewStyle 
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    : 
                    // CommonStyle.blockItemViewStyle
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 30)/3,
                        borderRadius: 4
                    }
                    ]}>
                    <Text style={[item.kilnCarId === this.state.selKilnCarId ? 
                        // CommonStyle.selectedBlockItemTextStyle16 
                        {
                            color: '#1E6EFA'
                        }                       
                         : 
                        // CommonStyle.blockItemTextStyle16
                        {
                            color: '#404956'
                        }
                        ,
                        {
                            fontSize: 16, textAlign : 'center'
                        }
                        ]}>
                        {item.kilnCarName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    saveEncastage = () => {
        // console.log("=======saveEncastage==this.state.selBrickTypeList", this.state.selBrickTypeList);
        let toastOpts;
        if (!this.state.selKilnCarId || this.state.selKilnCarId === 0) {
            toastOpts = getFailToastOpts("请选择窑车");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selBrickTypeList || this.state.selBrickTypeList.length === 0) {
            toastOpts = getFailToastOpts("装窑至少指定一种砖型");
            WToast.show(toastOpts)
            return;
        }
        let brickAmountVal = true;
        this.state.selBrickTypeList.map((elem, index) => {
            // console.log("======elem:", elem)
            if (!elem || !elem.brickAmount || elem.brickAmount <= 0) {
                brickAmountVal = false;
                return;
            }
        })
        if (!brickAmountVal) {
            toastOpts = getFailToastOpts("砖的数量不合法");
            WToast.show(toastOpts)
            return;
        }
        // 新增、编辑接口都走添加
        let url = "/biz/encastage/record/add";
        let _spEncastageDetailDTOList = [];
        this.state.selBrickTypeList.map((elem, index) => {
            var encastageDetailDTO = {
                "brickTypeId": elem.brickTypeId,
                "orderId": elem.orderId,
                "brickAmount": elem.brickAmount,
                "checkId": elem.checkId
            }
            _spEncastageDetailDTOList.push(encastageDetailDTO);
        })
        let requestParams = {
            "encastageId": this.state.encastageId,
            "kilnCarId": this.state.selKilnCarId,
            "spEncastageDetailDTOList": _spEncastageDetailDTOList,
            "encastageTime":this.state.encastageTime
        };
        // console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.saveEncastage_call_back);
    }

    // 保存回调函数
    saveEncastage_call_back = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('装窑完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    renderOrderItem = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selBrickTypeId: item.brickTypeId,
                    selBrickTypeName: item.brickTypeName,
                    selOrderId: item.orderId,
                    selOrderName: item.orderName,
                    // brickAmount:item.brickAmount,
                })
            }}>
                <View key={item.orderId} style={item.orderId === this.state.selOrderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.orderId === this.state.selOrderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    openEncastageDate(){
        this.refs.SelectedEncastageDate.showDate(this.state.selectedEncastageDate)
    }

    callBackSelectEncastageDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedEncastageDate:value
        })
        if (value && value.length) {
            var encastageTime = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    encastageTime += vartime;
                }
                else{
                    encastageTime += "-" + vartime;
                }
            }
            this.setState({
                encastageTime:encastageTime
            })
        }
    }

    render() {
        // 动态显示装窑砖类数据
        var pages = [];
        for (var i = 0; i < this.state.selBrickTypeList.length; i++) {
            const _completeCheckBrickTypeDataSource = _.cloneDeep(this.state.completeCheckBrickTypeDataSource);
            _completeCheckBrickTypeDataSource.map((elem, index) => {
                elem._index = i;
                return elem;
            })
            pages.push(
                <View key={"view_" + this.state.selBrickTypeList[i].orderId + "_" + i}>

                    <View style={[{ flexWrap: 'wrap' }, this.state.checkId ? CommonStyle.disableViewStyle : null]}>
                        <TouchableOpacity onPress={(event) => {
                            // console.log("=============eventevent._dispatchInstances.memoizedProps:", event._dispatchInstances.memoizedProps);
                            // console.log("=============eventqwqwe:");
                            // var productionLineId = event._dispatchInstances.memoizedProps.productionLineId;
                            // console.log("=============productionLineId:", productionLineId);
                            // this.loadOrder(productionLineId);
                            // this.setState({ 
                            //     modal:true,
                            //     selProductionLineId:productionLineId,
                            // })
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: 'rgba(178,178,178,0.5)', padding: 10, margin: 5 }]}>
                                <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                    选择砖型
                                    {this.state.selBrickTypeList[i].orderId && this.state.selBrickTypeList[i].orderName ? ("：" + this.state.selBrickTypeList[i].orderName) : null}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    {/* <View style={{flexDirection:'row', flexWrap:'wrap'}}>
                        {
                            _completeCheckBrickTypeDataSource.map((elem, index) => {
                                return (
                                    <TouchableOpacity key={elem.orderId} onPress={(event) => { 
                                        // console.log("=======event", event);
                                        var tempSelBrickTypeList = this.state.selBrickTypeList;
                                        tempSelBrickTypeList[elem._index] = {
                                            index:elem.index,
                                            checkId:elem.checkId,
                                            brickTypeId:elem.brickTypeId,
                                            brickTypeName:elem.brickTypeName,
                                            orderId:elem.orderId,
                                            brickAmount:tempSelBrickTypeList[elem._index].brickAmount
                                        }
                                        this.setState({
                                            selBrickTypeList:tempSelBrickTypeList
                                        })
                                    }} >
                                        <View style={this.state.selBrickTypeList[i].orderId===elem.orderId && this.state.selBrickTypeList[i].brickTypeId == elem.brickTypeId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                                            <Text style={this.state.selBrickTypeList[i].orderId===elem.orderId && this.state.selBrickTypeList[i].brickTypeId == elem.brickTypeId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                                                {elem.orderName}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                    
                                );
                            })
                        }
                    </View> */}
                    <View style={CommonStyle.rowLabView}>
                        <View style={CommonStyle.rowLabLeftView}>
                            <Text style={CommonStyle.rowLabTextStyle}>数量</Text>
                            <Text style={CommonStyle.rowLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            placeholder={'请输入砖的数量'}
                            style={CommonStyle.rowRightTextInput}
                            orderId={this.state.selBrickTypeList[i].orderId}
                            onChange={(event) => {
                                // 通过回调事件查看控件属性
                                // var orderId = event.target._internalFiberInstanceHandleDEV.memoizedProps.orderId;
                                var orderId = event._dispatchInstances.memoizedProps.orderId;
                                var text = event.nativeEvent.text;
                                console.log("=====isNumber:", isNumber(text));
                                var varselBrickType;
                                for (var index = 0; index < this.state.selBrickTypeList.length; index++) {
                                    varselBrickType = this.state.selBrickTypeList[index];
                                    if (orderId === varselBrickType.orderId) {
                                        varselBrickType.brickAmount = text;
                                        this.state.selBrickTypeList[index] = varselBrickType;
                                        console.log("==数据更新==this.state.selBrickTypeList", this.state.selBrickTypeList);
                                    }
                                }
                            }}
                        >
                            {this.state.selBrickTypeList[i].brickAmount}
                        </TextInput>
                    </View>
                </View>
            );
        }
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title='新增装窑'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()} />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={CommonStyle.rowLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>装窑日期</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openEncastageDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.encastageTime ? "请选择装窑日期" : this.state.encastageTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    {
                        this.state.productionLineDataSource.length == 1 ?
                            <View>
                                <View style={CommonStyle.rowLabView}>
                                    <Text style={CommonStyle.rowLabRedTextStyle}>*</Text>
                                    <Text style={CommonStyle.rowLabTextStyle}>窑车</Text>
                                    {/* <Text style={CommonStyle.rowLabRedTextStyle}>*</Text> */}
                                </View>
                                <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row' }}>
                                    {
                                        (this.state.kilnCarDataSource && this.state.kilnCarDataSource.length > 0)
                                            ?
                                            this.state.kilnCarDataSource.map((item, index) => {
                                                return this.renderKilnCarRow(item)
                                            })
                                            : <EmptyRowViewComponent />
                                    }
                                </View>
                                <View style={CommonStyle.lineBorderBottomStyle} />

                            </View>
                            :
                            <View>
                                <View style={CommonStyle.rowLabView}>
                                    <Text style={CommonStyle.rowLabRedTextStyle}>*</Text>
                                    <Text style={CommonStyle.rowLabTextStyle}>窑道名称</Text>
                                    {/* <Text style={CommonStyle.rowLabRedTextStyle}>*</Text> */}
                                </View>
                                <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row', marginLeft:20}}>
                                    {
                                        (this.state.kilnRoadDataSource && this.state.kilnRoadDataSource.length > 0)
                                            ?
                                            this.state.kilnRoadDataSource.map((item, index) => {
                                                if (item.kilnRoadName) {
                                                    return this.renderKilnRoadNameRow(item)
                                                }
                                            })
                                            : <EmptyRowViewComponent />
                                    }
                                </View>
                                <View style={CommonStyle.lineBorderBottomStyle} />

                                <View style={CommonStyle.rowLabView}>
                                    <Text style={CommonStyle.rowLabRedTextStyle}>*</Text>
                                    <Text style={CommonStyle.rowLabTextStyle}>窑车</Text>
                                    {/* <Text style={CommonStyle.rowLabRedTextStyle}>*</Text> */}
                                </View>
                                <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row', marginLeft:20 }}>
                                    {
                                        (this.state.kilnCarDataSource && this.state.kilnCarDataSource.length > 0)
                                            ?
                                            this.state.kilnCarDataSource.map((item, index) => {
                                                return this.renderKilnCarRow(item)
                                            })
                                            : <EmptyRowViewComponent />
                                    }
                                </View>
                                {/* <View style={CommonStyle.lineBorderBottomStyle} /> */}

                                {/* <View style={CommonStyle.rowLabView}>
                            <Text style={CommonStyle.rowLabTextStyle}>生产车间</Text>
                            <Text style={styles.rowLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0) 
                                ? 
                                this.state.productionLineDataSource.map((item, index)=>{
                                    return this.renderProductLineRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View> */}
                            </View>
                    }

                    <View style={[styles.addItemSplitRowView]}>
                        <Text style={[CommonStyle.rowLabRedTextStyle,{marginLeft:15}]}>*</Text>
                        <Text style={[CommonStyle.addItemSplitRowText,{paddingLeft:0}]}>砖型</Text>
                        {/* <Text style={CommonStyle.rowLabRedTextStyle}>*</Text> */}

                    </View>

                    <View>
                        {
                            pages.map((elem, index) => {
                                return elem;
                            })
                        }
                    </View>

                    {/* {
                    this.state.selBrickTypeList.map((elem, index) => {
                        return (
                            <View>
                                <View style={[{flexWrap:'wrap'}, this.state.checkId ? CommonStyle.disableViewStyle : null]}>
                                    <TouchableOpacity onPress={(index)=>{
                                        this.loadOrder(elem.productionLineId);
                                        this.setState({ 
                                            modal:true,
                                            selProductionLineId:elem.productionLineId,
                                        })
                                    }}>
                                        <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}]}>
                                            <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                                                选择砖型
                                                {elem.orderId && elem.orderName ? ("：" + elem.orderName) : null}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <View style={CommonStyle.rowLabView}>
                                    <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>数量</Text>
                                    </View>
                                    <TextInput 
                                        keyboardType='numeric'
                                        placeholder={'请输入砖的数量'}
                                        style={CommonStyle.rowRightTextInput}
                                        orderId={this.state.selBrickTypeList[i].orderId}
                                        onChange={(event) => {
                                            // 通过回调事件查看控件属性
                                            // var orderId = event.target._internalFiberInstanceHandleDEV.memoizedProps.orderId;
                                            var orderId = event._dispatchInstances.memoizedProps.orderId;
                                            var text = event.nativeEvent.text;
                                            console.log("=====isNumber:", isNumber(text));
                                            var varselBrickType;
                                            for(var index=0; index<this.state.selBrickTypeList.length;index++){
                                                varselBrickType = this.state.selBrickTypeList[index];
                                                if (orderId === varselBrickType.orderId) {
                                                    varselBrickType.brickAmount = text;
                                                    this.state.selBrickTypeList[index] = varselBrickType;
                                                    console.log("==数据更新==this.state.selBrickTypeList", this.state.selBrickTypeList);
                                                }
                                            }
                                        }}
                                    >
                                        {this.state.selBrickTypeList[i].brickAmount}
                                    </TextInput>
                                </View>
                            </View>
                        );
                    })
                } */}

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.alignCenterStyle}>
                                    {/* <Text style={CommonStyle.rowLabTextStyle}>生产车间</Text>
                                <Text style={CommonStyle.rowLabRedTextStyle}>*</Text> */}
                                    <Text style={[CommonStyle.rowLabRedTextStyle, CommonStyle.boldTextStyle]}>{this.state.errorMsg}</Text>
                                </View>
                                {/* <View style={{flexWrap:'wrap', flexDirection:'row'}}>
                                {
                                    (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0) 
                                    ? 
                                    this.state.productionLineDataSource.map((item, index)=>{
                                        return this.renderProductLineRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View> */}
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                    <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                </View> */}
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadOrder();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state.ordersDataSource && this.state.ordersDataSource.length > 0)
                                                ?
                                                this.state.ordersDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderOrderItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={CommonStyle.rowLabView}>
                                    <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabRedTextStyle}>*</Text>
                                        <Text style={CommonStyle.rowLabTextStyle}>数量</Text>
                                        {/* <Text style={CommonStyle.rowLabRedTextStyle}>*</Text> */}
                                    </View>
                                    <TextInput
                                        keyboardType='numeric'
                                        placeholder={'请输入砖的数量'}
                                        style={[CommonStyle.rowRightTextInput, { width: screenWidth - 150 }]}
                                        onChangeText={(text) => this.setState({ brickAmount: text })}
                                    >
                                        {this.state.brickAmount}
                                    </TextInput>
                                </View>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                            orderId: null,
                                            orderName: null,
                                            brickAmount: null,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selOrderId) {
                                            let errorMsg = "您还没有选择砖型";
                                            this.setState({
                                                errorMsg: errorMsg,
                                            })
                                            let toastOpts = getFailToastOpts(errorMsg);
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        if (!this.state.brickAmount) {
                                            let errorMsg = "请输入砖的数量";
                                            this.setState({
                                                errorMsg: errorMsg,
                                            })
                                            let toastOpts = getFailToastOpts(errorMsg);
                                            WToast.show(toastOpts);
                                            return;
                                        }

                                        // 判断装窑总数量是否小于烧结总数量
                                        var loadTypeUrl = "/biz/encastage/record/compareChecked";
                                        var loadRequest = { 
                                            'orderId': this.state.selOrderId, 
                                            'encastageAmount':this.state.brickAmount
                                        };
                                        httpPost(loadTypeUrl, loadRequest, (response)=>{
                                            if (response.code == 200) {
                                                if (this.state.completeCheckBrickTypeDataSource.length > 0) {
                                                    var existAdd = false;
                                                    let selBrickTypeList = this.state.selBrickTypeList;
                                                    // JS 数组遍历
                                                    selBrickTypeList.forEach((obj) => {
                                                        console.log("==========obj.orderId:", obj.orderId, "==========this.state.orderId:", this.state.orderId)
                                                        if (obj.orderId === this.state.selOrderId) {
                                                            // obj.brickAmount = parseInt(obj.brickAmount) + parseInt(this.state.brickAmount);
                                                            obj.brickAmount = parseInt(this.state.brickAmount);
                                                            existAdd = true;
                                                        }
                                                    })
                                                    if (existAdd) {
                                                        this.setState({
                                                            selBrickTypeList: selBrickTypeList,
                                                        })
                                                    }
                                                    else {
                                                        var varBrickType = {
                                                            brickTypeId: this.state.selBrickTypeId,
                                                            brickTypeName: this.state.selBrickTypeName,
                                                            orderId: this.state.selOrderId,
                                                            orderName: this.state.selOrderName,
                                                            brickAmount: this.state.brickAmount,
                                                            productionLineId: this.state.selProductionLineId,
                                                        };
                                                        this.setState({
                                                            selBrickTypeList: this.state.selBrickTypeList.concat(varBrickType)
                                                        })
                                                    }
        
                                                    // console.log("xxxxxx======selBrickTypeList:", this.state.selBrickTypeList)
                                                }
                                                this.setState({
                                                    modal: false,
                                                    orderId: null,
                                                    orderName: null,
                                                    brickAmount: null,
                                                })
                                                
                                            }
                                            else{
                                                this.setState({
                                                    errorMsg: response.message,
                                                })
                                                WToast.show({ data: response.message });
                                                return;
                                            }
                                        });
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>

                        </View>
                        <View>

                        </View>
                    </Modal>

                    <View style={CommonStyle.blockTwoEditDelStyle}>
                        <TouchableOpacity onPress={() => {
                            // console.log("==========this.state.brickTypeDataSource.length:", this.state.completeCheckBrickTypeDataSource.length);
                            // if (this.state.selBrickTypeList.length >= this.state.completeCheckBrickTypeDataSource.length){
                            //     WToast.show({data:"砖型数量达到上限，不能再添加了"});
                            //     return;
                            // }
                            this.setState({
                                errorMsg: null,
                                modal: true,
                            })
                            // if (this.state.completeCheckBrickTypeDataSource.length > 0) {
                            //     var selIndex = this.state.selBrickTypeList.length;
                            //     var varBrickType={
                            //         index:selIndex,
                            //         checkId:this.state.completeCheckBrickTypeDataSource[selIndex].checkId,
                            //         brickTypeId:this.state.completeCheckBrickTypeDataSource[selIndex].brickTypeId,
                            //         brickTypeName:this.state.completeCheckBrickTypeDataSource[selIndex].brickTypeName,
                            //         orderId:this.state.completeCheckBrickTypeDataSource[selIndex].orderId,
                            //         brickAmount:""
                            //     };
                            //     this.setState({
                            //         selBrickTypeList:this.state.selBrickTypeList.concat(varBrickType)
                            //     })
                            //     // console.log("xxxxxx======selBrickTypeList:", this.state.selBrickTypeList)
                            // }
                        }}>
                            <View style={[CommonStyle.btnTwoEditBtnView,{width:80}]}>
                                <Text style={CommonStyle.btnTwoEditBtnText}>+新增砖型</Text>
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity onPress={() => {
                            if (!this.state.selBrickTypeList) {
                                WToast.show({ data: "没有可删除的，请先指定砖型" });
                                return;
                            }
                            // if (this.state.selBrickTypeList.length <= 1) {
                            //     WToast.show({data:"至少要指定一种砖型"});
                            //     return;
                            // }
                            this.setState({
                                selBrickTypeList: this.state.selBrickTypeList.slice(0, -1)
                            })
                        }}>
                            <View style={[CommonStyle.btnTwoDeleteBtnView,{marginRight:10}]}>
                                <Text style={CommonStyle.btnTwoDeleteBtnText}>-删除</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={{height:ifIphoneXContentViewHeight()-192-250, backgroundColor:'#F2F5FC'}}>
                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                        <TouchableOpacity onPress={() => {
                            this.props.navigation.goBack()
                        }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveEncastage.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>

                    </View>
                </ScrollView>
                <BottomScrollSelect 
                    ref={'SelectedEncastageDate'} 
                    callBackDateValue={this.callBackSelectEncastageDateValue.bind(this)}
                />
            </KeyboardAvoidingView>
        )
    }

}
const styles = StyleSheet.create({

    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    addItemSplitRowView: {
        width: screenWidth,
        height: 50,
        flexDirection: 'row',
        // justifyContent: 'flex-start',
        alignItems: 'center',
        backgroundColor: '#F6F9FA',
        marginTop: 10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    }
})
module.exports = EncastageAdd