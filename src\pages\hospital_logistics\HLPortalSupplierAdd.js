import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TextInput,TouchableOpacity,ScrollView, Alert,BottomScrollSelect,
    FlatList, RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class PortalStaffMgrAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            operate:"",

            supplierType:"",
            supplierId: "",
            supplierName: "",
            supplierAbbreviation: "",
            supplierCode: "",
            supplierSort:0,
            typeSource:[
                {
                    id:1,
                    suppliertype:"药品",
                    supplierName:"M"
                },
                {
                    id:2,
                    suppliertype:"物资",
                    supplierName:"P"
                }
            ],
            selId:1,
            selSupplierName:"M",
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { supplierId } = route.params;
            if (supplierId) {
                this.setState({
                    operate: "编辑",
                    supplierId: supplierId,
                })
                loadTypeUrl = "/biz/portal/supplier/get";
                loadRequest = { 'supplierId': supplierId };
                httpPost(loadTypeUrl, loadRequest, this.loadPortalStaffMgrCallBack);

            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
        }
    }

    loadPortalStaffMgrCallBack = (response) => {
        if (response.code == 200 && response.data) {
            if(response.data.supplierType == 'M'){
                this.setState({
                    selId:1
                })
            }
            if(response.data.supplierType == 'P'){
                this.setState({
                    selId:2
                })
            }
            this.setState({
                supplierName: response.data.supplierName,
                supplierAbbreviation:response.data.supplierAbbreviation,
                supplierType: response.data.supplierType,
                supplierCode:response.data.supplierCode,
                supplierSort: response.data.supplierSort,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLPortalSupplierList",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Text style={CommonStyle.headRightText}>供货单位</Text>
            </TouchableOpacity>
        )
    }

     //type列表展示
     renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selId:item.id,
                        selSupplierName:item.supplierName,
                        selSupplieType :item.suppliertype
                    })
                }}>
                <View key={item.id} style={[item.id===this.state.selId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.id===this.state.selId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.suppliertype}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    savePortalStaff = () => {
        let toastOpts;
        if (!this.state.supplierName) {
            toastOpts = getFailToastOpts("请填写供货单位");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selId) {
            toastOpts = getFailToastOpts("请选择单位分类");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.supplierSort) {
        //     toastOpts = getFailToastOpts("请填写排序");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url = "/biz/portal/supplier/add";
        if (this.state.supplierId) {
            console.log("=========Edit===supplierId", this.state.supplierId)
            url = "/biz/portal/supplier/modify";
        }
        let requestParams = {
            supplierId: this.state.supplierId,
            supplierName: this.state.supplierName,
            supplierCode:this.state.supplierCode,
            supplierType: this.state.selSupplierName,
            supplierSort: this.state.supplierSort,
        };
        console.log("======requestParams======",requestParams)
        httpPost(url, requestParams, this.savePortalStaffMgrCallBack);
    }

    // 保存回调函数
    savePortalStaffMgrCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '供货单位'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>供货单位</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入供货单位名称'}
                            onChangeText={(text) => this.setState({ supplierName: text })}
                        >
                            {this.state.supplierName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>关联标识</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入关联标识'}
                            onChangeText={(text) => this.setState({ supplierCode: text })}
                        >
                            {this.state.supplierCode}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.rowLabView}>
                            <Text style={styles.leftLabNameTextStyle}>单位分类</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.typeSource && this.state.typeSource.length > 0) 
                                ? 
                                this.state.typeSource.map((item, index)=>{
                                    return this.renderRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>
                    

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'0'}
                            onChangeText={(text) => this.setState({ supplierSort: text })}
                        >
                            {this.state.supplierSort}
                        </TextInput>
                    </View>


                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.savePortalStaff.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>           
            </View>
        )
    }
}
const styles = StyleSheet.create({
    leftLabNameTextStyle:{
        fontSize:18,
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight:30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth +30),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }

});