import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Linking, Clipboard, Image, Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import ImageViewer from 'react-native-image-zoom-viewer';
import '../../utils/Global';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
var screenHeight = Dimensions.get('window').height;
export default class InformationConfigList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            searchKeyWord: "",
            topBlockLayoutHeight: 0,
            isShowImage: false,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
            this.loadInformationList();
        }
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "advertisingTypeList": ["D", "N"],
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "advertisingTypeList": ["D", "N"],
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadInformationList();
    }

    loadInformationList = () => {
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "advertisingTypeList": ["D", "N"],
        };
        httpPost(url, loadRequest, this.loadInformationListCallBack);
    }

    loadInformationListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            console.log("=========资讯数据：", JSON.stringify(response.data, null, 6))
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })

        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteInformation = (advertisingId) => {
        console.log("=======delete=advertisingId", advertisingId);
        let url = "/biz/portal/advertising/delete";
        let requestParams = { 'advertisingId': advertisingId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    renderRow = (item, index) => {
        return (
            <View key={item.advertisingId} style={styles.innerViewStyle}>
                {
                    item.advertisingImage ?
                        <View>
                            <View style={[{ width: 120, height: 150, marginLeft: 10, marginBottom: 10, display: 'flex' }]}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        image: item.advertisingImage,
                                        isShowImage: true,
                                    })
                                }}>
                                    <Image source={{ uri: (constants.image_addr + '/' + item.advertisingImage) }} style={{ height: 150, width: 120 }} />
                                </TouchableOpacity>
                                <Modal visible={this.state.isShowImage} transparent={true}>
                                    <ImageViewer saveToLocalByLongPress={false} onClick={() => { this.setState({ isShowImage: false }) }}
                                        enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} onSave={() => alert("点击了保存图片")}
                                        onSwipeDown={() => { this.setState({ isShowImage: false }) }} imageUrls={[{ url: (constants.image_addr + '/' + this.state.image) }]} />
                                </Modal>
                            </View>
                        </View>
                        :
                        <View />
                }
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>资讯主标题：{item.advertisingTitle}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>资讯副标题：{item.advertisingContent ? item.advertisingContent : '无'}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>资讯类型：{item.advertisingType}</Text>
                </View>

                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>资讯链接：{item.advertisingLink}</Text>
                </View>

                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                    <TouchableOpacity onPress={() => {
                        Alert.alert('确认', '您确定要删除该资讯吗？', [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                    // this在这里可用，传到方法里还有问题
                                    // this.props.navigation.goBack();
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteInformation(item.advertisingId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 70, flexDirection: "row" }]}>
                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        this.props.navigation.navigate("InformationConfigAdd",
                            {
                                // 传递参数
                                advertisingId: item.advertisingId,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle, { width: 70, flexDirection: "row" }]}>
                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    // 分隔线
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("InformationConfigAdd",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Text style={CommonStyle.headRightText}>新增资讯</Text>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='资讯配置'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        // marginTop:10,
        borderLeftColor: '#FFFFFF',
        borderRightColor: '#FFFFFF',
        borderTopColor: '#F4F4F4',
        borderBottomColor: '#F4F4F4',
        borderWidth: 4
    },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#F2F5FC",
        backgroundColor: "#FFFFFF",
        borderRadius: 50,
        marginTop: 5
    },
    itemContentTextStyle: {
        marginLeft: 12,
        marginRight: 16,
        marginTop: 3,
        lineHeight: 24,
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
});