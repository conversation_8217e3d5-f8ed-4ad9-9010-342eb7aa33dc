import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Modal,Image ,Alert } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class HLStorageInDAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            storageInId:"",
            supplierName:"",
            storageInDate:"",
            operator:"",
            selSupplierId:"",
            selSupplierName:"",
            selSupplierAbbreviation:"",
            selectStorageInDate: [],
            supplierDataSource:[],
            _supplierDataSource:[],
            supplierAbbreviation:"",
            
            receiptRemark:"",
            usage:"",
            hlStorageInDetailDTOList:[],
            sourceId:"",
            supplierId:"",
            selSourceId:1,
            selSourceData:"",
            departmentId:"",
            backPeople:"",
            sourceDataSource:[
                {
                    sourceId:1,
                    sourceName:"采购入库",
                    sourceData:"1"
                },
                {
                    sourceId:2,
                    sourceName:"归还入库",
                    sourceData:"2"
                }
            ],
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageInId ,departmentId } = route.params;
            if (storageInId) {
                console.log("=============storageInId" + storageInId + "");
                this.setState({
                    storageInId:storageInId,
                    operate:"编辑",
                    modal: false
                    
                })
                let loadTypeUrl = "/biz/hl/storage/in/get";
                let loadRequest = { 'storageInId': storageInId };
                httpPost(loadTypeUrl, loadRequest, this.loadStorageInDDCallBack);
            }
            else{
                this.setState({
                    operate:"新增",
                    operator:constants.loginUser.userName,
                    modal: false
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectStorageInDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    storageInDate: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                })
            }
            if (departmentId) {
                console.log("=============departmentId" + departmentId + "");
                this.setState({
                    departmentId:departmentId,
                    
                })
            }
        }
        this.loadSupplierData();
    }

    loadStorageInDDCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectStorageInDate = response.data.storageInDate.split("-");
            this.setState({
                operator: response.data.operator,
                receiptRemark: response.data.receiptRemark,
                usage: response.data.usage,
                backPeople:response.data.backPeople,
                selSupplierName:response.data.supplierName,
                selSupplierId:response.data.supplierId,
                storageInDate:response.data.storageInDate,
                storageOutFlag:response.data.storageOutFlag,
                departmentId:response.data.departmentId,
                selectStorageInDate:selectStorageInDate,
                selSourceId:response.data.sourceId== '2' ? 2 : 1,
                sourceId:response.data.sourceId,
                sourceName:response.data.sourceId == '2' ? "归还入库" : "采购入库",
                
            })
            if (response.data.hlStorageInDetailDTOList && response.data.hlStorageInDetailDTOList.length > 0) {
                this.setState({
                    // 成品出库详细
                    hlStorageInDetailDTOList:response.data.hlStorageInDetailDTOList,
                })
            }
            // console.log("--------明细"+ hlStorageInDetailDTOList)
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("HLStorageInDList")
            }}>
                <Text style={CommonStyle.headRightText}>科室入库</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }
    saveStorageInDD = () => {
        console.log("=======saveStorageInDD");
        let toastOpts;
        if (!this.state.storageInDate) {
            toastOpts = getFailToastOpts("请选择入库日期");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.hlStorageInDetailDTOList || this.state.hlStorageInDetailDTOList.length < 1) {
            toastOpts = getFailToastOpts("至少新增一条明细");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.selSexId) {
        //     toastOpts = getFailToastOpts("请选择入库类型");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.supplierName) {
        //     toastOpts = getFailToastOpts("请填写供货单位");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url = "/biz/hl/storage/in/add";
        if (this.state.storageInId) {
            console.log("=========Edit===storageInId", this.state.storageInId)
            url = "/biz/hl/storage/in/modify";
        }

        let requestParams = {
            storageInId: this.state.storageInId,
            supplierId : this.state.selSourceId == 1 ? this.state.selSupplierId : null,
            operator: this.state.operator,
            receiptRemark: this.state.receiptRemark,
            supplierName: this.state.selSupplierName,
            storageInDate: this.state.storageInDate,
            hlStorageInDetailDTOList:this.state.hlStorageInDetailDTOList,
            unitId:this.state.unitId,
            storageOutFlag:"D",
            sourceId: this.state.selSourceId == 1 ? this.state.selSourceId : 2,
            departmentId:this.state.departmentId ? this.state.departmentId : null,
            backPeople: this.state.selSourceId == 2 ? this.state.backPeople : null,

        };
        httpPost(url, requestParams, this.saveStorageInDDCallBack);
    }

    // 保存回调函数
    saveStorageInDDCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    openStorageInDate() {
        this.refs.SelectStorageInDate.showDate(this.state.selectStorageInDate)
    }
    callBackSelectStorageInDateValue(value) {
        console.log("==========c出库日期选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectStorageInDate: value
        })
        if (value && value.length) {
            var storageInDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    storageInDate += vartime;
                }
                else{
                    storageInDate += "-" + vartime;
                }
            }
            this.setState({
                storageInDate: storageInDate
            })
        }
        var dateString = this.state.signingTime + ' 00:00:01';
        dateString = dateString.substring(0, 19);
        dateString = dateString.replace(/-/g, '/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var SevenDaysLast = new Date(dateStringTimestamp);
        // 用获取毫秒数 加上30天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        SevenDaysLast.setTime(dateStringTimestamp + (30 * 86400000));
        //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
        //格式化月，如果小于9，前面补0  
        var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0  
        var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
        this.setState({
            selectDeliveryDate: [SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
            deliveryDate: SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
        })
        if (this.state.selectDeliveryDate && this.state.selectDeliveryDate.length) {
            var deliveryDate = "";
            var vartime;
            for (var index = 0; index < this.state.selectDeliveryDate.length; index++) {
                vartime = this.state.selectDeliveryDate[index];
                if (index === 0) {
                    deliveryDate += vartime;
                }
                else {
                    deliveryDate += "-" + vartime;
                }
            }
            this.setState({
                deliveryDate: deliveryDate
            })
        }
    }

    _loadFreshHlStorageInDetailDTOList=(_hlStorageInDetailDTOList)=>{
        if (_hlStorageInDetailDTOList && _hlStorageInDetailDTOList.length > 0) {
            console.log("=========回退数据：", _hlStorageInDetailDTOList);
            this.setState({
                hlStorageInDetailDTOList:_hlStorageInDetailDTOList,
            })
        }
        else {
            console.log("=========回退不成功");
        }
    }

    //source来源 列表展示
    renderSourceRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selSourceId:item.sourceId,
                        // sourceName:item.sourceName,
                        // selSourceData:item.selSourceData,
                    })
                }}>
                <View key={item.sourceId} style={[item.sourceId===this.state.selSourceId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.sourceId===this.state.selSourceId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.sourceName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    
    //加载供应商
    loadSupplierData=()=>{
        let loadUrl= "/biz/portal/supplier/list";
        let loadRequest={'currentPage':1,'pageSize':100,'supplierType':"P"};
        httpPost(loadUrl, loadRequest, this.callBackLoadSupplierData);
    }

    callBackLoadSupplierData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                supplierDataSource:response.data.dataList,
                supplierId:response.data.supplierId,
            })
            // this.loadContractList(this.state.selCustomerId);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    //加载供应商
    loadSupplier = () => {
        var _supplierDataSource = copyArr(this.state.supplierDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _supplierDataSource = _supplierDataSource.filter(item => item.supplierName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _supplierDataSource: _supplierDataSource,
        })
    }
    renderSupplierRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                
                this.setState({
                    selSupplierId: item.supplierId,
                    selSupplierName: item.supplierName,
                    
                })
                
            }}>
                <View key={item.supplierId} style={[item.supplierId === this.state.selSupplierId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.supplierId === this.state.selSupplierId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.supplierName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    loadProtalSupplierListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                supplierDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    render(){
        return(
            <ScrollView style={[CommonStyle.contentViewStyle]}>
                <CommonHeadScreen title={this.state.operate + '入库'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={styles.inputRowStyle}>
                    <View style={[styles.rowLabView]}>
                        <Text style={styles.leftLabNameTextStyle}>入库类型</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.sourceDataSource && this.state.sourceDataSource.length > 0) 
                            ? 
                            this.state.sourceDataSource.map((item, index)=>{
                                return this.renderSourceRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                </View>
                {
                    this.state.selSourceId == 1  ?
                    <View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>供货单位</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                        </View>
                        <View style={[{flexWrap:'wrap'},(!this.state.supplierDataSource || this.state.supplierDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (this.state.supplierDataSource && this.state.supplierDataSource.length > 0) {
                                    this.setState({
                                        _supplierDataSource: copyArr(this.state.supplierDataSource),
                                    })
                                }
                                if (!this.state.supplierDataSource || this.state.supplierDataSource.length === 0) {
                                    let errorMsg = '请先添加供货单位';
                                    Alert.alert('确认', errorMsg, [
                                        {
                                            text: "确定", onPress: () => {
                                                WToast.show({ data: '点击了确定' });
                                            }
                                        }
                                    ]);
                                    return;
                                }
                                    this.setState({
                                    modal: true,
                                    searchKeyWord: ""
                                })
                                if (!this.state.selSupplierId && this.state.supplierDataSource && this.state.supplierDataSource.length > 0) {
                                    this.setState({
                                        selSupplierId: this.state.supplierDataSource[0].supplierId,
                                        selSupplierName: this.state.supplierDataSource[0].supplierName,
                                        // selSupplierAbbreviation: this.state.supplierDataSource[0].supplierAbbreviation,
                                        // contractId:"",
                                        // contractName:"",
                                        // selectContract:[],
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, {height:40, flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)',marginLeft:10}]}>
                                    {this.state.selSupplierId && this.state.selSupplierName ? 
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.selSupplierName}
                                        </Text>
                                        :
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        选择供货单位
                                        </Text>
                                    }
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    :
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>归还人</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={[styles.inputRightText]}
                            placeholder={'请输入归还人'}
                            onChangeText={(text) => this.setState({ backPeople: text })}
                        >
                            {this.state.backPeople}
                        </TextInput>
                    </View>
                }
                
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <View style={styles.leftLabViewImage}>
                                        <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                        <TextInput
                                            style={[styles.searchInputText]}
                                            returnKeyType="search"
                                            returnKeyLabel="搜索"
                                            onSubmitEditing={e => {
                                                this.loadSupplier();
                                        }}        
                                            placeholder={'供货单位'}
                                            onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                        >
                                            {this.state.searchKeyWord}
                                        </TextInput>
                                    </View>
                                    {/* <TouchableOpacity onPress={() => {
                                        this.loadSupplier();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity> */}
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._supplierDataSource && this.state._supplierDataSource.length > 0)
                                                ?
                                                this.state._supplierDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderSupplierRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {

                                        if (!this.state.selSupplierId) {
                                            let toastOpts = getFailToastOpts("您还没有选择供货单位");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        let loadUrl = "/biz/protal/supplier/list";
                                        let loadRequest = {
                                            "currentPage":1,
                                            "pageSize":1000,
                                            // 此处可能存在问题 yty
                                            "supplieId":this.state.selSupplierId,
                                            // "partyA": this.state.selCustomerId,
                                        };
                                        httpPost(loadUrl, loadRequest, this.loadProtalSupplierListCallBack);
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>入库日期</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TouchableOpacity onPress={() => this.openStorageInDate()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 30)}]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.storageInDate ? "请选择入库日期" : this.state.storageInDate}
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>
                
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>经办人</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput
                        //keyboardType='text'
                        style={[styles.inputRightText]}
                        placeholder={'请输入经办人'}
                        onChangeText={(text) => this.setState({ operator: text })}
                    >
                        {this.state.operator}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>备注说明</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <TextInput
                        //keyboardType='text'
                        style={[styles.inputRightText]}
                        placeholder={'请输入备注说明'}
                        onChangeText={(text) => this.setState({ receiptRemark: text })}
                    >
                        {this.state.receiptRemark}
                    </TextInput>
                </View>
                <View style={CommonStyle.rowSplitViewStyle}></View>
                <View style={styles.btnRowView}>
                    {/* <TouchableOpacity onPress={()=>{
                        //  WToast.show({data:"功能未开通"});
                        if (this.state.selCustomerName) {
                            this.props.navigation.navigate("HLStorageInAddDetail",
                            {
                                customerId:this.state.customerId,
                                // 传递回调函数
                                refresh: this._loadFreshSpStorageInDetailDTOList 
                            })
                        }
                        else {
                            WToast.show({data:"请先选择需货单位"});
                            return;
                        }
                     }}>
                         <View style={[styles.btnAddView]}>
                             <Text style={styles.btnAddText}>+ 出库明细</Text>
                         </View>
                    </TouchableOpacity> */}
                    <TouchableOpacity onPress={()=>{
                        if (this.state.selSourceId == 1) {
                            if (!this.state.selSupplierId) {
                                let toastOpts = getFailToastOpts("您还没有选择供货单位");
                                WToast.show(toastOpts);
                                return;
                            }
                    }

                        if (this.state.selSourceId == 2) {
                            if (!this.state.backPeople) {
                                let toastOpts = getFailToastOpts("您还没有填写归还人");
                                WToast.show(toastOpts);
                                return;
                            }
                            
                        }
                        
                        // if (this.state.storageInId) {
                            this.props.navigation.navigate("HLStorageInAddDetail", 
                        {
                            storageInFlag:"D",
                            // 传递参数
                            storageInId:this.state.storageInId,
                            hlStorageInDetailDTOList:this.state.hlStorageInDetailDTOList,
                            // 传递回调函数
                            refresh: this._loadFreshHlStorageInDetailDTOList 
                        })
                    // }
                    }}>
                         <View style={[styles.btnAddView]}>
                             <Text style={styles.btnAddText}>+ 入库明细</Text>
                         </View>
                    </TouchableOpacity>
                </View>
                <View>
                    <FlatList 
                    data={this.state.hlStorageInDetailDTOList}
                    renderItem={({item}) => 
                    <View key={item._index} style={styles.titleViewStyle}>

                        <View style={{ marginTop:10}}>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                名称：{item.materialName}
                            </Text>
                        </View>
                        <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10,marginTop:10}]}>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>规格型号：{item.materialModal}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>单位：{item.unitName}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>数量：{item.materialAmount}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>价格：{item.materialPrice}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>金额：{item.materialTotalPrice}</Text>
                            </View>
                        </View>

                        
                        <TouchableOpacity
                            style={{marginLeft:-145,zIndex:1000,marginTop:130}}
                            onPress={() => {
                                console.log("========deleteStorageInDetailDTO")
                                var urls = this.state.hlStorageInDetailDTOList;
                                urls.splice(item._index,1);                   
                                console.log(urls)
                                this.setState({
                                    hlStorageInDetailDTOList:urls,
                                })
                            }}
                        >
                            <View style={styles.btnDeleteView}>
                                <Text style={styles.btnDeleteText}>-删除</Text>
                            </View>
                        </TouchableOpacity>

                    </View>
                    }
                    />
                </View>
                <View style={CommonStyle.btnRowStyle}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveStorageInDD.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row', width: 130, height: 40, marginRight: 35, marginTop: 15 }]}>
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                <BottomScrollSelect
                    ref={'SelectStorageInDate'}
                    callBackDateValue={this.callBackSelectStorageInDateValue.bind(this)}
                />
            </ScrollView>
        )
    }
}
const styles = StyleSheet.create({
    searchInputText: {
        width: screenWidth -100,
        // borderColor: '#000000',
        // borderBottomWidth: 1,
        // marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        // marginLeft: 0,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabViewImage: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        borderWidth:1,
        borderColor:"#E4E4E4",
        borderRadius:5,
        marginTop:5,
        // marginRight:5
    },   
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        // marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },
    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight:30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})