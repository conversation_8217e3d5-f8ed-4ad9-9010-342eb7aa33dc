import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,TextInput,ScrollView,KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
var screenHeight = Dimensions.get('window').height;
export default class ExamConfigAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            examId:"",
            examDate:"",
            examTime:"",
            examPeoplesLimit:5,
            examRemark:"",
            examSort:0,
            timeDataSource:[
                {
                    timeId:1,
                    examTime:"10:00-12:00"
                },
                {
                    timeId:2,
                    examTime:"14:00-16:00"
                }
            ],
            selTimeId:1,
            selExamTime:"10:00-12:00",
            selectedExamDate:[],

        }
    }
    
    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { examId } = route.params;
            if (examId) {
                console.log("=============examId" + examId + "");
                this.setState({
                    examId:examId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/exam/config/get";
                loadRequest={'examId':examId};
                httpPost(loadTypeUrl, loadRequest, this.callBackLoadExamConfigData);
            }
            else{
                this.setState({
                    operate:"新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectedExamDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    examDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
    }

    callBackLoadExamConfigData=(response)=>{
        if (response.code == 200 && response.data) {
            var selectedExamDate = response.data.examDate.split("-");
            console.log("======load==edit=obj=", response.data);
            this.setState({
                examId:response.data.examId,
                examDate: response.data.examDate,
                examTime:response.data.examTime,
                selExamTime:response.data.examTime,
                examPeoplesLimit:response.data.examPeoplesLimit,
                examRemark:response.data.examRemark,
                examSort:response.data.examSort,
                selectedExamDate:selectedExamDate,
            })
        }
    }

    saveExamConfig =()=> {
        console.log("=======saveExamConfig");
        let toastOpts;
        if (!this.state.selTimeId) {
            toastOpts = getFailToastOpts("请选择考试时间");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.examDate) {
            toastOpts = getFailToastOpts("请选择考试日期");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.examPeoplesLimit) {
            toastOpts = getFailToastOpts("请输入考场人数");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.examSort) {
        //     toastOpts = getFailToastOpts("请输入排序");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url= "/biz/exam/config/add";
        if (this.state.examId) {
            console.log("=========Edit===examId", this.state.examId)
            url= "/biz/exam/config/modify";
        }
        let requestParams={
            examId:this.state.examId,
            examDate:this.state.examDate,
            examTime:this.state.selExamTime,
            examPeoplesLimit:this.state.examPeoplesLimit,
            examRemark:this.state.examRemark,
            examSort:this.state.examSort,
        };
        httpPost(url, requestParams, this.saveExamConfigCallBack);
    }

    // 保存回调函数
    saveExamConfigCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
        }
    }
        
    openExamDate(){
        this.refs.SelectExamDate.showDate(this.state.selectedExamDate)
    }

    callBackSelectSelectExamDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedExamDate:value
        })
        if (value && value.length) {
            var examDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    examDate += vartime;
                }
                else{
                    examDate += "-" + vartime;
                }
            }
            this.setState({
                examDate:examDate
            })
        }
    }

    //考试时间展示
    renderRow=(item)=>{
    return (
        <TouchableOpacity onPress={() => {
                this.setState({
                    selTimeId:item.timeId,
                    selExamTime:item.examTime
                })
                console.log("=======  " +item.examTime);
            }}>
                
            <View key={item.timeId} style={[item.examTime===this.state.selExamTime ? [CommonStyle.selectedBlockItemViewStyle,{paddingLeft:5, paddingRight:5}]
                : [CommonStyle.blockItemViewStyle,{paddingLeft:5, paddingRight:5}]] }>
                <Text style={item.examTime===this.state.selExamTime ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                    {item.examTime}
                </Text>
            </View>
        </TouchableOpacity>
    )
}

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("ExamConfig")
            }}>
                <Text style={CommonStyle.headRightText}>考试设置</Text>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]}  behavior="padding">
                <CommonHeadScreen title={this.state.operate + '考试设置'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
            <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>考试日期</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TouchableOpacity onPress={()=>this.openExamDate()}>
                                <View style={CommonStyle.inputTextStyleTextStyle}>
                                    <Text style={{color:'#A0A0A0', fontSize:15}}>
                                        {!this.state.examDate ? "请选择考试日期" : this.state.examDate}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考试时间</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.timeDataSource && this.state.timeDataSource.length > 0) 
                                ? 
                                this.state.timeDataSource.map((item, index)=>{
                                    return this.renderRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>          
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>考场人数</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            keyboardType='numeric'
                            placeholder={'请输入考场人数'}
                            onChangeText={(text) => this.setState({examPeoplesLimit:text})}
                        >
                            {this.state.examPeoplesLimit}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>注意事项</Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle,{height:150}]}>
                            <TextInput 
                                multiline={true}
                                textAlignVertical="top"
                                style={[CommonStyle.inputRowText,{height:150}]}
                                placeholder={'请输入考试注意事项'}
                                onChangeText={(text) => this.setState({examRemark:text})}
                            >
                                {this.state.examRemark}
                            </TextInput>
                        </View>
                        <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({ examSort: text })}
                        >
                            {this.state.examSort}
                        </TextInput>
                    </View>   
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveExamConfig.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                            <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>                                  
            </ScrollView>   
            <BottomScrollSelect 
                    ref={'SelectExamDate'} 
                    callBackDateValue={this.callBackSelectSelectExamDateValue.bind(this)}
                />
        </KeyboardAvoidingView>
       
        )
    }
}
const styles = StyleSheet.create({ 
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    leftLabNameTextStyle:{
        fontSize:18
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
    });