import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Image,TextInput,
    FlatList, RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class AuditPointAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate: "",
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            nodeId: "",
            nodeName: "",
            nodeSort: "",
            userId: "",
            userName: "",
            selectAuditStaff:[],
            auditStaffDataSource:[],
            fillDataSource:[],
            fillDataIdList:[0],
            selFillDataIdList: [],
            auditConfigId:"",
            auditTypeCode:""
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }


    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        // 加载审核人列表
        this.loadAuditStaffList();
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { nodeId, auditTypeCode,auditConfigId} = route.params;
            if (nodeId) {
                console.log("=============nodeId" + nodeId + "");
                this.setState({
                    nodeId: nodeId,
                    operate: "编辑"
                })
                let loadTypeUrl = "/biz/audit/node/get";
                let loadRequest = { 'nodeId': nodeId };
                httpPost(loadTypeUrl, loadRequest, this.loadAuditPointCallBack);

            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
            if (auditTypeCode) {
                console.log("=============auditTypeCode" + auditTypeCode + "");
                this.setState({
                    auditTypeCode: auditTypeCode
                })
                // 通过审核类型获取补充数据列表
                this.loadFillDataByType(auditTypeCode);
            }
            if (auditConfigId) {
                console.log("=============auditConfigId" + auditConfigId + "");
                this.setState({
                    auditConfigId: auditConfigId
                })
            }
        }
    }

    loadAuditPointCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                selectAuditStaff:[response.data.userName],
                nodeId: response.data.nodeId,
                nodeSort: response.data.nodeSort,
                nodeName: response.data.nodeName,
                userId:response.data.userId,
                userName: response.data.userName,
                fillDataId: response.data.fillDataId,
            })
            var fillDataIdList = [];
            if(response.data.fillDataIdList && response.data.fillDataIdList.length > 0) {
                for(var i = 0; i < response.data.fillDataIdList.length; i++) {
                    fillDataIdList = fillDataIdList.concat(response.data.fillDataIdList[i].fillDataId);
                }
                console.log("fillDataIdList",fillDataIdList);
                this.setState({
                    fillDataIdList:fillDataIdList
                })    
            }

            // var list = [];
            // if (response.data.fillDataId) {
            //     var fillDataIdList = response.data.fillDataId.split(",")
            //     console.log("====fillDataIdList====" + fillDataIdList)
            //     for (var i = 0; i < fillDataIdList.length; i++) {
            //         list = list.concat(fillDataIdList[i])
            //     }
            //     console.log(list)
            //     this.setState({
            //         fillDataIdList: list
            //     })
            // }
        }
    }

    // 通过审核类型获取补充数据列表
    loadFillDataByType = (auditTypeCode) => {
        let loadUrl = "/biz/audit/fill/data/list";
        let loadRequest = { "auditTypeCode": auditTypeCode, 'currentPage': 1, 'pageSize': 50 };
        console.log("==========loadRequest", loadRequest)
        httpPost(loadUrl, loadRequest, this.loadFillDataByNodeIdCallBack);
    }

    loadFillDataByNodeIdCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            response.data.dataList.unshift({"fillDataId":0,"fillData":null,"fillDataDesc":"不补充"})
            this.setState({
                fillDataSource: response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    loadAuditStaffList=()=>{
        let loadTypeUrl= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(loadTypeUrl, loadRequest, this.loadAuditStaffListCallBack);
    }

    loadAuditStaffListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                auditStaffDataSource:response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    
    openAuditStaffSelect(){
        if (!this.state.auditStaffDataSource || this.state.auditStaffDataSource.length < 1) {
            WToast.show({data:"请先添加审核人"});
            return
        }
        this.refs.SelectAuditStaff.showAudit(this.state.selectAuditStaff, this.state.auditStaffDataSource)
    }
    callBackAuditStaffValue(value){
        console.log("==========审核人选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectAuditStaff:value
        })
        var userName= value.toString();
        let loadUrl= "/biz/audit/node/getAuditStaffByName";
        let loadRequest={
            "userName":userName
        };
        httpPost(loadUrl, loadRequest, this.callBackAuditStaffData);
    }

    callBackAuditStaffData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                userName:response.data.userName,
                userId:response.data.userId,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    saveNode = () => {
        console.log("=======saveNode");
        let toastOpts;
        if (!this.state.nodeName) {
            toastOpts = getFailToastOpts("请输入节点名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.userName) {
            toastOpts = getFailToastOpts("请选择审核人");
            WToast.show(toastOpts)
            return;
        }
        var fillDataIdList = [];
        if(this.state.fillDataIdList && this.state.fillDataIdList.length > 0){
            console.log("this.state.fillDataIdList",this.state.fillDataIdList);
            for(var m = 0; m < this.state.fillDataIdList.length; m++) {
                if(this.state.fillDataIdList[m] != 0) {
                    var fillDataDto = {
                        "fillDataId":this.state.fillDataIdList[m]
                    }
                    fillDataIdList = fillDataIdList.concat(fillDataDto);    
                }
            }
            console.log("fillDataIdList",fillDataIdList);
        }
        let url = "/biz/audit/node/add";
        // if (this.state.nodeId) {
        //     console.log("=========Edit===nodeId", this.state.nodeId)
        //     url = "/biz/audit/node/modify";
        // }
        let requestParams = {
            nodeId: this.state.nodeId,
            nodeName: this.state.nodeName,
            userId: this.state.userId,
            nodeSort: this.state.nodeSort,
            auditConfigId:this.state.auditConfigId,
            fillDataIdList:fillDataIdList
        };
        httpPost(url, requestParams, this.saveNodeCallBack);
    }

    // 保存回调函数
    saveNodeCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
            //     <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("AuditPointList",
            //         {
            //             // 传递回调函数
            //             refresh: this.callBackFunction
            //         })
            // }}>
            //     <Text style={CommonStyle.headRightText}>节点设置</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>节点设置</Text>
                </TouchableOpacity>
            </View>
        )
    }

    compare=(fillDataId)=>{
        for(var i=0;i<this.state.fillDataIdList.length;i++){
            if(this.state.fillDataIdList[i] == fillDataId){
                return true;
            }
        }
        return false;
    }

    // 补充数据单项渲染
    renderFillDataRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                var fillDataIdList = this.state.fillDataIdList;
                if (this.compare(item.fillDataId)) {
                    if(item.fillDataId == 0) {
                        return;
                    }
                    arrayRemoveItem(fillDataIdList, item.fillDataId);
                    if(!fillDataIdList || fillDataIdList.length <= 0){
                        console.log("dddddddddd");
                        this.setState({
                            fillDataIdList:[0]
                        })
                        return;
                    }
                }
                else {
                    fillDataIdList = fillDataIdList.concat(item.fillDataId);
                    if(item.fillDataId == 0) {
                        this.setState({
                            fillDataIdList:[0],
                        })
                        return;
                    }
                    else {
                        arrayRemoveItem(fillDataIdList, 0);
                    }
                }
                this.setState({
                    fillDataIdList:fillDataIdList,
                })
                WToast.show({data:'点击了' + item.fillDataDesc});
                console.log("======fillDataIdList:", fillDataIdList)
            }}>
                <View key={item.fillDataId} style={this.compare(item.fillDataId) ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={this.compare(item.fillDataId) ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.fillDataDesc}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '节点'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <View style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>节点名称</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({nodeName:text})}
                        >
                            {this.state.nodeName}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>审核人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={()=>this.openAuditStaffSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 25),borderWidth:0}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.userName ? "请选择" : this.state.userName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>补充数据</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            this.state.fillDataSource.map((item, index)=>{
                                return this.renderFillDataRow(item)
                            })
                        }
                    </View>  
                    <View style={CommonStyle.lineBorderBottomStyle} />
                  
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'0'}
                            onChangeText={(text) => this.setState({nodeSort:text})}
                        >
                            {this.state.nodeSort}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    <View style={{height:ifIphoneXContentViewHeight()-204-95, backgroundColor:'#F2F5FC'}}>
                        
                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{ marginTop:0 }]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveNode.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <BottomScrollSelect 
                            ref={'SelectAuditStaff'} 
                            callBackAuditStaffValue={this.callBackAuditStaffValue.bind(this)}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle: {
        marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 14,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    bodyViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 8,
        marginTop: 8
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:6,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 25),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },


});