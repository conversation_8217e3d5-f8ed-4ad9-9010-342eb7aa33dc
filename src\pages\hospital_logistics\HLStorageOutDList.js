import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,
    FlatList,RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class HLStorageOutDList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            departmentId:""
        }
    }

      //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        //判断是否为库管员，并获取科室ID
        this.loadKeepRel();

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }


    }

    loadKeepRel=()=>{
        let url= "/biz/hl/department/store/keeper/rel/keepRel";
        let loadRequest={
            "userId":constants.loginUser.userId,
        };
        httpPost(url, loadRequest, this.loadKeepRelCallBack);
    }

    loadKeepRelCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                departmentId:response.data.departmentId,
            })

            //加载列表
            this.loadStorageOutDDList(response.data.departmentId);
        }
        else{
            this.loadStorageOutDDList();
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/hl/storage/out/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "storageOutFlag":"D",
            "departmentId":this.state.departmentId ? this.state.departmentId : null,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/hl/storage/out/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "storageOutFlag":"D",
            "departmentId":this.state.departmentId ? this.state.departmentId : null,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadStorageOutDDList();
    }
    
    loadStorageOutDDList=(departmentId)=>{
        let url= "/biz/hl/storage/out/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "storageOutFlag":"D",
            "departmentId": departmentId ? departmentId : (this.state.departmentId ? this.state.departmentId : null),
        };
        httpPost(url, loadRequest, this.loadStorageOutDDListCallBack);
    }

    loadStorageOutDDListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteStorageOutDD =(storageOutId)=> {
        console.log("=======delete=storageOutId", storageOutId);
        let url= "/biz/hl/storage/out/delete";
        let requestParams={'storageOutId':storageOutId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
               <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View>
                {
                this.state.departmentId ? 
                <TouchableOpacity onPress={() => {
                    this.props.navigation.navigate("HLStorageOutDAdd", 
                    {
                        departmentId:this.state.departmentId ? this.state.departmentId :null,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                })
                    }}>
                    {/* <Text style={CommonStyle.headRightText}>新增模版</Text> */}
                    <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
                : <View />
  
                }
            </View>
            
        )
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.kilnCarId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>科室名称：{item.departmentDName}</Text>
                    <View style={{position:'absolute',right:0,top:0}}>
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, borderRadius:12, 
                            backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>{item.usageName}</Text>
                    </View>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>领用人：{item.recipient}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>用途：{item.usageName}</Text>
                </View> */}
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>经办人：{item.operator}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>出库日期：{item.storageOutDate}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>备注说明：{item.receiptRemark ? item.receiptRemark:"无"}</Text>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    {
                        item.storageOutUsage ==="D" ?
                        <TouchableOpacity onPress={()=>{
                            if(item.storageOutState === '0AB'){
                                return
                            }
                            let url= "/biz/hl/storage/out/returnDAdd";
                            let loadRequest={
                                "storageOutId": item.storageOutId,
                                "sourceId": 2,
                                "operator":constants.loginUser.userName,
                            };
                            httpPost(url, loadRequest, (response)=>{
                                if (response.code == 200 && response.data) {
                                    this.callBackFunction()
                                }
                                else if (response.code == 401) {
                                    WToast.show({data:response.message});
                                    this.props.navigation.navigate("LoginView");
                                }
                            });
                        }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:95,marginLeft:0 ,flexDirection:'row'},(item.storageOutState === '0AB')? CommonStyle.disableViewStyle : ""]}>
                            <Image  style={{width:22, height:22,marginRight:1}} source={require('../../assets/icon/iconfont/giveBack.png')}></Image>
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>{item.storageOutState === '0AB'?"已归还":"归还入库"}</Text>
                            </View>
                        </TouchableOpacity>
                    :<View/>
                    }
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("HLStorageOutListDetail", 
                            {
                                // 传递参数
                                storageOutId:item.storageOutId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {backgroundColor:"#3ab240",marginLeft:0 ,width: 70 ,flexDirection:"row"}]}>
                        <Image  style={{width:25, height:25,marginRight:1}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>明细</Text>
                        </View>
                    </TouchableOpacity>
                    
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该出库信息吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteStorageOutDD(item.storageOutId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:70,flexDirection:'row',marginLeft:0 }]}>
                        <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    {/* <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("HLStorageOutDAdd", 
                            {
                                // 传递参数
                                storageOutId:item.storageOutId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,flexDirection:'row',marginLeft:0 }]}>
                        <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity> */}
                    
                    
                </View>
            </View>
        )
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='科室出库'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },

});