import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image, TextInput,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ScrollView } from 'react-native-gesture-handler';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
import <PERSON>Viewer from 'react-native-image-zoom-viewer';
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import EmptyPortalTenantComponent from '../../component/EmptyPortalTenantComponent';

const leftLabWidth = 130;
export default class PortalTenantParamItem extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord: 1,
            topBlockLayoutHeight: 0,
            pictureIndex:0,
            isShowImage:false,
            image:"",
            bannerConfigList:["RESUME_SHARE_WEBSITE_BANNER", "ENTERPRISE_SHARE_WEBSITE_BANNER", "MEMBER_SHARE_WEBSITE_BANNER"],

            imageConfigList:["WECHAT_SHARE_PAGE_BOTTOM_IMAGE","RESUME_SHARE_LOGO","ENTERPRISE_SHARE_LOGO","MEMBER_SHARE_LOGO","MEMBER_SHARE_CARD_LOGO",
            "MEMBER_SHARE_WEBSITE_NOTIFICATION_PICTURE","ALI_PAY_QR_CODE","WECHAT_PAY_QR_CODE","PAY_QR_CODE", "APPLY_CHARGE_PERSON_WECHAT_QR_CODE"],
            
            textConfigList:["ADMISSION_TICKET_NOTICE","CHARGE_DESCRIPTION",
            "CONTACT_WEB_URL","WECHAT_SHARE_PAGE_TITLE","WECHAT_SHARE_PAGE_FINISHED_WORK_TITLE"
            ,"WECHAT_SHARE_PAGE_WORK_PLAN_TITLE","WECHAT_SHARE_PAGE_BOTTOM_LINK","RESUME_SHARE_TITLE","RESUME_SHARE_SUB_TITLE",
            "RESUME_SHARE_WEBSITE_TITLE","RESUME_SHARE_WEBSITE_NOTIFICATION","ENTERPRISE_SHARE_TITLE","ENTERPRISE_SHARE_SUB_TITLE",
            "ENTERPRISE_SHARE_WEBSITE_TITLE","ENTERPRISE_SHARE_WEBSITE_NOTIFICATION","MEMBER_SHARE_TITLE","MEMBER_SHARE_SUB_TITLE",
            "MEMBER_SHARE_WEBSITE_TITLE","MEMBER_SHARE_WEBSITE_NOTIFICATION_WORD","MEMBER_SHARE_WEBSITE_APPLY_NOTIFICATION_SUCCESS",
            "MEMBER_SHARE_WEBSITE_APPLY_NOTIFICATION_CONTACT", "MEMBER_SHARE_WEBSITE_CONTACT_PLATFORM_LINK", "MEMBER_SHARE_WEBSITE_NOTIFICATION_LINK"],
            
            noteConfigList:["MEMBER_CONTACT_US_IMAGE_AND_TEXT","MEMBER_CONTACT_US_LINK","MEMBER_CONTACT_PLATFORM_IMAGE_AND_TEXT","MEMBER_CONTACT_PLATFORM_LINK"],
            resourceConfigList: ["MEMBER_SHARE_WEBSITE_HOME_RESOURCE_DISPLAY"],

            contactConfigList:["MEMBER_SHARE_WEBSITE_CONTACT_US","MEMBER_SHARE_WEBSITE_CONTACT_PLATFORM"],
            
            moreModal:false,
            deleteModal:false,
            editModal:false,
            bizModuleCode:null,
            paramName:null,
            ContactUsConfigData: {},
            ContactPlatformConfigData: {},
            homeResourceDisplayFlag: false

        }
    }

 


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { bizModuleCode, paramName} = route.params;
            if (paramName) {
                this.setState({
                    paramName: paramName
                })
            }
            if (bizModuleCode) {
                this.setState({
                    bizModuleCode: bizModuleCode,
                })
                this.loadPortalTenantConfigList(bizModuleCode);        
            }
        }
        this.loadHomeResourceDisplayConfig();
        this.loadContactUsConfig();
        this.loadContactPlatformConfig();
    }

    loadPortalTenantConfigList=(bizModuleCode)=>{
        let url= "/biz/config/item/param/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "bizModuleCode": bizModuleCode ? bizModuleCode : this.state.bizModuleCode,
            "tenantId":constants.loginUser.tenantId,
            "isConfigItemSetting": "N" // 判断是否为配置项设置
        };
        console.log("========bizModuleCode",bizModuleCode);
        httpPost(url, loadRequest, this.loadPortalTenantConfigListCallBack);
    }

    loadPortalTenantConfigListCallBack=(response)=>{
        console.log("==================")
        if (response.code == 200 && response.data && response.data.dataList) {
            // console.log("======",response.data);
            // var dataNew = response.data.dataList;
            // var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            // var dataAll = [...dataOld,...dataNew];
            console.log("======dataAll",response.data.dataList);
            this.setState({
                dataSource:response.data.dataList,
                // currentPage:response.data.currentPage + 1,
                // totalPage:response.data.totalPage,
                // totalRecord:response.data.totalRecord,
                refreshing:false,
            })
            // console.log("======",dataAll)
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        // console.log("+++++++dataSource",dataSource);
        // console.log("打印2........................")
    }

    // 加载首页资源展示数据
    loadHomeResourceDisplayConfig=()=>{
        let url= "/biz/tenant/config/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "paramCode": "MEMBER_SHARE_WEBSITE_HOME_RESOURCE_DISPLAY",
            "tenantId": constants.loginUser.tenantId,
        };
        httpPost(url, loadRequest, this.loadHomeResourceDisplayConfigCallBack);
    }
    loadHomeResourceDisplayConfigCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log("loadHomeResourceDisplayConfig======", response.data)
            this.setState({
                homeResourceDisplayFlag: true,
            })
        }
        if (response.code == 200 && response.data.dataList.length == 0) {
            this.setState({
                homeResourceDisplayFlag: false,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 加载会员库联系我们数据
    loadContactUsConfig = () => {
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10000,
            "isManagerQry": "Y",
            "advertisingState": "0AA",
            "advertisingType": "E"
        };
        httpPost(url, loadRequest, this.loadContactUsConfigCallBack);
    }

    loadContactUsConfigCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log('loadContactUsConfig...........', response.data.dataList)
            var dataList = response.data.dataList;
            this.setState({
                ContactUsConfigData: dataList[0]
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 加载会员库联系平台数据
    loadContactPlatformConfig = () => {
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10000,
            "isManagerQry": "Y",
            "advertisingState": "0AA",
            "advertisingType": "P"
        };
        httpPost(url, loadRequest, this.loadContactPlatformConfigCallBack);
    }

    loadContactPlatformConfigCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log('loadContactPlatformConfig...........', response.data.dataList)
            var dataList = response.data.dataList;
            this.setState({
                ContactPlatformConfigData: dataList[0]
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        // if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
        //     console.log("==========不刷新=====");
        //     return;
        // }
        this.setState({
            currentPage:1
        })
        let url= "/biz/config/item/param/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "bizModuleCode": this.state.bizModuleCode,
            "tenantId":constants.loginUser.tenantId,
            "isConfigItemSetting": "N" // 判断是否为配置项设置
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

       // 回调函数
       callBackFunction = ()=>{
        let url= "/biz/config/item/param/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "bizModuleCode": this.state.bizModuleCode,
            "tenantId":constants.loginUser.tenantId,
            "isConfigItemSetting": "N" // 判断是否为配置项设置
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
        this.loadHomeResourceDisplayConfig();
        this.loadContactUsConfig();
        this.loadContactPlatformConfig();
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadPortalTenantConfigList();
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity> 
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("PortalTenantParamAdd", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            
            // </TouchableOpacity>
        )
    }


    // deletePortalTenantParam =(paramCode)=> {
    //     console.log("=======delete=paramCode", paramCode);
    //     let url= "/biz/tenant/config/delete";
    //     let requestParams={'paramCode':paramCode};
    //     httpDelete(url, requestParams, this.deleteCallBack);
    // }

    // // 删除操作的回调操作
    // deleteCallBack=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         WToast.show({data:"删除完成"});
    //         this.callBackFunction();
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    //     else {
    //         WToast.show({data:response.message});
    //     }
    // }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyPortalTenantComponent/>
    }

    renderRow=(item, index)=>{
        console.log("renderRow",index,JSON.stringify(item, null, 6))
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate(
                    item.paramCode == "MEMBER_SHARE_WEBSITE_HOME_RESOURCE_DISPLAY" ?
                        "HomeResourceDisplay"
                        :
                        item.paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_US" || item.paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_PLATFORM" ?
                            "MemberContactConfig"
                            :
                            "PortalTenantParamData"
                    ,
                    {
                        // 传递参数
                        paramName: item.paramName,
                        paramCode: item.paramCode,
                        paramValue: item.configParamValue,
                        homeResourceDisplayFlag: this.state.homeResourceDisplayFlag,
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                
            <View key={item.paramCode} style={{backgroundColor:'rgba(255, 255, 255, 1)',paddingTop:10}}>
                <View style={styles.titleViewStyle}>
                    <Text style={{fontWeight: "600", fontSize: 18,color:'rgba(0,10,32,1)',fontFamily:'PingFangSC',lineHeight:22}}>{item.paramName}</Text>
                    <View style={[styles.titleViewStyle, { position: 'absolute', right: 0, top: 0, flexDirection: 'column',marginRight:15}]}>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                moreModal: true,
                            })
                        }}>
                            {/* <View style={[{ width: 35, height: 35, alignItems: 'flex-end' }]}>
                                <Image style={{ width: 28, height: 28,alignItems: 'flex-end' }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                            </View> */}
                        </TouchableOpacity>
                    </View>
                </View>
                
                {
                    item.configParamValue ? 
                        this.state.imageConfigList.includes(item.paramCode) ?
                            <View>
                                <View style={[{ width: 120, height: 130, marginLeft: 10, display: 'flex'}]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            image:item.configParamValue,
                                            isShowImage:true,
                                        })
                                    }}>
                                        <Image source={{ uri: (constants.image_addr + '/' + item.configParamValue) }} style={{ height: 120, width:90,marginTop:10 }} />                                                    
                                    </TouchableOpacity>
                                    <Modal visible={this.state.isShowImage} transparent={true}>
                                        <ImageViewer saveToLocalByLongPress={false} onClick={()=>{this.setState({isShowImage:false})}} 
                                        enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} onSave={() => alert("点击了保存图片")} 
                                        onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={[{url:(constants.image_addr + '/' + this.state.image)}]} />
                                    </Modal>
                                </View>
                            </View>
                            : 
                            this.state.bannerConfigList.includes(item.paramCode) ?
                                <View>
                                    <View style={[{ width: screenWidth - 36 ,height: 90,marginLeft:10,marginRight:10,display:'flex'}]}>
                                        <TouchableOpacity onPress={() => {
                                            this.setState({
                                                image:item.configParamValue,
                                                isShowImage:true,
                                            })
                                        }}>
                                            <Image source={{ uri: (constants.image_addr + '/' + item.configParamValue) }} style={{ width: screenWidth - 36 , height:80, resizeMode: 'contain',marginTop:10 }} />                                                    
                                        </TouchableOpacity>
                                        <Modal visible={this.state.isShowImage} transparent={true}>
                                            <ImageViewer saveToLocalByLongPress={false} onClick={()=>{this.setState({isShowImage:false})}} 
                                            enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} onSave={() => alert("点击了保存图片")} 
                                            onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={[{url:(constants.image_addr + '/' + this.state.image)}]} />
                                        </Modal>
                                    </View>
                                </View>
                                :
                                this.state.textConfigList.includes(item.paramCode) ?
                                    <View style={styles.titleViewStyle}>
                                        <Text style={{fontSize:14,fontWeight:'400',color:'rgba(0,10,32,0.65)'}}>{item.configParamValue ? item.configParamValue : "暂未配置"}</Text>
                                    </View>
                                    : 
                                    this.state.noteConfigList.includes(item.paramCode) ?
                                        <View style={styles.titleViewStyle}>
                                            <Text style={{fontSize:14,fontWeight:'400',color:'rgba(0,10,32,0.65)'}}>{item.configParamValue ? item.configParamValue : "暂未配置"}</Text>
                                        </View>
                                        :
                                        this.state.resourceConfigList.includes(item.paramCode) ?
                                            <View style={styles.titleViewStyle}>
                                                <Text style={{ fontSize: 14, fontWeight: '400', color: 'rgba(0,10,32,0.65)' }}>
                                                    {
                                                        this.state.homeResourceDisplayFlag ?
                                                            ("已展示" + (item.configParamValue.split(",").map(Number).length) + "个组织")
                                                            :
                                                            "暂未配置"
                                                    }
                                                </Text>
                                            </View>
                                            :
                                            item.paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_US" ?
                                                <View style={styles.titleViewStyle}>
                                                    <Text style={{ fontSize: 14, fontWeight: '400', color: 'rgba(0,10,32,0.65)' }}>
                                                        {
                                                            this.state.ContactUsConfigData ?
                                                                (
                                                                    (!this.state.ContactUsConfigData.advertisingLink && this.state.ContactUsConfigData.advertisingContent != "<p></p>") ?
                                                                        "已配置图文"
                                                                        :
                                                                        (this.state.ContactUsConfigData.advertisingLink && this.state.ContactUsConfigData.advertisingContent == "<p></p>") ?
                                                                            "已配置链接"
                                                                            :
                                                                            "暂未配置"
                                                                )
                                                                : "暂未配置"
                                                        }
                                                    </Text>
                                                </View>
                                                :
                                                item.paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_PLATFORM" ?
                                                    <View style={styles.titleViewStyle}>
                                                        <Text style={{ fontSize: 14, fontWeight: '400', color: 'rgba(0,10,32,0.65)' }}>
                                                            {
                                                                this.state.ContactPlatformConfigData ?
                                                                    (
                                                                        (!this.state.ContactPlatformConfigData.advertisingLink && this.state.ContactPlatformConfigData.advertisingContent != "<p></p>") ?
                                                                            "已配置图文"
                                                                            :
                                                                            (this.state.ContactPlatformConfigData.advertisingLink && this.state.ContactPlatformConfigData.advertisingContent == "<p></p>") ?
                                                                                "已配置链接"
                                                                                :
                                                                                "暂未配置"
                                                                    )
                                                                    : "暂未配置"
                                                            }
                                                        </Text>
                                                    </View>
                                                    :
                                                    <View style={styles.titleViewStyle}>
                                                        <Text style={{fontSize:14,fontWeight:'400',color:'rgba(0,10,32,0.65)'}}>{"暂未配置"}</Text>
                                                    </View>
                        : 
                        (
                            item.paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_US" ?
                            <View style={styles.titleViewStyle}>
                                <Text style={{ fontSize: 14, fontWeight: '400', color: 'rgba(0,10,32,0.65)' }}>
                                    {
                                        this.state.ContactUsConfigData ?
                                            (
                                                (!this.state.ContactUsConfigData.advertisingLink && this.state.ContactUsConfigData.advertisingContent != "<p></p>") ?
                                                    "已配置图文"
                                                    :
                                                    (this.state.ContactUsConfigData.advertisingLink && this.state.ContactUsConfigData.advertisingContent == "<p></p>") ?
                                                        "已配置链接"
                                                        :
                                                        "暂未配置"
                                            )
                                            : "暂未配置"
                                    }
                                </Text>
                            </View>
                            :
                            item.paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_PLATFORM" ?
                                <View style={styles.titleViewStyle}>
                                    <Text style={{ fontSize: 14, fontWeight: '400', color: 'rgba(0,10,32,0.65)' }}>
                                        {
                                            this.state.ContactPlatformConfigData ?
                                                (
                                                    (!this.state.ContactPlatformConfigData.advertisingLink && this.state.ContactPlatformConfigData.advertisingContent != "<p></p>") ?
                                                        "已配置图文"
                                                        :
                                                        (this.state.ContactPlatformConfigData.advertisingLink && this.state.ContactPlatformConfigData.advertisingContent == "<p></p>") ?
                                                            "已配置链接"
                                                            :
                                                            "暂未配置"
                                                )
                                                : "暂未配置"
                                        }
                                    </Text>
                                </View>
                                :
                                item.paramCode == "MEMBER_SHARE_WEBSITE_HOME_RESOURCE_DISPLAY" ?
                                    <View style={styles.titleViewStyle}>
                                        <Text style={{ fontSize: 14, fontWeight: '400', color: 'rgba(0,10,32,0.65)' }}>
                                            {
                                                this.state.homeResourceDisplayFlag ?
                                                    "已展示0个组织"
                                                    :
                                                    "暂未配置"
                                            }
                                        </Text>
                                    </View>
                                    :
                                    <View style={styles.titleViewStyle}>
                                        <Text style={{fontSize:14,fontWeight:'400',color:'rgba(0,10,32,0.65)'}}>{"暂未配置"}</Text>
                                    </View>
                        )

                }
                

                
                <View style={styles.lineViewStyle}/> 

                {/* 更多操作弹窗Modal */}
                {/* <Modal
                        animationType='fade'
                        transparent={true}
                        visible={this.state.moreModal}
                        onRequestClose={() => console.log('onRequestClose...')}
                    >
                        <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.08)' }]}>
                            <View style={{ width: 291, bottom: screenHeight / 2 - 30, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                                <View>
                                    <TouchableOpacity onPress={() => {
                                        this.props.navigation.navigate("PortalTenantParamData",
                                            {
                                                refresh: this.callBackFunction
                                            })
                                        this.setState({
                                            moreModal: false,
                                        })
                                    }}>
                                        <View style={{ width: 145, height: 50, paddingLeft: 30, marginTop: 5 }}>
                                            <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>查看配置内容</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                
                                <View style={{ width: 291, height: 50, alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8' }}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            moreModal: false
                                        });
                                        WToast.show({ data: '点击了取消' });
                                    }}>
                                        <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                            <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal> */}
                
            </View>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.paramName+"配置"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                    
                    <View style={{backgroundColor: 'rgba(242, 245, 252, 1)', height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}}>
                        <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    titleViewStyle:{
        flexDirection:'row',
        // justifyContent:'space-between',
        marginLeft:12,
        marginRight:16,
        // marginBottom:5,
        marginTop:8,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    lineViewStyle:{
        // height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 10,
        marginBottom: 5,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },

});