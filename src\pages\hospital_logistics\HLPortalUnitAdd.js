import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class HLPortalUnitAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            unitId:"",
            unitName:"",
            unitCode:"",
            unitSort:0,
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { unitId } = route.params;
            if (unitId) {
                console.log("=============unitId" + unitId + "");
                this.setState({
                    unitId:unitId,
                    operate:"编辑",
                })
                let loadTypeUrl = "/biz/portal/unit/get";
                let loadRequest = { 'unitId': unitId };
                httpPost(loadTypeUrl, loadRequest, this.loadHLPortalUnitCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }

    loadHLPortalUnitCallBack = (response) => {
        if (response.code == 200 && response.data) {

            this.setState({
                unitName: response.data.unitName,
                unitCode: response.data.unitCode,
                unitSort: response.data.unitSort,
            })
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLPortalUnitList")
            }}>
                <Text style={CommonStyle.headRightText}>单位设置</Text>
            </TouchableOpacity>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveHLPortalUnit = () => {
        console.log("=======saveHLPortalUnit");
        let toastOpts;
        if (!this.state.unitName) {
            toastOpts = getFailToastOpts("请填写单位名称");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.unitCode) {
        //     toastOpts = getFailToastOpts("请输入外系统关联标识");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.unitSort) {
        //     toastOpts = getFailToastOpts("请输入排序");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url = "/biz/portal/unit/add";
        if (this.state.unitId) {
            console.log("=========Edit===unitId", this.state.unitId)
            url = "/biz/portal/unit/modify";
        }

        let requestParams = {
            unitId: this.state.unitId,
            unitName: this.state.unitName,
            unitCode: this.state.unitCode,
            unitSort: this.state.unitSort,
        };
        httpPost(url, requestParams, this.saveHLPortalUnitCallBack);
    }

    // 保存回调函数
    saveHLPortalUnitCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }


    render(){
        return(
            <ScrollView style={[CommonStyle.contentViewStyle]}>
                <CommonHeadScreen title={this.state.operate + '单位'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>单位名称</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput
                        //keyboardType='text'
                        style={styles.inputRightText}
                        placeholder={'请填写单位名称'}
                        onChangeText={(text) => this.setState({unitName: text })}
                    >
                        {this.state.unitName}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>
                        关联标识
                        </Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <TextInput 
                        style={styles.inputRightText}
                        placeholder={'请输入外系统关联标识'}
                        onChangeText={(text) => this.setState({unitCode:text})}
                    >
                        {this.state.unitCode}
                    </TextInput>
                </View>
                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>
                        排序
                        </Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <TextInput 
                        keyboardType='numeric'
                        style={styles.inputRightText}
                        placeholder={'请输入排序'}
                        onChangeText={(text) => this.setState({unitSort:text})}
                    >
                        {this.state.unitSort}
                    </TextInput>
                </View>
                <View style={CommonStyle.btnRowStyle}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveHLPortalUnit.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row', width: 130, height: 40, marginRight: 35, marginTop: 15 }]}>
                            <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        )
    }
}
const styles = StyleSheet.create({
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        // marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth +30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})