import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,TextInput,Modal,Image,KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import _ from 'lodash'
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class DryKoleOutMgrAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            recordId:"",
            ordersDataSource:[],
            // 勾选的砖型（订单名称）
            selOrderId:0,
            selOrderName:"",
            // 废品原因列表
            wasteCauseDataSource: [],
            // 准备新增的废品原因列表
            selWasteCauseList:[],
            // dryHoleDataSource:[],
            // selDryHoleId:0,
            qualityGoodsNumber:"",

            productionLineDataSource:[],
            selProductionLineId:null,
            modal:false,
            searchKeyWord:null,
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // this.loadOrderList();
        this.loadWasteCauseList();
        // this.loadDryHoleList();
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { recordId ,productionLineId } = route.params;
            if (recordId) {
                console.log("=============record" + recordId + "");
                this.setState({
                    recordId:recordId,
                    selProductionLineId:productionLineId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/dry/hole/record/get";
                loadRequest={'recordId':recordId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditDryHoleRecordCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }

        let url= "/biz/production/line/list";
        let loadRequest1={'currentPage':1, 'pageSize':1000};
        httpPost(url, loadRequest1, this.callBackLoadProductionLine);
    }

    callBackLoadProductionLine=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            let productionLineDataSource = response.data.dataList;
            let selProductionLineId = response.data.dataList[0].productionLineId;
            // console.log("======现在的iD" + this.state.selProductionLineId);

            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                selProductionLineId = constants.loginUser.spUserExtDTO.productionLineId;
            }
            if(this.state.selProductionLineId) {
                selProductionLineId = this.state.selProductionLineId;
            }
            // console.log("===-=设置之前的id===" + this.state.selproductionLineId);
            this.setState({
                productionLineDataSource:productionLineDataSource,
                selProductionLineId:selProductionLineId,
            })
            // console.log("设置后的sel=" + selProductionLineId);
            // console.log("===-=设置之后的id===" + this.state.selProductionLineId);
            
            this.loadOrder();
        }
    }

    loadOrder=()=>{
        let url= "/biz/order/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':1000,
            "display":"Y",
            "excludeOrderStateList":[
                "A","K"
            ],
            'productionLineId': this.state.selProductionLineId,
            "qryContent":"order",
            "searchKeyWord":this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this.callBackLoadOrder);
    }

    loadEditDryHoleRecordCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                selOrderId:response.data.orderId,
                selOrderName:response.data.orderName,
                selDryHoleId:response.data.dryHoleId
            })
            this.setState({
                qualityGoodsNumber:response.data.qualityGoodsNumber,
            })

            if (response.data.spDryHoleWasteDetailDTOList && response.data.spDryHoleWasteDetailDTOList.length > 0) {
                // 遍历装窑详情
                response.data.spDryHoleWasteDetailDTOList.forEach((item)=>{
                    var varWasteCause={
                        causeId:item.causeId,
                        causeTitle:item.causeTitle,
                        wasteNumber:item.wasteNumber,
                    };
                    this.setState({
                       selWasteCauseList :this.state.selWasteCauseList.concat(varWasteCause)
                    })
                })
            }
            console.log(this.state.selWasteCauseList)
        }
    }

    // 加载砖型（订单名称）列表
    // loadOrderList=()=>{
    //     // 加载排产状态的订单，显示砖型
    //     let url= "/biz/order/list";
    //     let loadRequest={
    //         'currentPage':1,
    //         'pageSize':100,
    //         "display":"Y",
    //         "excludeOrderStateList":[
    //             "A","K"
    //         ]
    //     };
    //     httpPost(url, loadRequest, this.callBackLoadOrder);
    // }

    callBackLoadOrder=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log(response.data.dataList);
            this.setState({
                ordersDataSource:response.data.dataList
            })
            // if (this.state.recordId && this.state.selOrderId != 0) {
            //     // 读编辑数据
            // }
            // else {
            //     this.setState({
            //         selOrderId:response.data.dataList[0] ? response.data.dataList[0].orderId : 0,
            //     })
            // }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }


    loadWasteCauseList=()=>{
        let url= "/biz/ungraded/cause/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':100,
            "causeType":"D"
        };
        httpPost(url, loadRequest, this.loadWasteCauseListCallBack);
    }

    loadWasteCauseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log("response.data.dataList");
            this.setState({
                wasteCauseDataSource:response.data.dataList,
            })
            if (this.state.recordId) {
                // 走编辑
            }
            else {
                if (response.data.dataList.length>0) {
                    var varWasteCause={
                        index:0,
                        causeId:response.data.dataList[0].causeId,
                        causeTitle:response.data.dataList[0].causeTitle,
                        wasteNumber:""
                        // brickAmount:response.data.dataList[0].brickAmount
                    };
                    this.setState({
                        selWasteCauseList:this.state.selWasteCauseList.concat(varWasteCause)
                    })
                    
                }
            }
            console.log(this.state.selWasteCauseList)
        }
    }
    // loadDryHoleList=()=>{
    //     let url= "/biz/dry/hole/list";
    //     let loadRequest={
    //         'currentPage':1,
    //         'pageSize':100,
    //     };
    //     httpPost(url, loadRequest, this.loadDryHoleListCallBack);
    // }

    // loadDryHoleListCallBack=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         console.log(response.data.dataList);
    //         this.setState({
    //             dryHoleDataSource:response.data.dataList
    //         })
    //         if (this.state.recordId && this.state.selDryHoleId != 0) {
    //             // 读编辑数据
    //         }
    //         else {
    //             this.setState({
    //                 selDryHoleId:response.data.dataList[0] ? response.data.dataList[0].dryHoleId : 0,
    //             })
    //         }
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    saveDryHoleRecord=()=>{
        console.log("=======selOrderId======="+this.state.selOrderId);
        console.log("=======qualityGoodsNumber======="+this.state.qualityGoodsNumber);
        console.log("=======selWasteCauseList======="+this.state.selWasteCauseList);
        console.log("=======selDryHoleId======="+this.state.selDryHoleId);
        let toastOpts;
        if (!this.state.selOrderId || this.state.selOrderId === 0) {
            toastOpts = getFailToastOpts("请选择砖型");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.qualityGoodsNumber) {
            toastOpts = getFailToastOpts("请输入正品数量");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.selDryHoleId || this.state.selDryHoleId === 0) {
        //     toastOpts = getFailToastOpts("请选择砖型");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url= "/biz/dry/hole/record/add";
        let _spWasteCauseDetailDTOList = [];
        this.state.selWasteCauseList.map((elem, index)=>{
            var wasteCauseDetailDTO = {
                "causeId": elem.causeId,
                "wasteNumber": elem.wasteNumber,
            }
            _spWasteCauseDetailDTOList.push(wasteCauseDetailDTO);
        })
        let requestParams={
            "recordId":this.state.recordId,
            "recordType":"O",
            "orderId": this.state.selOrderId,
            "qualityGoodsNumber":this.state.qualityGoodsNumber,
            "spDryHoleWasteDetailDTOList": _spWasteCauseDetailDTOList,
            "dryHoleId":this.state.selDryHoleId
        };
        console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.saveDryHoleRecordCallBack);
    }

    saveDryHoleRecordCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('记录完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
        }
    }

    renderProductLineRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.recordId) {
                    return;
                }
                // var selMachineId = null;
                // 切换生产车间时，下面的机台也要跟着变，机台默认选择第一个
                this.setState({
                    selProductionLineId:item.productionLineId,
                    selOrderId:"",
                    selOrderName:"",
                    searchKeyWord:""
                    // machineDataSource:item.machineDTOList,
                }) 
            let url= "/biz/order/list";
            let loadRequest={
            'currentPage':1,
            'pageSize':1000,
            "display":"Y",
            "excludeOrderStateList":[
                "A","K"
            ],
            'productionLineId': item.productionLineId,
            "qryContent":"order",
        };
        httpPost(url, loadRequest, this.callBackLoadOrder);
                // if (item.machineDTOList && item.machineDTOList.length > 0) {
                //     selMachineId = item.machineDTOList[0].machineId;
                // }
                // this.setState({
                //     selMachineId:selMachineId,
                // })
            }}>
                <View key={item.productionLineId} style={[item.productionLineId === this.state.selProductionLineId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/2,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.productionLineId === this.state.selProductionLineId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.productionLineName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("DryKoleOutMgrList", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Text style={CommonStyle.headRightText}>出干燥洞</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>出干燥洞</Text>
                </TouchableOpacity>
            </View>
        )
    }

    // 砖型单项渲染
    renderBrickTypeRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.recordId) {
                    return;
                }
                this.setState({
                selOrderId:item.orderId,
                selOrderName:item.orderName
            }) }}>
                <View key={item.orderId} style={[item.orderId===this.state.selOrderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle,this.state.recordId? CommonStyle.disableViewStyle : ''] }>
                    <Text style={item.orderId===this.state.selOrderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        // 动态显示废品原因数据
        var pages = [];
        for (var i = 0; i < this.state.selWasteCauseList.length; i++) {
            const _wasteCauseDataSource = _.cloneDeep(this.state.wasteCauseDataSource);
            _wasteCauseDataSource.map((elem, index)=>{
                elem._index = i;
                return elem;
            })
            pages.push(        
                <View key={"view_" +this.state.selWasteCauseList[i].causeId+"_"+i}>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>废品原因</Text>
                    </View>
                    <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                        {
                            _wasteCauseDataSource.map((elem, index) => {
                                return (
                                    <TouchableOpacity key={elem.causeId} onPress={(event) => { 
                                        // console.log("=======event", event);
                                        var tempSelWasteCauseList = this.state.selWasteCauseList;
                                        tempSelWasteCauseList[elem._index] = {
                                            index:elem.index,
                                            causeId:elem.causeId,
                                            causeTitle:elem.causeTitle,
                                            wasteNumber:tempSelWasteCauseList[elem._index].wasteNumber
                                        }
                                        this.setState({
                                            selWasteCauseList:tempSelWasteCauseList
                                        })
                                    }} >
                                        <View style={[this.state.selWasteCauseList[i].causeId===elem.causeId ?
                                            {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                            :
                                            {backgroundColor: '#F2F5FC'}
                                            ,
                                            {
                                                marginRight: 8,
                                                marginTop: 8,
                                                marginBottom: 4,
                                                borderRadius: 4,
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                                height: 36,
                                                width: (screenWidth - 54)/3,
                                                borderRadius: 4
                                            }
                                        ]}>
                                            <Text style={[this.state.selWasteCauseList[i].causeId===elem.causeId ?
                                                {
                                                    color: '#1E6EFA'
                                                }
                                                :
                                                {
                                                    color: '#404956'
                                                }
                                                ,
                                            {
                                                fontSize: 16, textAlign : 'center'
                                            }
                                            ]}>
                                                {elem.causeTitle}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                    
                                );
                            })
                        }
                    </View>
                    <View style={CommonStyle.rowLabView}>
                        <View style={CommonStyle.rowLabLeftView}>
                            <Text style={CommonStyle.rowLabTextStyle}>数量</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入'}
                            style={styles.inputRightText}
                            causeId={this.state.selWasteCauseList[i].causeId}
                            onChange={(event) => {
                                // 通过回调事件查看控件属性
                                // var orderId = event.target._internalFiberInstanceHandleDEV.memoizedProps.orderId;
                                var causeId = event._dispatchInstances.memoizedProps.causeId;
                                var text = event.nativeEvent.text;
                                console.log("=====isNumber:", isNumber(text));
                                var varselWasteCause;
                                for(var index=0; index<this.state.selWasteCauseList.length;index++){
                                    varselWasteCause = this.state.selWasteCauseList[index];
                                    if (causeId === varselWasteCause.causeId) {
                                        varselWasteCause.wasteNumber = text;
                                        this.state.selWasteCauseList[index] = varselWasteCause;
                                        console.log("==数据更新==this.state.selWasteCauseList", this.state.selWasteCauseList);
                                    }
                                }
                            }}
                        >
                            {this.state.selWasteCauseList[i].wasteNumber}
                        </TextInput>
                        
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                </View>
            );
        }

        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '出洞'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.formContentViewStyle}>

                <View style={styles.rowLabView}>
                    <Text style={[styles.leftLabRedTextStyle,{color:'white'}]}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>生产车间</Text>
                </View>
                <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                        {
                            (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0) 
                            ? 
                            this.state.productionLineDataSource.map((item, index)=>{
                                return this.renderProductLineRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                </View>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           <Text style={styles.leftLabRedTextStyle}>*</Text>
                           <Text style={styles.leftLabNameTextStyle}>砖型</Text>
                        </View>
                    </View>
                    <View style={[{flexWrap:'wrap',flexDirection:'row'}, this.state.recordId? CommonStyle.disableViewStyle : null]}>
                        <Text style={[styles.leftLabRedTextStyle,{color:'white'}]}>*</Text>
                        <TouchableOpacity onPress={()=>{
                            if(this.state.recordId) {
                                return;
                            }
                            this.setState({ 
                                modal:true,
                            })

                            if (!this.state.selOrderId && this.state.ordersDataSource && this.state.ordersDataSource.length > 0) {
                                this.setState({
                                    // selBrickTypeId:this.state.ordersDataSource[0].brickTypeId,
                                    selOrderId:this.state.ordersDataSource[0].orderId,
                                    selOrderName:this.state.ordersDataSource[0].orderName,
                                })
                            }
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle,
                                // {backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}
                                {
                                    marginLeft:0,
                                    // marginRight: 8,
                                    marginTop: 8,
                                    marginBottom: 4,
                                    borderRadius: 4,
                                    justifyContent: 'center',
                                    alignContent: 'center',
                                    height: 36,
                                    // width: 300,
                                    borderRadius: 4,
                                    backgroundColor: '#F2F5FC'
                                }
                                ]}>
                                <Text style={[CommonStyle.blockItemTextStyle16,
                                    // {fontWeight:'bold'}
                                    {
                                        fontSize: 16, textAlign : 'center', color: '#404956'
                                    }
                                    ]}>
                                    选择砖型
                                    {this.state.selOrderId && this.state.selOrderName ? ("：" + this.state.selOrderName) : null}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput 
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({searchKeyWord:text})}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={()=>{
                                        this.loadOrder();
                                        }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{flexDirection:'row', flexWrap:'wrap', overflow:'scroll'}}>
                                    {
                                        (this.state.ordersDataSource && this.state.ordersDataSource.length > 0) 
                                        ? 
                                        this.state.ordersDataSource.map((item, index)=>{
                                            if (index < 1000) {
                                                return this.renderBrickTypeRow(item)
                                            }
                                        })
                                        : <EmptyRowViewComponent/> 
                                    }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                    <TouchableOpacity onPress={() => { 
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selOrderId) {
                                            let toastOpts = getFailToastOpts("您还没有选择砖型");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            
                        </View>
                        <View>

                        </View>
                    </Modal>
                    {/* <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.ordersDataSource && this.state.ordersDataSource.length > 0) 
                            ? 
                            this.state.ordersDataSource.map((item, index)=>{
                                return this.renderBrickTypeRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View> */}
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           <Text style={styles.leftLabRedTextStyle}>*</Text>
                           <Text style={styles.leftLabNameTextStyle}>正品数量</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({qualityGoodsNumber:text})}
                        >
                            {this.state.qualityGoodsNumber}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View>
                        {
                            pages.map((elem, index) => {
                                return elem;
                            })
                        }
                    </View>

                    <View style={styles.btnRowView}>
                        <TouchableOpacity onPress={()=>{
                                // console.log("==========this.state.brickTypeDataSource.length:", this.state.wasteCauseDataSource.length);
                                if (this.state.selWasteCauseList.length >= this.state.wasteCauseDataSource.length){
                                    WToast.show({data:"废品数量达到上限，不能再添加了"});
                                    return;
                                }
                                if (this.state.wasteCauseDataSource.length > 0) {
                                    var selIndex = this.state.selWasteCauseList.length;
                                    var varBrickType={
                                        index:selIndex,
                                        causeId:this.state.wasteCauseDataSource[selIndex].causeId,
                                        causeTitle:this.state.wasteCauseDataSource[selIndex].causeTitle,
                                        wasteNumber:""
                                    };
                                    this.setState({
                                        selWasteCauseList:this.state.selWasteCauseList.concat(varBrickType)
                                    })
                                    // console.log("xxxxxx======selWasteCauseList:", this.state.selWasteCauseList)
                                }
                        }}>
                            <View style={styles.btnAddView}>
                                <Text style={styles.btnAddText}>+新增废品</Text>
                            </View>
                        </TouchableOpacity>
                    
                        <TouchableOpacity onPress={()=>{
                            if (!this.state.selWasteCauseList) {
                                WToast.show({data:"没有可删除的，请先指定废品原因"});
                                return;
                            }
                            // if (this.state.selWasteCauseList.length <= 1) {
                            //     WToast.show({data:"至少要指定一种砖型"});
                            //     return;
                            // }
                            this.setState({
                                selWasteCauseList:this.state.selWasteCauseList.slice(0,-1)
                            })
                        }}>
                            <View style={styles.btnDeleteView}>
                                <Text style={styles.btnDeleteText}>-删除</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    {/* <View style={{height:ifIphoneXContentViewHeight()-200, backgroundColor:'#F2F5FC'}}>
                        {/* <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                        >
                        </TextInput> */}
                    {/* </View> */} 
                    <View style={[CommonStyle.btnRowStyle,{marginTop:ifIphoneXContentViewHeight()-620}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={styles.textContainerCancel} >
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={styles.textCancel}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveDryHoleRecord.bind(this)}>
                            <View style={styles.textContainerCertain}>
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={styles.textCertain}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({
    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#1E6EFA', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#FFFFFF',
        // borderWidth:1,
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
    },
    textCertain: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#FFFFFF',
        lineHeight: 24,
        marginTop:10,
        textAlign: 'center',
        // fontStyle: 'normal',
    },
    textCancel: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#404956',
        lineHeight: 24,
        marginTop:10,
        textAlign:'center'
        // fontStyle: 'normal',
    },
    textContainerCertain: {
        width: 180,
        height: 48,
        marginRight:8,
        backgroundColor: '#255BDA',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    textContainerCancel: {
        width: 180,
        height: 48,
        marginLeft:8,
        backgroundColor: '#FFFFFF',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },

});