import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  ImageBackground,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {WToast} from 'react-native-smart-tip';
import ClassHeadScreen from '../../../component/ClassHeadScreen';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import ProgressBar from '../../../component/ProgressBar';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;

const commonStyles = {
  color: 'rgba(255, 255, 255, 1)',
  fontSize: 12,
  width: 55,
  height: 19,
  paddingLeft: 9,
  paddingTop: 2,
};

const taskStateBgColor = {
  '0AA': '#0000ff',
  '0BB': '#ff0000',
  '0CC': '#008000',
  default: 'rgba(0,10,32,0.45)',
};

const taskStateText = {
  '0AA': '实习中',
  '0BB': '已超期',
  '0CC': '已完成',
  default: '未开始',
};

const taskTitleBgColor = {
  0: '#FB7B04',
  1: '#1084FD',
  2: '#1E85A3',
  3: '#FBB100',
  4: '#BF181E',
  5: '#1B9342',
};

export default class CourseTrackDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      //页面功能直接相关
      courseTaskId: null,
      courseId: null,
      checkOutUserId: null,
      checkInUserId: null,
      resetModal: false,
      closeModal: false,
      dataItem: null,
      courseDataSource: null,

      //页面显示直接相关数据
      userPhoto: null,
      userName: null,
      courseName: null,
      coursePhoto: '',
      checkOutUserName: null,
      checkOutUserPhoto: null,
      checkInUserPhoto: null,
      checkInUserName: null,
      courseDurations: null,
      courseTypeName: null,
      courseLevelName: null,
      taskState: null,
      lastStudyTime: null, //最后实习时间
      gmtCreated: null, //开启实习时间
      courseDurations: null, //实习持续天数
      actualCompletionTime: null, //实际完成时间
      gmtModified: null,

      selTaskStateCode: 'd',
      taskStateDataSource: [
        {
          stateCode: 'd',
          stateName: '流程',
          lenths: 0,
        },
        {
          stateCode: 'c',
          stateName: '进展',
          lenths: 0,
        },
      ],
      trackingDataSource: [],
      dataIndex: 0,
    };
  }

  UNSAFE_componentWillMount() {
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {dataItem, courseDataSource, dataIndex} = route.params;

      console.log(
        '========',
        courseDataSource,
        JSON.stringify(courseDataSource, null, 6),
      );
      if (dataIndex) {
        this.setState({
          dataIndex: dataIndex,
        });
      }

      if (dataItem.courseTaskId) {
        console.log('courseTaskId================', dataItem.courseTaskId);
        console.log(JSON.stringify(dataItem, null, 6));
        console.log(
          'lastStudyTime================',
          dataItem.portalTrackDetailDTOList &&
            dataItem.portalTrackDetailDTOList[0] &&
            dataItem.portalTrackDetailDTOList[0].gmtCreated
            ? dataItem.portalTrackDetailDTOList[0].gmtCreated
            : dataItem.gmtCreated,
        );
        // var dataAll = [...dataItem];
        this.setState({
          ...dataItem,
          //以最新的进展创建时间为最后学习时间
          lastStudyTime:dataItem.portalTrackDetailDTOList&&dataItem.portalTrackDetailDTOList[0]&&dataItem.portalTrackDetailDTOList[0].gmtModified?dataItem.portalTrackDetailDTOList[0].gmtModified:dataItem.gmtCreated,
          dataItem: dataItem,
          trackingDataSource: dataItem.portalTrackDetailDTOList,
          courseDataSource: courseDataSource,
          taskStateDataSource: [
            {
              stateCode: 'd',
              stateName: '流程',
              lenths: dataItem.taskState == '0CC' ? 2 : 1,
            },
            {
              stateCode: 'c',
              stateName: '进展',
              lenths: dataItem.portalTrackDetailDTOList.length,
            },
          ],
        });
        this.loadCourseTask(dataItem.courseTaskId);
      }
    }
  }

  loadCourseTask = (courseTaskId) => {
    let loadTypeUrl = '/biz/course/task/get_more';
    let loadRequest = {
      courseTaskId: courseTaskId,
      documentTypeList: ['TV', 'TD'],
    };
    httpPost(loadTypeUrl, loadRequest, this.loadCourseTaskCallBack);
  };
  loadCourseTaskCallBack = (response) => {
    if (response.code == 200 && response.data) {
      let dataItem = response.data;

      console.log('courseTaskId================', dataItem.courseTaskId);
      console.log(JSON.stringify(dataItem, null, 6));
      console.log(
        'lastStudyTime================',
        dataItem.portalTrackDetailDTOList &&
          dataItem.portalTrackDetailDTOList[0] &&
          dataItem.portalTrackDetailDTOList[0].gmtCreated
          ? dataItem.portalTrackDetailDTOList[0].gmtCreated
          : dataItem.gmtCreated,
      );

      this.setState({
        ...dataItem,
        //以最新的进展创建时间为最后学习时间
        lastStudyTime:
          dataItem.portalTrackDetailDTOList &&
          dataItem.portalTrackDetailDTOList[0] &&
          dataItem.portalTrackDetailDTOList[0].gmtCreated
            ? dataItem.portalTrackDetailDTOList[0].gmtCreated
            : dataItem.gmtCreated,
        dataItem: dataItem,
        trackingDataSource: dataItem.portalTrackDetailDTOList,
        taskStateDataSource: [
          {
            stateCode: 'd',
            stateName: '流程',
            lenths: dataItem.checkInUserId ? 2 : 1,
          },
          {
            stateCode: 'c',
            stateName: '进展',
            lenths: dataItem.portalTrackDetailDTOList.length,
          },
        ],
      });
    }
  };
  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
          if (this.props.route.params.refresh) {
            console.log('goBack refresh');
            this.props.route.params.refresh();
          }
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 22, height: 22}}
          source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return <View></View>;
  }

  setCourseTask = (taskItem) => {
    console.log(
      '=======setCourseTask=taskItem',
      JSON.stringify(taskItem, null, 6),
    );
    let requestUrl = '/biz/course/task/modify';
    let requestParams = {
      courseTaskId: taskItem.courseTaskId,
      checkOutUserId: taskItem.checkOutUserId,
      checkInUserId: constants.loginUser.userId,
      taskState: taskItem.taskState === '0CC' ? '0AA' : '0CC',
    };
    httpPost(requestUrl, requestParams, (response) => {
      if (response.code == 200) {
        // 更新页面上显示
        this.loadCourseTask(taskItem.courseTaskId);
      } else {
        WToast.show({data: response.message});
      }
    });
  };

  addCourseTask = (taskItem) => {
    console.log('=======addCourseTask=taskItem', taskItem);
    console.log(
      '@_addCourseTask的dataSource_@',
      JSON.stringify(_dataSource, null, 6),
    );
    var _dataSource = copyArr(this.state.courseDataSource);
    let num = Infinity;
    let index1 = -1;
    if (_dataSource && Object.keys(_dataSource).length > 0) {
      // console.log("找到的结果是", JSON.stringify(outPut, null, 6));
      _dataSource.forEach((obj, index) => {
        if (
          obj.courseSort > taskItem.courseSort &&
          obj.courseSort < num &&
          obj.courseTypeId === taskItem.courseTypeId
        ) {
          num = obj.courseSort;
          index1 = index;
        }
      });
      if (index1 == -1) {
        WToast.show({data: '无后续实习'});
        //console.log("@_后续无实习_@");
      }
      if (index1 >= 0) {
        //console.log("@_后续学习实习_@" + JSON.stringify(_dataSource[index1], null, 6));

        var dateTime = new Date();
        // console.log("dateTime1=====" + dateTime);
        dateTime = dateTime.setHours(dateTime.getHours() + 8);
        dateTime = new Date(dateTime);
        dateTime = dateTime.setDate(
          dateTime.getDate() + _dataSource[index1].courseDuration,
        );
        dateTime = new Date(dateTime);
        // console.log("dateTime3=====" + dateTime);
        let Y = dateTime.getFullYear() + '-';
        let M =
          (dateTime.getMonth() + 1 < 10
            ? '0' + (dateTime.getMonth() + 1)
            : dateTime.getMonth() + 1) + '-';
        let D =
          dateTime.getDate() < 10
            ? '0' + dateTime.getDate()
            : dateTime.getDate();
        let date = Y + M + D;
        console.log('=====date=====', date);
        let requestUrl = '/biz/course/task/add';
        let requestParams = {
          courseId: _dataSource[index1].courseId,
          planCompletionTime: date,
          checkOutUserId: constants.loginUser.userId,
        };
        httpPost(requestUrl, requestParams, this.addCourseTaskCallBack);
      }
    }
  };
  renderTaskStateRow = (item, index) => {
    return (
      <View key={item.stateCode}>
        <TouchableOpacity
          onPress={() => {
            let selTaskStateCode = item.stateCode;
            this.setState({
              selTaskStateCode: selTaskStateCode,
            });
          }}>
          <View style={CommonStyle.tabItemViewStyle}>
            <Text
              style={[
                item.stateCode === this.state.selTaskStateCode
                  ? [CommonStyle.selectedtabItemTextStyle]
                  : [CommonStyle.tabItemTextStyle],
              ]}>
              {item.stateName + '(' + item.lenths + ')'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };
  // 开始实习按操作的回调
  addCourseTaskCallBack = (response) => {
    if (response.code == 200 && response.data) {
      var _dataSource = copyArr(this.state.courseDataSource);
      let outPut = _dataSource.filter(
        (item) => item.courseId == response.data.courseId,
      );
      // console.log("@_addCourseTaskCallBack_@", JSON.stringify(response, null, 6));
      // console.log("@_outPut_@", JSON.stringify(outPut, null, 6));
      WToast.show({
        data:
          '任务' +
          outPut[0].courseSort +
          ' ' +
          outPut[0].courseName +
          '实习任务已开始',
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };
  trackingRenderRow = (item, index) => {
    return (
      <View
        key={item.trackId + 'trackId'}
        style={{
          backgroundColor: 'white',
          marginTop: 2,
        }}>
        {/* 成果顶部信息 */}
        <View
          style={{
            flexDirection: 'row',
            marginLeft: 14,
            marginRight: 11,
            marginTop: 11,
            alignItems: 'center',
            // backgroundColor:"red"
          }}>
          {this.state.checkOutUserPhoto ? (
            <Image
              source={{
                uri: constants.image_addr + '/' + this.state.checkOutUserPhoto,
              }}
              style={{height: 32, width: 32, borderRadius: 50}}
            />
          ) : (
            <ImageBackground
              source={require('../../../assets/icon/iconfont/profilePicture.png')}
              style={{width: 32, height: 32}}>
              <View
                style={{
                  height: 32,
                  width: 32,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                {this.state.checkOutUserName &&
                this.state.checkOutUserName <= 2 ? (
                  <Text
                    style={{
                      color: '#FFFFFF',
                      fontSize: 12,
                      fontWeight: 'normal',
                      textAlign: 'center',
                      lineHeight: 22,
                    }}>
                    {this.state.checkOutUserName}
                  </Text>
                ) : (
                  <Text
                    style={{
                      color: '#FFFFFF',
                      fontSize: 12,
                      fontWeight: 'normal',
                      textAlign: 'center',
                      lineHeight: 22,
                    }}>
                    {this.state.checkOutUserName
                      ? this.state.checkOutUserName.slice(-2)
                      : ''}
                  </Text>
                )}
              </View>
            </ImageBackground>
          )}
          <View
            style={{
              marginLeft: 11,
              flexDirection: 'row',
              width: screenWidth - 14 * 2 - 32 - 35 - 11,
              // backgroundColor:"white",
              alignItems: 'center',
            }}>
            <Text style={{fontSize: 16}}>{this.state.checkOutUserName}</Text>

            <Image
              style={{height: 13, width: 12, marginLeft: 9}}
              source={require('../../../assets/icon/iconfont/clock.png')}></Image>
            <Text style={{fontSize: 13, marginLeft: 3}}>{item.gmtCreated}</Text>
          </View>
        </View>
        <View
          style={{
            marginLeft: 55,
            marginRight: 11,
            marginBottom: 17,
            // backgroundColor:"green"
          }}>
          <Text style={{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)'}}>
            {item.trackRemark}
          </Text>
        </View>
        <View style={styles.lineViewStyle} />
      </View>
    );
  };
  render() {
    if (isNaN(Date.parse(this.state.gmtCreated.replace(' ', 'T')))) {
      console.error(
        'Invalid date format in gmtCreated:',
        this.state.gmtCreated,
      );
      return null; // 或者抛出异常：throw new Error('Invalid date format in gmtCreated');
    }
    // 计算学习天数
    const studyDays =
      this.state.taskState == '0CC'
        ? this.state.courseDurations
        : (
            (Date.now() - Date.parse(this.state.gmtCreated.replace(' ', 'T'))) /
            (1000 * 3600 * 24)
          ).toFixed(0);

    const indexOfBgColor = (parseInt(this.state.dataIndex) + 1) % 6;
    return (
      <View>
        <CommonHeadScreen
          title={this.state.checkOutUserName + '的实习任务'}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />

        <ScrollView style={CommonStyle.contentViewStyle}>
          <View style={{backgroundColor: 'rgba(255, 255, 255, 1)'}}>
            {/* 实习顶部信息 */}
            <View
              style={{
                flexDirection: 'row',
                marginLeft: 14,
                marginTop: 11,
                marginBottom: 11,
                backgroundColor: 'rgba(255, 255, 255, 1)',
                // backgroundColor:'red'
              }}>
              {this.state.checkOutUserPhoto ? (
                <Image
                  source={{
                    uri:
                      constants.image_addr + '/' + this.state.checkOutUserPhoto,
                  }}
                  style={{height: 48, width: 48, borderRadius: 50}}
                />
              ) : (
                <ImageBackground
                  source={require('../../../assets/icon/iconfont/profilePicture.png')}
                  style={{width: 48, height: 48}}>
                  <View
                    style={{
                      height: 48,
                      width: 48,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    {this.state.checkOutUserName &&
                    this.state.checkOutUserName.length <= 2 ? (
                      <Text
                        style={{
                          color: '#FFFFFF',
                          fontSize: 17,
                          fontWeight: 'normal',
                          textAlign: 'center',
                          lineHeight: 22,
                        }}>
                        {this.state.checkOutUserName}
                      </Text>
                    ) : (
                      <Text
                        style={{
                          color: '#FFFFFF',
                          fontSize: 17,
                          fontWeight: 'normal',
                          textAlign: 'center',
                          lineHeight: 22,
                        }}>
                        {this.state.checkOutUserName
                          ? this.state.checkOutUserName.slice(-2)
                          : ''}
                      </Text>
                    )}
                  </View>
                </ImageBackground>
              )}
              <View style={{marginLeft: 11, flexDirection: 'column'}}>
                <View style={{flexDirection: 'row', marginTop: 4}}>
                  <View style={{flexDirection: 'row'}}>
                    <Text style={{fontSize: 16}}>
                      {this.state.checkOutUserName}的实习
                    </Text>
                  </View>
                </View>
                <View style={{flexDirection: 'row'}}>
                  <Image
                    style={{
                      height: 13,
                      width: 12,
                      marginTop: 5,
                      marginLeft: 1,
                      marginRight: 5,
                    }}
                    source={require('../../../assets/icon/iconfont/clock.png')}></Image>
                  <View style={{marginTop: 4, marginBottom: 3, marginRight: 4}}>
                    <Text
                      style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)'}]}>
                      {this.state.gmtModified ? this.state.gmtModified.substr(0,10) : this.state.lastStudyTime.substr(0,10)} 最后实习时间
                    </Text>
                  </View>
                </View>
              </View>
            </View>
            {/* 分隔线 */}
            <View style={styles.lineViewStyle} />

            {/* 实习中部信息 */}
            <View
              style={{
                flexDirection: 'row',
                height: 129,
                width: screenWidth - 13,
                // backgroundColor:"red"
              }}>
              <View
                style={{
                  width: 130 + 28,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <View>
                  {this.state.coursePhoto ? (
                    <View>
                      <Image
                        source={{
                          uri:
                            constants.image_addr + '/' + this.state.coursePhoto,
                        }}
                        style={{
                          width: 130,
                          height: 93,
                          borderRadius: 10,
                        }}></Image>
                    </View>
                  ) : (
                    <View>
                      <View
                        style={{
                          width: 130,
                          height: 93,
                          alignItems: 'center',
                          justifyContent: 'center',
                          padding: 8,
                          borderRadius: 10,
                          backgroundColor: taskTitleBgColor[indexOfBgColor],
                        }}>
                        <Text style={{fontSize: 20, color: 'white'}}>
                          {this.state.courseName}
                        </Text>
                      </View>
                    </View>
                  )}
                  <View
                    style={{
                      position: 'absolute',
                      zIndex: 10,
                      borderTopLeftRadius: 10,
                      borderBottomRightRadius: 10,
                      backgroundColor: taskStateBgColor[this.state.taskState],
                    }}>
                    <Text style={commonStyles}>
                      {taskStateText[this.state.taskState]}
                    </Text>
                  </View>
                </View>
              </View>

              <View
                style={{
                  flex: 1,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <View style={{height: 93, width: '100%'}}>
                  {/* 自定义组件 */}
                  <ClassHeadScreen
                    redTitle={this.state.courseLevelName}
                    blackTitle={
                      ' 任务' +
                      this.state.courseSort +
                      ' ' +
                      this.state.courseName
                    }
                  />

                  <View style={{height: 20, flexDirection: 'row'}}>
                    <View
                      style={{
                        //外边距
                        borderRadius: 10,
                        backgroundColor: 'rgba(27,188,130,0.2)',
                        height: 20,
                        paddingLeft: 10,
                        paddingRight: 10,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <Text style={{fontSize: 12, color: '#1BBC82'}}>
                        {this.state.courseTypeName}
                      </Text>
                    </View>
                  </View>

                  <View
                    style={{
                      height: 12,
                      marginTop: 8,
                    }}>
                    <ProgressBar
                      fillColor={'rgba(30, 110, 250, 1)'}
                      height={3}
                      progress={
                        studyDays / this.state.courseDurations >= 1
                          ? 1
                          : studyDays / this.state.courseDurations
                      }
                    />
                  </View>
                  <View style={{height: 17, flexDirection: 'row'}}>
                    <Text>{'已实习(天）：'}</Text>
                    {this.state.taskState == '0AA' ? (
                      <Text>{studyDays}</Text>
                    ) : this.state.taskState == '0BB' ? (
                      <Text style={{color: 'red'}}>{'超期'}</Text>
                    ) : (
                      <Text>{this.state.courseDurations}</Text>
                    )}
                    <Text>{'/' + this.state.courseDurations}</Text>
                  </View>
                </View>
              </View>
            </View>
            {/* 分隔线 */}
            <View style={styles.lineViewStyle} />

            {/* tab分类 */}
            <View style={[CommonStyle.headViewStyle]}>
              <View
                style={{width: '100%', flexWrap: 'wrap', flexDirection: 'row'}}>
                {this.state.taskStateDataSource &&
                this.state.taskStateDataSource.length > 0 ? (
                  this.state.taskStateDataSource.map((item, index) => {
                    return this.renderTaskStateRow(item);
                  })
                ) : (
                  <View />
                )}
              </View>
            </View>

            {/* 流程 */}
            {this.state.selTaskStateCode === 'd' ? (
              <View style={{backgroundColor: 'white'}}>
                {/* 开启学习者 */}
                <View
                  style={{
                    flexDirection: 'row',
                    marginLeft: 21,
                    marginTop: 14,
                    marginRight: 16,
                    marginBottom: 30,
                  }}>
                  {this.state.checkOutUserPhoto ? (
                    <Image
                      source={{
                        uri:
                          constants.image_addr +
                          '/' +
                          this.state.checkOutUserPhoto,
                      }}
                      style={{height: 48, width: 48, borderRadius: 50}}
                    />
                  ) : (
                    <ImageBackground
                      source={require('../../../assets/icon/iconfont/profilePicture.png')}
                      style={{width: 48, height: 48}}>
                      <View
                        style={{
                          height: 48,
                          width: 48,
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        {this.state.checkOutUserName &&
                        this.state.checkOutUserName.length <= 2 ? (
                          <Text
                            style={{
                              color: '#FFFFFF',
                              fontSize: 17,
                              fontWeight: 'normal',
                              textAlign: 'center',
                              lineHeight: 22,
                            }}>
                            {this.state.checkOutUserName}
                          </Text>
                        ) : (
                          <Text
                            style={{
                              color: '#FFFFFF',
                              fontSize: 17,
                              fontWeight: 'normal',
                              textAlign: 'center',
                              lineHeight: 22,
                            }}>
                            {this.state.checkOutUserName
                              ? this.state.checkOutUserName.slice(-2)
                              : ''}
                          </Text>
                        )}
                      </View>
                    </ImageBackground>
                  )}
                  <View style={{marginLeft: 8, flexDirection: 'column'}}>
                    <View style={{flexDirection: 'row', marginTop: 1}}>
                      <Text
                        style={{
                          fontSize: 16,
                          lineHeight: 20,
                          color: 'rgba(0, 10, 32, 0.85)',
                        }}>
                        开启实习
                      </Text>
                    </View>

                    <View style={{flexDirection: 'row', marginTop: 4}}>
                      <Text
                        style={{
                          fontSize: 14,
                          lineHeight: 20,
                          color: 'rgba(0, 10, 32, 0.65)',
                        }}>
                        {this.state.checkOutUserName}
                      </Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      position: 'absolute',
                      right: 0,
                      top: 5,
                    }}>
                    <Text style={[{fontSize: 12, color: 'rgba(0,10,32,0.45)'}]}>
                      {this.state.gmtCreated.slice(0, 16)}
                    </Text>
                  </View>
                </View>
                {/* 关闭人 */}
                {this.state.taskState === '0CC' ? (
                  <View
                    style={{
                      flexDirection: 'row',
                      marginLeft: 21,
                      marginRight: 16,
                      marginBottom: 4,
                    }}>
                    {/* <Text>已完成</Text> */}
                    {this.state.checkInUserPhoto ? (
                      <Image
                        source={{
                          uri:
                            constants.image_addr +
                            '/' +
                            this.state.checkInUserPhoto,
                        }}
                        style={{height: 48, width: 48, borderRadius: 50}}
                      />
                    ) : (
                      <ImageBackground
                        source={require('../../../assets/icon/iconfont/profilePicture.png')}
                        style={{width: 48, height: 48}}>
                        <View
                          style={{
                            height: 48,
                            width: 48,
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          {this.state.checkInUserName &&
                          this.state.checkInUserName.length <= 2 ? (
                            <Text
                              style={{
                                color: '#FFFFFF',
                                fontSize: 17,
                                fontWeight: 'normal',
                                textAlign: 'center',
                                lineHeight: 22,
                              }}>
                              {this.state.checkInUserName}
                            </Text>
                          ) : (
                            <Text
                              style={{
                                color: '#FFFFFF',
                                fontSize: 17,
                                fontWeight: 'normal',
                                textAlign: 'center',
                                lineHeight: 22,
                              }}>
                              {this.state.checkInUserName
                                ? this.state.checkInUserName.slice(-2)
                                : ''}
                            </Text>
                          )}
                        </View>
                      </ImageBackground>
                    )}
                    <View
                      style={{marginLeft: 8, flexDirection: 'column', flex: 1}}>
                      <Text
                        style={{
                          fontSize: 16,
                          color: 'rgba(0,10,32,0.85)',
                          lineHeight: 20,
                        }}>
                        关闭人
                      </Text>
                      <View style={{flexDirection: 'row', marginTop: 1}}>
                        <Text
                          style={{
                            fontSize: 14,
                            lineHeight: 20,
                            color: 'rgba(0, 10, 32, 0.65)',
                          }}>
                          {this.state.checkInUserName}（关闭任务）
                        </Text>
                      </View>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        position: 'absolute',
                        right: 0,
                        top: 5,
                      }}>
                      <Text
                        style={[{fontSize: 12, color: 'rgba(0,10,32,0.45)'}]}>
                        {this.state.gmtModified.slice(0, 16)}
                      </Text>
                    </View>
                  </View>
                ) : (
                  <View></View>
                )}
                {/* 按钮 */}
                <View
                  style={[
                    {
                      marginTop: 9,
                      marginRight: 8,
                      backgroundColor: 'rgba(242, 245, 252, 0.5)',
                      height: 48,
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                    },
                  ]}>
                  <TouchableOpacity
                    onPress={() => {
                      this.state.taskState === '0CC'
                        ? // '重启'
                          this.setState({
                            resetModal: true,
                            // modalDataItem:item,
                          })
                        : // '关闭'
                          this.setState({
                            closeModal: true,
                            // modalDataItem:item,
                          });
                    }}>
                    <View
                      style={
                        this.state.taskState === '0CC'
                          ? [
                              CommonStyle.itemBottomEditBlueBtnViewStyle,
                              {
                                width: 64,
                                backgroundColor: 'rgba(253, 66, 70, 1)',
                              },
                            ]
                          : [
                              CommonStyle.itemBottomEditBlueBtnViewStyle,
                              {
                                width: 64,
                                flexDirection: 'row',
                                backgroundColor: 'rgba(253, 66, 70, 1)',
                              },
                            ]
                      }>
                      <Image
                        style={{width: 17, height: 17, marginRight: 3}}
                        source={require('../../../assets/icon/iconfont/close.png')}></Image>
                      <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                        {this.state.taskState === '0CC' ? '重启' : '关闭'}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <View></View>
            )}
            {this.state.selTaskStateCode === 'c' ? (
              <FlatList
                data={this.state.trackingDataSource}
                renderItem={({item, index}) =>
                  this.trackingRenderRow(item, index)
                }
                ListEmptyComponent={this.emptyComponent}
              />
            ) : (
              <View></View>
            )}
          </View>
        </ScrollView>

        {/* 关闭弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.closeModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                height: 122,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>您确定要关闭该任务吗？</Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  width: 291,
                  height: 50,
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      closeModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontFamily: 'PingFangSC',
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    WToast.show({data: '点击了确定'});
                    this.setState({
                      closeModal: false,
                    });
                    if (
                      this.state.taskState == '0BB' ||
                      this.state.taskState == '0AA'
                    ) {
                      this.addCourseTask(this.state.dataItem);
                    }
                    this.setCourseTask(this.state.dataItem);
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderLeftWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontFamily: 'PingFangSC',
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      确定
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* 重启弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.resetModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                height: 122,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>您确定要重启该任务吗？</Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  width: 291,
                  height: 50,
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      resetModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontFamily: 'PingFangSC',
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    WToast.show({data: '点击了确定'});
                    this.setState({
                      resetModal: false,
                    });
                    this.setCourseTask(this.state.dataItem);
                  }}>
                  <View
                    style={{
                      width: 145,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderLeftWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontFamily: 'PingFangSC',
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      确定
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  itemContentTextStyle: {
    marginLeft: 12,
    marginRight: 16,
    marginTop: 3,
    lineHeight: 24,
  },
  titleViewStyle: {
    flexDirection: 'row',
    marginLeft: 12,
    marginRight: 16,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    fontSize: 14,
    lineHeight: 24,
    textAlign: 'left',
    textAlignVertical: 'top',
    color: 'rgba(0, 10, 32, 0.65)',
  },
  lineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    // marginTop: 15,
    // marginBottom: 6,
    borderBottomWidth: 0.5,
    borderColor: '#E8E9EC',
  },
});
