import React,{ Component } from 'react';
import {View, Text,ActivityIndicator} from 'react-native';
class CustomListFooterComponent extends Component {
    constructor(props){
        super(props);
    }
    render(){
        if (this.props.isloading) {
            return(
                <View style={{ 
                    alignItems:'center', 
                    marginBottom: platformos === 'ios' ? 30 : 0,
                }}>
                    <ActivityIndicator 
                        style={{margin:10}}
                        size={'large'}
                        color={'red'}
                        animating={true}
                    />
                </View>
            )
        }
        else {
            return(
                <View>
                    <View style={{ 
                        alignItems:'center', 
                        marginBottom: platformos === 'ios' ? 0 : 0,
                        padding:10,
                    }}>
                        <Text>已经是最后一页了，我们也是有底线的</Text>
                    </View>
                </View>
            )
        }
        
    }
}
module.exports = CustomListFooterComponent;