import React,{ Component } from 'react';
import {View, Text, StyleSheet,Dimensions,Image} from 'react-native';

var CommonStyle = require('../assets/css/CommonStyle');
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
class EmptyJobComponent extends Component {

    render(){
        return(
            <View style={{width:screenWidth,alignItems: 'center', justifyContent: 'center',height:screenHeight - 125,backgroundColor:'rgba(242, 245, 252, 1)'}}>
                <Image  style={{width:122, height:129}} source={require('../assets/image/emptyDepartment.png')}></Image>
                <Text style={styles.contentTextStyle}>职位为空</Text>
            </View>
        )
    }
}
const styles = StyleSheet.create({

    contentTextStyle:{
        width:70,
        height:22,
        fontSize:16,
        fontWeight:'500',
        color:'rgba(0,10,32,0.15)',
        // backgroundColor:'red',
    }
})
module.exports = EmptyJobComponent;