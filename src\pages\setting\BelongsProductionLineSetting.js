import React,{Component} from 'react';
import {View, ScrollView, Text, TextInput, Image,StyleSheet,TouchableOpacity,Dimensions,Alert} from 'react-native';
import CommonHeadScreen from '../../component/CommonHeadScreen';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
class BelongsProductionLineSetting extends Component {
    constructor(props) {
        super(props);
        this.state = {
            productionLineDataSource:[],
            selProductionLineId:null,
            userId:null,
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        let loadTypeUrl;
        let loadRequest;

        loadTypeUrl= "/biz/production/line/list";
        loadRequest={'currentPage':1, "pageSize":10000};
        httpPost(loadTypeUrl, loadRequest, this.loadProductionLineCallBack);
    }

    loadProductionLineCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                productionLineDataSource:response.data.dataList
            })
            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                this.setState({
                    selProductionLineId:constants.loginUser.spUserExtDTO.productionLineId,
                    userId:constants.loginUser.userId
                })
            }
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }


    // 租户生产车间
    renderProductLineRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selProductionLineId:item.productionLineId
            }) }}>
                <View key={item.productionLineId} style={item.productionLineId===this.state.selProductionLineId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.productionLineId===this.state.selProductionLineId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.productionLineName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    saveProductionLineSetting=()=>{
        console.log("=======saveProductionLineSetting");
        let toastOpts;
        if (!this.state.selProductionLineId) {
            toastOpts = getFailToastOpts("请选择车间");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/user/ext/sp_add";
        if (constants.loginUser && constants.loginUser.spUserExtDTO) {
            console.log("=========Edit===userId", constants.loginUser.spUserExtDTO.userId)
            url= "/biz//user/ext/sp_modify";
        }
        let requestParams={
            userId:constants.loginUser.userId,
            productionLineId: this.state.selProductionLineId,
        };
        httpPost(url, requestParams, this.saveProductionLineSettingCallBack);
    }

    saveProductionLineSettingCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                constants.loginUser.spUserExtDTO.productionLineId = this.state.selProductionLineId;
                toastOpts = getSuccessToastOpts('所属车间修改成功');
                WToast.show(toastOpts);
                this.props.navigation.goBack();
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return(
            <View >
                <CommonHeadScreen title='所属车间设置'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>所属车间</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0) 
                                ? 
                                this.state.productionLineDataSource.map((item, index)=>{
                                    return this.renderProductLineRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={()=>{
                            this.props.navigation.goBack()
                        }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>返回</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveProductionLineSetting.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>

            </View>
        )}
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     backgroundColor:'#FFFFFF',
    //     height:screenHeight - 140
    // },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})
module.exports = BelongsProductionLineSetting;