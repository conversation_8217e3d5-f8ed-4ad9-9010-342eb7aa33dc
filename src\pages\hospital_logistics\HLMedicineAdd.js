import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class HLMedicineAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            medicineName:"",
            medicineCode:"",
            medicineSpec:"",
            medicineId:"",
            registrationNumber:"",
            guidingPrice:"",
            salePrice:"",
            productionAddr:"",
            medicingSort:0,
            unitId:"",
            unitName:"",
            unitDataSource:[],
            selectUnit:[],

            selMedicineSpec:"",
            selSpecId:1,
            specDataSource:[
                {
                    specId:1,
                    medicineSpecName:"其他",
                },
                {
                    specId:2,
                    medicineSpecName:"自填",
                }
            ],
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        //加载单位
        this.loadUnit();

        if (route && route.params) {
            const { medicineId } = route.params;
            if (medicineId) {
                console.log("=============medicineId" + medicineId + "");
                this.setState({
                    medicineId:medicineId,
                    operate:"编辑"
                    
                })
               let loadTypeUrl = "/biz/hl/medicine/get";
               let loadRequest = { 'medicineId': medicineId };
                httpPost(loadTypeUrl, loadRequest, this.loadHLMedicineCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }

    loadUnit=()=>{
        let loadTypeUrl= "/biz/portal/unit/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000
        };

        httpPost(loadTypeUrl, loadRequest, this.loadUnitListCallBack);

    }

    loadUnitListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                unitDataSource:response.data.dataList
            })
        }
    }

    loadHLMedicineCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                medicineName: response.data.medicineName,
                medicineCode:response.data.medicineCode,
                medicineSpec: response.data.medicineSpec,
                registrationNumber: response.data.registrationNumber,
                guidingPrice: response.data.guidingPrice,
                salePrice: response.data.salePrice,
                productionAddr: response.data.productionAddr,
                medicingSort: response.data.medicingSort,
                unitId:response.data.unitId,
                unitName:response.data.unitName,
                selectUnit:[response.data.unitName],

                selSpecId:response.data.medicineSpec== '其他' ? 1 : 2,

            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLMedicineList", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>药品目录</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent />
    }

    saveHLMedicine = () => {
        console.log("=======saveHLMedicine");
        let toastOpts;
        if (!this.state.medicineName) {
            toastOpts = getFailToastOpts("请输入物资名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.medicineSpec && this.state.selSpecId == 2) {
            toastOpts = getFailToastOpts("请输入规格型号");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.unitId) {
            toastOpts = getFailToastOpts("请选择单位");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.guidingPrice) {
            toastOpts = getFailToastOpts("请输入指导价格");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/hl/medicine/add";
        if (this.state.medicineId) {
            console.log("=========Edit===medicineId", this.state.medicineId)
            url = "/biz/hl/medicine/modify";
        }
        let requestParams = {
            medicineId:this.state.medicineId,
            medicineName: this.state.medicineName,
            medicineCode:this.state.medicineCode,
            medicineSpec:this.state.selSpecId == 1 ? "其他" : this.state.medicineSpec,
            registrationNumber: this.state.registrationNumber,
            guidingPrice: this.state.guidingPrice,
            salePrice: this.state.salePrice,
            productionAddr: this.state.productionAddr,
            medicingSort: this.state.medicingSort,
            unitId: this.state.unitId,

        };
        console.log("======requestParams======",requestParams)
        httpPost(url, requestParams, this.saveHLMedicineCallBack);
    }

    // 保存回调函数
    saveHLMedicineCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    openUnitSelect(){
        if (!this.state.unitDataSource || this.state.unitDataSource.length < 1) {
            WToast.show({data:"请先添加单位"});
            return
        }
        this.refs.SelectUnit.showUnit(this.state.selectUnit, this.state.unitDataSource)
    }
    callBackUnitValue(value){
        console.log("==========单位选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectUnit:value
        })
        var unitName = value.toString();
        let loadUrl= "/biz/portal/unit/getUnitByName";
        let loadRequest={
            "unitName":unitName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadUnitDetailData);
    }

    callBackLoadUnitDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                unitName:response.data.unitName,
                unitId:response.data.unitId,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

        //spec来源 列表展示
        renderSpecRow=(item)=>{
            return (
                <TouchableOpacity onPress={() => {
                        this.setState({
                            selSpecId:item.specId,
                            // sourceName:item.sourceName,
                            // selSourceData:item.selSourceData,
                        })
                        if (item.specId == 2) {
                            this.setState({
                                medicineSpec:""
                            })
                        }
                    }}>
                    <View key={item.specId} style={[item.specId===this.state.selSpecId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                        <Text style={item.specId===this.state.selSpecId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                            {item.medicineSpecName}
                        </Text>
                    </View>
                </TouchableOpacity>
            )
        }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '药品'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入药品名称'}
                            onChangeText={(text) => this.setState({medicineName:text})}
                        >
                            {this.state.medicineName}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                    <View style={[styles.rowLabView]}>
                        <Text style={styles.leftLabNameTextStyle}>型号选择</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.specDataSource && this.state.specDataSource.length > 0) 
                            ? 
                            this.state.specDataSource.map((item, index)=>{
                                return this.renderSpecRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                </View>

                {
                    this.state.selSpecId == 1 ?
                    <View/> :
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>规格型号</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入规格型号'}
                            onChangeText={(text) => this.setState({ medicineSpec:text})}
                        >
                            {this.state.medicineSpec}
                        </TextInput>
                    </View> 
                }
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>注册证号</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入注册证号'}
                            onChangeText={(text) => this.setState({ registrationNumber:text})}
                        >
                            {this.state.registrationNumber}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>单位</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openUnitSelect()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth +30)}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.unitName ? "请选择单位" : this.state.unitName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>                    

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>指导价格</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入指导价格'}
                            onChangeText={(text) => this.setState({ guidingPrice:text})}
                        >
                            {this.state.guidingPrice}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>零售价格</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入零售价格'}
                            onChangeText={(text) => this.setState({ salePrice:text})}
                        >
                            {this.state.salePrice}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>生产地址</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入生产地址'}
                            onChangeText={(text) => this.setState({ productionAddr:text})}
                        >
                            {this.state.productionAddr}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'0'}
                            onChangeText={(text) => this.setState({ medicingSort: text })}
                        >
                            {this.state.medicingSort}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>关联标识</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入关联标识'}
                            onChangeText={(text) => this.setState({ medicineCode:text})}
                        >
                            {this.state.medicineCode}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveHLMedicine.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <BottomScrollSelect 
                            ref={'SelectUnit'} 
                            callBackUnitValue={this.callBackUnitValue.bind(this)}
                        />
                </ScrollView>           
            </View>
        )
    }
}
const styles = StyleSheet.create({

    leftLabNameTextStyle:{
        fontSize:18,
    },
    contentViewStyle:{
       
        height:screenHeight - 90,
        
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight:30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth +30),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },

});