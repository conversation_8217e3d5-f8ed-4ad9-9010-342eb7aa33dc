import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Clipboard,Linking,Image,Modal,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import ImageViewer from 'react-native-image-zoom-viewer';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class OutsourcingStorageOutList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:6,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            customerDataSource:[],
            selCustomerId:null,
            selCustomerName:"",
            gmtCreated:null,
            selectGmtCreated:null,
            compressFileList:[],
            urls:[],
            isShowImage: false,
            pictureIndex:0
        }
    }


    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    initGmtCreated=()=>{
        // 当前时间
        var currentDate = new Date();
        //获取当前时间的毫秒数
        var nowMilliSeconds = currentDate.getTime();
        // 设置查询30内的数据
        currentDate.setTime(nowMilliSeconds - (30 * 86400000));
        currentDate.setMonth(currentDate.getMonth());
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + (currentDate.getDate()-1)).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectGmtCreated:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            gmtCreated:_gmtCreated,
            initGmtCreated:_gmtCreated
        })
        return _gmtCreated;
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        var _gmtCreated = this.initGmtCreated();
        this.loadStorageOutList(_gmtCreated);

        // 加载客户
        let loadTypeUrl= "/biz/tenant/customer/list";
        let loadRequest={'currentPage':1,'pageSize':1000};
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadCustomerData);
    }

    // 查询客户回调函数
    callBackLoadCustomerData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                customerDataSource:response.data.dataList
            })
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/storage/out/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "customerId":this.state.selCustomerId ? this.state.selCustomerId : null,
            "checkOutTime": this.state.gmtCreated ? this.state.gmtCreated : null,
            "storageOutType":"B"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.selCustomerId == null) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            selCustomerName:"",
            selCustomerId:null,
        })
        this.setState({
            currentPage:1
        })
        let url= "/biz/storage/out/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "customerId":this.state.selCustomerId,
            "checkOutTime": this.state.gmtCreated ? this.state.gmtCreated : null,
            "storageOutType":"B"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            // var dataAll = [...dataNew];
            this.setState({
                dataSource:response.data.dataList,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========第一页即是最后一页，不加载=====");
            return;
        }
        this.loadStorageOutList();
    }

    loadStorageOutList=(_gmtCreated)=>{
        let url= "/biz/storage/out/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "customerId":this.state.selCustomerId,
            "checkOutTime": _gmtCreated ? _gmtCreated : this.state.gmtCreated,
            "storageOutType":"B"
        };
        httpPost(url, loadRequest, this.loadStorageOutListCallBack);
    }

    loadStorageOutListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            // var dataAll = dataOld.concat(dataNew.filter(v => !dataOld.includes(v)))
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteStorageOut =(storageOutId)=> {
        console.log("=======delete=storageOutId", storageOutId);
        let url= "/biz/storage/out/delete";
        let requestParams={'storageOutId':storageOutId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    openGmtCreated(){
        this.refs.SelectGmtCreated.showDate(this.state.selectGmtCreated)
    }

    callBackSelectGmtCreatedValue(value){
        console.log("==========时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectGmtCreated:value
        })
        if (this.state.selectGmtCreated && this.state.selectGmtCreated.length) {
            var _gmtCreated = "";
            var vartime;
            for(var index=0;index<this.state.selectGmtCreated.length;index++) {
                vartime = this.state.selectGmtCreated[index];
                if (index===0) {
                    _gmtCreated += vartime;
                }
                else if (index < 3){
                    _gmtCreated += "-" + vartime;
                }
                else if (index===3){
                    _gmtCreated += " " + vartime;
                }
                else {
                    _gmtCreated += ":" + vartime;
                }
            }
            this.setState({
                currentPage: 1,
                gmtCreated:_gmtCreated
            })

            

            let url= "/biz/storage/out/list";
            let loadRequest={
                "currentPage": this.state.currentPage,
                "pageSize": this.state.pageSize,
                "customerId":this.state.selCustomerId,
                "checkOutTime": _gmtCreated ? _gmtCreated : this.state.gmtCreated,
                "storageOutType":"B"
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
            
        }
    }

    exportPdfFile=(storageOutId)=> {
        console.log("=======exportPdfFile=storageOutId", storageOutId);
        let url= "/biz/generate/pdf/storage_out_detail";
        let requestParams={'storageOutId':storageOutId};
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data); 
                WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
                Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                    {
                        text:"不打开", onPress:()=>{
                        WToast.show({data:'点击了不打开'});
                        }
                    },
                    {
                        text:"打开", onPress:()=>{
                            WToast.show({data:'点击了打开'});
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }

    exportPdfFile1=()=> {
        // console.log("=======exportPdfFile=storageOutId", storageOutId);
        let url= "/biz/generate/pdf/storage_out";
        let requestParams={
            "checkOutTime":this.state.gmtCreated,
            "currentPage": 1,
            "pageSize": 1000,
            "customerId":this.state.selCustomerId,
            "storageOutType":"B"
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data); 
                WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
                Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                    {
                        text:"不打开", onPress:()=>{
                        WToast.show({data:'点击了不打开'});
                        }
                    },
                    {
                        text:"打开", onPress:()=>{
                            WToast.show({data:'点击了打开'});
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }

    renderItemDetailRow=(itemDetailList)=>{                
        return (
            <FlatList 
            data={itemDetailList}
            renderItem={({item}) => 
            <View key={item.detailId} style={CommonStyle.titleViewStyle}>
                <View style={{ }}>
                    <Text style={[CommonStyle.titleTextStyle,{flexWrap:"wrap"}]}>外协仓库：{item.outsourcingTenantAbbreviation?item.outsourcingTenantAbbreviation:item.outsourcingTenantName}</Text>
                    <Text style={[CommonStyle.titleTextStyle,{flexWrap:"wrap"}]}>订单名称：{item.orderName}</Text>
                    <Text style={[CommonStyle.titleTextStyle,{flexWrap:"wrap"}]}>砖型：{item.seriesName}-{item.brickTypeName}</Text>
                </View>
                <View style={[{width:(screenWidth - 35) * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10}]}>
                    <View style={[CommonStyle.itemContentChildViewStyle]}>
                        <Text style={CommonStyle.titleTextStyle}>件数：{item.packageAmount}</Text>
                    </View>
                    <View style={[CommonStyle.itemContentChildViewStyle]}>
                        <Text style={CommonStyle.titleTextStyle}>数量：{item.outAmount}</Text>
                    </View>
                    {/* <View style={[styles.itemContentChildViewStyle]}>
                        <Text style={styles.titleTextStyle}>库区：{item.locationAreaName}</Text>
                    </View>
                    <View style={[styles.itemContentChildViewStyle]}>
                        <Text style={styles.titleTextStyle}>库位：{item.locationName}</Text>
                    </View> */}
                    <View style={[CommonStyle.itemContentChildViewStyle]}>
                        <Text style={CommonStyle.titleTextStyle}>重量(吨)：{item.outWeight}</Text>
                    </View>
                    <View style={[CommonStyle.itemContentChildViewStyle]}>
                        <Text style={CommonStyle.titleTextStyle}>包装形式：{item.packageStyleName}</Text>
                    </View>
                </View>
            </View>}
            />
        );
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.storageOutId} style={styles.innerViewStyle}>
                {
                    index == 0 ?
                        <View style={{ width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF', borderBottomWidth: 10, borderBottomColor: '#F4F7F9' }}>
                        </View>
                        :
                        <View></View>
                }
                <View style={CommonStyle.titleViewStyleSpecial}>
                    <Text style={[CommonStyle.titleTextStyleSpecial, {width:screenWidth - 120}]}>发货日期：{item.checkOutTime}</Text>
                    {
                        item.enterpriseName ? 
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                            {item.enterpriseAbbreviation ? item.enterpriseAbbreviation : item.enterpriseName}
                        </Text>
                        :
                        null
                    }
                </View>
                <View style={[CommonStyle.newTitleViewStyle]}>
                    <View>
                        <Text style={[CommonStyle.newTitleTextStyle,{width:null}]} numberOfLines={2}>需货单位：</Text>
                    </View>
                    <View>
                        <Text style={[CommonStyle.newTitleTextStyle,{width:180}]} numberOfLines={2}>{item.customerName}</Text>
                    </View>
                </View>
                {/* <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>需货单位：{item.customerName}</Text>
                </View> */}
                <View style={[CommonStyle.newTitleViewStyle]}>
                    <View>
                        <Text style={[CommonStyle.newTitleTextStyle,{width:null}]} numberOfLines={2}>需货合同：</Text>
                    </View>
                    <View>
                        <Text style={[CommonStyle.newTitleTextStyle,{width:180}]} numberOfLines={2}>{item.contractName}</Text>
                    </View>
                </View>
                {/* <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>需货合同：{item.contractName}</Text>
                </View> */}
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>运输方式：{item.deliveryModeName}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>收货人：{item.consignee ? item.consignee : "无"}</Text>
                </View>
                {
                    (constants.loginUser.tenantId === 66)?
                    <View style={CommonStyle.titleViewStyle}>
                        <Text style={CommonStyle.titleTextStyle}>车型：{item.carModelsName?item.carModelsName:"无"}</Text>
                    </View>
                    :<View/>
                }
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>运输车号：{item.licensePlate}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>承运人：{item.driverName?item.driverName:"无"}</Text>
                </View>
                {
                    (constants.loginUser.tenantId === 66)?
                    <View style={CommonStyle.titleViewStyle}>
                        <Text style={CommonStyle.titleTextStyle}>司机：{item.driveNameExt?item.driveNameExt:"无"}</Text>
                    </View>
                    :<View/>
                }
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>联系电话：{item.driverTel}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>出库时间：{item.gmtCreated}</Text>
                </View>

                {
                    item.compressFileList && item.compressFileList.length > 0 ?
                    (
                        <View>
                            {/* <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>附件：</Text>
                            </View> */}
                            {
                                item.pictureDisplay === "N"?
                                    <View style={[CommonStyle.titleViewStyle, { justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                                        <Text style={CommonStyle.titleTextStyle}>附件：</Text>
                                        <TouchableOpacity onPress={() => {
                                            var urls = [];
                                            if(item.compressFileList && item.compressFileList.length > 0){
                                                for(var i=0;i<item.compressFileList.length;i++){
                                                    var url = {
                                                        url:constants.image_addr + '/' +  item.compressFileList[i].compressFile
                                                    }
                                                    urls=urls.concat(url)
                                                    console.log(url)
                                                }
                                            }
                                            this.setState({
                                                urls:urls
                                            })
                                            let list = this.state.dataSource;
                                            list.map((elem, index) => {
                                                if(elem.storageOutId == item.storageOutId){
                                                    elem.pictureDisplay = "Y"
                                                }
                                            })
                                            this.setState({
                                                dataSource:list
                                            })
                                            // console.log("==============",list)
                                        }}>
                                                <Text style={[CommonStyle.titleTextStyle,{color:"#CB4139"}]}>点击展开</Text>
                                        </TouchableOpacity>
                                    </View>
                                :
                                <View>
                                    <View style={CommonStyle.titleViewStyle}>
                                        <Text style={CommonStyle.titleTextStyle}>附件：</Text>
                                    </View>
                                    <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                        {
                                            item.compressFileList.map((item,index) =>{
                                            return(
                                                <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex'}]}>

                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        isShowImage:true,
                                                        pictureIndex:index
                                                    })
                                                    // uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                                    //     console.log("========imageUploadResponse", imageUploadResponse)
                                                    //     if (imageUploadResponse.code === 200) {
                                                    //         WToast.show({ data: "上传成功" });
                                                    //         let compressFileList = imageUploadResponse.data
                                                    //         this.setState({
                                                    //             compressFileList: compressFileList
                                                    //         })
                                                    //     }
                                                    //     else {
                                                    //         WToast.show({ data: imageUploadResponse.message });
                                                    //     }
                                                    // });

                                                }}>
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />
                                                </TouchableOpacity>
                                                <Modal visible={this.state.isShowImage} transparent={true}>
                                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} onSave={() => alert("点击了保存图片")} onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} />
                                                </Modal>
                                            </View>
                                            )
                                            })
                                        }
                                    </View>
                                    <View style={[CommonStyle.titleViewStyle,{justifyContent:'center'}]}>
                                        {
                                            item.pictureDisplay === "Y"?
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    urls:[]
                                                })
                                                let list = this.state.dataSource;
                                                list.map((elem, index) => {
                                                    if(elem.storageOutId == item.storageOutId){
                                                        elem.pictureDisplay = "N"
                                                    }
                                                })
                                                this.setState({
                                                    dataSource:list
                                                })
                                                // console.log("==============",list)
                                            }}>
                                                    <Text style={[CommonStyle.titleTextStyle,{color:"#CB4139",textAlign:'center'}]}>点击收起</Text>
                                            </TouchableOpacity>
                                            :
                                            <View/>
                                        }
                                    </View>
                                </View>

                            }

                        </View>
                    ):
                    <View style={CommonStyle.titleViewStyle}>
                        <Text style={CommonStyle.titleTextStyle}>附件：无</Text>
                    </View>
                }

                <View style={{}}>
                    {this.renderItemDetailRow(item.spStorageOutDetailDTOList)}
                </View>

                
                <View style={[CommonStyle.itemBottomBtnStyle,{marginRight:15}]}>
                    <TouchableOpacity onPress={()=>{
                        if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                            return;
                        }
                        Alert.alert('确认','您确定要将该条出库信息导出为PDF文件吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.exportPdfFile(item.storageOutId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle
                        ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ,{backgroundColor: '#F2C16D',width:100,flexDirection:'row'}]}>
                            <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/output.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>导出PDF</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                            return;
                        }
                        Alert.alert('确认','您确定要删除该条出库记录吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteStorageOut(item.storageOutId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle
                        ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ,{width:70},{width:80,flexDirection:'row'}]}>
                            <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                                return;
                            }
                            this.props.navigation.navigate("StorageOutAdd", 
                            {
                                // 传递参数
                                storageOutId:item.storageOutId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.hiddenViewStyle, CommonStyle.itemBottomEditBtnViewStyle
                        ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ,{width:70},{width:80,flexDirection:'row'}]}>
                            <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0',marginHorizontal:16}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("OutsourcingStorageOutAdd", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            //     {/* <Text style={CommonStyle.headRightText}>新增出库</Text> */}
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("OutsourcingStorageOutAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    });
                }}  >
                    <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }

        // 渲染客户底部滚动数据
        openCustomerSelect(){
            if (!this.state.customerDataSource || this.state.customerDataSource.length < 1) {
                WToast.show({data:"请先添加客户"});
                return
            }
            this.refs.SelectCustomer.showCustomer(this.state.selectCustomer, this.state.customerDataSource)
        }
        callBackCustomerValue(value){
            console.log("==========客户选择结果：", value)
            if (!value) {
                return;
            }
            this.setState({
                selectCustomer:value
            })
            var customerName = value.toString();
            let loadUrl= "/biz/tenant/customer/getCustomerByName";
            let loadRequest={
                "customerName":customerName
            };
            httpPost(loadUrl, loadRequest, this.callBackLoadCustomerDetailData);
        }
    
        callBackLoadCustomerDetailData=(response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    selCustomerName:response.data.customerName,
                    selCustomerId:response.data.customerId,
                })
                let url= "/biz/storage/out/list";
                let loadRequest={
                    "currentPage": 1,
                    "pageSize": this.state.pageSize,
                    "customerId":response.data.customerId,
                    "gmtCreated": this.state.gmtCreated ? this.state.gmtCreated:"",
                    "storageOutType":"B"
                };
                httpPost(url, loadRequest, this._loadFreshDataCallBack);
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({data:response.message});
                this.setState({
                    selCustomerName:'',
                    selCustomerId:'',
                })
            }
        }
    

    render(){
        return(
            <View>
                <CommonHeadScreen title='外协出库'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.rightAbsoluteButtonContainer]}>
                    <View style={[CommonStyle.rightAbsoluteButtonView]}>
                        <TouchableOpacity onPress={() => this.openGmtCreated()}>
                            <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>
                                {!this.state.gmtCreated ? "时间" : this.state.gmtCreated}
                            </Text>
                        </TouchableOpacity>
                    </View>
                    <View style={[CommonStyle.rightAbsoluteButtonView,{width:110}]}>
                        <TouchableOpacity onPress={()=>this.openCustomerSelect()}>
                            <Text numberOfLines={1} ellipsizeMode='tail' style={[CommonStyle.rightAbsoluteButtonTextView]}>
                            {!this.state.selCustomerName ? "客户" : this.state.selCustomerName}
                            </Text>
                        </TouchableOpacity>
                    </View>

                    <View style={[CommonStyle.rightAbsoluteButtonView,{width:90}]}>
                        <TouchableOpacity onPress={() => {
                            Alert.alert('确认', '您确定要导出PDF文件吗？', [
                                {
                                    text: "取消", onPress: () => {
                                        WToast.show({ data: '点击了取消' });
                                    }
                                },
                                {
                                    text: "确定", onPress: () => {
                                        WToast.show({ data: '点击了确定' });
                                        this.exportPdfFile()
                                    }
                                }
                            ]);
                        }}>
                            <View style={[CommonStyle.rightAbsoluteButtonBoxView]}>
                                <Image  style={[CommonStyle.rightAbsoluteButtonIconView]} source={require('../../assets/icon/iconfont/output.png')}></Image>
                                <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>导出</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View>
                {/* <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {
                    width: 70, backgroundColor: "#F2C16D", flexDirection: "row"
                    ,top:screenHeight/5.3, zIndex: 100, position: 'absolute', right: 15, opacity: 0.6, alignItems: 'center', justifyContent: 'center'
                }]}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要导出PDF文件吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.exportPdfFile1()
                                }
                            }
                        ]);
                    }}>
                        
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 70, backgroundColor: "#F2C16D", flexDirection: "row" }]}>
                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/output.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>导出</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}

                <View style={CommonStyle.contentViewStyle}>
                        <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            ItemSeparatorComponent={this.space}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                            />
                </View>
                <BottomScrollSelect 
                    ref={'SelectCustomer'} 
                    callBackCustomerValue={this.callBackCustomerValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectGmtCreated'} 
                    callBackDateValue={this.callBackSelectGmtCreatedValue.bind(this)}
                />
            </View>

        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginLeft:20
        marginTop:10,
        // borderColor:"#F4F4F4",
        // borderBottomWidth:1,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    // itemContentViewStyle:{
    //     flexDirection:'row',
    //     justifyContent:'space-between',
    //     marginLeft:25
    // },
    itemContentChildViewStyle:{
        justifyContent:'space-between',
        flexDirection:'row',
    },
});