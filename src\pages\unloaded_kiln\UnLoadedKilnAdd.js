import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Keyboard} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'

import CommonHeadScreen from '../../component/CommonHeadScreen';

var CommonStyle = require('../../assets/css/CommonStyle');
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
var cols = 5;
var cellWH = screenWidth / cols;
var vMargin = (screenWidth - cellWH * cols) / (cols + 1);

class UnLoadedKilnAdd extends Component{
    constructor(props){
        super(props);
        this.state = {
            // 软键盘高度
            keyboardHeight:0,
            kilnCarDataSource:[],
            encastageDetailDataSource:[],
            selKilnCarId:0,
            selspUnloadedKilnDetailDTOList:[]
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadFireingKilnCarList();
    }

    loadFireingKilnCarList=()=>{
        let url= "/biz/kiln/car/list";
        let loadRequest={
            "kilnCarState":"W",
            "currentPage": 1,
            "pageSize": 200,
        };
        httpPost(url, loadRequest, this.callBackLoadFireingKilnCarList);
    }

    callBackLoadFireingKilnCarList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                kilnCarDataSource:response.data.dataList,
                selKilnCarId:response.data.dataList[0] ? response.data.dataList[0].kilnCarId : 0,
                encastageDetailDataSource: response.data.dataList[0] ? response.data.dataList[0].spEncastageDetailDTOList : []
            })
            
            if (response.data.dataList[0] && response.data.dataList[0].spEncastageDetailDTOList) {
                // 清空数组
                this.state.selspUnloadedKilnDetailDTOList.length = 0;
                var spUnloadedKilnDetailDTO;
                var encastageDetail;
                for(var index=0; index < response.data.dataList[0].spEncastageDetailDTOList.length; index++) {
                    encastageDetail = response.data.dataList[0].spEncastageDetailDTOList[index];
                    console.log("==========encastageDetail:", encastageDetail);
                    spUnloadedKilnDetailDTO = {
                        "brickTypeId": encastageDetail.brickTypeId,
                        "orderId": encastageDetail.orderId,
                        "amount": encastageDetail.brickAmount,
                        "wasteAmount": 0
                    }
                    this.state.selspUnloadedKilnDetailDTOList.push(spUnloadedKilnDetailDTO);
                }
                console.log("==========selspUnloadedKilnDetailDTOList:", this.state.selspUnloadedKilnDetailDTOList);
            }

        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // componentDidMount(){
    //     if (unloadedKilnAddJSONData.code == 200 && unloadedKilnAddJSONData.data && unloadedKilnAddJSONData.data.dataList) {
    //         this.setState({
    //             kilnCarDataSource:unloadedKilnAddJSONData.data.dataList,
    //             selKilnCarId:unloadedKilnAddJSONData.data.dataList[0] ? unloadedKilnAddJSONData.data.dataList[0].kilnCarId : 0,
    //             encastageDetailDataSource:unloadedKilnAddJSONData.data.dataList[0] ? unloadedKilnAddJSONData.data.dataList[0].spEncastageDetailDTOList : []
    //         })
    //     }
    // }

    //键盘弹起后执行
    _keyboardDidShow(e){
        console.log("=======e", e)
        this.setState({
            keyboardHeight:e.endCoordinates.height
        })
    }

    //键盘收起后执行
    _keyboardDidHide(e){
        this.setState({
            keyboardHeight:0
        })
    }

    componentWillMount() {
        // 监听键盘
        this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow.bind(this));
        this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide.bind(this));
    }
    componentWillUnmount(){
        this.keyboardWillShowListener && this.keyboardWillShowListener.remove();
        this.keyboardWillHideListener && this.keyboardWillHideListener.remove();
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0',marginBottom:10,marginTop:10}}/>)
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("UnLoadedKilnList")
            }}>
                <Text style={CommonStyle.headRightText}>卸窑管理</Text>
            </TouchableOpacity>
        )
    }

    // 窑车单项渲染
    renderKilnCarRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                console.log("==========onPress=Item:", item);
                this.setState({
                    selKilnCarId:item.kilnCarId,
                    encastageDetailDataSource:item.spEncastageDetailDTOList,
                })
                // 清空数组
                this.state.selspUnloadedKilnDetailDTOList.length = 0;
                if (item.spEncastageDetailDTOList) {
                    var spUnloadedKilnDetailDTO;
                    var encastageDetail;
                    for(var index=0; index < item.spEncastageDetailDTOList.length; index++) {
                        encastageDetail = item.spEncastageDetailDTOList[index];
                        console.log("==========encastageDetail:", encastageDetail);
                        spUnloadedKilnDetailDTO = {
                            "brickTypeId": encastageDetail.brickTypeId,
                            "orderId": encastageDetail.orderId,
                            "amount": encastageDetail.brickAmount,
                            "wasteAmount": 0
                        }
                        this.state.selspUnloadedKilnDetailDTOList.push(spUnloadedKilnDetailDTO);
                    }
                }
                console.log("==========selspUnloadedKilnDetailDTOList:", this.state.selspUnloadedKilnDetailDTOList);
             }}>
                <View key={item.kilnCarId} style={item.kilnCarId===this.state.selKilnCarId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.kilnCarId===this.state.selKilnCarId ? CommonStyle.selectedBlockItemTextStyle : CommonStyle.blockItemTextStyle }>
                        {item.kilnCarName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    renderEncastageDetail=(encastageDetailItem)=>{
        return(
            <View key={encastageDetailItem.detailId} 
            style={{ margin:10, flexDirection:'row', justifyContent:'flex-start'}}>
                <View>
                    <View style={{flexDirection:'row'}}>
                        <Text style={CommonStyle.bodyTextStyle}>砖型：</Text>
                        <Text style={styles.brickTypeNameTextStyle}>{encastageDetailItem.brickTypeName}</Text>
                    </View>
                    <View style={{flexDirection:'row', marginTop:10}}>
                        <Text style={CommonStyle.bodyTextStyle}>块数：</Text>
                        <Text style={CommonStyle.bodyTextStyle}>{encastageDetailItem.brickAmount}</Text>
                        <Text style={CommonStyle.bodyTextStyle}> 块</Text>
                    </View>
                </View>
                
                <View style={{ flexDirection:'row', justifyContent:'center', paddingLeft:10}}>
                    <View style={{ justifyContent:'center'}}>
                        <Text style={CommonStyle.bodyTextStyle} >废品数：</Text>
                    </View>
                    <View style={{justifyContent:'center'}}>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.wasteAmountTextStyle}
                            placeholder={'废品数'}
                            onChangeText={(text) => {
                                console.log("======变化的是", encastageDetailItem.brickTypeId);
                                var selspUnloadedKilnDetailDTO;
                                for(var index=0; index<this.state.selspUnloadedKilnDetailDTOList.length;index++){
                                    selspUnloadedKilnDetailDTO = this.state.selspUnloadedKilnDetailDTOList[index];
                                    if (encastageDetailItem.brickTypeId === selspUnloadedKilnDetailDTO.brickTypeId) {
                                        selspUnloadedKilnDetailDTO.wasteAmount = text;
                                        this.state.selspUnloadedKilnDetailDTOList[index] = selspUnloadedKilnDetailDTO;
                                    }
                                }
                            }}
                        >0</TextInput>
                    </View>
                </View>
            </View>
        )
    }

    saveUnLoadKiln =()=> {
        console.log("=======saveUnLoadKiln");
        console.log("======this.state.selspUnloadedKilnDetailDTOList:", this.state.selspUnloadedKilnDetailDTOList);
        let toastOpts;
        // let brickTypes = this.state.encastageDetailDataSource.length;
        // toastOpts = getFailToastOpts("砖数：" + brickTypes);
        // WToast.show(toastOpts)


        if (!this.state.selspUnloadedKilnDetailDTOList || this.state.selspUnloadedKilnDetailDTOList.length === 0) {
            toastOpts = getFailToastOpts("数据不全，不合法，不能完成卸窑");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/unloaded/kiln/record/add";
        let requestParams={
            "kilnCarId": this.state.selKilnCarId,
            "spUnloadedKilnDetailDTOList": this.state.selspUnloadedKilnDetailDTOList
        };
        console.log("=========url:", url)
        console.log("=========requestParams:", requestParams)
        httpPost(url, requestParams, this.saveUnLoadKiln_call_back);
    }

    // 保存回调函数
    saveUnLoadKiln_call_back=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='新增卸窑'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}/>
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>窑车</Text>
                    </View>
                    <View>
                        <FlatList 
                            numColumns = {3}
                            data={this.state.kilnCarDataSource}
                            ItemSeparatorComponent={this.space}
                            ListEmptyComponent={EmptyRowViewComponent}
                            renderItem={({item}) => this.renderKilnCarRow(item)}
                            />
                    </View>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>登记烧制结果</Text>
                    </View>
                    <View>
                        <FlatList 
                            data={this.state.encastageDetailDataSource}
                            ItemSeparatorComponent={this.space}
                            ListEmptyComponent={EmptyRowViewComponent}
                            renderItem={({item}) => this.renderEncastageDetail(item)}
                            />
                    </View>

                    <View style={CommonStyle.btnRowStyle}>
                        {/* <View style={CommonStyle.btnRowLeftCancelBtnView} >
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                        <View style={CommonStyle.btnRowRightSaveBtnView}>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                        </View> */}
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveUnLoadKiln.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    brickTypeNameTextStyle:{
        width:150,
        flexWrap:"wrap", 
        fontSize:15
    },
    wasteAmountTextStyle:{
        width:100,
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})
module.exports = UnLoadedKilnAdd;