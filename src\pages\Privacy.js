import React from 'react';
import {
    Text, View, TouchableOpacity, Image
} from 'react-native';
import CommonHeadScreen from '../component/CommonHeadScreen';
import { WebView } from 'react-native-webview';
var CommonStyle = require('../assets/css/CommonStyle');

export default class Privacy extends React.Component{
    
    constructor(props){
        super(props);
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title=''
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.onlyContainHeaderContentViewStyle}>
                    <WebView 
                        source={{uri:constants.privacyUrl}}
                        scalesPageToFit={true}
                        style={{width:'100%',height:'100%'}} 
                    />
                </View>
                {/* <View>
                    <Text>liminzhi</Text>
                </View> */}
                {/* <WebView
                    originWhitelist={['*']}
                    source={{ html: '<h1>Hello world</h1>' }}
                /> */}
                {/* <WebView
                    source={{uri: 'https://github.com/facebook/react-native'}}
                    style={{marginTop: 20}}
                /> */}
            </View>
            
            // <WebView
            //     originWhitelist={['*']}
            //     source={{ html: '<h1>Hello world</h1>' }}
            // />
        )
    }

}

