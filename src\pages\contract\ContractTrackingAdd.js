import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class ContractTrackingAdd extends Component {
    constructor(){
        super()
        this.state = {
            trackId: "",
            contractId:"",
            trackRemark:"",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { contractId,trackId } = route.params;
            if (contractId) {
                console.log("=====contractId:", contractId);
                this.setState({
                    contractId:contractId,
                })
            }
            if (trackId) {
                console.log("========Edit==trackId:", trackId);
                this.setState({
                    trackId:trackId,
                    operate:"编辑"
                })
                loadTypeUrl= "biz/contract/track/get";
                loadRequest={'trackId':trackId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditContractTrackDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }

    loadEditContractTrackDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                trackId:response.data.trackId,
                contractId:response.data.contractId,
                trackRemark: response.data.trackRemark
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("ContractTrackingList")
            }}>
                <Text style={CommonStyle.headRightText}>合同跟踪</Text>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveContractTrack =()=> {
        console.log("=======saveContractTrack");
        let toastOpts;
        if (!this.state.trackRemark) {
            toastOpts = getFailToastOpts("请输入进展说明");
            WToast.show(toastOpts)
            return;
        }
        
        let url= "/biz/contract/track/add";
        if (this.state.trackId) {
            console.log("=========Edit===trackId", this.state.trackId)
            url= "/biz/contract/track/modify";
        }
        let requestParams={
            trackId:this.state.trackId,
            trackRemark: this.state.trackRemark,
            contractId: this.state.contractId,
            operator: constants.loginUser.userName
        };
        httpPost(url, requestParams, this.saveContractTrackCallBack);
    }
    
    // 保存回调函数
    saveContractTrackCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '跟踪'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>进展说明</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[styles.inputRightText,{height:90}]}
                            placeholder={'请输入进展说明'}
                            onChangeText={(text) => this.setState({trackRemark:text})}
                        >
                            {this.state.trackRemark}
                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView,{flexDirection:'row'}]} >
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/cancel2.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveContractTrack.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})