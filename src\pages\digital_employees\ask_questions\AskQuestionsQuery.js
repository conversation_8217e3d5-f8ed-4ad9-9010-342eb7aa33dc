import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  Linking,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import ImageViewer from 'react-native-image-zoom-viewer';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import EmptyListComponent from '../../../component/EmptyListComponent';
import {saveImage} from '../../../utils/CameraRollUtils';
import {ifIphoneXContentViewDynamicHeight} from '../../../utils/ScreenUtil';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class AskQuestionsQuery extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      operate: '',

      askQuestionsToClaimUserId: '',
      selAskQuestionsStateCode: 'all',
      showSearchItemBlock: false,

      departmentDataSource: null,
      selDepartmentId: null,
      selDepartmentName: null,
      selDepartmentStaffList: null,
      selStaffId: null,
      selStaffName: null,

      qryStartTime: null,
      selectedQryStartDate: [],
      compressFileList: [],
      urls: [],
      isShowImage: false,
      pictureIndex: 0,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  initqryStartTime = () => {
    // 当前时间
    var currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 1);
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _qryStartTime =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      selectedQryStartDate: [
        currentDate.getFullYear(),
        currentDateMonth,
        currentDateDay,
      ],
      qryStartTime: _qryStartTime,
    });
    return _qryStartTime;
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    var _qryStartTime = this.initqryStartTime();
    console.log('componentWillMount==_qryStartTime', _qryStartTime);
    let loadTypeUrl = '/biz/department/list_for_tenant';
    let loadRequest = {qryAll_NoPower: 'Y', currentPage: 1, pageSize: 1000};
    httpPost(loadTypeUrl, loadRequest, (response) => {
      if (response.code == 200 && response.data) {
        this.setState({
          departmentDataSource: response.data,
        });
      }
    });

    let askQuestionsState = [
      {
        stateCode: 'all',
        stateName: '全部',
      },
      {
        stateCode: '0AA',
        stateName: '待解决',
      },
      {
        stateCode: '0BB',
        stateName: '解决中',
      },
      {
        stateCode: '0CC',
        stateName: '已解决',
      },
    ];
    this.setState({
      askQuestionsState: askQuestionsState,
    });
    const {route, navigation} = this.props;

    this.loadAskQuestionsList(_qryStartTime);
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/portal/ask/questions/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      askQuestionsState:
        this.state.selAskQuestionsStateCode === 'all'
          ? null
          : this.state.selAskQuestionsStateCode,
      departmentId: this.state.selDepartmentId,
      askQuestionsUserId: this.state.selStaffId,
      //   qryStartTime: this.state.qryStartTime,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/portal/ask/questions/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      askQuestionsState:
        this.state.selAskQuestionsStateCode === 'all'
          ? null
          : this.state.selAskQuestionsStateCode,
      departmentId: this.state.selDepartmentId,
      askQuestionsUserId: this.state.selStaffId,
      //   qryStartTime: this.state.qryStartTime,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };
  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      let list = dataAll;
      let listNew = [];
      list.map((item, index) => {
        listNew.push(Object.assign({}, item, {pictureDisplay: 'N'}));
      });

      this.setState({
        dataSource: listNew,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  loadAskQuestionsList = (_qryStartTime, selStaffId) => {
    let url = '/biz/portal/ask/questions/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      askQuestionsState:
        this.state.selAskQuestionsStateCode === 'all'
          ? null
          : this.state.selAskQuestionsStateCode,
      departmentId: this.state.selDepartmentId,
      //   qryStartTime: _qryStartTime ? _qryStartTime : this.state.qryStartTime,
      askQuestionsUserId: selStaffId ? selStaffId : this.state.selStaffId,
    };
    httpPost(url, loadRequest, this.loadAskQuestionsListCallBack);
  };

  loadAskQuestionsListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      let list = dataAll;
      let listNew = [];
      list.map((item, index) => {
        listNew.push(Object.assign({}, item, {pictureDisplay: 'N'}));
      });
      this.setState({
        dataSource: listNew,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadAskQuestionsList();
  };

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/ask_questions_query';
    let requestParams = {
      currentPage: 1,
      pageSize: 1000,
      askQuestionsUserId: this.state.selStaffId,
      departmentId: this.state.selDepartmentId,
      qryStartTime: this.state.qryStartTime,
      askQuestionsState:
        this.state.selAskQuestionsStateCode === 'all'
          ? null
          : this.state.selAskQuestionsStateCode,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 22, height: 22}}
          source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
      </TouchableOpacity>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          Alert.alert('确认', '您确定要导出PDF文件吗？', [
            {
              text: '取消',
              onPress: () => {
                WToast.show({data: '点击了取消'});
              },
            },
            {
              text: '确定',
              onPress: () => {
                WToast.show({data: '点击了确定'});
                this.exportPdfFile();
              },
            },
          ]);
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 23, height: 23}}
          source={require('../../../assets/icon/iconfont/outputBlack.png')}></Image>
      </TouchableOpacity>
    );
  }

  claimAskQuestions = (quesItem, index) => {
    console.log('=======setPromotionPlan=quesItem', quesItem);
    let requestUrl = '/biz/portal/ask/questions/modify';
    let requestParams = {
      askQuestionsId: quesItem.askQuestionsId,
      askQuestionsState: quesItem.askQuestionsState === '0BB' ? '0AA' : '0BB',
      askQuestionsToClaimUserId: constants.loginUser.userId,
      compressFileList: quesItem.compressFileList,
    };
    httpPost(requestUrl, requestParams, (response) => {
      if (response.code == 200) {
        // 更新页面上显示
        quesItem.askQuestionsState =
          quesItem.askQuestionsState === '0BB' ? '0AA' : '0BB';
        let askQuestionsDataSource = this.state.dataSource;
        // JS 数组遍历
        askQuestionsDataSource.forEach((obj) => {
          if (obj.askQuestionsId === quesItem.askQuestionsId) {
            obj.askQuestionsState = quesItem.askQuestionsState;
            obj.askQuestionsToClaimUserId = quesItem.askQuestionsToClaimUserId;
            obj.compressFileList = quesItem.compressFileList;
            WToast.show({
              data:
                (quesItem.askQuestionsState === '0BB' ? '认领' : '取消认领') +
                '完成',
            });
          }
        });
        this.callBackFunction();
      } else {
        WToast.show({data: response.message});
      }
    });
  };

  setPictureUrls = (compressFileList) => {
    var urls = [];
    if (compressFileList && compressFileList.length > 0) {
      for (var i = 0; i < compressFileList.length; i++) {
        var url = {
          url: constants.image_addr + '/' + compressFileList[i].compressFile,
        };
        urls = urls.concat(url);
        console.log(url);
      }
    }
    this.setState({
      urls: urls,
    });
  };

  renderRow = (item, index) => {
    // 切换图片显示/隐藏
    const togglePictureDisplay = () => {
      const list = this.state.dataSource.map((elem) => {
        if (elem.askQuestionsId === item.askQuestionsId) {
          return {
            ...elem,
            pictureDisplay: elem.pictureDisplay === 'N' ? 'Y' : 'N',
          };
        }
        return elem;
      });
      this.setState({dataSource: list});
    };

    // 设置图片预览的urls
    const setPictureUrls = () => {
      if (item.compressFileList && item.compressFileList.length > 0) {
        const urls = item.compressFileList.map((file) => ({
          url: constants.image_addr + '/' + file.compressFile,
        }));
        this.setState({urls});
      }
    };
    return (
      <View key={item.askQuestionsId} style={[CommonStyle.innerViewStyle]}>
        <View style={[styles.titleViewStyle, {}]}>
          <Text
            style={[
              styles.titleTextStyle,
              {
                marginRight: 50,
                marginLeft: 15,
                marginTop: 10,
                fontWeight: 'bold',
                fontSize: 20,
                color: '#404956',
              },
            ]}>
            {item.askQuestionsTitle}
          </Text>
          {item.askQuestionsState !== '0AA' ? (
            <View style={{position: 'absolute', right: 0, top: 10}}>
              {item.askQuestionsState === '0BB' ? (
                <Text style={{color: 'green'}}>解决中</Text>
              ) : (
                <Text style={{color: '#CB4139'}}>已解决</Text>
              )}
            </View>
          ) : (
            <View />
          )}
        </View>
        <View style={[styles.titleViewStyle, {}]}>
          <Text style={styles.titleTextStyle}>{item.askQuestionsContent}</Text>
        </View>
        <View style={[styles.titleViewStyle, {}]}>
          <Text selectable={true} style={styles.titleTextStyle}>
            {item.askQuestionsContent}
          </Text>
        </View>
        {item.compressFileList && item.compressFileList.length > 0 ? (
          <View>
            <View
              style={[
                [styles.titleViewStyle, {}],
                {justifyContent: 'flex-start', flexWrap: 'wrap'},
              ]}>
              <Text style={styles.titleTextStyle}>附件：</Text>
              <TouchableOpacity onPress={togglePictureDisplay}>
                <Text style={[styles.titleTextStyle, {color: '#CB4139'}]}>
                  {item.pictureDisplay === 'N' ? '点击展开' : null}
                </Text>
              </TouchableOpacity>
            </View>
            {item.pictureDisplay === 'Y' && (
              <View>
                <FlatList
                  data={item.compressFileList}
                  renderItem={({item: file, index}) => (
                    <View
                      style={{
                        width: 120,
                        height: 150,
                        marginLeft: 10,
                        marginBottom: 10,
                        display: 'flex',
                      }}>
                      <TouchableOpacity
                        onPress={() => {
                          setPictureUrls();
                          this.setState({
                            isShowImage: true,
                            pictureIndex: index,
                          });
                        }}>
                        <Image
                          source={{
                            uri: constants.image_addr + '/' + file.compressFile,
                          }}
                          style={{height: 150, width: 120}}
                        />
                      </TouchableOpacity>
                    </View>
                  )}
                  numColumns={3}
                />
                <Modal visible={this.state.isShowImage} transparent={true}>
                  <ImageViewer
                    onClick={() => this.setState({isShowImage: false})}
                    index={this.state.pictureIndex}
                    enableSwipeDown
                    menuContext={{saveToLocal: '保存到本地', cancel: '取消'}}
                    onSwipeDown={() => this.setState({isShowImage: false})}
                    imageUrls={this.state.urls}
                    onSave={() =>
                      saveImage(this.state.urls[this.state.pictureIndex].url)
                    }
                  />
                </Modal>
                <TouchableOpacity
                  onPress={togglePictureDisplay}
                  style={{width: '100%', alignItems: 'center'}}>
                  <Text style={[styles.titleTextStyle, {color: '#CB4139'}]}>
                    {item.pictureDisplay === 'N' ? '点击展开' : '点击收起'}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        ) : null}

        <View style={[styles.titleViewStyle, {}]}>
          <Text style={styles.titleTextStyle}>
            提问人：{item.askQuestionsUserName}
          </Text>
        </View>
        {item.askQuestionsState === '0BB' ? null : ( // </View> //     <Text style={styles.titleTextStyle}>认领人：{item.askQuestionsToClaimUserName? item.askQuestionsToClaimUserName:"无"}</Text> // <View style={styles.titleViewStyle}>
          <View>
            {item.askQuestionsState === '0CC' ? (
              <View style={[styles.titleViewStyle, {}]}>
                <Text style={styles.titleTextStyle}>
                  解决人：
                  {item.askQuestionsToClaimUserName
                    ? item.askQuestionsToClaimUserName
                    : '无'}
                </Text>
              </View>
            ) : (
              <View />
            )}
          </View>
        )}
        <View style={[styles.titleViewStyle, {}]}>
          <Text style={styles.titleTextStyle}>提问时间：{item.gmtCreated}</Text>
        </View>
        {item.askQuestionsState == '0CC' ? (
          <View style={[styles.titleViewStyle, {}]}>
            <Text style={styles.titleTextStyle}>
              解决时间：{item.gmtModified}
            </Text>
          </View>
        ) : (
          <View />
        )}
        <View style={[styles.titleViewStyle, {}]}>
          {item.askQuestionsState === '0BB' ? (
            <Text style={{fontSize: 14, color: '#FF8C28'}}>
              {' ' + item.askQuestionsToClaimUserName + ' 已认领该提问'}
            </Text>
          ) : null}
        </View>
        <View style={[CommonStyle.itemBottomBtnStyle, {flexWrap: 'wrap'}]}>
          <TouchableOpacity
            onPress={() => {
              this.props.navigation.navigate('AskQuestionsSolveTrackingList', {
                askQuestionsId: item.askQuestionsId,
                listTitleName: '解决进展',
                operate: 'query',
              });
            }}>
            <View
              style={[
                CommonStyle.itemBottomStudyGreyBtnViewStyle,
                {marginRight: 10, width: 64},
              ]}>
              <Image
                style={{width: 17, height: 17, marginRight: 3}}
                source={require('../../../assets/icon/iconfont/progress.png')}></Image>
              <Text style={[{color: '#F0F0F0', fontSize: 14, lineHeight: 20}]}>
                进展
              </Text>
            </View>
          </TouchableOpacity>
          {item.askQuestionsState === '0CC' ||
          item.askQuestionsState === '0BB' ? (
            <View></View>
          ) : (
            <TouchableOpacity
              onPress={() => {
                let message =
                  '您确定要' +
                  (item.askQuestionsState === '0BB' ? '取消认领' : '认领') +
                  '该提问吗？';
                Alert.alert('确认', message, [
                  {
                    text: '取消',
                    onPress: () => {
                      WToast.show({data: '点击了取消'});
                    },
                  },
                  {
                    text: '确定',
                    onPress: () => {
                      WToast.show({data: '点击了确定'});
                      this.claimAskQuestions(item);
                    },
                  },
                ]);
              }}>
              <View
                style={[
                  {
                    width: 64,
                    height: 28,
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    margin: 10,
                    borderColor: '#FF8C28',
                    borderWidth: 0.85,
                    borderRadius: 6,
                  },
                ]}>
                {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                <Text
                  style={[{color: '#FF8C28', fontSize: 14, lineHeight: 20}]}>
                  {item.askQuestionsState === '0BB' ? '取消认领' : '认领'}
                </Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };
  renderAskQuestionsStateRow = (item, index) => {
    return (
      <View key={item.stateCode} style={{width: '25%'}}>
        <TouchableOpacity
          onPress={() => {
            let selAskQuestionsStateCode = item.stateCode;
            this.setState({
              selAskQuestionsStateCode: selAskQuestionsStateCode,
            });
            let loadUrl = '/biz/portal/ask/questions/list';
            let loadRequest = {
              currentPage: 1,
              pageSize: this.state.pageSize,
              departmentId: this.state.selDepartmentId,
              askQuestionsUserId: this.state.selStaffId,
              askQuestionsState:
                selAskQuestionsStateCode === 'all'
                  ? null
                  : selAskQuestionsStateCode,
              //   qryStartTime: this.state.qryStartTime,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
          }}>
          <View key={item.stateCode} style={[CommonStyle.tabItemViewStyle]}>
            <Text
              style={[
                item.stateCode === this.state.selAskQuestionsStateCode
                  ? [CommonStyle.selectedtabItemTextStyle]
                  : [CommonStyle.tabItemTextStyle],
              ]}>
              {item.stateName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };
  // 部门
  renderDepartmentRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selDepartmentId: item.departmentId,
            selDepartmentName: item.departmentName,
            selDepartmentStaffDataSource: item.departmentUserDTOList,
            selStaffId: null,
            selStaffName: null,
          });
        }}>
        <View
          key={'department_' + item.departmentId}
          style={[
            item.departmentId === this.state.selDepartmentId
              ? {
                  backgroundColor: 'rgba(83,100,255,0.1)',
                }
              : {
                  backgroundColor: 'rgba(246,246,246,1)',
                },
            {
              //外边距
              marginLeft: 6,
              marginRight: 6,
              marginTop: 8,
              marginBottom: 0,
              //内边距
              paddingTop: 5,
              paddingBottom: 5,
              paddingLeft: 15,
              paddingRight: 15,
              borderRadius: 4,
              justifyContent: 'center',
              height: 30,
              borderRadius: 4,
            },
          ]}>
          <Text
            style={[
              item.departmentId === this.state.selDepartmentId
                ? {
                    color: 'rgba(30,110,250,1)',
                  }
                : {
                    color: 'rgba(0,10,32,0.45)',
                  },
              {
                fontSize: 16,
              },
            ]}>
            {item.departmentName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
  // 显示搜索项目
  showSearchItemSelect() {
    if (
      !this.state.departmentDataSource ||
      this.state.departmentDataSource.length < 1
    ) {
      WToast.show({data: '请先添加部门'});
      return;
    }
    this.setState({
      showSearchItemBlock: true,
    });
  }

  renderDepartmentStaffRow = (item, index) => {
    return (
      <View key={item.jobUserId}>
        <TouchableOpacity
          onPress={() => {
            this.setState({
              selStaffId: item.userId,
              selStaffName: item.staffName,
            });
          }}>
          <View
            key={'jobuser_' + item.jobUserId}
            style={[
              item.userId === this.state.selStaffId
                ? {
                    backgroundColor: 'rgba(83,100,255,0.1)',
                  }
                : {
                    backgroundColor: 'rgba(246,246,246,1)',
                  },
              {
                //外边距
                marginLeft: 6,
                marginRight: 6,
                marginTop: 8,
                marginBottom: 0,
                //内边距
                paddingTop: 5,
                paddingBottom: 5,
                paddingLeft: 15,
                paddingRight: 15,
                borderRadius: 4,
                justifyContent: 'center',
                height: 30,
                borderRadius: 4,
              },
            ]}>
            <Text
              style={[
                item.userId === this.state.selStaffId
                  ? {
                      color: 'rgba(30,110,250,1)',
                    }
                  : {
                      color: 'rgba(0,10,32,0.45)',
                    },
                {
                  fontSize: 16,
                },
              ]}>
              {item.staffName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  openQryStartDate() {
    this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate);
  }

  callBackSelectQryStartDateValue(value) {
    console.log('==========提交时间选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectedQryStartDate: value,
    });
    if (value && value.length) {
      var qryStartTime = '';
      var vartime;
      for (var index = 0; index < value.length; index++) {
        vartime = value[index];
        if (index === 0) {
          qryStartTime += vartime;
        } else {
          qryStartTime += '-' + vartime;
        }
      }
      this.setState({
        qryStartTime: qryStartTime,
      });
      let loadUrl = '/biz/portal/ask/questions/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        departmentId: this.state.selDepartmentId,
        askQuestionsUserId: this.state.selStaffId,
        // qryStartTime: qryStartTime,
        askQuestionsState:
          this.state.selAskQuestionsStateCode === 'all'
            ? null
            : this.state.selAskQuestionsStateCode,
      };
      httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }
  }

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="提问查询"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        {/* <View
          style={[
            CommonStyle.rightTop50FloatingBlockView,
            {
              height: 32,
              width: 110,
              opacity: 0.6,
              borderRadius: 8,
              backgroundColor: 'rgba(242,242,242,1)',
            },
          ]}>
          <TouchableOpacity onPress={() => this.openQryStartDate()}>
            <Text style={{color: 'rgba(0,10,32,0.85)', fontSize: 14}}>
              {!this.state.qryStartTime ? '时间' : this.state.qryStartTime}
            </Text>
          </TouchableOpacity>
        </View> */}

        <View
          style={[CommonStyle.headViewStyle]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View style={{width: '100%', flexWrap: 'wrap', flexDirection: 'row'}}>
            {this.state.askQuestionsState &&
            this.state.askQuestionsState.length > 0 ? (
              this.state.askQuestionsState.map((item, index) => {
                return this.renderAskQuestionsStateRow(item);
              })
            ) : (
              <View />
            )}
          </View>
          <View style={{width: '100%', flexWrap: 'wrap', flexDirection: 'row'}}>
            <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
              {this.state.showSearchItemBlock ? (
                <View style={[CommonStyle.choseToSearchViewStyle]}>
                  <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                    {this.state.selDepartmentId && this.state.selDepartmentName
                      ? this.state.selDepartmentName
                      : '选择部门'}
                  </Text>
                  <Image
                    style={[CommonStyle.choseToSearchClosedIconSize]}
                    source={require('../../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                </View>
              ) : (
                <View style={[CommonStyle.choseToSearchViewStyle]}>
                  <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                    {this.state.selDepartmentId && this.state.selDepartmentName
                      ? this.state.selDepartmentName
                      : '选择部门'}
                  </Text>
                  <Image
                    style={[CommonStyle.choseToSearchOpenedIconSize]}
                    source={require('../../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                </View>
              )}
            </TouchableOpacity>
            {this.state.selStaffId && this.state.selStaffName ? (
              <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                {this.state.showSearchItemBlock ? (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                      {this.state.selStaffName}
                    </Text>
                  </View>
                ) : (
                  <View style={[CommonStyle.choseToSearchViewStyle]}>
                    <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                      {this.state.selStaffName}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        <View>
          {this.state.showSearchItemBlock ? (
            <View
              style={[
                CommonStyle.choseToSearchBigBoxViewStyle,
                {
                  height: ifIphoneXContentViewDynamicHeight(
                    this.state.topBlockLayoutHeight,
                  ),
                },
              ]}>
              <View style={CommonStyle.heightLimited}>
                <ScrollView>
                  <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                    <View
                      style={[
                        {backgroundColor: 'rgba(255,255,255,1)'},
                        CommonStyle.choseToSearchItemsViewSize,
                      ]}>
                      <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                        部门：
                      </Text>
                    </View>
                    {this.state.departmentDataSource &&
                    this.state.departmentDataSource.length > 0
                      ? this.state.departmentDataSource.map((item, index) => {
                          return this.renderDepartmentRow(item);
                        })
                      : null}
                  </View>
                  {this.state.selDepartmentStaffDataSource &&
                  this.state.selDepartmentStaffDataSource.length > 0 ? (
                    <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                      <View
                        style={[
                          {backgroundColor: 'rgba(255,255,255,1)'},
                          CommonStyle.choseToSearchItemsViewSize,
                        ]}>
                        <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                          提交人：
                        </Text>
                      </View>
                      {this.state.selDepartmentStaffDataSource.map(
                        (item, index) => {
                          return this.renderDepartmentStaffRow(item);
                        },
                      )}
                    </View>
                  ) : null}
                </ScrollView>
              </View>
              <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]}>
                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    let loadUrl = '/biz/portal/ask/questions/list';
                    let loadRequest = {
                      currentPage: 1,
                      pageSize: this.state.pageSize,
                      askQuestionsState:
                        this.state.selAskQuestionsStateCode === 'all'
                          ? null
                          : this.state.selAskQuestionsStateCode,
                      departmentId: this.state.selDepartmentId,
                      askQuestionsUserId: this.state.selStaffId,
                      //   qryStartTime: this.state.qryStartTime,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                    this.setState({
                      showSearchItemBlock: false,
                    });
                  }}>
                  <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>
                      确定搜索
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}
          <View
            style={[
              CommonStyle.contentViewStyle,
              {
                height: ifIphoneXContentViewDynamicHeight(
                  this.state.topBlockLayoutHeight,
                ),
              },
            ]}>
            <FlatList
              data={this.state.dataSource}
              renderItem={({item, index}) => this.renderRow(item, index)}
              ListEmptyComponent={this.emptyComponent}
              keyExtractor={(item) => item.askQuestionsId}
              // 自定义下拉刷新
              refreshControl={
                <RefreshControl
                  tintColor="#FF0000"
                  title="loading"
                  colors={['#FF0000', '#00FF00', '#0000FF']}
                  progressBackgroundColor="#FFFF00"
                  refreshing={this.state.refreshing}
                  onRefresh={() => {
                    this._loadFreshData();
                  }}
                />
              }
              // 底部加载
              ListFooterComponent={() => this.flatListFooterComponent()}
              onEndReached={() => this._loadNextData()}
            />
          </View>
        </View>
        <BottomScrollSelect
          ref={'SelectQryStartDate'}
          callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  innerHeadViewStyle: {
    borderColor: '#ffffff',
    borderWidth: 4,
    backgroundColor: '#ffffff',
  },
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    // marginTop:10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  // 分段器样式
  blockItemViewStyle: {
    margin: 5,
    width: 60,
    borderRadius: 0,
    paddingTop: 2,
    paddingBottom: 0,
    paddingLeft: 2,
    paddingRight: 2,
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
  },
  selectedBlockItemViewStyle: {
    margin: 5,
    width: 60,
    borderRadius: 0,
    paddingTop: 2,
    paddingBottom: 0,
    paddingLeft: 2,
    paddingRight: 2,
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
  },

  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
    marginLeft: 15,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
});
