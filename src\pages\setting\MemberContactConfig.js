import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image, TextInput,Modal, Linking
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ScrollView } from 'react-native-gesture-handler';
import { WebView } from 'react-native-webview';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const {ifIphoneXContentViewHeight} = require('../../utils/ScreenUtil');
import EmptyPortalTenantComponent from '../../component/EmptyPortalTenantComponent';

const leftLabWidth = 300;
export default class MemberContactConfig extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            pictureIndex:0,
            isShowImage:false,
            image: "",
            titleName: "",
            advertisingType: "",
            contactConfigList:["MEMBER_SHARE_WEBSITE_CONTACT_US","MEMBER_SHARE_WEBSITE_CONTACT_PLATFORM"],
            paramCode:"",
            paramName:"",
            paramValue: "",
            ContactConfigData: {},
            moreModal: false,
            deleteModal: false,
            previewModal: false,
            configItem: {},
            previewAdvertisingContent: ""
        }
    }

   

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { paramCode, paramName, paramValue } = route.params;
            if (paramName) {
                this.setState({
                    paramName: paramName
                })
            }
            if (paramValue) {
                this.setState({
                    paramValue: paramValue
                })
            }
            if (paramCode) {
                this.setState({
                    paramCode: paramCode,
                })
                if (paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_US") {
                    this.setState({
                        titleName: "联系我们",
                        advertisingType: "E"
                    })
                }
                if (paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_PLATFORM") {
                    this.setState({
                        titleName: "联系平台",
                        advertisingType: "P"
                    })
                }
            }
            this.loadContactConfig(paramCode);
            this.loadContactConfigDraftList(paramCode);
        }
        
    }

    // 回调函数
    callBackFunction = () => {
        this.loadContactConfig(this.state.paramCode);
        this.loadContactConfigDraftList(this.state.paramCode);
    }

    // 加载显示数据
    loadContactConfig = (paramCode) => {
        console.log("loadContactConfig======")
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10000,
            "isManagerQry": "Y",
            "advertisingState": "0AA",
            "advertisingType": paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_US" ? "E" : (paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_PLATFORM" ? "P" : "")
        };
        httpPost(url, loadRequest, this.loadContactConfigCallBack);
    }

    loadContactConfigCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log('loadContactConfig...........', response.data.dataList)
            var dataList = response.data.dataList;
            this.setState({
                ContactConfigData: dataList[0],
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 加载草稿箱列表数据
    loadContactConfigDraftList = (paramCode) => {
        console.log("loadContactConfigDraftList======")
        let url = "/biz/portal/advertising/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10000,
            "isManagerQry": "Y",
            "advertisingState": "0AB",
            "advertisingType": paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_US" ? "E" : (paramCode == "MEMBER_SHARE_WEBSITE_CONTACT_PLATFORM" ? "P" : "")
        };
        httpPost(url, loadRequest, this.loadContactConfigDraftListCallBack);
    }

    loadContactConfigDraftListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log('loadContactConfigDraftList...........', response.data.dataList)
            var dataNew = response.data.dataList;
            // var dataOld = this.state.dataSource;
            // var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataNew,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadPortalTenantConfigDataList();
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => {
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
            }} style={[{ marginBottom: 1.5 }]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.ContactConfigData) {
                    WToast.show({ data: '已自定义内容，不可重复添加' });
                    return;
                }
                this.props.navigation.navigate("MemberContactConfigAdd",
                    {
                        // 传递参数
                        paramCode: this.state.paramCode,
                        paramName: this.state.paramName,
                        paramValue: this.state.paramValue,
                        advertisingType: this.state.advertisingType,
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/addBlack.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 移入草稿箱
    moveToDraft = (configItem) => {
        console.log("======= moveToDraft");
        let url = "/biz/portal/advertising/draft";
        let requestParams = {
            advertisingId: configItem.advertisingId,
            advertisingState: "0AB",
            advertisingLink: configItem.advertisingLink,
            advertisingContent: configItem.advertisingContent,
            advertisingType: configItem.advertisingType
        };
        httpPost(url, requestParams, this.moveToDraftCallBack);
    }

    // 回调函数
    moveToDraftCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                this.loadContactConfig(this.state.paramCode);
                this.loadContactConfigDraftList(this.state.paramCode);
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    // 移出草稿箱
    displayContent = (configItem) => {
        console.log("======= displayContent");
        let url = "/biz/portal/advertising/draft";
        let requestParams = {
            advertisingId: configItem.advertisingId,
            advertisingState: "0AA",
            advertisingLink: configItem.advertisingLink,
            advertisingContent: configItem.advertisingContent,
            advertisingType: configItem.advertisingType
        };
        httpPost(url, requestParams, this.displayContentCallBack);
    }

    // 回调函数
    displayContentCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                this.loadContactConfig(this.state.paramCode);
                this.loadContactConfigDraftList(this.state.paramCode);
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }


    // 删除配置
    deleteContactConfig = (advertisingId) => {
        console.log("=======delete=advertisingId", advertisingId);
        let url = "/biz/portal/advertising/delete";
        let requestParams = { 'advertisingId': advertisingId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.loadContactConfig(this.state.paramCode);
            this.loadContactConfigDraftList(this.state.paramCode);
            WToast.show({ data: "删除完成" });
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    openLinks = (link) => {
        console.log("=======openLinks", link);
        Linking.openURL(link)
    }

    openPreview = (advertisingId) => {
        let url = constants.websit_server_Addr +"/contactDetailPreviewPage.html?tenantId=" + constants.loginUser.tenantId + "&contactType=" + this.state.advertisingType + "&advertisingId=" + advertisingId
        console.log("=======openPreview", url);
        Linking.openURL(url)
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyPortalTenantComponent/>
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.paramName}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>自定义内容</Text>
                    </View>
                    {
                        this.state.ContactConfigData ? 
                            <View style={{backgroundColor:'rgba(242, 245, 252, 0.5)', borderRadius:10,width:screenWidth-24, marginTop: 10, marginLeft: 12, marginRight: 12, marginBottom: 10}}>
                                <TouchableOpacity 
                                style={{position:'absolute',right:10,zIndex:99999}}
                                onPress={() => {
                                    this.setState({
                                        moreModal: true,
                                        configItem: this.state.ContactConfigData
                                    })
                                }}>
                                    <View style={[{width: 28, height: 28, justifyContent:'center', alignItems: 'center'}]}>
                                        <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                                    </View>
                                </TouchableOpacity>
                                <View style={styles.itemContentTextStyle}>
                                    <Text style={styles.itemContentStyle}>{!this.state.ContactConfigData.advertisingLink && this.state.ContactConfigData.advertisingContent != "<p></p>" ? "自定义图文" : (this.state.ContactConfigData.advertisingLink && this.state.ContactConfigData.advertisingContent == "<p></p>" ? "自定义链接" : "自定义图文")}</Text>
                                </View>
                                {/* <View style={styles.itemContentTextStyle}>
                                    <Text style={styles.itemContentStyle}>配置内容：</Text>
                                </View> */}
                                {
                                    this.state.ContactConfigData.advertisingLink ?
                                        <TouchableOpacity onPress={() => { this.openLinks(this.state.ContactConfigData.advertisingLink) }}>
                                            <View style={styles.itemContentTextStyle}>
                                                <Text style={[styles.itemContentStyle, {color: '#0000EE'}]}>{this.state.ContactConfigData.advertisingLink}</Text>
                                            </View>
                                        </TouchableOpacity>
                                        :
                                        (
                                            this.state.ContactConfigData.advertisingContent != "<p></p>" ?
                                                <TouchableOpacity onPress={() => { this.openPreview(this.state.ContactConfigData.advertisingId) }}>
                                                    <View style={styles.itemContentTextStyle}>
                                                        <Text style={[styles.itemContentStyle, {color: '#0000EE'}]}>点击预览</Text>
                                                    </View>
                                                </TouchableOpacity>
                                                :
                                                <TouchableOpacity onPress={() => { this.openPreview(this.state.ContactConfigData.advertisingId) }}>
                                                    <View style={styles.itemContentTextStyle}>
                                                        <Text style={[styles.itemContentStyle, {color: '#0000EE'}]}>点击预览</Text>
                                                    </View>
                                                </TouchableOpacity>
                                        )
                                }
                                <View style={styles.itemContentTextStyle}>
                                    <Text style={styles.itemContentStyle}>{this.state.ContactConfigData.gmtModified ? this.state.ContactConfigData.gmtModified.slice(0,16) : (this.state.ContactConfigData.gmtCreated ? this.state.ContactConfigData.gmtCreated.slice(0,16) : "")}</Text>
                                </View>

                                {
                                    !this.state.ContactConfigData.advertisingLink && this.state.ContactConfigData.advertisingContent != "<p></p>" ?
                                        <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                                            <TouchableOpacity 
                                            onPress={() => {
                                                this.props.navigation.navigate("ConfigPreview",
                                                {
                                                    // 传递参数
                                                    configContent: this.state.ContactConfigData.advertisingContent,
                                                    // 传递回调函数
                                                    refresh: this.callBackFunction
                                                })
                                            }}>
                                                <View>
                                                    <View style={{ height: 30, width: 75, backgroundColor: "rgba(30,110,250,1)", margin: 10, marginRight: 16, 
                                                        flexDirection: 'row', justifyContent: 'center', alignItems: 'center', borderRadius: 3
                                                    }}>
                                                        <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/preview.png')}></Image>
                                                        <Text style={{color: '#FFFFFF', fontSize: 16,}}>预览</Text>
                                                    </View>
                                                </View>
                                            </TouchableOpacity>
                                        </View>
                                        :
                                        <View></View>
                                }
                            </View>
                            :
                            <View style={{backgroundColor:'rgba(242, 245, 252, 0.5)', borderRadius:10,width:screenWidth-24, marginTop: 10, marginLeft: 12, marginRight: 12, marginBottom: 10}}>
                                <View style={styles.itemContentTextStyle}>
                                    <Text style={styles.itemContentStyle}>暂无自定义内容</Text>
                                </View>
                            </View>
                    }

                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>草稿箱</Text>
                    </View>

                    {
                        (this.state.dataSource && this.state.dataSource.length > 0) ?
                            <View style={{backgroundColor:'rgba(242, 245, 252, 0.5)', borderRadius:10,width:screenWidth-24, marginTop: 10, marginLeft: 12, marginRight: 12, marginBottom: 15, paddingBottom: 5}}>
                                {
                                    this.state.dataSource.map((item, index)=>{
                                        return(
                                            <View key={item.advertisingId} style={{ marginTop: 5, marginBottom: 5 }}>
                                                {/* <View style={{ position:'absolute', right: 0, top: 0}}> */}
                                                    <TouchableOpacity 
                                                    style={{position:'absolute',right:10,zIndex:9999}}
                                                    onPress={() => {
                                                        this.setState({
                                                            moreModal: true,
                                                            configItem: item
                                                        })
                            
                                                    }}>
                                                        <View style={[{width: 28, height: 28, justifyContent:'center', alignItems: 'center'}]}>
                                                            <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                                                        </View>
                                                    </TouchableOpacity>
                                                {/* </View> */}
                                                <View style={styles.itemContentTextStyle}>
                                                    <Text style={styles.itemContentStyle}>{!item.advertisingLink && item.advertisingContent != "<p></p>" ? "自定义图文" : (item.advertisingLink && item.advertisingContent == "<p></p>" ? "自定义链接" : "自定义图文")}</Text>
                                                </View>
                                                {
                                                    item.advertisingLink ?
                                                        <TouchableOpacity onPress={() => { this.openLinks(item.advertisingLink) }}>
                                                            <View style={styles.itemContentTextStyle}>
                                                                <Text style={[styles.itemContentStyle, {color: '#0000EE'}]}>{item.advertisingLink}</Text>
                                                            </View>
                                                        </TouchableOpacity>
                                                        :
                                                        (
                                                            item.advertisingContent != "<p></p>" ?
                                                                <TouchableOpacity onPress={() => { this.openPreview(item.advertisingId) }}>
                                                                    <View style={styles.itemContentTextStyle}>
                                                                        <Text style={[styles.itemContentStyle, {color: '#0000EE'}]}>点击预览</Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                                :
                                                                <TouchableOpacity onPress={() => { this.openPreview(item.advertisingId) }}>
                                                                    <View style={styles.itemContentTextStyle}>
                                                                        <Text style={[styles.itemContentStyle, {color: '#0000EE'}]}>点击预览</Text>
                                                                    </View>
                                                                </TouchableOpacity>
                                                        )
                                                }

                                                <View style={styles.itemContentTextStyle}>
                                                    <Text style={styles.itemContentStyle}>{item.gmtModified ? item.gmtModified.slice(0,16) : (item.gmtCreated ? item.gmtCreated.slice(0,16) : "")}</Text>
                                                </View>

                                                {
                                                    !item.advertisingLink && item.advertisingContent != "<p></p>" ?
                                                        <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                                                            <TouchableOpacity onPress={() => {
                                                                this.props.navigation.navigate("ConfigPreview",
                                                                    {
                                                                        // 传递参数
                                                                        configContent: item.advertisingContent,
                                                                        // 传递回调函数
                                                                        refresh: this.callBackFunction
                                                                    })
                                                            }}>
                                                                <View>
                                                                    {
                                                                        <View style={{ height: 30, width: 75, backgroundColor: "rgba(30,110,250,1)", margin: 10, marginRight: 16, 
                                                                            flexDirection: 'row', justifyContent: 'center', alignItems: 'center', borderRadius: 3
                                                                        }}>
                                                                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/preview.png')}></Image>
                                                                            <Text style={{color: '#FFFFFF', fontSize: 16,}}>预览</Text>
                                                                        </View>
                                                                    }
                                                                </View>
                                                            </TouchableOpacity>
                                                        </View>
                                                        :
                                                        <View></View>
                                                }
                                                
                                                
                                                <View style={styles.lineViewStyle}/> 
                                            </View>
                                        )                           
                                    })
                                }
                            </View>
                            :
                            <View style={{backgroundColor:'rgba(242, 245, 252, 0.5)', borderRadius:10,width:screenWidth-24, marginTop: 10, marginLeft: 12, marginRight: 12, marginBottom: 10}}>
                                <View style={styles.itemContentTextStyle}>
                                    <Text style={styles.itemContentStyle}>草稿箱暂无内容</Text>
                                </View>
                            </View>
                    }
                </ScrollView>

                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            {
                                this.state.configItem && this.state.configItem.advertisingState == "0AA" ?
                                    <View>
                                        <TouchableOpacity onPress={() => {
                                            this.setState({
                                                moreModal: false,
                                            })
                                            WToast.show({ data: '点击了确定' });
                                            this.moveToDraft(this.state.configItem)
                                            // this.removePortalTenantParam()
                                        }}>
                                            <View style={[{ width: 145, height: 50, paddingLeft: 30, marginTop: 5 }]}>
                                                <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>移入草稿箱</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    :
                                    <View>
                                        <TouchableOpacity onPress={() => {
                                            if (this.state.ContactConfigData) {
                                                WToast.show({ data: '已存在自定义内容，不可移出' });
                                                return;
                                            }
                                            this.setState({
                                                moreModal: false,
                                            })
                                            WToast.show({ data: '点击了确定' });
                                            this.displayContent(this.state.configItem)
                                        }}>
                                            <View style={[{ width: 145, height: 50, paddingLeft: 30, marginTop: 5 }
                                                , (this.state.ContactConfigData) ? CommonStyle.disableViewStyle : ""]}>
                                                <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>移出草稿箱</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                            }

                            <View>
                                <TouchableOpacity onPress={() => {
                                    if (!this.state.configItem.advertisingLink && this.state.configItem.advertisingContent) {
                                        WToast.show({ data: '自定义图文不可编辑' });
                                        return;
                                    }
                                    console.log("this.state.paramCode===", this.state.paramCode)
                                    this.setState({
                                        moreModal: false,
                                    })
                                    this.props.navigation.navigate("MemberContactConfigAdd",
                                        {
                                            // 传递参数
                                            paramCode: this.state.paramCode,
                                            paramName: this.state.paramName,
                                            paramValue: this.state.paramValue,
                                            advertisingId: this.state.configItem.advertisingId,
                                            advertisingType: this.state.advertisingType,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , (!this.state.configItem.advertisingLink && this.state.configItem.advertisingContent) ? CommonStyle.disableViewStyle : ""]}>
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => {
                                    console.log("configItem=================",this.state.configItem)
                                    // 删除弹窗Modal
                                    this.setState({
                                        moreModal: false,
                                        deleteModal: true
                                    })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}]}>
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                
                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>
                                    {
                                        !this.state.configItem.advertisingLink && this.state.configItem.advertisingContent != "<p></p>" ?
                                            "确认删除该图文?"
                                            :
                                            this.state.configItem.advertisingLink && this.state.configItem.advertisingContent == "<p></p>" ?
                                                "确认删除该链接?"
                                                :
                                                "确认删除该配置?"
                                    }
                                </Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteContactConfig(this.state.configItem.advertisingId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                {/* 预览弹窗 */}
                {/* <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.previewModal}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.08)' }]}>
                        <View style={{ width: 292, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <View style={[styles.titleViewStyle]}>
                                    <Text style={[styles.titleTextStyle, {fontSize: 16}]}>预览信息</Text>
                                </View>
                                <View style={{flex: 1}}>
                                    <WebView bounces={false}
                                        scalesPageToFit={true}
                                        source={{html: this.state.previewAdvertisingContent}}
                                        style={{width:280, height:300}}>
                                        </WebView>
                                </View>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        previewModal: false
                                    });
                                    WToast.show({ data: '点击了关闭' });
                                }}>
                                    <View style={{width: 292, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17,fontWeight: '400', color: '#000A20', }}>关闭</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal> */}
            </View>
        )
    }
}
const styles = StyleSheet.create({
    leftLabNameTextStyle: {
        fontSize: 24,
        fontWeight:'bold',
        color:"#787878"
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#000000'
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight: 30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 35,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 14,
        marginRight: 16,
        marginTop: 5
    },
    titleTextStyle: {
        fontSize: 18,
        lineHeight: 22,
        fontWeight: "bold"
    },
    itemContentTextStyle: {
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    itemContentStyle: {
        flexDirection:'row',
        alignItems: 'center',
        lineHeight: 24
    },
    lineViewStyle:{
        height:1,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC',
    },
});