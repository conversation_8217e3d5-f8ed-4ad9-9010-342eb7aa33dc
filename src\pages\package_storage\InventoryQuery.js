import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Clipboard,Image,Linking,ScrollView,Modal,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class InventoryQuery extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:6,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            brickTypeDataSource:[],
            brickTypeName:"",
            brickTypeId:"",
            exportPdfModal:false,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadInventoryList();

        // 加载砖型
        var loadTypeUrl= "/biz/brick/series/type/effBrickTreeCatalog";
        var loadRequest={'currentPage':1,'pageSize':10000};
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data) {
                this.setState({
                    brickTypeDataSource:response.data
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });

    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/brick/series/type/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "isInventoryQuery":true,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        
        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.brickTypeId == null) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            brickTypeName:"",
            brickTypeId:"",
        })
        this.setState({
            currentPage:1
        })
        let url= "/biz/brick/series/type/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "isInventoryQuery":true,
            "brickTypeId":null,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            // var dataAll = [...dataNew];
            this.setState({
                dataSource:response.data.dataList,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadInventoryList();
    }

    loadInventoryList=()=>{
        let url= "/biz/brick/series/type/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "isInventoryQuery":true,
            "brickTypeId":null,
        };
        httpPost(url, loadRequest, this.loadInventoryListCallBack);
    }

    loadInventoryListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            // var dataAll = dataOld.concat(dataNew.filter(v => !dataOld.includes(v)))
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.brickTypeId} style={styles.innerViewStyle}>
                <View style={CommonStyle.titleViewStyleSpecial}>
                    <Text style={CommonStyle.titleTextStyleSpecial}>{item.seriesName}-{item.brickTypeName}</Text>
                    {/* <Text style={styles.titleTextStyle}>砖型：{item.seriesName}-{item.brickTypeName}</Text> */}
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>入库总数：{item.checkInAmount}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>出库总数：{item.checkOutAmount}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>现有库存：{(item.checkInAmount - item.checkOutAmount).toFixed(2)}</Text>
                </View>
                <View style={[CommonStyle.titleViewStyle,{marginBottom:10}]}>
                    <Text style={CommonStyle.titleTextStyle}>库存总重(Kg)：{((item.checkInAmount - item.checkOutAmount) * item.standardWeight).toFixed(2)}</Text>
                </View>
                {/* <View style={[CommonStyle.itemBottomBtnStyle]}>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("InventoryAdjust", 
                            {
                                // 传递参数
                                brickTypeId:item.brickTypeId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle
                        ]}>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>库存调整</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0',marginHorizontal:16}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                    this.setState({
                        exportPdfModal: true
                    })
                }} >
                    <Image style={{ width: 24, height: 24 }} source={require('../../assets/icon/iconfont/Export.png')}></Image>
                    {/* <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
                </TouchableOpacity>
            </View>
        )
    }
    // 渲染砖型底部滚动数据
    openBrickTypeSelect(){
        if (!this.state.brickTypeDataSource || this.state.brickTypeDataSource.length < 1) {
            WToast.show({data:"请先添加砖型"});
            return
        }
        this.refs.SelectBrickType.showBrickType(this.state.selectBrirck, this.state.brickTypeDataSource)
    }

    callBackBrickTypeValue(value){
        console.log("==========砖型选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectBrirck:value
        })
        // 取选定的砖型ID
        if (value.length == 2) {
            // 加载砖型
            let loadTypeUrl= "/biz/brick/series/type/getBrickByName";
            let loadRequest={
                "brickTypeName":value[1],
                "seriesName":value[0],
            };
            httpPost(loadTypeUrl, loadRequest, this._callBackLoadBrickTypeData);
        }
        else{
            console.log("======选择砖型返回数据不合法", value)
        }
    }

    _callBackLoadBrickTypeData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                brickTypeName:response.data.brickTypeName,
                brickTypeId:response.data.brickTypeId,
            })
            let url= "/biz/brick/series/type/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "isInventoryQuery":true,
                "brickTypeId":response.data.brickTypeId,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
            this.setState({
                brickTypeName:'',
                brickTypeId:'',
            })
        }
    }

    exportPdfFile=()=> {
        console.log("=======exportPdfFile");
        let url= "/biz/generate/pdf/inventory_query";
        let requestParams={
            "isInventoryQuery":true,
            "brickTypeId":this.state.brickTypeId,
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data); 
                WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
                Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                    {
                        text:"不打开", onPress:()=>{
                        WToast.show({data:'点击了不打开'});
                        }
                    },
                    {
                        text:"打开", onPress:()=>{
                            WToast.show({data:'点击了打开'});
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='库存查询'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                {/* <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {
                    width: 100,marginRight:0, backgroundColor: 'green', flexDirection: "row", top:screenHeight/15,height: 35,zIndex: 100,
                    position: 'absolute', right: 15, opacity: 0.6, alignItems: 'center', justifyContent: 'center'
                }]}> */}
                <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                        <TouchableOpacity onPress={()=>this.openBrickTypeSelect()}>
                            {
                                !this.state.brickTypeName ?
                                    <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: '#ffffff', padding: 10, margin: 5, flexDirection: 'row' }]}>
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(30,110,250,1)", paddingRight: 10 }]}>
                                            砖型
                                        </Text>
                                        <Image style={{ width: 22, height: 22, tintColor: "rgba(30,110,250,1)"}} source={require('../../assets/icon/iconfont/arrow_down.png')}></Image>
                                    </View>
                                :
                                    <View style={[CommonStyle.blockItemViewStyle, { backgroundColor: '#ffffff', padding: 10, margin: 5, flexDirection: 'row' }]}>
                                                <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(30,110,250,1)", paddingRight: 10 }]}>
                                                {this.state.brickTypeName}
                                                </Text>
                                                {/* <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold', color: "rgba(0, 10, 2, 0.45)", paddingRight: 10 }]}>
                                                    {"选择部门"}
                                                </Text> */}
                                        <Image style={{ width: 20, height: 20, tintColor: "rgba(30,110,250,1)"}} source={require('../../assets/icon/iconfont/arrow-up.png')}></Image>
                                    </View>
                            // <Text style={[CommonStyle.rightTop50FloatingBlockText,{fontSize:16,backgroundColor:'red'}]}>
                            // {!this.state.brickTypeName ? "砖型" : this.state.brickTypeName}
                            // </Text>
                            }
                        </TouchableOpacity>
                    </View>
                </View>
                {/* <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 70 ,backgroundColor:"#F2C16D",flexDirection:"row"
                ,top:screenHeight/7.5,zIndex:100,position: 'absolute',right: 15,opacity:0.6,alignItems:'center',justifyContent:'center'}]}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要导出PDF文件吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.exportPdfFile()
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 70, backgroundColor: "#F2C16D", flexDirection: "row" }]}>
                            <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/output.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>导出</Text>
                        </View>                    
                    </TouchableOpacity>
                </View> */}
                
                {/* 导出pdf弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.exportPdfModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.08)' }]}>
                        <View style={{ width: 291, height: 135, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认导出PDF文件吗？</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 291, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        exportPdfModal: false
                                    });
                                    // WToast.show({ data: '点击了不打开' });
                                }}>

                                    <View style={{ width: 145, height: 56, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    // WToast.show({ data: '点击了打开' });
                                    this.setState({
                                        exportPdfModal: false
                                    })
                                    this.exportPdfFile()
                                }}>

                                    <View style={{ width: 145, height: 56, alignItems: 'center', justifyContent: 'center', borderLeftWidth: 1, borderColor: '#DFE3E8' }}>
                                        <Text style={{ fontSize: 17,fontWeight: '400', color: '#1E6EFA' }}>确定</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                </Modal>


                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        ItemSeparatorComponent={this.space}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
                <BottomScrollSelect 
                    ref={'SelectBrickType'} 
                    callBackBrickTypeValue={this.callBackBrickTypeValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginLeft:15,
        marginTop:10,
        // borderColor:"#F4F4F4",
        // borderWidth:8,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        // marginTop:5,
    },
    titleViewStyleSpecial:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:10,
        marginTop:8
    },
    titleTextStyle:{
        fontSize:16
    },
    titleTextStyleSpecial:{
        width: 200,
        height: 24,
        // fontFamily: 'PingFangSC',
        fontWeight: 'bold',
        fontSize: 20,
        color: '#404956',
        lineHeight: 24,
        textAlign: 'left',
        fontStyle: 'normal',
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    // itemContentViewStyle:{
    //     flexDirection:'row',
    //     justifyContent:'space-between',
    //     marginLeft:25
    // },
    itemContentChildViewStyle:{
        justifyContent:'space-between',
        flexDirection:'row',
    },
});