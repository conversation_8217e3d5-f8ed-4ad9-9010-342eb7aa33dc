import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,KeyboardAvoidingView,Image,TouchableOpacity,Dimensions} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class NaturalGasFlowMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            flowId:"",
            flowValue:"",
            operate:"",
            //班次
            workingShiftName:"",
            workingShiftId:"",
            workingShiftList:"",
            selWorkingShift:[],
            //窑道
            kilnRoadDataSource:[],
            selKilnRoadId:null,
            //员工
            staffName:"",
            staffId:"",
            staffList:[],
            selStaff:[],
            //接班数字
            flowStartValue:"",
            //交班数字
            flowEndValue:"",
            addDate:"",
            selectAddDate:[],
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        const { flowId } = this.props;
        if (route && route.params) {
            const { flowId , shiftName, staffName, productionLineId,shiftId} = route.params;
            if (flowId) {
                this.setState({
                    operate:"编辑",
                    flowId:flowId
                })
                this.loadWorkingShiftData(productionLineId);
                loadTypeUrl= "/biz/natural/gas/flow/get";
                loadRequest={'flowId':flowId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditNaturalGasFlowCallBack);

            }
            else {
                this.setState({
                    operate:"新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    
                    selectAddDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    addDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })   
                // this.loadLastFlowEndValue();
            }
            if (shiftName) {
                this.setState({
                    workingShiftName:shiftName,
                })
            }
            if (staffName) {
                this.setState({
                    staffName:staffName,
                    selStaff:[staffName]
                })
            }
            if (shiftId) {
                this.loadEmployeeListByShiftId(shiftId)
            }
        }
        this.loadProductLineList();
    }

    loadLastFlowEndValue=(productionLineId)=>{
        let url= "/biz/natural/gas/flow/lastFlowEndValue";
        let loadRequest={
            'productionLineId':productionLineId
        };
        httpPost(url, loadRequest, this.callBackLoadLastFlowEndValue);
    }

    callBackLoadLastFlowEndValue=(response)=>{
        if (response.code == 200) {
            this.setState({
                flowStartValue:response.data,
            })
        }
    }

    loadEditNaturalGasFlowCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            var addDate;
            if (response.data.addTime) {
                addDate = response.data.addTime.split("-");
            }
            this.setState({
                addDate:response.data.addTime,
                selKilnRoadId:response.data.productionLineId,
                workingShiftId:response.data.shiftId,
                staffId:response.data.staffId,
                flowStartValue:response.data.flowStartValue,
                flowEndValue:response.data.flowEndValue,
                selectAddDate:addDate
            })
        }
    }

    openAddDate(){
        this.refs.SelectAddDate.showDate(this.state.selectAddDate)
    }
    
    callBackSelectAddDate(value){
        console.log("==========计划收款时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectAddDate:value
        })
        if (value && value.length) {
            var addDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    addDate += vartime;
                }
                else{
                    addDate += "-" + vartime;
                }
            }
            this.setState({
                addDate:addDate
            })
        }
    }

    // 生产车间 & 窑车
    loadProductLineList=()=>{
        let url= "/biz/production/line/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 200,
            'qryRoad':'Y'
        };
        httpPost(url, loadRequest, this.callBackLoadProductLineList);
    }

    callBackLoadProductLineList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            let productionLineDataSource = response.data.dataList;
            //获取窑道数据
            let kilnRoadDataSource = [];
            var i=0;
            for(i; i<productionLineDataSource.length;i++){
                if(productionLineDataSource[i].kilnRoadName){
                    kilnRoadDataSource = kilnRoadDataSource.concat(productionLineDataSource[i])
                }
            }
            this.setState({
                kilnRoadDataSource:kilnRoadDataSource
            })

            //生产车间
            let selProductionLineId = response.data.dataList[0].productionLineId;
            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                selProductionLineId = constants.loginUser.spUserExtDTO.productionLineId;
            }

            if(this.state.selKilnRoadId){
                selProductionLineId = this.state.selKilnRoadId
            }
            this.loadWorkingShiftData(selProductionLineId);
            if (!this.state.flowId) {
                this.loadLastFlowEndValue(selProductionLineId);
            }
            //判断所在车间是否设置窑道名称，若有则选中该窑道，没有则选中第一个窑道
            if(kilnRoadDataSource && kilnRoadDataSource.length>0){
                kilnRoadDataSource.forEach(element => {
                    if(element.productionLineId == selProductionLineId){
                        this.setState({
                            // 窑道
                            selKilnRoadId:selProductionLineId
                        })
                    }
                });
                if(!this.state.selKilnRoadId){
                    this.setState({
                        // 窑道
                        selKilnRoadId:kilnRoadDataSource[0].productionLineId
                    })
                }
            }
            else{
                this.setState({
                    // 窑道
                    selKilnRoadId:selProductionLineId
                })
            }
        }
    }

    loadWorkingShiftData=(productionLineId)=>{
        console.log("===productionLineId==="+productionLineId)
        let url= "/biz/working/shift/list";
        let loadRequest={'currentPage':1, 'pageSize':1000,'shiftType':"C",'productionLineId':productionLineId};
        httpPost(url, loadRequest, this.callBackLoadWorkingShiftData);
      }

    callBackLoadWorkingShiftData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList && response.data.dataList.length > 0) {
            this.setState({
                workingShiftList:response.data.dataList,
                // workingShiftName:response.data.dataList[0] ? response.data.dataList[0].workingShiftName : 0,
            })
        }

    }


    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("NaturalGasFlowMgrList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>流量管理</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>流量管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveNaturalGasFlow =()=> {
        console.log("=======saveNaturalGasFlow");
        let toastOpts;
        if (!this.state.addDate) {
            toastOpts = getFailToastOpts("请选择日期");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.flowStartValue) {
            toastOpts = getFailToastOpts("请填写接班数字");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.flowEndValue) {
            toastOpts = getFailToastOpts("请填写交班数字");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/natural/gas/flow/add";
        if (this.state.flowId) {
            console.log("=========Edit===flowId", this.state.flowId)
            url= "/biz/natural/gas/flow/modify";
        }
        let requestParams={
            flowId:this.state.flowId,
            productionLineId:this.state.selKilnRoadId,
            addTime:this.state.addDate,
            flowStartValue: this.state.flowStartValue,
            flowEndValue: this.state.flowEndValue,
            shiftId:this.state.workingShiftId,
            staffId:this.state.staffId,
            flowValue:this.state.flowEndValue
        };
        httpPost(url, requestParams, this.saveNaturalGasFlowCallBack);
    }

    // 保存回调函数
    saveNaturalGasFlowCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    openWorkingShift(){
        console.log(this.state.workingShiftList)
        if (this.state.workingShiftList && this.state.workingShiftList.length > 0) {
            this.refs.SelectWorkingShift.showWorkingShift(this.state.selWorkingShift,this.state.workingShiftList)
        }
        else {
            WToast.show({data:"没有班次信息"})
        }
    }

    openStaff(){
        if(!this.state.workingShiftName){
            // console.log("没有选择班次");
            WToast.show({data:"请选择班次"})
        }
        else{
            console.log(this.state.staffList)
            if (this.state.staffList && this.state.staffList.length > 0) {
                this.refs.SelectStaff.showStaff(this.state.selStaff,this.state.staffList)
            }
            else {
                WToast.show({data:"没有员工信息"})
            }
        }
        
    }

    callBackWorkingShiftValue(value){
        console.log("==========班次选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selWorkingShift:value,
            selStaff:[],
            staffName:"",
        })
        // 取选定的客户ID
        var workingShiftName = value.toString();
        // this.setState({
        //     workingShiftName:workingShiftName
        // })
        let loadUrl= "/biz/working/shift/getWorkingShiftByName";
        let loadRequest={"shiftName":workingShiftName,'shiftType':"C","productionLineId":this.state.selKilnRoadId};
        console.log("==========loadDetailRequest", loadRequest)
        httpPost(loadUrl, loadRequest, this.loadWorkingShiftDetailData);
    }

    callBackStaffValue(value){
        console.log("==========员工选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selStaff:value
        })
        var staffName = value.toString();
        // this.setState({
        //     staffName:staffName
        // })
        let loadUrl= "/biz/portal/staff/getPortalStaffByName";
        let loadRequest={"staffName":staffName};
        console.log("==========loadRequest", loadRequest)
        httpPost(loadUrl, loadRequest, this.loadStaffDetailData);
    }

    loadStaffDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                staffName:response.data.staffName,
                staffId:response.data.staffId,
            })
            // console.log(response.data.staffId)
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    loadWorkingShiftDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                workingShiftName:response.data.shiftName,
                workingShiftId:response.data.shiftId,
            })
            // 通过shiftId获取员工列表
            this.loadEmployeeListByShiftId(response.data.shiftId)

        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    // 通过shiftId获取员工列表
    loadEmployeeListByShiftId=(shiftId)=>{
        let loadUrl= "/biz/working/shift/staff/list";
        let loadRequest={"shiftId":shiftId};
        console.log("==========loadRequest", loadRequest)
        httpPost(loadUrl, loadRequest, this.loadEmployeeListByShiftIdCallBack);
    }

    loadEmployeeListByShiftIdCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                staffList:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    // 窑道名称 
    renderProductLineRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                // 切换生产车间时，下面的机台也要跟着变，机台默认选择第一个
                this.setState({
                    selKilnRoadId:item.productionLineId,
                    workingShiftName:"",
                    selWorkingShift:[],
                    workingShiftList:"",
                    selStaff:[],
                    staffName:"",
                    staffList:[],
                }) 
                this.loadLastFlowEndValue(item.productionLineId);
                this.loadWorkingShiftData(item.productionLineId);
            }}>
                <View key={item.productionLineId} style={[item.productionLineId === this.state.selKilnRoadId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.productionLineId === this.state.selKilnRoadId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.kilnRoadName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    
    render(){
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <View style={CommonStyle.lineHeadBorderStyle} />
                 <ScrollView style={[CommonStyle.formContentViewStyle]}>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>日期</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={()=>this.openAddDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{marginLeft:5,width:screenWidth - (leftLabWidth+60), borderWidth:0}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.addDate ? "请选择日期" : this.state.addDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={CommonStyle.rowLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={CommonStyle.rowLabTextStyle}>窑道名称</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                        {
                            (this.state.kilnRoadDataSource && this.state.kilnRoadDataSource.length > 0) 
                            ? 
                            this.state.kilnRoadDataSource.map((item, index)=>{
                                return this.renderProductLineRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    
                    <View>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>
                                    班次
                                </Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                            <TouchableOpacity onPress={()=>{
                                    this.openWorkingShift()
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth+60),borderWidth:0}]}>
                                    <Text style={{color:'#A0A0A0', fontSize:15}}>
                                        {!this.state.workingShiftName ? "请选择" : this.state.workingShiftName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={CommonStyle.lineBorderBottomStyle} />

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>
                                    员工
                                </Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                            <TouchableOpacity onPress={()=>{
                                    this.openStaff()
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth+60),borderWidth:0}]}>
                                    <Text style={{color:'#A0A0A0', fontSize:15}}>
                                        {!this.state.staffName ? "请选择" : this.state.staffName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={CommonStyle.lineBorderBottomStyle} />

                    </View>
                       

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>接班数字</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60),borderWidth:0}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({flowStartValue:text})}
                        >
                            {this.state.flowStartValue}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>交班数字</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={[styles.inputRightText,{width:screenWidth - (leftLabWidth+60),borderWidth:0}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({flowEndValue:text})}
                        >
                            {this.state.flowEndValue}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    <View style={{height:ifIphoneXContentViewHeight()-378-72, backgroundColor:'#F2F5FC'}}>
                        {/* <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                        >
                        </TextInput> */}
                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle, { marginTop: 0 }]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                            {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveNaturalGasFlow.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                            {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>    
                <BottomScrollSelect 
                    ref={'SelectAddDate'} 
                    callBackDateValue={this.callBackSelectAddDate.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectWorkingShift'} 
                    callBackWorkingShiftValue={this.callBackWorkingShiftValue.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectStaff'} 
                    callBackStaffValue={this.callBackStaffValue.bind(this)}
                />
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({

    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:6,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }

})