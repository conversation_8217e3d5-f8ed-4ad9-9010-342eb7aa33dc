import React, {Component} from 'react';
import {
  Alert,
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class DryKoleInMgrList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
    };
  }
  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/dry/hole/record/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      recordType: 'I',
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    const {route, navigation} = this.props;
    this.loadDryHoleRecordList();
  }

  loadDryHoleRecordList = () => {
    let url = '/biz/dry/hole/record/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      recordType: 'I',
    };
    httpPost(url, loadRequest, this.loadDryHoleRecordListCallBack);
  };

  loadDryHoleRecordListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      console.log(dataNew);
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      if (dataAll.length > response.data.totalRecord) {
        this.setState({
          refreshing: false,
        });
        console.log(
          '=====数据错误了========' +
            dataAll.length +
            '/' +
            response.data.totalRecord,
        );
        return;
      }
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      (this.state.currentPage == 1 ||
        this.state.totalRecord <= this.state.pageSize) &&
      this.state.gmtCreated == this.state.initGmtCreated
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/dry/hole/record/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      recordType: 'I',
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========第一页即是最后一页，不加载=====');
      return;
    }
    this.loadDryHoleRecordList();
  };

  deleteProductCheck = (recordId) => {
    console.log('=======delete=recordId', recordId);
    let url = '/biz/dry/hole/record/delete';
    let requestParams = {recordId: recordId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  // 分隔线
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      // </TouchableOpacity>
      <View style={CommonStyle.viewListLeftViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[CommonStyle.btnListLeftBtn]}>
          <Image
            style={CommonStyle.btnListLeftBtnImage}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={CommonStyle.btnListLeftBtnText}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      // <TouchableOpacity onPress={() => {
      //     this.props.navigation.navigate("DryKoleInMgrAdd",
      //     {
      //         // 传递回调函数
      //         refresh: this.callBackFunction
      //     })
      // }}>
      //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
      //     {/* <Text style={CommonStyle.headRightText}>新增进洞</Text> */}
      // </TouchableOpacity>
      <View style={CommonStyle.viewListRightViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('DryKoleInMgrAdd', {
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Image
            style={CommonStyle.btnListRightBtnImage}
            source={require('../../assets/icon/iconfont/add.png')}></Image>
        </TouchableOpacity>
      </View>
    );
  }

  renderRowNew = (item, index) => {
    return (
      <View key={item.recordId} style={styles.innerViewStyle}>
        <View style={CommonStyle.titleViewStyleSpecial}>
          {/* <Text style={styles.titleTextStyle}>砖型：{item.orderName}</Text> */}
          <Text style={CommonStyle.titleTextStyleSpecial}>
            {item.orderName}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            正品数量（块）：{item.qualityGoodsNumber}
          </Text>
        </View>
        <View style={[CommonStyle.titleViewStyle, {alignItems: 'center'}]}>
          <View style={{flexDirection: 'column'}}>
            {item.spDryHoleWasteDetailDTOList.map((elem, index) => {
              return (
                <View
                  key={elem.detailId}
                  style={{flexDirection: 'row', width: screenWidth - 134 + 90}}>
                  <View style={{width: screenWidth - 230 + 80}}>
                    <Text style={CommonStyle.titleTextStyle}>
                      废品原因：{elem.causeTitle}
                    </Text>
                  </View>
                  <View style={{width: 90 + 10, justifyContent: 'center'}}>
                    <Text style={CommonStyle.titleTextStyle}>
                      数量：{elem.wasteNumber}
                    </Text>
                  </View>
                </View>
              );
            })}
          </View>
        </View>

        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            干燥洞号：{item.dryHoleName}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            创建时间：{item.gmtCreated}
          </Text>
        </View>

        <View style={[CommonStyle.blockTwoEditDelStyle, {marginRight: 15}]}>
          <TouchableOpacity
            onPress={() => {
              // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
              //     return;
              // }
              Alert.alert('确认', '您确定要删除该条进洞记录吗？', [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                    // this在这里可用，传到方法里还有问题
                    // this.props.navigation.goBack();
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.deleteProductCheck(item.recordId);
                  },
                },
              ]);
            }}>
            <View style={[CommonStyle.btnTwoDeleteBtnView, {marginRight: 0}]}>
              <Image
                style={[CommonStyle.btnTwoDeleteBtnImage]}
                source={require('../../assets/icon/iconfont/delete.png')}></Image>
              <Text style={CommonStyle.btnTwoDeleteBtnText}>删除</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
              //     return;
              // }
              this.props.navigation.navigate('DryKoleInMgrAdd', {
                recordId: item.recordId,
                // 传递回调函数
                productionLineId: item.productionLineId,
                refresh: this.callBackFunction,
              });
            }}>
            <View style={[CommonStyle.btnTwoEditBtnView]}>
              <Image
                style={CommonStyle.btnTwoEditBtnImage}
                source={require('../../assets/icon/iconfont/edit.png')}></Image>
              <Text style={CommonStyle.btnTwoEditBtnText}>编辑</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="进干燥洞"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View style={CommonStyle.contentViewStyle}>
          <FlatList
            data={this.state.dataSource}
            ItemSeparatorComponent={this.space}
            renderItem={({item}) => this.renderRowNew(item)}
            keyExtractor={(item) => item.recordId}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    marginTop: 10,
    marginBottom: 10,
    // borderColor:"#F4F4F4",
    // borderWidth:14,
    // marginLeft:16,
  },
  titleViewStyleSpecial: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 10,
    marginTop: 5,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 4,
    marginTop: 5,
  },
  titleTextStyleSpecial: {
    // fontSize:16,
    width: 200,
    height: 24,
    // fontFamily: 'PingFangSC',
    fontWeight: 'bold',
    fontSize: 20,
    color: '#404956',
    lineHeight: 24,
    textAlign: 'left',
    fontStyle: 'normal',
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 15,
    paddingTop: 5,
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'row',
  },
  itemContentChildCol1ViewStyle: {
    marginLeft: 20,
    marginTop: 15,
  },
  itemContentChildCol2ViewStyle: {
    marginLeft: 40,
    marginTop: 15,
  },
  itemContentChildTextStyle: {
    fontSize: 15,
  },
  itemBottomBtnStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  itemBottomDeleteBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 1,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    borderRadius: 4,
  },
  itemBottomEditBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: '#CB4139',
    borderRadius: 4,
  },
  itemBottomEditBtnTextStyle: {
    color: '#F0F0F0',
  },
});
