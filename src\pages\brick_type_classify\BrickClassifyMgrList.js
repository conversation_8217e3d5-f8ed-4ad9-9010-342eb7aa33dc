import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Image, TextInput,
    FlatList, RefreshControl,ScrollView,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class BrickClassifyMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            topBlockLayoutHeight: 0,
            year: null,
            searchKeyWord: "",
            selYearsChooseName: '全部',
            totalRecord: 1,
            moreModal:false,
            modalItem:{},
            deleteModal:false,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        var currentDate = new Date();
        var year = currentDate.getFullYear();
        let yearsChooseDataSource = [
            {
                chooseCode: 1,
                chooseName: year,
            },
            {
                chooseCode: 2,
                chooseName: year - 1,
            },
            {
                chooseCode: 3,
                chooseName: year - 2,
            },
            {
                chooseCode: 4,
                chooseName: year - 3,
            },
            {
                chooseCode: 'all',
                chooseName: '全部',
            }
        ]
        this.setState({
            yearsChooseDataSource: yearsChooseDataSource,
            selYearsChooseName:year
        })
        this.loadBrickClassIfyList(year);
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/brick/class/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "qryStartYear": this.state.selYearsChooseName === '全部' ? null : this.state.selYearsChooseName,
            "qryEndYear": this.state.selYearsChooseName === '全部' ? null : this.addOneYear(this.state.selYearsChooseName),
            "searchKeyWord": this.state.searchKeyWord,
            "qryAllInfo":"Y"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/brick/class/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "qryStartYear": this.state.selYearsChooseName === '全部' ? null : this.state.selYearsChooseName,
            "qryEndYear": this.state.selYearsChooseName === '全部' ? null : this.addOneYear(this.state.selYearsChooseName),
            "searchKeyWord": this.state.searchKeyWord,
            "qryAllInfo":"Y"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadBrickClassIfyList();
    }

    loadBrickClassIfyList = (selYearsChooseName) => {
        let url = "/biz/brick/class/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "qryStartYear": selYearsChooseName ? selYearsChooseName : (this.state.selYearsChooseName === '全部' ? null : this.state.selYearsChooseName),
            "qryEndYear": selYearsChooseName ? this.addOneYear(selYearsChooseName) : (this.state.selYearsChooseName === '全部' ? null : this.addOneYear(this.state.selYearsChooseName)),
            "searchKeyWord": this.state.searchKeyWord,
            "qryAllInfo":"Y"
        };
        httpPost(url, loadRequest, this.loadKilnCarListCallBack);
    }

    loadKilnCarListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteBrickClassify = (brickClassId) => {
        console.log("=======delete=brickClassId", brickClassId);
        let url = "/biz/brick/class/delete";
        let requestParams = { 'brickClassId': brickClassId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    displayBrickClass=(item)=>{
        console.log("=======displayBrickClass=brickClassId", item.brickClassId);
        let requestUrl = "/biz/brick/class/modifyDisplay";
        let requestParams = {
            'brickClassId': item.brickClassId,
            'display': item.display === 'Y' ? 'N' : 'Y',
        };
        httpPost(requestUrl, requestParams, (response) => {
            if (response.code == 200) {
                // 更新页面上订单状态
                item.display = (item.display === 'Y' ? 'N' : 'Y');
                WToast.show({ data: (item.display === 'Y' ? '显示' : '隐藏') + "设置完成" });
                // let brickClassDataSource = this.state.dataSource;
                // // JS 数组遍历
                // brickClassDataSource.forEach((obj) => {
                //     if (obj.brickClassId === item.brickClassId) {
                //         obj.display = item.display;
                //     }
                // })
                // this.setState({
                //     dataSource: brickClassDataSource,
                // })
                let url = "/biz/brick/class/list";
                let loadRequest = {
                    "currentPage": 1,
                    "pageSize": this.state.pageSize,
                    "qryStartYear": this.state.selYearsChooseName === '全部' ? null : this.state.selYearsChooseName,
                    "qryEndYear": this.state.selYearsChooseName === '全部' ? null : this.addOneYear(this.state.selYearsChooseName),
                    "searchKeyWord": this.state.searchKeyWord,
                    "qryAllInfo":"Y"
                };
                httpPost(url, loadRequest, this._loadFreshDataCallBack);
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }

    renderRow = (item, index) => {
        return (
            <View key={item.brickClassId} style={styles.innerViewStyle}>
                {
                    index == 0 ?
                        <View style={{ width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF', borderBottomWidth: 10, borderBottomColor: '#F4F7F9' }}>
                        </View>
                        :
                        <View></View>
                }
                <View style={styles.titleViewStyle}>
                    <View style={styles.bodyViewStyleSpecial}>
                        <Text style={styles.texRowSpecial}>{item.brickClassName}</Text>
                    </View>
                    {
                        item.display === 'Y' ? 
                        <View style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,marginLeft:3,
                            height:23, borderRadius:12, backgroundColor:'rgba(255,184,0,0.4)', color:'#FFFFFF',marginTop:6}}>
                            <Text style={{color:'white'}}>显示</Text>
                        </View>
                            :
                        <View style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,marginLeft:3,
                            height:23, borderRadius:12, backgroundColor:'rgba(134,134,134,0.4)', color:'#FFFFFF',marginTop:6}}>
                            <Text style={{color:'white'}}>不显示</Text>
                        </View>
                    }
                </View>
                <View style={[styles.titleViewStyle, { position: 'absolute', top: 0, right:0 , flexDirection: 'column' ,marginRight:15}]}>
                    <TouchableOpacity onPress={() => {
                        this.setState({
                            moreModal: true,
                            modalItem:item
                        })
                    }}>
                        <View style={[{ width: 35, height: 35, alignItems: 'center' }]}>
                            <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                        </View>
                    </TouchableOpacity>
                </View>
                <View style={styles.bodyViewStyleCommon}>
                    <Text style={styles.texRowCommon}>大屏工厂产品统计：{item.pcBigScreenProductStatisticalFlag === 'Y' ? '是' : '否'}</Text>
                </View>
                <View style={styles.bodyViewStyleCommon}>
                    <Text style={styles.texRowCommon}>是否长期生产：{item.longTermProduction === 'Y' ? '是' : '否'}</Text>
                </View>
                <View style={styles.bodyViewStyleCommon}>
                    <Text style={styles.texRowCommon}>排序：{item.brickClassSort}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>最近更新时间：{item.gmtModified ? item.gmtModified: item.gmtCreated }</Text>
                </View> */}

                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap', marginLeft: 12, marginRight: 15 }]}>
                    <TouchableOpacity onPress={() => {
                        let message = '您确定要' + (item.display === 'Y' ? '隐藏' : '显示') + '该产品分类以及其下的产品和砖型吗？';
                        Alert.alert('确认', message, [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.displayBrickClass(item);
                                }
                            }
                        ]);
                    }}>
                        <View style={[item.display === 'Y' ? [CommonStyle.itemBottomDeleteBtnViewStyle] : [CommonStyle.itemBottomDetailBtnViewStyle, { backgroundColor: "#FFB800" }] 
                            , { width: 64, flexDirection: "row", marginLeft: 0 }
                        ]}>
                            {
                                item.display === 'Y' ?
                                <Image style={{ width: 17, height: 17, marginRight: 2 }} source={require('../../assets/icon/iconfont/hide.png')}></Image>
                                :
                                    <Image style={{ width: 17, height: 17, marginRight: 2 }} source={require('../../assets/icon/iconfont/show.png')}></Image>
                            }
                            <Text style={item.display === 'Y' ? CommonStyle.itemBottomDeleteBtnTextStyle :CommonStyle.itemBottomDetailBtnTextStyle}>
                                {item.display === 'Y' ? '隐藏' : '显示'}
                            </Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        this.props.navigation.navigate("BrickClassifySeriesMgrList",
                            {
                                // 传递参数
                                brickClassId: item.brickClassId,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle, { backgroundColor: '#F27F0C' }, { width: 64, flexDirection: "row",marginLeft:0}
                        ]}>
                            <Image style={{ width: 17, height: 17, marginRight: 2 }} source={require('../../assets/icon/iconfont/product.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>产品</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("BrickClassifyMgrAdd",
            //         {
            //             // 传递回调函数
            //             refresh: this.callBackFunction
            //         })
            // }}>
            //     <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            //     {/* <Text style={CommonStyle.headRightText}>新增分类</Text> */}
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("BrickClassifyMgrAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    });
                }}  >
                    <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    addOneYear = (year) => {
        var date = new Date(year, 10, 24, 10, 33, 0, 0);
        var addYear = date.getFullYear() + 1;
        return addYear;
    }

    yearsChooseStateRow = (item, index) => {
        return (
            <View key={item.chooseCode} >
                <TouchableOpacity onPress={() => {
                    var selYearsChooseName = item.chooseName;
                    this.setState({
                        selYearsChooseName: selYearsChooseName
                    })

                    let loadUrl = "/biz/brick/class/list";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "qryStartYear": selYearsChooseName === '全部' ? null : selYearsChooseName,
                        "qryEndYear": selYearsChooseName === '全部' ? null : this.addOneYear(selYearsChooseName),
                        "searchKeyWord": this.state.searchKeyWord,
                        "qryAllInfo":"Y"
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.chooseCode} style={[{width: screenWidth/5 , height: 49, flexDirection: 'row', justifyContent: 'center'}
                    // ,item.stateCode === this.state.selCompletionStateCode ?
                    //     [styles.selectedBlockItemViewStyle]
                    //     :
                    //     [styles.blockItemViewStyle],
                    ]}>
                        <Text style={[item.chooseName === this.state.selYearsChooseName ?
                            { color: "#255BDA", fontSize: 16, fontWeight: '500', lineHeight: 49, textAlign: 'center', borderColor: "#255BDA", borderBottomWidth: 2, paddingLeft: 5, paddingRight: 5 }
                            :
                            { color: "#2B333F", fontSize: 16, fontWeight: '500', lineHeight: 49, textAlign: 'center'},
                        ]}>
                            {item.chooseName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    searchByKeyWord = () => {
        // let toastOpts;
        // if (!this.state.searchKeyWord) {
        //     toastOpts = getFailToastOpts("请输入客户、合同或签订时间");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let loadUrl = "/biz/brick/class/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "qryStartYear": this.state.selYearsChooseName === '全部' ? null : this.state.selYearsChooseName,
            "qryEndYear": this.state.selYearsChooseName === '全部' ? null : this.addOneYear(this.state.selYearsChooseName),
            "searchKeyWord": this.state.searchKeyWord,
            "qryAllInfo":"Y"
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='产品分类管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle, { marginTop: 0 }]} onLayout={this.topBlockLayout.bind(this)}>

                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.yearsChooseDataSource && this.state.yearsChooseDataSource.length > 0)
                                ?
                                this.state.yearsChooseDataSource.map((item, index) => {
                                    return this.yearsChooseStateRow(item)
                                })
                                : <View />
                        }
                    </View>

                    <View style={[CommonStyle.headViewStyle, { borderLeftWidth: 0, borderRightWidth: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                        <View style={CommonStyle.singleSearchBox}>
                            <View style={CommonStyle.searchBoxWithoutOthers}>
                                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                                <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                <TextInput
                                    style={{color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 5, paddingTop: 0, paddingBottom: 0, paddingRight: 0, paddingLeft: 0, width:'100%' }}
                                    returnKeyType="search"
                                    returnKeyLabel="搜索"
                                    onSubmitEditing={e => {
                                        this.searchByKeyWord();
                                    }}
                                    placeholder={'产品分类名称/创建时间'}
                                    onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                >
                                    {this.state.searchKeyWord}
                                </TextInput>
                            </View>
                        </View>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        ItemSeparatorComponent={this.space}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>

                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0, 0, 0, 0.64)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.props.navigation.navigate("BrickClassifyMgrAdd",
                                        {
                                            // 传递参数
                                            brickClassId: this.state.modalItem.brickClassId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                    this.setState({
                                        moreModal: false,
                                    })
                                }}>

                                    <View style={{ width: 145, height: 50, paddingLeft: 30, marginTop: 5 }}>
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false,
                                        deleteModal: true
                                    })
                                    }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5},]}>
                                        {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut,{ backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该产品分类?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteBrickClassify(this.state.modalItem.brickClassId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#FFFFFF",
        backgroundColor: "#FFFFFF",
        borderRadius: 5,
        marginTop: 5
    },
    innerViewStyle: {
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        // borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        // justifyContent: 'space-between',
        // marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    texRowSpecial: {
        // width: 200,
        height: 24,
        // fontFamily: 'PingFangSC',
        fontWeight: 'bold',
        fontSize: 20,
        color: '#404956',
        lineHeight: 24,
        textAlign: 'left',
        fontStyle: 'normal',
      },
      texRowCommon: {
        width:220,
        height: 24,
        // fontFamily: 'PingFangSC',
        fontWeight: '400',
        fontSize: 14,
        color: 'rgba(0,10,32,0.65)',
        lineHeight: 24,
        textAlign: 'left',
        fontStyle: 'normal',
      },
      bodyViewStyleSpecial:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:40,
        // marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyViewStyleCommon:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:40,
        marginRight:10,
        marginBottom:0,
        marginTop:0
    },
});