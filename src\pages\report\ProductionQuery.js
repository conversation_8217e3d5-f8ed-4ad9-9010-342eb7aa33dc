import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Linking,Clipboard,ScrollView,Image,Modal,TextInput,Alert
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 95;
export default class ProductionQuery extends Component {
    constructor(props) {
        super(props);
        this.state = {
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight: 0,
            selCategoryChooseName:"全部",
            selCategoryChooseCode:"semifinished,encastage,storageIn,storageOut",
            qryStartTime:null,
            selectedQryStartDate:[],
            qryEndTime:null,
            selectedQryEndDate:[],
            semiFinishedList:[],
            encastageList:[],
            goodsList:[],
            wasteList:[],
            semiFinishedTotalAmount:"",
            encastageTotalAmount:"",
            goodsTotalWeight:"",
            wasteTotalWeight:"",
            // 原始数据
            seriesDataSource:[],
            _seriesDataSource:[],
            seriesModal:false,
            seriesSearchKeyWord:null,
            brickTypeModal:false,
            brickTypeSearchKeyWord:null,
            selBrickTypeId:null,
            selBrickTypeName:null,
            selSeriesName:null,
            brickTypeDataSource:[],
            _brickTypeDataSource:[],
            semiFinishedTotalAmount:"",
            encastageTotalAmount:"",
            storageInTotalAmount:"",
            storageOutTotalAmount:"",
            modalType:"",
            semiFinishedDetailList:[],
            encastageDetailList:[],
            storageInDetailList:[],
            storageOutDetailList:[],
            detailModal:false,
            detailTile:"",
            exportPdfModal: false,
        }
    }

    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }


        this.loadInitData();

        

        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var qryEndTime = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectedQryEndDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            qryEndTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })

        var dateString = this.state.qryEndTime +' 00:00:01';
        dateString = dateString.substring(0,19);    
        dateString = dateString.replace(/-/g,'/');
        var dateStringTimestamp = new Date(dateString).getTime();
        // 根据毫秒数构建 Date 对象
        var SevenDaysLast = new Date(dateStringTimestamp);
        //获取当前时间的毫秒数
        var nowMilliSeconds = currentDate.getTime();
        // 用获取毫秒数 加上七天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        SevenDaysLast.setTime(nowMilliSeconds-(8*86400000));
        //通过赋值后的SevenDaysLast对象来得到 两天前的 年月日。这里我们将日期格式化为20180301的样子。
        //格式化月，如果小于9，前面补0  
        var SevenDaysLastOfMonth = ("0" + (SevenDaysLast.getMonth() + 1)).slice(-2);
        //格式化日，如果小于9，前面补0  
        var SevenDaysLastOfDay = ("0" + SevenDaysLast.getDate()).slice(-2);
        var qryStartTime = SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay;
        this.setState({
            selectedQryStartDate:[SevenDaysLast.getFullYear(), SevenDaysLastOfMonth, SevenDaysLastOfDay],
            qryStartTime:SevenDaysLast.getFullYear() + "-" + SevenDaysLastOfMonth + "-" + SevenDaysLastOfDay
        })

    }

    qryProductAmount = () => {
        if (!this.state.selBrickTypeId) {
            WToast.show({data:"请选择产品"});
            return;
        }
        this.setState({
            semiFinishedTotalAmount:"-",
            encastageTotalAmount:"-",
            storageInTotalAmount:"-",
            storageOutTotalAmount:"-"
        })
        let url = "/biz/output/get";
        let loadRequest = {
            "qryStartDate": this.state.qryStartTime,
            "qryEndDate": this.state.qryEndTime,
            "category":this.state.selCategoryChooseCode,
            "brickTypeId":this.state.selBrickTypeId,
        };
        httpPost(url, loadRequest, this.qryProductAmountCallBack);
    }

    qryProductAmountCallBack = (response) => {
        if (response.code == 200 && response.data) {
            console.log("=======qryProductAmountCallBack========" + response.data)
            this.setState({
                semiFinishedDetailList:response.data.semiFinishedDetailList,
                encastageDetailList:response.data.encastageDetailList,
                storageInDetailList:response.data.storageInDetailList,
                storageOutDetailList:response.data.storageOutDetailList,

                semiFinishedTotalAmount:response.data.semiFinishedTotalAmount,
                encastageTotalAmount:response.data.encastageTotalAmount,
                storageInTotalAmount:response.data.storageInTotalAmount,
                storageOutTotalAmount:response.data.storageOutTotalAmount,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={()=>{
                    this.setState({
                        exportPdfModal: true
                    })
                }}>
                    <Image style={{ width: 23, height: 23 }} source={require('../../assets/icon/iconfont/outputBlack.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }

    openQryStartDate(){
        this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate)
    }

    openQryEndDate(){
        this.refs.SelectQryEndDate.showDate(this.state.selectedQryEndDate)
    }

    callBackSelectQryStartDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryStartDate:value
        })
        if (value && value.length) {
            var qryStartTime = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    qryStartTime += vartime;
                }
                else{
                    qryStartTime += "-" + vartime;
                }
            }
            this.setState({
                qryStartTime:qryStartTime
            })
        }
    }

    callBackSelectQryEndDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryEndDate:value
        })
        if (value && value.length) {
            var qryEndTime = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    qryEndTime += vartime;
                }
                else{
                    qryEndTime += "-" + vartime;
                }
            }
            this.setState({
                qryEndTime:qryEndTime
            })
        }
    }

    // _loadFreshDataCallBack = (response) => {
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             semiFinishedList:response.data.semiFinishedList,
    //             encastageList:response.data.encastageList,
    //             goodsList:response.data.goodsList,
    //             wasteList:response.data.wasteList,
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }





    loadInitData=()=>{
        // 加载排产状态的订单，显示砖型
        let loadTypeUrl= "/biz/brick/class/series/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':100
        };
        httpPost(loadTypeUrl, loadRequest, this.seriesLoadCallBack);
    }

    // 砖的系列回调加载
    seriesLoadCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                WToast.show({data:"没有砖大类"});
                return;
            }
            this.setState({
                seriesDataSource:response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    searchSeriesByKey = () => {
        var _seriesDataSource = copyArr(this.state.seriesDataSource);
        if (this.state.seriesSearchKeyWord && this.state.seriesSearchKeyWord.length > 0) {
            _seriesDataSource = _seriesDataSource.filter(item => item.seriesName.indexOf(this.state.seriesSearchKeyWord) > -1);
        }
        this.setState({
            _seriesDataSource: _seriesDataSource,
        })
    }

    searchBrickTypeKey = () => {
        var _brickTypeDataSource = copyArr(this.state.brickTypeDataSource);
        if (this.state.brickTypeSearchKeyWord && this.state.brickTypeSearchKeyWord.length > 0) {
            _brickTypeDataSource = _brickTypeDataSource.filter(item => item.brickTypeName.indexOf(this.state.brickTypeSearchKeyWord) > -1);
        }
        this.setState({
            _brickTypeDataSource: _brickTypeDataSource,
        })
    }

    renderSeriesRow = (item) => {
        return (
            <TouchableOpacity onPress={()=>{
                this.setState({
                    selSeriesId:item.seriesId,
                    selSeriesName:item.seriesName
                })
                WToast.show({data:'点击了' + item.seriesName});
            }}>
                <View key={item.seriesId} style={[item.seriesId===this.state.selSeriesId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.seriesId===this.state.selSeriesId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.seriesName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    renderBrickTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={()=>{
                this.setState({
                    selBrickTypeId:item.brickTypeId,
                    selBrickTypeName:item.brickTypeName,
                })
                WToast.show({data:'点击了' + item.brickTypeName});

            }}>
                <View key={item.brickTypeId} style={[item.brickTypeId===this.state.selBrickTypeId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.brickTypeId===this.state.selBrickTypeId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.brickTypeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    brickTypeLoadCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                brickTypeDataSource:response.data.dataList,
            })
        }
    }

    renderProcessRow=(item,index)=>{
        return(                    
            <View key={item.productionTime}>
                <View style={[{marginTop:10,flexWrap:'nowrap',flexDirection:'row',justifyContent:'center'}]}>
                    <View style={[{width:(screenWidth - 30) / 2, justifyContent:'center'}]}>
                        <Text style={[{fontSize:16,marginTop:10}]}>{item.productionTime}</Text>
                    </View>
                    <View style={[{ justifyContent:'center'}]}>
                        <Text style={[{fontSize:16,marginTop:10}]}>{item.completeAmount}</Text>
                    </View>
                </View>
            </View>
        )  
    }

    exportPdfFile = () => {
        console.log("=======exportPdfFile");
        let url = "/biz/generate/pdf/production_query";
        let requestParams = {
            "qryStartDate": this.state.qryStartTime,
            "qryEndDate": this.state.qryEndTime,
            "category":this.state.selCategoryChooseCode,
            "brickTypeId":this.state.selBrickTypeId,
            "selSeriesName":this.state.selSeriesName,
            "selBrickTypeName":this.state.selBrickTypeName
        };
        httpPost(url, requestParams, (response) => {
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data);
                WToast.show({ data: "导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data });
                Linking.openURL(response.data)
            }
        });
    }


    render(){
        return(
            <View>
                <CommonHeadScreen title='产品查询'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={[styles.inputRowStyle,{marginTop:5}]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>产品分类</Text>
                        </View>
                        <View style={[(!this.state.seriesDataSource || this.state.seriesDataSource.length === 0 ) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                
                                if (this.state.seriesDataSource && this.state.seriesDataSource.length > 0) {
                                    this.setState({
                                        _seriesDataSource: copyArr(this.state.seriesDataSource),
                                    })
                                }
                                this.setState({
                                    seriesModal: true,
                                    seriesSearchKeyWord: ""
                                })

                                if (!this.state.selSeriesId && this.state.seriesDataSource && this.state.seriesDataSource.length > 0) {
                                    this.setState({
                                        selSeriesId: this.state.seriesDataSource[0].seriesId,
                                        selSeriesName: this.state.seriesDataSource[0].seriesName,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 10), flexWrap: 'wrap', borderWidth: 0}]}>
                                    {
                                        this.state.selSeriesId ?
                                        <View>
                                        <Text style={{color: '#000000', fontSize: 15,fontWeight:'bold'}}>{this.state.selSeriesName}</Text>

                                        </View>
                                            :
                                        <Text style={[{ color: '#A0A0A0', fontSize: 15, }]}>选择产品分类</Text>
                                    }
                                    <Image style={{ width: 22, height: 22, position:'absolute', right: 10, top: 11 }} source={require('../../assets/icon/iconfont/arrowRight.png')}></Image>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{marginTop:5}]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>产品</Text>
                        </View>
                        
                        <View style={[(!this.state.brickTypeDataSource || this.state.brickTypeDataSource.length === 0 ) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (!this.state.brickTypeDataSource || this.state.brickTypeDataSource.length === 0) {
                                     errorMsg = "没有产品数据";
                                    // let errorMsg = getFailToastOpts("没有产品数据");
                                    if (!this.state.selSeriesName ) {
                                        errorMsg = "请先选择产品类别";
                                        WToast.show({data:errorMsg})
                                    }
                                    // toastOpts = getFailToastOpts("请输入支付类别");
                                    // WToast.show(errorMsg)
                                    WToast.show({data:errorMsg})
                                    // Alert.alert('错误', errorMsg, [
                                    //     {
                                    //         text: "确定", onPress: () => {
                                    //             WToast.show({ data: '点击了确定' });
                                    //         }
                                    //     }
                                    // ]);
                                    return;
                                }
                                if (this.state.brickTypeDataSource && this.state.brickTypeDataSource.length > 0) {
                                    this.setState({
                                        _brickTypeDataSource: copyArr(this.state.brickTypeDataSource),
                                    })
                                }
                                this.setState({
                                    brickTypeModal: true,
                                    brickTypeSearchKeyWord:null,
                                })
                                if (!this.state.selBrickTypeId && this.state.brickTypeDataSource && this.state.brickTypeDataSource.length > 0) {
                                    this.setState({
                                        selBrickTypeId: this.state.brickTypeDataSource[0].brickTypeId,
                                        selBrickTypeName: this.state.brickTypeDataSource[0].brickTypeName,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 10), flexWrap: 'wrap', borderWidth: 0}]}>
                                    {
                                        this.state.selBrickTypeId ?
                                        <View>
                                        <Text style={{color: '#000000', fontSize: 15,fontWeight:'bold'}}>{this.state.selBrickTypeName}</Text>

                                        </View>
                                            :
                                        <Text style={[{ color: '#A0A0A0', fontSize: 15, }]}>选择产品</Text>
                                    }
                                    <Image style={{ width: 22, height: 22, position:'absolute', right: 10, top: 11 }} source={require('../../assets/icon/iconfont/arrowRight.png')}></Image>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={[styles.inputRowStyle,{height:48}]}>
                        <View style={[styles.leftLabView,{width:60}]}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>时间</Text>
                        </View>
                        <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.65, justifyContent:'flex-start'}]}>
                            <TouchableOpacity onPress={()=>this.openQryStartDate()}>
                                <View style={[CommonStyle.inputTextStyleViewStyle,
                                    {marginRight:0,marginLeft:0,paddingLeft:5,paddingRight:5,alignItems:'center',width: screenWidth*0.28, backgroundColor:'#F2F5FC',borderColor:'#F2F5FC'}]}>
                                    <Text style={{color:'#A0A0A0', fontSize:15}}>
                                        {!this.state.qryStartTime ? "起始日期" : this.state.qryStartTime}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                            <View style={[CommonStyle.inputTextStyleViewStyle,{marginRight:0,marginLeft:0,borderColor:'#F2F5FC'}]}>
                                <Text style={CommonStyle.blockItemTextStyle16}>--</Text>
                            </View>
                            <TouchableOpacity onPress={()=>this.openQryEndDate()}>
                                <View style={[CommonStyle.inputTextStyleViewStyle,
                                    {marginRight:0,marginLeft:0,paddingLeft:5,paddingRight:5,alignItems:'center',width:screenWidth*0.28, backgroundColor:'#F2F5FC',borderColor:'#F2F5FC'}]}>
                                    <Text style={{color:'#A0A0A0', fontSize:15}}>
                                        {!this.state.qryEndTime ? "结束日期" : this.state.qryEndTime}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        {/* <View style={{marginLeft:10 , width:screenWidth*0.1,height:50}}>
                        </View> */}
                        <View style={[{justifyContent: "center"}]}>
                            <TouchableOpacity onPress={()=>{
                                this.qryProductAmount()
                                }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle,
                                {
                                    margin: 0,
                                    alignItems: 'center',
                                    width: screenWidth*0.15,
                                    backgroundColor: '#1E6EFA',
                                    height: 38,
                                    borderRadius: 20
                                }]}>
                                    <Text style={[{color:'#ffffff',fontSize:16}]}>查询</Text> 
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    

                    <Modal      
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.seriesModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ seriesSearchKeyWord: text })}
                                    >
                                        {this.state.seriesSearchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.searchSeriesByKey();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._seriesDataSource && this.state._seriesDataSource.length > 0)
                                                ?
                                                this.state._seriesDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderSeriesRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            seriesModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            seriesModal: false,
                                            brickTypeDataSource:[],
                                            selBrickTypeId:null,
                                        })
                                        // 调接口查询该系列下的具体砖型
                                        let loadTypeUrl;
                                        let loadRequest;
                                        loadTypeUrl= "/biz/brick/series/type/list";
                                        loadRequest={
                                            "seriesId": this.state.selSeriesId,
                                            "currentPage": 1,
                                            "pageSize": 1000
                                        };
                                        httpPost(loadTypeUrl, loadRequest, this.brickTypeLoadCallBack);
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <Modal      
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.brickTypeModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ brickTypeSearchKeyWord: text })}
                                    >
                                        {this.state.brickTypeSearchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.searchBrickTypeKey();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._brickTypeDataSource && this.state._brickTypeDataSource.length > 0)
                                                ?
                                                this.state._brickTypeDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderBrickTypeRow(item);
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            brickTypeModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            brickTypeModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    {/* 导出pdf弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.exportPdfModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.08)' }]}>
                        <View style={{ width: 291, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认导出产品生产情况？</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>导出地址已复制到粘贴板，使用浏览器打开</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 291, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        exportPdfModal: false
                                    })
                                    WToast.show({ data: '点击了不打开' });
                                }}>

                                    <View style={{ width: 145, height: 56, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#000A20', }}>不打开</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    WToast.show({ data: '点击了打开' });
                                    this.setState({
                                        exportPdfModal: false
                                    })
                                    this.exportPdfFile()
                                }}>

                                    <View style={{ width: 145, height: 56, alignItems: 'center', justifyContent: 'center', borderLeftWidth: 1, borderColor: '#DFE3E8' }}>
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>打开</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                    <ScrollView style={[CommonStyle.contentViewStyle, {height:"auto", paddingBottom:20}]}>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                detailTile:"成型",
                                detailModal:true,
                                detailData:this.state.semiFinishedDetailList
                            })
                        }}>  
                            <View style={[styles.itemContentViewStyle,{ }]}>
                                <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>成型数量合计</Text>
                                </View>
                                <View style={styles.itemContentRightChildViewStyle}>
                                    
                                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18, alignContent:"flex-end"}]}>
                                            {this.state.semiFinishedTotalAmount ? this.state.semiFinishedTotalAmount  : "-" } 
                                        </Text>
                                    
                                </View>
                            </View>
                        </TouchableOpacity> 
                        <TouchableOpacity  onPress={() => {
                            this.setState({
                                detailTile:"入窑",
                                detailModal:true,
                                detailData:this.state.encastageDetailList
                            })
                        }}>
                            <View style={[styles.itemContentViewStyle,{ backgroundColor:"#F6F9FA"  }]}>
                                <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18,}]}>入窑数量合计</Text>
                                </View>
                                <View style={styles.itemContentRightChildViewStyle}>
                                    
                                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18, alignContent:"flex-end"}]}>
                                            {this.state.encastageTotalAmount ? this.state.encastageTotalAmount  : "-" } 
                                        </Text>
                                    
                                </View>
                            </View>
                        </TouchableOpacity> 

                        <TouchableOpacity  onPress={() => {
                            this.setState({
                                detailTile:"入库",
                                detailModal:true,
                                detailData:this.state.storageInDetailList
                            })
                        }}>
                            <View style={[styles.itemContentViewStyle,{  }]}>
                                <View style={[styles.itemContentLeftChildViewStyle,{ }]}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>入库数量合计</Text>
                                </View>
                                <View style={styles.itemContentRightChildViewStyle}>
                                    
                                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18, alignContent:"flex-end"}]}>
                                            {this.state.storageInTotalAmount ? this.state.storageInTotalAmount  : "-" } 
                                        </Text>
                                    
                                </View>
                            </View>
                        </TouchableOpacity>  
                        <TouchableOpacity  onPress={() => {
                            this.setState({
                                detailTile:"出库",
                                detailModal:true,
                                detailData:this.state.storageOutDetailList
                            })
                        }}>
                            <View style={[styles.itemContentViewStyle,{ backgroundColor:"#F6F9FA"  }]}>
                                <View style={[styles.itemContentLeftChildViewStyle,{  }]}>
                                    <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18}]}>出库数量合计</Text>
                                </View>
                                <View style={[styles.itemContentRightChildViewStyle, {}]}>
                                    
                                        <Text style={[styles.itemContentChildTextStyle,{fontWeight:'bold', fontSize:18,}]}>
                                            {this.state.storageOutTotalAmount ? this.state.storageOutTotalAmount  : "-" } 
                                        </Text>
                                    
                                </View>
                            </View>
                        </TouchableOpacity>
                    </ScrollView>

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.detailModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                            <View style={[styles.titleViewStyle,{height:35, backgroundColor:'#91b893',borderRadius:5, justifyContent:'center',alignItems:'center',marginTop:10}]}>
                                <Text style={[styles.titleTextStyle,{fontSize:20,fontWeight:'bold',color:"#ffffff"}]}>{this.state.detailTile + '详情'}</Text>
                            </View>
                            <ScrollView style={{ }}>
                                
                                <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row',justifyContent:'center',marginLeft:20}}>
                                    <View style={{}}>
                                        <View style={[{marginTop:10,flexWrap:'nowrap',flexDirection:'row',justifyContent:'center'}]}>
                                            <View style={[{width:(screenWidth - 30) / 2, justifyContent:'center'}]}>
                                                <Text style={[{fontSize:16,marginTop:10}]}>生产日期</Text>
                                            </View>
                                            <View style={[{ justifyContent:'center',left:-20}]}>
                                                <Text style={[{fontSize:16,marginTop:10}]}>完成数量</Text>
                                            </View>
                                        </View>
                                    </View>
                                </View>
                                                        
                                <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row',justifyContent:'center' ,display:'flex',marginLeft:-20}}>
                                    {
                                        (this.state.detailData && this.state.detailData.length > 0)
                                        ?
                                        this.state.detailData.map((item, index) => {
                                            if (index < 1000) {
                                                return this.renderProcessRow(item)
                                            }
                                        })
                                        :
                                        <View style={[{alignItems: 'center', justifyContent: 'center',marginTop:200}]}>
                                            <Text style={{color:'#A0A0A0',fontSize:25}}>暂无数据</Text>
                                        </View>
                                    }
                                </View> 

                            </ScrollView>

                                <View>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            detailModal:false
                                        })
                                    }}>
                                        <View style={[styles.btnRowLeftCancelBtnView,{ height:35}]} >
                                        {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                            <Text  style={[styles.titleTextStyle,{fontWeight: 'bold',fontSize:18,color:'#a1a1a1'}]}>返       回</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                        <View>
                        </View>
                    </Modal>

                    <BottomScrollSelect 
                        ref={'SelectQryStartDate'} 
                        callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectQryEndDate'} 
                        callBackDateValue={this.callBackSelectQryEndDateValue.bind(this)}
                    />

                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({

    _innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:0,
    },

    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0
    },
    innerViewStyle: {
        marginTop: 10,
        // borderColor: "#ffffff",
        backgroundColor:'#ffffff',
        // borderWidth: 14,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    // itemContentViewStyle: {
    //     flexDirection: 'row',
    //     justifyContent: 'space-between',
    //     marginLeft: 25
    // },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginRight:25,
        // marginTop: 15,
        margin:15,
        fontSize: 16
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        // marginLeft:10,
        // marginTop:10
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:180,
    },
    itemContentRightChildViewStyle:{
        flexDirection:"row",
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        width:screenWidth - 180,
        // paddingRight:10,
        width:200, 
        justifyContent:"flex-end"
    },
    // itemContentChildTextStyle:{
    //     // marginLeft:10,
    //     marginBottom:10,
    //     fontSize:16
    // },


    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },

    btnRowLeftCancelBtnView:{
        flexDirection:'row',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
        alignItems:'center',
        justifyContent:'center',
        borderWidth:1,
        borderColor:'#a1a1a1',
        borderRadius:5,
        height:40,        
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        // paddingTop:5,
        // paddingBottom:5,
        marginTop:4,
        marginBottom:4,
        marginLeft:15, 
        // borderTopWidth:1,
        // borderTopColor:'#F1F1F1',
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:0,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'#E63633',
        marginLeft:4,
        marginRight:3
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:4,
        marginRight:3,
    },
});