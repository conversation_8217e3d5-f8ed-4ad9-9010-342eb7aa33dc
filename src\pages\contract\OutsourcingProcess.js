import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class OutsourcingProcess extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 4,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      selCategoryChooseName: '全部',
      selCategoryChooseCode: 'all',
      searchKeyWord: '',
      topBlockLayoutHeight: 0,
      modal: false,
      modalType: 'common',
    };
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {tenantId} = route.params;
      if (tenantId) {
        console.log('=============tenantId' + tenantId + '');
      }
    }

    let categoryChooseDataSource = [
      {
        chooseCode: 'all',
        chooseName: '全部',
      },
      {
        chooseCode: 'semifinished',
        chooseName: '成型',
      },
      {
        chooseCode: 'encastage',
        chooseName: '入窑',
      },
      {
        chooseCode: 'goods',
        chooseName: '成品',
      },
      {
        chooseCode: 'storageOut',
        chooseName: '出库',
      },
    ];

    this.setState({
      categoryChooseDataSource: categoryChooseDataSource,
    });

    this.loadOutsourcingProcessList();
  }

  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/out/sourcing/progress/get';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      category:
        this.state.selCategoryChooseName === '全部'
          ? 'all'
          : this.state.selCategoryChooseCode,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      let list = dataAll;
      list.map((item, index) => {
        item.spOutSourcingProgressProductDTOList.map((elem, index) => {
          elem.spOutSourcingProgressInfoDTOList.map((el, index) => {
            el.modal = false;
            el.semiModal = false;
            el.encastageModal = false;
            el.goodsModal = false;
          });
        });
      });
      this.setState({
        dataSource: list,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  loadOutsourcingProcessList = () => {
    let url = '/biz/out/sourcing/progress/get';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      category:
        this.state.selCategoryChooseName === '全部'
          ? 'all'
          : this.state.selCategoryChooseCode,
    };
    httpPost(url, loadRequest, this.loadOutsourcingProcessListCallBack);
  };

  loadOutsourcingProcessListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      let list = dataAll;
      list.map((item, index) => {
        item.spOutSourcingProgressProductDTOList.map((elem, index) => {
          elem.spOutSourcingProgressInfoDTOList.map((el, index) => {
            el.modal = false;
            el.semiModal = false;
            el.encastageModal = false;
            el.goodsModal = false;
          });
        });
      });
      console.log(list);
      this.setState({
        dataSource: list,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  categoryChooseStateRow = (item, index) => {
    return (
      <View key={item.chooseCode}>
        <TouchableOpacity
          onPress={() => {
            var selCategoryChooseName = item.chooseName;
            var selCategoryChooseCode = item.chooseCode;
            console.log('=========' + selCategoryChooseCode);
            this.setState({
              selCategoryChooseName: selCategoryChooseName,
              selCategoryChooseCode: selCategoryChooseCode,
            });

            let loadUrl = '/biz/out/sourcing/progress/get';
            let loadRequest = {
              currentPage: 1,
              pageSize: this.state.pageSize,
              searchKeyWord: this.state.searchKeyWord,
              category:
                selCategoryChooseName === '全部'
                  ? 'all'
                  : selCategoryChooseCode,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
          }}>
          <View
            key={item.chooseCode}
            style={[
              item.chooseName === this.state.selCategoryChooseName
                ? CommonStyle.selectedBlockItemViewStyle
                : CommonStyle.blockItemViewStyle,
              {paddingLeft: 13, paddingRight: 13},
            ]}>
            <Text
              style={[
                item.chooseName === this.state.selCategoryChooseName
                  ? CommonStyle.selectedBlockItemTextStyle16
                  : CommonStyle.blockItemTextStyle16,
                {fontWeight: 'bold'},
              ]}>
              {item.chooseName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      </TouchableOpacity>
    );
  }
  // 头部右侧
  renderRightItem() {
    return <View></View>;
  }

  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadOutsourcingProcessList();
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };

  searchByKeyWord = () => {
    let loadUrl = '/biz/out/sourcing/progress/get';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      category:
        this.state.selCategoryChooseName === '全部'
          ? 'all'
          : this.state.selCategoryChooseCode,
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  renderProcessRow = (item, index) => {
    return (
      <View key={item.productionTime}>
        <View style={[styles.itemContentViewStyle, {marginTop: 5}]}>
          <View style={[styles.itemContentLeftChildViewStyle, {}]}>
            <Text style={[styles.itemContentChildTextStyle, {fontSize: 16}]}>
              {item.productionTime}
            </Text>
          </View>
          <View style={styles.itemContentRightChildViewStyle}>
            <Text
              style={[
                styles.itemContentChildTextStyle,
                {fontSize: 16, textAlign: 'center'},
              ]}>
              {item.completeAmount}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  renderRow = (item, index) => {
    return (
      <View style={styles.innerViewStyle}>
        <View style={styles.titleViewStyle}>
          <Text style={[styles.titleTextStyle, {fontWeight: 'bold'}]}>
            客户：{item.customerName}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={[styles.titleTextStyle, {fontWeight: 'bold'}]}>
            合同：{item.contractName}
          </Text>
          {/* <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>{item.outsourcingTenantName}</Text> */}
        </View>
        {item.spOutSourcingProgressProductDTOList &&
        item.spOutSourcingProgressProductDTOList.length > 0 ? (
          item.spOutSourcingProgressProductDTOList.map((productItem, index) => {
            return (
              <View key={productItem.seriesName}>
                <View
                  style={[
                    styles.titleViewStyle,
                    {
                      height: 35,
                      backgroundColor: '#91b893',
                      borderRadius: 5,
                      justifyContent: 'center',
                      alignItems: 'center',
                    },
                  ]}>
                  <Text
                    style={[
                      styles.titleTextStyle,
                      {fontWeight: 'bold', fontSize: 18, color: '#FFFFFF'},
                    ]}>
                    {productItem.seriesName}
                  </Text>
                </View>
                {productItem.spOutSourcingProgressInfoDTOList &&
                productItem.spOutSourcingProgressInfoDTOList.length > 0 ? (
                  productItem.spOutSourcingProgressInfoDTOList.map(
                    (item, index) => {
                      return (
                        <View>
                          <View style={styles.titleViewStyle}>
                            <Text
                              style={[
                                styles.titleTextStyle,
                                {width: screenWidth - 150},
                              ]}>
                              产品型号：{item.brickTypeName}
                            </Text>
                            <Text
                              style={{
                                paddingTop: 3,
                                paddingBottom: 3,
                                paddingLeft: 5,
                                paddingRight: 5,
                                height: 23,
                                borderRadius: 12,
                                backgroundColor: 'rgba(255,0,0,0.4)',
                                color: '#FFFFFF',
                              }}>
                              {item.outsourcingTenantName}
                            </Text>
                          </View>
                          {this.state.selCategoryChooseCode == 'storageOut' ? (
                            <View
                              style={[
                                styles.titleViewStyle,
                                {flexWrap: 'wrap'},
                              ]}>
                              <Text style={styles.titleTextStyle}>
                                合同数：{item.contractAmount}
                              </Text>
                              <TouchableOpacity
                                onPress={() => {
                                  this.setState({
                                    modalType: 'common',
                                  });
                                  var list = this.state.dataSource;
                                  list.map((elem) => {
                                    elem.spOutSourcingProgressProductDTOList.map(
                                      (ele) => {
                                        if (
                                          ele.seriesName ===
                                          productItem.seriesName
                                        ) {
                                          ele.spOutSourcingProgressInfoDTOList.map(
                                            (el) => {
                                              if (
                                                el.brickTypeName ===
                                                  item.brickTypeName &&
                                                el.outsourcingTenantName ===
                                                  item.outsourcingTenantName
                                              ) {
                                                el.modal = true;
                                              }
                                            },
                                          );
                                        }
                                      },
                                    );
                                  });
                                  this.setState({
                                    dataSource: list,
                                  });
                                }}>
                                <Text
                                  style={[
                                    styles.titleTextStyle,
                                    {color: '#3ab240', fontSize: 16},
                                  ]}>
                                  完成：{item.completeAmount}
                                </Text>
                              </TouchableOpacity>

                              <Text style={styles.titleTextStyle}>
                                下欠：{item.owingAmount}
                              </Text>
                            </View>
                          ) : this.state.selCategoryChooseCode == 'all' ? (
                            <View />
                          ) : (
                            <View style={styles.titleViewStyle}>
                              <Text style={styles.titleTextStyle}>
                                合同数：{item.contractAmount}
                              </Text>
                              <TouchableOpacity
                                onPress={() => {
                                  this.setState({
                                    modalType: 'common',
                                  });
                                  var list = this.state.dataSource;
                                  list.map((elem) => {
                                    elem.spOutSourcingProgressProductDTOList.map(
                                      (ele) => {
                                        if (
                                          ele.seriesName ===
                                          productItem.seriesName
                                        ) {
                                          ele.spOutSourcingProgressInfoDTOList.map(
                                            (el) => {
                                              if (
                                                el.brickTypeName ===
                                                  item.brickTypeName &&
                                                el.outsourcingTenantName ===
                                                  item.outsourcingTenantName
                                              ) {
                                                el.modal = true;
                                              }
                                            },
                                          );
                                        }
                                      },
                                    );
                                  });
                                  this.setState({
                                    dataSource: list,
                                  });
                                }}>
                                <Text
                                  style={[
                                    styles.titleTextStyle,
                                    {color: '#3ab240'},
                                  ]}>
                                  完成：{item.completeAmount}
                                </Text>
                              </TouchableOpacity>
                            </View>
                          )}

                          {this.state.selCategoryChooseCode == 'all' ? (
                            <View>
                              <View
                                style={[
                                  styles.titleViewStyle,
                                  {flexWrap: 'wrap'},
                                ]}>
                                <Text style={styles.titleTextStyle}>
                                  合同数：{item.contractAmount}
                                </Text>
                                <TouchableOpacity
                                  onPress={() => {
                                    this.setState({
                                      modalType: 'semi',
                                    });
                                    var list = this.state.dataSource;
                                    list.map((elem) => {
                                      elem.spOutSourcingProgressProductDTOList.map(
                                        (ele) => {
                                          if (
                                            ele.seriesName ===
                                            productItem.seriesName
                                          ) {
                                            ele.spOutSourcingProgressInfoDTOList.map(
                                              (el) => {
                                                if (
                                                  el.brickTypeName ===
                                                    item.brickTypeName &&
                                                  el.outsourcingTenantName ===
                                                    item.outsourcingTenantName
                                                ) {
                                                  el.modal = true;
                                                }
                                              },
                                            );
                                          }
                                        },
                                      );
                                    });
                                    this.setState({
                                      dataSource: list,
                                    });
                                  }}>
                                  <Text
                                    style={[
                                      styles.titleTextStyle,
                                      {color: '#3ab240', fontSize: 16},
                                    ]}>
                                    成型数：{item.semifinishedAmount}
                                  </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                  onPress={() => {
                                    this.setState({
                                      modalType: 'encastage',
                                    });
                                    var list = this.state.dataSource;
                                    list.map((elem) => {
                                      elem.spOutSourcingProgressProductDTOList.map(
                                        (ele) => {
                                          if (
                                            ele.seriesName ===
                                            productItem.seriesName
                                          ) {
                                            ele.spOutSourcingProgressInfoDTOList.map(
                                              (el) => {
                                                if (
                                                  el.brickTypeName ===
                                                    item.brickTypeName &&
                                                  el.outsourcingTenantName ===
                                                    item.outsourcingTenantName
                                                ) {
                                                  el.modal = true;
                                                }
                                              },
                                            );
                                          }
                                        },
                                      );
                                    });
                                    this.setState({
                                      dataSource: list,
                                    });
                                  }}>
                                  <Text
                                    style={[
                                      styles.titleTextStyle,
                                      {color: '#3ab240', fontSize: 16},
                                    ]}>
                                    入窑数：{item.encastageAmount}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                              <View
                                style={[
                                  styles.titleViewStyle,
                                  {flexWrap: 'wrap'},
                                ]}>
                                <TouchableOpacity
                                  onPress={() => {
                                    this.setState({
                                      modalType: 'goods',
                                    });
                                    var list = this.state.dataSource;
                                    list.map((elem) => {
                                      elem.spOutSourcingProgressProductDTOList.map(
                                        (ele) => {
                                          if (
                                            ele.seriesName ===
                                            productItem.seriesName
                                          ) {
                                            ele.spOutSourcingProgressInfoDTOList.map(
                                              (el) => {
                                                if (
                                                  el.brickTypeName ===
                                                    item.brickTypeName &&
                                                  el.outsourcingTenantName ===
                                                    item.outsourcingTenantName
                                                ) {
                                                  el.modal = true;
                                                }
                                              },
                                            );
                                          }
                                        },
                                      );
                                    });
                                    this.setState({
                                      dataSource: list,
                                    });
                                  }}>
                                  <Text
                                    style={[
                                      styles.titleTextStyle,
                                      {color: '#3ab240', fontSize: 16},
                                    ]}>
                                    成品数：{item.goodsAmount}
                                  </Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                  onPress={() => {
                                    this.setState({
                                      modalType: 'storageOut',
                                    });
                                    var list = this.state.dataSource;
                                    list.map((elem) => {
                                      elem.spOutSourcingProgressProductDTOList.map(
                                        (ele) => {
                                          if (
                                            ele.seriesName ===
                                            productItem.seriesName
                                          ) {
                                            ele.spOutSourcingProgressInfoDTOList.map(
                                              (el) => {
                                                if (
                                                  el.brickTypeName ===
                                                    item.brickTypeName &&
                                                  el.outsourcingTenantName ===
                                                    item.outsourcingTenantName
                                                ) {
                                                  el.modal = true;
                                                }
                                              },
                                            );
                                          }
                                        },
                                      );
                                    });
                                    this.setState({
                                      dataSource: list,
                                    });
                                  }}>
                                  <Text
                                    style={[
                                      styles.titleTextStyle,
                                      {color: '#3ab240', fontSize: 16},
                                    ]}>
                                    出库数：{item.storageOutAmount}
                                  </Text>
                                </TouchableOpacity>
                                <Text style={[styles.titleTextStyle, {}]}>
                                  下欠：{item.owingAmount}
                                </Text>
                              </View>
                            </View>
                          ) : (
                            <View />
                          )}

                          {/* <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                                                    <TouchableOpacity onPress={() => {
                                                        var list = this.state.dataSource;
                                                        list.map((elem)=>{
                                                            elem.spOutSourcingProgressProductDTOList.map((ele)=>{
                                                                if (ele.seriesName === productItem.seriesName) {
                                                                    ele.spOutSourcingProgressInfoDTOList.map((el)=>{
                                                                        if(el.brickTypeName === item.brickTypeName){
                                                                            el.modal = true
                                                                        }
                                                                    })
                                                                }
                                                            })
                                                        })
                                                        this.setState({
                                                            dataSource:list
                                                        })
                                                    }}>
                                                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle
                                                            , { width: 80,flexDirection:'row' ,backgroundColor:"#F2C16D"}]}>
                                                            <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/progress.png')}></Image>
                                                            <Text style={[CommonStyle.itemBottomDetailBtnTextStyle]}>详情</Text>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View> */}
                          <Modal
                            animationType={'slide'}
                            transparent={true}
                            onRequestClose={() =>
                              console.log('onRequestClose...')
                            }
                            visible={item.modal}>
                            <View style={CommonStyle.fullScreenKeepOut}>
                              <View style={CommonStyle.modalContentViewStyle}>
                                <View
                                  style={[
                                    styles.titleViewStyle,
                                    {
                                      height: 35,
                                      backgroundColor: '#91b893',
                                      borderRadius: 5,
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      marginTop: 10,
                                    },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.titleTextStyle,
                                      {
                                        fontSize: 20,
                                        fontWeight: 'bold',
                                        color: '#ffffff',
                                      },
                                    ]}>
                                    进度详情
                                  </Text>
                                </View>
                                <ScrollView style={{}}>
                                  <View
                                    style={[styles.itemContentViewStyle, {}]}>
                                    <View
                                      style={[
                                        styles.itemContentLeftChildViewStyle,
                                        {},
                                      ]}>
                                      <Text
                                        style={[
                                          styles.itemContentChildTextStyle,
                                          {fontWeight: 'bold', fontSize: 16},
                                        ]}>
                                        生产日期
                                      </Text>
                                    </View>
                                    <View
                                      style={
                                        styles.itemContentRightChildViewStyle
                                      }>
                                      <Text
                                        style={[
                                          styles.itemContentChildTextStyle,
                                          {
                                            fontWeight: 'bold',
                                            fontSize: 16,
                                            textAlign: 'center',
                                          },
                                        ]}>
                                        完成数量
                                      </Text>
                                    </View>
                                  </View>

                                  {this.state.modalType == 'semi' ? (
                                    <View
                                      style={{
                                        marginTop: 0,
                                        index: 1000,
                                        flexWrap: 'wrap',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                      }}>
                                      {item.semiDetailList &&
                                      item.semiDetailList.length > 0 ? (
                                        item.semiDetailList.map(
                                          (item, index) => {
                                            if (index < 1000) {
                                              return this.renderProcessRow(
                                                item,
                                              );
                                            }
                                          },
                                        )
                                      ) : (
                                        <View
                                          style={[
                                            {
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                              marginTop: 20,
                                            },
                                          ]}>
                                          <Text
                                            style={{
                                              color: '#A0A0A0',
                                              fontSize: 25,
                                            }}>
                                            暂无数据
                                          </Text>
                                        </View>
                                      )}
                                    </View>
                                  ) : this.state.modalType == 'encastage' ? (
                                    <View
                                      style={{
                                        marginTop: 0,
                                        index: 1000,
                                        flexWrap: 'wrap',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                      }}>
                                      {item.encastageDetailList &&
                                      item.encastageDetailList.length > 0 ? (
                                        item.encastageDetailList.map(
                                          (item, index) => {
                                            if (index < 1000) {
                                              return this.renderProcessRow(
                                                item,
                                              );
                                            }
                                          },
                                        )
                                      ) : (
                                        <View
                                          style={[
                                            {
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                              marginTop: 20,
                                            },
                                          ]}>
                                          <Text
                                            style={{
                                              color: '#A0A0A0',
                                              fontSize: 25,
                                            }}>
                                            暂无数据
                                          </Text>
                                        </View>
                                      )}
                                    </View>
                                  ) : this.state.modalType == 'goods' ? (
                                    <View
                                      style={{
                                        marginTop: 0,
                                        index: 1000,
                                        flexWrap: 'wrap',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                      }}>
                                      {item.goodsDetailList &&
                                      item.goodsDetailList.length > 0 ? (
                                        item.goodsDetailList.map(
                                          (item, index) => {
                                            if (index < 1000) {
                                              return this.renderProcessRow(
                                                item,
                                              );
                                            }
                                          },
                                        )
                                      ) : (
                                        <View
                                          style={[
                                            {
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                              marginTop: 20,
                                            },
                                          ]}>
                                          <Text
                                            style={{
                                              color: '#A0A0A0',
                                              fontSize: 25,
                                            }}>
                                            暂无数据
                                          </Text>
                                        </View>
                                      )}
                                    </View>
                                  ) : this.state.modalType == 'storageOut' ? (
                                    <View
                                      style={{
                                        marginTop: 0,
                                        index: 1000,
                                        flexWrap: 'wrap',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                      }}>
                                      {item.storageOutDetailList &&
                                      item.storageOutDetailList.length > 0 ? (
                                        item.storageOutDetailList.map(
                                          (item, index) => {
                                            if (index < 1000) {
                                              return this.renderProcessRow(
                                                item,
                                              );
                                            }
                                          },
                                        )
                                      ) : (
                                        <View
                                          style={[
                                            {
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                              marginTop: 20,
                                            },
                                          ]}>
                                          <Text
                                            style={{
                                              color: '#A0A0A0',
                                              fontSize: 25,
                                            }}>
                                            暂无数据
                                          </Text>
                                        </View>
                                      )}
                                    </View>
                                  ) : (
                                    <View
                                      style={{
                                        marginTop: 0,
                                        index: 1000,
                                        flexWrap: 'wrap',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                      }}>
                                      {item.detailList &&
                                      item.detailList.length > 0 ? (
                                        item.detailList.map((item, index) => {
                                          if (index < 1000) {
                                            return this.renderProcessRow(item);
                                          }
                                        })
                                      ) : (
                                        <View
                                          style={[
                                            {
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                              marginTop: 20,
                                            },
                                          ]}>
                                          <Text
                                            style={{
                                              color: '#A0A0A0',
                                              fontSize: 25,
                                            }}>
                                            暂无数据
                                          </Text>
                                        </View>
                                      )}
                                    </View>
                                  )}
                                </ScrollView>
                                <View>
                                  <TouchableOpacity
                                    onPress={() => {
                                      var list = this.state.dataSource;
                                      list.map((elem) => {
                                        elem.spOutSourcingProgressProductDTOList.map(
                                          (ele) => {
                                            if (
                                              ele.seriesName ===
                                              productItem.seriesName
                                            ) {
                                              ele.spOutSourcingProgressInfoDTOList.map(
                                                (el) => {
                                                  if (
                                                    el.brickTypeName ===
                                                    item.brickTypeName
                                                  ) {
                                                    el.modal = false;
                                                  }
                                                },
                                              );
                                            }
                                          },
                                        );
                                      });
                                      this.setState({
                                        dataSource: list,
                                      });
                                    }}>
                                    <View
                                      style={[
                                        styles.btnRowLeftCancelBtnView,
                                        {height: 35},
                                      ]}>
                                      {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                      <Text
                                        style={[
                                          styles.titleTextStyle,
                                          {
                                            fontWeight: 'bold',
                                            fontSize: 18,
                                            color: '#a1a1a1',
                                          },
                                        ]}>
                                        返 回
                                      </Text>
                                    </View>
                                  </TouchableOpacity>
                                </View>
                              </View>
                            </View>
                            <View></View>
                          </Modal>
                        </View>
                      );
                    },
                  )
                ) : (
                  <View />
                )}
              </View>
            );
          })
        ) : (
          <View />
        )}
      </View>
    );
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="外协进度"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />

        <View
          style={[styles.innerViewStyle, {marginTop: 0}]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={{
              marginTop: 0,
              index: 1000,
              flexWrap: 'wrap',
              flexDirection: 'row',
            }}>
            {this.state.categoryChooseDataSource &&
            this.state.categoryChooseDataSource.length > 0 ? (
              this.state.categoryChooseDataSource.map((item, index) => {
                return this.categoryChooseStateRow(item);
              })
            ) : (
              <View />
            )}
          </View>

          <View style={{}}>
            <View style={styles.inputRowStyle}>
              <View style={styles.leftLabView}>
                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                <Image
                  style={{width: 25, height: 25}}
                  source={require('../../assets/icon/iconfont/search.png')}></Image>
              </View>
              <TextInput
                style={[styles.searchInputText, {}]}
                returnKeyType="search"
                returnKeyLabel="搜索"
                onSubmitEditing={(e) => {
                  this.searchByKeyWord();
                }}
                numberOfLines={1}
                placeholder={'外协/客户/合同/订单'}
                onChangeText={(text) => this.setState({searchKeyWord: text})}>
                {this.state.searchKeyWord}
              </TextInput>
              {/* <TouchableOpacity onPress={()=>{
                                this.searchByKeyWord();
                                }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:70}]}>
                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>查询</Text>
                                </View>
                            </TouchableOpacity> */}
            </View>
          </View>
        </View>

        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.customerName + item.contractName}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
    marginTop: 5,
  },
  btnRowLeftCancelBtnView: {
    flexDirection: 'row',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#a1a1a1',
    borderRadius: 5,
    height: 40,
  },
  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
  },
  searchInputText: {
    width: screenWidth - 100,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },

  innerViewStyle: {
    // marginTop:10,
    // borderColor:"#F4F4F4",
    // borderWidth:14,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 20,
    marginTop: 15,
    fontSize: 16,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginTop: 10,
  },
  itemContentLeftChildViewStyle: {
    flexDirection: 'column',
    // alignContent:'flex-start',
    // justifyContent:'flex-start',
    // alignItems:'flex-start',
    width: screenWidth - 180,
    marginLeft: 20,
  },
  itemContentRightChildViewStyle: {
    flexDirection: 'column',
    // alignContent:'flex-start',
    // justifyContent:'flex-start',
    // alignItems:'flex-start',
    width: 120,
    marginLeft: -20,
  },
});
