import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class MaterialStorageAdd extends Component {
    constructor(){
        super()
        this.state = {
            storageId: "",
            classifyId: "",
            amount: "",
            supplier:"",
            actualCast: "",
            operator: "",
            storageTime:"",
            operate:"",
            selectStorageTime:[],
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        // 原料大类
        loadTypeUrl= "/biz/material/classify/list";
        loadRequest={
            'currentPage':1,
            'pageSize':100,
            "classifyType": "B",
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    materialClassifyDataSource:response.data.dataList,
                })
            }
        });

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageId } = route.params;
            if (storageId) {
                console.log("========Edit==storageId:", storageId);
                this.setState({
                    storageId:storageId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/material/storage/get";
                loadRequest={'storageId':storageId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditPurchaseDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectStorageTime:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                })
            }
        }
    }
    loadEditPurchaseDataCallBack=(response)=>{

        if (response.code == 200 && response.data) {
            var selectStorageTime;
            if (response.data.storageTime) {
                console.log("=========selectStorageTime=1:", response.data.storageTime);
                selectStorageTime = response.data.storageTime.split("-");
            }
            else {
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                selectStorageTime = [currentDate.getFullYear(), currentDateMonth, currentDateDay];
                this.setState({
                    selectStorageTime:selectStorageTime,
                })
                console.log("=========selectStorageTime===3:", selectStorageTime);
            }
            console.log("=========selectStorageTime:", selectStorageTime);
            this.setState({
                storageId:response.data.storageId,
                classifyId: response.data.classifyId,
                amount: response.data.amount,
                supplier: response.data.supplier,
                actualCast: response.data.actualCast,
                operator: response.data.operator,
                storageTime: response.data.storageTime,
                selectStorageTime:selectStorageTime,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Text style={CommonStyle.headLeftText}>返回</Text>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("MaterialStorageList")
            }}>
                <Text style={CommonStyle.headRightText}>原料入库</Text>
            </TouchableOpacity>
        )
    }

    renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.checkId) {
                    return;
                }
                    this.setState({
                        classifyId:item.classifyId,
                    })
                }}>
                <View key={item.orderId} style={[item.classifyId===this.state.classifyId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.classifyId===this.state.classifyId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.classifyName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    openStorageTime(){
        this.refs.SelectStorageTime.showDate(this.state.selectStorageTime)
    }
    callBackSelectStorageTimeValue(value){
        console.log("==========入库时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectStorageTime:value
        })
        if (this.state.selectStorageTime && this.state.selectStorageTime.length) {
            var storageTime = "";
            var vartime;
            for(var index=0;index<this.state.selectStorageTime.length;index++) {
                vartime = this.state.selectStorageTime[index];
                if (index===0) {
                    storageTime += vartime;
                }
                else{
                    storageTime += "-" + vartime;
                }
            }
            this.setState({
                storageTime:storageTime
            })
        }
    }

    saveContract =()=> {
        console.log("=======saveContract");
        let toastOpts;
        if (!this.state.classifyId) {
            toastOpts = getFailToastOpts("请勾选原料大类");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.amount) {
            toastOpts = getFailToastOpts("请输入数量");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.supplier) {
            toastOpts = getFailToastOpts("请输入供应商");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/material/storage/add";
        if (this.state.storageId) {
            console.log("=========Edit=save===storageId", this.state.storageId)
            url= "/biz/material/storage/modify";
        }
        let requestParams={
            "storageId": this.state.storageId,
            "classifyId": this.state.classifyId,
            "amount": this.state.amount,
            "supplier": this.state.supplier,
            "actualCast": this.state.actualCast,
            "operator": constants.loginUser.userName,
            "storageTime": this.state.storageTime,
        };
        httpPost(url, requestParams, this.saveStorageCallBack);
    }
    
    // 保存回调函数
    saveStorageCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '原料入库'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabNameTextStyle}>原料大类</Text>
                    </View>
                    
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.materialClassifyDataSource && this.state.materialClassifyDataSource.length > 0) 
                            ? 
                            this.state.materialClassifyDataSource.map((item, index)=>{
                                return this.renderRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>数量</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入数量'}
                            onChangeText={(text) => this.setState({amount:text})}
                        >
                            {this.state.amount}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>供应商</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入供应商'}
                            onChangeText={(text) => this.setState({supplier:text})}
                        >
                            {this.state.supplier}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>费用</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入费用'}
                            onChangeText={(text) => this.setState({actualCast:text})}
                        >
                            {this.state.actualCast}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>入库时间</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openStorageTime()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.storageTime ? "请选择入库时间" : this.state.storageTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveContract.bind(this)}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <BottomScrollSelect 
                        ref={'SelectStorageTime'} 
                        callBackDateValue={this.callBackSelectStorageTimeValue.bind(this)}
                    />
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})