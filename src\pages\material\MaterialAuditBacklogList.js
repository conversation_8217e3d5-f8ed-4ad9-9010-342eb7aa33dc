import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,TextInput,
    FlatList,RefreshControl,Image,ScrollView,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class MaterialAuditBacklogList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            auditDataSource:[],
            selAuditType:"all",
            searchKeyWord:""

        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        let auditDataSource = [
            {
                typeId:0,
                auditTypeCode: 'all',
                auditTypeName: '全部',
            },
            {
                typeId:1,
                auditTypeCode: 'MATERIAL_PURCHASE',
                auditTypeName: "原料采购",
            },
            {
                typeId:2,
                auditTypeCode: 'MATERIAL_INVENTORY_I',
                auditTypeName: "原料入库",
            },
            {
                typeId:3,
                auditTypeCode: 'MATERIAL_INVENTORY_O',
                auditTypeName: "原料出库",
            },
            // {
            //     auditTypeCode: '4',
            //     auditTypeName: "驳回",
            // },
        ]
        this.setState({
            auditDataSource: auditDataSource,
        })
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
            this.loadAuditBacklogList();

        }
    }
        // 回调函数
        callBackFunction=()=>{
            let url= "/biz/audit/node/record/auditList";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "auditUserId":constants.loginUser.userId,
                "auditResult":"I",
                "searchKeyWord":this.state.searchKeyWord,
                "auditTypeCode":this.state.selAuditType ==="all" ? null :this.state.selAuditType,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    
        // 下拉触顶刷新到第一页
        _loadFreshData=()=>{
            if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
                console.log("==========不刷新=====");
                return;
            }
            this.setState({
                currentPage:1
            })
            let url= "/biz/audit/node/record/auditList";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "auditUserId":constants.loginUser.userId,
                "auditResult":"I",
                "searchKeyWord":this.state.searchKeyWord,
                "auditTypeCode":this.state.selAuditType ==="all" ? null :this.state.selAuditType,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    
        _loadFreshDataCallBack=(response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                var dataNew = response.data.dataList;
                var dataAll = [...dataNew];
                this.setState({
                    dataSource:dataAll,
                    currentPage:response.data.currentPage + 1,
                    totalPage:response.data.totalPage,
                    totalRecord:response.data.totalRecord,
                    refreshing:false
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        }
    
        flatListFooterComponent=()=>{
            return(
                <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
            )
        }
    
        // 上拉触底加载下一页
        _loadNextData=()=>{
            if ((this.state.currentPage-1) >= this.state.totalPage) {
                WToast.show({data:"已经是最后一页了，我们也是有底线的"});
                return;
            }
            if (this.state.refreshing) {
                WToast.show({data: 'loading...'});
                return;
            }
            this.setState({ refreshing: true }, () => {
                    console.log('refreshing 已更新:', this.state.refreshing);
                    // 在这里执行后续操作
                    this.loadAuditBacklogList();
            });
        }
    
        loadAuditBacklogList=()=>{
            let url= "/biz/audit/node/record/auditList";
            let loadRequest={
                "currentPage": this.state.currentPage,
                "pageSize": this.state.pageSize,
                "auditUserId":constants.loginUser.userId,
                "auditResult":"I",
                "searchKeyWord":this.state.searchKeyWord,
                "auditTypeCode":this.state.selAuditType ==="all" ? null :this.state.selAuditType,
            };
            httpPost(url, loadRequest, this.loadAuditBacklogListCallBack);
        }
    
    
        loadAuditBacklogListCallBack=(response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                var dataNew = response.data.dataList;
                console.log(dataNew)
                var dataOld = this.state.dataSource;
                var dataAll = [...dataOld,...dataNew];
                this.setState({
                    dataSource:dataAll,
                    currentPage:response.data.currentPage + 1,
                    totalPage:response.data.totalPage,
                    totalRecord:response.data.totalRecord,
                    refreshing:false
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        }

        renderRow=(item, index)=>{
            return (
                    <View key={item.recordId} style={styles.innerViewStyle}>
                        {
                            index==0?
                            <View style={CommonStyle.lineListHeadRenderRowStyle}>
                            </View>
                            :
                            <View></View>
                        }
                        {
                            item.materialPurchaseList && item.materialPurchaseList.length > 0?
                            <View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>原料类别：{item.materialPurchaseList[0].parentClassifyName}</Text>
                                    <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(220,150,30,0.6)', color:'#FFFFFF'}}>
                                        原料采购
                                    </Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>原料名称：{item.materialPurchaseList[0].classifyName}</Text>
                                </View>
                                <View style={[styles.titleViewStyle]}>
                                    <Text style={styles.titleTextStyle}>重量(吨)：{item.materialPurchaseList[0].amount}</Text>
                                </View>
                                {
                                    item.materialPurchaseList[0].supplier ?
                                    <View style={[styles.titleViewStyle]}>
                                        <Text style={styles.titleTextStyle}>供应商：{item.materialPurchaseList[0].supplier}</Text>
                                    </View>
                                    :
                                    <View/>
                                }
                                {
                                    item.materialPurchaseList[0].unitPrice ?
                                    <View style={[styles.titleViewStyle]}>
                                        <Text style={styles.titleTextStyle}>单价：{item.materialPurchaseList[0].unitPrice}</Text>
                                    </View>
                                    :
                                    <View/>
                                }
                                {
                                    item.materialPurchaseList[0].budget ?
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>预计费用：{item.materialPurchaseList[0].budget}</Text>
                                    </View>
                                    :
                                    <View/>
                                }
                                {
                                    item.materialPurchaseList[0].summation ?
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>合计费用：{item.materialPurchaseList[0].summation}</Text>
                                    </View>
                                    :
                                    <View/>
                                }
                                {/* <View style={[styles.titleViewStyle]}>
                                    <Text style={styles.titleTextStyle}>供应商：{item.materialPurchaseList[0].supplier ? item.materialPurchaseList[0].supplier : "信息尚未完善" }</Text>
                                </View>
                                <View style={[styles.titleViewStyle]}>
                                    <Text style={styles.titleTextStyle}>单价：{item.materialPurchaseList[0].unitPrice ? item.materialPurchaseList[0].unitPrice : "信息尚未完善" }</Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>预计费用：{item.materialPurchaseList[0].budget ? item.materialPurchaseList[0].budget : "信息尚未完善"}</Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>提交人：{item.materialPurchaseList[0].operator}</Text>
                                </View> */}
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>提交时间：{item.gmtCreated}</Text>
                                </View>
                                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                    <TouchableOpacity onPress={()=>{
                                            this.props.navigation.navigate("MaterialAudit", 
                                            {
                                                // 传递参数
                                                recordId: item.recordId,
                                                // 传递回调函数
                                                refresh: this.callBackFunction 
                                            })
                                        }}>
                                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:75,flexDirection:"row"}
                                        ]}>
                                            <Image  style={{width:18, height:18,marginRight:5}} source={require('../../assets/icon/iconfont/examine.png')}></Image>
                                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>审核</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={()=>{
                                        this.props.navigation.navigate("MaterialAuditBacklogDetail", 
                                        {
                                            // 传递参数
                                            auditItemId: item.auditItemId,
                                            purchaseItem:item.materialPurchaseList[0],
                                            // 传递回调函数
                                            refresh: this.callBackFunction,
                                            auditTypeCode : "MATERIAL_PURCHASE"
                                        })
                                    }}>
                                    <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64}]}>
                                            <Image  style={{width: 17, height: 17, marginRight: 3}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                                <Text style={{color: '#F0F0F0', fontSize: 14, lineHeight: 20}}>详情</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            <View/>
                        }

                        {
                            item.materialInventoryList && item.materialInventoryList.length > 0?
                            <View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>原料类别：{item.materialInventoryList[0].parentClassifyName}</Text>
                                    {
                                    item.materialInventoryList[0].io==="I" ? 
                                    <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                                        原料入库
                                    </Text>
                                    :
                                    <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'#3ab24070', color:'#FFFFFF'}}>
                                        原料出库
                                    </Text>
                                    }
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>原料名称：{item.materialInventoryList[0].classifyName}</Text>
                                </View>

                                {
                                    (constants.loginUser.tenantId == 66)?
                                    (
                                        item.materialInventoryList[0].io==="I" ? 
                                        <View/>
                                        :
                                        <View>
                                            <View style={styles.titleViewStyle}>
                                                <Text style={styles.titleTextStyle}>用料类型：{item.materialInventoryList[0].materialType === 'Y'?"定型":(item.materialInventoryList[0].materialType === 'N'?"不定型":"暂未选择")}</Text>
                                            </View>
                                        </View>
                                    )
                                    
                                    :
                                    <View/>
                                }

                                {
                                    item.materialInventoryList[0].seriesName ? 
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>需求产品：{item.materialInventoryList[0].seriesName}</Text>
                                    </View> :
                                    
                                    <View />
                                }

                                <View style={[styles.titleViewStyle]}>
                                    <Text style={styles.titleTextStyle}>重量(吨)：{item.materialInventoryList[0].weight}</Text>
                                </View>
                               
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>提交人：{item.materialInventoryList[0].operator}</Text>
                                </View>
                                <View style={styles.titleViewStyle}>
                                    <Text style={styles.titleTextStyle}>提交时间：{item.gmtCreated}</Text>
                                </View>
                                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                    <TouchableOpacity onPress={()=>{
                                            this.props.navigation.navigate("MaterialInventoryAudit", 
                                            {
                                                // 传递参数
                                                recordId: item.recordId,
                                                inventoryId:item.materialInventoryList[0].inventoryId,
                                                // 传递回调函数
                                                refresh: this.callBackFunction 
                                            })
                                        }}>
                                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:75,flexDirection:"row"}
                                        ]}>
                                            <Image  style={{width:18, height:18,marginRight:5}} source={require('../../assets/icon/iconfont/examine.png')}></Image>
                                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>审核</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={()=>{
                                        this.props.navigation.navigate("InventoryAuditBacklogDetail", 
                                        {
                                            auditTypeCode : item.materialInventoryList[0].io==="I" ? "MATERIAL_INVENTORY_I" : "MATERIAL_INVENTORY_O",
                                            // 传递参数
                                            auditItemId: item.auditItemId,
                                            inventoryItem:item,
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                    }}>
                                    <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64}]}>
                                            <Image  style={{width: 17, height: 17, marginRight: 3}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                                <Text style={{color: '#F0F0F0', fontSize: 14, lineHeight: 20}}>详情</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            <View/>
                        }
                    </View>
            )
        }
        
        space(){
            return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
        }
        emptyComponent() {
            return <EmptyListComponent/>
        }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
  
                }}  >
                    {/* <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
                </TouchableOpacity>
            </View>
        )
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    auditTypeChooseStateRow = (item, index) => {
        return (
            <View key={item.auditTypeCode} >
                <TouchableOpacity onPress={() => {
                    var selAuditType = item.auditTypeCode;
                    console.log("selAuditType",selAuditType);
                    this.setState({
                        selAuditType: selAuditType
                    })

                    let url= "/biz/audit/node/record/auditList";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "searchKeyWord":this.state.searchKeyWord,
                        "auditTypeCode":selAuditType ==="all" ? null :selAuditType,
                        "auditUserId":constants.loginUser.userId,
                        "auditResult":"I"
                    };
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.auditTypeCode} style={[{width: screenWidth/4 , height: 49, flexDirection: 'row', justifyContent: 'center'}
                    // ,item.stateCode === this.state.selCompletionStateCode ?
                    //     [styles.selectedBlockItemViewStyle]
                    //     :
                    //     [styles.blockItemViewStyle],
                    ]}>
                        <Text style={[item.auditTypeCode === this.state.selAuditType ?
                            { color: "#255BDA", fontSize: 16, fontWeight: '500', lineHeight: 49, textAlign: 'center', borderColor: "#255BDA", borderBottomWidth: 2, paddingLeft: 5, paddingRight: 5 }
                            :
                            { color: "#2B333F", fontSize: 16, fontWeight: '500', lineHeight: 49, textAlign: 'center'},
                        ]}>
                            {item.auditTypeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }
    searchByKeyWord = () => {
        let loadUrl = "/biz/audit/node/record/auditList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord":this.state.searchKeyWord,
            "auditTypeCode":this.state.selAuditType ==="all" ? null :this.state.selAuditType,
            "auditUserId":constants.loginUser.userId,
            "auditResult":"I"
    };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='我的待办'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                {/* <View style={CommonStyle.contentViewStyle}> */}
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={[{marginLeft:0,marginTop: 0,paddingBottom:0 }]}>
                        <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                            {
                                (this.state.auditDataSource && this.state.auditDataSource.length > 0)
                                    ?
                                    this.state.auditDataSource.map((item, index) => {
                                        return this.auditTypeChooseStateRow(item)
                                    })
                                    :
                                <View />
                            }
                        </View>
                    </View>
                    <View style={[CommonStyle.headViewStyle, { borderLeftWidth: 0, borderRightWidth: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                        <View style={CommonStyle.singleSearchBox}>
                            <View style={CommonStyle.searchBoxWithoutOthers}>
                                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                                <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                <TextInput
                                    style={{color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 5, paddingTop: 0, paddingBottom: 0, paddingRight: 0, paddingLeft: 0, width:'100%' }}
                                    returnKeyType="search"
                                    returnKeyLabel="搜索"
                                    onSubmitEditing={e => {
                                        this.searchByKeyWord();
                                    }}
                                    placeholder={'原料类别/名称'}
                                    onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                >
                                    {this.state.searchKeyWord}
                                </TextInput>
                            </View>
                        </View>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> */}
                        <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        ItemSeparatorComponent={this.space}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                    {/* </ScrollView> */}
                </View>
             </View>
            // </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle: {
        // marginTop:10,
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        // borderWidth: 8
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});