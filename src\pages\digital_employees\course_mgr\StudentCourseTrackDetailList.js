import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  ImageBackground,
  Linking,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import ClassHeadScreen from '../../../component/ClassHeadScreen';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import EmptyListComponent from '../../../component/EmptyListComponent';
import ProgressBar from '../../../component/ProgressBar';
import {ifIphoneXContentViewDynamicHeight} from '../../../utils/ScreenUtil';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;

const commonStyles = {
  color: 'rgba(255, 255, 255, 1)',
  fontSize: 12,
  width: 55,
  height: 19,
  paddingLeft: 9,
  paddingTop: 2,
};

const taskStateBgColor = {
  '0AA': '#0000ff',
  '0BB': '#ff0000',
  '0CC': '#008000',
  default: 'rgba(0,10,32,0.45)',
};

const taskStateText = {
  '0AA': '实习中',
  '0BB': '已超期',
  '0CC': '已完成',
  default: '未开始',
};

const taskTitleBgColor = {
  0: '#FB7B04',
  1: '#1084FD',
  2: '#1E85A3',
  3: '#FBB100',
  4: '#BF181E',
  5: '#1B9342',
};

export default class StudentCourseTrackDetailList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      operate: '',
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      selTaskStateCode: 'all',

      qryStartTime: null,
      selectedQryStartDate: [],

      showSearchItemBlock: false,
      courseTypeDataSource: null,
      selcourseTypeId: null,
      selcourseTypeName: null,

      selStaffId: null,
      documentTypeList: ['TD', 'TV'],
      qryNowTime: null,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  initqryNowTime = () => {
    // 当前时间
    var currentDate = new Date();
    // currentDate.setMonth(currentDate.getMonth() - 1);
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _qryNowTime =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      qryNowTime: _qryNowTime,
    });
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    var _qryStartTime = this.initqryNowTime();
    // 实习类型
    this.initqryNowTime();
    let loadCourseTypeUrl = '/biz/course/type/list';
    let loadCourseTypeRequest = {qryAll: 'Y', currentPage: 1, pageSize: 1000};
    httpPost(loadCourseTypeUrl, loadCourseTypeRequest, (response) => {
      if (response.code == 200 && response.data.dataList) {
        var courseTypeData = response.data.dataList;
        courseTypeData.unshift({courseTypeId: 0, courseTypeName: '全部'});
        this.setState({
          courseTypeDataSource: courseTypeData,
        });
        console.log(
          '==========实习类型数据源：',
          this.state.courseTypeDataSource,
        );
      }
    });

    let taskStateDataSource = [
      {
        stateCode: 'all',
        stateName: '全部',
      },
      {
        stateCode: '0AA',
        stateName: '实习中',
      },
      {
        stateCode: '0BB',
        stateName: '已超期',
      },
      {
        stateCode: '0CC',
        stateName: '已完成',
      },
    ];
    this.setState({
      taskStateDataSource: taskStateDataSource,
    });

    const {route, navigation} = this.props;
    if (route && route.params) {
      const {selStaffId, selStaffName} = route.params;
      if (selStaffId) {
        console.log('=============selStaffId' + selStaffId + '');
        this.setState({
          selStaffId: selStaffId,
          selStaffName: selStaffName,
          operate: '的实习',
        });
        this.loadCourseTaskList(_qryStartTime, selStaffId);
      } else {
        this.setState({
          operate: '实习任务',
        });
        this.loadCourseTaskList(_qryStartTime);
      }
    } else {
      this.setState({
        operate: '实习任务',
      });
      this.loadCourseTaskList(_qryStartTime);
    }
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/course/task/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      checkOutUserId: this.state.selStaffId
        ? this.state.selStaffId
        : constants.loginUser.userId,
      courseTypeId:
        this.state.selcourseTypeId == 0 ? null : this.state.selcourseTypeId,
      taskState:
        this.state.selTaskStateCode === 'all'
          ? null
          : this.state.selTaskStateCode,
      qryStartTime: this.state.qryStartTime,
      documentTypeList: this.state.documentTypeList,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/course/task/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      checkOutUserId: this.state.selStaffId
        ? this.state.selStaffId
        : constants.loginUser.userId,
      courseTypeId:
        this.state.selcourseTypeId == 0 ? null : this.state.selcourseTypeId,
      taskState:
        this.state.selTaskStateCode === 'all'
          ? null
          : this.state.selTaskStateCode,
      qryStartTime: this.state.qryStartTime,
      documentTypeList: this.state.documentTypeList,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadCourseTaskList();
  };

  loadCourseTaskList = (_qryStartTime, selStaffId) => {
    let url = '/biz/course/task/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      checkOutUserId: selStaffId ? selStaffId : constants.loginUser.userId,
      courseTypeId:
        this.state.selcourseTypeId == 0 ? null : this.state.selcourseTypeId,
      taskState:
        this.state.selTaskStateCode === 'all'
          ? null
          : this.state.selTaskStateCode,
      qryStartTime: selStaffId
        ? null
        : _qryStartTime
        ? _qryStartTime
        : this.state.qryStartTime,
      documentTypeList: this.state.documentTypeList,
    };
    httpPost(url, loadRequest, this.loadCourseTaskListCallBack);
  };

  loadCourseTaskListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      console.log(
        'loadCourseTaskListCallBack',
        JSON.stringify(dataAll, null, 6),
      );
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteCourseTask = (courseTaskId) => {
    console.log('=======delete=courseTaskId', courseTaskId);
    let url = '/biz/course/task/delete';
    let requestParams = {courseTaskId: courseTaskId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  setCourseTask = (taskItem, index) => {
    console.log('=======setCourseTask=taskItem', taskItem);
    let requestUrl = '/biz/course/task/modify';
    let requestParams = {
      courseTaskId: taskItem.courseTaskId,
      taskState: taskItem.taskState === '0CC' ? '0AA' : '0CC',
    };
    httpPost(requestUrl, requestParams, (response) => {
      if (response.code == 200) {
        // 更新页面上显示
        let courseTaskDataSource = this.state.dataSource;
        // JS 数组遍历
        courseTaskDataSource.forEach((obj) => {
          if (obj.courseTaskId === taskItem.courseTaskId) {
            obj.taskState = response.data.taskState;
            WToast.show({
              data:
                (response.data.taskState === '0CC' ? '关闭' : '重启') + '完成',
            });
          }
        });
        this.setState({
          dataSource: courseTaskDataSource,
        });
      } else {
        WToast.show({data: response.message});
      }
    });
  };

  renderTaskStateRow = (item, index) => {
    return (
      <View key={item.stateCode}>
        <TouchableOpacity
          onPress={() => {
            let selTaskStateCode = item.stateCode;
            this.setState({
              selTaskStateCode: selTaskStateCode,
            });
            let loadUrl = '/biz/course/task/list';
            let loadRequest = {
              currentPage: 1,
              pageSize: this.state.pageSize,
              checkOutUserId: this.state.selStaffId
                ? this.state.selStaffId
                : constants.loginUser.userId,
              courseTypeId:
                this.state.selcourseTypeId == 0
                  ? null
                  : this.state.selcourseTypeId,
              taskState: selTaskStateCode === 'all' ? null : selTaskStateCode,
              qryStartTime: this.state.qryStartTime,
              documentTypeList: this.state.documentTypeList,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
          }}>
          <View
            key={item.stateCode}
            style={[CommonStyle.tabItemViewStyle, {flexDirection: 'row'}]}>
            <Text
              style={[
                item.stateCode === this.state.selTaskStateCode
                  ? {
                      color: '#255BDA',
                      fontSize: 16,
                      fontWeight: '500',
                      textAlign: 'center',
                      paddingBottom: 12,
                      borderColor: '#255BDA',
                      borderBottomWidth: 2,
                      paddingLeft: 3,
                      paddingRight: 3,
                    }
                  : {
                      color: '#2B333F',
                      fontSize: 16,
                      fontWeight: '500',
                      textAlign: 'center',
                      paddingBottom: 12,
                    },
              ]}>
              {item.stateName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // 实习类型
  renderCourseTypeRow = (item) => {
    return (
      <TouchableOpacity
        onPress={() => {
          this.setState({
            selcourseTypeId: item.courseTypeId,
            selcourseTypeName: item.courseTypeName,
          });
        }}>
        <View
          key={'department_' + item.courseTypeId}
          style={[
            item.courseTypeId === this.state.selcourseTypeId
              ? CommonStyle.choseToSearchItemsSelectedViewColor
              : CommonStyle.choseToSearchItemsViewColor,
            CommonStyle.choseToSearchItemsViewSize,
          ]}>
          <Text
            style={[
              item.courseTypeId === this.state.selcourseTypeId
                ? CommonStyle.choseToSearchItemsSelectedTextStyle
                : CommonStyle.choseToSearchItemsTextStyle,
            ]}>
            {item.courseTypeName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  renderRow = (item, index) => {
    //计算最后学习时间
    item.lastStudyTime =
      item.portalTrackDetailDTOList &&
      item.portalTrackDetailDTOList[0] &&
      item.portalTrackDetailDTOList[0].gmtCreated
        ? item.portalTrackDetailDTOList[0].gmtCreated
        : item.gmtCreated;

    const state = item.taskState || 'default';
    const indexOfBgColor = (parseInt(index) + 1) % 6;
    const studyDays =
      item.taskState == '0CC'
        ? item.courseDurations
        : Math.abs(
            (
              (Date.now() - Date.parse(item.gmtCreated.substr(0, 10))) /
              (1000 * 3600 * 24)
            ).toFixed(0),
          );

    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('StudentCourseTrackDetail', {
            courseTaskId: item.courseTaskId,
            syCourseTaskDto: item,
            trackType: 'CT',
            operate: 'query',
            listTitleName: item.courseName,
          });
        }}>
        <View
          style={{
            flexDirection: 'row',
            height: 129,
            width: screenWidth - 13,
            // backgroundColor:"red"
          }}>
          <View
            style={{
              width: 130 + 28,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <View>
              {item.coursePhoto ? (
                <View>
                  <Image
                    source={{
                      uri: constants.image_addr + '/' + item.coursePhoto,
                    }}
                    style={{width: 130, height: 93, borderRadius: 10}}></Image>
                </View>
              ) : (
                <View>
                  <View
                    style={{
                      width: 130,
                      height: 93,
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 8,
                      borderRadius: 10,
                      backgroundColor: taskTitleBgColor[indexOfBgColor],
                    }}>
                    <Text style={{fontSize: 18, color: 'white'}}>
                      {item.courseName}
                    </Text>
                  </View>
                </View>
              )}
              <View
                style={{
                  position: 'absolute',
                  zIndex: 10,
                  borderTopLeftRadius: 10,
                  borderBottomRightRadius: 10,
                  backgroundColor: taskStateBgColor[state],
                }}>
                <Text style={commonStyles}>{taskStateText[state]}</Text>
              </View>
            </View>
          </View>

          <View
            style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
            <View style={{height: 93, width: '100%'}}>
              {/* 自定义组件 */}
              <ClassHeadScreen
                redTitle={item.courseLevelName}
                blackTitle={'任务' + item.courseSort + ' ' + item.courseName}
              />

              <View style={{height: 20, flexDirection: 'row'}}>
                <View
                  style={{
                    //外边距
                    borderRadius: 10,
                    backgroundColor: 'rgba(27,188,130,0.2)',
                    height: 20,
                    paddingLeft: 10,
                    paddingRight: 10,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Text style={{fontSize: 12, color: '#1BBC82'}}>
                    {item.courseTypeName}
                  </Text>
                </View>
              </View>

              <View
                style={{
                  height: 12,
                  marginTop: 8,
                }}>
                <ProgressBar
                  fillColor={'rgba(30, 110, 250, 1)'}
                  height={3}
                  progress={
                    studyDays / item.courseDurations >= 1
                      ? 1
                      : studyDays / item.courseDurations
                  }
                />
              </View>
              <View style={{height: 17, flexDirection: 'row'}}>
                <Text>{'已实习(天）：'}</Text>
                {item.taskState == '0AA' ? (
                  <Text>{studyDays}</Text>
                ) : item.taskState == '0BB' ? (
                  <Text style={{color: 'red'}}>{'超期'}</Text>
                ) : (
                  <Text>{item.courseDurations}</Text>
                )}
                <Text>{'/' + item.courseDurations}</Text>
              </View>
            </View>
          </View>
        </View>
        {/* 分隔线 */}
        <View style={styles.lineViewStyle} />
        {/* 流程 */}
        <View
          style={[
            styles.titleViewStyle,
            {marginLeft: 21, alignItems: 'center'},
          ]}>
          <Text style={styles.titleTextStyle}>流程</Text>
        </View>
        <View
          style={{
            flexDirection: 'row',
            marginLeft: 21,
            marginTop: 14,
            marginRight: 16,
            marginBottom: 30,
          }}>
          {item.checkOutUserPhoto ? (
            <Image
              source={{
                uri: constants.image_addr + '/' + item.checkOutUserPhoto,
              }}
              style={{height: 48, width: 48, borderRadius: 50}}
            />
          ) : (
            <ImageBackground
              source={require('../../../assets/icon/iconfont/profilePicture.png')}
              style={{width: 48, height: 48}}>
              <View
                style={{
                  height: 48,
                  width: 48,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                {item.checkOutUserName && item.checkOutUserName.length <= 2 ? (
                  <Text
                    style={{
                      color: '#FFFFFF',
                      fontSize: 17,
                      fontWeight: 'normal',
                      textAlign: 'center',
                      lineHeight: 22,
                    }}>
                    {item.checkOutUserName}
                  </Text>
                ) : (
                  <Text
                    style={{
                      color: '#FFFFFF',
                      fontSize: 17,
                      fontWeight: 'normal',
                      textAlign: 'center',
                      lineHeight: 22,
                    }}>
                    {item.checkOutUserName
                      ? item.checkOutUserName.slice(-2)
                      : ''}
                  </Text>
                )}
              </View>
            </ImageBackground>
          )}
          <View style={{marginLeft: 8, flexDirection: 'column'}}>
            <View style={{flexDirection: 'row', marginTop: 1}}>
              <Text
                style={{
                  fontSize: 16,
                  lineHeight: 20,
                  color: 'rgba(0, 10, 32, 0.85)',
                }}>
                开启实习
              </Text>
            </View>

            <View style={{flexDirection: 'row', marginTop: 4}}>
              <Text
                style={{
                  fontSize: 14,
                  lineHeight: 20,
                  color: 'rgba(0, 10, 32, 0.65)',
                }}>
                {item.checkOutUserName}
              </Text>
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              position: 'absolute',
              right: 0,
              top: 5,
            }}>
            <Text style={[{fontSize: 12, color: 'rgba(0,10,32,0.45)'}]}>
              {item.gmtCreated.slice(0, 16)}
            </Text>
          </View>
        </View>
        {item.taskState === '0CC' ? (
          <View
            style={{
              flexDirection: 'row',
              marginLeft: 21,
              marginRight: 16,
              marginBottom: 10,
            }}>
            {/* <Text>已完成</Text> */}
            {item.checkInUserPhoto ? (
              <Image
                source={{
                  uri: constants.image_addr + '/' + item.checkInUserPhoto,
                }}
                style={{height: 48, width: 48, borderRadius: 50}}
              />
            ) : (
              <ImageBackground
                source={require('../../../assets/icon/iconfont/profilePicture.png')}
                style={{width: 48, height: 48}}>
                <View
                  style={{
                    height: 48,
                    width: 48,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  {item.checkInUserName && item.checkInUserName.length <= 2 ? (
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: 17,
                        fontWeight: 'normal',
                        textAlign: 'center',
                        lineHeight: 22,
                      }}>
                      {item.checkInUserName}
                    </Text>
                  ) : (
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: 17,
                        fontWeight: 'normal',
                        textAlign: 'center',
                        lineHeight: 22,
                      }}>
                      {item.checkInUserName
                        ? item.checkInUserName.slice(-2)
                        : ''}
                    </Text>
                  )}
                </View>
              </ImageBackground>
            )}
            <View style={{marginLeft: 8, flexDirection: 'column', flex: 1}}>
              <Text
                style={{
                  fontSize: 16,
                  color: 'rgba(0,10,32,0.85)',
                  lineHeight: 20,
                }}>
                关闭人
              </Text>
              <View style={{flexDirection: 'row', marginTop: 1}}>
                <Text
                  style={{
                    fontSize: 14,
                    lineHeight: 20,
                    color: 'rgba(0, 10, 32, 0.65)',
                  }}>
                  {item.checkInUserName}（关闭任务）
                </Text>
              </View>
            </View>
            <View
              style={{
                flexDirection: 'row',
                position: 'absolute',
                right: 0,
                top: 5,
              }}>
              <Text style={[{fontSize: 12, color: 'rgba(0,10,32,0.45)'}]}>
                {item.gmtModified.slice(0, 16)}
              </Text>
            </View>
          </View>
        ) : (
          <View></View>
        )}

        <View style={{backgroundColor: '#F2F5FC', height: 10}}></View>
      </TouchableOpacity>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 22, height: 22}}
          source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          Alert.alert('确认', '您确定要导出PDF文件吗？', [
            {
              text: '取消',
              onPress: () => {
                WToast.show({data: '点击了取消'});
              },
            },
            {
              text: '确定',
              onPress: () => {
                WToast.show({data: '点击了确定'});
                this.exportPdfFile();
              },
            },
          ]);
        }}
        style={[{marginBottom: 1.5}]}>
        <Image
          style={{width: 23, height: 23}}
          source={require('../../../assets/icon/iconfont/newExport.png')}></Image>
      </TouchableOpacity>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/course_task';
    let requestParams = {
      currentPage: 1,
      pageSize: 1000,
      checkOutUserId: this.state.selStaffId,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  openQryStartDate() {
    this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate);
  }

  callBackSelectQryStartDateValue(value) {
    console.log('==========提交时间选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectedQryStartDate: value,
    });
    if (value && value.length) {
      var qryStartTime = '';
      var vartime;
      for (var index = 0; index < value.length; index++) {
        vartime = value[index];
        if (index === 0) {
          qryStartTime += vartime;
        } else {
          qryStartTime += '-' + vartime;
        }
      }
      this.setState({
        qryStartTime: qryStartTime,
      });
      let loadUrl = '/biz/course/task/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        checkOutUserId: this.state.selStaffId
          ? this.state.selStaffId
          : constants.loginUser.userId,
        courseTypeId:
          this.state.selcourseTypeId == 0 ? null : this.state.selcourseTypeId,
        taskState:
          this.state.selTaskStateCode === 'all'
            ? null
            : this.state.selTaskStateCode,
        qryStartTime: qryStartTime,
        documentTypeList: this.state.documentTypeList,
      };
      httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }
  }

  // 显示搜索项目
  showSearchItemSelect() {
    if (
      !this.state.courseTypeDataSource ||
      this.state.courseTypeDataSource.length < 1
    ) {
      WToast.show({data: '请先添加实习类型'});
      return;
    }
    this.setState({
      showSearchItemBlock: true,
    });
  }

  render() {
    return (
      <View>
        <CommonHeadScreen
          title={this.state.selStaffName + this.state.operate}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />

        {/* <View style={[CommonStyle.headViewStyle,{width:screenWidth,borderWidth:0,paddingRight:10}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row',justifyContent:'space-between' }}>
                        {
                            (this.state.taskStateDataSource && this.state.taskStateDataSource.length > 0)
                                ?
                                this.state.taskStateDataSource.map((item, index) => {
                                    return this.renderTaskStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                </View> */}

        <View>
          <View
            style={[
              CommonStyle.contentViewStyle,
              {
                height: ifIphoneXContentViewDynamicHeight(
                  this.state.topBlockLayoutHeight,
                ),
              },
            ]}>
            <FlatList
              data={this.state.dataSource}
              renderItem={({item, index}) => this.renderRow(item, index)}
              ListEmptyComponent={this.emptyComponent}
              // 自定义下拉刷新
              refreshControl={
                <RefreshControl
                  tintColor="#FF0000"
                  title="loading"
                  colors={['#FF0000', '#00FF00', '#0000FF']}
                  progressBackgroundColor="#FFFF00"
                  refreshing={this.state.refreshing}
                  onRefresh={() => {
                    this._loadFreshData();
                  }}
                />
              }
              // 底部加载
              ListFooterComponent={() => this.flatListFooterComponent()}
              onEndReached={() => this._loadNextData()}
            />
          </View>
        </View>
        <BottomScrollSelect
          ref={'SelectQryStartDate'}
          callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  titleViewStyle: {
    flexDirection: 'row',
    marginLeft: 12,
    marginRight: 16,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentTextStyle: {
    marginLeft: 12,
    marginRight: 16,
    marginTop: 3,
    lineHeight: 24,
  },
  itemContentStyle: {
    fontSize: 14,
    lineHeight: 24,
    textAlign: 'left',
    textAlignVertical: 'top',
    color: 'rgba(0, 10, 32, 0.65)',
  },
  lineViewStyle: {
    height: 1,
    marginLeft: 13,
    marginRight: 13,
    marginTop: 15,
    marginBottom: 6,
    borderBottomWidth: 0.5,
    borderColor: '#E8E9EC',
  },
});
