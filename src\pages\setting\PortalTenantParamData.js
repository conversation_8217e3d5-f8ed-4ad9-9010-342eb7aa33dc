import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image, TextInput,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ScrollView } from 'react-native-gesture-handler';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
import <PERSON>Viewer from 'react-native-image-zoom-viewer';
var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const {ifIphoneXContentViewHeight} = require('../../utils/ScreenUtil');
import EmptyPortalTenantComponent from '../../component/EmptyPortalTenantComponent';

const leftLabWidth = 300;
export default class PortalTenantParamData extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            pictureIndex:0,
            isShowImage:false,
            image:"",
            bannerConfigList:["RESUME_SHARE_WEBSITE_BANNER", "ENTERPRISE_SHARE_WEBSITE_BANNER", "MEMBER_SHARE_WEBSITE_BANNER"],

            imageConfigList:["WECHAT_SHARE_PAGE_BOTTOM_IMAGE","RESUME_SHARE_LOGO","ENTERPRISE_SHARE_LOGO","MEMBER_SHARE_LOGO","MEMBER_SHARE_CARD_LOGO",
            "MEMBER_SHARE_WEBSITE_NOTIFICATION_PICTURE","ALI_PAY_QR_CODE","WECHAT_PAY_QR_CODE","PAY_QR_CODE","APPLY_CHARGE_PERSON_WECHAT_QR_CODE"],
            
            textConfigList:["ADMISSION_TICKET_NOTICE","CHARGE_DESCRIPTION",
            "CONTACT_WEB_URL","WECHAT_SHARE_PAGE_TITLE","WECHAT_SHARE_PAGE_FINISHED_WORK_TITLE"
            ,"WECHAT_SHARE_PAGE_WORK_PLAN_TITLE","WECHAT_SHARE_PAGE_BOTTOM_LINK","RESUME_SHARE_TITLE","RESUME_SHARE_SUB_TITLE",
            "RESUME_SHARE_WEBSITE_TITLE","RESUME_SHARE_WEBSITE_NOTIFICATION","ENTERPRISE_SHARE_TITLE","ENTERPRISE_SHARE_SUB_TITLE",
            "ENTERPRISE_SHARE_WEBSITE_TITLE","ENTERPRISE_SHARE_WEBSITE_NOTIFICATION","MEMBER_SHARE_TITLE","MEMBER_SHARE_SUB_TITLE",
            "MEMBER_SHARE_WEBSITE_TITLE","MEMBER_SHARE_WEBSITE_NOTIFICATION_WORD","MEMBER_SHARE_WEBSITE_APPLY_NOTIFICATION_SUCCESS",
            "MEMBER_SHARE_WEBSITE_APPLY_NOTIFICATION_CONTACT","MEMBER_SHARE_WEBSITE_CONTACT_PLATFORM_LINK","MEMBER_SHARE_WEBSITE_NOTIFICATION_LINK"],
            
            noteConfigList:["MEMBER_CONTACT_US_IMAGE_AND_TEXT","MEMBER_CONTACT_US_LINK","MEMBER_CONTACT_PLATFORM_IMAGE_AND_TEXT","MEMBER_CONTACT_PLATFORM_LINK"],

            moreModal:false,
            deleteModal:false,
            editModal:false,
            paramCode:"",

            paramName:"",
            paramValue:"",
        }
    }

   

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { paramCode, paramName, paramValue} = route.params;
            this.setState({
                operate: "新增"
            })
            if (paramName) {
                this.setState({
                    paramName: paramName
                })
            }
            if (paramCode) {
                this.setState({
                    paramCode: paramCode,
                })   
            }
            if (paramValue) {
                this.setState({
                    paramValue: paramValue,
                    operate: "编辑"

                })       
            }

        }
    }

    //  // 回调函数
    //  callBackFunction=(paramCode)=>{
    //     let url= "/biz//list";
    //     let loadRequest={
    //         // "currentPage": 1,
    //         // "pageSize": this.state.pageSize,
    //         "paramCode": paramCode ? paramCode : this.state.paramCode,
    //     };
    //     httpPost(url, loadRequest, this._loadFreshDataCallBack);
    // }

    // loadPortalTenantConfigDataList=(paramCode)=>{
    //     let url= "/biz/tenant/config/paramNameByCodeList";
    //     let loadRequest={
    //         "paramCode": paramCode ? paramCode : this.state.paramCode,
    //     };
    //     console.log("========paramCode",paramCode);
    //     httpPost(url, loadRequest, this.loadPortalTenantConfigListCallBack);
    // }

    // loadPortalTenantConfigListCallBack=(response)=>{
    //     console.log("==================")
    //     if (response.code == 200 && response.data) {
    //         console.log("======",response.data);
    //         this.setState({
    //             dataSource:response.data,
    //             refreshing:false,
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // // 下拉触顶刷新到第一页
    // _loadFreshData=()=>{
    //     if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
    //         console.log("==========不刷新=====");
    //         return;
    //     }
    //     this.setState({
    //         currentPage:1
    //     })
    //     let url= "/biz/tenant/config/paramNameByCodeList";
    //     let loadRequest={
    //         "paramCode": paramCode ? paramCode : this.state.paramCode,
    //     };
    //     httpPost(url, loadRequest, this._loadFreshDataCallBack);
    // }

    // _loadFreshDataCallBack=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         var dataNew = response.data.dataList;
    //         // dataOld.unshift(dataNew);
    //         var dataAll = [...dataNew];
    //         this.setState({
    //             dataSource:dataAll,
    //             currentPage:response.data.currentPage + 1,
    //             totalPage:response.data.totalPage,
    //             totalRecord:response.data.totalRecord,
    //             refreshing:false
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadPortalTenantConfigDataList();
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={this.savePortalTenantParam.bind(this)
            }>
                <Text style={CommonStyle.headRightText}>保存</Text>
            </TouchableOpacity>
        )
    }

    savePortalTenantParam = () => {
        console.log("=======savePortalTenantParam");
        let toastOpts;

        if (!this.state.paramValue) {
            toastOpts = getFailToastOpts("请输入配置的值");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/tenant/config/add";
        if (this.state.operate === '编辑') {
            url = "/biz/tenant/config/modify";
        }
        let requestParams = {
            "paramCode": this.state.paramCode,
            "paramValue": this.state.paramValue,
            //"valueType": this.state.valueType
        };
        console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.savePortalTenantParamCallBack);
    }

    savePortalTenantParamCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('更新完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyPortalTenantComponent/>
    }

  

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.paramName}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                    {/* <View style={[styles.leftLabView,{marginTop:10}]}>
                        <Text style={styles.leftLabNameTextStyle}>{this.state.paramName}</Text>
                    </View> */}

                    {
                        this.state.textConfigList.includes(this.state.paramCode) ?
                            <View> 
                                <View style={[styles.inputRowStyle, { height: 200 }]}>
                                    <TextInput
                                        multiline={true}
                                        textAlignVertical="top"
                                        style={[CommonStyle.inputRowText, { height: 200 }]}
                                        placeholder={'请输入配置的值'}
                                        onChangeText={(text) => this.setState({ paramValue: text })}
                                    >
                                        {this.state.paramValue}
                                    </TextInput>
                                </View>
                            </View> :

                            <View style={{marginTop:10}}>
                                <View>
                                    {/* <Text style={styles.leftLabNameTextStyle}>配置图片</Text> */}
                                    <Text style={[styles.leftLabNameTextStyle,{marginLeft:10,width:150,fontSize:14,color:"red",marginBottom:15}]} >
                                    {
                                    (this.state.paramCode == "RESUME_SHARE_LOGO" || this.state.paramCode == "ENTERPRISE_SHARE_LOGO")  
                                    ?  "(推荐尺寸200x200px)" 
                                    :
                                    (
                                        [(this.state.bannerConfigList.includes(this.state.paramCode)) ? "(推荐尺寸750x160px)" : ""]
                                    )
                                    
                                    } 
                                    </Text>
                                </View>
                                {
                                    this.state.bannerConfigList.includes(this.state.paramCode) ? 
                                    <View style={[{
                                        width: screenWidth -20, height: 80, marginLeft: 10, marginBottom: 10, display: 'flex', justifyContent: 'center',
                                        alignItems: 'center'
                                    }, { borderColor: '#AAAAAA', borderWidth: 1, borderStyle: 'dashed', borderRadius: 5 }]}>
                                        
                                            <TouchableOpacity
                                            style={{ width: screenWidth -20, height: 80, alignItems: 'center', justifyContent: 'center' }}
                                            onPress={() => {
                                                console.log("1++" + this.state.paramCode);
                                                console.log("2++" + this.state.bannerConfigList.includes(this.state.paramCode));
                                                uploadImageLibrary(this.state.paramValueImageUrl,  "banner_image", (imageUploadResponse) => {
                                                    console.log("========imageUploadResponse", imageUploadResponse)
                                                    if (imageUploadResponse.code === 200) {

                                                        WToast.show({ data: "成功上传" });
                                                        let { compressFile } = imageUploadResponse.data
                                                        this.setState({
                                                            paramValue: compressFile,
                                                        })
                                                    }
                                                    else {
                                                        WToast.show({ data: imageUploadResponse.message });
                                                    }
                                                },  this.state.bannerConfigList.includes(this.state.paramCode));
                                            }}
                                        >
                                            {
                                                this.state.paramValue ?
                                                
                                                    <Image source={{ uri: (constants.image_addr + '/' + this.state.paramValue) }} style={{ width: screenWidth -20, height: 80, resizeMode: 'contain', justifyContent: 'center', alignItems: 'center' }} />
                                                    :
                                                    <Image source={require('../../assets/icon/iconfont/addPhoto.png')} style={{ width: 24, height: 24 }}></Image>
                                            }
                                        </TouchableOpacity>

                                    </View>
                                    :
                                    <View style={[{
                                        width: 120, height: 150, marginLeft: 10, marginBottom: 10, display: 'flex', justifyContent: 'center',
                                        alignItems: 'center'
                                    }, { borderColor: '#AAAAAA', borderWidth: 1, borderStyle: 'dashed', borderRadius: 5 }]}>
                                        
                                            <TouchableOpacity
                                            style={{ width: 120, height: 150, alignItems: 'center', justifyContent: 'center' }}
                                            onPress={() => {
                                                console.log("1++" + this.state.paramCode);
                                                console.log("2++" + this.state.bannerConfigList.includes(this.state.paramCode));
                                                uploadImageLibrary(this.state.paramValueImageUrl,  "banner_image", (imageUploadResponse) => {
                                                    console.log("========imageUploadResponse", imageUploadResponse)
                                                    if (imageUploadResponse.code === 200) {

                                                        WToast.show({ data: "成功上传" });
                                                        let { compressFile } = imageUploadResponse.data
                                                        this.setState({
                                                            paramValue: compressFile,
                                                        })
                                                    }
                                                    else {
                                                        WToast.show({ data: imageUploadResponse.message });
                                                    }
                                                },  this.state.bannerConfigList.includes(this.state.paramCode));
                                            }}
                                        >
                                            {
                                                this.state.paramValue ?
                                                
                                                    <Image source={{ uri: (constants.image_addr + '/' + this.state.paramValue) }} style={{ width: 120, height: 150, justifyContent: 'center', alignItems: 'center' }} />
                                                    :
                                                    <Image source={require('../../assets/icon/iconfont/addPhoto.png')} style={{ width: 24, height: 24 }}></Image>
                                            }
                                        </TouchableOpacity>

                                    </View>
                                }

                            </View>                           

                    }


                </ScrollView>
                
            </View>
        )
    }
}
const styles = StyleSheet.create({
    leftLabNameTextStyle: {
        fontSize: 24,
        fontWeight:'bold',
        color:"#787878"
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#000000'
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight: 30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 35,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }

});