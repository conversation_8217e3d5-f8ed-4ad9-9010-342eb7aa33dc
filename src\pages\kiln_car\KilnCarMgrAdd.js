import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class KilnCarMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            kilnCarId:"",
            kilnCarName:"",
            productionLineId: null,
            productionLineDataSource:[],
            sel:0
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { kilnCarId, productionLineId, operateTenantId } = route.params;
            if (productionLineId) {
                console.log("==========productionLineId:", productionLineId);
                this.setState({
                    productionLineId:productionLineId,
                    sel:1
                })
            }
            else {
                if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                    this.setState({
                        productionLineId:constants.loginUser.spUserExtDTO.productionLineId,
                    })
                }
                this.loadProductionLineList();
            }
            if (operateTenantId) {
                console.log("==========operateTenantId:", operateTenantId);
                this.setState({
                    operateTenantId:operateTenantId,
                })
            }
            if (kilnCarId) {
                console.log("========Edit==kilnCarId:", kilnCarId);
                this.setState({
                    kilnCarId:kilnCarId
                })
                loadTypeUrl= "/biz/kiln/car/get";
                loadRequest={'kilnCarId':kilnCarId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditKilnCarDataCallBack);
            }
        }
    }
    loadEditKilnCarDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                productionLineId:response.data.productionLineId,
                kilnCarId:response.data.kilnCarId,
                kilnCarName:response.data.kilnCarName,
            })
        }
    }

    loadProductionLineList=()=>{
        let url= "/biz/production/line/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 200,
        };
        httpPost(url, loadRequest, this.loadProductionLineListCallBack);
    }

    loadProductionLineListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                productionLineDataSource:response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("KilnCarMgrList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>窑车管理</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>窑车管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveKilnCar =()=> {
        console.log("=======saveKilnCar");
        let toastOpts;
        if (!this.state.kilnCarName) {
            toastOpts = getFailToastOpts("请输入窑车号");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/kiln/car/add";
        if (this.state.kilnCarId) {
            console.log("=========Edit===kilnCarId", this.state.kilnCarId)
            url= "/biz/kiln/car/modify";
        }
        let requestParams={
            "kilnCarId":this.state.kilnCarId,
            "kilnCarName":this.state.kilnCarName,
            "productionLineId":this.state.productionLineId,
            "operateTenantId":this.state.operateTenantId,
        };
        httpPost(url, requestParams, this.saveKilnCarCallBack);
    }
    
    // 保存回调函数
    saveKilnCarCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    renderProductLineRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                    productionLineId:item.productionLineId,
                }) 
            }}>
                <View key={item.productionLineId} style={[item.productionLineId === this.state.productionLineId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/2,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.productionLineId === this.state.productionLineId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.productionLineName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title='新增窑车'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                  
                        {
                            (this.state.sel == 0)?
                            <View>
                                <View style={CommonStyle.rowLabView}>
                                    <Text style={[styles.leftLabRedTextStyle,{marginLeft:9}]}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>生产车间</Text>
                                </View>
                                <View style={{ width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15 }}>
                                    {
                                        (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0)
                                            ?
                                            this.state.productionLineDataSource.map((item, index) => {
                                                return this.renderProductLineRow(item)
                                            })
                                            : <EmptyRowViewComponent />
                                    }
                                </View>
                            </View>
                            :<View/>
                        }
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>窑车号</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({kilnCarName:text})}
                        >
                            {this.state.kilnCarName}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0}} />
                    <View style={{height:ifIphoneXContentViewHeight()-122-155, backgroundColor:'#F2F5FC'}}>
                        {/* <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                        >
                        </TextInput> */}
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={styles.textContainerCancel} >
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={styles.textCancel}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveKilnCar.bind(this)}>
                            <View style={styles.textContainerCertain}>
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={styles.textCertain}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#FFFFFF',
        // borderWidth:1,
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
    },
    textCertain: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#FFFFFF',
        lineHeight: 24,
        marginTop:10,
        textAlign: 'center',
        // fontStyle: 'normal',
    },
    textCancel: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#404956',
        lineHeight: 24,
        marginTop:10,
        textAlign:'center'
        // fontStyle: 'normal',
    },
    textContainerCertain: {
        width: 180,
        height: 48,
        marginRight:8,
        backgroundColor: '#255BDA',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    textContainerCancel: {
        width: 180,
        height: 48,
        marginLeft:8,
        backgroundColor: '#FFFFFF',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        // paddingTop:5,
        // paddingBottom:5,
        marginTop:4,
        marginBottom:4,
        marginLeft:15, 
        // borderTopWidth:1,
        // borderTopColor:'#F1F1F1',
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:0,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'#E63633',
        marginLeft:4,
        marginRight:3
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:4,
        marginRight:3,
    },
})