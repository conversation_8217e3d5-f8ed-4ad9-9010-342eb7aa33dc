import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,
    FlatList,RefreshControl, Modal,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class WorkingShiftMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            moreModal:false,
            modalItem:{},
            deleteModal:false,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { shiftId } = route.params;
            if (shiftId) {
                console.log("=============shiftId" + shiftId + "");
            }
        }
        this.loadWorkingShiftList();
    }



        // 回调函数
        callBackFunction=()=>{
            let url= "/biz/working/shift/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    
        // 下拉触顶刷新到第一页
        _loadFreshData=()=>{
            if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
                console.log("==========不刷新=====");
                return;
            }
            this.setState({
                currentPage:1
            })
            let url= "/biz/working/shift/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    
        _loadFreshDataCallBack=(response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                var dataNew = response.data.dataList;
                // dataOld.unshift(dataNew);
                var dataAll = [...dataNew];
                this.setState({
                    dataSource:dataAll,
                    currentPage:response.data.currentPage + 1,
                    totalPage:response.data.totalPage,
                    totalRecord:response.data.totalRecord,
                    refreshing:false
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        }
    
        flatListFooterComponent=()=>{
            return(
                <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
            )
        }
    
        // 上拉触底加载下一页
        _loadNextData=()=>{
            if ((this.state.currentPage-1) >= this.state.totalPage) {
                WToast.show({data:"已经是最后一页了，我们也是有底线的"});
                return;
            }
            this.setState({
                refreshing:true
            })
            this.loadWorkingShiftList();
        }
    
        loadWorkingShiftList=()=>{
            let url= "/biz/working/shift/list";
            let loadRequest={
                "currentPage": this.state.currentPage,
                "pageSize": this.state.pageSize,
            };
            httpPost(url, loadRequest, this.loadWorkingShiftListCallBack);
        }
    
    
        loadWorkingShiftListCallBack=(response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                var dataNew = response.data.dataList;
                var dataOld = this.state.dataSource;
                // dataOld.unshift(dataNew);
                var dataAll = [...dataOld,...dataNew];
                this.setState({
                    dataSource:dataAll,
                    currentPage:response.data.currentPage + 1,
                    totalPage:response.data.totalPage,
                    totalRecord:response.data.totalRecord,
                    refreshing:false
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        }
    
        deleteWorkingShift =(shiftId)=> {
            console.log("=======delete=shiftId", shiftId);
            let url= "/biz/working/shift/delete";
            let requestParams={'shiftId':shiftId};
            httpDelete(url, requestParams, this.deleteCallBack);
        }
    
        // 删除操作的回调操作
        deleteCallBack=(response)=>{
            if (response.code == 200 && response.data) {
                WToast.show({data:"删除完成"});
                this.callBackFunction();
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({data:response.message});
            }
        }
    
        renderRow=(item, index)=>{
            return (
                <View key={item.shiftId} style={styles.innerViewStyle}>
                    {
                        index == 0 ?
                            <View style={{ width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF', borderBottomWidth: 10, borderBottomColor: '#F4F7F9' }}>
                            </View>
                            :
                            <View></View>
                    }
                    <View style={{ position:'absolute', right: 0, top: 0, marginRight:15}}>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                moreModal: true,
                                modalItem:item
                            })
                        }}>
                            <View style={[{width: 35, height: 35, flexDirection: 'column', justifyContent:'center', alignItems: 'center'}]}>
                                <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.titleViewStyleSpecial}>
                        <Text style={CommonStyle.titleTextStyleSpecial}>{item.shiftName}</Text>
                    </View>
                    <View style={CommonStyle.titleViewStyle}>
                        <Text style={CommonStyle.titleTextStyle}>生产车间：{item.productionLineName}</Text>
                    </View>
                    <View style={CommonStyle.titleViewStyle}>
                        <Text style={CommonStyle.titleTextStyle}>所属部门：{item.shiftTypeName}</Text>
                    </View>
                    <View style={CommonStyle.titleViewStyle}>
                        <Text style={CommonStyle.titleTextStyle}>排序：{item.shiftSort}</Text>
                    </View>
                    <View style={[CommonStyle.itemBottomBtnStyle,{marginRight:15}]}>
                        <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("WorkingShiftRelStaffMgr", 
                                {
                                    // 传递参数
                                    shiftId:item.shiftId,
                                    // 传递回调函数
                                    refresh: this.callBackFunction 
                                })
                            }}>
                            <View style={[styles.itemEditBtnViewStyle,{width:80,flexDirection:'row'}]}>
                                <Image  style={{width:20, height:20,marginRight:5 }} source={require('../../assets/icon/iconfont/shift.png')}></Image>
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>排班</Text>
                            </View>
                        </TouchableOpacity>
                        {/* <TouchableOpacity onPress={()=>{
                            Alert.alert('确认','您确定要删除该班次吗？',[
                                {
                                    text:"取消", onPress:()=>{
                                    WToast.show({data:'点击了取消'});
                                    // this在这里可用，传到方法里还有问题
                                    // this.props.navigation.goBack();
                                    }
                                },
                                {
                                    text:"确定", onPress:()=>{
                                        WToast.show({data:'点击了确定'});
                                        this.deleteWorkingShift(item.shiftId)
                                    }
                                }
                            ]);
                        }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:75,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                        </TouchableOpacity> */}
                        {/* <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("WorkingShiftMgrAdd", 
                                {
                                    // 传递参数
                                    shiftId:item.shiftId,
                                    productionLineId:item.productionLineId,
                                    shiftType:item.shiftType,
                                    // 传递回调函数
                                    refresh: this.callBackFunction 
                                })
                            }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:75,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                        </TouchableOpacity> */}
                    </View>
                </View>
            )
        }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("WorkingShiftMgrAdd", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            //     {/* <Text style={CommonStyle.headRightText}>新增班次</Text> */}
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("WorkingShiftMgrAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    });
                }}  >
                    <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='班次管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF'}}>
                {/* <View style={styles.searchTimeBoxWithExport11}>
                            <TouchableOpacity onPress={() => {}}>
                                <View style={{ alignItems: 'center', flexDirection: 'row', width: screenWidth / 1.9, borderRadius: 80}}>
                                    <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                    <Text style={{ color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 15 }}>
                                        搜索名称/型号/材质
                                    </Text>
                                </View>
                            </TouchableOpacity>
                </View> */}
                </View>
                <View style={CommonStyle.contentViewStyle}>
                        <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            ItemSeparatorComponent={this.space}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                            />
                </View>
                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                        this.setState({
                                            moreModal: false,
                                        })
                                    this.props.navigation.navigate("WorkingShiftMgrAdd",
                                        {
                                            // 传递参数
                                            shiftId: this.state.modalItem.shiftId,
                                            // 传递参数
                                            // shiftId:item.shiftId,
                                            productionLineId:this.state.modalItem.productionLineId,
                                            // productionLineId:item.productionLineId,
                                            shiftType: this.state.modalItem.shiftType,
                                            // shiftType:item.shiftType,
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                    }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}]}>
                                        {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false,
                                        deleteModal: true
                                    })
                                    }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}]}>
                                        {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该员工?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deleteWorkingShift(this.state.modalItem.shiftId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>


            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    itemEditBtnViewStyle:{
        fontSize: 16,
        width: 100,
        height: 30,
        borderWidth: 1,
        borderColor: '#255BDA',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 10,
        borderRadius: 6,
        backgroundColor:'#255BDA'
    },
    bodyViewStyleSpecial:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:40,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyViewStyleCommon:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:40,
        marginRight:10,
        marginBottom:0,
        marginTop:0
    },
    texRowSpecial: {
        width: 200,
        height: 24,
        // fontFamily: 'PingFangSC',
        fontWeight: 'bold',
        fontSize: 20,
        color: '#404956',
        lineHeight: 24,
        textAlign: 'left',
        fontStyle: 'normal',
      },
      texRowCommon: {
        width:220,
        height: 24,
        // fontFamily: 'PingFangSC',
        fontWeight: '400',
        fontSize: 14,
        color: 'rgba(0,10,32,0.65)',
        lineHeight: 24,
        textAlign: 'left',
        fontStyle: 'normal',
      },
});