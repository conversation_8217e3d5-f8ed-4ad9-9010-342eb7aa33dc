import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class ContractAcceptMoneyMgr extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 6,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      searchKeyWord: '',
      year: null,
      selYearsChooseName: '全部',
      topBlockLayoutHeight: 0,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    var currentDate = new Date();
    var year = currentDate.getFullYear();
    this.setState({
      year: year,
    });

    let yearsChooseDataSource = [
      {
        chooseCode: 'all',
        chooseName: '全部',
      },
      {
        chooseCode: 1,
        chooseName: year,
      },
      {
        chooseCode: 2,
        chooseName: year - 1,
      },
      {
        chooseCode: 3,
        chooseName: year - 2,
      },
      {
        chooseCode: 4,
        chooseName: year - 3,
      },
      // {
      //     chooseCode:5,
      //     chooseName:year-4,
      // }
    ];
    this.setState({
      yearsChooseDataSource: yearsChooseDataSource,
    });
    this.loadContractList();
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/contract/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      qryStartYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.state.selYearsChooseName,
      qryEndYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.addOneYear(this.state.selYearsChooseName),
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/contract/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      qryStartYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.state.selYearsChooseName,
      qryEndYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.addOneYear(this.state.selYearsChooseName),
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      let list = dataAll;
      let listNew = [];
      list.map((item, index) => {
        listNew.push(Object.assign({}, item, {display: 'N'}));
      });
      this.setState({
        dataSource: listNew,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadContractList();
  };

  loadContractList = () => {
    let url = '/biz/contract/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      qryStartYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.state.selYearsChooseName,
      qryEndYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.addOneYear(this.state.selYearsChooseName),
      partyB:
        constants.loginUser.tenantId === 66
          ? constants.loginUser.roleEnterpriseId
            ? constants.loginUser.roleEnterpriseId
            : null
          : null,
    };
    httpPost(url, loadRequest, this.loadContractListCallBack);
  };

  loadContractListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      let list = dataAll;
      let listNew = [];
      list.map((item, index) => {
        listNew.push(Object.assign({}, item, {display: 'N'}));
      });
      this.setState({
        dataSource: listNew,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  searchByKeyWord = () => {
    // let toastOpts;
    // if (!this.state.searchKeyWord) {
    //     toastOpts = getFailToastOpts("请输入客户、合同、签订时间");
    //     WToast.show(toastOpts)
    //     return;
    // }
    let loadUrl = '/biz/contract/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      qryStartYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.state.selYearsChooseName,
      qryEndYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.addOneYear(this.state.selYearsChooseName),
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  deleteContract = (contractId) => {
    console.log('=======delete=contractId', contractId);
    let url = '/biz/contract/delete';
    let requestParams = {contractId: contractId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  modifyContractState = (contractId, contractState) => {
    console.log('=======delete=contractId', contractId);
    let url = '/biz/contract/modify';
    let requestParams = {contractId: contractId, contractState: contractState};
    httpPost(url, requestParams, this.modifyContractStateCallBack);
  };

  // 修改状态操作的回调操作
  modifyContractStateCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '状态修改完成'});
      this._loadFreshData();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.contractId} style={styles.innerViewStyle}>
        {index == 0 ? (
          <View style={CommonStyle.lineListHeadRenderRowStyle}></View>
        ) : (
          <View></View>
        )}
        <View style={CommonStyle.titleViewStyleSpecial}>
          <Text
            style={[
              CommonStyle.titleTextStyleSpecial,
              {fontSize: 20},
              item.enterpriseName ? {width: screenWidth - 110} : null,
            ]}>
            {item.contractName}
          </Text>
          {/* <Text style={[styles.titleTextStyle, item.enterpriseName ? {width:screenWidth - 110} : null]}>合同名称：{item.contractName}</Text> */}
          {item.enterpriseName ? (
            <Text
              style={{
                paddingTop: 3,
                paddingBottom: 3,
                paddingLeft: 5,
                paddingRight: 5,
                height: 23,
                borderRadius: 12,
                backgroundColor: 'rgba(255,0,0,0.4)',
                color: '#FFFFFF',
              }}>
              {item.enterpriseAbbreviation
                ? item.enterpriseAbbreviation
                : item.enterpriseName}
            </Text>
          ) : null}
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            合同编号：{item.contractCode}
          </Text>
        </View>
        <View style={[CommonStyle.titleViewStyle]}>
          <Text style={CommonStyle.titleTextStyle}>
            合同金额：{item.contractAmount ? item.contractAmount : '无'}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            已开票金额：
            {item.actualInvoiceTotalAmount
              ? item.actualInvoiceTotalAmount
              : '无'}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            未开票金额：
            {item.contractAmount
              ? item.owedInvoiceTotalAmount
                ? item.owedInvoiceTotalAmount
                : '无'
              : '请添加合同金额'}
          </Text>
        </View>
        {item.deliveryDate ? (
          <View style={CommonStyle.titleViewStyle}>
            <Text style={CommonStyle.titleTextStyle}>
              交付日期：{item.deliveryDate}
            </Text>
          </View>
        ) : null}
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            签订时间：{item.signingTime}
          </Text>
        </View>
        <View style={[CommonStyle.newTitleViewStyle]}>
          <View>
            <Text
              style={[CommonStyle.newTitleTextStyle, {width: null}]}
              numberOfLines={2}>
              客户名称：
            </Text>
          </View>
          <View>
            <Text style={[CommonStyle.newTitleTextStyle]} numberOfLines={2}>
              {item.customerName}
            </Text>
          </View>
        </View>
        {/* <View style={CommonStyle.titleViewStyle}>
                    <Text style={[CommonStyle.titleTextStyle,{width:null}]}>客户名称：{item.customerName}</Text>
                </View> */}
        <View
          style={[
            CommonStyle.titleViewStyle,
            {justifyContent: 'flex-start', flexWrap: 'wrap'},
          ]}>
          <Text style={CommonStyle.titleTextStyle}>合同内容：</Text>
          {item.orderDTOList && item.orderDTOList.length > 0 ? (
            item.display === 'N' ? (
              <TouchableOpacity
                onPress={() => {
                  let list = this.state.dataSource;
                  list.map((elem, index) => {
                    if (elem.contractId == item.contractId) {
                      elem.display = 'Y';
                    }
                  });
                  this.setState({
                    dataSource: list,
                  });
                  // console.log("==============",list)
                }}>
                <Text style={[styles.titleTextStyle, {color: '#CB4139'}]}>
                  点击展开
                </Text>
              </TouchableOpacity>
            ) : (
              item.orderDTOList.map((elem, key) => {
                if (key + 1 === item.orderDTOList.length) {
                  return (
                    <TouchableOpacity
                      onPress={() => {
                        this.props.navigation.navigate('OrderStateTracking', {
                          // 传递参数
                          orderId: elem.orderId,
                          _orderItem: elem,
                          // 传递回调函数
                          refresh: this.callBackFunction,
                        });
                      }}>
                      {/* <Text style={[styles.titleTextStyle, { marginLeft: 5, marginBottom: 5, padding: 5, backgroundColor: (key % 2 == 0 ? 'rgba(255,0,0,0.2)' : 'rgba(0,255,0,0.2)') }]}>{elem.seriesName}-{elem.brickTypeName}</Text> */}
                      <Text
                        style={[
                          styles.titleTextStyle,
                          {
                            marginLeft: 5,
                            marginBottom: 5,
                            padding: 5,
                            color: '#404956',
                            backgroundColor:
                              key % 2 == 0 ? '#F2F5FC' : 'rgba(0,255,0,0.2)',
                          },
                        ]}>
                        {elem.seriesName}-{elem.brickTypeName}
                      </Text>
                    </TouchableOpacity>
                  );
                } else {
                  return (
                    <TouchableOpacity
                      onPress={() => {
                        this.props.navigation.navigate('OrderStateTracking', {
                          // 传递参数
                          orderId: elem.orderId,
                          _orderItem: elem,
                          // 传递回调函数
                          refresh: this.callBackFunction,
                        });
                      }}>
                      {/* <Text style={[styles.titleTextStyle, { marginLeft: (key == 0 ? -5 : 5), marginBottom: 5, padding: 5, backgroundColor: (key % 2 == 0 ? 'rgba(255,0,0,0.2)' : 'rgba(0,255,0,0.2)') }]}>{elem.seriesName}-{elem.brickTypeName}</Text> */}
                      <Text
                        style={[
                          styles.titleTextStyle,
                          {
                            marginLeft: key == 0 ? -5 : 5,
                            marginBottom: 5,
                            padding: 5,
                            color: '#404956',
                            backgroundColor:
                              key % 2 == 0 ? '#F2F5FC' : 'rgba(0,255,0,0.2)',
                          },
                        ]}>
                        {elem.seriesName}-{elem.brickTypeName}
                      </Text>
                    </TouchableOpacity>
                  );
                }
              })
            )
          ) : (
            <Text style={CommonStyle.titleTextStyle}>暂无内容</Text>
          )}
        </View>
        <View style={[styles.titleViewStyle, {justifyContent: 'center'}]}>
          {item.display === 'Y' ? (
            <TouchableOpacity
              onPress={() => {
                let list = this.state.dataSource;
                list.map((elem, index) => {
                  if (elem.contractId == item.contractId) {
                    elem.display = 'N';
                  }
                });
                this.setState({
                  dataSource: list,
                });
                // console.log("==============",list)
              }}>
              <Text
                style={[
                  styles.titleTextStyle,
                  {color: '#CB4139', textAlign: 'center'},
                ]}>
                点击收起
              </Text>
            </TouchableOpacity>
          ) : (
            <View />
          )}
        </View>

        <View style={[CommonStyle.itemBottomBtnStyle, {flexWrap: 'wrap'}]}>
          <TouchableOpacity
            onPress={() => {
              this.props.navigation.navigate('CollectMoneyPlanList', {
                contractId: item.contractId,
                contractAmount: item.actualAmount
                  ? item.actualAmount
                  : item.contractAmount,
              });
            }}>
            <View
              style={[
                CommonStyle.itemBottomDetailBtnViewStyle,
                {width: 105, flexDirection: 'row', backgroundColor: '#FFB800'},
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 5}}
                source={require('../../assets/icon/iconfont/planCollectMoney.png')}></Image>
              <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                收款计划
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              this.props.navigation.navigate('CollectMoneyActualList', {
                contractId: item.contractId,
                contractAmount: item.actualAmount
                  ? item.actualAmount
                  : item.contractAmount,
              });
            }}>
            <View
              style={[
                CommonStyle.itemBottomDetailBtnViewStyle,
                {
                  backgroundColor: '#5F9EA0',
                  width: 105,
                  flexDirection: 'row',
                  backgroundColor: '#FA353F',
                },
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 5}}
                source={require('../../assets/icon/iconfont/actualCollectMoney.png')}></Image>
              <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                实际收款
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
      //     <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons>
      //     <Text style={CommonStyle.headLeftText}>返回</Text>
      //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      // </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[{flexDirection: 'row', alignItems: 'center'}]}>
          {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
          {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={{color: '#3C6CDE', fontWeight: 'bold'}}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity onPress={() => {}}>
          {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
          <Text style={{color: '#FFFFFF'}}>合同汇款</Text>
          {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
        </TouchableOpacity>
      </View>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  addOneYear = (year) => {
    var date = new Date(year, 10, 24, 10, 33, 0, 0);
    var addYear = date.getFullYear() + 1;
    return addYear;
  };

  yearsChooseStateRow = (item, index) => {
    return (
      <View key={item.chooseCode}>
        <TouchableOpacity
          onPress={() => {
            var selYearsChooseName = item.chooseName;
            this.setState({
              selYearsChooseName: selYearsChooseName,
            });

            let loadUrl = '/biz/contract/list';
            let loadRequest = {
              currentPage: 1,
              pageSize: this.state.pageSize,
              qryStartYear:
                selYearsChooseName === '全部' ? null : selYearsChooseName,
              qryEndYear:
                selYearsChooseName === '全部'
                  ? null
                  : this.addOneYear(selYearsChooseName),
              searchKeyWord: this.state.searchKeyWord,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
          }}>
          <View
            key={item.chooseCode}
            style={[
              {
                width: screenWidth / 5,
                height: 49,
                flexDirection: 'row',
                justifyContent: 'center',
              },
              // ,item.stateCode === this.state.selCompletionStateCode ?
              //     [styles.selectedBlockItemViewStyle]
              //     :
              //     [styles.blockItemViewStyle],
            ]}>
            <Text
              style={[
                item.chooseName === this.state.selYearsChooseName
                  ? {
                      color: '#255BDA',
                      fontSize: 16,
                      fontWeight: '500',
                      lineHeight: 49,
                      textAlign: 'center',
                      borderColor: '#255BDA',
                      borderBottomWidth: 2,
                      paddingLeft: 5,
                      paddingRight: 5,
                    }
                  : {
                      color: '#2B333F',
                      fontSize: 16,
                      fontWeight: '500',
                      lineHeight: 49,
                      textAlign: 'center',
                    },
              ]}>
              {item.chooseName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="合同回款"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[
            CommonStyle.headViewStyle,
            {width: screenWidth, borderWidth: 0},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={{
              marginTop: 0,
              index: 1000,
              flexWrap: 'wrap',
              flexDirection: 'row',
            }}>
            {this.state.yearsChooseDataSource &&
            this.state.yearsChooseDataSource.length > 0 ? (
              this.state.yearsChooseDataSource.map((item, index) => {
                return this.yearsChooseStateRow(item);
              })
            ) : (
              <View />
            )}
          </View>
          <View
            style={[
              CommonStyle.headViewStyle,
              {borderLeftWidth: 0, borderRightWidth: 0},
            ]}
            onLayout={this.topBlockLayout.bind(this)}>
            <View style={CommonStyle.singleSearchBox}>
              <View style={CommonStyle.searchBoxWithoutOthers}>
                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                <Image
                  style={{width: 16, height: 16, marginLeft: 7}}
                  source={require('../../assets/icon/iconfont/search.png')}></Image>
                <TextInput
                  style={{
                    color: 'rgba(rgba(0, 10, 32, 0.45))',
                    fontSize: 14,
                    marginLeft: 5,
                    paddingTop: 0,
                    paddingBottom: 0,
                    paddingRight: 0,
                    paddingLeft: 0,
                    width: '100%',
                  }}
                  returnKeyType="search"
                  returnKeyLabel="搜索"
                  onSubmitEditing={(e) => {
                    this.searchByKeyWord();
                  }}
                  placeholder={'客户/合同/签订时间'}
                  onChangeText={(text) => this.setState({searchKeyWord: text})}>
                  {this.state.searchKeyWord}
                </TextInput>
              </View>
            </View>
          </View>
        </View>

        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> */}
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.contractId}
            ListEmptyComponent={this.emptyComponent}
            ItemSeparatorComponent={this.space}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
          {/* </ScrollView> */}
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
    marginTop: 5,
  },

  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
  },
  searchInputText: {
    width: screenWidth - 100,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },
  innerViewStyle: {
    // marginLeft:15,
    marginTop: 10,
    // borderColor: "#F4F4F4",
    // borderWidth: 8,
  },
  innerViewStyleSearch: {
    // marginTop: 10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
});
