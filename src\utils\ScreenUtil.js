import {
    Dimensions,Platform,
} from 'react-native';

var screenHeight = Dimensions.get('window').height;

export function isIphoneX() {
    const window = Dimensions.get('window');
    return (
        Platform.OS === 'ios' &&
        !Platform.isPad &&
        !Platform.isTVOS && [812, 844, 896, 926, 932].includes(window.height)
    )
}

// 判断是否有灵动岛:14Pro/14ProMax/15/
export function isHaveSpiritIsland(windowHeight) {
    // console.log("======isHaveSpiritIsland==windowHeight:", windowHeight)
    return (
        Platform.OS === 'ios' &&
        !Platform.isPad &&
        !Platform.isTVOS && [932,852].includes(windowHeight)
    )
}

export const iosTop = isIphoneX() ? 50 : 20

export const iosBottom = isIphoneX() ? 34 : 0

export const isLessKitKat = Platform.OS === 'android' && Platform.Version < 19


/**
 * 根据是否是iPhoneX返回不同的样式
 * @param iphoneXStyle
 * @param iosStyle
 * @param androidStyle
 * @returns {*}
 */

export function ifIphoneX(iphoneXStyle, iosStyle, androidStyle) {
    if (isIphoneX()) {
        return iphoneXStyle;
    } else if (Platform.OS === 'ios') {
        return iosStyle
    } else {
        if (androidStyle) return androidStyle;
        return iosStyle
    }
}
/***
 * 底部导航--根据是否是iPhoneX返回不同的样式
 * @param iphoneXStyleTabBarOptionHeight
 * @param iosStyleTabBarOptionHeight
 * @param androidStyleTabBarOptionHeight
 * @returns {*}
 */
export function ifIphoneXTabBarOptionHeight() {
    const window = Dimensions.get('window');
    if (isHaveSpiritIsland(window.height)) {
        return 85;
    }
    else if (isIphoneX()) {
        return 85;
    }
    else if (Platform.OS === 'ios') {
        return 62;
    }
    else if (Platform.OS === 'android') {
        return 65;
    }
    else {
        return 62
    }
}

/***
 * 头部--根据是否是iPhoneX返回不同的样式
 * @returns {*} body高度
 */
 export function ifIphoneXHeaderHeight(){
    const window = Dimensions.get('window');
    // iphone14ProMax
    if (isHaveSpiritIsland(window.height)) {
        return 90;
    }
    else if (isIphoneX()) {
        return 75;
    }
    else if (Platform.OS === 'ios') {
        return 50;
    }
    else if (Platform.OS === 'android') {
        return 45;
    }
    else {
        return 50;
    }
}

/***
 * body高度--根据是否是iPhoneX返回不同的样式
 * 只减去顶部的调试
 * @returns {*} body高度
 */
 export function ifIphoneXBodyViewHeight(){
    return screenHeight - ifIphoneXHeaderHeight() - 30;
}

export function fullScreenIfIphoneXContentViewHeight(){
    const window = Dimensions.get('window');
    if (isHaveSpiritIsland(window.height)) {
        return screenHeight - 175 + 62;
    }
    else if (isIphoneX()) {
        return screenHeight - 160 + 62;
    }
    else if (Platform.OS === 'ios') {
        return screenHeight - 110 + 62;
    }
    else if (Platform.OS === 'android') {
        return screenHeight - 126 + 100;
    }
    else {
        return screenHeight - 120 + 100
    }
}

/***
 * body高度--根据是否是iPhoneX返回不同的样式
 * @returns {*} body高度
 */
export function ifIphoneXContentViewHeight(){
    const window = Dimensions.get('window');
    if (isHaveSpiritIsland(window.height)) {
        return screenHeight - 175;
    }
    else if (isIphoneX()) {
        return screenHeight - 160;
    }
    else if (Platform.OS === 'ios') {
        return screenHeight - 110;
    }
    else if (Platform.OS === 'android') {
        return screenHeight - 126;
    }
    else {
        return screenHeight - 120
    }
}

/***
 *
 * 屏幕高-底部菜单调试
 * --根据是否是iPhoneX返回不同的样式
 * @returns {*} 除去底部菜单的高度
 */
 export function ifIphoneXScreenHeightMinusBottomBarHeight(){
    const window = Dimensions.get('window');
    if (isHaveSpiritIsland(window.height)) {
        return screenHeight - 100;
    }
    else if (isIphoneX()) {
        return screenHeight - 85;
    }
    else if (Platform.OS === 'ios') {
        return screenHeight - 62;
    }
    else if (Platform.OS === 'android') {
        return screenHeight - 65;
    }
    else {
        return screenHeight - 65
    }
}

/***
 * body高度--根据是否是iPhoneX返回不同的样式
 * @param dynamicBlockLayoutHeight 动态块高度
 * @returns {*} body高度
 */
export function ifIphoneXContentViewDynamicHeight(dynamicBlockLayoutHeight){
    const window = Dimensions.get('window');
    if (isHaveSpiritIsland(window.height)) {
        return screenHeight - (175 + dynamicBlockLayoutHeight);
    }
    else if (isIphoneX()) {
        return screenHeight - (160 + dynamicBlockLayoutHeight);
    }
    else if (Platform.OS === 'ios') {
        return screenHeight - (110 + dynamicBlockLayoutHeight);
    }
    else if (Platform.OS === 'android') {
        return screenHeight - (126 + dynamicBlockLayoutHeight);
    }
    else {
        return screenHeight - (120 + dynamicBlockLayoutHeight);
    }
}

/***
 * 耐材首页
 * body高度--根据是否是iPhoneX返回不同的样式
 * @param dynamicBlockLayoutHeight 动态块高度
 * @returns {*} body高度
 */
 export function naicaiIndexIfIphoneXContentViewDynamicHeight(dynamicBlockLayoutHeight){
    const window = Dimensions.get('window');
    if (isHaveSpiritIsland(window.height)) {
        return screenHeight - (175 + dynamicBlockLayoutHeight);
    }
    else if (isIphoneX()) {
        return screenHeight - (160 + dynamicBlockLayoutHeight);
    }
    else if (Platform.OS === 'ios') {
        return screenHeight - (135 + dynamicBlockLayoutHeight);
    }
    else if (Platform.OS === 'android') {
        return screenHeight - (135 + dynamicBlockLayoutHeight);
    }
    else {
        return screenHeight - (135 + dynamicBlockLayoutHeight);
    }
}
