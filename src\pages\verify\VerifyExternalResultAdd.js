import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,KeyboardAvoidingView,TouchableOpacity,Dimensions,Image,} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = (screenWidth - 40) / 2;

export default class VerifyExternalResultAdd extends Component {
    constructor(){
        super()
        this.state = {
            standardType:"E",
            operate:"",
            resultId:'',
            ordersDataSource:[],
            selBrickTypeId:0,
            selOrderId:0,
            selStandardId:0,
            spVerifyItemDTOList:[],
            selectVerifyDate:[],
            verifyDate:"",
            selectReportDate:[],
            reportDate:"",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('==VerifyResultAdd==componentWillMount');

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { resultId } = route.params;
            if (resultId) {
                console.log("========Edit==resultId:", resultId);
                this.setState({
                    operate:"编辑",
                    resultId:resultId
                })
            }
            else {
                this.setState({
                    operate:"新增",
                    resultDeclare:'1.检验报告无检验报告专用章无效。\n2.复制检验报告无效。\n3.检验报告涂改无效。\n4.对检验报告若有异议，应于收到检验报告起十五日内提出，逾期不予受理。\n5.本检验报告仅对来样负责。'
                })
            }
        }
        this.loadInitData();

        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectVerifyDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            verifyDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
            selectReportDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            reportDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
        })
    }
    
    componentWillUnmount(){
        console.log('==VerifyResultAdd==componentWillUnmount');
    }

    loadInitData=()=>{
        // 加载排产状态的订单，显示砖型
        let loadUrl= "/biz/verify/standard/eff_list";
        let loadRequest={
            "standardType": this.state.standardType,
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadEffVerifyResult);
    }

    // 订单回调加载
    callBackLoadEffVerifyResult=(response)=>{
        console.log("=====response=sssss=:", response);
        console.log("=====response.data.spVerifyItemDTOList=sssss=:", response.data.spVerifyItemDTOList);
        if (response.code == 200 && response.data) {
            if (response.data.length <= 0) {
                WToast.show({data:"没有中检验标准"});
                return;
            }
            this.setState({
                ordersDataSource:response.data,
                selBrickTypeId:response.data[0] ? response.data[0].brickTypeId : 0,
                selOrderId:response.data[0] ? response.data[0].orderId : 0,
                selStandardId:response.data[0] ? response.data[0].standardId : 0,
                spVerifyItemDTOList:response.data[0].spVerifyItemDTOList,
            })
            let loadUrl;
            let loadRequest;
            const { route, navigation } = this.props;
            if (route && route.params) {
                const { resultId } = route.params;
                if (resultId) {
                    console.log("========Edit==resultId:", resultId);
                    this.setState({
                        operate:"编辑",
                        resultId:resultId
                    })
                    loadUrl= "/biz/verify/result/get";
                    loadRequest={'resultId':resultId};
                    httpPost(loadUrl, loadRequest, this.loadVerifyResultDataCallBack);
                }
                else {
                    this.setState({
                        operate:"新增",
                    })
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadVerifyResultDataCallBack=(response)=>{
        console.log("=========loadVerifyResultDataCallBack===response:", response);
        if (response.code == 200 && response.data) {
            var selectVerifyDate = response.data.verifyDate.split("-");
            var selectReportDate = response.data.reportDate.split("-");
            this.setState({
                resultId:response.data.resultId,
                selBrickTypeId:response.data.brickTypeId,
                selOrderId:response.data.orderId,
                verifyDate:response.data.verifyDate,
                reportDate:response.data.reportDate,
                verifyAmount:response.data.verifyAmount,
                remark:response.data.remark,
                resultDeclare:response.data.resultDeclare,
                organization:response.data.organization,
                examine:response.data.examine,
                approval:response.data.approval,
                spVerifyItemDTOList:JSON.parse(response.data.resultValue),
                selectVerifyDate:selectVerifyDate,
                selectReportDate:selectReportDate,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
            //     {/*<Text style={CommonStyle.headLeftText}>返回</Text>*/}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("VerifyExternalResultList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>检验结果</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>检验结果</Text>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.resultId) {
                    return;
                }
                    this.setState({
                        selBrickTypeId:item.brickTypeId,
                        selOrderId:item.orderId,
                    })
                }}>
                    <View key={item.orderId} style={[this.state.resultId ? CommonStyle.disableViewStyle : '' , item.orderId === this.state.selOrderId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/2,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.orderId === this.state.selOrderId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    saveVerifyResult =()=> {
        console.log("=======saveVerifyResult");
        let toastOpts;
        if (!this.state.selBrickTypeId || this.state.selBrickTypeId === 0) {
            toastOpts = getFailToastOpts("请选择要入库的砖型");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selOrderId || this.state.selOrderId === 0) {
            toastOpts = getFailToastOpts("请选择要入库的砖型.");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.verifyAmount || this.state.verifyAmount === 0) {
            toastOpts = getFailToastOpts("请输入试样数量.");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/verify/result/add";
        if (this.state.resultId) {
            console.log("=========Edit===resultId", this.state.resultId)
            url= "/biz/verify/result/modify";
        }
        // 遍历取检验要求的值
        var _spVerifyItemDTOList = [];
        var _spVerifyItemDTO;
        this.state.spVerifyItemDTOList.forEach((item)=>{
            _spVerifyItemDTO = {
                "itemId":item.itemId,
                "itemName":item.itemName,
                "itemRefValue":item.itemRefValue,
                "itemResultValue":item.itemResultValue
            }
            _spVerifyItemDTOList.push(_spVerifyItemDTO);
        });


        let requestParams={
            "standardType": this.state.standardType,
            "resultId":this.state.resultId,
            "standardId":this.state.selStandardId,
            "brickTypeId":this.state.selBrickTypeId,
            "orderId": this.state.selOrderId,
            "verifyDate":this.state.verifyDate,
            "reportDate":this.state.reportDate,
            "verifyAmount":this.state.verifyAmount,
            "resultValue":JSON.stringify(_spVerifyItemDTOList),
            "remark":this.state.remark,
            "resultDeclare":this.state.resultDeclare,
            "organization":this.state.organization,
            "examine":this.state.examine,
            "approval":this.state.approval,
            // "resultValue":
            // "spVerifyItemDTOList":this.state.spVerifyItemDTOList,
        };
        httpPost(url, requestParams, this.saveVerifyResultCallBack);
    }
    
    // 保存回调函数
    saveVerifyResultCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 检验要求
    renderVerifyResultItemRow=(item, index)=>{
        return (
            <View>
            <View style={styles.inputRowStyle}>
                <View style={styles.leftLabView}>
                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>{item.itemName}</Text>
                </View>
                <TextInput 
                    style={[styles.inputRightText]}
                    placeholder={'请输入' }
                    // placeholder={'请输入' + item.itemName }
                    onChangeText={(text) => {
                        item.itemResultValue = text
                    }}
                >
                    {item.itemResultValue}
                </TextInput>
            </View>
                <View style={CommonStyle.lineBorderBottomStyle} />
            </View>


        )
    }
    openVerifyDate(){
        this.refs.SelectVerifyDate.showDate(this.state.selectVerifyDate)
    }
    openReportDate(){
        this.refs.SelectReportDate.showDate(this.state.selectReportDate)
    }
    callBackSelectVerifyDateValue(value){
        console.log("==========检验日期选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectVerifyDate:value
        })
        if (this.state.selectVerifyDate && this.state.selectVerifyDate.length) {
            var verifyDate = "";
            var vartime;
            for(var index=0;index<this.state.selectVerifyDate.length;index++) {
                vartime = this.state.selectVerifyDate[index];
                if (index===0) {
                    verifyDate += vartime;
                }
                else{
                    verifyDate += "-" + vartime;
                }
            }
            this.setState({
                verifyDate:verifyDate
            })
        }
    }
    callBackSelectReportDateValue(value){
        console.log("==========报告日期选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectReportDate:value
        })
        if (this.state.selectReportDate && this.state.selectReportDate.length) {
            var reportDate = "";
            var vartime;
            for(var index=0;index<this.state.selectReportDate.length;index++) {
                vartime = this.state.selectReportDate[index];
                if (index===0) {
                    reportDate += vartime;
                }
                else{
                    reportDate += "-" + vartime;
                }
            }
            this.setState({
                reportDate:reportDate
            })
        }
    }

    render(){
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '结果'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    
                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>订单名称</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    
                    <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                        {
                            (this.state.ordersDataSource && this.state.ordersDataSource.length > 0) 
                            ? 
                            this.state.ordersDataSource.map((item, index)=>{
                                return this.renderRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>检验日期</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={()=>this.openVerifyDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 5), borderWidth:0}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {this.state.verifyDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>试样数量</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({verifyAmount:text})}
                        >
                            {this.state.verifyAmount}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>报告日期</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TouchableOpacity onPress={()=>this.openReportDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 5), borderWidth:0}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {this.state.reportDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={[styles.rowLabView,{backgroundColor:'#F5F5F5', justifyContent:'center', paddingLeft:0}]}>
                        <Text style={[styles.leftLabNameTextStyle,{fontWeight:'bold'}]}>检验结果</Text>
                    </View>

                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.spVerifyItemDTOList && this.state.spVerifyItemDTOList.length > 0) 
                            ? 
                            this.state.spVerifyItemDTOList.map((item, index)=>{
                                return this.renderVerifyResultItemRow(item, index)
                            })
                            : <EmptyRowViewComponent title="请新增检验要求"/> 
                        }
                    </View>
                    <View style={[styles.rowLabView,{backgroundColor:'#F5F5F5', justifyContent:'center', paddingLeft:0}]}>
                        <Text style={[styles.leftLabNameTextStyle,{fontWeight:'bold'}]}>其它</Text>
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <View style={[styles.leftLabView,{ height:100, textAlignVertical:'center' }]}>
                            <Text style={[styles.leftLabWhiteTextStyle, {textAlignVertical:'center'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>备注</Text>
                        </View>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[styles.inputRightText,{height:100, textAlignVertical:'center'}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({remark:text})}
                        >
                            {this.state.remark}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <View style={[styles.leftLabView,{ height:100, textAlignVertical:'center' }]}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={[styles.leftLabNameTextStyle]}>声明</Text>
                        </View>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[styles.inputRightText,{height:90}]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({resultDeclare:text})}
                        >
                            {this.state.resultDeclare}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>编制</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({organization:text})}
                        >
                            {this.state.organization}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>审核</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({examine:text})}
                        >
                            {this.state.examine}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>批准</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({approval:text})}
                        >
                            {this.state.approval}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    
                    <View style={[CommonStyle.blockAddCancelSaveStyle]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveVerifyResult.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect 
                        ref={'SelectVerifyDate'} 
                        callBackDateValue={this.callBackSelectVerifyDateValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectReportDate'} 
                        callBackDateValue={this.callBackSelectReportDateValue.bind(this)}
                    />
                    
                </ScrollView>
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:5,
        marginRight:5,
    },
    inputLeftText:{
        width: (leftLabWidth - 35),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})