import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,ScrollView,Image,
    FlatList,RefreshControl,TextInput,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import ImageViewer from 'react-native-image-zoom-viewer';
var CommonStyle = require('../../assets/css/CommonStyle');
import { saveImage } from '../../utils/CameraRollUtils';

const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class MaterialInventoryAudit extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            parentClassifyName:"",
            classifyName:"",
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            fillDataList:[],
            selReplyId:1,
            replyDataSource:[
                {
                    replyId:1,
                    replyType:"同意",
                    replyName:"Y"
                },
                {
                    replyId:2,
                    replyType:"驳回",
                    replyName:"N"
                }
            ],
            selReplyName:"Y",
            recordId:"",
            nodeId:"",
            auditUserId:"",
            auditOpinion:"",
            inventoryId:"",
            auditTypeCode:"",
            locationName:"",
            locationAreaName:"",
            weight:"",
            seriesName:"",
            // materialImage:"",
            // materialImageUrl:"",
            pictureIndex:0,
            urls:[],
            isShowImage: false,
            compressFileList:[],
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');        
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId,recordId,inventoryId } = route.params;
            if (recordId) {
                let url= "/biz/audit/node/record/get";
                let loadRequest={
                    "recordId": recordId,
                };
                httpPost(url, loadRequest, this.loadAuditRecordCallBack);
            }

            // if (inventoryId) {
            //     httpPost("/biz/material/inventory/get", { "inventoryId": inventoryId }, (response) => {
            //         if (response.code === 200 && response.data) {
            //             var urls = [];
            //             let materialImage = response.data.materialImage;
            //             console.log("inventoryId +++++++",inventoryId);
            //             console.log("materialImage +++++++",materialImage)
            //             var url = {
            //                 url:constants.image_addr + '/' + materialImage
            //             }
            //             urls=urls.concat(url);
            //             this.setState({
            //                 materialImage : response.data.materialImage,
            //                 materialImageUrl: constants.image_addr + '/' + materialImage,
            //                 urls:urls,
            //             })
            //             console.log(url);
            //         }
            //     });
            // }
            
        }
        
    }

    loadAuditRecordCallBack=(response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                parentClassifyName:response.data.materialInventoryList[0].parentClassifyName,
                classifyName:response.data.materialInventoryList[0].classifyName,
                weight:response.data.materialInventoryList[0].weight,
                fillDataList:response.data.fillDataList,
                recordId:response.data.recordId,
                nodeId:response.data.nodeId,
                auditUserId:response.data.auditUserId,
                inventoryId:response.data.materialInventoryList[0].inventoryId,
                auditTypeCode:response.data.auditTypeCode,
                seriesName:response.data.materialInventoryList[0].seriesName,
                compressFileList:response.data.materialInventoryList[0].compressFileList,
                locationName:response.data.materialInventoryList[0].locationName,
                locationAreaName:response.data.materialInventoryList[0].locationAreaName,
            })

            var urls = [];
            if(response.data.materialInventoryList[0].compressFileList && response.data.materialInventoryList[0].compressFileList.length > 0){
                for(var i=0;i<response.data.materialInventoryList[0].compressFileList.length;i++){
                    var url = {
                        url:constants.image_addr + '/' +  response.data.materialInventoryList[0].compressFileList[i].compressFile
                    } 
                    urls=urls.concat(url)
                    console.log(url)
                }
            }
            this.setState({
                urls:urls
            })

        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    saveAudit = () => {
        console.log("=======saveAuditConfig");
        let toastOpts;
        let url = "/biz/audit/node/record/inventoryAuditAdd";
        let requestParams = {
            "inventoryId":this.state.inventoryId,

            "recordId":this.state.recordId,
            "nodeId":this.state.nodeId,
            "auditUserId":this.state.auditUserId,
            "auditTypeCode":this.state.auditTypeCode,

            "auditResult":this.state.selReplyName,
            "auditOpinion":this.state.auditOpinion,

        };
        httpPost(url, requestParams, this.saveAuditCallBack);
    }

    saveAuditCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    //sex列表展示
    renderReplyRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selReplyId:item.replyId,
                        selReplyName:item.replyName,
                    })
                }}>
                <View key={item.replyId} style={[item.replyId===this.state.selReplyId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.replyId===this.state.selReplyId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.replyType}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }



    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='审核'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>原料类别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入原料类别'}
                            onChangeText={(text) => this.setState({parentClassifyName:text})}
                        >
                            {this.state.parentClassifyName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>原料名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入原料名称'}
                            onChangeText={(text) => this.setState({classifyName:text})}
                        >
                            {this.state.classifyName}
                        </TextInput>
                    </View>

                    {
                        this.state.seriesName ? 
                        <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>需求产品</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入需求产品'}
                            onChangeText={(text) => this.setState({seriesName:text})}
                        >
                            {this.state.seriesName}
                        </TextInput>
                    </View>
                    : <View />
                    }
                    
                    {
                        ("MATERIAL_INVENTORY_I" === this.state.auditTypeCode) ?
                        <View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>库区</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <TextInput
                                    editable={false} 
                                    style={styles.inputRightText}
                                >
                                    {this.state.locationAreaName}
                                </TextInput>
                            </View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>库位</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <TextInput
                                    editable={false} 
                                    style={styles.inputRightText}
                                >
                                    {this.state.locationName}
                                </TextInput>
                            </View>
                        </View>
                        :
                        null
                    }


                    {
                        this.state.compressFileList && this.state.compressFileList.length > 0 ?
                        (
                        <View>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>附件</Text>
                        </View>
                        <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                        {
                            this.state.compressFileList.map((item,index) =>{
                                return(
                                    <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex'}]}>

                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            isShowImage:true,
                                            pictureIndex:index
                                        })
                                        // uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                        //     console.log("========imageUploadResponse", imageUploadResponse)
                                        //     if (imageUploadResponse.code === 200) {
                                        //         WToast.show({ data: "上传成功" });
                                        //         let compressFileList = imageUploadResponse.data
                                        //         this.setState({
                                        //             compressFileList: compressFileList
                                        //         })
                                        //     }
                                        //     else {
                                        //         WToast.show({ data: imageUploadResponse.message });
                                        //     }
                                        // });

                                    }}>
                                        <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />                                                    
                                                
                                    </TouchableOpacity>
                                    <Modal visible={this.state.isShowImage} transparent={true}>
                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} index={this.state.pictureIndex} 
                                            enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}  
                                            onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} 
                                            onSave={()=>{
                                                saveImage( this.state.urls[this.state.pictureIndex].url)
                                            }}/>
                                    </Modal>
                                </View>
                                )
                                
                            })
                        }
                    </View>
                    </View>
                        ) :
                        <View/>
                    }

                    {/* {
                        this.state.materialImage ? 
                        
                        <View>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>附件</Text>
                            </View>
                            <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                    <View>   
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            isShowImage:true,
                                        })
                                    }}>
                                    <Image source={{ uri: this.state.materialImageUrl }} style={{width:120,height:150,justifyContent:'center',alignItems:'center'}} />                                                    
                                    </TouchableOpacity>
                                    <Modal visible={this.state.isShowImage} transparent={true}>
                                        <ImageViewer enableSwipeDown={false} menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} onSave={() => alert("点击了保存图片")} 
                                        onClick={() => { // 图片单击事件
                                            this.setState({
                                                isShowImage:false
                                            })
                                        }}
                                        imageUrls={this.state.urls} />
                                    </Modal>
                                    </View>
                            </View>
                        </View> :
                        <View/>

                    } */}
                    

                    <View style={styles.inputRowStyle}>
                        <View style={[styles.rowLabView,{marginRight:30}]}>
                            <Text style={styles.leftLabNameTextStyle}>审核批复</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row',marginLeft:0}}>
                            {
                                (this.state.replyDataSource && this.state.replyDataSource.length > 0) 
                                ? 
                                this.state.replyDataSource.map((item, index)=>{
                                    return this.renderReplyRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>

                    

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>审核意见</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:150}]}
                            placeholder={'请输入审核意见'}
                            onChangeText={(text) => this.setState({auditOpinion:text})}
                        >
                            {this.state.auditOpinion}
                        </TextInput>
                    </View>

                    

                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveAudit.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                </ScrollView>

                </View>
        )
    }
}
const styles = StyleSheet.create({

    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:18
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputTextStyleTextStyle:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        height:45,
        justifyContent:'center'
    }

});