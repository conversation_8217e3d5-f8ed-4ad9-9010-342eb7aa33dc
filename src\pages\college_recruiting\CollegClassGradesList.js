import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class CollegClassGradesList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            professionalId:null,
        }
    }


    UNSAFE_componentWillMount(){
        const { route, navigation,professionalId } = this.props;
        if (route && route.params) {
            const { professionalId } = route.params;
            if (professionalId) {
                console.log("=============professionalId" + professionalId + "");
                this.setState({
                    professionalId:professionalId,
                })
                this.loadClassList(professionalId);
            }
            else {
                this.loadClassList();
            }
        }
        else {
            this.loadClassList();
        }
    }
 //下拉视图开始刷新时调用
 _onRefresh() {

    if (this.state.refreshing === false) {
        this._updateState('正在刷新......', true);
        //5秒后结束刷新
        setTimeout( ()=>{
            this._updateState('结束状态', false)
        }, 2000)
    }
}

//更新State
_updateState(message, refresh){
    this.setState({text:message,refreshing: refresh});
}


// 回调函数
callBackFunction=()=>{
    let url= "/biz/college/class/grades/list";
    let loadRequest={
        "currentPage": 1,
        "pageSize": this.state.pageSize,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
}
// 下拉触顶刷新到第一页
_loadFreshData=()=>{
    if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
        return;
    }
    this.setState({
        currentPage:1
    })
    let url= "/biz/college/class/grades/list";
    let loadRequest={
        "currentPage": 1,
        "pageSize": this.state.pageSize,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
}

_loadFreshDataCallBack=(response)=>{
    if (response.code == 200 && response.data && response.data.dataList) {
        var dataNew = response.data.dataList;
        // dataOld.unshift(dataNew);
        var dataAll = [...dataNew];
        this.setState({
            dataSource:dataAll,
            currentPage:response.data.currentPage + 1,
            totalPage:response.data.totalPage,
            totalRecord:response.data.totalRecord,
            refreshing:false
        })
    }
    else if (response.code == 401) {
        WToast.show({data:response.message});
        this.props.navigation.navigate("LoginView");
    }
}

flatListFooterComponent = () => {
    return (
        <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
    )
}
// 上拉触底加载下一页
_loadNextData=()=>{
    if ((this.state.currentPage-1) >= this.state.totalPage) {
        WToast.show({data:"已经是最后一页了，我们也是有底线的"});
        return;
    }
    this.setState({
        refreshing:true
    })
    this.loadClassList();
}

loadClassList=(professionalId)=>{
    let url= "/biz/college/class/grades/list";
    let loadRequest={
        "currentPage": this.state.currentPage,
        "pageSize": this.state.pageSize,
        "professionalId":professionalId ? professionalId : this.state.professionalId
    };
    httpPost(url, loadRequest, this.callBackLoadClassList);
}

callBackLoadClassList=(response)=>{
    if (response.code == 200 && response.data && response.data.dataList) {
        var dataNew = response.data.dataList;
        var dataOld = this.state.dataSource;
        // dataOld.unshift(dataNew);
        var dataAll = [...dataOld,...dataNew];
        this.setState({
            dataSource:dataAll,
            currentPage:response.data.currentPage + 1,
            totalPage:response.data.totalPage,
            totalRecord:response.data.totalRecord,
            refreshing:false
        })
    }
    else if (response.code == 401) {
        WToast.show({data:response.message});
        this.props.navigation.navigate("LoginView");
    }
}

deleteClass =(classId)=> {
    console.log("=======delete=classId", classId);
    let url= "/biz/college/class/grades/delete";
    let requestParams={'classId':classId};
    httpDelete(url, requestParams, this.deleteCallBack);
}

// 删除操作的回调操作
deleteCallBack=(response)=>{
    if (response.code == 200 && response.data) {
        WToast.show({data:"成功删除"});
        this.callBackFunction();
    }
    else if (response.code == 401) {
        WToast.show({data:response.message});
        this.props.navigation.navigate("LoginView");
    }
    else {
        WToast.show({data:response.message});
    }
}
renderRow=(item,index)=>{
    return (
        <View key={item.classId} style={styles.innerViewStyle}>
            <View style={styles.titleViewStyle}>
                <Text style={styles.titleTextStyle}>班级名称：{item.className}</Text>
            </View>
            <View style={styles.titleViewStyle}>
                <Text style={styles.titleTextStyle}>所属角色：{item.roleName}</Text>
            </View>
            <View style={styles.titleViewStyle}>
                <Text style={styles.titleTextStyle}>所属专业：{item.professionalName}</Text>
            </View>
            <View style={styles.titleViewStyle}>
                <Text style={styles.titleTextStyle}>学历：{item.educationName}</Text>
            </View>   
            <View style={styles.titleViewStyle}>
                <Text style={styles.titleTextStyle}>排序：{item.classSort}</Text>
            </View>
            <View style={CommonStyle.itemBottomBtnStyle}>
                <TouchableOpacity onPress={()=>{
                        console.log('===loadUserPWD:', item.classId);
                        let loadTypeUrl= "/biz/college/class/grades/send_class_pwd";
                        let loadRequest={tenantId: constants.loginUser.tenantId, classId:item.classId};
                        httpPost(loadTypeUrl, loadRequest, (response)=>{
                            if (response.code == 200 && response.data) {
                                WToast.show({data:"发送成功"});
                            }
                            else {
                                WToast.show({data:response.message});
                            }
                        });
                    }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { height:28,width:105,backgroundColor:"#5DD421",flexDirection:"row"}]}>
                            <Image style={{width:16, height:16,marginRight:5}} source={require('../../assets/icon/iconfont/sendPwd.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>发送密码</Text>
                        </View>
                </TouchableOpacity>

                <TouchableOpacity onPress={()=>{
                    this.props.navigation.navigate("CollegStudentList", 
                    {
                        // 传递回调函数
                        classId: item.classId,
                        refresh: this.callBackFunction,
                    })
                }}>
                    <View style={[{
                        width: 65,
                        height: 28,
                        flexDirection: "row",
                        justifyContent: 'center',
                        alignItems: 'center',
                        margin: 10,
                        marginRight: 0,
                        borderColor: 'rgba(27, 188, 130, 1)',
                        borderWidth: 0.85,
                        borderRadius: 6
                    }]}>
                        {/* <Image style={{ width: 24, height: 24, marginRight: 2, marginLeft: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                        <Text style={[{ color: 'rgba(27, 188, 130, 1)', fontSize: 14, lineHeight: 20 }]}>学生</Text>
                    </View>
                </TouchableOpacity>

                <TouchableOpacity onPress={()=>{
                    Alert.alert('确认','您确定要删除该班级吗？',[
                        {
                            text:"取消", onPress:()=>{
                            WToast.show({data:'点击了取消'});
                            }
                        },
                        {
                            text:"确定", onPress:()=>{
                                WToast.show({data:'点击了确定'});
                                this.deleteClass(item.classId)
                            }
                        }
                    ]);
                }}>
                    <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle]}>
                        <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                        <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={()=>this.props.navigation.navigate("CollegClassGradesAdd",
                {
                    classId:item.classId,
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })}>
                    <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle,{marginRight:10}]}>
                        <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                        <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20}}>编辑</Text>
                    </View>
                </TouchableOpacity>
                
            </View>
        </View>
    )
}
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/*< Text style={CommonStyle.headLeftText}>返回</Text>*/}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("CollegClassGradesAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title='班级管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
     // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
    },

    leftLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0
    },
    innerViewStyle: {
        // marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
})

