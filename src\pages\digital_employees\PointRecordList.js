import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Clipboard,
    FlatList,RefreshControl,Image,Modal ,TextInput,Linking,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class PointRecordList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,

            showSearchItemBlock:false,
            departmentDataSource:null,
            selDepartmentId:null,
            selDepartmentName:null,
            showClassSearchItemBlock:false,
            classDataSource:null,
            selClassId:null,
            selClassName:null,
            selDepartmentStaffDataSource:null,
            selStaffId:null,
            selStaffName:null,
            selClassUserDataSource:null,
            selUserId:null,
            selUserName:null,
            showPointClassSearchItemBlock:false,
            pointClassDataSource:null,
            selPointClassId:null,
            selPointClassName:null,

            qryStartTime:null,
            selectedQryStartDate:[]
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    initqryStartTime=()=>{
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth()-1);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _qryStartTime = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectedQryStartDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            qryStartTime:_qryStartTime
        })
        return _qryStartTime;
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        var _qryStartTime = this.initqryStartTime();
        let loadTypeUrl= "/biz/department/list_for_tenant";
        let loadRequest={"qryAll_NoPower":"Y", "currentPage": 1, "pageSize": 1000};
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    departmentDataSource:response.data,
                })
            }
        });

        let loadClassTypeUrl= "/biz/point/class/list";
        let loadClassRequest={"currentPage": 1, "pageSize": 1000};
        httpPost(loadClassTypeUrl, loadClassRequest, (response)=>{
            if (response.code == 200 && response.data) {
                var pointClassData = response.data;
                pointClassData.unshift({"pointClassId":0,"pointClassName":"全部"})
                this.setState({
                    pointClassDataSource:pointClassData,
                })
            }
        });
        

        let loadPointClassTypeUrl= "/biz/college/class/grades/list";
        let loadPointClassRequest={"qryAll":"Y","tenantId":constants.loginUser.tenantId, "currentPage": 1, "pageSize": 1000};
        httpPost(loadPointClassTypeUrl, loadPointClassRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                var classData = response.data.dataList;
                classData.unshift({"classId":0,"className":"全部"})
                this.setState({
                    classDataSource:classData,
                })
            }
        });
        
        this.loadPointRecord(_qryStartTime);
    }

    loadPointRecord=(_qryStartTime)=>{
        let url= "/biz/point/record/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "qryStartTime":_qryStartTime ? _qryStartTime : this.state.qryStartTime,
            "departmentId":this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId,         
            "classId":this.state.selClassId == 0 ? null : this.state.selClassId,
            // "tenantId":constants.loginUser.tenantId,
            "pointClassId":this.state.selPointClassId ? this.state.selPointClassId :null,
            "notBalance":"notBalance"
        };
        httpPost(url, loadRequest, this.loadPointRecordListCallBack);
    }

    loadPointRecordListCallBack=(response)=>{
        console.log("=====================积分记录",response.data)
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/point/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,   
            "departmentId":this.state.selDepartmentId,
            "userId":this.state.selStaffId,
            "classId":this.state.selClassId == 0 ? null : this.state.selClassId,
            "qryStartTime":this.state.qryStartTime,
            //"tenantId":constants.loginUser.tenantId,
            "pointClassId":this.state.selPointClassId ? this.state.selPointClassId :null,
            "notBalance":"notBalance"
    
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

     // 下拉触顶刷新到第一页
     _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        // var _qryStartTime = this.initqryStartTime();
        // this.setState({
        //     qryStartTime:_qryStartTime,
        //     currentPage:1
        // })
        let url= "/biz/point/record/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId":this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId,
            "userId":this.state.selStaffId,
            "qryStartTime":this.state.qryStartTime,
            "classId":this.state.selClassId == 0 ? null : this.state.selClassId,
            // "tenantId":constants.loginUser.tenantId,
            "pointClassId":this.state.selPointClassId ? this.state.selPointClassId :null,
            "notBalance":"notBalance"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    


    // 显示搜索项目
    showSearchItemSelect(){
        if (this.state.selClassId != null && this.state.selClassId != 0){
            return;
         } 
        if (!this.state.departmentDataSource || this.state.departmentDataSource.length < 1) {
            WToast.show({data:"请先添加部门"});
            return
        }
        this.setState({
            showSearchItemBlock:true,
        })
    }
    // 显示搜索项目
    showClassSearchItemSelect(){
        if (this.state.selDepartmentId != null && this.state.selDepartmentId != 0){
           return;
        }
        if (!this.state.classDataSource || this.state.classDataSource.length < 1) {
            WToast.show({data:"请先添加班级"});
            return
        }
        this.setState({
            showClassSearchItemBlock:true,
        })
    }
    // 显示搜索项目
    showPointClassSearchItemSelect(){

        if (!this.state.pointClassDataSource || this.state.pointClassDataSource.length < 1) {
            WToast.show({data:"请先添加积分类别"});
            return
        }
        this.setState({
            showPointClassSearchItemBlock:true,
        })
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadPointRecord();
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={()=>{
                Alert.alert('确认','您确定要导出PDF文件吗？',[
                    {
                        text:"取消", onPress:()=>{
                            WToast.show({data:'点击了取消'});
                        }
                    },
                    {
                        text:"确定", onPress:()=>{
                            WToast.show({data:'点击了确定'});
                            this.exportPdfFile()
                        }
                    }
                ]);
            }} style={[{marginBottom:1.5}]}>
                    <Image style={{ width: 23, height: 23}} source={require('../../assets/icon/iconfont/outputBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    exportPdfFile=()=> {
        console.log("=======exportPdfFile");
        let url= "/biz/generate/pdf/point_record_list";
        let requestParams={
            // "userId": constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
            "currentPage": 1,
            "pageSize": 1000,
            "departmentId":this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId,
            "userId":this.state.selStaffId,
            "classId":this.state.selClassId == 0 ? null : this.state.selClassId,
            "pointClassId":this.state.selPointClassId ? this.state.selPointClassId :null,
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data); 
                WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
                Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                    {
                        text:"不打开", onPress:()=>{
                        WToast.show({data:'点击了不打开'});
                        }
                    },
                    {
                        text:"打开", onPress:()=>{
                            WToast.show({data:'点击了打开'});
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    // 部门
    renderDepartmentRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                selDepartmentId:item.departmentId,
                selDepartmentName:item.departmentName,
                selDepartmentStaffDataSource:item.departmentUserDTOList,
                selStaffId:null,
                selStaffName:null,
            }) }}>
                <View key={"department_" + item.departmentId} style={[item.departmentId===this.state.selDepartmentId? 
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                    CommonStyle.choseToSearchItemsViewSize   
                    ] }>
                    <Text style={[item.departmentId===this.state.selDepartmentId?
                    CommonStyle.choseToSearchItemsSelectedTextStyle:CommonStyle.choseToSearchItemsTextStyle
                ]}>
                        {item.departmentName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    //员工
    renderDepartmentStaffRow=(item, index)=>{
        return (
            <View key={item.jobUserId} >
                <TouchableOpacity onPress={()=>{
                    this.setState({
                        selStaffId:item.userId,
                        selStaffName:item.staffName
                    })
                }}>
                    <View key={"jobuser_" + item.jobUserId} style={[item.userId===this.state.selStaffId? 
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                    CommonStyle.choseToSearchItemsViewSize   
                        ] }>
                        <Text style={[item.userId===this.state.selStaffId? 
                    CommonStyle.choseToSearchItemsSelectedTextStyle:CommonStyle.choseToSearchItemsTextStyle
                        ]}>
                            {item.staffName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    // 班级
    renderClasstRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                selClassId:item.classId,
                selClassName:item.className,
                selClassUserDataSource:item.classUserDTOList,
                selUserId:null,
                selUserName:null
            }) }}>
                <View key={"department_" + item.classId} style={[item.classId===this.state.selClassId? 
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                    CommonStyle.choseToSearchItemsViewSize   
                    ] }>
                    <Text style={[item.classId===this.state.selClassId? 
                    CommonStyle.choseToSearchItemsSelectedTextStyle:CommonStyle.choseToSearchItemsTextStyle
                ]}>
                        {item.className}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    //学生
    renderClassUserRow=(item, index)=>{
        return (
            <View key={item.staffId} >
                <TouchableOpacity onPress={()=>{
                    this.setState({
                        selUserId:item.staffId,
                        selUserName:item.staffName,
                    })
                }}>
                    <View key={"staff_" + item.staffId} style={[item.staffId===this.state.selUserId? 
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                    CommonStyle.choseToSearchItemsViewSize                        
                    ] }>
                        <Text style={[item.staffId===this.state.selUserId? 
                    CommonStyle.choseToSearchItemsSelectedTextStyle:CommonStyle.choseToSearchItemsTextStyle
                ]}>
                            {item.staffName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    // 积分类型
    renderPointClasstRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                selPointClassId:item.pointClassId,
                selPointClassName:item.pointClassName,
            }) }}>
                <View key={"pointClass_" + item.pointClassId} style={[item.pointClassId===this.state.selPointClassId?                     
                    CommonStyle.choseToSearchItemsSelectedViewColor
                    :
                    CommonStyle.choseToSearchItemsViewColor
                    ,
                    CommonStyle.choseToSearchItemsViewSize
                    ] }>
                    <Text style={[item.pointClassId===this.state.selPointClassId? 
                    CommonStyle.choseToSearchItemsSelectedTextStyle:CommonStyle.choseToSearchItemsTextStyle
                ]}>
                        {item.pointClassName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    openQryStartDate(){
        this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate)
    }

    callBackSelectQryStartDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryStartDate:value
        })
        if (value && value.length) {
            var qryStartTime = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    qryStartTime += vartime;
                }
                else{
                    qryStartTime += "-" + vartime;
                }
            }
            this.setState({
                qryStartTime:qryStartTime
            })


            let url= "/biz/point/record/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "departmentId":this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId,
                "userId":this.state.selStaffId,
                "qryStartTime":qryStartTime,
                "classId":this.state.selClassId == 0 ? null : this.state.selClassId,
                "pointClassId":this.state.selPointClassId ? this.state.selPointClassId :null,
                "notBalance":"notBalance"
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    }

    renderRow=(item,index)=>{
        return (
            <View key={item.recordId} style={[CommonStyle.innerViewStyle]}>
                <View style={styles.itemContentViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>员工姓名：{item.staffName}</Text>
                </View>
                <View style={styles.itemContentViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>积分类别：{item.pointClassNameA ? item.pointClassNameA:item.pointClassNameB}</Text>
                </View>
                <View style={styles.itemContentViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>积分描述：{item.pointConfigAlias ? item.pointConfigAlias:item.pointRewardAlias}</Text>
                </View>
                <View style={styles.itemContentViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>获得积分：{item.pointValue}</Text>
                </View>
                <View style={styles.itemContentViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>获得时间：{item.gmtCreated}</Text>
                </View>
                {
                    item.rewardUserName?
                    <View style={styles.itemContentViewStyle}>
                        <Text style={CommonStyle.bodyTextStyle}>奖励人：{item.rewardUserName}</Text>
                    </View>                    
                    :
                    <></>
                }                
                <View style={styles.itemContentViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>员工总积分：{item.pointTotalValue}</Text>
                </View>
                <View style={styles.itemContentViewStyle}>
                </View>

            </View>
        )
    }


    render(){
        return(
            <View>
                <CommonHeadScreen title='积分记录'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <View style={[CommonStyle.headViewStyle]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ width: '100%', flexWrap: 'wrap', flexDirection: 'row' }}>
                        <TouchableOpacity onPress={() => this.showSearchItemSelect()}>
                            {
                                this.state.showSearchItemBlock ?
                                    <View style={[CommonStyle.choseToSearchViewStyle]}>
                                        <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                            {this.state.selDepartmentId && this.state.selDepartmentName ? (this.state.selDepartmentName) : "选择部门"}
                                        </Text>
                                        <Image style={[CommonStyle.choseToSearchClosedIconSize]} source={require('../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                                    </View>
                                    :
                                    <View style={[CommonStyle.choseToSearchViewStyle]}>
                                        <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                            {this.state.selDepartmentId && this.state.selDepartmentName ? (this.state.selDepartmentName) : "选择部门"}
                                        </Text>
                                        <Image style={[CommonStyle.choseToSearchOpenedIconSize]} source={require('../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                                    </View>
                            }
                        </TouchableOpacity>
                    {
                        this.state.classDataSource?
                        <TouchableOpacity onPress={()=>this.showClassSearchItemSelect()}>
                            {
                                this.state.showClassSearchItemBlock?
                                <View style={[CommonStyle.choseToSearchViewStyle]}>
                                <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                    {this.state.selClassId && this.state.selClassName ? (this.state.selClassName) : "选择班级"}
                                    </Text>
                                    <Image style={[CommonStyle.choseToSearchClosedIconSize]} source={require('../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                                </View>
                                :
                                <View style={[CommonStyle.choseToSearchViewStyle]}>
                                <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                    {this.state.selClassId && this.state.selClassName ? (this.state.selClassName) : "选择班级"}
                                    </Text>
                                    <Image style={[CommonStyle.choseToSearchOpenedIconSize]} source={require('../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                                </View>
                            }
                        </TouchableOpacity>
                        :
                        null
                    }
                    {
                        this.state.pointClassDataSource?
                                <TouchableOpacity onPress={()=>this.showPointClassSearchItemSelect()}>
                                    {
                                        this.state.showPointClassSearchItemBlock?
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                        <Text style={[CommonStyle.choseToSearchOpenedTextStyle]}>
                                                {this.state.selPointClassId && this.state.selPointClassName ? (this.state.selPointClassName) : "积分类别"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchClosedIconSize]} source={require('../../assets/icon/iconfont/arrow_up_blue.png')}></Image>
                                        </View>   
                                        :
                                        <View style={[CommonStyle.choseToSearchViewStyle]}>
                                            <Text style={[CommonStyle.choseToSearchClosedTextStyle]}>
                                            {this.state.selPointClassId && this.state.selPointClassName ? (this.state.selPointClassName) : "积分类别"}
                                            </Text>
                                            <Image style={[CommonStyle.choseToSearchOpenedIconSize]} source={require('../../assets/icon/iconfont/arrow_down_grey.png')}></Image>
                                        </View>                                        
                                    }
                                </TouchableOpacity>
                        :
                        null
                    }
                </View>
            </View>
                <View>
                {
                    this.state.showSearchItemBlock ?
                    <View style={[CommonStyle.choseToSearchBigBoxViewStyle,{height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                        <View style={CommonStyle.heightLimited}>
                            <ScrollView>
                                <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]} onLayout={(a)=>{console.log(a)}}>
                                    <View style={[{backgroundColor: 'rgba(255,255,255,1)'},CommonStyle.choseToSearchItemsViewSize]}>
                                        <Text style={{ fontSize: 16, fontWeight: 'bold' }}>部门：</Text>
                                    </View>
                                    {
                                        (this.state.departmentDataSource && this.state.departmentDataSource.length > 0)
                                            ?
                                            this.state.departmentDataSource.map((item, index) => {
                                                return this.renderDepartmentRow(item)
                                            })
                                            : null
                                    }
                                </View>
                                {
                                (this.state.selDepartmentStaffDataSource && this.state.selDepartmentStaffDataSource.length > 0) 
                                ? 
                                <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                    <View style={[{backgroundColor: 'rgba(255,255,255,1)'},CommonStyle.choseToSearchItemsViewSize]}>
                                        <Text style={{ fontSize: 16, fontWeight: 'bold' }}>员工：</Text>
                                    </View>
                                    {
                                        this.state.selDepartmentStaffDataSource.map((item, index)=>{
                                            return this.renderDepartmentStaffRow(item)
                                        })
                                    }
                                </View>
                                : null
                                }
                            </ScrollView> 
                        </View>
                        <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    showSearchItemBlock: false,
                                })
                            }}>
                                <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                    <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => { 
                                let url= "/biz/point/record/list";
                                let loadRequest={
                                    "currentPage": 1,
                                    "pageSize": this.state.pageSize,
                                    "departmentId":this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId,
                                    "userId":this.state.selStaffId ? this.state.selStaffId :null,
                                    "qryStartTime":this.state.qryStartTime,
                                    "classId":null,
                                    "staffId":null,
                                    "pointClassId":this.state.selPointClassId ? this.state.selPointClassId :null,
                                    "notBalance":"notBalance"
                                };
                                console.log("选择的部门====="+this.state.selDepartmentId)
                                httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                this.setState({
                                    showSearchItemBlock:false,
                                }) 
                            }}>
                                <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                    <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    :
                    null
                }
                {
                    this.state.showClassSearchItemBlock?
                    <View style={[CommonStyle.choseToSearchBigBoxViewStyle,{height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                        <View style={CommonStyle.heightLimited}>
                            <ScrollView>
                                <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                <View style={[{backgroundColor: 'rgba(255,255,255,1)'},CommonStyle.choseToSearchItemsViewSize]}>
                                    <Text style={{ fontSize: 16, fontWeight: 'bold' }}>班级：</Text>
                                </View>
                                    {
                                        (this.state.classDataSource && this.state.classDataSource.length > 0) 
                                        ? 
                                        this.state.classDataSource.map((item, index)=>{
                                            return this.renderClasstRow(item)
                                        })
                                        : null 
                                    }
                                </View>
                                {
                                (this.state.selClassUserDataSource && this.state.selClassUserDataSource.length > 0) 
                                ? 
                                <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                <View style={[{backgroundColor: 'rgba(255,255,255,1)'},CommonStyle.choseToSearchItemsViewSize]}>
                                    <Text style={{ fontSize: 16, fontWeight: 'bold' }}>学生：</Text>
                                </View>
                                    {
                                        this.state.selClassUserDataSource.map((item, index)=>{
                                            return this.renderClassUserRow(item)
                                        })
                                    }
                                </View>
                                : null
                                }
                            </ScrollView> 
                        </View>
                        <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showClassSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => { 
                                let url= "/biz/point/record/list";
                                let loadRequest={
                                    "currentPage": 1,
                                    "pageSize": this.state.pageSize,
                                    "departmentId":null,
                                    "userId":null,
                                    "qryStartTime":this.state.qryStartTime,
                                    "classId":this.state.selClassId == 0 ? null : this.state.selClassId,
                                    "pointClassId":this.state.selPointClassId ? this.state.selPointClassId :null,
                                    "staffId":this.state.selUserId ? this.state.selUserId : null,
                                    "notBalance":"notBalance"
                                };
                                console.log("选择的班级====="+this.state.selClassId)
                                httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                this.setState({
                                    showClassSearchItemBlock:false,
                                }) 
                            }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                            </TouchableOpacity>
                        </View>
                    </View>    
                    :
                    null
                }
                {
                    this.state.showPointClassSearchItemBlock?
                    <View style={[CommonStyle.choseToSearchBigBoxViewStyle,{height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                        <View style={CommonStyle.heightLimited}>
                            <ScrollView>
                                <View style={[CommonStyle.choseToSearchSmallBoxViewStyle]}>
                                <View style={[{backgroundColor: 'rgba(255,255,255,1)'},CommonStyle.choseToSearchItemsViewSize]}>
                                    <Text style={{ fontSize: 16, fontWeight: 'bold' }}>积分类别：</Text>
                                </View>
                                    {
                                        (this.state.pointClassDataSource && this.state.pointClassDataSource.length > 0) 
                                        ? 
                                        this.state.pointClassDataSource.map((item, index)=>{
                                            return this.renderPointClasstRow(item)
                                        })
                                        : null 
                                    }
                                </View>
                            </ScrollView> 
                        </View>
                        <View style={[CommonStyle.choseToSearchBtnRowStyle]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            showPointClassSearchItemBlock: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.choseToSearchBtnCanleViewStyle]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText]}>取消</Text>
                                        </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => { 
                                let url= "/biz/point/record/list";
                                let loadRequest={
                                    "currentPage": 1,
                                    "pageSize": this.state.pageSize,
                                    "departmentId":this.state.selDepartmentId == 0 ? null : this.state.selDepartmentId,
                                    "userId":this.state.selStaffId ? this.state.selStaffId :null,
                                    "qryStartTime":this.state.qryStartTime,
                                    "classId":this.state.selClassId == 0 ? null : this.state.selClassId,
                                    "staffId":this.state.selUserId ? this.state.selUserId : null,
                                    "pointClassId":this.state.selPointClassId ? this.state.selPointClassId :null,
                                    "notBalance":"notBalance"
                                };
                                console.log("选择的类别====="+this.state.selPointClassId)
                                httpPost(url, loadRequest, this._loadFreshDataCallBack);
                                this.setState({
                                    showPointClassSearchItemBlock:false,
                                }) 
                            }}>
                                        <View style={[CommonStyle.choseToSearchBtnOKViewStyle]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText]}>确定搜索</Text>
                                        </View>
                            </TouchableOpacity>
                        </View>
                    </View>    
                    :
                    null
                }

                <View style={[CommonStyle.rightTop50FloatingBlockView,this.state.qryStartTime ? 
                    {borderRadius:3, width:null, paddingLeft:15, paddingRight:15, height: 40,top:16,opacity:0.6,backgroundColor: "rgba(242, 245, 252, 1)"} : {}]}>
                    <TouchableOpacity onPress={()=>this.openQryStartDate()}>
                        <Text style={{ color: 'rgba(0,10,32,0.85)', fontSize: 14 }}>
                        {!this.state.qryStartTime ? "时间" : this.state.qryStartTime}
                        </Text>
                    </TouchableOpacity>
                </View>

                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                    />
                </View>                    
                </View>
                <BottomScrollSelect 
                    ref={'SelectQryStartDate'} 
                    callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle: {
        //marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 8,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop:10,
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});