import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,ScrollView,Image,
    FlatList,RefreshControl,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class MaterialAudit extends Component {
    constructor(props) {
        super(props);
        this.state = {
            parentClassifyName:"",
            classifyName:"",
            amount:"",
            unitPrice:"",
            summation:"",
            budget:"",
            supplier:"",
            unitPriceCopy:"",
            summationCopy:"",
            budgetCopy:"",
            supplierCopy:"",
            fillDataList:[],
            selReplyId:1,
            replyDataSource:[
                {
                    replyId:1,
                    replyType:"同意",
                    replyName:"Y"
                },
                {
                    replyId:2,
                    replyType:"驳回",
                    replyName:"N"
                }
            ],
            selReplyName:"Y",
            recordId:"",
            nodeId:"",
            auditUserId:"",
            auditOpinion:"",
            purchaseId:"",
            auditTypeCode:"",
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId,recordId } = route.params;
            if (recordId) {
                let url= "/biz/audit/node/record/get";
                let loadRequest={
                    "recordId": recordId,
                };
                httpPost(url, loadRequest, this.loadAuditRecordCallBack);
            }
            
        }
    }

    loadAuditRecordCallBack=(response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                parentClassifyName:response.data.materialPurchaseList[0].parentClassifyName,
                classifyName:response.data.materialPurchaseList[0].classifyName,
                amount:response.data.materialPurchaseList[0].amount,
                unitPrice:response.data.materialPurchaseList[0].unitPrice,
                summation:response.data.materialPurchaseList[0].summation,
                budget:response.data.materialPurchaseList[0].budget,
                supplier:response.data.materialPurchaseList[0].supplier,
                fillDataList:response.data.fillDataList,
                recordId:response.data.recordId,
                nodeId:response.data.nodeId,
                auditUserId:response.data.auditUserId,
                purchaseId:response.data.materialPurchaseList[0].purchaseId,
                auditTypeCode:response.data.auditTypeCode,

            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    saveAudit = () => {
        console.log("=======saveAuditConfig");
        let toastOpts;
        if(this.state.selReplyName == 'Y') {
            if (this.state.fillDataList.includes("SUPPLIER")) {
                if (!this.state.supplier) {
                    if (!this.state.supplierCopy) {
                        toastOpts = getFailToastOpts("请输入供应商");
                        WToast.show(toastOpts)
                        return;
                    }
                }
            }
            
            if (this.state.fillDataList.includes("UNIT_PRICE")) {
                if (!this.state.unitPrice) {
                    if (!this.state.unitPriceCopy) {
                        toastOpts = getFailToastOpts("请输入单价");
                        WToast.show(toastOpts)
                        return;
                    }
                }
            }
    
            if (this.state.fillDataList.includes("BUDGET")) {
                if (!this.state.budget) {
                    if (!this.state.budgetCopy) {
                        toastOpts = getFailToastOpts("请输入预计费用");
                        WToast.show(toastOpts)
                        return;
                    }
                }
            }
    
            if (this.state.fillDataList.includes("SUMMATION")) {
                if (!this.state.summation) {
                    if (!this.state.summationCopy) {
                        toastOpts = getFailToastOpts("请输入合计费用");
                        WToast.show(toastOpts)
                        return;
                    }
                }
            }    
        }

        let url = "/biz/audit/node/record/auditAdd";
        let requestParams = {
            "unitPrice":this.state.unitPrice ? this.state.unitPrice : this.state.unitPriceCopy,
            "summation":this.state.summation ? this.state.summation : this.state.summationCopy,
            "budget":this.state.budget ? this.state.budget : this.state.budgetCopy,
            "supplier":this.state.supplier ? this.state.supplier : this.state.supplierCopy,
            "purchaseId":this.state.purchaseId,

            "recordId":this.state.recordId,
            "nodeId":this.state.nodeId,
            "auditUserId":this.state.auditUserId,
            "auditTypeCode":this.state.auditTypeCode,

            "auditResult":this.state.selReplyName,
            "auditOpinion":this.state.auditOpinion,
        };
        httpPost(url, requestParams, this.saveAuditCallBack);
    }

    // 保存回调函数
    saveAuditCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    //sex列表展示
    renderReplyRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selReplyId:item.replyId,
                        selReplyName:item.replyName,
                    })
                }}>
                <View key={item.replyId} style={[item.replyId===this.state.selReplyId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.replyId===this.state.selReplyId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.replyType}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='审核'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>原料类别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入原料类别'}
                            onChangeText={(text) => this.setState({parentClassifyName:text})}
                        >
                            {this.state.parentClassifyName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>原料名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入原料名称'}
                            onChangeText={(text) => this.setState({classifyName:text})}
                        >
                            {this.state.classifyName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>采购重量</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            editable={false} 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入采购重量'}
                            onChangeText={(text) => this.setState({amount:text})}
                        >
                            {this.state.amount}
                        </TextInput>
                    </View>
                    {
                        this.state.supplier?
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>供应商</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                editable={false} 
                                //keyboardType='text'
                                style={styles.inputRightText}
                                placeholder={'请输入供应商'}
                                onChangeText={(text) => this.setState({supplier:text})}
                            >
                                {this.state.supplier}
                            </TextInput>
                        </View>
                        :
                        (
                            this.state.fillDataList.includes("SUPPLIER")?
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>供应商</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <TextInput 
                                    //keyboardType='text'
                                    style={styles.inputRightText}
                                    placeholder={'请输入供应商'}
                                    onChangeText={(text) => this.setState({supplierCopy:text})}
                                >
                                    {this.state.supplierCopy}
                                </TextInput>
                            </View>
                            :
                            <View/>
                        )
                    }
                    {
                        this.state.unitPrice?
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>单价</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                editable={false} 
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入单价'}
                                onChangeText={(text) => this.setState({unitPrice:text})}
                            >
                                {this.state.unitPrice}
                            </TextInput>
                        </View>
                        :
                        (
                            this.state.fillDataList.includes("UNIT_PRICE")?
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>单价</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <TextInput 
                                    keyboardType='numeric'
                                    style={styles.inputRightText}
                                    placeholder={'请输入单价'}
                                    onChangeText={(text) => this.setState({unitPriceCopy:text})}
                                >
                                    {this.state.unitPriceCopy}
                                </TextInput>
                            </View>
                            :
                            <View/>
                        )
                    }
                    {
                        this.state.budget?
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>预计费用</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                editable={false} 
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入预计费用'}
                                onChangeText={(text) => this.setState({budget:text})}
                            >
                                {this.state.budget}
                            </TextInput>
                        </View>
                        :
                        (
                            this.state.fillDataList.includes("BUDGET")?
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>预计费用</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <TextInput 
                                    keyboardType='numeric'
                                    style={styles.inputRightText}
                                    placeholder={'请输入预计费用'}
                                    onChangeText={(text) => this.setState({budgetCopy:text})}
                                >
                                    {this.state.budgetCopy}
                                </TextInput>
                            </View>
                            :
                            <View/>
                        )
                    }
                    {
                        this.state.summation?
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>合计费用</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                editable={false}
                                keyboardType='numeric'
                                style={styles.inputRightText}
                                placeholder={'请输入合计费用'}
                                onChangeText={(text) => this.setState({summation:text})}
                            >
                                {this.state.summation}
                            </TextInput>
                        </View>
                        :
                        (
                            this.state.fillDataList.includes("SUMMATION")?
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>合计费用</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <TextInput 
                                    keyboardType='numeric'
                                    style={styles.inputRightText}
                                    placeholder={'请输入合计费用'}
                                    onChangeText={(text) => this.setState({summationCopy:text})}
                                >
                                    {this.state.summationCopy}
                                </TextInput>
                            </View>
                            :
                            <View/>
                        )
                    }

                    <View style={styles.inputRowStyle}>
                        <View style={[styles.rowLabView,{marginRight:30}]}>
                            <Text style={styles.leftLabNameTextStyle}>审核批复</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row',marginLeft:0}}>
                            {
                                (this.state.replyDataSource && this.state.replyDataSource.length > 0) 
                                ? 
                                this.state.replyDataSource.map((item, index)=>{
                                    return this.renderReplyRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>审核意见</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle,{height:150}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:150}]}
                            placeholder={'请输入审核意见'}
                            onChangeText={(text) => this.setState({auditOpinion:text})}
                        >
                            {this.state.auditOpinion}
                        </TextInput>
                    </View>



                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveAudit.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:18
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputTextStyleTextStyle:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        height:45,
        justifyContent:'center'
    }
});