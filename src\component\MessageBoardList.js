import React, {Component} from 'react';
import {
  Dimensions,
  Image,
  ImageBackground,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {WToast} from 'react-native-smart-tip';
import CommonStyle from '../assets/css/CommonStyle';
import MessageInputModal from './MessageInputModal';

const screenWidth = Dimensions.get('window').width;
const screenHeight = Dimensions.get('window').height;

// 提取重复样式到常量，提高代码可读性
const messageContainerStyle = {
  backgroundColor: 'rgba(242, 245, 252, 0.5)',
  borderRadius: 10,
  width: screenWidth - 24,
  marginLeft: 12,
  marginRight: 12,
  paddingTop: 5,
  marginBottom: 5,
};

const messageItemStyle = {
  flexDirection: 'row',
  marginLeft: 10,
  marginTop: 10,
  marginRight: 6,
  marginBottom: 10,
};

const operatorNameStyle = {
  color: '#FFFFFF',
  fontSize: 13,
  fontFamily: 'PingFangSC',
  fontWeight: 'normal',
  textAlign: 'center',
  lineHeight: 20,
};

const timeStyle = {
  fontSize: 12,
  color: 'rgba(0,10,32,0.45)',
};

const contentStyle = {
  fontSize: 14,
  lineHeight: 24,
  textAlign: 'left',
  textAlignVertical: 'top',
  color: 'rgba(0, 10, 32, 0.65)',
};

export default class MessageBoardList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedMessageId: null,
      showModal: false,
      deleteModal: false,
      messageModal: false,
      messageContent: '',
      parentMessageId: '',
    };
  }

  handleMessageLongPress = (messageId) => {
    if (this.props.userId != constants.loginUser.userId) {
      return;
    } else {
      this.setState({
        selectedMessageId: messageId,
        showModal: true,
      });
    }
  };

  handleBubbleMenuItemPress = () => {
    this.setState({showModal: false, deleteModal: true});
  };

  deleteMessage = (messageId) => {
    console.log('=======delete=messageId', messageId);
    if (this.props.deleteMessage) {
      this.props.deleteMessage(messageId);
    }
    this.setState({
      deleteModal: false,
    });
  };

  saveDailyMessage = () => {
    // 这里可以添加保存消息的逻辑
    console.log('Saving message:', this.state.messageContent);
    if (!this.state.messageContent) {
      return;
    }
    // 调用父页面的 saveDailyMessage 方法
    if (this.props.saveDailyMessage) {
      this.props.saveDailyMessage({
        messageContent: this.state.messageContent,
        parentMessageId: this.state.parentMessageId,
      });
    }
    this.setState({
      messageModal: false,
      messageContent: '',
      parentMessageId: '',
    });
  };

  renderMessageItem = (item, index) => {
    return (
      <View key={item.messageId} ref={(ref) => (this.messageItem = ref)}>
        <TouchableOpacity
          onPress={() => {
            this.setState({
              parentMessageId: item.messageId,
              messageModal: true,
            });
          }}
          onLongPress={() => this.handleMessageLongPress(item.messageId)}
          delayLongPress={500}
          style={messageItemStyle}>
          {item.operatorPhoto ? (
            <Image
              source={{uri: constants.image_addr + '/' + item.operatorPhoto}}
              style={{height: 36, width: 36, borderRadius: 50}}
            />
          ) : (
            <ImageBackground
              source={require('../assets/icon/iconfont/profilePicture.png')}
              style={{height: 36, width: 36}}>
              <View
                style={{
                  height: 36,
                  width: 36,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text style={operatorNameStyle}>
                  {item.operatorName.length <= 2
                    ? item.operatorName
                    : item.operatorName.slice(-2)}
                </Text>
              </View>
            </ImageBackground>
          )}

          <View style={{flexDirection: 'column', marginLeft: 10, flex: 1}}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'flex-start',
                alignItems: 'center',
                paddingTop: 4,
              }}>
              <View style={{flexDirection: 'row'}}>
                <Text style={{fontSize: 16}}>{item.operatorName}</Text>
              </View>
              <View style={{flexDirection: 'row', marginLeft: 6}}>
                <Text style={timeStyle}>{item.gmtCreated.slice(0, 16)}</Text>
              </View>
            </View>

            {item.parentMessageId ? (
              <View
                style={{flexDirection: 'column', justifyContent: 'flex-start'}}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    alignItems: 'flex-start',
                    marginLeft: 9,
                    marginTop: 11,
                  }}>
                  <Text style={[contentStyle, {color: 'rgba(0,10,32,0.45)'}]}>
                    {'回复 ' +
                      item.parentUserName +
                      ': ' +
                      item.parentMessageContent}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    alignItems: 'flex-start',
                    marginTop: 8,
                  }}>
                  <Text style={contentStyle}>
                    {item.messageContent ? item.messageContent : '无'}
                  </Text>
                </View>
              </View>
            ) : (
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'flex-start',
                  alignItems: 'flex-start',
                  marginTop: 10,
                }}>
                <Text style={contentStyle}>
                  {item.messageContent ? item.messageContent : '无'}
                </Text>
              </View>
            )}
          </View>
        </TouchableOpacity>

        {/* 分割线 */}
        {index < this.props.messageBoardList.length - 1 && (
          <View
            style={{
              height: 1,
              backgroundColor: '#E8E9EC',
              marginLeft: 50,
              marginRight: 10,
            }}
          />
        )}
      </View>
    );
  };

  render() {
    const {messageBoardList} = this.props;

    return (
      <View style={{position: 'relative', flex: 1}}>
        {messageBoardList && messageBoardList.length > 0 ? (
          <View style={messageContainerStyle}>
            {messageBoardList.map(this.renderMessageItem)}
          </View>
        ) : (
          <View />
        )}

        {/* 删除弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.deleteModal}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 292,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认删除该留言?</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  删除后数据不可恢复，请谨慎操作
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 292,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({deleteModal: false});
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    this.setState({deleteModal: false});
                    WToast.show({data: '点击了确定'});
                    this.deleteMessage(this.state.selectedMessageId);
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Modal 组件 */}
        <Modal
          //   animationType="slide" // 从底部弹出动画
          transparent={true}
          visible={this.state.showModal}
          onRequestClose={() => {
            this.setState({showModal: false});
          }}>
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => this.setState({showModal: false})}>
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                {/* 这里可以添加 Modal 的内容，例如删除和回复按钮 */}
                <TouchableOpacity
                  onPress={this.handleBubbleMenuItemPress}
                  style={styles.modalButton}>
                  <Text style={styles.modalButtonText}>删除</Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        </Modal>

        {/* 留言输入框弹窗 */}
        <MessageInputModal
          visible={this.state.messageModal}
          onClose={() =>
            this.setState({
              messageModal: false,
              messageContent: '',
              parentMessageId: '',
            })
          }
          onSend={() => {
            if (!this.state.messageContent) {
              return;
            }
            this.setState({messageModal: false});
            this.saveDailyMessage();
          }}
          messageContent={this.state.messageContent}
          onChangeMessageContent={(text) =>
            this.setState({messageContent: text})
          }
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  itemContentStyle: contentStyle,
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    padding: 20,
  },
  modalButton: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E8E9EC',
  },
  modalButtonText: {
    fontSize: 16,
    textAlign: 'center',
  },
});
