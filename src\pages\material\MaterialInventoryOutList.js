import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class MaterialInventoryOutList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      io: 'O',
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      materialLocationList: [],
      selLocationId: 0,
      selLocationName: '全部',
      totalLocationRecord: 0,
      moreModal: false,
      modalItem: {},
      deleteModal: false,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    this.loadMaterialLocationList();
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {classifyId, classifyName} = route.params;
      if (classifyId) {
        this.setState({
          classifyId: classifyId,
        });
      }
      if (classifyName) {
        this.setState({
          classifyName: classifyName,
        });
      }
      this.loadStorageList(classifyId);
    } else {
      this.loadStorageList();
    }
  }

  loadMaterialLocationList = () => {
    let url = '/biz/storage/location/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: 1000,
      locationType: 'M',
    };
    httpPost(url, loadRequest, this.loadMaterialLocationListCallBack);
  };

  loadMaterialLocationListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataSource = response.data.dataList;
      dataSource.unshift({
        locationName: '全部',
        locationId: 0,
        locationType: 'M',
        locationSort: 0,
      });
      this.setState({
        materialLocationList: dataSource,
        totalLocationRecord: response.data.totalRecord,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/material/inventory/inventoryList';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      operator: constants.loginUser.userName,
      userId: constants.loginUser.userId,
      // "classifyId": this.state.classifyId,
      io: this.state.io,
      locationId:
        this.state.selLocationId == 0 ? null : this.state.selLocationId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/material/inventory/inventoryList';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      operator: constants.loginUser.userName,
      userId: constants.loginUser.userId,
      // "classifyId": this.state.classifyId,
      io: this.state.io,
      locationId:
        this.state.selLocationId == 0 ? null : this.state.selLocationId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadStorageList();
  };

  loadStorageList = (classifyId) => {
    let url = '/biz/material/inventory/inventoryList';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      // "classifyId": classifyId ? classifyId : this.state.classifyId,
      io: this.state.io,
      operator: constants.loginUser.userName,
      userId: constants.loginUser.userId,
      locationId:
        this.state.selLocationId == 0 ? null : this.state.selLocationId,
    };
    httpPost(url, loadRequest, this.loadStorageListCallBack);
  };

  loadStorageListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteStorage = (inventoryId) => {
    console.log('=======delete=inventoryId', inventoryId);
    let url = '/biz/material/inventory/delete';
    let requestParams = {inventoryId: inventoryId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.inventoryId} style={styles.innerViewStyle}>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            原料名称：{item.classifyName}
          </Text>
          {constants.loginUser.userName == item.operator ? null : (
            <Text
              style={{
                paddingTop: 3,
                paddingBottom: 3,
                paddingLeft: 5,
                paddingRight: 5,
                height: 23,
                borderRadius: 12,
                backgroundColor: 'rgba(255,0,0,0.4)',
                color: '#FFFFFF',
              }}>
              抄送
            </Text>
          )}
        </View>
        <View
          style={[
            styles.titleViewStyle,
            {
              position: 'absolute',
              right: 0,
              top: 0,
              flexDirection: 'column',
              marginRight: 15,
            },
          ]}>
          <TouchableOpacity
            onPress={() => {
              this.setState({
                moreModal: true,
                modalItem: item,
              });
            }}>
            <View style={[{width: 35, height: 35, alignItems: 'center'}]}>
              <Image
                style={{width: 28, height: 28}}
                source={require('../../assets/icon/iconfont/more.png')}></Image>
            </View>
          </TouchableOpacity>
        </View>
        {constants.loginUser.tenantId == 66 ? (
          <View>
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>
                用料类型：
                {item.materialType === 'Y'
                  ? '定型'
                  : item.materialType === 'N'
                  ? '不定型'
                  : '暂未选择'}
              </Text>
            </View>
          </View>
        ) : (
          <View />
        )}

        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>需求产品：{item.seriesName}</Text>
        </View>

        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            库区：{item.locationAreaName}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>库位：{item.locationName}</Text>
        </View>

        <View style={[styles.titleViewStyle]}>
          <Text style={styles.titleTextStyle}>重量(吨)：{item.weight}</Text>
        </View>

        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>提交人：{item.operator}</Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>提交时间：{item.gmtCreated}</Text>
          {item.currentAuditUserName ? null : (
            <View style={{marginRight: 15}}>
              {item.auditState == '发起审核' || item.auditState == '审核中' ? (
                <Text style={{color: '#FFB800'}}>{item.auditState}</Text>
              ) : (
                <View>
                  {item.auditState == '审核通过' ? (
                    <Text style={{color: 'green'}}>{item.auditState}</Text>
                  ) : (
                    <Text style={{color: '#CB4139'}}>{item.auditState}</Text>
                  )}
                </View>
              )}
            </View>
          )}
        </View>
        {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>审核状态：{item.auditState}</Text>
                </View> */}
        {
          item.currentAuditUserName ? (
            <View style={styles.titleViewStyle}>
              <Text style={styles.titleTextStyle}>
                审核人：{item.currentAuditUserName}
              </Text>
              <View style={{marginRight: 15}}>
                {item.auditState == '发起审核' ||
                item.auditState == '审核中' ? (
                  <Text style={{color: '#FFB800'}}>{item.auditState}</Text>
                ) : (
                  <View>
                    {item.auditState == '审核通过' ? (
                      <Text style={{color: 'green'}}>{item.auditState}</Text>
                    ) : (
                      <Text style={{color: '#CB4139'}}>{item.auditState}</Text>
                    )}
                  </View>
                )}
              </View>
            </View>
          ) : null
          // <View style={styles.titleViewStyle}>
          //     <Text style={styles.titleTextStyle}>审核人：无</Text>
          //     <View>
          //         {
          //             item.auditState == "发起审核" || item.auditState == "审核中" ?
          //             <Text style={{color:'#FFB800'}}>{item.auditState}</Text>
          //             :
          //             <View>
          //             {
          //                 item.auditState == "审核通过" ?
          //                 <Text style={{color:'green'}}>{item.auditState}</Text>
          //                 :
          //                 <Text style={{color:'#CB4139'}}>{item.auditState}</Text>
          //             }
          //             </View>
          //         }
          //     </View>
          // </View>
        }

        {constants.loginUser.userName == item.operator ? (
          <View
            style={[
              CommonStyle.itemBottomBtnStyle,
              {flexWrap: 'wrap', marginLeft: 12, marginRight: 16},
            ]}>
            {item.auditState == '发起审核' || item.auditState == '审核中' ? (
              <TouchableOpacity
                onPress={() => {
                  let url = '/biz/audit/node/record/auditSendNotificationPush';
                  let loadRequest = {
                    auditItemId: item.inventoryId,
                    auditTypeCode: 'MATERIAL_INVENTORY_O',
                  };
                  httpPost(url, loadRequest, (response) => {
                    if (response.code == 200) {
                      WToast.show({data: '成功催审'});
                      return;
                    }
                  });
                }}>
                <View
                  style={[
                    CommonStyle.itemBottomEditBtnViewStyle,
                    {backgroundColor: 'red'},
                    {width: 64, flexDirection: 'row', marginLeft: 0},
                  ]}>
                  <Image
                    style={{width: 17, height: 17, marginRight: 2}}
                    source={require('../../assets/icon/iconfont/detail.png')}></Image>
                  <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                    催审
                  </Text>
                </View>
              </TouchableOpacity>
            ) : null}
            {item.auditState == '发起审核' ? (
              <View
                style={[CommonStyle.itemBottomBtnStyle, {flexWrap: 'wrap'}]}>
                <TouchableOpacity
                  onPress={() => {
                    this.props.navigation.navigate(
                      'InventoryAuditBacklogDetail',
                      {
                        // 传递参数
                        auditItemId: item.inventoryId,
                        inventoryItem: item,
                        // 传递回调函数
                        refresh: this.callBackFunction,
                        auditTypeCode: 'MATERIAL_INVENTORY_O',
                      },
                    );
                  }}>
                  <View
                    style={[
                      CommonStyle.itemBottomDetailBtnViewStyle,
                      {width: 64, flexDirection: 'row', marginLeft: 0},
                    ]}>
                    <Image
                      style={{width: 17, height: 17, marginRight: 2}}
                      source={require('../../assets/icon/iconfont/detail.png')}></Image>
                    <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                      详情
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            ) : (
              <View>
                <TouchableOpacity
                  onPress={() => {
                    this.props.navigation.navigate(
                      'InventoryAuditBacklogDetail',
                      {
                        // 传递参数
                        auditItemId: item.inventoryId,
                        inventoryItem: item,
                        // 传递回调函数
                        refresh: this.callBackFunction,
                        auditTypeCode: 'MATERIAL_INVENTORY_O',
                      },
                    );
                  }}>
                  <View
                    style={[
                      CommonStyle.itemBottomDetailBtnViewStyle,
                      {width: 64, flexDirection: 'row', marginLeft: 0},
                    ]}>
                    <Image
                      style={{width: 17, height: 17, marginRight: 2}}
                      source={require('../../assets/icon/iconfont/detail.png')}></Image>
                    <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                      详情
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            )}
          </View>
        ) : (
          <View style={[CommonStyle.itemBottomBtnStyle, {flexWrap: 'wrap'}]}>
            {item.auditState == '发起审核' || item.auditState == '审核中' ? (
              <TouchableOpacity
                onPress={() => {
                  let url = '/biz/audit/node/record/auditSendNotificationPush';
                  let loadRequest = {
                    auditItemId: item.inventoryId,
                    auditTypeCode: 'MATERIAL_INVENTORY_O',
                  };
                  httpPost(url, loadRequest, (response) => {
                    if (response.code == 200) {
                      WToast.show({data: '成功催审'});
                      return;
                    }
                  });
                }}>
                <View
                  style={[
                    CommonStyle.itemBottomDetailBtnViewStyle,
                    {
                      width: 75,
                      margin: 5,
                      flexDirection: 'row',
                      backgroundColor: 'red',
                    },
                  ]}>
                  <Image
                    style={{width: 20, height: 20, marginRight: 5}}
                    source={require('../../assets/icon/iconfont/detail.png')}></Image>
                  <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                    催审
                  </Text>
                </View>
              </TouchableOpacity>
            ) : null}
            <TouchableOpacity
              onPress={() => {
                this.props.navigation.navigate('InventoryAuditBacklogDetail', {
                  // 传递参数
                  auditItemId: item.inventoryId,
                  inventoryItem: item,
                  // 传递回调函数
                  refresh: this.callBackFunction,
                  auditTypeCode: 'MATERIAL_INVENTORY_O',
                });
              }}>
              <View
                style={[
                  CommonStyle.itemBottomDetailBtnViewStyle,
                  {width: 64, flexDirection: 'row', marginLeft: 0},
                ]}>
                <Image
                  style={{width: 17, height: 17, marginRight: 2}}
                  source={require('../../assets/icon/iconfont/detail.png')}></Image>
                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                  详情
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                let url = '/biz/audit/cc/record/modify';
                let loadRequest = {
                  recordId: item.ccRecordId,
                  ccRecordState: item.ccRecordState == '0AA' ? '0AB' : '0AA',
                };
                httpPost(url, loadRequest, (response) => {
                  if (response.code == 200 && response.data) {
                    WToast.show({
                      data:
                        response.data.ccRecordState == '0AA'
                          ? '成功标为未读'
                          : '成功标为已读',
                    });
                    this.callBackFunction();
                  } else if (response.code == 401) {
                    WToast.show({data: response.message});
                    this.props.navigation.navigate('LoginView');
                  } else {
                    WToast.show({data: response.message});
                  }
                });
              }}>
              <View
                style={[
                  CommonStyle.itemBottomEditBtnViewStyle,
                  {width: 75, margin: 5, flexDirection: 'row'},
                  item.ccRecordState == '0AA'
                    ? {backgroundColor: '#FA353F'}
                    : {backgroundColor: '#FFB800'},
                ]}>
                {item.ccRecordState == '0AA' ? (
                  <Image
                    style={{width: 20, height: 20, marginRight: 5}}
                    source={require('../../assets/icon/iconfont/unread.png')}></Image>
                ) : (
                  <Image
                    style={{width: 20, height: 20, marginRight: 5}}
                    source={require('../../assets/icon/iconfont/read.png')}></Image>
                )}
                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>
                  {item.ccRecordState == '0AA' ? '未读' : '已读'}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        )}

        {/* <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该条出库吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteStorage(item.inventoryId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width:80,flexDirection:"row"}
                        ]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("MaterialInventoryOutAdd", 
                            {
                                // 传递参数
                                inventoryId:item.inventoryId,
                                classifyId: this.state.classifyId,
                                classifyName : this.state.classifyName,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle, { width:80,flexDirection:"row"}
                        ]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}
      </View>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={styles.navLeft}>
        {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/back.png')}></Image>
      </TouchableOpacity>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('MaterialInventoryOutAdd', {
            classifyId: this.state.classifyId,
            classifyName: this.state.classifyName,
            // 传递回调函数
            refresh: this.callBackFunction,
          });
        }}>
        {/* <Text style={CommonStyle.headRightText}>新增出库</Text> */}
        <Image
          style={{width: 27, height: 27}}
          source={require('../../assets/icon/iconfont/add.png')}></Image>
      </TouchableOpacity>
    );
  }

  materialLocationRow = (item, index) => {
    return (
      <View key={item.locationId}>
        <TouchableOpacity
          onPress={() => {
            var selLocationName = item.locationName;
            var selLocationId = item.locationId;
            this.setState({
              selLocationId: selLocationId,
              selLocationName: selLocationName,
            });
            let url = '/biz/material/inventory/inventoryList';
            let loadRequest = {
              currentPage: 1,
              pageSize: this.state.pageSize,
              operator: constants.loginUser.userName,
              userId: constants.loginUser.userId,
              // "classifyId": this.state.classifyId,
              io: this.state.io,
              locationId: selLocationId == 0 ? null : selLocationId,
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
          }}>
          <View
            key={item.locationId}
            style={[
              {
                width: screenWidth / (this.state.totalLocationRecord + 1),
                height: 49,
                flexDirection: 'row',
                justifyContent: 'center',
              },
              // ,item.stateCode === this.state.selCompletionStateCode ?
              //     [styles.selectedBlockItemViewStyle]
              //     :
              //     [styles.blockItemViewStyle],
            ]}>
            <Text
              style={[
                item.locationId === this.state.selLocationId
                  ? {
                      color: '#255BDA',
                      fontSize: 16,
                      fontWeight: '500',
                      lineHeight: 49,
                      textAlign: 'center',
                      borderColor: '#255BDA',
                      borderBottomWidth: 2,
                      paddingLeft: 5,
                      paddingRight: 5,
                    }
                  : {
                      color: '#2B333F',
                      fontSize: 16,
                      fontWeight: '500',
                      lineHeight: 49,
                      textAlign: 'center',
                    },
              ]}>
              {item.locationName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="原料出库"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[styles.innerViewStyle, {marginTop: 0}]}
          onLayout={this.topBlockLayout.bind(this)}>
          {this.state.parentClassifyId ? (
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginLeft: 10,
              }}>
              <Text style={styles.titleTextStyle}>
                所属库位：{this.state.selLocationName}
              </Text>
            </View>
          ) : (
            <View
              style={{
                marginTop: 0,
                index: 1000,
                flexWrap: 'wrap',
                flexDirection: 'row',
              }}>
              {this.state.materialLocationList &&
              this.state.materialLocationList.length > 0 ? (
                this.state.materialLocationList.map((item, index) => {
                  return this.materialLocationRow(item);
                })
              ) : (
                <View />
              )}
            </View>
          )}
        </View>
        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.inventoryId}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
        {/* 更多操作弹窗Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.moreModal}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0, 0, 0, 0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    if (
                      constants.loginUser.userName !=
                        this.state.modalItem.operator ||
                      this.state.modalItem.auditState != '发起审核'
                    ) {
                      WToast.show({data: '该条出库不可编辑'});
                      return;
                    }
                    this.props.navigation.navigate('MaterialInventoryOutAdd', {
                      // 传递参数
                      inventoryId: this.state.modalItem.inventoryId,
                      classifyId: this.state.classifyId,
                      classifyName: this.state.classifyName,
                      locationAreaId: this.state.modalItem.locationAreaId,
                      // 传递回调函数
                      refresh: this.callBackFunction,
                    });
                    this.setState({
                      moreModal: false,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                      constants.loginUser.userName !=
                        this.state.modalItem.operator ||
                      this.state.modalItem.auditState != '发起审核'
                        ? CommonStyle.disableViewStyle
                        : '',
                    ]}>
                    <Text
                      style={{
                        color: 'rgba(0, 10, 32, 0.85)',
                        fontSize: 18,
                        lineHeight: 52,
                      }}>
                      编辑
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    if (
                      constants.loginUser.userName !=
                        this.state.modalItem.operator ||
                      this.state.modalItem.auditState != '发起审核'
                    ) {
                      WToast.show({data: '该条出库不可删除'});
                      return;
                    }
                    this.setState({
                      moreModal: false,
                      deleteModal: true,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                      constants.loginUser.userName !=
                        this.state.modalItem.operator ||
                      this.state.modalItem.auditState != '发起审核'
                        ? CommonStyle.disableViewStyle
                        : '',
                    ]}>
                    {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                    <Text
                      style={[
                        {
                          color: 'rgba(0, 10, 32, 0.85)',
                          fontSize: 18,
                          lineHeight: 52,
                        },
                      ]}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: 291,
                  height: 50,
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      moreModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 105,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontFamily: 'PingFangSC',
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 删除弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.deleteModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 292,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>确认删除该条出库?</Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  删除后数据不可恢复，请谨慎操作
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 292,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontFamily: 'PingFangSC',
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了确定'});
                    this.deleteStorage(this.state.modalItem.inventoryId);
                  }}>
                  <View
                    style={[
                      {
                        width: 146,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontFamily: 'PingFangSC',
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    backgroundColor: '#ffffff',
    borderColor: '#ffffff',
    // borderWidth: 8
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
});
