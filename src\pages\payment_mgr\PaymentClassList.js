import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenHeight = Dimensions.get('window').height;

var screenWidth = Dimensions.get('window').width;
export default class PayTypeList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            tenantId:"",
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { paymentClassId } = route.params;
            if (paymentClassId) {
                console.log("=============paymentClassId" + paymentClassId + "");
            }
           this.loadEditpaymentClassDataList(paymentClassId);
        }
        
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/payment/class/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/payment/class/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadEditpaymentClassDataList();
    }

    loadEditpaymentClassDataList=(paymentClassId)=>{
        let url= "/biz/payment/class/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this.loadEditpaymentClassDataListCallBack);
    }


    loadEditpaymentClassDataListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deletePaymentClass =(paymentClassId)=> {
        console.log("=======delete=paymentClassId", paymentClassId);
        let url= "/biz/payment/class/delete";
        let requestParams={'paymentClassId':paymentClassId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.kilnCarId} style={styles.innerViewStyle}>
                {
                    index == 0 ?
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>
                        :
                        <View></View>
                }
                <View style={CommonStyle.titleViewStyleSpecial}>
                    <Text style={CommonStyle.titleTextStyleSpecial}>{item.paymentClassName}</Text>
                    {/* <Text style={styles.titleTextStyle}>支付类别：{item.paymentClassName}</Text> */}
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>排序：{item.paymentClassSort}</Text>
                </View>

                <View style={[CommonStyle.blockTwoEditDelStyle,{marginRight:15,marginBottom:-10}]}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该类别吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deletePaymentClass(item.paymentClassId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.btnTwoDeleteBtnView, {marginRight:0}]}>
                            <Image  style={[CommonStyle.btnTwoDeleteBtnImage]} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.btnTwoDeleteBtnText}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("PaymentClassAdd", 
                            {
                                // 传递参数
                                paymentClassId:item.paymentClassId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.btnTwoEditBtnView]}>
                            <Image  style={CommonStyle.btnTwoEditBtnImage} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.btnTwoEditBtnText}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("PaymentClassAdd", 
            //     {
            //         paymentClassId:this.state.paymentClassId,
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("PaymentClassAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    });
                }}  >
                    <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='支付类别'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        ItemSeparatorComponent={this.space}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle: {
        marginTop: 10,
        marginBottom:10,
        // borderColor: "#F4F4F4",
        // borderWidth: 8,
        // marginLeft:16,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleViewStyleSpecial: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 10,
    },
    titleTextStyle: {
        fontSize: 16
    },
    titleTextStyleSpecial: {
        width: 200,
        height: 24,
        // fontFamily: 'PingFangSC',
        fontWeight: 'bold',
        fontSize: 20,
        color: '#404956',
        lineHeight: 24,
        textAlign: 'left',
        fontStyle: 'normal',
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});