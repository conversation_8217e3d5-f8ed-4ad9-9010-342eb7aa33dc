import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,KeyboardAvoidingView,
    FlatList,RefreshControl,ScrollView,TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;

var screenHeight = Dimensions.get('window').height;
export default class HLStorageOutMAdd extends Component {
    constructor() {
        super();
        this.state = {
            storageOutId: "",
            storageOutFlag:'M',
            // 出库科室
            selDepartmentId:"",
            selectDepartmentName:[],
            selDepartmentName:"",
            departmentDataSource:[],

            // 出库类型
            selStorageOutTypeId:"",
            selectStorageOutTypeName:[],
            selStorageOutTypeName:"",
            storageOutTypeDataSource:[],

            // 日期
            storageOutDate:"",
            selectStorageOutDate:[],            
            auditDate:"",
            selectAuditDate:[],
            bookKeepingDate:"",
            selectBookKeepingDate:[],

            //出库明细列表
            hlStorageOutDetailDTOList:[],

            operator: "",
            reviewerName:"",
            reviewerId:"", 
            carrier:"",
            recipient:"",  
            bookKeeper:"",              
            receiptRemark:"",     
            
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 加载部门列表
        this.loadDepartmentList();
        // 加载出库类型列表
        this.loadStorageOutTypeList();
        //加载审核人
        this.loadreviewer();

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageOutId } = route.params;
            if (storageOutId) {
                console.log("=============storageOutId" + storageOutId + "");
                this.setState({
                    storageOutId:storageOutId,
                    operate:"编辑"
                })
                let loadTypeUrl= "/biz/hl/storage/out/get";
                let loadRequest={'storageOutId':storageOutId};
                httpPost(loadTypeUrl, loadRequest, this.loadHlStorageOutDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增",
                    operator:constants.loginUser.userName,
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectStorageOutDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    storageOutDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                    selectAuditDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    auditDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                    selectBookKeepingDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    bookKeepingDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
    }

    loadDepartmentList=()=>{
        let url= "/biz/hl/department/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    departmentDataSource: response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    loadStorageOutTypeList=()=>{
        let url= "/biz/hl/storage/out/type/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    storageOutTypeDataSource: response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    loadreviewer = ()=>{
        let loadTypeUrl= "/biz/hl/storage/out/reviewer";
        let loadRequest={
            // "storageOutFlag":this.state.storageOutFlag,
            "operaterId": constants.loginUser.userId
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    reviewerName:response.data.userName,
                    reviewerId:response.data.userId,
                })
            }
        });
    }

    loadHlStorageOutDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            // 出库日期
            var selectStorageOutDate;
            if (response.data.storageOutDate) {
                console.log("=========selectStorageOutDate=1:", response.data.storageOutDate);
                selectStorageOutDate = response.data.storageOutDate.split("-");
            }
            else {
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                selectStorageOutDate = [currentDate.getFullYear(), currentDateMonth, currentDateDay];
                this.setState({
                    selectStorageOutDate:selectStorageOutDate,
                })
                console.log("=========selectStorageOutDate===3:", selectStorageOutDate);
            }
            console.log("=========selectStorageOutDate:", selectStorageOutDate);

            // 审核日期
            var selectAuditDate;
            if (response.data.auditDate) {
                console.log("=========selectAuditDate=1:", response.data.auditDate);
                selectAuditDate = response.data.auditDate.split("-");
            }
            else {
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                selectAuditDate = [currentDate.getFullYear(), currentDateMonth, currentDateDay];
                this.setState({
                    selectAuditDate:selectAuditDate,
                })
                console.log("=========selectAuditDate===3:", selectAuditDate);
            }
            console.log("=========selectAuditDate:", selectAuditDate);

            // 记账日期
            var selectBookKeepingDate;
            if (response.data.bookKeepingDate) {
                console.log("=========selectBookKeepingDate=1:", response.data.bookKeepingDate);
                selectBookKeepingDate = response.data.bookKeepingDate.split("-");
            }
            else {
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                selectBookKeepingDate = [currentDate.getFullYear(), currentDateMonth, currentDateDay];
                this.setState({
                    selectBookKeepingDate:selectBookKeepingDate,
                })
                console.log("=========selectBookKeepingDate===3:", selectBookKeepingDate);
            }
            console.log("=========selectBookKeepingDate:", selectBookKeepingDate);

            this.setState({
                storageOutId:response.data.storageOutId,
                storageOutDate:response.data.storageOutDate,
                selectStorageOutDate:selectStorageOutDate,               
                storageOutFlag:response.data.storageOutFlag,
                selDepartmentId:response.data.toDepartmentId,
                selDepartmentName:response.data.departmentName,
                selectDepartmentName:[response.data.departmentName],
                selStorageOutTypeId:response.data.storageOutTypeId,
                selStorageOutTypeName:response.data.storageOutTypeName,
                selectStorageOutTypeName:[response.data.storageOutTypeName],
                operator: response.data.operator,
                carrier:response.data.carrier,
                recipient:response.data.recipient,
                bookKeeper:response.data.bookKeeper,
                auditDate:response.data.auditDate,
                selectAuditDate:response.data.selectAuditDate,
                bookKeepingDate:response.data.bookKeepingDate,
                selectBookKeepingDate:selectBookKeepingDate,
                receiptRemark:response.data.receiptRemark,
            })
            if (response.data.hlStorageOutDetailDTOList && response.data.hlStorageOutDetailDTOList.length > 0) {
                this.setState({
                    // 出库详细
                    hlStorageOutDetailDTOList:response.data.hlStorageOutDetailDTOList,
                })
            }
            // console.log("--------明细"+ hlStorageOutDetailDTOList)
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("HLStorageOutMList")
            }}>
                <Text style={CommonStyle.headRightText}>物资出库</Text>
            </TouchableOpacity>
        )
    }

    openStorageOutDate(){
        this.refs.SelectStorageOutDate.showDate(this.state.selectStorageOutDate)
    }
    callBackSelectStorageOutDateValue(value){
        console.log("==========出库日期选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectStorageOutDate:value
        })
        if (this.state.selectStorageOutDate && this.state.selectStorageOutDate.length) {
            var storageOutDate = "";
            var vartime;
            for(var index=0;index<this.state.selectStorageOutDate.length;index++) {
                vartime = this.state.selectStorageOutDate[index];
                if (index===0) {
                    storageOutDate += vartime;
                }
                else{
                    storageOutDate += "-" + vartime;
                }
            }
            this.setState({
                storageOutDate:storageOutDate
            })
        }
    }

    openAuditDate(){
        this.refs.SelectAuditDate.showDate(this.state.selectAuditDate)
    }
    callBackSelectAuditDateValue(value){
        console.log("==========审核日期选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectAuditDate:value
        })
        if (this.state.selectAuditDate && this.state.selectAuditDate.length) {
            var auditDate = "";
            var vartime;
            for(var index=0;index<this.state.selectAuditDate.length;index++) {
                vartime = this.state.selectAuditDate[index];
                if (index===0) {
                    auditDate += vartime;
                }
                else{
                    auditDate += "-" + vartime;
                }
            }
            this.setState({
                auditDate:auditDate
            })
        }
    }

    openBookKeepingDate(){
        this.refs.SelectBookKeepingDate.showDate(this.state.selectBookKeepingDate)
    }
    callBackSelectBookKeepingDateValue(value){
        console.log("==========出库日期选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectBookKeepingDate:value
        })
        if (this.state.selectBookKeepingDate && this.state.selectBookKeepingDate.length) {
            var bookKeepingDate = "";
            var vartime;
            for(var index=0;index<this.state.selectBookKeepingDate.length;index++) {
                vartime = this.state.selectBookKeepingDate[index];
                if (index===0) {
                    bookKeepingDate += vartime;
                }
                else{
                    bookKeepingDate += "-" + vartime;
                }
            }
            this.setState({
                bookKeepingDate:bookKeepingDate
            })
        }
    }

    saveHlStorageOutM =()=> {
        console.log("=======saveHlStorageOutM");
        let toastOpts;
        if (!this.state.selDepartmentId) {
            toastOpts = getFailToastOpts("请选择科室名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selStorageOutTypeId) {
            toastOpts = getFailToastOpts("请选择出库类型名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.carrier) {
            toastOpts = getFailToastOpts("请填写运送人");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.recipient) {
            toastOpts = getFailToastOpts("请填写领用人");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.bookKeeper) {
        //     toastOpts = getFailToastOpts("请填写记账人");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.hlStorageOutDetailDTOList || this.state.hlStorageOutDetailDTOList.length < 1) {
            toastOpts = getFailToastOpts("至少新增一条明细");
            WToast.show(toastOpts)
            return;
        }
        
        let url= "/biz/hl/storage/out/add";
        if (this.state.storageOutId) {
            console.log("=========Edit=save===storageOutId", this.state.storageOutId)
            url= "/biz/hl/storage/out/modify";
        }
        let requestParams={
            "storageOutId": this.state.storageOutId,
            "toDepartmentId": this.state.selDepartmentId,
            "storageOutDate": this.state.storageOutDate,  
            "storageOutTypeId": this.state.selStorageOutTypeId,
            "storageOutFlag": this.state.storageOutFlag,
            "operator": this.state.operator,
            "carrier": this.state.carrier,          
            "recipient": this.state.recipient,
            "operatorId":constants.loginUser.userId,
            "currentAuditUserId": this.state.reviewerId,           
            "auditDate": this.state.auditDate,
            "bookKeepingDate":this.state.bookKeepingDate,
            "bookKeeper":this.state.bookKeeper,
            "receiptRemark":this.state.receiptRemark,
            "hlStorageOutDetailDTOList":this.state.hlStorageOutDetailDTOList,
            "sourceId":1,


        };
        httpPost(url, requestParams, this.saveStorageCallBack);
    }
    
    // 保存回调函数
    saveStorageCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 渲染客户底部滚动数据
    openDepartmentName() {
        if (!this.state.departmentDataSource || this.state.departmentDataSource.length < 1) {
            WToast.show({ data: "请先添加部门" });
            return
        }
        this.refs.SelectDepartmentName.showDepartmentName(this.state.selectDepartmentName, this.state.departmentDataSource)
    }

    callBackSelectDepartmentNameValue(value) {
        console.log("==========部门选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectDepartmentName: value,
            selDepartmentName:value.toString()
        })
        var departmentName = value.toString();
        let loadUrl = "/biz/hl/department/getDepartmentByName";
        let loadRequest = {
            "departmentName": departmentName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadDepartmentDetailData);
    }

    callBackLoadDepartmentDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                selDepartmentName: response.data.departmentName,
                selDepartmentId:response.data.departmentId,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    
    // 渲染客户底部滚动数据
    openStorageOutTypeName() {
        if (!this.state.storageOutTypeDataSource || this.state.storageOutTypeDataSource.length < 1) {
            WToast.show({ data: "请先添加出库类型" });
            return
        }
        this.refs.SelectStorageOutTypeName.showStorageOutTypeName(this.state.selectStorageOutTypeName, this.state.storageOutTypeDataSource)
    }

    callBackSelectStorageOutTypeNameValue(value) {
        console.log("==========出库类型选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectStorageOutTypeName: value,
            selStorageOutTypeName:value.toString()
        })
        var storageOutTypeName = value.toString();
        let loadUrl = "/biz/hl/storage/out/type/getStorageOutTypeByName";
        let loadRequest = {
            "storageOutTypeName": storageOutTypeName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadStorageOutTypeDetailData);
    }

    callBackLoadStorageOutTypeDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                selStorageOutTypeName: response.data.storageOutTypeName,
                selStorageOutTypeId:response.data.storageOutTypeId,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }  

    _loadFreshHlStorageOutDetailDTOList=(_hlStorageOutDetailDTOList)=>{
        if (_hlStorageOutDetailDTOList && _hlStorageOutDetailDTOList.length > 0) {
            console.log("=========回退数据：", _hlStorageOutDetailDTOList);
            this.setState({
                hlStorageOutDetailDTOList:_hlStorageOutDetailDTOList,
            })
        }
        else {
            console.log("=========回退不成功");
        }
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '出库'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.formContentViewStyle]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库科室</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openDepartmentName()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30) }]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.selDepartmentName ? "请选择出库科室" : this.state.selDepartmentName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>                    
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库日期</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openStorageOutDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,, { width: screenWidth - (leftLabWidth + 30) }]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.storageOutDate ? "请选择出库日期" : this.state.storageOutDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库类型</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openStorageOutTypeName()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30) }]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.selStorageOutTypeName ? "请选择出库类型" : this.state.selStorageOutTypeName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>经办人</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={[styles.inputRightText]}
                            placeholder={'请输入经办人'}
                            onChangeText={(text) => this.setState({ operator: text })}
                        >
                            {this.state.operator}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>运送人</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={[styles.inputRightText]}
                            placeholder={'请输入运送人'}
                            onChangeText={(text) => this.setState({ carrier: text })}
                        >
                            {this.state.carrier}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>领用人</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={[styles.inputRightText]}
                            placeholder={'请输入领用人'}
                            onChangeText={(text) => this.setState({ recipient: text })}
                        >
                            {this.state.recipient}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>单据备注</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        {/* <TextInput                             
                            placeholder={'请输入单据备注'}
                            onChangeText={(text) => this.setState({receiptRemark:text})}
                            style={[styles.inputRightText]}
                        >
                            {this.state.receiptRemark}
                        </TextInput> */}
                    </View>
                    <View style={[styles.inputRowStyle,{height:100}]}>
                        <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            placeholder={'请输入单据备注'}
                            onChangeText={(text) => this.setState({receiptRemark:text})}
                            style={[CommonStyle.inputRowText,{height:100,width:screenWidth - (leftLabWidth/3)}]}
                        >
                            {this.state.receiptRemark}
                        </TextInput>
                    </View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>记账人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput                             
                            placeholder={'请输入记账人'}
                            onChangeText={(text) => this.setState({bookKeeper:text})}
                            style={[styles.inputRightText]}
                        >
                            {this.state.bookKeeper}
                        </TextInput>
                    </View>                    
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>审核日期</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openAuditDate()}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.auditDate ? "请选择审核时间" : this.state.auditDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>记账日期</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openBookKeepingDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30) }]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.bookKeepingDate ? "请选择记账时间" : this.state.bookKeepingDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>                    

                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>审核人</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            editable={false}
                            //keyboardType='text'
                            style={[styles.inputRightText]}
                            placeholder={'请输入审核人'}
                            onChangeText={(text) => this.setState({ reviewerName: text })}
                        >
                            {this.state.reviewerName}
                        </TextInput>
                    </View>

                    <View style={CommonStyle.rowSplitViewStyle}></View>
                    <View style={styles.btnRowView}>                        
                        <TouchableOpacity onPress={()=>{
                            // if (this.state.storageOutId) {
                                this.props.navigation.navigate("HLStorageOutAddDetail", 
                            {
                                // 传递参数
                                storageOutId:this.state.storageOutId,
                                storageOutFlag:"M",
                                hlStorageOutDetailDTOList:this.state.hlStorageOutDetailDTOList,
                                // 传递回调函数
                                refresh: this._loadFreshHlStorageOutDetailDTOList 
                            })
                        // }
                        }}>
                            <View style={[styles.btnAddView]}>
                                <Text style={styles.btnAddText}>+ 出库明细</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View>
                        <FlatList 
                        data={this.state.hlStorageOutDetailDTOList}
                        renderItem={({item}) => 
                        <View key={item._index} style={styles.titleViewStyle}>

                            <View style={{marginTop:10 }}>
                                <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                    名称：{item.materialName}
                                </Text>
                            </View>
                            <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10,marginTop:10}]}>
                                <View style={[styles.itemContentChildViewStyle]}>
                                    <Text style={styles.titleTextStyle}>规格型号：{item.materialModal}</Text>
                                </View>
                                <View style={[styles.itemContentChildViewStyle]}>
                                    <Text style={styles.titleTextStyle}>单位：{item.unitName}</Text>
                                </View>
                                <View style={[styles.itemContentChildViewStyle]}>
                                    <Text style={styles.titleTextStyle}>数量：{item.materialAmount}</Text>
                                </View>
                                <View style={[styles.itemContentChildViewStyle]}>
                                    <Text style={styles.titleTextStyle}>价格：{item.materialPrice}</Text>
                                </View>
                                <View style={[styles.itemContentChildViewStyle]}>
                                    <Text style={[styles.titleTextStyle]}>金额：{item.materialTotalPrice}</Text>
                                </View>
                            </View>

                            <TouchableOpacity
                                style={{marginLeft:-145,zIndex:1000,marginTop:130}}
                                onPress={() => {
                                    console.log("========deleteStorageInDetailDTO")
                                    var urls = this.state.hlStorageOutDetailDTOList;
                                    urls.splice(item._index,1);                   
                                    console.log(urls)
                                    this.setState({
                                        hlStorageOutDetailDTOList:urls,
                                    })
                                }}
                            >
                                <View style={styles.btnDeleteView}>
                                    <Text style={styles.btnDeleteText}>-删除</Text>
                                </View>
                            </TouchableOpacity>

                        </View>
                        }
                        />
                    </View>
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row', width: 130, height: 40, marginLeft: 35, marginTop: 15 }]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveHlStorageOutM.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView, { flexDirection: 'row', width: 130, height: 40, marginRight: 35, marginTop: 15 }]}>
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <BottomScrollSelect 
                        ref={'SelectDepartmentName'} 
                        callBackDepartmentNameValue={this.callBackSelectDepartmentNameValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectStorageOutTypeName'} 
                        callBackStorageOutTypeNameValue={this.callBackSelectStorageOutTypeNameValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectStorageOutDate'} 
                        callBackDateValue={this.callBackSelectStorageOutDateValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectAuditDate'} 
                        callBackDateValue={this.callBackSelectAuditDateValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectBookKeepingDate'} 
                        callBackDateValue={this.callBackSelectBookKeepingDateValue.bind(this)}
                    />
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({

    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },


    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }

});