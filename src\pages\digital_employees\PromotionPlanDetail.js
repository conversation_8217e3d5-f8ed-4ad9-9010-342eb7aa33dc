import React,{Component} from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,TextInput,
    FlatList, RefreshControl, Linking, Clipboard, Image, Modal,ScrollView,KeyboardAvoidingView, ImageBackground
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
// import EmptyListComponent from '../../component/EmptyRowViewComponent';
// import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

import moment from 'moment';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
var currentDate = (moment(new Date()).format('YYYY-MM-DD'));
export default class PromotionPlanDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            currentTime: "",
            showSearchItemBlock: false,

            planId: "",
            planTitle: "",
            planContent: "",
            completionState: "",
            checkInUserId: "",
            checkInUserName: "",
            belongClassDataSource: [],
            actualCompletionTime: "",
            planCreatedTime: "",

            userId: "",
            userName: "",
            gmtCreated: "",
            userPhoto: "",
            userPhotoUrl: "",
            checkInUserPhoto: "",
            operatorPhotoUrl:'',
            moreModal:false,
            deleteModal: false,
            trackDetailModal: false,

            trackFkId: null,
            trackType: "",
            trackDetailList: []
        }
    }

    initGmtCreated = () => {
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var currentHour = ("0" + (currentDate.getHours() + 8)).slice(-2);
        var currentMinute = ("0" + currentDate.getMinutes()).slice(-2);
        var currentSecond = ("0" + currentDate.getSeconds()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay + " " + currentHour + ":" + currentMinute + ":" + currentSecond;
        return _gmtCreated;
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        var currentTime = this.initGmtCreated();
        this.setState({
            currentTime: currentTime
        })
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { planId, userName, trackFkId, trackType } = route.params;
            if (planId) {
                console.log('planId================', planId)
                this.setState({
                    planId: planId,
                    userName: userName,
                    trackFkId: trackFkId,
                    trackType: trackType
                })
                loadTypeUrl = "/biz/promotion/plan/get";
                loadRequest = { 'planId': planId };
                httpPost(loadTypeUrl, loadRequest, this.loadPromotionPlanDataCallBack);
            }
            this.loadTractList(trackFkId, trackType);
        }
    }

    loadPromotionPlanDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            var selectedPlannedCompletionTime;
            if (response.data.plannedCompletionTime) {
                selectedPlannedCompletionTime = response.data.plannedCompletionTime.split("-");
            }
            this.setState({
                planId: response.data.planId,
                planTitle: response.data.planTitle,
                completionState: response.data.completionState,
                planContent: response.data.planContent,
                plannedCompletionTime: response.data.plannedCompletionTime,
                actualCompletionTime: response.data.actualCompletionTime,
                selectedPlannedCompletionTime: selectedPlannedCompletionTime,
                userId: response.data.userId,
                userName: response.data.userName,
                userPhoto: response.data.userPhoto,
                checkInUserName: response.data.checkInUserName,
                checkInUserPhoto: response.data.checkInUserPhoto,
                gmtCreated: response.data.gmtCreated,
                planCreatedTime: response.data.planCreatedTime,
                belongClassName: response.data.belongClassName,
            })
        }
    }

    loadTractList = (trackFkId, trackType) => {
        let url = "/biz/track/detail/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "trackFkId": trackFkId ? trackFkId : this.state.trackFkId,
            "trackType": trackType ? trackType : this.state.trackType,
        };
        httpPost(url, loadRequest, this.loadTractListCallBack);
    }

    loadTractListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            console.log('trackDetailList' , response.data)
            this.setState({
                trackDetailList: response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    //头部右侧
    renderRightItem() {
        return (
            constants.loginUser.userId == this.state.userId ?
                <TouchableOpacity onPress={() => {
                    this.setState({
                        moreModal: true,
                    })
                }}>
                    <Image style={{ width: 28, height: 28}} source={require('../../assets/icon/iconfont/more.png')}></Image>
                </TouchableOpacity>
                :
                <View />
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }
    
    // 保存进展
    saveTrackDetail =()=> {
        console.log("=======saveTrackDetail");
        let toastOpts;
        if (!this.state.trackRemark) {
            toastOpts = getFailToastOpts("请输入进展说明");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/track/detail/add";
        let requestParams={
            trackRemark: this.state.trackRemark,
            trackType: this.state.trackType,
            trackFkId: this.state.trackFkId,
            userId: constants.loginUser.userId
        };
        httpPost(url, requestParams, this.saveTrackDetailCallBack);
    }
    
    // 保存回调函数
    saveTrackDetailCallBack=(response)=>{
        this.setState({
            trackRemark: ""
        })
        let toastOpts;
        switch (response.code) {
            case 200:
                WToast.show({ data: "进展提交成功" });
                this.loadTractList(this.state.trackFkId, this.state.trackType);
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    deletePromotionPlan = (planId) => {
        console.log("=======delete=planId", planId);
        let url = "/biz/promotion/plan/delete";
        let requestParams = { 'planId': planId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            if (this.props.route.params.refresh) {
                this.props.route.params.refresh();
            }
            WToast.show({ data: "删除完成" });
            this.props.navigation.goBack()
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    render(){
        return (
            <View style={{height:screenHeight}}>
                <CommonHeadScreen title={this.state.userName + "的任务"}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <ScrollView>
                    <View style={{backgroundColor: '#ffffffff' ,borderRadius:20,margin:10}}>
                    {/* 任务顶部信息 */}
                    <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
                        {
                            this.state.userPhoto ?
                                <Image source={{ uri: (constants.image_addr + '/' + this.state.userPhoto) }} style={{ height: 48, width: 48, borderRadius: 50}} />
                                :
                                <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                    <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                        {
                                            this.state.userName.length <= 2 ? 
                                            <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                {this.state.userName}
                                            </Text>
                                            :
                                            <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                {this.state.userName.slice(-2)}
                                            </Text>
                                        }
                                    </View>
                                </ImageBackground>
                        }
                        
                        <View style={{marginLeft:11, flexDirection: 'column'}}>
                            <View style={{flexDirection: 'row', marginTop: 4 }}>
                                <View style={{ flexDirection: 'row'}}>
                                    <Text style={{ fontSize: 16 }}>{this.state.userName}的任务</Text>
                                </View>
                            </View>

                            <View style={{flexDirection: 'row'}}>
                            <Image style={{ height: 13 , width: 12, marginTop: 5, marginLeft: 1, marginRight: 5}} source={require('../../assets/icon/iconfont/clock.png')}></Image> 
                                <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                                    <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)'}]}>{this.state.planCreatedTime} 提交</Text>
                                </View>
                            </View>
                        </View>
                        <View style={{ position:'absolute', right: 25, top: 10,flexDirection: 'column'}}>
                                                  <View style={{flexDirection: 'row',marginBottom:3}}>
                                                        {
                                                            this.state.completionState === 'C' ?
                                                                <View style={{ width: 52, height: 20, marginLeft: 7, borderRadius: 15, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: '#1BBC82' }}>
                                                                    <Text style={{fontSize: 13, color: '#FFFFFF' }}>已完成</Text>
                                                                </View>
                                                                :
                                                                <View style={{ width: 52, height: 20, marginLeft: 7, borderRadius: 15, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor: '#D50400' }}>
                                                                    <Text style={{fontSize: 13, color: '#FFFFFF' }}>未完成</Text>
                                                                </View>
                                                        }
                                                    </View>
                                                    </View>
                    </View>

                    {/* 分隔线 */}
                    <View style={styles.lineViewStyle}/>
                    
                    <View style={styles.titleViewStyle}>
                                       {<Text style={styles.titleTextStyle}>标题</Text>}
                                       <View style={{ height: 24, paddingLeft: 6, paddingRight: 6, position:'absolute', right: 0, top:-1, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center',borderColor: '#1E6EFA',borderWidth:1}}>
                                           <Text style={ {fontSize:14,color: '#1E6EFA'}}>{this.state.belongClassName}</Text>
                                       </View>
                                   </View>
                    <View style={styles.itemContentTextStyle}>
                        <Text style={styles.itemContentStyle}>{this.state.planTitle}</Text>
                    </View>
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>内容</Text>
                    </View>
                    <View style={[styles.itemContentTextStyle, { marginBottom: 5 }]}>
                        <Text style={styles.itemContentStyle}>{this.state.planContent}</Text>
                    </View>
                    {
                        constants.loginUser.userId == this.state.userId && this.state.completionState != 'C' ?
                            <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            trackDetailModal: true
                                        })
                                    }}>
                                        <View style={[{width: 78, height: 28, flexDirection: "row", alignItems: 'center', margin: 10, 
                                            marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                                        }]}>
                                            <Image style={{ width: 20, height: 20, marginRight: 8, marginLeft: 12 }} source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                                            <Text style={[{ color: 'rgba(0, 10, 32, 0.65)', fontSize: 14, lineHeight: 24 }]}>进展</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            :
                            <View/>
                    }
                    </View>
                    {/* 流程 */}
                    <View style={{backgroundColor: '#ffffffff' ,borderRadius:20,margin:10,paddingVertical: 10 }}>
                    <View style={[styles.titleViewStyle, { marginLeft: 21, alignItems: 'center' }]}>
                        <Text style={styles.titleTextStyle}>流程</Text>
                    </View>

                    <View style={{flexDirection: 'row', marginLeft: 21, marginTop: 14, marginRight: 16, marginBottom: 30}}>
                        {
                            this.state.userPhoto ?
                                <Image source={{ uri: (constants.image_addr + '/' + this.state.userPhoto) }} style={{ height: 48, width: 48, borderRadius: 50}} />
                                :
                                <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                    <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                        {
                                            this.state.userName.length <= 2 ? 
                                            <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                {this.state.userName}
                                            </Text>
                                            :
                                            <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                {this.state.userName.slice(-2)}
                                            </Text>
                                        }
                                    </View>
                                </ImageBackground>
                        }
                        
                        <View style={{marginLeft:8, flexDirection: 'column'}}>
                            <View style={{flexDirection: 'row', marginTop: 1}}>
                                <Text style={{ fontSize: 16, lineHeight: 20, color: 'rgba(0, 10, 32, 0.85)' }}>提交任务</Text>
                            </View>

                            <View style={{flexDirection: 'row', marginTop: 4}}>
                                <Text style={{fontSize: 14, lineHeight: 20, color: 'rgba(0, 10, 32, 0.65)' }}>{this.state.userName}</Text>
                            </View>
                        </View>

                        <View style={{flexDirection: 'row', position: 'absolute', right: 0, top: 5}}>
                            <Text style={[{fontSize: 12, color:'rgba(0,10,32,0.45)'}]}>{this.state.gmtCreated.slice(0,16)}</Text>
                        </View>
                        </View>{
                        this.state.completionState === 'C' ?
                            <View style={{flexDirection: 'row', marginLeft: 21, marginRight: 16, marginBottom: 4}}>
                                {
                                    this.state.checkInUserPhoto ?
                                        <Image source={{ uri: (constants.image_addr + '/' + this.state.checkInUserPhoto) }} style={{ height: 48, width: 48, borderRadius: 50}} />
                                        :
                                        <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                            <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                                {
                                                    this.state.checkInUserName ? 
                                                    (
                                                        this.state.checkInUserName.length <= 2 ? 
                                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                            {this.state.checkInUserName}
                                                        </Text>
                                                        :
                                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                            {this.state.checkInUserName.slice(-2)}
                                                        </Text>
                                                    )
                                                    :
                                                    <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                        {"无"}
                                                    </Text>
                                                }
                                            </View>
                                        </ImageBackground>
                                }

                                <View style={{marginLeft:8, flexDirection: 'column'}}>
                                    <View style={{flexDirection: 'row', marginTop: 1}}>
                                        <Text style={{ fontSize: 16, lineHeight: 20, color: 'rgba(0, 10, 32, 0.85)' }}>关闭人</Text>
                                    </View>

                                    <View style={{flexDirection: 'row', marginTop: 4}}>
                                        <Text style={[{fontSize: 14, lineHeight: 20, color: 'rgba(0, 10, 32, 0.65)' }]}>{this.state.checkInUserName}（关闭任务）</Text>
                                    </View>
                                </View>

                                <View style={{flexDirection: 'row', position: 'absolute', right: 0, top: 5}}>
                                    <Text style={[{fontSize: 12, color:'rgba(0,10,32,0.45)'}]}>{this.state.actualCompletionTime}</Text>
                                </View>
                            </View>
                            :
                            <View style={{flexDirection: 'row', marginLeft: 21, marginTop: 11, marginRight: 16}}>
                                {
                                    this.state.checkInUserPhoto ?
                                        <Image source={{ uri: (constants.image_addr + '/' + this.state.checkInUserPhoto) }} style={{ height: 48, width: 48, borderRadius: 50}} />
                                        :
                                        <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ width: 48, height: 48}}>
                                            <View style={{height: 48,width:48,justifyContent: "center",alignItems: "center"}}>
                                                {
                                                    this.state.checkInUserName ? 
                                                    (
                                                        this.state.checkInUserName.length <= 2 ? 
                                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                            {this.state.checkInUserName}
                                                        </Text>
                                                        :
                                                        <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                            {this.state.checkInUserName.slice(-2)}
                                                        </Text>
                                                    )
                                                    :
                                                    <Text style={{color:'#FFFFFF',fontSize:17,fontWeight:"normal",textAlign:'center', lineHeight:22}}>
                                                        {"无"}
                                                    </Text>
                                                }
                                            </View>
                                        </ImageBackground>
                                }
                                
                                <View style={{marginLeft:8, flexDirection: 'column'}}>
                                    <View style={{flexDirection: 'row', marginTop: 1}}>
                                        <Text style={{ fontSize: 16, lineHeight: 20, color: 'rgba(0, 10, 32, 0.85)' }}>关闭人</Text>
                                    </View>

                                    <View style={{flexDirection: 'row', marginTop: 4}}>
                                        <Text style={[{fontSize: 14, lineHeight: 20, color: 'rgba(0, 10, 32, 0.65)' }]}>{this.state.checkInUserName}（未处理）</Text>
                                    </View>
                                </View>
                            </View>
                    }
                    </View>
                    {/* 留言 */}
                    <View style={{backgroundColor: '#ffffffff' ,borderRadius:20,margin:10}}>
                    <View style={[styles.titleViewStyle, { marginLeft: 21, alignItems: 'center', paddingBottom:10}]}>
                        <Text style={styles.titleTextStyle}>任务进展</Text>
                    </View>
                    {
                        (this.state.trackDetailList && this.state.trackDetailList.length > 0) ?
                            <View style={{backgroundColor:'rgba(242, 245, 252, 0.5)', borderRadius:10,width:screenWidth-24, marginLeft: 12, marginRight: 12, paddingTop: 5, marginBottom: 5}}>
                                {
                                    this.state.trackDetailList.map((item, index)=>{
                                        return(
                                            <View key={item.trackId} style={{ flexDirection: 'row', marginLeft: 10, marginTop: 10, marginRight: 6, marginBottom: 10}}>
                                                {
                                                    this.state.userPhoto ?
                                                        <Image source={{ uri: (constants.image_addr + '/' + this.state.userPhoto) }} style={{ height: 36, width: 36, borderRadius: 50}} />
                                                        :
                                                        <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ height: 36, width: 36}}>
                                                            <View style={{height: 36, width: 36,justifyContent: "center",alignItems: "center"}}>
                                                                {
                                                                    this.state.userName <= 2 ? 
                                                                    <Text style={{color:'#FFFFFF',fontSize:13,fontFamily:'PingFangSC-Regular',fontWeight:"normal",textAlign:'center', lineHeight:20}}>
                                                                        {this.state.userName}
                                                                    </Text>
                                                                    :
                                                                    <Text style={{color:'#FFFFFF',fontSize:13,fontFamily:'PingFangSC-Regular',fontWeight:"normal",textAlign:'center', lineHeight:20,}}>
                                                                        {this.state.userName.slice(-2)}
                                                                    </Text>
                                                                }
                                                            </View>
                                                        </ImageBackground>
                                                }

                                                <View style={{ flexDirection: 'column', marginLeft: 10, flex: 1}}>
                                                    <View style={{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'center', paddingTop: 4 }}>
                                                        <View style={{ flexDirection: 'row'}}>
                                                            <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16 }}>{this.state.userName}</Text>
                                                        </View>
                                                        <View style={{ flexDirection: 'row', marginLeft: 6}}>
                                                            <Text style={[{ fontSize: 12, color: 'rgba(0,10,32,0.45)' }]}>{item.gmtCreated.slice(0,16)}</Text>
                                                        </View>
                                                        
                                                    </View>

                                                    <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 10}]}>
                                                        <Text style={styles.itemContentStyle}>{item.trackRemark ? item.trackRemark : "无"}</Text>
                                                    </View>
                                                </View>
                                            </View>
                                        )                           
                                    })
                                }
                            </View>
                            :
                            <View/>
                    }
                      </View>

                </ScrollView>
              
                {/* 分隔线 */}
                <View style={[styles.lineViewStyle, {paddingBottom: 60, backgroundColor: '#FFFFFF'}]}/>

                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.6)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            
                            <View>
                                <TouchableOpacity onPress={() => {
                                    if (this.state.completionState === 'C' || dateDiffHours(this.state.currentTime, this.state.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                        WToast.show({ data: '该任务不可编辑' });
                                        return;
                                    }
                                    this.setState({
                                        moreModal: false,
                                    })
                                    this.props.navigation.navigate("PromotionPlanAdd",
                                        {
                                            // 传递参数
                                            planId: this.state.planId,
                                            checkInUserName: this.state.checkInUserName,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , (this.state.completionState === 'C' || dateDiffHours(this.state.currentTime, this.state.gmtCreated) > constants.loginUser.editDeleteTimeLimit) ? CommonStyle.disableViewStyle : ""]}>
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => {
                                    if (dateDiffHours(this.state.currentTime, this.state.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                                        WToast.show({ data: '任务已超出删除时限' });
                                        return;
                                    }
                                    // 删除弹窗Modal
                                    this.setState({
                                        moreModal: false,
                                        deleteModal: true
                                    })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , dateDiffHours(this.state.currentTime, this.state.gmtCreated) > constants.loginUser.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""]}>
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                                
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.6)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该日报?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deletePromotionPlan(this.state.planId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontFamily: 'PingFangSC', fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                
                {/* 进展说明输入框弹窗 */}
                <Modal
                    animationType='slide'
                    transparent={true}
                    visible={this.state.trackDetailModal}
                >
                    <TouchableOpacity style={{flex: 1, position: 'relative'}}
                        onPress={() => {
                            this.setState({
                                trackDetailModal: false,
                                trackRemark: ""
                            })
                    }}>
                        <View style={{backgroundColor: '#FFFFFF', flexDirection: 'row', alignItems: 'center',
                            position: 'absolute', width: '100%', left: 0, bottom: 0, padding: 5
                        }}>
                            <TextInput 
                                autoFocus
                                multiline={true}
                                placeholder="提交进展，快速迭代"
                                style={{backgroundColor: '#F2F5FC', flex: 5, borderRadius: 15, height: 40, marginLeft: 10, paddingLeft: 15}} 
                                onChangeText={(text) => this.setState({ trackRemark: text })}
                            />
                            <TouchableOpacity onPress={() => {
                                if (!this.state.trackRemark) {
                                    return;
                                }
                                this.setState({
                                    trackDetailModal: false,
                                })
                                this.saveTrackDetail();
                            }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{flex: 1,width: 64, height: 32, backgroundColor: '#1E6EFA', borderRadius: 20 }, 
                                    (this.state.trackRemark) ? "" : CommonStyle.disableViewStyle]}>
                                    <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { textAlign: 'center', fontSize: 14 }]}>提交</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </TouchableOpacity>
                </Modal>

            </View>
        )
    }
}
const styles = StyleSheet.create({
    headline:{
        height:75,
        // paddingLeft:13,
        // paddingRight:13,
        flexDirection:'row',
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 14,
        marginRight: 16,
        marginTop: 5
    },
    titleTextStyle: {
        fontSize: 16,
        lineHeight: 22
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        // color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },
    holdbtnView: {
        fontSize: 16, width: 60, height: 30,
        borderWidth: 1,
        borderColor: 'rgba(250, 250, 250, 1)',
        backgroundColor: 'rgba(30, 110, 250, 1)',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 5,
        borderRadius: 4,
        flexDirection: 'row'
    },
    holdBtnText: {
        color: 'rgba(250, 250, 250, 1)', fontSize: 16
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        marginBottom: 6,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
    itemContentTextStyle: {
        marginLeft: 14,
        marginRight: 16,
        marginTop: 3
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
     verticalDashedLine: {
        height: 40,           // 虚线高度（可根据需求调整）
        width: 1,             // 虚线宽度（竖线）
        borderLeftWidth: 1,   // 左边框宽度
        borderLeftColor: '#1E6EFA', // 蓝色边框
        borderStyle: 'dashed',      // 虚线样式
        alignSelf: 'center',        // 水平居中
        marginVertical: 8           // 上下边距
    },
});