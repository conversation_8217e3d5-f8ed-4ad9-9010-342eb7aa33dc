import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class HLMedicineStorageInList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    
    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        this.loadHlMedicineStorageInList();
    }

    loadHlMedicineStorageInList=(storageInId)=>{
        let url= "/biz/hl/medicine/storage/in/storageInList";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "operator":constants.loginUser.userName,
            "userId":constants.loginUser.userId,
            "storageInId": this.state.storageInId,
        };
        httpPost(url, loadRequest, this.loadHlMedicineStorageInListCallBack);
    }
    loadHlMedicineStorageInListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/hl/medicine/storage/in/storageInList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            // "supplierId": this.state.supplierId,
            // "currentAuditUserId": this.state.currentAuditUserId,
            "operator":constants.loginUser.userName,
            "userId":constants.loginUser.userId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/hl/medicine/storage/in/storageInList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            // "supplierId": this.state.supplierId,
            // "currentAuditUserId": this.state.currentAuditUserId,
            "operator":constants.loginUser.userName,
            "userId":constants.loginUser.userId,

        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadHlMedicineStorageInList();
    }
    deleteHlMedicineStorageIn =(storageInId)=> {
        console.log("=======delete=storageInId", storageInId);
        let url= "/biz/hl/medicine/storage/in/delete";
        let requestParams={'storageInId':storageInId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }
    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return(
            <View key={item.equipmentId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>供货单位：{item.supplierName}</Text>
                </View>               
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>入库日期：{item.storageInDate}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>入库药房：{item.pharmacyName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>药房位置：{item.pharmacyAddr?item.pharmacyAddr:"尚未完善"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>所属院区：{item.hospitalName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>经办人：{item.operator}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>单据备注：{item.receiptRemark?item.receiptRemark :"无"}</Text>
                </View>
                {
                    item.bookKeeper?
                    <View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>记账人：{item.bookKeeper?item.bookKeeper:"无"}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>记账日期：{item.bookKeeper?(item.bookKeepingDate?item.bookKeepingDate:"无"):"无"}</Text>
                        </View>
                    </View>
                    :
                    <View/>
                }
                {
                    item.currentAuditUserId ?
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>审核人：{item.auditUserName}</Text>
                        <View>
                            {
                                item.auditState == "发起审核" || item.auditState == "审核中" ? 
                                <Text style={{color:'#FFB800'}}>{item.auditState}</Text> 
                                :
                                <View>
                                {
                                    item.auditState == "审核通过" ? 
                                    <Text style={{color:'green'}}>{item.auditState}</Text>
                                    :
                                    <Text style={{color:'#CB4139'}}>{item.auditState}</Text> 
                                }
                                </View>
                            }
                        </View>
                    </View> :
                    <View/>
                }
                
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    <TouchableOpacity onPress={()=>{
                        this.props.navigation.navigate("HLMedicineStorageInDetailList", 
                        {
                            // 传递参数
                            storageInId:item.storageInId,
                            
                            // 传递回调函数
                            refresh: this.callBackFunction 
                        })
                        }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {backgroundColor:"#3ab240",marginLeft:0,width: 70 ,flexDirection:"row"}]}>
                            <Image  style={{width:25, height:25,marginRight:2}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>明细</Text>
                        </View>
                    </TouchableOpacity>
                    {/* <TouchableOpacity onPress={()=>{
                        this.props.navigation.navigate("HLStorageInAuditDetail", 
                        {
                            // 传递参数
                            auditItemId:item.storageInId,
                            
                            // 传递回调函数
                            refresh: this.callBackFunction 
                        })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{backgroundColor:"#5eaef5",width:75,flexDirection:"row"}]}>
                            <Image  style={{width:25, height:25,marginRight:3}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>审核</Text>
                        </View>
                    </TouchableOpacity> */}
                    {/* <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该入库信息吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteHlStorageIn(item.storageInId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:80,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity> */}
                    {/* { <TouchableOpacity onPress={()=>{
                        this.props.navigation.navigate("HLStorageInMAdd", 
                            {
                                // 传递参数
                                storageInId:item.storageInId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                            }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:'row'}]}>
                                <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                            </View>
                    </TouchableOpacity> }                     */}
                
                {
                    constants.loginUser.userName == item.operator?
                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                        {
                            item.auditState == "发起审核"?
                            <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                <TouchableOpacity onPress={()=>{
                                    this.props.navigation.navigate("HLMedicineStorageInAuditDetail", 
                                    {
                                        // 传递参数
                                        auditItemId: item.storageInId,
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {marginLeft:0,width: 70 ,flexDirection:"row"}]}>
                                    <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={()=>{
                                    Alert.alert('确认','您确定要删除该条药品入库记录吗？',[
                                        {
                                            text:"取消", onPress:()=>{
                                            WToast.show({data:'点击了取消'});
                                            // this在这里可用，传到方法里还有问题
                                            // this.props.navigation.goBack();
                                            }
                                        },
                                        {
                                            text:"确定", onPress:()=>{
                                                WToast.show({data:'点击了确定'});
                                                this.deleteHlMedicineStorageIn(item.storageInId)
                                            }
                                        }
                                    ]);
                                }}>
                                    <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{marginLeft:0,width:70,flexDirection:'row'}]}>
                                        <Image style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={()=>{
                                        this.props.navigation.navigate("HLMedicineStorageInAdd", 
                                        {
                                            // 传递参数
                                            storageInId:item.storageInId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                    }}>
                                    <View style={[CommonStyle.itemBottomEditBtnViewStyle,{marginLeft:0,width:70,flexDirection:'row'}]}>
                                        <Image style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                        <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            :
                            <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("HLMedicineStorageInAuditDetail", 
                                {
                                    // 传递参数
                                    auditItemId: item.storageInId,
                                    // 传递回调函数
                                    refresh: this.callBackFunction 
                                })
                            }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 75 ,flexDirection:"row"}]}>
                                <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                </View>
                            </TouchableOpacity>
                        }
                    </View>
                    :
                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                        <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("HLMedicineStorageInAuditDetail", 
                            {
                                // 传递参数
                                auditItemId: item.storageInId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 75 ,flexDirection:"row"}]}>
                                <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                                let url= "/biz/audit/cc/record/modify";
                                let loadRequest={
                                    "recordId": item.ccRecordId,
                                    "ccRecordState":item.ccRecordState == '0AA'?"0AB":"0AA" ,
                                };
                                httpPost(url, loadRequest, (response)=>{
                                    if (response.code == 200 && response.data) {
                                        WToast.show({data:response.data.ccRecordState == '0AA'?"成功标为未读":"成功标为已读"});
                                        this.callBackFunction();
                                    }
                                    else if (response.code == 401) {
                                        WToast.show({data:response.message});
                                        this.props.navigation.navigate("LoginView");
                                    }
                                    else {
                                        WToast.show({data:response.message});
                                    }
                                });
                            }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:'row'},item.ccRecordState == '0AA'?{backgroundColor:'#FA353F'}:{backgroundColor:'#FFB800'}]}>
                                {
                                    item.ccRecordState == '0AA'?
                                    <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/unread.png')}></Image>
                                    :
                                    <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/read.png')}></Image>
                                }
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>{item.ccRecordState == '0AA'?"未读":"已读"}</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                }
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>               
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLMedicineStorageInAdd", 
                {
                    storageInId:this.state.storageInId,
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                {/* <Text style={CommonStyle.headRightText}>新增模版</Text> */}
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='药品入库'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.contentViewStyle ]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});