import React,{Component} from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, TextInput, Clipboard, Linking,Image,Modal,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../utils/CameraRollUtils';

export default class PaymentApplyList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:10,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight: 0,
            //userId:"",
            searchKeyWord:"",
            auditState:"",
            paymentApplyStateSource:[],
            selPaymentApplyStateCode:"all",
            attachImage:"",
            attachImageUrl:"",
            isShowImage: false,
            urls:[],
            display:"N",
            pictureIndex:0,
            moreModal:false,
            modalItem:{},
            deleteModal:false,
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId} = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        let paymentApplyStateSource = [
            {
                stateCode:'all',
                stateName:'全部',
            },
            {
                stateCode:'1',
                stateName:'发起',
            },
            {
                stateCode:'2',
                stateName:'审核中',
            },
            {
                stateCode:'3',
                stateName:'通过',
            },
            {
                stateCode:'4',
                stateName:'驳回',
            }
        ]
        this.setState({
            paymentApplyStateSource:paymentApplyStateSource,
        })
        this.loadPaymentApplyList();
    }

    loadPaymentApplyList=()=>{
        let url= "/biz/payment/apply/audit/paymentApplyList";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "auditState":this.state.selPaymentApplyStateCode === 'all' ? null : this.state.selPaymentApplyStateCode,
            "searchKeyWord":this.state.searchKeyWord,
            "applyUserName":constants.loginUser.userName,
            "userId":constants.loginUser.userId
        };
        httpPost(url, loadRequest, this._loadPaymentApplyListCallBack);
    }
    _loadPaymentApplyListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N" ,pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    deletePaymentApply=(applyAuditId)=>{
        console.log("=======delete=applyAuditId", applyAuditId);
        let url= "/biz/payment/apply/audit/delete";
        let requestParams={'applyAuditId':applyAuditId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }  
    paymentApplyStateRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    let stateCode=item.stateCode
                    this.setState({
                        selPaymentApplyStateCode:stateCode,
                    })
                    console.log("======selPaymentApplyStateCode==", this.state.selPaymentApplyStateCode,",",item.stateName,",",item.stateCode)
                    let url= "/biz/payment/apply/audit/paymentApplyList";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "auditState":item.stateCode === 'all' ? null : item.stateCode,
                        "searchKeyWord":this.state.searchKeyWord,
                        "applyUserName":constants.loginUser.userName,
                        "userId":constants.loginUser.userId
                    };
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.stateCode} style={[{width: screenWidth/5 , height: 49, flexDirection: 'row', justifyContent: 'center'}
                    // ,item.stateCode === this.state.selCompletionStateCode ?
                    //     [styles.selectedBlockItemViewStyle]
                    //     :
                    //     [styles.blockItemViewStyle],
                    ]}>
                        <Text style={[item.stateCode === this.state.selPaymentApplyStateCode ?
                            { color: "#255BDA", fontSize: 16, fontWeight: '500', lineHeight: 49, textAlign: 'center', borderColor: "#255BDA", borderBottomWidth: 2, paddingLeft: 5, paddingRight: 5 }
                            :
                            { color: "#2B333F", fontSize: 16, fontWeight: '500', lineHeight: 49, textAlign: 'center'},
                        ]}>
                            {item.stateName}
                        </Text>
                    </View>

                </TouchableOpacity>
            </View>
        )
    } 
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        //此处导致无法刷新
        //if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
        //    console.log("==========不刷新=====");
        //    return;
        //}
        this.setState({
            currentPage:1
        })
        let url= "/biz/payment/apply/audit/paymentApplyList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditState":this.state.selPaymentApplyStateCode === 'all' ? null : this.state.selPaymentApplyStateCode,
            "searchKeyWord":this.state.searchKeyWord,
            "applyUserName":constants.loginUser.userName,
            "userId":constants.loginUser.userId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N" ,pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        console.log("123")
        this.setState({
            refreshing:true
        })
        this.loadPaymentApplyList();
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/payment/apply/audit/paymentApplyList";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "auditState":this.state.selPaymentApplyStateCode === 'all' ? null : this.state.selPaymentApplyStateCode,
            "searchKeyWord":this.state.searchKeyWord,
            "applyUserName":constants.loginUser.userName,
            "userId":constants.loginUser.userId
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("PaymentApplyAdd", 
            //     {
            //         // 传递回调函数
            //         operate:"新增",
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     {/* <Text style={CommonStyle.headRightText}>新增采购</Text> */}
            //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("PaymentApplyAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    });
                }}  >
                    <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }
    callBackFunction=()=>{
        let url= "/biz/payment/apply/audit/paymentApplyList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "applyUserName":constants.loginUser.userName,
            "userId":constants.loginUser.userId
            // "classifyId": this.state.classifyId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }
    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    renderRow=(item, index)=>{
        return (
            <View key={item.applyAuditId} style={styles.innerViewStyleRenderRow}>
                {
                    index == 0 ?
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>
                        :
                        <View></View>
                }
                <View style={CommonStyle.titleViewStyleSpecial}>
                    {/* <Text style={styles.titleTextStyle}>付款名称：{item.paymentApplyName}</Text> */}
                    <Text style={CommonStyle.titleTextStyleSpecial}>{item.paymentApplyName}</Text>
                    {/* {
                        constants.loginUser.userName == item.applyUserName ? 
                        null
                        :
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, marginLeft:5, height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                            抄送
                        </Text>
                        
                    }        */}
                </View>
                <View style={[CommonStyle.titleViewStyle, { position: 'absolute', right: 0, top: 0, flexDirection: 'column' ,marginRight:15}]}>
                        <TouchableOpacity onPress={() => {
                            this.setState({
                                moreModal: true,
                                modalItem:item
                            })
                        }}>
                            <View style={[{ width: 35, height: 35, alignItems: 'center' }]}>
                                <Image style={{ width: 28, height: 28 }} source={require('../../assets/icon/iconfont/more.png')}></Image>
                            </View>
                        </TouchableOpacity>
                    </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>支付对象：{item.customerName}</Text>
                    {
                        constants.loginUser.userName == item.applyUserName ? 
                        null
                        :
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, marginLeft:5, height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                            抄送
                        </Text>
                        
                    }  
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>支付类别：{item.paymentClassName}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>支付日期：{item.paymentDate}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>付款金额：{item.paymentAmount}元</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>付款事由：{item.paymentReason?item.paymentReason:"无"}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>支付方式：{item.paymentModeName}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>提交人：{item.applyUserName}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>提交时间：{item.gmtCreated}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>审核状态：{item.auditStateName}</Text>
                </View>
                {
                    item.auditUserName ? 
                    <View style={CommonStyle.titleViewStyle}>
                        <Text style={CommonStyle.titleTextStyle}>当前审核人：{item.auditUserName}</Text>
                    </View> :
                    <View />
                }
                

                {
                    item.auditPatchAttach ?
                    (
                        <View>
                            {
                                item.pictureDisplay === "N"?
                                    <View style={[CommonStyle.titleViewStyle, { justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                                        <Text style={CommonStyle.titleTextStyle}>附件：</Text>
                                        <TouchableOpacity onPress={() => {
                                            if (item.auditPatchAttach) {
                                                var urls = [];
                                                var url = {
                                                    url:constants.image_addr + '/' +  item.auditPatchAttach
                                                } 
                                                urls=urls.concat(url)
                                            }
                                            this.setState({
                                                urls:urls
                                            })
                                            let list = this.state.dataSource;
                                            list.map((elem, index) => {
                                                if(elem.applyAuditId == item.applyAuditId){
                                                    elem.pictureDisplay = "Y"
                                                }
                                            })
                                            this.setState({
                                                dataSource:list
                                            })
                                            // console.log("==============",list)
                                        }}>
                                                <Text style={[CommonStyle.titleTextStyle,{color:"#CB4139"}]}>点击展开</Text>
                                        </TouchableOpacity>
                                    </View>
                                :
                                <View>
                                    <View style={CommonStyle.titleViewStyle}>
                                        <Text style={CommonStyle.titleTextStyle}>附件：</Text>
                                    </View>
                                    <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                        {
                                           
                                                <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex'}]}>

                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        isShowImage:true,
                                                        // pictureIndex:index
                                                    })
                                                }}>
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.auditPatchAttach) }} style={{ height: 150, width:120 }} />                                                    
                                                </TouchableOpacity>                                                
                                                <Modal visible={this.state.isShowImage} transparent={true}>
                                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} index={this.state.pictureIndex} 
                                                    enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}  
                                                    onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} 
                                                    onSave={()=>{
                                                        saveImage( this.state.urls[this.state.pictureIndex].url)
                                                    }}/>
                                                </Modal>
                                            </View>
                                           
                                        }
                                    </View>
                                    <View style={[CommonStyle.titleViewStyle]}>
                                        {
                                            item.pictureDisplay === "Y"?
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    urls:[]
                                                })
                                                let list = this.state.dataSource;
                                                list.map((elem, index) => {
                                                    if(elem.applyAuditId == item.applyAuditId){
                                                        elem.pictureDisplay = "N"
                                                    }
                                                })
                                                this.setState({
                                                    dataSource:list
                                                })
                                                // console.log("==============",list)
                                            }}>
                                                    <Text style={[CommonStyle.titleTextStyle,{color:"#CB4139"}]}>点击收起</Text>
                                            </TouchableOpacity>
                                            :
                                            <View/>
                                        }
                                    </View>
                                </View>
                                
                            }
                            
                        </View>
                    ):
                    <View style={CommonStyle.titleViewStyle}>
                        <Text style={CommonStyle.titleTextStyle}>附件：无</Text>
                    </View>
                }


                {
                    constants.loginUser.userName == item.applyUserName?
                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap',marginRight:15}]}>
                        
                        {
                            item.auditStateName == "发起审核"?
                            <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                <TouchableOpacity onPress={()=>{
                                    this.props.navigation.navigate("MaterialAuditBacklogDetail", 
                                    {
                                        // 传递参数
                                        auditItemId: item.applyAuditId,
                                        // 传递回调函数
                                        refresh: this.callBackFunction,
                                        auditTypeCode:"PAYMENT_AUDIT"    
                                    })
                                }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 64 ,flexDirection:"row"}]}>
                                    <Image  style={{width:17, height:17,marginRight:2}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                    </View>
                                </TouchableOpacity>
                                {/* <TouchableOpacity onPress={()=>{
                                    if (item.auditScore) {
                                        return;
                                    }
                                    Alert.alert('确认','您确定要删除吗？',[
                                        {
                                            text:"取消", onPress:()=>{
                                            WToast.show({data:'点击了取消'});
                                            // this在这里可用，传到方法里还有问题
                                            // this.props.navigation.goBack();
                                            }
                                        },
                                        {
                                            text:"确定", onPress:()=>{
                                                WToast.show({data:'点击了确定'});
                                                this.deletePaymentApply(item.applyAuditId)
                                            }
                                        }
                                    ]);
                                }}>
                                    <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:64,flexDirection:"row"}
                                        ,item.auditScore ? CommonStyle.disableViewStyle : ""]}>
                                            <Image  style={{width:17, height:17,marginRight:2}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={()=>{
                                    //此处auditSource
                                        if (item.auditScore) {
                                            return;
                                        }
                                        this.props.navigation.navigate("PaymentApplyAdd", 
                                        {
                                            // 传递参数
                                            applyAuditId: item.applyAuditId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                    }}>
                                    <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:64,flexDirection:"row"}
                                        ,item.auditScore ? CommonStyle.disableViewStyle : ""]}>
                                            <Image  style={{width:17, height:17,marginRight:2}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                        <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                    </View>
                                </TouchableOpacity> */}

                            </View>
                            :
                            <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("PaymengApplyDetail", 
                                {
                                    // 传递参数
                                    auditItemId: item.applyAuditId,
                                    // 传递回调函数
                                    refresh: this.callBackFunction,
                                    auditTypeCode:"PAYMENT_AUDIT"  
                                })
                            }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 64 ,flexDirection:"row"}]}>
                                <Image  style={{width:17, height:17,marginRight:2}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                </View>
                            </TouchableOpacity>
                        }
                    </View>
                    :
                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap',marginRight:15}]}>
                        <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("PaymengApplyDetail", 
                            {
                                // 传递参数
                                auditItemId: item.applyAuditId,
                                // 传递回调函数
                                refresh: this.callBackFunction,
                                auditTypeCode:"PAYMENT_AUDIT"  
                            })
                        }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 64 ,flexDirection:"row"}]}>
                                <Image  style={{width:17, height:17,marginRight:2}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                                let url= "/biz/audit/cc/record/modify";
                                let loadRequest={
                                    "recordId": item.ccRecordId,
                                    "ccRecordState":item.ccRecordState == '0AA'?"0AB":"0AA" ,
                                };
                                httpPost(url, loadRequest, (response)=>{
                                    if (response.code == 200 && response.data) {
                                        WToast.show({data:response.data.ccRecordState == '0AA'?"成功标为未读":"成功标为已读"});
                                        this.callBackFunction();
                                    }
                                    else if (response.code == 401) {
                                        WToast.show({data:response.message});
                                        this.props.navigation.navigate("LoginView");
                                    }
                                    else {
                                        WToast.show({data:response.message});
                                    }
                                });
                            }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:64,flexDirection:'row'},item.ccRecordState == '0AA'?{backgroundColor:'#FA353F'}:{backgroundColor:'#FFB800'}]}>
                                {
                                    item.ccRecordState == '0AA'?
                                    <Image style={{width:17, height:17,marginRight:2}} source={require('../../assets/icon/iconfont/unread.png')}></Image>
                                    :
                                    <Image style={{width:17, height:17,marginRight:2}} source={require('../../assets/icon/iconfont/read.png')}></Image>
                                }
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>{item.ccRecordState == '0AA'?"未读":"已读"}</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                }

            </View>
        )
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title='付款申请'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>

                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.paymentApplyStateSource && this.state.paymentApplyStateSource.length > 0)
                                ?
                                this.state.paymentApplyStateSource.map((item, index) => {
                                    return this.paymentApplyStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                        
                    <View style={[CommonStyle.headViewStyle, { borderLeftWidth: 0, borderRightWidth: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                        <View style={CommonStyle.singleSearchBox}>
                            <View style={CommonStyle.searchBoxWithoutOthers}>
                                <Image  style={{width: 16, height: 16, marginLeft: 7}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                <TextInput
                                    style={{color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 5, paddingTop: 0, paddingBottom: 0, paddingRight: 0, paddingLeft: 0, width:'100%' }}
                                    returnKeyType="search"
                                    returnKeyLabel="搜索"
                                    onSubmitEditing={e => {
                                        this.searchByKeyWord();
                                    }}
                                    placeholder={'支付对象'}
                                    onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                >
                                    {this.state.searchKeyWord}
                                </TextInput>
                            </View>
                        </View>
                    </View>

                </View>               
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> */}
                        <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            ItemSeparatorComponent={this.space}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                        />
                    {/* </ScrollView> */}
                </View>
                {/* 更多操作弹窗Modal */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.moreModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >
                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 291, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    if (this.state.modalItem.auditStateName != "发起审核"||constants.loginUser.userName != this.state.modalItem.applyUserName) {
                                        WToast.show({ data: '该申请不可编辑' });
                                        return;
                                    }
                                    this.setState({
                                        moreModal: false,
                                    })
                                    this.props.navigation.navigate("PaymentApplyAdd",
                                        {
                                            // 传递参数
                                            applyAuditId: this.state.modalItem.applyAuditId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction
                                        })
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , (this.state.modalItem.auditStateName != "发起审核"||constants.loginUser.userName != this.state.modalItem.applyUserName) ? CommonStyle.disableViewStyle : ""]}>
                                        {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                                        <Text style={{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }}>编辑</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View>
                                <TouchableOpacity onPress={() => {
                                    // console.log("dailyItem=================",this.state.dailyItem)
                                    if (this.state.modalItem.auditStateName != "发起审核"||constants.loginUser.userName != this.state.modalItem.applyUserName) {
                                        WToast.show({ data: '该申请不可删除' });
                                        return;
                                    }
                                    // 删除弹窗Modal
                                    this.setState({
                                        moreModal: false,
                                        deleteModal: true
                                    })
                                    
                                }}>
                                    <View style={[{width: 145, height: 50, paddingLeft: 30, marginTop: 5}
                                        , (this.state.modalItem.auditStateName != "发起审核"||constants.loginUser.userName != this.state.modalItem.applyUserName) ? CommonStyle.disableViewStyle : ""]}>
                                        {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                                        <Text style={[{ color: 'rgba(0, 10, 32, 0.85)', fontSize: 18, lineHeight: 52 }]}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={{ width: 291, height: 50,alignItems: 'flex-end', justifyContent: 'flex-end', marginTop: 10, borderTopWidth: 1, borderColor: '#DFE3E8'}}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        moreModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 105, height: 50, alignItems: 'center', justifyContent: 'center' }} >
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA' }}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                {/* 删除弹窗 */}
                <Modal
                    animationType='fade'
                    transparent={true}
                    visible={this.state.deleteModal}
                    //  onShow={this.onShow.bind(this)}
                    onRequestClose={() => console.log('onRequestClose...')}
                >

                    <View style={[CommonStyle.fullScreenKeepOut, { backgroundColor: 'rgba(0,0,0,0.64)' }]}>
                        <View style={{ width: 292, height: 156, bottom: screenHeight / 2 - 80, position: 'absolute', backgroundColor: '#FFFFFF', borderRadius: 10, }}>
                            <View style={{ height: 50, justifyContent: 'center', alignItems: 'center', marginTop: 10 }}>
                                <Text style={{ fontSize: 18 }}>确认删除该日报?</Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', height: 24 }}>
                                <Text style={{ fontSize: 14, color: 'rgba(0,10,32,0.65)' }}>删除后数据不可恢复，请谨慎操作</Text>
                            </View>

                            <View style={{ flexDirection: 'row', width: 292, height: 56, marginTop: 15, borderTopWidth: 1, borderColor: '#DFE3E8', alignItems: 'center', justifyContent: 'center' }}>
                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false
                                    });
                                    WToast.show({ data: '点击了取消' });
                                }}>
                                    <View style={{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center', borderRightWidth: 1, borderColor: '#DFE3E8' }} >
                                        <Text style={{ fontSize: 17,  fontWeight: '400', color: '#000A20', }}>取消</Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity onPress={() => {
                                    this.setState({
                                        deleteModal: false,
                                    })
                                    WToast.show({ data: '点击了确定' });
                                    this.deletePaymentApply(this.state.modalItem.applyAuditId)
                                }}>
                                    <View style={[{ width: 146, height: 56, alignItems: 'center', justifyContent: 'center' }]}>
                                        <Text style={{ fontSize: 17, fontWeight: '400', color: '#1E6EFA'}}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:3,
        marginTop:5,
    },
    titleViewStyleSpecial:{
        flexDirection: 'row',
        // justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 10,
    },
    titleTextStyle:{
        fontSize:16
    },
    titleTextStyleSpecial:{
        // width: 200,
        height: 24,
        // fontFamily: 'PingFangSC',
        fontWeight: 'bold',
        fontSize: 20,
        color: '#404956',
        lineHeight: 24,
        textAlign: 'left',
        fontStyle: 'normal',
    },
    innerViewStyle: {
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        // borderWidth: 8
    },
    innerViewStyleRenderRow:{
        // marginLeft:16
    },
    inputOutsideText:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },
    inputInsideText:{
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        marginTop:5,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
});