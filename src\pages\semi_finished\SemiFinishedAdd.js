import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,KeyboardAvoidingView,FlatList,TouchableOpacity,Image,Dimensions,Modal} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
// const leftLabWidth = 70;
const leftLabWidth = 130;
class SemiFinishedAdd extends Component{
    constructor(){
        super()
        this.state = {
            checkId:'',
            ordersDataSource:[],
            carrierDataSource:[],
            machineDataSource:[],
            selBrickTypeId:0,
            selOrderId:0,
            brickAmount:"",
            selCarrierId:0,
            selMachineId:0,
            selectProductionTime:'',
            productionTime:'',
            unitWeight:'',
            standIn:'',
            wasteNumber:"",
            samplingSize:"",
            hammerNumber:"",
            singleWeight:"",
            customerName:"",
            //班次
            workingShiftName:"",
            workingShiftId:"",
            workingShiftList:"",
            selWorkingShift:[],
            productionLineDataSource:[],
            selProductionLineId:null,
            //员工
            // staffName:"",
            staffId:"",
            // staffList:[],
            // selStaff:[],
            staffDataSource:[],
            staffIdList:[],
            modal:false,
            searchKeyWord:null,
            // 不显示相关字段的租户列表
            excludeTenantIdList:[59,66,67]
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        this.loadInitData();
        // this.loadWorkingShiftData();
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { checkId, productionLineId, shiftName, staffName, shiftId} = route.params;
            console.log("传过来的生产线Id====" + productionLineId);
            if (checkId) {
                console.log("========Edit==checkId:", checkId);
                this.setState({
                    checkId:checkId,
                    selProductionLineId:productionLineId

                })
                this.loadWorkingShiftData(productionLineId);
                loadTypeUrl= "/biz/semi/finished/check/get";
                loadRequest={'checkId':checkId};
                httpPost(loadTypeUrl, loadRequest, this.callBackLoadSemiFinishedData);
            }
            if (shiftName) {
                this.setState({
                    workingShiftName:shiftName,
                })
            }
            // if (staffName) {
            //     this.setState({
            //         staffName:staffName,
            //         selStaff:[staffName]
            //     })
            // }
            if (shiftId) {
                this.loadEmployeeListByShiftId(shiftId)
            }
        }
        
    }

    loadWorkingShiftData=(productionLineId)=>{
        console.log("===productionLineId==="+productionLineId)
        let url= "/biz/working/shift/list";
        let loadRequest={'currentPage':1, 'pageSize':1000,'shiftType':"A",'productionLineId':productionLineId};
        httpPost(url, loadRequest, this.callBackLoadWorkingShiftData);
      }

    callBackLoadWorkingShiftData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList && response.data.dataList.length > 0) {
            this.setState({
                workingShiftList:response.data.dataList,
                // workingShiftName:response.data.dataList[0] ? response.data.dataList[0].workingShiftName : 0,
            })
        }

    }

    callBackLoadSemiFinishedData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                checkId:response.data.checkId,
                selBrickTypeId:response.data.brickTypeId,
                selOrderId:response.data.orderId,
                selOrderName:response.data.orderName,
                brickAmount:response.data.brickAmount,
                selCarrierId:response.data.carrierId,
                selMachineId:response.data.machineId,
                // selectProductionTime:'',
                productionTime:response.data.productionTime,
                unitWeight:response.data.unitWeight,
                standIn:response.data.standIn,
                wasteNumber:response.data.wasteNumber,
                samplingSize:response.data.samplingSize,
                hammerNumber:response.data.hammerNumber,
                singleWeight:response.data.singleWeight,
                staffIds:response.data.staffIds
            })
            var list = [];
            if(response.data.staffIds){
                
                var staffIdList = response.data.staffIds.split(",")
                console.log("====staffIdList===="+staffIdList)
                for(var i=0; i< staffIdList.length; i++){
                  list =  list.concat(staffIdList[i])
                }
                console.log(list)
                this.setState({
                    staffIdList:list
                })
                
                
            }
        }
    }

    loadInitData=()=>{
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectProductionTime:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            productionTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })

        // 生产车间列表
        let url= "/biz/production/line/list";
        let loadRequest={'currentPage':1, 'pageSize':1000, "qryIncludeContent":"machine"};
        httpPost(url, loadRequest, this.callBackLoadProductionLine);
        // console.log("---------" + this.state.selProductionLineId + "----------");
    }

    // 订单回调加载
    callBackLoadOrder=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            this.setState({
                ordersDataSource:response.data.dataList,
                // selBrickTypeId:response.data.dataList[0] ? response.data.dataList[0].brickTypeId : null,
                // selOrderId:response.data.dataList[0] ? response.data.dataList[0].orderId : null,
                // selOrderName:response.data.dataList[0] ? response.data.dataList[0].orderName : null,
                // customerName:response.data.dataList[0] ? response.data.dataList[0].customerName : 0,
            })
            // if (!this.state.checkId) {
            //     this.setState({
            //         selOrderId:response.data.dataList[0] ? response.data.dataList[0].orderId : 0,
            //     })
            // }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 运送人回调加载
    callBackLoadCarrier=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员配置运送人");
                WToast.show(toastOpts);
                return;
            }
            this.setState({
                carrierDataSource:response.data.dataList,
            })
            if (this.state.checkId && this.state.selCarrierId != 0) {
                // 读编辑数据
            }
            else if (response.data.dataList.length > 0){
                this.setState({
                    selCarrierId:response.data.dataList[0].carrierId,
                })
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 生产车间
    callBackLoadProductionLine=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            let productionLineDataSource = response.data.dataList;
            let selProductionLineId = response.data.dataList[0].productionLineId;
            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                selProductionLineId = constants.loginUser.spUserExtDTO.productionLineId;
            }

            if(this.state.selProductionLineId) {
                selProductionLineId = this.state.selProductionLineId;
            }

            this.setState({
                productionLineDataSource:productionLineDataSource,
                selProductionLineId:selProductionLineId,
            })
            this.loadWorkingShiftData(selProductionLineId);
            console.log("================" + this.state.selProductionLineId);
            if (productionLineDataSource.length == 1) {
                this.setState({
                    machineDataSource:productionLineDataSource[0].machineDTOList,
                })
                if (this.state.checkId && this.state.selMachineId != 0) {
                    this.setState({
                        selMachineId:this.state.selMachineId,
                    })
                }
                else if (productionLineDataSource[0].machineDTOList.length > 1) {
                    this.setState({
                        selMachineId:productionLineDataSource[0].machineDTOList[0].machineId
                    })
                }
            }
            else {
                // JS 数组遍历
                productionLineDataSource.forEach((obj)=>{
                    if (selProductionLineId && obj.machineDTOList && obj.productionLineId === selProductionLineId) {
                        this.setState({
                            machineDataSource:obj.machineDTOList
                        })
                    }
                })

            } 

            this.loadOrder(this.state.selProductionLineId);
            // let url1= "/biz/order/list";
            // let loadRequest1={
            //     'currentPage':1,
            //     'pageSize':100,
            //     "display":"Y",
            //     "excludeOrderStateList":[
            //         "A","K"
            //     ],
            //     'productionLineId':this.state.selProductionLineId
            // };
            // httpPost(url1, loadRequest1, this.callBackLoadOrder);

        }
    }

    loadOrder =(productionLineId)=>{
        let loadUrl= "/biz/order/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':1000,
            "display":"Y",
            "qryContent":"order",
            "searchKeyWord":this.state.searchKeyWord,
            "excludeOrderStateList":[
                "A","K"
            ],
            'productionLineId':productionLineId ? productionLineId : this.state.selProductionLineId
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadOrder);
    }
    // 机台回调加载
    callBackLoadMachine=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员配置机台");
                WToast.show(toastOpts);
                return;
            }
            this.setState({
                machineDataSource:response.data.dataList,
            })
            if (this.state.checkId && this.state.selMachineId != 0) {
                this.setState({
                    selMachineId:this.state.selMachineId,
                })
            }
            else if (response.data.dataList.length > 0) {
                this.setState({
                    selMachineId:response.data.dataList[0].machineId
                })
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("SemiFinishedList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>半成品管理</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>半成品点验</Text>
                </TouchableOpacity>
            </View>
        )
    }


    //订单列表展示
    renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.checkId) {
                    return;
                }
                    this.setState({
                        selBrickTypeId:item.brickTypeId,
                        selOrderId:item.orderId,
                        customerName:item.customerName
                        // brickAmount:item.brickAmount,
                    })
                }}>
                <View key={item.orderId} style={[item.orderId===this.state.selOrderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle, this.state.checkId ? CommonStyle.disableViewStyle : ''] }>
                    <Text style={item.orderId===this.state.selOrderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 运送人
    renderCarrierRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                this.setState({
                selCarrierId:item.carrierId
            }) }}>
                <View key={item.selCarrierId} style={item.carrierId===this.state.selCarrierId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.carrierId===this.state.selCarrierId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.carrierName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 生产车间 
    renderProductLineRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.checkId) {
                    return;
                }

                var selMachineId = null;
                // 切换生产车间时，下面的机台也要跟着变，机台默认选择第一个
                this.setState({
                    selProductionLineId:item.productionLineId,
                    machineDataSource:item.machineDTOList,
                    workingShiftName:"",
                    selWorkingShift:[],
                    workingShiftList:"",
                    staffDataSource:[],
                    staffIdList:[]
                }) 
                this.loadWorkingShiftData(item.productionLineId);
                if (item.machineDTOList && item.machineDTOList.length > 0) {
                    selMachineId = item.machineDTOList[0].machineId;
                }
                this.setState({
                    selMachineId:selMachineId,
                    selBrickTypeId:null,
                    selOrderId:null,
                    selOrderName:null,
                })

                this.loadOrder(item.productionLineId);
            }}>
                <View key={item.productionLineId} style={[item.productionLineId === this.state.selProductionLineId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/2,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.productionLineId === this.state.selProductionLineId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.productionLineName}
                    </Text>
                </View>
            </TouchableOpacity>

        )
    }

    // 机器 
    renderMachineRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selMachineId:item.machineId
            }) 
            }}>
                <View key={item.machineId} style={[item.machineId === this.state.selMachineId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.machineId === this.state.selMachineId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.machineName}
                    </Text>
                </View>
            </TouchableOpacity>
            
        )
    }

    saveSemiFinishedCheck =()=> {
        console.log("=======saveSemiFinishedCheck");
        let toastOpts;
        if (!this.state.selBrickTypeId || this.state.selBrickTypeId === 0) {
            toastOpts = getFailToastOpts("请选择要点验的砖型");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selOrderId || this.state.selOrderId === 0) {
            toastOpts = getFailToastOpts("请选择要点验的砖型.");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.selCarrierId || this.state.selCarrierId === 0) {
        //     toastOpts = getFailToastOpts("请选择运送人");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.selMachineId || this.state.selMachineId === 0) {
            toastOpts = getFailToastOpts("请选择机台");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.brickAmount || this.state.brickAmount === 0) {
            toastOpts = getFailToastOpts("请输入数量");
            WToast.show(toastOpts)
            return;
        }
        if(constants.loginUser.tenantId == 66){
            if (!this.state.unitWeight || this.state.unitWeight === 0) {
                toastOpts = getFailToastOpts("请输入单重");
                WToast.show(toastOpts)
                return;
            }
        }
        // if (!this.state.wasteNumber || this.state.wasteNumber === 0) {
        //     toastOpts = getFailToastOpts("请输入废品块数");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.samplingSize || this.state.samplingSize === 0) {
        //     toastOpts = getFailToastOpts("请输入抽检尺寸");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.hammerNumber || this.state.hammerNumber === 0) {
        //     toastOpts = getFailToastOpts("请输入锤数");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.singleWeight || this.state.singleWeight === 0) {
        //     toastOpts = getFailToastOpts("请输入单重(湿重)");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url= "/biz/semi/finished/check/add";
        if (this.state.checkId) {
            console.log("=========Edit===checkId", this.state.checkId)
            url= "/biz/semi/finished/check/modify";
        }
        let requestParams={
            "checkId":this.state.checkId,
            "brickTypeId":this.state.selBrickTypeId,
            "orderId": this.state.selOrderId,
            "carrierId":this.state.selCarrierId,
            "machineId":this.state.selMachineId,
            "brickAmount":this.state.brickAmount,
            "inspector":constants.loginUser.userName,
            "selectProductionTime":this.state.selectProductionTime,
            "productionTime":this.state.productionTime,
            "unitWeight":this.state.unitWeight,
            "standIn":this.state.standIn,
            "wasteNumber":this.state.wasteNumber,
            "samplingSize":this.state.samplingSize,
            "hammerNumber":this.state.hammerNumber,
            "singleWeight":this.state.singleWeight,
            "shiftId":this.state.workingShiftId,
            "staffIds":this.state.staffIdList.toString()
        };
        console.log("=========", this.state.staffIdList.toString())
        httpPost(url, requestParams, this.saveSemiFinishedCheck_call_back);
    }
    
    // 保存回调函数
    saveSemiFinishedCheck_call_back=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    openProductionTime(){
        this.refs.SelectProductionTime.showDate(this.state.selectProductionTime)
    }
    
    openWorkingShift(){
        console.log(this.state.workingShiftList)
        if (this.state.workingShiftList && this.state.workingShiftList.length > 0) {
            this.refs.SelectWorkingShift.showWorkingShift(this.state.selWorkingShift,this.state.workingShiftList)
        }
        else {
            WToast.show({data:"没有班次信息"})
        }
    }

    // openStaff(){
    //     if(!this.state.workingShiftName){
    //         // console.log("没有选择班次");
    //         WToast.show({data:"请选择班次"})
    //     }
    //     else{
    //         console.log(this.state.staffList)
    //         if (this.state.staffList && this.state.staffList.length > 0) {
    //             this.refs.SelectStaff.showStaff(this.state.selStaff,this.state.staffList)
    //         }
    //         else {
    //             WToast.show({data:"没有员工信息"})
    //         }
    //     }
        
    // }

    callBackWorkingShiftValue(value){
        console.log("==========班次选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selWorkingShift:value,
            selStaff:[],
            staffName:"",
            // standIn:""
        })
        // 取选定的客户ID
        var workingShiftName = value.toString();
        // this.setState({
        //     workingShiftName:workingShiftName
        // })
        let loadUrl= "/biz/working/shift/getWorkingShiftByName";
        let loadRequest={"shiftName":workingShiftName,'shiftType':"A",'productionLineId':this.state.selProductionLineId};
        console.log("==========loadDetailRequest", loadRequest)
        httpPost(loadUrl, loadRequest, this.loadWorkingShiftDetailData);
    }

    // callBackStaffValue(value){
    //     console.log("==========员工选择结果：", value)
    //     if (!value) {
    //         return;
    //     }
    //     this.setState({
    //         selStaff:value
    //     })
    //     var staffName = value.toString();
    //     // this.setState({
    //     //     staffName:staffName
    //     // })
    //     let loadUrl= "/biz/portal/staff/getPortalStaffByName";
    //     let loadRequest={"staffName":staffName};
    //     console.log("==========loadRequest", loadRequest)
    //     httpPost(loadUrl, loadRequest, this.loadStaffDetailData);
    // }

    // loadStaffDetailData=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             staffName:response.data.staffName,
    //             staffId:response.data.staffId,
    //             // standIn:response.data.staffName
    //         })
    //         // console.log(response.data.staffId)
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    //     else {
    //         WToast.show({data:response.message});
    //     }
    // }

    loadWorkingShiftDetailData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                workingShiftName:response.data.shiftName,
                workingShiftId:response.data.shiftId,
            })
            // 通过shiftId获取员工列表
            this.loadEmployeeListByShiftId(response.data.shiftId)

        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    // 通过shiftId获取员工列表
    loadEmployeeListByShiftId=(shiftId)=>{
        let loadUrl= "/biz/working/shift/staff/list";
        let loadRequest={"shiftId":shiftId,'currentPage':1,'pageSize':50};
        console.log("==========loadRequest", loadRequest)
        httpPost(loadUrl, loadRequest, this.loadEmployeeListByShiftIdCallBack);
    }

    loadEmployeeListByShiftIdCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                staffDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    callBackSelectProductionTimeValue(value){
        console.log("==========生产时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectProductionTime:value
        })
        if (this.state.selectProductionTime && this.state.selectProductionTime.length) {
            var productionTime = "";
            var vartime;
            for(var index=0;index<this.state.selectProductionTime.length;index++) {
                vartime = this.state.selectProductionTime[index];
                if (index===0) {
                    productionTime += vartime;
                }
                else if (index < 3){
                    productionTime += "-" + vartime;
                }
                else if (index===3){
                    productionTime += " " + vartime;
                }
                else {
                    productionTime += ":" + vartime;
                }
            }
            this.setState({
                productionTime:productionTime
            })
        }
    }

    compare=(staffId)=>{
        for(var i=0;i<this.state.staffIdList.length;i++){
            if(this.state.staffIdList[i] == staffId){
                return true;
            }
        }
        return false;
    }

    // 员工单项渲染
    renderStaffRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                var staffIdList = this.state.staffIdList;
                if (this.compare(item.staffId)) {
                    arrayRemoveItem(staffIdList, item.staffId);
                }
                else {
                    staffIdList = staffIdList.concat(item.staffId)
                }
                this.setState({
                    staffIdList:staffIdList,
                })
                WToast.show({data:'点击了' + item.staffName});
                console.log("======staffIdList:", staffIdList)
            }}>
                <View key={item.staffId} style={this.compare(item.staffId) ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={this.compare(item.staffId) ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.staffName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    renderOrderItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.checkId) {
                    return;
                }
                this.setState({
                    selBrickTypeId:item.brickTypeId,
                    selOrderId:item.orderId,
                    selOrderName:item.orderName,
                    customerName:item.customerName
                    // brickAmount:item.brickAmount,
                })
            }}>
                <View key={item.orderId} style={item.orderId===this.state.selOrderId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.orderId===this.state.selOrderId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title='半成品点验'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    {/* <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>砖料信息</Text>
                    </View> */}
                    
                    {
                        this.state.productionLineDataSource.length == 1 ?
                        <View>
                            <View style={styles.rowLabView}>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>机台</Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                            <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                                {
                                    (this.state.machineDataSource && this.state.machineDataSource.length > 0) 
                                    ? 
                                    this.state.machineDataSource.map((item, index)=>{
                                        return this.renderMachineRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                        </View>
                        :
                        <View>
                            <View style={styles.rowLabView}>
                                <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>生产车间</Text>
                            </View>
                            <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                                {
                                    (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0) 
                                    ? 
                                    this.state.productionLineDataSource.map((item, index)=>{
                                        return this.renderProductLineRow(item)
                                    })
                                    
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                            <View style={CommonStyle.lineBorderBottomStyle} />

                            <View style={styles.rowLabView}>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>机台</Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                            <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                                {
                                    (this.state.machineDataSource && this.state.machineDataSource.length > 0) 
                                    ? 
                                    this.state.machineDataSource.map((item, index)=>{
                                        return this.renderMachineRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                            <View style={CommonStyle.lineBorderBottomStyle} />

                        </View>
                    }
                    
                    {/* <View style={styles.rowLabView}>
                        <Text style={styles.leftLabNameTextStyle}>机台</Text>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.machineDataSource && this.state.machineDataSource.length > 0) 
                            ? 
                            this.state.machineDataSource.map((item, index)=>{
                                return this.renderMachineRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View> */}
                    <View style={CommonStyle.rowLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={CommonStyle.rowLabTextStyle}>砖型</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    
                    <View style={[{flexWrap:'wrap',marginLeft:25}, this.state.checkId ? CommonStyle.disableViewStyle : null]}>
                        <TouchableOpacity onPress={()=>{
                            if(this.state.checkId) {
                                return;
                            }
                            this.setState({ 
                                modal:true,
                            })

                            if (!this.state.selOrderId && this.state.ordersDataSource && this.state.ordersDataSource.length > 0) {
                                this.setState({
                                    selBrickTypeId:this.state.ordersDataSource[0].brickTypeId,
                                    selOrderId:this.state.ordersDataSource[0].orderId,
                                    selOrderName:this.state.ordersDataSource[0].orderName,
                                    customerName:this.state.ordersDataSource[0].customerName,
                                })
                            }
                        }}>
                            <View style={[this.state.selOrderId && this.state.selOrderName ?
                                {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                :
                                {backgroundColor: '#F2F5FC'}
                                ,
                                {
                                    marginRight: 8,
                                    marginTop: 8,
                                    marginBottom: 4,
                                    borderRadius: 4,
                                    justifyContent: 'center',
                                    alignContent: 'center',
                                    height: 36,
                                    paddingLeft:6,
                                    paddingRight:6,
                                    // width: (screenWidth - 54)/2,
                                    borderRadius: 4,
                                }
                            ]}>
                                <Text style={[this.state.selOrderId && this.state.selOrderName ?
                                    {
                                        color: '#1E6EFA'
                                    }
                                    :
                                    {
                                        color: '#404956'
                                    }
                                    ,
                                {
                                    fontSize: 16, textAlign : 'center'
                                }
                                ]}>
                                    选择砖型
                                    {this.state.selOrderId && this.state.selOrderName ? ("：" + this.state.selOrderName) : null}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput 
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({searchKeyWord:text})}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={()=>{
                                        this.loadOrder();
                                        }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{flexDirection:'row', flexWrap:'wrap', overflow:'scroll'}}>
                                    {
                                        (this.state.ordersDataSource && this.state.ordersDataSource.length > 0) 
                                        ? 
                                        this.state.ordersDataSource.map((item, index)=>{
                                            if (index < 1000) {
                                                return this.renderOrderItem(item)
                                            }
                                        })
                                        : <EmptyRowViewComponent/> 
                                    }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                    <TouchableOpacity onPress={() => { 
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selOrderId) {
                                            let toastOpts = getFailToastOpts("您还没有选择砖型");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    {/* <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.ordersDataSource && this.state.ordersDataSource.length > 0) 
                            ? 
                            this.state.ordersDataSource.map((item, index)=>{
                                return this.renderRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View> */}

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>客户名称</Text>
                        </View>
                        <TextInput 
                            editable = {false}
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            // onChangeText={(text) => this.setState({customerName:text})}
                        >
                            {this.state.customerName}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>数量(块)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({brickAmount:text})}
                        >
                            {this.state.brickAmount}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>客户名称</Text>
                        </View>
                        <TextInput 
                            editable = {false}
                            style={styles.inputRightText}
                            placeholder={'请输入客户名称'}
                            // onChangeText={(text) => this.setState({customerName:text})}
                        >
                            {this.state.customerName}
                        </TextInput>
                    </View> */}

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>生产时间</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openProductionTime()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.productionTime ? "请选择生产时间" : this.state.productionTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>单重（Kg）</Text>
                            {
                                constants.loginUser.tenantId == 66?
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                :
                                <View/>
                            }
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({unitWeight:text})}
                        >
                            {this.state.unitWeight}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                <View>
                    {
                        (this.state.workingShiftList && this.state.workingShiftList.length > 0)
                        ?
                        <View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>
                                        班次
                                    </Text>
                                    {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                </View>
                                <TouchableOpacity onPress={()=>{
                                        this.openWorkingShift()
                                }}>
                                    <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                                        <Text style={{color:'#A0A0A0', fontSize:15}}>
                                            {!this.state.workingShiftName ? "请选择" : this.state.workingShiftName}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={CommonStyle.lineBorderBottomStyle} />

                            <View style={[styles.inputRowStyle]}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>员工</Text>
                                    {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                </View>
                            </View>
                            <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                                {
                                    (this.state.staffDataSource && this.state.staffDataSource.length > 0) 
                                    ? 
                                    this.state.staffDataSource.map((item, index)=>{
                                        return this.renderStaffRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                            <View style={CommonStyle.lineBorderBottomStyle} />

                        </View>
                        :<View/>
                    }
                </View>

                


                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>代班</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({standIn:text})}
                        >
                            {this.state.standIn}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>废品数</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({wasteNumber:text})}
                        >
                            {this.state.wasteNumber}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    {
                        !this.state.excludeTenantIdList.includes(constants.loginUser.tenantId) ?
                        <View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>抽检尺寸</Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                </View>
                                <TextInput 
                                    style={styles.inputRightText}
                                    placeholder={'请输入'}
                                    onChangeText={(text) => this.setState({samplingSize:text})}
                                >
                                    {this.state.samplingSize}
                                </TextInput>
                            </View>
                            <View style={CommonStyle.lineBorderBottomStyle} />

                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>锤数</Text>
                                    {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                </View>
                                <TextInput 
                                    style={styles.inputRightText}
                                    placeholder={'请输入'}
                                    onChangeText={(text) => this.setState({hammerNumber:text})}
                                >
                                    {this.state.hammerNumber}
                                </TextInput>
                            </View>
                            <View style={CommonStyle.lineBorderBottomStyle} />

                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>单重(湿重)</Text>
                                    {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                </View>
                                <TextInput 
                                    style={styles.inputRightText}
                                    placeholder={'请输入'}
                                    onChangeText={(text) => this.setState({singleWeight:text})}
                                >
                                    {this.state.singleWeight}
                                </TextInput>
                            </View>
                            <View style={CommonStyle.lineBorderBottomStyle} />

                        </View>
                        :
                        <View/>
                    }
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>抽检尺寸</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入抽检尺寸'}
                            onChangeText={(text) => this.setState({samplingSize:text})}
                        >
                            {this.state.samplingSize}
                        </TextInput>
                    </View> */}
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>锤数</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入锤数'}
                            onChangeText={(text) => this.setState({hammerNumber:text})}
                        >
                            {this.state.hammerNumber}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>单重(湿重)</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入单重(湿重)'}
                            onChangeText={(text) => this.setState({singleWeight:text})}
                        >
                            {this.state.singleWeight}
                        </TextInput>
                    </View> */}

                    {/* <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>运送</Text>
                    </View> */}
                    {/* <View style={styles.rowLabView}>
                        <Text style={styles.leftLabNameTextStyle}>运送人</Text>
                    </View>
                    <View>
                        <FlatList 
                            numColumns = {3}
                            data={this.state.carrierDataSource}
                            ItemSeparatorComponent={this.space}
                            renderItem={({item}) => this.renderCarrierRow(item)}
                            ListEmptyComponent={this.emptyComponent}
                            />
                    </View> */}

                    
                    <View style={[CommonStyle.blockAddCancelSaveStyle]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveSemiFinishedCheck.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect 
                        ref={'SelectProductionTime'} 
                        callBackDateValue={this.callBackSelectProductionTimeValue.bind(this)}
                    />
                    <BottomScrollSelect 
                        ref={'SelectWorkingShift'} 
                        callBackWorkingShiftValue={this.callBackWorkingShiftValue.bind(this)}
                    />
                    {/* <BottomScrollSelect 
                        ref={'SelectStaff'} 
                        callBackStaffValue={this.callBackStaffValue.bind(this)}
                    /> */}
                </ScrollView>
                
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:5,
        marginRight:5,
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})
module.exports = SemiFinishedAdd;