import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,TextInput,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
export default class EnterpriseResumeCollection extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            electronicPhotos:"",
            staffName:"",
            staffSex:"",
            staffTel:"",
            classId:"",
            professionalName:"",
            gradePoint:"",
            personalHonor:"",
            staffAttitude:"",
            searchKeyWord:"",
            topBlockLayoutHeight:0
            
        }
    }
    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
         this._updateState('正在刷新......', true);
         //5秒后结束刷新
          setTimeout( ()=>{
             this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        this.loadRoleList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/collection/staff/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "collectionStaff":'Y',
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }


    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            searchKeyWord:null,
        })
        this.setState({
            currentPage:1,
        })
        let url= "/biz/collection/staff/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "collectionStaff":'Y',
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,  
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

     // 上拉触底加载下一页
     _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadRoleList();
    }

    loadRoleList = () => {
        let url= "/biz/collection/staff/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "collectionStaff": "Y",
        };
        httpPost(url, loadRequest, this.loadRoleListCallBack);
    }

    loadRoleListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/collection/staff/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord":this.state.searchKeyWord,
            "enterpriseId":constants.loginUser.enterpriseId,
            "collectionStaff": "Y",
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    modifyCollectionStaff =(item)=> {
        console.log("=========item========", item);
        let url= "/biz/collection/staff/add";
        let requestParams={
            "staffId":item.staffId,
            "collectionId":item.collectionId,
            "collectionStaff":item.collectionStaff == 'Y'? "N" : "Y",
        };
        httpPost(url, requestParams, this.modifyCollectionStaffCallBack);
    }

   
    modifyCollectionStaffCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.callBackFunction();
            WToast.show({ data: response.data.collectionStaff == 'Y'?"加入收藏成功":"取消收藏成功" });
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        
    }
    

    inviteInterview=(item)=>{
        console.log("=========item========",item);
        let loadUrl = "/biz/collection/staff/add";
        let loadRequest = {
            "staffId":item.staffId,
            "enterpriseId":constants.loginUser.enterpriseId,
            "inviteStaff":item.inviteStaff == 'Y'? "N" : "Y",
        };
        httpPost(loadUrl, loadRequest, this.inviteInterviewCallBack);
    }
    inviteInterviewCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({ data: response.data.inviteStaff == 'Y'?"已发出面试邀请":"已取消邀请" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }

    }
    
    renderRow=(item, index)=>{
        return (
            <View key={item.dailyId} style={{ borderColor: '#F2F5FC', borderBottomWidth: 7, borderTopWidth: 8 }}>
                {/* <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>电子照片：{item.electronicPhotos}</Text>
                </View>  */}
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>姓名：{item.staffName}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>性别：{item.staffSex ==='L' ? "女" : (item.staffSex ==='M' ? "男" : "无")}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>联系电话：{item.staffTel}</Text>
                </View>
                {/* <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>班级：{item.className}</Text>
                </View> */}
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>专业：{item.professionalName}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>综合绩点：{item.comprehensivePoint?(item.comprehensivePoint/100).toFixed(2):"信息尚未完善"}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={[CommonStyle.bodyTextStyle,{fontWeight:'bold'}]}>个人荣誉</Text>
                </View> 
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.personalHonor ? item.personalHonor :"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,{fontWeight:'bold'}]}>学院评价</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.collegeEvaluation ? item.collegeEvaluation : "无"}</Text>
                </View>
                {
                    item.inviteStaff ==='Y' ? 
                    <View style={[styles.bodyViewStyle,{marginBottom:3}]}>
                        <Text style={{fontSize:14,color:'#FF8C28'}}>已发出面试邀请</Text>
                    </View>
                    :
                    null
                }
                {
                    (item.inviteStaff === "Y") ?
                    <View style={[styles.bodyViewStyle,{marginTop:3}]}>
                        <Text style={{fontSize:14,color:'#FF8C28'}}>学生态度：{item.staffAttitude ==='Y' ? "接受邀请" : (item.staffAttitude ==='N' ? "拒绝邀请" : "未表态")}</Text>
                    </View>
                    :
                    <View/>
                }
                <View style={{width: 40, height: 40, 
                    backgroundColor: 'rgba(255,0,0,0.0)', 
                    position:'absolute', 
                    alignItems:'center',
                    justifyContent:'center',
                    right: 10,
                    top:5,
                    }}>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要取消对该学生的收藏吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.modifyCollectionStaff(item)
                                }
                            }
                        ]);
                        
                    }}>
                    {
                        item.collectionStaff == 'Y' ? 
                        <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/start_full.png')}></Image>
                        :
                        <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/start.png')}></Image>
                    }
                    </TouchableOpacity>
                </View>
                {/* <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>最近更新时间：{item.gmtModified ? item.gmtModified: item.gmtCreated }</Text>
                </View> */}
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>                
                    <TouchableOpacity onPress={()=>{this.inviteInterview(item)}}>
                        {
                            item.inviteStaff == 'Y' ?
                            <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle,{borderColor:'rgba(145, 147, 152, 0.5)',flexDirection:"row"}]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 3 }} source={require('../../assets/icon/iconfont/closeGrey.png')}></Image> */}
                                <Text style={[{ color: 'rgba(145, 147, 152, 0.5)', fontSize: 14, lineHeight: 20 }]}>取消邀请</Text>
                            </View>
                            :
                            <View style={[{
                                width: 80,
                                height: 28,
                                flexDirection: "row",
                                justifyContent: 'center',
                                alignItems: 'center',
                                margin: 10,
                                marginRight: 0,
                                borderColor: '#FF8C28',
                                borderWidth: 0.85,
                                borderRadius: 6
                            }]}>
                                {/* <Image style={{ width: 24, height: 24, marginRight: 2 }} source={require('../../assets/icon/iconfont/newShareGreen.png')}></Image> */}
                                <Text style={[{ color: '#FF8C28', fontSize: 14, lineHeight: 20 }]}>邀请面试</Text>
                            </View>    
                        }
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{this.props.navigation.navigate("StudentMyInterViewPreview", 
                        {
                            // 传递回调函数
                            staffId: item.staffId,
                            refresh: this.callBackFunction,
                            userPhotoUrl:constants.image_addr + '/' + item.electronicPhotos,
                            
                        })}}>
                        <View style={[{
                            width: 80,
                            height: 28,
                            flexDirection: "row",
                            justifyContent: 'center',
                            alignItems: 'center',
                            margin: 10,
                            marginRight: 10,
                            borderColor: 'rgba(27, 188, 130, 1)',
                            borderWidth: 0.85,
                            borderRadius: 6
                        }]}>
                            <Image style={{ width: 24, height: 24, marginRight: 0.5, marginLeft: 3 }} source={require('../../assets/icon/iconfont/detailGreen.png')}></Image>
                            <Text style={[{ color: 'rgba(27, 188, 130, 1)', fontSize: 14, lineHeight: 20 }]}>详情</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    



    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='我的收藏'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle, { marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{}}>
                        <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Image  style={{width:18, height:18}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                </View>
                                <TextInput
                                    style={[styles.searchInputText, {}]}
                                        returnKeyType="search"
                                        returnKeyLabel="搜索"
                                        onSubmitEditing={e => {
                                        this.searchByKeyWord();
                                    }}
                                    placeholder={'搜索姓名/班级/专业'}
                                    onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                >
                                    {this.state.searchKeyWord}
                                </TextInput>
                        </View>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        // marginTop: 10,
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        borderWidth: 8
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight:30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    inputRowStyle: {
        // justifyContent: "space-between",
        alignItems: 'center',
        width: screenWidth / 1.05,
        paddingLeft: 5,
        height: 34,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#F2F5FC",
        backgroundColor: '#F2F5FC',
        borderRadius: 15,
        margin: 0,
        marginTop: 0,
        marginLeft: 0,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 14,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    }, 
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
});