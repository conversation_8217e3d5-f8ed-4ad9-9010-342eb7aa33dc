PODS:
  - boost-for-react-native (1.63.0)
  - BVLinearGradient (2.5.6):
    - React
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.64.4)
  - FBReactNativeSpec (0.64.4):
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.4)
    - RCTTypeSafety (= 0.64.4)
    - React-Core (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - Flipper (0.75.1):
    - Flipper-Folly (~> 2.5)
    - Flipper-RSocket (~> 1.3)
  - Flipper-DoubleConversion (1.1.7)
  - Flipper-<PERSON>olly (2.5.3):
    - boost-for-react-native
    - Flipper-DoubleConversion
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.180)
  - Flipper-Glog (0.3.6)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.3.1):
    - Flipper-<PERSON>olly (~> 2.5)
  - FlipperKit (0.75.1):
    - FlipperKit/Core (= 0.75.1)
  - FlipperKit/Core (0.75.1):
    - Flipper (~> 0.75.1)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
  - FlipperKit/CppBridge (0.75.1):
    - Flipper (~> 0.75.1)
  - FlipperKit/FBCxxFollyDynamicConvert (0.75.1):
    - Flipper-Folly (~> 2.5)
  - FlipperKit/FBDefines (0.75.1)
  - FlipperKit/FKPortForwarding (0.75.1):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.75.1)
  - FlipperKit/FlipperKitLayoutPlugin (0.75.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.75.1)
  - FlipperKit/FlipperKitNetworkPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.75.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - glog (0.3.5)
  - libevent (2.1.12)
  - OpenSSL-Universal (1.1.180)
  - Picker (4.3.7):
    - Picker/Core (= 4.3.7)
  - Picker/Core (4.3.7):
    - React
  - RCT-Folly (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
    - RCT-Folly/Default (= 2020.01.13.00)
  - RCT-Folly/Default (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
  - RCTRequired (0.64.4)
  - RCTTypeSafety (0.64.4):
    - FBLazyVector (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.4)
    - React-Core (= 0.64.4)
  - RCTWeChat (1.1.26):
    - React-Core
  - React (0.64.4):
    - React-Core (= 0.64.4)
    - React-Core/DevSupport (= 0.64.4)
    - React-Core/RCTWebSocket (= 0.64.4)
    - React-RCTActionSheet (= 0.64.4)
    - React-RCTAnimation (= 0.64.4)
    - React-RCTBlob (= 0.64.4)
    - React-RCTImage (= 0.64.4)
    - React-RCTLinking (= 0.64.4)
    - React-RCTNetwork (= 0.64.4)
    - React-RCTSettings (= 0.64.4)
    - React-RCTText (= 0.64.4)
    - React-RCTVibration (= 0.64.4)
  - React-callinvoker (0.64.4)
  - React-Core (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.4)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/CoreModulesHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/Default (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/DevSupport (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.4)
    - React-Core/RCTWebSocket (= 0.64.4)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-jsinspector (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTBlobHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTImageHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTTextHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTWebSocket (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.4)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-CoreModules (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.4)
    - React-Core/CoreModulesHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-RCTImage (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-cxxreact (0.64.4):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsinspector (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - React-runtimeexecutor (= 0.64.4)
  - React-jsi (0.64.4):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-jsi/Default (= 0.64.4)
  - React-jsi/Default (0.64.4):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
  - React-jsiexecutor (0.64.4):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-perflogger (= 0.64.4)
  - React-jsinspector (0.64.4)
  - react-native-cameraroll (4.1.2):
    - React-Core
  - react-native-image-picker (4.3.0):
    - React-Core
  - react-native-orientation-locker (1.3.1):
    - React-Core
  - react-native-pager-view (5.4.9):
    - React-Core
  - react-native-safe-area-context (3.3.2):
    - React-Core
  - react-native-slider (3.0.3):
    - React
  - react-native-video (5.2.0):
    - React-Core
    - react-native-video/Video (= 5.2.0)
  - react-native-video/Video (5.2.0):
    - React-Core
  - react-native-webview (11.14.2):
    - React-Core
  - React-perflogger (0.64.4)
  - React-RCTActionSheet (0.64.4):
    - React-Core/RCTActionSheetHeaders (= 0.64.4)
  - React-RCTAnimation (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.4)
    - React-Core/RCTAnimationHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTBlob (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTBlobHeaders (= 0.64.4)
    - React-Core/RCTWebSocket (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-RCTNetwork (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTImage (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.4)
    - React-Core/RCTImageHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-RCTNetwork (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTLinking (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - React-Core/RCTLinkingHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTNetwork (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.4)
    - React-Core/RCTNetworkHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTSettings (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.4)
    - React-Core/RCTSettingsHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTText (0.64.4):
    - React-Core/RCTTextHeaders (= 0.64.4)
  - React-RCTVibration (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTVibrationHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-runtimeexecutor (0.64.4):
    - React-jsi (= 0.64.4)
  - ReactCommon/turbomodule/core (0.64.4):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.4)
    - React-Core (= 0.64.4)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-perflogger (= 0.64.4)
  - RNCAsyncStorage (1.12.1):
    - React-Core
  - RNCMaskedView (0.1.11):
    - React
  - RNDateTimePicker (5.0.1):
    - React-Core
  - RNDeviceInfo (8.4.6):
    - React-Core
  - RNFS (2.18.0):
    - React
  - RNGestureHandler (1.10.3):
    - React-Core
  - RNReanimated (1.13.3):
    - React-Core
  - RNScreens (2.18.1):
    - React-Core
  - RNVectorIcons (7.1.0):
    - React
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (~> 0.75.1)
  - Flipper-DoubleConversion (= 1.1.7)
  - Flipper-Folly (~> 2.5.3)
  - Flipper-Glog (= 0.3.6)
  - Flipper-PeerTalk (~> 0.0.4)
  - Flipper-RSocket (~> 1.3)
  - FlipperKit (~> 0.75.1)
  - FlipperKit/Core (~> 0.75.1)
  - FlipperKit/CppBridge (~> 0.75.1)
  - FlipperKit/FBCxxFollyDynamicConvert (~> 0.75.1)
  - FlipperKit/FBDefines (~> 0.75.1)
  - FlipperKit/FKPortForwarding (~> 0.75.1)
  - FlipperKit/FlipperKitHighlightOverlay (~> 0.75.1)
  - FlipperKit/FlipperKitLayoutPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitLayoutTextSearchable (~> 0.75.1)
  - FlipperKit/FlipperKitNetworkPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitReactPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitUserDefaultsPlugin (~> 0.75.1)
  - FlipperKit/SKIOSNetworkPlugin (~> 0.75.1)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Picker (from `../node_modules/react-native-picker`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - RCTWeChat (from `../node_modules/react-native-wechat-lib`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - "react-native-cameraroll (from `../node_modules/@react-native-community/cameraroll`)"
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-orientation-locker (from `../node_modules/react-native-orientation-locker`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-community/async-storage`)"
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - boost-for-react-native
    - CocoaAsyncSocket
    - Flipper
    - Flipper-DoubleConversion
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - libevent
    - OpenSSL-Universal
    - YogaKit

EXTERNAL SOURCES:
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  Picker:
    :path: "../node_modules/react-native-picker"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  RCTWeChat:
    :path: "../node_modules/react-native-wechat-lib"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-community/cameraroll"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-orientation-locker:
    :path: "../node_modules/react-native-orientation-locker"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-community/async-storage"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost-for-react-native: 39c7adb57c4e60d6c5479dd8623128eb5b3f0f2c
  BVLinearGradient: e3aad03778a456d77928f594a649e96995f1c872
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: cf9b38bf0b2d048436d9a82ad2abe1404f11e7de
  FBLazyVector: fa8275d5086566e22a26ddc385ab5772e7f9b1bd
  FBReactNativeSpec: 4809e19da4d16085a903a35b8a5aafdaf02e1a66
  Flipper: d3da1aa199aad94455ae725e9f3aa43f3ec17021
  Flipper-DoubleConversion: 38631e41ef4f9b12861c67d17cb5518d06badc41
  Flipper-Folly: 755929a4f851b2fb2c347d533a23f191b008554c
  Flipper-Glog: 1dfd6abf1e922806c52ceb8701a3599a79a200a6
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: 127954abe8b162fcaf68d2134d34dc2bd7076154
  FlipperKit: 8a20b5c5fcf9436cac58551dc049867247f64b00
  glog: 73c2498ac6884b13ede40eda8228cb1eee9d9d62
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  OpenSSL-Universal: 1aa4f6a6ee7256b83db99ec1ccdaa80d10f9af9b
  Picker: 4f1722c94059567cabd80e53d0ef72ca290d2b93
  RCT-Folly: ec7a233ccc97cc556cf7237f0db1ff65b986f27c
  RCTRequired: f85fa00af016059cf88b90b8f8ff9a6af9e4b6c3
  RCTTypeSafety: 5279aaf0fb1ad715cbbbbee32d5c98c72598bc9c
  RCTWeChat: 289c92186c1f55716a6378af55388292d94b2d9e
  React: ff4e89fbcb05461c9533fd4da3c0f44cda6ab618
  React-callinvoker: 4670ac7842699e4a39b19a08b4ede02573c1e5dd
  React-Core: ee61e8a8aea912e1504ebec230b3e07d96cf82e1
  React-CoreModules: 8544ba0d319003b33707cdeed66e3122685d31a0
  React-cxxreact: 668500d4ce359515bbf8f907bca0e66fd357d36f
  React-jsi: 64f80675a66899bf0f4a58b8e3908966fa516234
  React-jsiexecutor: 8c077bef1c64430b6034f27df1000d194551e2eb
  React-jsinspector: d4f6973dd474357dbaaf6f52f31ffc713bf3e766
  react-native-cameraroll: 60ac50a5209777cbccfe8d7a62d0743a9da87060
  react-native-image-picker: 1dbc397ab78be161bf08bc4f3ad9c39bec12be11
  react-native-orientation-locker: 998c0744e26624407dac068c04c605b4af7304a2
  react-native-pager-view: 311c10a4eead1be627cad59062aa059d8108b943
  react-native-safe-area-context: 5cf05f49df9d17261e40e518481f2e334c6cd4b5
  react-native-slider: b733e17fdd31186707146debf1f04b5d94aa1a93
  react-native-video: 77ce22be7abff9e373527ca10106a99dea3ba56c
  react-native-webview: 01791db11d056b9e880db8232809b9ee3e10e9cf
  React-perflogger: 5a890ca0911669421b7611661e9b58f91c805f5c
  React-RCTActionSheet: bd180e0879f8424a73650c5c28fbef4f3b5b27fb
  React-RCTAnimation: 1004d2b4be1f2cedfdc4cb2326adc95b989e6c6b
  React-RCTBlob: 55a984137d10e4c41071e8d39374336b48656e54
  React-RCTImage: fa346c899c2f7301eddc79860c37f836db185eb2
  React-RCTLinking: 7af1444a261610ff29a1afeab8c1cfcb5c3280a9
  React-RCTNetwork: fdaad596311091a2c48e0327b8431ba3e011a684
  React-RCTSettings: a7879d48e2951d107763f53e1db80be29f462ab7
  React-RCTText: d7e66b2600487f631531f77bb9d336e33c1187d9
  React-RCTVibration: 761849eea2a1abc99d5e4171bae17ab3da3143ac
  React-runtimeexecutor: 5b441857030bb6c3abaa7517f333cb01875ae499
  ReactCommon: b4a65d2d6e9eeffd4b32dde1245962b3f43907d0
  RNCAsyncStorage: cb9a623793918c6699586281f0b51cbc38f046f9
  RNCMaskedView: f127cd9652acfa31b91dcff613e07ba18b774db6
  RNDateTimePicker: bc5241b3d38713bc8bb4367b23216257d8bf26cf
  RNDeviceInfo: 204a8386385464662f14af06169dc2afec267976
  RNFS: 3ab21fa6c56d65566d1fb26c2228e2b6132e5e32
  RNGestureHandler: a479ebd5ed4221a810967000735517df0d2db211
  RNReanimated: 514a11da3a2bcc6c3dfd9de32b38e2b9bf101926
  RNScreens: f7ad633b2e0190b77b6a7aab7f914fad6f198d8d
  RNVectorIcons: bc69e6a278b14842063605de32bec61f0b251a59
  Yoga: d1fc3575b8b68891ff5ef3c276daa855e841eb32
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: fa2ef55562c9554c6e5f69614eb44e1cb928cb51

COCOAPODS: 1.10.1
