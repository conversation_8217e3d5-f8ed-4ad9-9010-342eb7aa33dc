import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';

var screenHeight = Dimensions.get('window').height;
export default class ProductionLineMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            tenantId:null,
            editDeleteBtnEnable:false,
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============给" + tenantId + "添加车间");
                
                this.setState({
                    tenantId:tenantId,
                    editDeleteBtnEnable:true,
                })
                this.loadProductionLineList(tenantId);
            }
            else {
                console.log("=============车间管理");
                let letTenantId = constants.loginUser.tenantId;
                this.setState({
                    tenantId:letTenantId,
                    editDeleteBtnEnable:false,
                })
                this.loadProductionLineList(letTenantId);
            }
        }
    }

    callBackFunction=(tenantId)=>{
        let url= "/biz/production/line/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "operateTenantId":tenantId ? tenantId : this.state.tenantId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=(tenantId)=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/production/line/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "operateTenantId":tenantId ? tenantId : this.state.tenantId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadProductionLineList();
    }

    loadProductionLineList=(tenantId)=>{
        let url= "/biz/production/line/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "operateTenantId":tenantId ? tenantId : null
        };
        httpPost(url, loadRequest, this.loadProductionLineListCallBack);
    }
    
    loadProductionLineListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // console.log(response.data)
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 删除节点
    deleteProductionLine=(productionLineId)=>{
        console.log("=======delete=productionLineId", productionLineId);
        // 编写删除的请求
        let url= "/biz/production/line/delete";
        let requestParams={'productionLineId':productionLineId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除回调
    deleteCallBack=(response)=>{
      if (response.code == 200 && response.data) {
        WToast.show({data:"删除完成"});
        this.callBackFunction();
      }
      else if (response.code == 401) {
          WToast.show({data:response.message});
          this.props.navigation.navigate("LoginView");
      }
      else {
          WToast.show({data:response.message});
      }
    }

    // 列表底部组件
    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 收款节点单项渲染
    renderRow=(item, index)=>{
        return (
            <View key={item.productionLineId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>编号：{index + 1}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>车间名称：{item.productionLineName}</Text>
                </View>
                {/* {
                item.kilnRoadName ? */}
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>窑道名称：{item.kilnRoadName ? item.kilnRoadName : "无"}</Text>
                </View>
                 {/* :
                <View></View>
                } */}
                
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    <TouchableOpacity onPress={()=>{
                        this.props.navigation.navigate("MachineMgrList", 
                        {
                            // 传递参数
                            productionLineId:item.productionLineId,
                            operateTenantId:item.tenantId,
                            // 传递回调函数
                            refresh: this.callBackFunction 
                        })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{flexDirection:'row',width:70},{backgroundColor:'#FFB800'}]}>
                        <Image  style={{width:27, height:27,marginRight:0}} source={require('../../assets/icon/iconfont/machine.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>机台</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        this.props.navigation.navigate("KilnCarMgrList", 
                        {
                            // 传递参数
                            productionLineId:item.productionLineId,
                            operateTenantId:item.tenantId,
                            // 传递回调函数
                            refresh: this.callBackFunction 
                        })
                    }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{flexDirection:'row',width:70,backgroundColor:'#FA353F'}]}>
                        <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/car1.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>窑车</Text>
                        </View>
                    </TouchableOpacity>
                    {
                        this.state.editDeleteBtnEnable ?
                        <TouchableOpacity onPress={()=>{
                            Alert.alert('确认','您确定要删除该计划吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteProductionLine(item.productionLineId)
                                }
                            }
                            ]);
                        }}>
                            <View style={[CommonStyle.itemBottomDeleteBtnViewStyle]}>
                                <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                </View>
                            </TouchableOpacity>
                        :
                        null

                    }
                    
                    {
                        this.state.editDeleteBtnEnable ? 
                        <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("ProductionLineMgrAdd", 
                            {
                                // 传递参数
                                operateTenantId:this.state.tenantId,
                                productionLineId:item.productionLineId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle]}>
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                </View>
                            </TouchableOpacity>
                        :
                        null
                    }
                    
                </View>
            </View>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
     // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("ProductionLineMgrAdd", 
                {
                    // 传递回调函数
                    operateTenantId:this.state.tenantId,

                    refresh: this.callBackFunction 
                })
            }}>
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='车间管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                            />
                    </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    }
});