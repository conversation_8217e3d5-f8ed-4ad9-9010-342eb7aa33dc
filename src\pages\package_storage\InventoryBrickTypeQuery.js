import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  Linking,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class InventoryBrickTypeQuery extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 6,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      brickTypeName: '',
      brickTypeId: null,
      topBlockLayoutHeight: 0,
      searchKeyWord: null,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    this.loadInventoryList();

    // 加载砖型
    var loadTypeUrl = '/biz/brick/series/type/effBrickTreeCatalog';
    var loadRequest = {currentPage: 1, pageSize: 10000};
    httpPost(loadTypeUrl, loadRequest, (response) => {
      if (response.code == 200 && response.data && response.data) {
        this.setState({
          brickTypeDataSource: response.data,
        });
      } else if (response.code == 401) {
        WToast.show({data: response.message});
        this.props.navigation.navigate('LoginView');
      }
    });
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/inventory/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      (this.state.currentPage == 1 ||
        this.state.totalRecord <= this.state.pageSize) &&
      this.state.brickTypeId == null &&
      this.state.searchKeyWord == null
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      brickTypeName: '',
      brickTypeId: null,
      searchKeyWord: null,
    });
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/inventory/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      brickTypeId: null,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      // var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      // var dataAll = [...dataNew];
      this.setState({
        dataSource: response.data.dataList,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadInventoryList();
  };

  loadInventoryList = () => {
    let url = '/biz/inventory/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      brickTypeId: null,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this.loadInventoryListCallBack);
  };

  loadInventoryListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      var dataAll = [...dataOld, ...dataNew];
      // var dataAll = dataOld.concat(dataNew.filter(v => !dataOld.includes(v)))
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.inventoryId} style={styles.innerViewStyle}>
        {index == 0 ? (
          <View
            style={{
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#FFFFFF',
              borderBottomWidth: 10,
              borderBottomColor: '#F4F7F9',
            }}></View>
        ) : (
          <View></View>
        )}
        <View style={CommonStyle.titleViewStyleSpecial}>
          {/* <Text style={styles.titleTextStyle}>砖型：{item.seriesName}-{item.brickTypeName}</Text> */}
          <Text
            style={[
              CommonStyle.titleTextStyleSpecial,
              {width: screenWidth - 80, height: 45},
            ]}>
            {item.seriesName}-{item.brickTypeName}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            尺寸：{item.standardSize ? item.standardSize : '无'}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            单重(Kg)：{item.standardWeight}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            库区：{item.locationAreaName}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            库位：{item.locationName}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            入库总数：{item.storageInAmount}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            出库总数：{item.storageOutAmount}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            现有库存：{item.storageInAmount - item.storageOutAmount}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            库存总重(Kg)：
            {(
              (item.storageInAmount - item.storageOutAmount) *
              item.standardWeight
            ).toFixed(2)}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={[CommonStyle.titleTextStyle, {marginBottom: 10}]}>
            更新时间：
            {item.gmtModified == null ? item.gmtCreated : item.gmtModified}
          </Text>
        </View>
        {/* <View style={[CommonStyle.itemBottomBtnStyle]}>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("InventoryAdjust", 
                            {
                                // 传递参数
                                brickTypeId:item.brickTypeId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle
                        ]}>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>库存调整</Text>
                        </View>
                    </TouchableOpacity>
                </View> */}
      </View>
    );
  };
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
      // </TouchableOpacity>
      <View style={CommonStyle.viewListLeftViewStyle}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[CommonStyle.btnListLeftBtn]}>
          <Image
            style={CommonStyle.btnListLeftBtnImage}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={CommonStyle.btnListLeftBtnText}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <View style={CommonStyle.viewListRightViewStyle}>
        <TouchableOpacity onPress={() => {}}>
          {/* <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
        </TouchableOpacity>
      </View>
    );
  }
  // 渲染砖型底部滚动数据
  openBrickTypeSelect() {
    if (
      !this.state.brickTypeDataSource ||
      this.state.brickTypeDataSource.length < 1
    ) {
      WToast.show({data: '请先添加砖型'});
      return;
    }
    this.refs.SelectBrickType.showBrickType(
      this.state.selectBrirck,
      this.state.brickTypeDataSource,
    );
  }

  callBackBrickTypeValue(value) {
    console.log('==========砖型选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectBrirck: value,
    });
    // 取选定的砖型ID
    if (value.length == 2) {
      // 加载砖型
      let loadTypeUrl = '/biz/brick/series/type/getBrickByName';
      let loadRequest = {
        brickTypeName: value[1],
        seriesName: value[0],
      };
      httpPost(loadTypeUrl, loadRequest, this._callBackLoadBrickTypeData);
    } else {
      console.log('======选择砖型返回数据不合法', value);
    }
  }

  _callBackLoadBrickTypeData = (response) => {
    if (response.code == 200 && response.data) {
      this.setState({
        brickTypeName: response.data.brickTypeName,
        brickTypeId: response.data.brickTypeId,
        searchKeyWord: null,
      });
      let loadUrl = '/biz/inventory/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        brickTypeId: response.data.brickTypeId,
        searchKeyWord: null,
      };
      httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
      this.setState({
        brickTypeName: '',
        brickTypeId: '',
      });
    }
  };

  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/inventory_brick_type';
    let requestParams = {
      currentPage: 1,
      pageSize: 1000,
      brickTypeId: this.state.brickTypeId ? this.state.brickTypeId : null,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  searchByKeyWord = () => {
    let toastOpts;
    if (!this.state.searchKeyWord) {
      toastOpts = getFailToastOpts('请输入型号或尺寸');
      WToast.show(toastOpts);
      return;
    }
    this.setState({
      brickTypeName: '',
      brickTypeId: null,
    });

    let loadUrl = '/biz/inventory/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      brickTypeId: null,
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="库存清单"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />

        {/*搜索框和导出*/}
        <View
          style={[
            CommonStyle.searchBoxAndExport,
            {backgroundColor: 'white', paddingBottom: 10},
          ]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={[
              CommonStyle.searchTimeBoxWithExport,
              {width: screenWidth / 1.3},
            ]}>
            <View
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                width: screenWidth / 1.7,
                borderRadius: 80,
              }}>
              <Image
                style={{width: 16, height: 16, marginLeft: 7}}
                source={require('../../assets/icon/iconfont/search.png')}></Image>
              <TextInput
                style={{
                  color: 'rgba(rgba(0, 10, 32, 0.45))',
                  fontSize: 14,
                  marginLeft: 5,
                  paddingTop: 0,
                  paddingBottom: 0,
                  paddingRight: 0,
                  paddingLeft: 0,
                  width: '100%',
                }}
                returnKeyType="search"
                onSubmitEditing={(e) => {
                  this.searchByKeyWord();
                }}
                placeholder={'型号或尺寸'}
                onChangeText={(text) => this.setState({searchKeyWord: text})}>
                {this.state.searchKeyWord}
              </TextInput>
            </View>
            {/* <TouchableOpacity onPress={() => this.resetQry()}>
                            <View style={[CommonStyle.resetBtnViewStyle, { width: 10, borderWidth: 0, backgroundColor: 'rgba(0,0,0,0)', borderRadius: 20 }]}>
                                <Image style={{ width: 16, height: 16 }} source={require('../../assets/icon/iconfont/replace.png')}></Image>
                            </View>
                        </TouchableOpacity> */}
          </View>
          <TouchableOpacity onPress={() => this.openBrickTypeSelect()}>
            <View
              style={[
                CommonStyle.itemBottomDetailBtnViewStyle,
                {
                  margin: 0,
                  marginLeft: 8,
                  alignItems: 'center',
                  width: screenWidth * 0.16,
                  backgroundColor: '#1E6EFA',
                  height: 32,
                  borderRadius: 20,
                },
              ]}>
              {/* <View style={[ CommonStyle.itemBottomDetailBtnViewStyle,{ width: 64, height: 32, backgroundColor: "#1E6EFA", flexDirection: "row", borderRadius: 20,alignItems: 'center' }]}> */}
              {/* <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/output.png')}></Image> */}
              <Text
                style={[
                  CommonStyle.itemBottomDetailBtnTextStyle,
                  {fontSize: 14},
                ]}>
                {!this.state.brickTypeName ? '砖型' : this.state.brickTypeName}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
        <View
          style={[
            CommonStyle.itemBottomDetailBtnViewStyle,
            {
              width: 80,
              flexDirection: 'row',
              top: 120,
              zIndex: 100,
              position: 'absolute',
              right: 5,
              opacity: 0.6,
              alignItems: 'center',
              justifyContent: 'center',
              height: 32,
              borderRadius: 8,
              backgroundColor: 'rgba(242, 245, 252, 1)',
            },
          ]}>
          <TouchableOpacity
            onPress={() => {
              Alert.alert('确认', '您确定要导出PDF文件吗？', [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.exportPdfFile();
                  },
                },
              ]);
            }}>
            <View
              style={[
                CommonStyle.itemBottomDetailBtnViewStyle,
                {
                  width: 70,
                  // backgroundColor:"#F2C16D",
                  backgroundColor: 'rgba(242, 245, 252, 1)',
                  flexDirection: 'row',
                },
              ]}>
              <Image
                style={{
                  width: 20,
                  height: 20,
                  marginRight: 5,
                  tintColor: 'rgba(0,10,32,0.85)',
                }}
                source={require('../../assets/icon/iconfont/output.png')}></Image>
              <Text
                style={[
                  CommonStyle.itemBottomDetailBtnTextStyle,
                  {
                    color: 'rgba(0,10,32,0.85)',
                    fontSize: 14,
                  },
                ]}>
                导出
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {
                    width: 100,marginRight:0, backgroundColor: 'green', flexDirection: "row", top:screenHeight/15, height: 35,zIndex: 100,
                    position: 'absolute', right: 15, opacity: 0.6, alignItems: 'center', justifyContent: 'center'
                }]}>
                    <TouchableOpacity onPress={()=>this.openBrickTypeSelect()}>
                        <Text style={[CommonStyle.rightTop50FloatingBlockText,{fontSize:16}]}>
                        {!this.state.brickTypeName ? "砖型" : this.state.brickTypeName}
                        </Text>
                    </TouchableOpacity>
                </View> */}

        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          {/* <View style={CommonStyle.contentViewStyle}> */}
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            ListEmptyComponent={this.emptyComponent}
            keyExtractor={(item) => item.brickTypeId + item.gmtCreated}
            ItemSeparatorComponent={this.space}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
        <BottomScrollSelect
          ref={'SelectBrickType'}
          callBackBrickTypeValue={this.callBackBrickTypeValue.bind(this)}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
  },

  leftLabView: {
    height: 40,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
  },
  searchInputText: {
    width: screenWidth / 2,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },

  innerViewStyleSearch: {
    // marginTop: 10,
    borderColor: '#F4F4F4',
    borderWidth: 8,
  },
  innerViewStyle: {
    marginTop: 10,
    // borderColor: "#F4F4F4",
    // borderWidth: 8,
    // marginLeft:15,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 3,
  },

  titleTextStyle: {
    fontSize: 16,
  },

  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  // itemContentViewStyle:{
  //     flexDirection:'row',
  //     justifyContent:'space-between',
  //     marginLeft:25
  // },
  itemContentChildViewStyle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
});
