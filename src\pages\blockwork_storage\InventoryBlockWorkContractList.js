import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class InventoryBlockWorkContractList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            customerId:"",
            customerName:"",
            selContractId:"",
        }
    }



    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { customerId, customerName} = route.params;
            if (customerId && customerName) {
                console.log("=============customerId" + customerId + "");
                this.setState({
                    customerId:customerId,
                    customerName:customerName
                })
                this.loadContractList(customerId);

            }
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/contract/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "partyA":this.state.customerId,
            "qryContent":"contract"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/contract/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "partyA":this.state.customerId,
            "qryContent":"contract"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadContractList();
    }

    loadContractList=(customerId)=>{
        let url= "/biz/contract/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "partyA": customerId ? customerId : this.state.customerId,
            "qryContent":"contract"
        };
        httpPost(url, loadRequest, this.loadContractListCallBack);
    }

    loadContractListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} >
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                }}  >
                    {/* <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
                </TouchableOpacity>
            </View>
        )
    }

    renderRow=(item, index)=>{
        return (
            <TouchableOpacity onPress={()=>{
                    this.setState({
                        selContractId:item.contractId
                    })
                    this.props.navigation.navigate("InventoryBlockWorkPositionList", 
                    {
                        // 传递参数
                        customerId:this.state.customerId,
                        customerName:this.state.customerName,
                        contractId:item.contractId,
                        contractName:item.contractName,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })
                }}>
                <View key={item.contractId} style={[styles.innerViewStyle,
                this.state.selContractId == item.contractId ? { backgroundColor:'rgba(255,0,0,0.4)',borderRadius:20,hight:80} : {}]}>
                    <View style={CommonStyle.titleViewStyleSpecial}>
                        <Text style={CommonStyle.titleTextStyleSpecial}>编号：{index + 1}</Text>
                    </View>
                    <View style={[CommonStyle.newTitleViewStyle]}>
                        <View>
                            <Text style={[CommonStyle.newTitleTextStyle,{width:null}]} numberOfLines={2}>合同名称：</Text>
                        </View>
                        <View>
                            <Text style={[CommonStyle.newTitleTextStyle]} numberOfLines={2}>{item.contractName}</Text>
                        </View>
                    </View>
                    {/* <View style={CommonStyle.titleViewStyle}>
                        <Text style={CommonStyle.titleTextStyle}>合同名称：{item.contractName}</Text>
                    </View> */}
                    <View style={{width: 40, height: 40, marginBottom:10,
                        backgroundColor: 'rgba(255,0,0,0.0)', 
                        position:'absolute', 
                        alignItems:'center',
                        justifyContent:'center',
                        right: 5,bottom:0
                        }}>
                        <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/enter4.png')}></Image>
                    </View>
                    
                </View>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }


    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }

    render(){
        return(
            <View>
                <CommonHeadScreen fontsize={16} title={'[' + this.state.customerName + ']' + '合同选择'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}> */}
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> 
                        <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            ItemSeparatorComponent={this.space}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={()=>{
                                    this._loadFreshData()
                                }}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                            onEndReachedThreshold={0.2}
                            />
                    {/* </ScrollView> */}
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        marginTop:10,
        marginBottom:10,
        // marginLeft:20,
        marginRight:20,
        // borderWidth: 8
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },

});