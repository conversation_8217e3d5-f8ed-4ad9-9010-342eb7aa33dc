import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,TextInput,
    FlatList,RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class CollegClassStudentList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            searchKeyWord: "",
            topBlockLayoutHeight: 0,
            classId:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { classId } = route.params;
            if (classId) {
                console.log("=============classId" + classId + "");
                this.setState({
                    classId:classId
                })
                this.loadClassStudentList(classId);
            }
        }
        else {
            this.loadClassStudentList();
        }

    }
    loadClassStudentList=(classId)=>{
        let url= "/biz/cr/staff/student/list";
        let data={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "classId":classId?classId:this.state.classId,
        };
        httpPost(url, data, this.callBackLoadStudentList);
    }
    callBackLoadStudentList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
     // 上拉触底加载下一页
     _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadClassStudentList();
    }
    
     // 回调函数
     callBackFunction=()=>{
        let url= "/biz/cr/staff/student/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "classId":this.state.classId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
     // 下拉触顶刷新到第一页
     _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/cr/staff/student/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "classId":this.state.classId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("TemplateMgrAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                {/* <Text style={CommonStyle.headRightText}>新增模版</Text> */}
            </TouchableOpacity>
        )
    }
    deleteStaff =(staffId)=> {
        console.log("=======delete=staffId", staffId);
        let url= "/biz/cr/staff/delete";
        let requestParams={'staffId':staffId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除成功"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }
    staffDisplaySetting = (item, index) => {
        console.log("=======staffDisplaySetting=staffId", item.staffId);
        let requestUrl = "/biz/cr/staff/update_staff_resumeDisplay";
        let requestParams = {
            'staffId': item.staffId,
            'resumeDisplay': item.resumeDisplay === 'Y' ? 'N' : 'Y'
        };
        httpPost(requestUrl, requestParams, (response) => {
            if (response.code == 200) {
                // 更新页面上订单状态
                item.resumeDisplay = (item.resumeDisplay === 'Y' ? 'N' : 'Y');
                WToast.show({ data: (item.resumeDisplay === 'Y' ? '显示' : '隐藏') + "设置完成" });
                let staffDataSource = this.state.dataSource;
                // JS 数组遍历
                staffDataSource.forEach((staffObj) => {
                    if (staffObj.staffId === item.staffId) {
                        staffObj.resumeDisplay = item.resumeDisplay;
                    }
                })
                this.setState({
                    dataSource: staffDataSource,
                })
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }
    renderRow=(item)=>{
        return (
            <View key={item.staffId} style={styles.innerViewStyle}>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>学生姓名：{item.staffName}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>联系电话：{item.staffTel}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>所属班级：{item.className}</Text>
                </View>
                {/* <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>专业绩点：{item.gradePoint/100}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>实践绩点：{item.comprehensiveAbility/100}</Text>
                </View> */}
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>综合绩点：{item.comprehensivePoint/100}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={[CommonStyle.bodyTextStyle,{fontWeight:'bold'}]}>个人荣誉</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>{item.personalHonor ? item.personalHonor : "无"}</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={[CommonStyle.bodyTextStyle,{fontWeight:'bold'}]}>学院评价</Text>
                </View>
                <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>{item.collegeEvaluation ? item.collegeEvaluation : "无"}</Text>
                </View>
                {/* <View style={styles.bodyViewStyle}>
                    <Text style={CommonStyle.bodyTextStyle}>排序：{item.staffSort}</Text>
                </View> */}
                <View style={CommonStyle.itemBottomBtnStyle}>
                <TouchableOpacity onPress={()=>{this.props.navigation.navigate("StudentMyInterViewPreview", 
                        {
                            // 传递回调函数
                            staffId: item.staffId,
                            // userPhotoUrl:constants.image_addr + '/' + item.electronicPhotos,
                            refresh: this.callBackFunction,
                            
                        })}}>
                    <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {marginRight:0, width: 75 ,flexDirection:"row"}]}>
                    <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                        <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>简历</Text>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => {
                        let message = '您确定要' + (item.resumeDisplay === 'Y' ? '隐藏' : '显示') + '该简历吗？';
                        Alert.alert('确认', message, [
                            {
                                text: "取消", onPress: () => {
                                    WToast.show({ data: '点击了取消' });
                                }
                            },
                            {
                                text: "确定", onPress: () => {
                                    WToast.show({ data: '点击了确定' });
                                    this.staffDisplaySetting(item)
                                }
                            }
                        ]);
                    }}>
                        <View style={[item.resumeDisplay === 'Y' ? CommonStyle.itemBottomDeleteBtnViewStyle : [CommonStyle.itemBottomDetailBtnViewStyle, { backgroundColor: "#FFB800" }] 
                            , { width: 75, flexDirection: "row", marginLeft: 10 }
                        ]}>
                            {
                                item.resumeDisplay === 'Y' ?
                                <Image style={{ width: 25, height: 30, marginRight: 5 }} source={require('../../assets/icon/iconfont/hide.png')}></Image>
                                :
                                    <Image style={{ width: 25, height: 30, marginRight: 5 }} source={require('../../assets/icon/iconfont/show.png')}></Image>
                            }
                            <Text style={item.resumeDisplay === 'Y' ? CommonStyle.itemBottomDeleteBtnTextStyle :CommonStyle.itemBottomDetailBtnTextStyle}>
                                {item.resumeDisplay === 'Y' ? '隐藏' : '显示'}
                            </Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该学生吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteStaff(item.staffId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{marginLeft:0,marginRight:0,width:75,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>this.props.navigation.navigate("CollegStudentAdd",
                    {
                        staffId:item.staffId,
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    })}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{marginRight:5,width:75,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                    
                </View>
            </View>
        )
    }
    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    emptyComponent() {
        return <EmptyListComponent/>
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/cr/staff/student/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord":this.state.searchKeyWord,
            "classId":this.state.classId,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title='学生列表'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{}}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                {/* <Text style={styles.leftLabNameTextStyle}>关键字</Text> */}
                                <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                this.searchByKeyWord();
                                }}
                                placeholder={'搜索姓名'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                            {/*<TouchableOpacity onPress={() => {
                                this.searchByKeyWord();
                            }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 70,backgroundColor:'#DEB887',borderColor:'#DEB887' }]}>
                                    <Text style={[CommonStyle.itemBottomDeleteBtnTextStyle,{color:'#FFFFFF'}]}>查询</Text>
                                </View>
                        </TouchableOpacity>*/}
                        </View>
                        {/* <View style={{ height: 5, backgroundColor: '#FFFFFF' }}></View> */}
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                <FlatList 
                        data={this.state.dataSource}
                        ItemSeparatorComponent={this.space}
                        ListEmptyComponent={this.emptyComponent}
                        renderItem={({item}) => this.renderRow(item)}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle:{
        marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10
    },
    titleTextStyle:{
        fontSize:23
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyRowLeftView:{
        width:screenWidth/2-40, 
        flexDirection:'row'
    },
    bodyRowRightView:{
        flexDirection:'row', 
        alignItems:'flex-start',
        paddingLeft:10,
        marginRight:5, 
        justifyContent:'flex-start',
        alignContent:'flex-start'
    }
});