import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Dimensions,Image,Modal} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import { saveImage } from '../../utils/CameraRollUtils';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

import { uploadMultiImageLibrary } from '../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';

export default class MaterialInventoryInAdd extends Component {
    constructor(){
        super()
        this.state = {
            inventoryId: "",
            classifyId: "",
            classifyName: null,
            io:'I',
            weight: "",
            ioDate:"",
            operate:"",
            operator: "",
            selectIoDate:[],
            materialClassifyDataSource:[],
            materialNameDataSource:[],
            reviewerName:"",
            reviewerId:"",           
            // materialImage:"",
            // materialImageUrl:"",
            pictureIndex:0,
            isShowImage: false,     //  显示弹窗组件  
            urls:[],
            compressFileList:[],
            modal:false,
            storageLocationDataSource:[],
            storageLocationAreaDataSource:[],
            selBrickTypeId:"",
            selLocationId:"",
            selLocationAreaId:0,
            location:"",          
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadreviewer();
        this.loadInitData();
        let loadTypeUrl;
        let loadRequest;
        // 原料类别
        loadTypeUrl= "/biz/material/classify/list";
        loadRequest={
            'currentPage':1,
            'pageSize':100,
            "parentClassifyId": null
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    materialClassifyDataSource:response.data.dataList,
                })
            }
        });
        
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { inventoryId, classifyId, classifyName } = route.params;
            if (classifyId) {
                this.setState({
                    classifyId:classifyId,
                })
            }
            if (classifyName) {
                this.setState({
                    classifyName:classifyName,
                })
            }
            if (inventoryId) {
                console.log("========Edit==inventoryId:", inventoryId);
                this.setState({
                    inventoryId:inventoryId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/material/inventory/get";
                loadRequest={'inventoryId':inventoryId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditPurchaseDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectIoDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    ioDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
    }

    // 加载对应库区的库位信息
    loadStorageLocationByAreaId=(locationAreaId)=>{
        let url= "/biz/storage/location/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':1000,
            'locationAreaId':locationAreaId,
            "locationType":'M'
        };
        httpPost(url, loadRequest, this.callBackLoadStorageLocationByAreaId);
    }

    // 库位信息回调
    callBackLoadStorageLocationByAreaId=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("该库区下没有添加原料库位");
                WToast.show(toastOpts);
                this.setState({
                    selLocationId:null,
                    storageLocationDataSource:[]
                })
                return;
            }
            this.setState({
                storageLocationDataSource:response.data.dataList,
            })
            if (this.state.inventoryId && this.state.selLocationId != 0) {
                this.setState({
                    selLocationId:this.state.selLocationId,
                })
            }
            else if (response.data.dataList.length > 0) {
                this.setState({
                    selLocationId:response.data.dataList[0].locationId
                })
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadInitData=()=>{
        // 加载库区列表
        let url= "/biz/storage/location/area/list";
        let request={'currentPage':1,'pageSize':1000};
        httpPost(url, request, this.callBackLoadStorageLocationArea);
    }

    // 库区回调加载
    callBackLoadStorageLocationArea=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库区");
                WToast.show(toastOpts);
                return;
            }
            this.setState({
                storageLocationAreaDataSource:response.data.dataList,
            })
            const { route, navigation } = this.props;
            if (route && route.params) {
                const { inventoryId,locationAreaId } = route.params;
                if(locationAreaId){
                    this.setState({
                        selLocationAreaId:locationAreaId,
                    })
                    this.loadStorageLocationByAreaId(locationAreaId)
                }
                else if (response.data.dataList.length > 0) {
                    this.setState({
                        selLocationAreaId:response.data.dataList[0].locationAreaId
                    })
                    this.loadStorageLocationByAreaId(response.data.dataList[0].locationAreaId);
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadreviewer = ()=>{
        let loadTypeUrl= "/biz/material/inventory/reviewer";
        let loadRequest={
            "io":this.state.io,
            "operaterId": constants.loginUser.userId
        };
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    reviewerName:response.data.userName,
                    reviewerId:response.data.userId,
                })
            }
        });
    }

    loadEditPurchaseDataCallBack=(response)=>{

        if (response.code == 200 && response.data) {
            var selectIoDate;
            if (response.data.ioDate) {
                console.log("=========selectIoDate=1:", response.data.ioDate);
                selectIoDate = response.data.ioDate.split("-");
            }
            else {
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                selectIoDate = [currentDate.getFullYear(), currentDateMonth, currentDateDay];
                this.setState({
                    selectIoDate:selectIoDate,
                })
                console.log("=========selectIoDate===3:", selectIoDate);
            }
            console.log("=========selectIoDate:", selectIoDate);
            this.setState({
                inventoryId:response.data.inventoryId,
                classifyId: response.data.classifyId,
                io: response.data.io,
                weight: response.data.weight,
                ioDate: response.data.ioDate,
                selectIoDate:selectIoDate,
                parentClassifyId:response.data.parentClassifyId,
                // materialImage:response.data.materialImage,
                // materialImageUrl: constants.image_addr + '/' + response.data.materialImage
                compressFileList:response.data.compressFileList,
                selLocationAreaId:response.data.locationAreaId,
                selLocationId:response.data.locationId,
            })

            var urls = [];
            if(response.data.compressFileList && response.data.compressFileList.length > 0){
                for(var i=0;i<response.data.compressFileList.length;i++){
                    var url = {
                        url:constants.image_addr + '/' +  response.data.compressFileList[i].compressFile
                    } 
                    urls=urls.concat(url)
                    console.log(url)
                }
            }
            this.setState({
                urls:urls
            })

            // 调接口查询原料名称
            let loadTypeUrl;
            let loadRequest;
            loadTypeUrl= "/biz/material/classify/list";
            loadRequest={
                'currentPage':1,
                'pageSize':100,
                "parentClassifyId":response.data.parentClassifyId
            };
            httpPost(loadTypeUrl, loadRequest, this.materialNameLoadCallBack);  
        }
    }
    materialNameLoadCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                materialNameDataSource:response.data.dataList,
            })
            console.log("=============this.state.materialNameDataSource:", this.state.materialNameDataSource);
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("MaterialInventoryInList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>原料入库</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>原料入库</Text>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.checkId) {
                    return;
                }
                    this.setState({
                        classifyId:item.classifyId,
                    })
                }}>
                <View key={item.orderId} style={[item.classifyId===this.state.classifyId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.classifyId===this.state.classifyId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.classifyName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    openIoDate(){
        this.refs.SelectIoDate.showDate(this.state.selectIoDate)
    }
    callBackSelectIoDateValue(value){
        console.log("==========入库时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectIoDate:value
        })
        if (this.state.selectIoDate && this.state.selectIoDate.length) {
            var ioDate = "";
            var vartime;
            for(var index=0;index<this.state.selectIoDate.length;index++) {
                vartime = this.state.selectIoDate[index];
                if (index===0) {
                    ioDate += vartime;
                }
                else{
                    ioDate += "-" + vartime;
                }
            }
            this.setState({
                ioDate:ioDate
            })
        }
    }

    saveMaterialInventoryIn =()=> {
        console.log("=======saveMaterialInventoryIn");
        let toastOpts;
        if (!this.state.classifyId) {
            toastOpts = getFailToastOpts("请选择原料名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.weight) {
            toastOpts = getFailToastOpts("请输入重量");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/material/inventory/add";
        if (this.state.inventoryId) {
            console.log("=========Edit=save===inventoryId", this.state.inventoryId)
            url= "/biz/material/inventory/modify";
        }
        let requestParams={
            "inventoryId": this.state.inventoryId,
            "io": this.state.io,
            "classifyId": this.state.classifyId,
            "weight": this.state.weight,
            "ioDate": this.state.ioDate,
            "operator": constants.loginUser.userName,
            "operatorId":constants.loginUser.userId,
            "currentAuditUserId": this.state.reviewerId,
            // "materialImage":this.state.materialImage
            "compressFileList":this.state.compressFileList,
            "locationId":this.state.selLocationId,
        };
        httpPost(url, requestParams, this.saveStorageCallBack);
    }
    
    // 保存回调函数
    saveStorageCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    // 库区
    renderLocationAreaRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.inventoryId) {
                    let toastOpts = getFailToastOpts('不能编辑');
                    WToast.show(toastOpts);
                    return;
                }
                this.setState({
                    selLocationAreaId:item.locationAreaId
                }) 
                this.loadStorageLocationByAreaId(item.locationAreaId);
            }}>
                <View key={item.locationAreaId} style={[this.state.inventoryId ? CommonStyle.disableViewStyle : null, item.locationAreaId===this.state.selLocationAreaId? 
                    // CommonStyle.selectedBlockItemViewStyle 
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    : 
                    // CommonStyle.blockItemViewStyle
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginLeft:16,
                        // marginRight: 8,
                        marginTop: 8,
                        marginBottom: 6,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        //width: (screenWidth - 54)/3,
                        borderRadius: 4  
                    }
                    ]}>
                    <Text style={[item.locationAreaId===this.state.selLocationAreaId? 
                        // CommonStyle.selectedBlockItemTextStyle16 
                        {
                            color: '#1E6EFA'
                        }
                        : 
                        // CommonStyle.blockItemTextStyle16
                        {
                            color: '#404956'
                        }
                        ,
                        {
                            fontSize: 16, textAlign : 'center'
                        },
                        {
                            paddingLeft:10,paddingRight:10
                        }
                        ]}>
                        {item.locationAreaName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 库位
    renderLocationRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.inventoryId) {
                    let toastOpts = getFailToastOpts('不能编辑');
                    WToast.show(toastOpts);
                    return;
                }
                this.setState({
                    selLocationId:item.locationId
                })
            }}>
                <View key={item.locationId} style={[this.state.inventoryId ? CommonStyle.disableViewStyle : null, item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle ]}>
                    <Text style={item.locationId===this.state.selLocationId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.locationName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '入库'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <View style={CommonStyle.lineHeadBorderStyle} />
                {/* <View style={[styles.innerViewStyle,{marginTop:0, index:1000}]}>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, fontWeight:'bold', marginRight:10}]}>
                         原料名称：{this.state.classifyName}
                     </Text>
                </View> */}
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>原料类别</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    
                    <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.95, justifyContent:'flex-start'}]}>
                    {this.state.materialClassifyDataSource.map((item, key)=>{
                        return(
                            <TouchableOpacity onPress={()=>{
                                this.setState({
                                    parentClassifyId:item.classifyId,
                                    classifyId:null,
                                })
                                WToast.show({data:'点击了' + item.classifyName});
                                // 调接口查询原料名称
                                let loadTypeUrl;
                                let loadRequest;
                                loadTypeUrl= "/biz/material/classify/list";
                                loadRequest={
                                    'currentPage':1,
                                    'pageSize':100,
                                    "parentClassifyId": item.classifyId
                                };
                                httpPost(loadTypeUrl, loadRequest, (response)=>{
                                    if (response.code == 200 && response.data && response.data.dataList) {
                                        this.setState({
                                            materialNameDataSource:response.data.dataList,
                                        })
                                    }
                                });

                            }}>
                                {/* <View style={{marginLeft:16}}> */}
                                <View key={item.classifyId} style={[item.classifyId===this.state.parentClassifyId ? 
                                    // CommonStyle.selectedBlockItemViewStyle 
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    : 
                                    // CommonStyle.blockItemViewStyle
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginLeft:16,
                                        // marginRight: 4,
                                        marginTop: 8,
                                        marginBottom: 6,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 40,
                                        //width: (screenWidth - 54)/3,
                                        borderRadius: 4
                                    }
                                    ] }>
                                    <Text style={[item.classifyId===this.state.parentClassifyId ? 
                                    // CommonStyle.selectedBlockItemTextStyle16 
                                    {
                                        color: '#1E6EFA'
                                    }
                                    : 
                                    // CommonStyle.blockItemTextStyle16 
                                    {
                                        color: '#404956'
                                    }
                                    ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }, 
                                    {
                                        paddingLeft:5,paddingRight:5
                                    }
                                    ]}>
                                        {item.classifyName}
                                    </Text>
                                </View>
                                {/* </View> */}
                            </TouchableOpacity>
                            
                        )
                    })}
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>原料名称</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    
                    <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.95, justifyContent:'flex-start'}]}>
                        {!this.state.materialNameDataSource || this.state.materialNameDataSource.length < 1 ? 
                        <View style={{width:screenWidth}}><EmptyRowViewComponent/></View> 
                        : <Text></Text>} 
                    {this.state.materialNameDataSource.map((item, key)=>{
                        return(
                            <TouchableOpacity onPress={()=>{
                                this.setState({
                                    classifyId:item.classifyId,
                                    classifyName:item.classifyName,
                                })
                                WToast.show({data:'点击了' + item.classifyName});
                            }}>
                                <View key={item.classifyId} style={[item.classifyId===this.state.classifyId ? 
                                    // CommonStyle.selectedBlockItemViewStyle 
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    : 
                                    // CommonStyle.blockItemViewStyle
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginLeft:16,
                                        // marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 6,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        //width: (screenWidth - 54)/3,
                                        borderRadius: 4
                                    }
                                    ] }>
                                    <Text style={[item.classifyId===this.state.classifyId ? 
                                        // CommonStyle.selectedBlockItemTextStyle16 
                                        {
                                            color: '#1E6EFA'
                                        }
                                        : 
                                        // CommonStyle.blockItemTextStyle16 
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                        {
                                            fontSize: 16, textAlign : 'center'
                                        },
                                        {
                                            paddingLeft:5,paddingRight:5
                                        }
                                        ]}>
                                        {item.classifyName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                            
                        )
                    })}
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>库区</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationAreaDataSource && this.state.storageLocationAreaDataSource.length > 0) 
                            ? 
                            this.state.storageLocationAreaDataSource.map((item, index)=>{
                                return this.renderLocationAreaRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>库位</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.storageLocationDataSource && this.state.storageLocationDataSource.length > 0) 
                            ? 
                            this.state.storageLocationDataSource.map((item, index)=>{
                                return this.renderLocationRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>重量(吨)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({weight:text})}
                        >
                            {this.state.weight}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>入库时间</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openIoDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle,{borderWidth:0}]}>
                                <Text style={{color:'#A0A0A0', fontSize:15}}>
                                    {!this.state.ioDate ? "请选择入库时间" : this.state.ioDate}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    {
                        this.state.reviewerName?
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>审核人</Text>
                            </View>
                            <TextInput 
                                editable={false}
                                style={styles.inputRightText}
                                placeholder={'请输入审核人'}
                                onChangeText={(text) => this.setState({reviewerName:text})}
                            >
                                {this.state.reviewerName}
                            </TextInput>
                        </View>
                        :
                        <View/>
                    }
                    <View style={CommonStyle.lineBorderBottomStyle} />

                    <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>附件</Text>
                    </View>
                    <View style={{marginLeft:15}}>
                            
                            {
                                this.state.compressFileList && this.state.compressFileList.length > 0 ?
                                (
                                    <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                        {
                                            this.state.compressFileList.map((item,index) =>{
                                                return(
                                                    <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex'}]}>
                                                    <TouchableOpacity
                                                        style={{position:'absolute',left:110,top:-10,zIndex:1000}}
                                                        onPress={() => {
                                                            console.log("========deletePhoto")
                                                            var urls = this.state.urls;
                                                            var compressFileList = this.state.compressFileList;

                                                            urls.splice(index,1);
                                                            compressFileList.splice(index,1);
                                                            console.log(urls)
                                                            console.log(this.state.compressFileList)

                                                            this.setState({
                                                                urls:urls,
                                                                compressFileList:compressFileList
                                                            })
                                                        }}
                                                    >
                                                        <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/deleteRed.png')}></Image>
                                                                
                                                    </TouchableOpacity>
                                                    <TouchableOpacity onPress={() => {
                                                        this.setState({
                                                            isShowImage:true,
                                                            pictureIndex:index
                                                        })
                                                        // uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                                        //     console.log("========imageUploadResponse", imageUploadResponse)
                                                        //     if (imageUploadResponse.code === 200) {
                                                        //         WToast.show({ data: "上传成功" });
                                                        //         let compressFileList = imageUploadResponse.data
                                                        //         this.setState({
                                                        //             compressFileList: compressFileList
                                                        //         })
                                                        //     }
                                                        //     else {
                                                        //         WToast.show({ data: imageUploadResponse.message });
                                                        //     }
                                                        // });
        
                                                    }}>
                                                        <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />                                                    
                                                                
                                                    </TouchableOpacity>
                                                    <Modal visible={this.state.isShowImage} transparent={true}>
                                                        <ImageViewer onClick={()=>{this.setState({isShowImage:false})}}  index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                         onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} 
                                                         onSave={() => {
                                                            saveImage( this.state.urls[this.state.pictureIndex].url)
                                                         }}/>
                                                    </Modal>
                                                </View>
                                                )
                                                
                                            })
                                        }
                                        <View style={[{ width: 120,height:150,marginLeft:20,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                            <TouchableOpacity onPress={() => {
                                                uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                                    console.log("========imageUploadResponse", imageUploadResponse)
                                                    if (imageUploadResponse.code === 200) {
                                                        WToast.show({ data: "上传成功" });
                                                        let compressFileList = imageUploadResponse.data
                                                        this.setState({
                                                            compressFileList: this.state.compressFileList.concat(compressFileList)
                                                        })
                                                        var urls = this.state.urls;
                                                        if(compressFileList && compressFileList.length > 0){
                                                            for(var i=0;i<compressFileList.length;i++){
                                                                var url = {
                                                                    url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                                } 
                                                                urls=urls.concat(url)
                                                                console.log(url)
                                                            }
                                                        }
                                                        this.setState({
                                                            urls:urls
                                                        })
                                                    }
                                                    else {
                                                        WToast.show({ data: imageUploadResponse.message });
                                                    }
                                                });

                                            }}>
                                                {/* <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                                    <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                                </View> */}
                                                <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                                    <Image style={{height: 66, width:66}}  source={require('../../assets/icon/iconfont/addPhoto.png')} ></Image>
                                                    {/* <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image> */}
                                                </View>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                    
                                )
                                
                                :
                                <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                <TouchableOpacity onPress={() => {
                                        uploadMultiImageLibrary(6, "attachment_image", (imageUploadResponse) => {
                                            console.log("========imageUploadResponse", imageUploadResponse)
                                            if (imageUploadResponse.code === 200) {
                                                WToast.show({ data: "上传成功" });
                                                let compressFileList = imageUploadResponse.data
                                                this.setState({
                                                    compressFileList: compressFileList
                                                })
                                                var urls = [];
                                                if(compressFileList && compressFileList.length > 0){
                                                    for(var i=0;i<compressFileList.length;i++){
                                                        var url = {
                                                            url:constants.image_addr + '/' +  compressFileList[i].compressFile
                                                        } 
                                                        urls=urls.concat(url)
                                                        console.log(url)
                                                    }
                                                }
                                                this.setState({
                                                    urls:urls
                                                })
                                            }
                                            else {
                                                WToast.show({ data: imageUploadResponse.message });
                                            }
                                        });

                                    }}>
                                        {/* <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                            <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                        </View> */}
                                        <View style={{width:120,height:150,display:'flex',justifyContent:'center',alignItems:'center'}}>
                                            <Image style={{height: 66, width:66}}  source={require('../../assets/icon/iconfont/addPhoto.png')} ></Image>
                                            {/* <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image> */}
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            }
                            
                        </View>
                        <View style={CommonStyle.lineBorderBottomStyle} />


                        {/* <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>附件</Text>
                        </View>
                        <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                        <TouchableOpacity 
                            onPress={() => {
                                uploadImageLibrary(this.state.materialImage, "attachment_image", (imageUploadResponse) => {
                                    console.log("========imageUploadResponse", imageUploadResponse)
                                    if (imageUploadResponse.code === 200) {
                                        WToast.show({ data: "成功上传" });
                                        let { compressFile } = imageUploadResponse.data
                                        this.setState({
                                            materialImageUrl: constants.image_addr + '/' + compressFile,
                                            materialImage:compressFile,
                                        })
                                        // httpPost("/biz/material/inventory/modify_inventory_photo", {
                                        //     "inventoryId":this.state.inventoryId, 
                                        //     "materialImage":compressFile
                                        // }, (updateResponse)=>{
                                        //     if (updateResponse.code === 200) {
                                        //         console.log("======附件信息已经更新")
                                        //     }
                                        // })
                                    }
                                    else {
                                        WToast.show({ data: imageUploadResponse.message });
                                    }
                                });

                        }}>
                                {
                                    this.state.materialImage ?
                                    <Image source={{ uri: this.state.materialImageUrl }} style={{width:120,height:150,justifyContent:'center',alignItems:'center'}} />
                                    :
                                    <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                }
                        </TouchableOpacity>
                                                    
                    </View> */}
                    
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{ marginTop: 20, marginBottom:20}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveMaterialInventoryIn.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <BottomScrollSelect 
                        ref={'SelectIoDate'} 
                        callBackDateValue={this.callBackSelectIoDateValue.bind(this)}
                    />
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },

    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        // marginLeft:15,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:6,
        marginRight:5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:6,
        marginRight:5,
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})