import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,KeyboardAvoidingView,TouchableOpacity,Dimensions,Alert,Modal,Image} from 'react-native';
import { or } from 'react-native-reanimated';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class EngineeringAcceptanceMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            operate:"",
            //工程接收编号
            acceptanceId:"",
            // 订单编号
            orderId:"",
            // 接收数量（块）
            acceptanceAmount:"",
            // 单重（kg）
            pieceWeight:"",
            //总重（吨）
            totalWeight:"",
            //订单名称
            orderName:"",
            //部位
            positionName:"",
            //砖型
            brickTypeName:"",
            //客户名称
            customerName:"",
            //合同名称
            contractName:"",
            //订单数据
            orderDataSource:[],
            //客户数据
            customerDataSource:[],
            selectCustomer:[],
            //合同数据
            contractDataSource:[],
            selectContract:[],
            modal:false,
            searchKeyWord:null,
            _orderDataSource:[],
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        // loadTypeUrl= "/biz/order/list";
        // loadRequest={
        // "currentPage":1,
        // "pageSize":100,
        // // "display":"Y",
        // "excludeOrderStateList":[
        //     "A","K","0XX"
        // ]
        // };
        // httpPost(loadTypeUrl, loadRequest, this.loadOrderDataCallBack);
        loadTypeUrl= "/biz/tenant/customer/getCustomerContractOrderTree";
        loadRequest={'currentPage':1,'pageSize':1000};
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadCustomerData);

        const { route, navigation } = this.props;
        if (route && route.params) {
            const { acceptanceId,orderId,customerName,positionName,contractName } = route.params;
            console.log("客户的名称是====" + customerName);
            console.log("合同的名称是=========" + contractName);
            if(customerName) {
                this.setState({
                    selectCustomer:[customerName],
                    customerName:customerName,
                    orderId:orderId,
                    positionName:positionName,
                })
                // this.loadContractList(customerName) 
            }
            if(contractName){
                this.setState({
                    selectContract:[contractName],
                    contractName:contractName
                })
                // this.loadOrder();
            }
            if (acceptanceId) {
                this.setState({
                    acceptanceId:acceptanceId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/engineering/acceptance/get";
                loadRequest={'acceptanceId': acceptanceId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditEngineeringAcceptanceDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增",
                })
            }
        }
    }

    callBackLoadCustomerData=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                customerDataSource:response.data
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // loadOrder=()=>{
    //     let loadTypeUrl= "/biz/order/list";
    //     let loadRequest={
    //         "contractName":contractName,
    //         "currentPage":1,
    //         "pageSize":1000,
    //         // "display":"Y",
    //         "excludeOrderStateList":[
    //             "A","K","0XX"
    //         ],
    //         "qryContent":"order",
    //         "searchKeyWord":this.state.searchKeyWord,
    //     };
    //     httpPost(loadTypeUrl, loadRequest, this.loadOrderDataCallBack);
    // }

    loadOrderDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                orderDataSource:response.data.dataList,
            })
        } else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadContractList=(customerName)=>{
        let loadTypeUrl= "/biz/contract/list";
        let loadRequest={
            "customerName":customerName,
            "currentPage":1,
            "pageSize":1000,
            "contractState":'0AA',
            "qryContent":"contract"
            // "display":"Y",
            // "excludeOrderStateList":[
            //     "A","K","0XX"
            // ]
        };
        httpPost(loadTypeUrl, loadRequest, this.loadContractDataCallBack);
    }

    loadContractDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                contractDataSource:response.data.dataList,
            })
        } else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // loadEditOrderDataCallBack=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             orderName:response.data.orderName,
    //             positionName:response.data.positionName,
    //             brickTypeName:response.data.brickTypeName,
    //             customerName:response.data.customerName,
    //         })
    //     }
    // }


    loadEditEngineeringAcceptanceDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                orderName:response.data.orderName,
                acceptanceAmount:response.data.acceptanceAmount,
                pieceWeight:response.data.pieceWeight,
                totalWeight:response.data.totalWeight,
            })
        } else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // loadSchedulData =()=>{
    //     // 加载未排产的订单
    //     let loadUrl= "/biz/order/list";
    //     let loadRequest={"currentPage":1,"pageSize":100,"orderState":"K"};
    //     httpPost(loadUrl, loadRequest, this.loadOrderData);
    
    // }

    // loadOrderData=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         if (response.data.dataList.length <= 0) {
    //             Alert.alert('确认','没有需要收货的订单',[
    //                 {
    //                     text:"确定", onPress:()=>{
    //                         WToast.show({data:'点击了确定'});
    //                         this.props.navigation.goBack();
    //                     }
    //                 }
    //             ]);
    //         }
    //         this.setState({
    //             orderDataSource:response.data.dataList
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }



    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("EngineeringAcceptanceMgrList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>工程接收</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>工程接收</Text>
                </TouchableOpacity>
            </View>
        )
    }

    openCustomerSelect(){
        if (!this.state.customerDataSource || this.state.customerDataSource.length < 1) {
            WToast.show({data:"请先添加客户"});
            return
        }
        this.setState({
            contractDataSource:[]
        })
        this.refs.SelectCustomer.showCustomer(this.state.selectCustomer, this.state.customerDataSource)
    }

    openContractSelect(){
        if(this.state.customerName){
            if (!this.state.contractDataSource || this.state.contractDataSource.length < 1) {
                WToast.show({data:"请先添加合同"});
                return;
            }
            this.refs.SelectContract.showContract(this.state.selectContract, this.state.contractDataSource)
        }
        else{
            WToast.show({data:"请先添加客户"});
            return;
        }
        
    }

    callBackContractValue(value){
        console.log("==========合同选择结果：", value)
        if (!value) {
            return;
        }
        var contractName = value.toString();
        this.setState({
            contractName:contractName,
            selectContract:value,
            contractName:value,
            orderId:"",
            searchKeyWord:"",
            _orderDataSource:[]
        })
        this.loadOrderList(contractName);

        // let loadTypeUrl= "/biz/order/list";
        // let loadRequest={
        // "contractName":contractName,
        // "currentPage":1,
        // "pageSize":1000,
        // // "display":"Y",
        // "excludeOrderStateList":[
        //     "A","K","0XX"
        // ],
        // "qryContent":"order"
        // };
        // httpPost(loadTypeUrl, loadRequest, this.loadOrderDataCallBack);



        // let loadUrl= "/biz/tenant/customer/getCustomerByName";
        // let loadRequest1={
        //     "customerName":customerName
        // };
        // httpPost(loadUrl, loadRequest1, this.callBackLoadCustomerDetailData);

    }

    loadOrderList=(contractName)=>{
        for(var i = 0; i < this.state.contractDataSource.length; i++){
            if(contractName == this.state.contractDataSource[i].contractName){
                var orderList = [];
                var orderDTOList = this.state.contractDataSource[i].orderDTOList;
                if(orderDTOList != null && orderDTOList.length > 0){
                    for(var m = 0; m < orderDTOList.length; m++){
                        if(orderDTOList[m].orderState != "A" && orderDTOList[m].orderState != "K" && orderDTOList[m].orderState != "0XX"){
                            orderList = orderList.concat(orderDTOList[m])
                        }
                    }
                }
                
                this.setState({
                    orderDataSource:orderList
                })
            }
        }
    }


    callBackCustomerValue(value){

        console.log("==========客户选择结果：", value)
        if (!value) {
            return;
        }
        var customerName = value.toString();
        this.setState({
            customerName:customerName,
            selectCustomer:value,
            selectContract:[],
            contractName:"",
            orderDataSource:[],
            positionName:"",
            orderId:"",
            searchKeyWord:"",
            _orderDataSource:[]
        })

        this.loadContractData(customerName)

        // let loadUrl= "/biz/tenant/customer/getCustomerByName";
        // let loadRequest1={
        //     "customerName":customerName
        // };
        // httpPost(loadUrl, loadRequest1, this.callBackLoadCustomerDetailData);
        

    }
    // callBackLoadCustomerDetailData=(response)=>{
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             customerName:response.data.customerName
    //         })
    //         this.loadContractList(response.data.customerName);
            
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    loadContractData=(customerName)=>{
        for(var i=0; i< this.state.customerDataSource.length; i++){
            if(customerName == this.state.customerDataSource[i].customerName){
                this.setState({
                    contractDataSource:this.state.customerDataSource[i].contractList
                })
            }
        }
    }

    loadOrder=()=>{
        var _orderDataSource = copyArr(this.state.orderDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length >0) {
            _orderDataSource = _orderDataSource.filter(item => item.orderName.indexOf(this.state.searchKeyWord) > 0);
        }
        this.setState({
            _orderDataSource:_orderDataSource,
        })
    }

    

    saveEngineeringAcceptance =()=> {
        console.log("=======saveEngineeringAcceptance");
        let toastOpts;
        if (!this.state.customerName) {
            toastOpts = getFailToastOpts("请选择客户");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contractName) {
            toastOpts = getFailToastOpts("请选择合同");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.orderId) {
            toastOpts = getFailToastOpts("请选择产品");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.acceptanceAmount) {
            toastOpts = getFailToastOpts("请输入接收数量");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.pieceWeight) {
            toastOpts = getFailToastOpts("请输入单重");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.totalWeight) {
            toastOpts = getFailToastOpts("请输入总重");
            WToast.show(toastOpts)
            return;
        }

        let url= "/biz/engineering/acceptance/add";

        if (this.state.acceptanceId) {
            console.log("=========Edit===acceptanceId", this.state.acceptanceId)
            url= "/biz/engineering/acceptance/modify";
        }

        let requestParams={
            'acceptanceId':this.state.acceptanceId,
            'orderId':this.state.orderId,
            'acceptanceAmount':this.state.acceptanceAmount,
            'pieceWeight':this.state.pieceWeight,
            'totalWeight':this.state.totalWeight?this.changeTwoDecimal(this.state.totalWeight):this.state.totalWeight,
        };
        httpPost(url, requestParams, this.saveOrder_call_back);
    }

    // 保存回调函数
    saveOrder_call_back=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    changeTwoDecimal=(x)=>{
        var f_x = parseFloat(x);
        if (isNaN(f_x))
        {
            alert('function:changeTwoDecimal->parameter error');
            return false;
        }
        f_x = Math.round(f_x *100)/100;

        return f_x;
    }

    renderOrderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if(this.state.acceptanceId){
                    // WToast.show({data:"编辑只能编辑客户、合同、砖型以外的信息"})
                    return;
                }
                this.setState({
                    orderId:item.orderId,
                    orderName:item.orderName
                })
            WToast.show({data:'点击了' + item.orderName});
                    // 调接口查询原料名称
                    let loadTypeUrl;
                    let loadRequest;
                    loadTypeUrl= "/biz/order/get";
                    loadRequest={
                        'currentPage':1,
                        'pageSize':1000,
                        "orderId":item.orderId
                    };
                    console.log("========目前的orderId是"+ item.orderId);
                    httpPost(loadTypeUrl, loadRequest, (response)=>{
                        if (response.code == 200 && response.data) {
                            this.setState({
                                positionName:response.data.positionName,
                            })
                        }
                    });
        }
            }>
                <View key={item.orderId} style={[item.orderId===this.state.orderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle,this.state.acceptanceId? CommonStyle.disableViewStyle : ''] }>
                    <Text style={item.orderId===this.state.orderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    
    render(){
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: -2 }} />

                <ScrollView style={CommonStyle.formContentViewStyle}>

                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>客户名称</Text>
                    </View>
                    
                    <TouchableOpacity onPress={()=>{
                            if(this.state.acceptanceId){
                                WToast.show({data:"编辑只能编辑客户、合同、产品以外的信息"})
                            }
                            else{
                                this.openCustomerSelect()
                            }
                        }
                        }>
                        <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 10), flexWrap: 'wrap', borderWidth: 0}]}>
                            {
                                this.state.customerName ?
                                    <Text style={{color: '#000000', fontSize: 15}}>{this.state.customerName}</Text>
                                    :
                                    <Text style={[{ color: '#A0A0A0', fontSize: 15}]}>请选择客户</Text>
                            }
                            <Image style={{ width: 22, height: 22, position:'absolute', right: 10, top: 11 }} source={require('../../assets/icon/iconfont/arrowRight.png')}></Image>
                        </View>
                    </TouchableOpacity>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>

                        <Text style={styles.leftLabNameTextStyle}>合同名称</Text>
                    </View>
                    <TouchableOpacity onPress={()=>{
                            if(this.state.acceptanceId){
                                WToast.show({data:"编辑只能编辑客户、合同、产品以外的信息"})
                            }
                            else{
                                this.openContractSelect()
                            }
                        }}>
                        <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 10), flexWrap: 'wrap', borderWidth: 0}]}>
                            {
                                this.state.contractName ?
                                    <Text style={{color: '#000000', fontSize: 15}}>{this.state.contractName}</Text>
                                    :
                                    <Text style={[{ color: '#A0A0A0', fontSize: 15}]}>请选择合同</Text>
                            }
                            <Image style={{ width: 22, height: 22, position:'absolute', right: 10, top: 11 }} source={require('../../assets/icon/iconfont/arrowRight.png')}></Image>
                        </View>
                    </TouchableOpacity>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>产品名称</Text>
                        </View>
                        <View style={[{ flexWrap: 'wrap' ,flexDirection:'row',marginLeft:6}, this.state.acceptanceId ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (this.state.acceptanceId) {
                                    return;
                                }
                                if (!this.state.orderDataSource || this.state.orderDataSource.length === 0) {
                                    let errorMsg = '没有接收的订单';
                                    if (!this.state.customerName || this.state.customerName.length === 0) {
                                        errorMsg = "请先选择客户";
                                    }
                                    else if (!this.state.contractName || this.state.contractName.length === 0) {
                                        errorMsg = "请先选择合同";
                                    }
                                    Alert.alert('确认', errorMsg, [
                                        {
                                            text: "确定", onPress: () => {
                                                WToast.show({ data: '点击了确定' });
                                            }
                                        }
                                    ]);
                                    return;
                                }
                                if (!this.state._orderDataSource || this.state._orderDataSource.length === 0) {
                                    this.setState({
                                        _orderDataSource: copyArr(this.state.orderDataSource),
                                    })
                                }

                                this.setState({
                                    modal: true,
                                })

                                if (!this.state.orderId && this.state.orderDataSource && this.state.orderDataSource.length > 0) {
                                    this.setState({
                                        orderId: this.state.orderDataSource[0].orderId,
                                        orderName: this.state.orderDataSource[0].orderName,
                                    })
                                }
                            }}>
                                <View style={[this.state.orderId &&  this.state.orderName ?
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    :
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginRight: 8,
                                        marginTop: 8,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        paddingLeft:6,
                                        paddingRight:6,
                                        // width: (screenWidth - 54)/2,
                                        borderRadius: 4,
                                    }
                                ]}>
                                    <Text style={[this.state.orderId && this.state.orderName ?
                                        {
                                            color: '#1E6EFA'
                                        }
                                        :
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>
                                        选择产品
                                        {this.state.orderId && this.state.orderName ? ("：" + this.state.orderName) : null}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput 
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({searchKeyWord:text})}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={()=>{
                                        this.loadOrder();
                                        }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{flexDirection:'row', flexWrap:'wrap', overflow:'scroll'}}>
                                    {
                                        (this.state._orderDataSource && this.state._orderDataSource.length > 0) 
                                        ? 
                                        this.state._orderDataSource.map((item, index)=>{
                                            if (index < 1000) {
                                                return this.renderOrderRow(item)
                                            }
                                        })
                                        : <EmptyRowViewComponent/> 
                                    }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                    <TouchableOpacity onPress={() => { 
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                        <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.orderId) {
                                            let toastOpts = getFailToastOpts("您还没有选择产品");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            modal:false,
                                        }) 
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                        <Image style={{width:30, height:30,marginRight:15}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            
                        </View>
                        <View>

                        </View>
                    </Modal>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                {/* <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.orderDataSource && this.state.orderDataSource.length > 0) 
                            ? 
                            this.state.orderDataSource.map((item, index)=>{
                                return this.renderOrderRow(item)

                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View> */}

                {/* <View style={styles.rowLabView}>
                        <Text style={styles.leftLabNameTextStyle}>砖型</Text>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                    </View>

                <View style={[{flexDirection:'row', flexWrap:'wrap', width:screenWidth*0.95, justifyContent:'flex-start'}]}>
                    {this.state.orderDataSource.map((item, key)=>{

                    <View key={item.orderId} style={[item.orderId===this.state.orderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                        <Text style={item.orderId===this.state.orderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                          {item.orderName}
                        </Text>
                    </View>
                        // return(
                        //     <TouchableOpacity onPress={()=>{
                        //         this.setState({
                        //             orderId:item.orderId,
                        //         })
                        //         WToast.show({data:'点击了' + item.orderName});
                        //         // 调接口查询原料名称
                        //         let loadTypeUrl;
                        //         let loadRequest;
                        //         loadTypeUrl= "/biz/order/get";
                        //         loadRequest={
                        //             'currentPage':1,
                        //             'pageSize':100,
                        //             "orderId":item.orderId
                        //         };
                        //         console.log("========目前的orderId是"+ item.orderId);
                        //         httpPost(loadTypeUrl, loadRequest, (response)=>{
                        //             if (response.code == 200 && response.data) {
                        //                 this.setState({
                        //                     customerName:response.data.customerName,
                        //                     positionName:response.data.positionName,
                        //                 })
                        //             }
                        //         });
    

                        //     }}>
                        //         <View key={item.orderId} style={[item.orderId===this.state.orderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                        //             <Text style={item.orderId===this.state.orderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        //                 {item.orderName}
                        //             </Text>
                        //         </View>
                        //     </TouchableOpacity>
                            
                        // )
                    })}
                    </View> */}

                <View style={styles.inputRowStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={[styles.leftLabRedTextStyle,{color:'white'}]}>*</Text>

                        <Text style={styles.leftLabNameTextStyle}>部位</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    <View style={[CommonStyle.inputTextStyleTextStyle,{width:screenWidth - (leftLabWidth + 20) , borderWidth:0}]}>
                        {/* <Text style={{color:'#A0A0A0', fontSize:15}}>{this.state.brickTypeName}</Text> */}
                        <Text style={{color:'#A0A0A0', fontSize:15}}>
                            {!this.state.positionName ? (!this.state.orderId?"请选择部位":"该订单未添加部位") : this.state.positionName}
                        </Text>
                    </View>
                </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                
                <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           <Text style={styles.leftLabRedTextStyle}>*</Text>

                           <Text style={styles.leftLabNameTextStyle}>接收(块):</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[styles.inputRightText]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({acceptanceAmount:text,totalWeight:text*this.state.pieceWeight/1000})}
                        >
                            {this.state.acceptanceAmount}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           <Text style={styles.leftLabRedTextStyle}>*</Text>

                           <Text style={styles.leftLabNameTextStyle}>单重(Kg):</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[styles.inputRightText]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({pieceWeight:text,totalWeight:text*this.state.acceptanceAmount/1000})}
                        >
                            {this.state.pieceWeight}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                           <Text style={styles.leftLabRedTextStyle}>*</Text>

                           <Text style={styles.leftLabNameTextStyle}>总重(吨):</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[styles.inputRightText]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({totalWeight:text})}
                        >
                            {this.state.totalWeight?this.changeTwoDecimal(this.state.totalWeight):this.state.totalWeight}
                        </TextInput>
                    </View>
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
                <View style={{height:ifIphoneXContentViewHeight()-378-85, backgroundColor:'#F2F5FC'}}>
                        {/* <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                        >
                        </TextInput> */}
                </View>
                <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={CommonStyle.btnAddCancelBtnView} >
                        {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveEngineeringAcceptance.bind(this)}>
                        <View style={CommonStyle.btnAddSaveBtnView}>
                        {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                        </View>
                    </TouchableOpacity>
                </View>
                {/* <BottomScrollSelect 
                    // ref={'SelectOrder'} 
                    // callBackOrderValue={this.callBackOrderValue.bind(this)}
                /> */}

                <BottomScrollSelect 
                    ref={'SelectCustomer'} 
                    callBackCustomerValue={this.callBackCustomerValue.bind(this)}
                />

                <BottomScrollSelect 
                    ref={'SelectContract'} 
                    callBackContractValue={this.callBackContractValue.bind(this)}
                />
                
            </ScrollView>
                
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({

    // contentViewStyle:{
    //     backgroundColor:'#FFFFFF',
    //     height:screenHeight - 140
    // },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    // inputRowStyle:{
    //     height:45,
    //     flexDirection:'row',
    //     marginTop:4,
    //     marginBottom:4,
    //     // flex: 1,
    //     // justifyContent: 'space-between',
    //     // alignContent:'center'
    //     // backgroundColor:'#000FFF',
    //     // width:screenWidth,
    //     // alignContent:'space-between',
    //     // justifyContent:'center'
    // },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        // paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        // marginLeft:5,
        marginRight:5
    },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    // inputRowStyle:{
    //     height:45,
    //     flexDirection:'row',
    //     marginTop:10,
    // },
  
    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#FFFFFF',
        // borderWidth:1,
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
    },
    rightLabView:{
      width:20,
      height:45,
      flexDirection:'row',
      alignItems:'center',
      paddingLeft:10,
    },
    textCertain: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#FFFFFF',
        lineHeight: 24,
        marginTop:10,
        textAlign: 'center',
        // fontStyle: 'normal',
    },
    textCancel: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#404956',
        lineHeight: 24,
        marginTop:10,
        textAlign:'center'
        // fontStyle: 'normal',
    },
    textContainerCertain: {
        width: 180,
        height: 48,
        marginRight:8,
        backgroundColor: '#255BDA',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    textContainerCancel: {
        width: 180,
        height: 48,
        marginLeft:8,
        backgroundColor: '#FFFFFF',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        // paddingTop:5,
        // paddingBottom:5,
        marginTop:4,
        marginBottom:4,
        marginLeft:16, 
        // borderTopWidth:1,
        // borderTopColor:'#F1F1F1',
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        // paddingLeft:0,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'#E63633',
        marginLeft:5,
        marginRight:5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:4,
        marginRight:3,
    },
})