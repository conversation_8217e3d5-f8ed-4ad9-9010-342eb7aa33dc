import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,TouchableOpacity,Image,Dimensions} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class VerifyInternalSettingAdd extends Component {
    constructor(){
        super()
        this.state = {
            standardType:"I",
            operate:"",
            templateId: "",
            templateItemName: "",
            templateItemDefaultValue:"",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { templateId } = route.params;
            if (templateId) {
                console.log("========Edit==templateId:", templateId);
                this.setState({
                    templateId:templateId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/verify/item/template/get";
                loadRequest={'templateId':templateId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditClassifyDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    loadEditClassifyDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                templateId:response.data.templateId,
                templateItemName: response.data.templateItemName,
                templateItemDefaultValue: response.data.templateItemDefaultValue,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("VerifyInternalSettingList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>自检设置</Text>
                </TouchableOpacity>
            </View>
        )
    }

    saveClassify =()=> {
        console.log("=======saveClassify");
        let toastOpts;
        if (!this.state.templateItemName) {
            toastOpts = getFailToastOpts("请输入检验项目");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/verify/item/template/add";
        if (this.state.templateId) {
            console.log("=========Edit===templateId", this.state.templateId)
            url= "/biz/verify/item/template/modify";
        }
        let requestParams={
            templateId:this.state.templateId,
            templateItemName: this.state.templateItemName,
            templateItemDefaultValue:this.state.templateItemDefaultValue,
            standardType: this.state.standardType,
        };
        httpPost(url, requestParams, this.saveBigClassCallBack);
    }
    
    // 保存回调函数
    saveBigClassCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return (
            <View>
                <CommonHeadScreen title={this.state.operate + '项目'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>检验项目</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入检验项目'}
                            onChangeText={(text) => this.setState({templateItemName:text})}
                        >
                            {this.state.templateItemName}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>默认值</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入默认值'}
                            onChangeText={(text) => this.setState({templateItemDefaultValue:text})}
                        >
                            {this.state.templateItemDefaultValue}
                        </TextInput>
                    </View>
                    <View style={{height:ifIphoneXContentViewHeight()-108-85, backgroundColor:'#F2F5FC'}}>
                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnAddCancelBtnView} >
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveClassify.bind(this)}>
                            <View style={CommonStyle.btnAddSaveBtnView}>
                            {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'#E63633',
        marginLeft:4,
        marginRight:3
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:4,
        marginRight:3,
    },
    inputLeftText:{
        width: (leftLabWidth - 35),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})