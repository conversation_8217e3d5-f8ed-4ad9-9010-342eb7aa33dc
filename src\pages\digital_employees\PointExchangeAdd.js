import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image,Modal} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

import { uploadImageLibrary } from '../../utils/UploadImageUtils';
import ImageViewer from 'react-native-image-zoom-viewer';

export default class PointExchangeAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            exchangeConfigId:"",
            exchangeGoodsName:"",
            exchangeGoodsImage:"",
            exchangePointValue:"",
            exchangeGoodsSort:0,
            goodsImage:"",
            goodsImageUrl:constants.image_addr + constants.loginUser.exchangeGoodsImage,
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { exchangeConfigId } = route.params;
            if (exchangeConfigId) {
                console.log("========Edit==exchangeConfigId:", exchangeConfigId);
                this.setState({
                    exchangeConfigId:exchangeConfigId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/point/exchange/config/get";
                loadRequest={'exchangeConfigId':exchangeConfigId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditPointExchangeConfigDataCallBack);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    loadEditPointExchangeConfigDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            let exchangeGoodsImage = response.data.exchangeGoodsImage;
            this.setState({
                exchangeConfigId:response.data.exchangeConfigId,
                exchangeGoodsName:response.data.exchangeGoodsName,
                goodsImageUrl:constants.image_addr + '/' + exchangeGoodsImage,
                exchangeGoodsImage:exchangeGoodsImage,
                exchangePointValue:response.data.exchangePointValue,
                exchangeGoodsSort:response.data.exchangeGoodsSort,
                // goodsImage:response.data.goodsImage,
                // goodsImageUrl: constants.image_addr + '/' + response.data.goodsImage
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                this.props.navigation.navigate("PointExchange")
            }}>
                <Text style={CommonStyle.headRightText}>积分兑换</Text>
            </TouchableOpacity>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent/>
    }
    
    savePointExchangeConfig =()=> {
        console.log("=======savePointExchangeConfig");
        let toastOpts;
        if (!this.state.exchangeGoodsName) {
            toastOpts = getFailToastOpts("请输入商品名称");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.exchangeGoodsImage) {
        //     toastOpts = getFailToastOpts("请上传商品图片");
        //     WToast.show(toastOpts)
        //     return;
        // }
        if (!this.state.exchangePointValue) {
            toastOpts = getFailToastOpts("请输入需要兑换的积分");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/point/exchange/config/add";
        if (this.state.exchangeConfigId) {
            console.log("=========Edit===exchangeConfigId", this.state.exchangeConfigId)
            url= "/biz/point/exchange/config/modify";
        }
        let requestParams={
            "exchangeConfigId":this.state.exchangeConfigId,
            "exchangeGoodsName":this.state.exchangeGoodsName,
            "exchangeGoodsImage":this.state.exchangeGoodsImage,
            "exchangePointValue":this.state.exchangePointValue,
            "exchangeGoodsSort":this.state.exchangeGoodsSort
        };
        httpPost(url, requestParams, this.savePointExchangeConfigCallBack);
    }
    
    // 保存回调函数
    savePointExchangeConfigCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate + '积分兑换'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>商品名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入商品名称'}
                            onChangeText={(text) => this.setState({exchangeGoodsName:text})}
                        >
                            {this.state.exchangeGoodsName}
                        </TextInput>
                    </View>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>商品图片</Text>
                        </View>
                        <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex',justifyContent:'center',
                            alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                            <TouchableOpacity 
                                onPress={() => {
                                    uploadImageLibrary(this.state.goodsImageUrl, "user_header", (imageUploadResponse) => {
                                        console.log("========imageUploadResponse", imageUploadResponse)
                                        if (imageUploadResponse.code === 200) {
                                            WToast.show({ data: "成功上传" });
                                            let { compressFile } = imageUploadResponse.data
                                            this.setState({
                                                goodsImageUrl: constants.image_addr + '/' + compressFile,
                                                exchangeGoodsImage:compressFile,
                                            })
                                            httpPost("/biz/point/exchange/config/uploading_image", {
                                                "exchangeConfigId":this.state.exchangeConfigId, 
                                                "exchangeGoodsImage":compressFile
                                            }, (updateResponse)=>{
                                                if (updateResponse.code === 200) {
                                                    console.log("======附件信息已经更新")
                                                }
                                            })
                                        }
                                        else {
                                            WToast.show({ data: imageUploadResponse.message });
                                        }
                                    });
                            }}>
                                    {
                                        this.state.exchangeGoodsImage ?
                                        <Image source={{ uri: this.state.goodsImageUrl }} style={{width:120,height:150,justifyContent:'center',alignItems:'center'}} />
                                        :
                                        <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                    }
                            </TouchableOpacity>
                        </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>所需积分</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入需要兑换的积分'}
                            onChangeText={(text) => this.setState({exchangePointValue:text})}
                        >
                            {this.state.exchangePointValue}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({exchangeGoodsSort:text})}
                        >
                            {this.state.exchangeGoodsSort}
                        </TextInput>
                    </View>
                    
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnRowLeftCancelBtnView, { flexDirection: 'row'}]} >
                                <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.savePointExchangeConfig.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView,{flexDirection:'row'}]}>
                                <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }

});