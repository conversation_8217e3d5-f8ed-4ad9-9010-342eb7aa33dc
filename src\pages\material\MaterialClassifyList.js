import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class MaterialClassifyList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      parentClassifyId: null,
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      moreModal: false,
      deleteModal: false,
      dailyItem: {},
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {parentClassifyId, classifyType} = route.params;
      if (parentClassifyId) {
        this.setState({
          parentClassifyId: parentClassifyId,
        });
      }
      this.loadMaterialBigClassifyList(parentClassifyId);
    } else {
      this.loadMaterialBigClassifyList();
    }
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/material/classify/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      parentClassifyId: this.state.parentClassifyId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/material/classify/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      parentClassifyId: this.state.parentClassifyId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadMaterialBigClassifyList();
  };

  loadMaterialBigClassifyList = (parentClassifyId) => {
    let loadUrl = '/biz/material/classify/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      parentClassifyId: parentClassifyId
        ? parentClassifyId
        : this.state.parentClassifyId,
    };
    httpPost(loadUrl, loadRequest, this.loadMaterialBigClassifyListCallBack);
  };

  loadMaterialBigClassifyListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteMaterialBigClassify = (classifyId) => {
    console.log('=======delete=classifyId', classifyId);
    let url = '/biz/material/classify/delete';
    let requestParams = {classifyId: classifyId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View key={item.classifyId} style={styles.innerViewStyle}>
        {index == 0 ? (
          <View style={[CommonStyle.lineListHeadRenderRowStyle]}></View>
        ) : (
          <View></View>
        )}
        <View style={{position: 'absolute', right: 0, top: 0, marginRight: 0}}>
          <TouchableOpacity
            onPress={() => {
              this.setState({
                moreModal: true,
                dailyItem: item,
              });
            }}>
            <View
              style={[
                {
                  width: 35,
                  height: 35,
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <Image
                style={{width: 28, height: 28}}
                source={require('../../assets/icon/iconfont/more.png')}></Image>
            </View>
          </TouchableOpacity>
        </View>
        <View style={[CommonStyle.titleViewStyleSpecial, {marginLeft: 24}]}>
          <Text style={[CommonStyle.titleTextStyleSpecial]}>
            {this.state.parentClassifyId ? '原料名称' : '原料类别'}：
            {item.classifyName}
          </Text>
        </View>
        <View style={[CommonStyle.titleViewStyle, {marginLeft: 24}]}>
          <Text style={CommonStyle.titleTextStyle}>
            排序：{item.classifySort}
          </Text>
        </View>
        <View style={[CommonStyle.titleViewStyle, {marginLeft: 24}]}>
          <Text style={CommonStyle.titleTextStyle}>
            更新时间：{item.gmtModified ? item.gmtModified : item.gmtCreated}
          </Text>
        </View>

        <View
          style={[
            CommonStyle.itemBottomBtnStyle,
            {flexWrap: 'wrap', marginRight: -1},
          ]}>
          {this.state.parentClassifyId ? (
            <View />
          ) : (
            <TouchableOpacity
              onPress={() => {
                this.props.navigation.push('MaterialClassifyList', {
                  // 传递参数
                  parentClassifyId: item.classifyId,
                  classifyName: item.classifyName,
                  // 传递回调函数
                  refresh: this.callBackFunction,
                });
              }}>
              <View
                style={[
                  CommonStyle.itemBottomEditBtnViewStyle,
                  {flexDirection: 'row', width: 70, backgroundColor: '#FFB800'},
                ]}>
                <Image
                  style={{width: 23, height: 23, marginRight: 3}}
                  source={require('../../assets/icon/iconfont/material1.png')}></Image>
                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>原料</Text>
              </View>
            </TouchableOpacity>
          )}

          {/* <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该' + (this.state.parentClassifyId ? "原料" : "类别") + '吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteMaterialBigClassify(item.classifyId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:75,flexDirection:'row'}]}>
                            <Image style={{width:20, height:20,marginRight:3}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity> */}
          {/* <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("MaterialClassifyAdd", 
                            {
                                // 传递参数
                                classifyId: item.classifyId,
                                parentClassifyId: item.parentClassifyId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:75,flexDirection:'row'}]}>
                            <Image style={{width:20, height:20,marginRight:3}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity> */}
        </View>
      </View>
    );
  };
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
      // </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[{flexDirection: 'row', alignItems: 'center'}]}>
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={{color: '#3C6CDE', fontWeight: 'bold'}}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      // <TouchableOpacity onPress={() => {
      //     this.props.navigation.push("MaterialClassifyAdd",
      //     {
      //         parentClassifyId: this.state.parentClassifyId,
      //         // 传递回调函数
      //         refresh: this.callBackFunction
      //     })
      // }}>
      //     {/* <Text style={CommonStyle.headRightText}>新增{this.state.parentClassifyId ? "原料" : "类别"}</Text> */}
      //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
      // </TouchableOpacity>
      <View
        style={{flexDirection: 'row-reverse', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.push('MaterialClassifyAdd', {
              parentClassifyId: this.state.parentClassifyId,
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Image
            style={{width: 22, height: 22, marginVertical: 2}}
            source={require('../../assets/icon/iconfont/add.png')}></Image>
        </TouchableOpacity>
      </View>
    );
  }

  render() {
    return (
      <View>
        <CommonHeadScreen
          title={this.state.parentClassifyId ? '原料管理' : '原料类别'}
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        {/* <View style={CommonStyle.rightTop50FloatingBlockView}>
                    <Text style={CommonStyle.rightTop50FloatingBlockText}>{this.state.dataSource.length}</Text>
                </View> */}
        <View style={CommonStyle.contentViewStyle}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>  */}
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.classifyId}
            ListEmptyComponent={this.emptyComponent}
            ItemSeparatorComponent={this.space}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
          {/* </ScrollView> */}
        </View>

        {/* 更多操作弹窗Modal */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.moreModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 291,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    // if (this.state.dailyItem.dailyState != "0BB" || this.state.dailyItem.auditScore || dateDiffHours(this.state.currentTime, this.state.dailyItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                    //     WToast.show({ data: '该日报不可编辑' });
                    //     return;
                    // }
                    this.setState({
                      moreModal: false,
                    });
                    this.props.navigation.navigate('MaterialClassifyAdd', {
                      // 传递参数
                      classifyId: this.state.dailyItem.classifyId,
                      parentClassifyId: this.state.dailyItem.parentClassifyId,
                      // 传递回调函数
                      refresh: this.callBackFunction,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                    ]}>
                    {/* <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image> */}
                    <Text
                      style={{
                        color: 'rgba(0, 10, 32, 0.85)',
                        fontSize: 18,
                        lineHeight: 52,
                      }}>
                      编辑
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              <View>
                <TouchableOpacity
                  onPress={() => {
                    console.log(
                      'dailyItem=================',
                      this.state.dailyItem,
                    );
                    // if (this.state.dailyItem.dailyState != "0BB" && this.state.dailyItem.auditScore) {
                    //     WToast.show({ data: '日报已审核不可删除' });
                    //     return;
                    // }
                    // if (this.state.dailyItem.dailyState != "0BB" && dateDiffHours(this.state.currentTime, this.state.dailyItem.gmtCreated) > constants.loginUser.editDeleteTimeLimit) {
                    //     WToast.show({ data: '日报已超出删除时限' });
                    //     return;
                    // }
                    // 删除弹窗Modal
                    this.setState({
                      moreModal: false,
                      deleteModal: true,
                    });
                  }}>
                  <View
                    style={[
                      {width: 145, height: 50, paddingLeft: 30, marginTop: 5},
                    ]}>
                    {/* <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image> */}
                    <Text
                      style={[
                        {
                          color: 'rgba(0, 10, 32, 0.85)',
                          fontSize: 18,
                          lineHeight: 52,
                        },
                      ]}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: 291,
                  height: 50,
                  alignItems: 'flex-end',
                  justifyContent: 'flex-end',
                  marginTop: 10,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      moreModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 105,
                      height: 50,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* 删除弹窗 */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={this.state.deleteModal}
          //  onShow={this.onShow.bind(this)}
          onRequestClose={() => console.log('onRequestClose...')}>
          <View
            style={[
              CommonStyle.fullScreenKeepOut,
              {backgroundColor: 'rgba(0,0,0,0.64)'},
            ]}>
            <View
              style={{
                width: 292,
                height: 156,
                bottom: screenHeight / 2 - 80,
                position: 'absolute',
                backgroundColor: '#FFFFFF',
                borderRadius: 10,
              }}>
              <View
                style={{
                  height: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <Text style={{fontSize: 18}}>
                  {'您确定要删除该' +
                    (this.state.parentClassifyId ? '原料' : '类别') +
                    '吗？'}
                </Text>
              </View>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 24,
                }}>
                <Text style={{fontSize: 14, color: 'rgba(0,10,32,0.65)'}}>
                  删除后数据不可恢复，请谨慎操作
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  width: 292,
                  height: 56,
                  marginTop: 15,
                  borderTopWidth: 1,
                  borderColor: '#DFE3E8',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了取消'});
                  }}>
                  <View
                    style={{
                      width: 146,
                      height: 56,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: '#DFE3E8',
                    }}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#000A20',
                      }}>
                      取消
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      deleteModal: false,
                    });
                    WToast.show({data: '点击了确定'});
                    this.deleteMaterialBigClassify(
                      this.state.dailyItem.classifyId,
                    );
                  }}>
                  <View
                    style={[
                      {
                        width: 146,
                        height: 56,
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    ]}>
                    <Text
                      style={{
                        fontSize: 17,
                        fontWeight: '400',
                        color: '#1E6EFA',
                      }}>
                      删除
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    marginTop: 10,
    // borderBottomWidth:1,
    // backgroundColor: '#F0F0F0',
    // marginHorizontal:16,
    // borderColor:"#F0F0F0",
    // borderWidth:14,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
});
