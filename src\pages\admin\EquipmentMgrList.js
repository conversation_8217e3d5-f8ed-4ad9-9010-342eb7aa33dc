import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class EquipmentMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:10,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            departmentId:null,
            departmentList:null,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    // //更新State
    // _updateState(message, refresh){
    //     this.setState({text:message,refreshing: refresh});
    // }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { departmentId } = route.params;
            // console.log("当前部门的 id:",departmentId);
             console.log("当前部门的列表:",constants.loginUser);
            if (departmentId) {
                this.setState({
                    departmentId:departmentId,
                })
                this.loadEquipmentList(departmentId, null);
            }
            else if (constants.loginUser.portalDepartmentDTOList) {
                console.log("2")
                this.setState({
                    departmentList:constants.loginUser.portalDepartmentDTOList
                })
                this.loadEquipmentList(null, constants.loginUser.portalDepartmentDTOList);
            }
            else {
                this.loadEquipmentList(null, null);
            }
            
        }
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/equipment/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId": this.state.departmentId,
            "departmentList": this.state.departmentList,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/equipment/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId": this.state.departmentId,
            "departmentId": this.state.departmentId,
            "departmentList": this.state.departmentList,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadEquipmentList();
    }

    loadEquipmentList=(departmentId, departmentList)=>{
        let url= "/biz/equipment/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "departmentId": departmentId ? departmentId : this.state.departmentId,
            "departmentList": departmentList ? departmentList : this.state.departmentList,
        };
        httpPost(url, loadRequest, this.loadEquipmentListCallBack);
    }

    loadEquipmentListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteEquipment =(equipmentId)=> {
        console.log("=======delete=equipmentId", equipmentId);
        let url= "/biz/equipment/delete";
        let requestParams={'equipmentId':equipmentId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.equipmentId} style={styles.innerViewStyle}>
                {
                    index == 0 ?
                        <View style={{ width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF', borderBottomWidth: 10, borderBottomColor: '#F4F7F9' }}>
                        </View>
                        :
                        <View></View>
                }
                <View style={CommonStyle.titleViewStyleSpecial}>
                    <Text style={CommonStyle.titleTextStyleSpecial}>设备名称：{item.equipmentName}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>设备状态：{item.equipmentStateName?item.equipmentStateName:"无"}</Text>
                </View>
                <View style={CommonStyle.titleViewStyle}>
                    <Text style={CommonStyle.titleTextStyle}>排序：{item.equipmentSort}</Text>
                </View>
                {
                    item.remark ?
                    <View style={CommonStyle.titleViewStyle}>
                        <Text style={CommonStyle.titleTextStyle}>设备说明：{item.remark}</Text>
                    </View>
                    :
                    <View/>
                }
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>最近更新时间：{item.gmtModified ? item.gmtModified: item.gmtCreated }</Text>
                </View>
                 */}
                <View style={[CommonStyle.itemBottomBtnStyle, {flexWrap:'wrap'}]}>
                    <TouchableOpacity onPress={()=>{
                            // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                            //     return;
                            // }
                            this.props.navigation.navigate("EquipmentStateMgrAdd", 
                            {
                                // 传递参数
                                equipmentId:item.equipmentId,
                                equipmentName:item.equipmentName,
                                equipmentStateName:item.equipmentStateName,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{width:70,flexDirection:'row',backgroundColor:'#FFB800'}
                        // ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ]}>
                            <Image  style={{width:17, height:17,marginRight:2,tintColor:'#FFFFFF'}} source={require('../../assets/icon/iconfont/add.png')}></Image>
                            <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{color:'#FFFFFF'}]}>明细</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("EquipmentStateMgrList", 
                            {
                                // 传递参数
                                equipmentId:item.equipmentId,
                                equipmentName:item.equipmentName,
                                equipmentStateName:item.equipmentStateName,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {backgroundColor:"#3ab240",width: 75 ,flexDirection:"row"}]}>
                        <Image  style={{width:25, height:25,marginRight:3}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                        </View>
                    </TouchableOpacity>
                    {
                        this.state.departmentId ? 
                        <Text>
                            <TouchableOpacity onPress={()=>{
                                Alert.alert('确认','您确定要删除该设备吗？',[
                                    {
                                        text:"取消", onPress:()=>{
                                        WToast.show({data:'点击了取消'});
                                        }
                                    },
                                    {
                                        text:"确定", onPress:()=>{
                                            WToast.show({data:'点击了确定'});
                                            this.deleteEquipment(item.equipmentId)
                                        }
                                    }
                                ]);
                            }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:70,flexDirection:"row"}]}>
                            <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={()=>{
                                    this.props.navigation.navigate("EquipmentMgrAdd", 
                                    {
                                        // 传递参数
                                        equipmentId:item.equipmentId,
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                            </TouchableOpacity>
                        </Text>
                        :
                        <View/>
                    }
                    
                </View>
            </View>
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }
    
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            this.state.departmentId ? 
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("EquipmentMgrAdd", 
            //     {
            //         departmentId:this.state.departmentId,
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //    <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("EquipmentMgrAdd", 
                    {
                        // 传递回调函数
                        departmentId:this.state.departmentId,
                        refresh: this.callBackFunction 
                    });
                }}  >
                    <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
            :
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
       
                }}  >
                    {/* <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
                </TouchableOpacity>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state?.departmentId ? '设备管理' : '设备巡检'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        // borderColor:"#F4F4F4",
        // borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});