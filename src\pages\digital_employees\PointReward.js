import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, TextInput,
    FlatList, RefreshControl, Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';

var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class PointReward extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            searchKeyWord: "",
            totalRecord: 1,
            gmtCreated: null,
            topBlockLayoutHeight: 0,

        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    initqryStartTime = () => {
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 1);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectedQryStartDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            gmtCreated: _gmtCreated
        })
        return _gmtCreated;
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount', constants.loginUser);
        var _gmtCreated = this.initqryStartTime();
        console.log('componentWillMount==_gmtCreated', _gmtCreated);
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { rewardId } = route.params;
            if (rewardId) {
                console.log("=============rewardId" + rewardId + "");
            }
            this.loadPointRewardList(_gmtCreated, rewardId);
        }
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/point/reward/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "gmtCreated": this.state.gmtCreated,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/point/reward/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "gmtCreated": this.state.gmtCreated,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadPointRewardList();
    }

    loadPointRewardList = (_gmtCreated, rewardId) => {
        let url = "/biz/point/reward/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "rewardId": this.state.rewardId,
            "gmtCreated": _gmtCreated ? _gmtCreated : this.state.gmtCreated,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this.loadPointRewardListCallBack);
    }

    loadPointRewardListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    deletePointReward = (rewardId) => {
        console.log("=======delete=rewardId", rewardId);
        let url = "/biz/point/reward/delete";
        let requestParams = { 'rewardId': rewardId };
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack = (response) => {
        if (response.code == 200 && response.data) {
            WToast.show({ data: "删除完成" });
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
        }
    }

    renderRow = (item, index) => {
        return (
            <View key={item.rewardId} style={[CommonStyle.innerViewStyle]}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>员工姓名：{item.staffName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>奖励积分：{item.rewardPointValue}</Text>
                </View>
                {/* {
                    item.pointClassName?
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>积分类别：{item.pointClassName}</Text>
                    </View>
                    :
                    null
                } */}
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>积分描述：{item.rwardPointDesc ? item.rwardPointDesc : "无"}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>积分余额：{item.rewardPointValue}</Text>
                </View> */}
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>奖励时间：{item.gmtCreated}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>奖励人：{item.rewardUserName ? item.rewardUserName : "无"}</Text>
                </View>

                {
                    item.rewardUserId == constants.loginUser.userId ?
                        <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                            <TouchableOpacity onPress={() => {
                                Alert.alert('确认', '确定要删除该奖励积分吗？', [
                                    {
                                        text: "取消", onPress: () => {
                                            WToast.show({ data: '点击了取消' });
                                            // this在这里可用，传到方法里还有问题
                                            // this.props.navigation.goBack();
                                        }
                                    },
                                    {
                                        text: "确定", onPress: () => {
                                            WToast.show({ data: '点击了确定' });
                                            this.deletePointReward(item.rewardId)
                                        }
                                    }
                                ]);
                            }}>
                                <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle, { width: 64 }]}>
                                    <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../assets/icon/iconfont/newDelete.png')}></Image>
                                    <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => {
                                this.props.navigation.navigate("PointRewardAdd",
                                    {
                                        // 传递参数
                                        rewardId: item.rewardId,
                                        // staffId:item.staffId,
                                        // 传递回调函数
                                        refresh: this.callBackFunction
                                    })
                            }}>
                                <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64 }]}>
                                    <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                    <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }}>编辑</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        :
                        <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                            <TouchableOpacity onPress={() => {
                                return;
                            }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, { width: 80, flexDirection: 'row', opacity: 0.3 }]}>
                                    <Image style={{ width: 20, height: 20, marginRight: 3 }} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => {
                                return
                            }}>
                                <View style={[CommonStyle.itemBottomEditBtnViewStyle, { width: 80, flexDirection: "row", opacity: 0.3 }]}>
                                    <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                    <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                }
            </View>
        )
    }
    searchByKeyWord = () => {
        let loadUrl = "/biz/point/reward/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "searchKeyWord": this.state.searchKeyWord,
            "gmtCreated": this.state.gmtCreated,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("PointRewardAdd",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Image style={{ width: 27, height: 27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>

            </TouchableOpacity>
        )
    }

    openGmtCreatedDate() {
        this.refs.SelectGmtCreated.showDate(this.state.selectedQryStartDate)
    }

    callBackSelectGmtCreatedDateValue(value) {
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryStartDate: value
        })
        if (this.state.selectedQryStartDate && this.state.selectedQryStartDate.length) {
            var gmtCreated = "";
            var vartime;
            for (var index = 0; index < this.state.selectedQryStartDate.length; index++) {
                vartime = this.state.selectedQryStartDate[index];
                if (index === 0) {
                    gmtCreated += vartime;
                }
                else if (index < 3) {
                    gmtCreated += "-" + vartime;
                }
                else if (index === 3) {
                    gmtCreated += " " + vartime;
                }
                else {
                    gmtCreated += ":" + vartime;
                }
            }
            this.setState({
                currentPage: 1,
                gmtCreated: gmtCreated
            })

            let loadUrl = "/biz/point/reward/list";
            let loadRequest = {
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "gmtCreated": gmtCreated,
                "searchKeyWord": this.state.searchKeyWord,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='积分奖励'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.headViewStyle, { borderLeftWidth: 0, borderRightWidth: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={CommonStyle.searchBoxAndExport} >
                        <View style={[CommonStyle.searchTimeBoxWithExport,{width: screenWidth / 1.5}]}>
                            <View style={{ alignItems: 'center', flexDirection: 'row',  backgroundColor: "#F2F5FC", borderRadius: 80 }}>
                                <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                <TextInput
                                     style={{ color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 15, paddingTop: 0, paddingBottom: 0, paddingRight: 0, paddingLeft: 0 }}
                                    returnKeyType="search"
                                    returnKeyLabel="搜索"
                                    onSubmitEditing={e => {
                                        this.searchByKeyWord();
                                    }}
                                    placeholder={'搜索姓名/积分描述/奖励人'}
                                    onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                >
                                    {this.state.searchKeyWord}
                                </TextInput>
                            </View>
                        </View>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle,
                            {
                                margin: 0,
                                alignItems: 'center',
                                width: 100,
                                backgroundColor: 'rgba(242, 245, 252, 1)',
                                height: 32,
                                borderRadius:8
                            }]}>
                            <TouchableOpacity onPress={() => this.openGmtCreatedDate()}>
                                <Text style={{ color: 'rgba(0,10,32,0.85)', fontSize: 14 }}>
                                    {!this.state.gmtCreated ? "时间" : this.state.gmtCreated}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>


                </View>



                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
                <BottomScrollSelect
                    ref={'SelectGmtCreated'}
                    callBackDateValue={this.callBackSelectGmtCreatedDateValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({

    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: "#FFFFFF",
        backgroundColor: "#FFFFFF",
        borderRadius: 5,
        marginTop: 5
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop: 0
    },
    innerViewStyle: {
        // marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 8,
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },

});