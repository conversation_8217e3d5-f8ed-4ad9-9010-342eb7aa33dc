import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,Modal,
    FlatList,RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../utils/CameraRollUtils';

var screenHeight = Dimensions.get('window').height;
export default class SYPointMall extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            pointTotalValue:0,
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadPointExchangeConfigList();
        this.loadPointTotalValue();
    }

    loadPointExchangeConfigList=()=>{
        let url= "/biz/point/exchange/config/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this.loadPointExchangeConfigListCallBack);
    }

    loadPointExchangeConfigListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N" ,pictureDisplay: "N",personPictureDisplay:false}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadPointTotalValue=()=>{
        let url= "/biz/portal/staff/get";
        let loadRequest={
            "staffId": constants.loginUser.staffId,
        };
        httpPost(url, loadRequest, this.loadPointTotalValueCallBack);
    }

    loadPointTotalValueCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                pointTotalValue:response.data.pointTotalValue,
            })
            console.log("=======累计pointValue",this.state.pointTotalValue);      
        }
    }

    exchangeGoods =(exchangeConfig)=> {
        console.log("=======exchangeGoods");
        let url= "/biz/point/record/add";
        let pointValue=0-exchangeConfig.exchangePointValue;
        console.log("=======消耗pointValue",pointValue);
        let requestParams={
            "staffId": constants.loginUser.staffId,
            "pointValue":pointValue,
        };
        httpPost(url, requestParams,);
        let pointTotalValue=this.state.pointTotalValue-exchangeConfig.exchangePointValue;
        console.log("=======累计pointValue",pointTotalValue);
        url="/biz/portal/staff/modify";
        requestParams={
            "staffId": constants.loginUser.staffId,
            "pointTotalValue":pointTotalValue,
        };
        httpPost(url, requestParams, this.exchangeCallBack);
    }

    // 兑换操作的回调操作
    exchangeCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"兑换完成"});
            this.loadPointTotalValue();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

     //下拉视图开始刷新时调用
     _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }
    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/point/exchange/config/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/point/exchange/config/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { display: "N" ,pictureDisplay: "N",personPictureDisplay:false}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
             this.props.navigation.navigate("LoginView");
        }
    }
    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadPointExchangeConfigList();
    }
    

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.exchangeConfigId} style={[styles.innerViewStyle,
            {borderWidth:0.5,justifyContent:'center',alignItems:'center',flexDirection:'row',width:(screenWidth / 2) - 15,flexWrap:'wrap'}]}>
                <View style ={{}}>
                    <View style={{ marginLeft:20}}>
                        {
                        item.exchangeGoodsImage?
                        <View style={{height: (screenWidth / 2) - 15, width:(screenWidth / 2) - 15,borderStyle:'dashed',justifyContent:'center',alignItems:'center'}}>
                            <View>   
                            <TouchableOpacity onPress={() => {
                                let list = this.state.dataSource;
                                list.map((elem, index) => {
                                    if(elem.exchangeConfigId == item.exchangeConfigId){
                                        item.personPictureDisplay = true;
                                    }
                                })
                                console.log("personPictureDisplay",item.personPictureDisplay)
                                this.setState({
                                    dataSource:list,
                                })
                            }}>
                            <Image source={{ uri: (constants.image_addr + '/' + item.exchangeGoodsImage) }} style={{width:(screenWidth / 2) - 40,height:(screenWidth / 2) - 40}} />                                                    
                            </TouchableOpacity>
                            <Modal visible={item.personPictureDisplay} transparent={true}>
                                <ImageViewer enableSwipeDown={false} menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                        saveToLocalByLongPress={true}
                                        onClick={() => { // 图片单击事件
                                            let list = this.state.dataSource;
                                            list.map((elem, index) => {
                                                if(elem.pointExchangeId == item.pointExchangeId){
                                                    item.personPictureDisplay = false;
                                                }
                                            })
                                            console.log("personPictureDisplay",item.personPictureDisplay)
                                            this.setState({
                                                dataSource:list,
                                            })
                                        }}
                                        imageUrls={[{url:(constants.image_addr + '/' + item.exchangeGoodsImage)}]} 
                                        onSave ={() => {
                                            var imageUrl = constants.image_addr + '/' + item.exchangeGoodsImage;
                                            saveImage( imageUrl); 
                                        } }
                                    />
                            </Modal>
                            </View>
                            {/* <Text>{constants.image_addr+ '/' + item.exchangeGoodsImage}</Text> */}
                        </View>
                        :
                        <View style={{height:(screenWidth / 2) - 15, width:(screenWidth / 2) - 15,borderColor:'#AAAAAA',borderWidth:0.1,justifyContent:'center',alignItems:'center'}}>
                            <Text style={[styles.titleTextStyle,{color:"#aaaaaa"}]}>无</Text>
                        </View>
                    }
                    </View>
                    <View style={[styles.titleViewStyle,{height:40,marginBottom:0}]}>
                        <Text numberOfLines={2} style={[styles.titleTextStyle,{marginLeft:10,overflow:'hidden'}]}>{item.exchangeGoodsName}</Text>
                    </View>
                    <View style={[styles.titleViewStyle,{flexDirection:'row',flexWrap:'wrap',justifyContent:'space-around',alignItems:'center',marginTop:0}]}>
                        <Text style={[styles.titleTextStyle,{marginLeft:10,marginTop:10}]}>￥{item.exchangePointValue}</Text>
                        <View style={[CommonStyle.itemBottomBtnStyle,{justifyContent:'center'}]}>
                            <TouchableOpacity onPress={()=>{
                                if (this.state.pointTotalValue>=item.exchangePointValue) {
                                    Alert.alert('确认','您当前累计积分为'+this.state.pointTotalValue+'，您确定要兑换吗？',[
                                        {
                                            text:"取消", onPress:()=>{
                                            WToast.show({data:'点击了取消'});
                                            // this在这里可用，传到方法里还有问题
                                            // this.props.navigation.goBack();
                                            }
                                        },
                                        {
                                            text:"确定", onPress:()=>{
                                                WToast.show({data:'点击了确定'});
                                                // this.exchangeGoods(item)
                                            }
                                        }
                                    ]);
                                }else{
                                    Alert.alert('提示','累计积分不足'+'，您当前累计积分为'+this.state.pointTotalValue,[
                                        {
                                            text:"确定", onPress:()=>{
                                            WToast.show({data:'点击了确定'});
                                            }
                                        }
                                    ]);
                                }
                            }}>
                                <View style={[CommonStyle.itemBottomEditBtnViewStyle,{borderColor:"#3ab240",borderWidth:1,backgroundColor:'rgba(0,0,0,0)',width:70,flexDirection:"row",justifyContent:'space-around'}
                                    ]}>
                                        <Image  style={{width:20, height:20}} source={require('../../assets/icon/iconfont/exchange3.png')}></Image>
                                    <Text style={[CommonStyle.itemBottomEditBtnTextStyle,{color:"#3ab240"}]}>兑换</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='积分商城'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                <FlatList 
                        data={this.state.dataSource}
                        numColumns = {2}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth / 2,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    innerViewStyle: {
        // marginTop: 10,
        borderColor: "#F4F4F4",
        borderWidth: 8,
    },
    titleViewStyle: {
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
});