import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Clipboard,Linking,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import ImageViewer from 'react-native-image-zoom-viewer';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class InventoryLocationAllotList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态',
            refreshing: false,
            pageSize:6,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            selCustomerId:null,
            selCustomerName:"",
            gmtCreated:null,
            selectGmtCreated:null,
            compressFileList:[],
            urls:[],
            isShowImage: false,
            pictureIndex:0
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    initGmtCreated=()=>{
        // 当前时间
        var currentDate = new Date();
        //获取当前时间的毫秒数
        var nowMilliSeconds = currentDate.getTime();
        // 设置查询30内的数据
        // currentDate.setTime(nowMilliSeconds - (30 * 86400000));
        currentDate.setMonth(currentDate.getMonth());
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + (currentDate.getDate())).slice(-2);

        // if ((currentDate.getDate() - 1) == 0) {
        //     currentDate.setMonth(currentDate.getMonth() -1);
        //     var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        //     currentDate.setMonth(currentDate.getMonth() +1 , 0);
        //     var lastDay = currentDate.getDate();
        //     var currentDateDay = ("0" + (lastDay)).slice(-2);
        // }


        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({  
            selectGmtCreated:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            gmtCreated:_gmtCreated,
            initGmtCreated:_gmtCreated
        })
        return _gmtCreated;
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        var _gmtCreated = this.initGmtCreated();
        this.loadInventoryLocationAllotList(_gmtCreated);
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/inventory/location/allot/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "customerId":this.state.selCustomerId ? this.state.selCustomerId : null,
            "checkOutTime": this.state.gmtCreated ? this.state.gmtCreated : null,
            "showDetail":true
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.selCustomerId == null) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            selCustomerName:"",
            selCustomerId:null,
        })
        this.setState({
            currentPage:1
        })
        let url= "/biz/inventory/location/allot/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "checkOutTime": this.state.gmtCreated ? this.state.gmtCreated : null,
            "showDetail":true
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, {pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========第一页即是最后一页，不加载=====");
            return;
        }
        this.loadInventoryLocationAllotList();
    }

    loadInventoryLocationAllotList=(_gmtCreated)=>{
        let url= "/biz/inventory/location/allot/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "checkOutTime": _gmtCreated ? _gmtCreated : this.state.gmtCreated,
            "showDetail":true
        };
        httpPost(url, loadRequest, this.loadInventoryLocationAllotListCallBack);
    }

    loadInventoryLocationAllotListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, {pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteInventoryLocationAllot =(allotId)=> {
        console.log("=======delete=allotId", allotId);
        let url= "/biz/inventory/location/allot/delete";
        let requestParams={'allotId':allotId};
        httpDelete(url, requestParams, this.deleteOrFinishCallBack);
    }

    finishInventoryLocationAllot =(allotId)=> {
        console.log("=======delete=allotId", allotId);
        let url= "/biz/inventory/location/allot/confirm_finish";
        let requestParams={'allotId':allotId};
        httpPost(url, requestParams, this.deleteOrFinishCallBack);
    }


    // 删除操作的回调操作
    deleteOrFinishCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"操作完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    openGmtCreated(){
        this.refs.SelectGmtCreated.showDate(this.state.selectGmtCreated)
    }

    callBackSelectGmtCreatedValue(value){
        console.log("==========时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectGmtCreated:value
        })
        if (this.state.selectGmtCreated && this.state.selectGmtCreated.length) {
            var _gmtCreated = "";
            var vartime;
            for(var index=0;index<this.state.selectGmtCreated.length;index++) {
                vartime = this.state.selectGmtCreated[index];
                if (index===0) {
                    _gmtCreated += vartime;
                }
                else if (index < 3){
                    _gmtCreated += "-" + vartime;
                }
                else if (index===3){
                    _gmtCreated += " " + vartime;
                }
                else {
                    _gmtCreated += ":" + vartime;
                }
            }
            this.setState({
                currentPage: 1,
                gmtCreated:_gmtCreated
            })

            let url= "/biz/inventory/location/allot/list";
            let loadRequest={
                "currentPage": this.state.currentPage,
                "pageSize": this.state.pageSize,
                "checkOutTime": _gmtCreated ? _gmtCreated : this.state.gmtCreated,
                "showDetail":true
            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);

        }
    }

    renderItemDetailRow=(itemDetailList)=>{
        return (
            <FlatList
            data={itemDetailList}
            renderItem={({item}) =>
            <View key={item.detailId} style={styles.titleViewStyle}>
                <View style={{ }}>
                    <Text style={[styles.titleTextStyle,{width:(screenWidth - 35) * 0.4,flexWrap:"wrap"}]}>产品：{item.seriesName}-{item.brickTypeName}</Text>
                </View>
                <View style={[{width:(screenWidth - 35) * 0.5,flexWrap:"wrap", marginLeft:5, marginRight:10}]}>
                    <View style={[styles.itemContentChildViewStyle]}>
                        <Text style={styles.titleTextStyle}>调拨前：{item.allotOutLocationName}</Text>
                    </View>
                    {
                       item.allotInLocationId ?
                        <View style={[styles.itemContentChildViewStyle]}>
                            <Text style={styles.titleTextStyle}>调拨后：{item.allotInLocationName}</Text>
                        </View>
                       :
                       null
                    }
                    <View style={[styles.itemContentChildViewStyle]}>
                        <Text style={styles.titleTextStyle}>数量：{item.allotAmount}</Text>
                    </View>
                    <View style={[styles.itemContentChildViewStyle]}>
                        <Text style={styles.titleTextStyle}>重量(吨)：{item.allotWeight}</Text>
                    </View>
                </View>
            </View>}
            />
        );
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.allotId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>调拨时间：{item.gmtCreated}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>调拨操作人：{item.userName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>调拨状态：{item.allotState === "0AA" ? "调拨中" : "调拨完成"}</Text>
                </View>
                {
                    item.compressFileList && item.compressFileList.length > 0 ?
                    (
                        <View>
                            {
                                item.pictureDisplay === "N"?
                                    <View style={[styles.titleViewStyle, { justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                                        <Text style={styles.titleTextStyle}>附件：</Text>
                                        <TouchableOpacity onPress={() => {
                                            var urls = [];
                                            if(item.compressFileList && item.compressFileList.length > 0){
                                                for(var i=0;i<item.compressFileList.length;i++){
                                                    var url = {
                                                        url:constants.image_addr + '/' +  item.compressFileList[i].compressFile
                                                    }
                                                    urls=urls.concat(url)
                                                    console.log(url)
                                                }
                                            }
                                            this.setState({
                                                urls:urls
                                            })
                                            let list = this.state.dataSource;
                                            list.map((elem, index) => {
                                                if(elem.allotId == item.allotId){
                                                    elem.pictureDisplay = "Y"
                                                }
                                            })
                                            this.setState({
                                                dataSource:list
                                            })
                                            // console.log("==============",list)
                                        }}>
                                                <Text style={[styles.titleTextStyle,{color:"#CB4139"}]}>点击展开</Text>
                                        </TouchableOpacity>
                                    </View>
                                :
                                <View>
                                    <View style={styles.titleViewStyle}>
                                        <Text style={styles.titleTextStyle}>附件：</Text>
                                    </View>
                                    <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                        {
                                            item.compressFileList.map((item,index) =>{
                                            return(
                                                <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex'}]}>

                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        isShowImage:true,
                                                        pictureIndex:index
                                                    })
                                                }}>
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />
                                                </TouchableOpacity>
                                                <Modal visible={this.state.isShowImage} transparent={true}>
                                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} index={this.state.pictureIndex} enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }} onSave={() => alert("点击了保存图片")} onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls} />
                                                </Modal>
                                            </View>
                                            )
                                            })
                                        }
                                    </View>
                                    <View style={[styles.titleViewStyle,{justifyContent:'center'}]}>
                                        {
                                            item.pictureDisplay === "Y"?
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    urls:[]
                                                })
                                                let list = this.state.dataSource;
                                                list.map((elem, index) => {
                                                    if(elem.allotId == item.allotId){
                                                        elem.pictureDisplay = "N"
                                                    }
                                                })
                                                this.setState({
                                                    dataSource:list
                                                })
                                                // console.log("==============",list)
                                            }}>
                                                    <Text style={[styles.titleTextStyle,{color:"#CB4139",textAlign:'center'}]}>点击收起</Text>
                                            </TouchableOpacity>
                                            :
                                            <View/>
                                        }
                                    </View>
                                </View>

                            }

                        </View>
                    ):
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>附件：无</Text>
                    </View>
                }

                <View style={{}}>
                    {this.renderItemDetailRow(item.allotDetailDTOList)}
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle]}>
                    <TouchableOpacity onPress={()=>{
                            if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                                return;
                            }
                            this.props.navigation.navigate("InventoryLocationAllotAdd",
                            {
                                // 传递参数
                                allotId:item.allotId,
                                compressFileList:item.compressFileList,
                                allotDetailDTOList:item.allotDetailDTOList,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBtnViewStyle,item.allotState === "0AC" ? CommonStyle.hiddenViewStyle : null
                        ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ,{width:90},{width:100,flexDirection:'row'}]}>
                            <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>调拨</Text>
                        </View>
                    </TouchableOpacity>
                    {/* <TouchableOpacity onPress={()=>{
                        if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                            return;
                        }
                        Alert.alert('确认','您确定要完成该条调拨操作吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.finishInventoryLocationAllot(item.allotId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle, item.allotState === "0AC" ? CommonStyle.hiddenViewStyle : null
                        ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ,{width:70,flexDirection:'row'}]}>
                            <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/acceptNonClickable.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>完成</Text>
                        </View>
                    </TouchableOpacity> */}
                    <TouchableOpacity onPress={()=>{
                        if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                            return;
                        }
                        Alert.alert('确认','您确定要删除该条出库记录吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                // this在这里可用，传到方法里还有问题
                                // this.props.navigation.goBack();
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteInventoryLocationAllot(item.allotId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle
                        ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ,{width:70,flexDirection:'row'}]}>
                            <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity>

                    <TouchableOpacity onPress={()=>{
                            if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                                return;
                            }
                            this.props.navigation.navigate("InventoryLocationAllotAdd",
                            {
                                // 传递参数
                                allotId:item.allotId,
                                // 传递回调函数
                                refresh: this.callBackFunction
                            })
                        }}>
                        <View style={[CommonStyle.hiddenViewStyle, CommonStyle.itemBottomEditBtnViewStyle
                        ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ,{width:70},{width:80,flexDirection:'row'}]}>
                            <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("InventoryLocationAllotAdd",
                {
                    // 传递回调函数
                    refresh: this.callBackFunction
                })
            }}>
             <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }


    render(){
        return(
            <View>
                <CommonHeadScreen title='库位调拨管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                <View style={CommonStyle.contentViewStyle}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    // itemContentViewStyle:{
    //     flexDirection:'row',
    //     justifyContent:'space-between',
    //     marginLeft:25
    // },
    itemContentChildViewStyle:{
        justifyContent:'space-between',
        flexDirection:'row',
    },
});
