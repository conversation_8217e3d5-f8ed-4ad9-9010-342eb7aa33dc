import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Clipboard,Linking,Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class InventoryBlockWorkDetailList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            initGmtCreated:null,
            gmtCreated:null,
            selectGmtCreated:null,
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:6,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            brickTypeDataSource:[],
            seriesName:"",
            brickTypeName:"",
            brickTypeId:"",
            brickTypeNameLayoutHeight:0,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {

        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    initGmtCreated=()=>{
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth()-3);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectGmtCreated:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            gmtCreated:_gmtCreated,
            initGmtCreated:_gmtCreated,
        })
        return _gmtCreated;
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');

        const { route, navigation } = this.props;
        if (route && route.params) {
            const {customerId,customerName,contractId,contractName,positionId,positionName } = route.params;
            if (customerId && contractId && positionId) {
                this.setState({
                    customerId:customerId
                })
                this.loadInventoryDetailList(customerId,contractId,positionId);
            }
            if (customerName) {
                this.setState({
                    customerName:customerName
                })
            }
            if (contractName) {
                this.setState({
                    contractName:contractName
                })
            }
            if (positionName) {
                this.setState({
                    positionName:positionName
                })
            }
            
        }

        // 加载砖型
        var loadTypeUrl= "/biz/brick/series/type/effBrickTreeCatalog";
        var loadRequest={'currentPage':1,'pageSize':10000};
        httpPost(loadTypeUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data) {
                this.setState({
                    brickTypeDataSource:response.data
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/blockwork/inventory/detail";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "brickTypeId":this.state.brickTypeId ? this.state.brickTypeId : null,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.brickTypeId == null && this.state.gmtCreated === this.state.initGmtCreated) {
            console.log("==========不刷新=====");
            return;
        }
        var _gmtCreated = this.initGmtCreated();
        this.setState({
            gmtCreated:_gmtCreated,
            brickTypeName:"",
            brickTypeId:"",
        })
        this.setState({
            currentPage:1
        })
        let url= "/biz/blockwork/inventory/detail";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "brickTypeId":this.state.brickTypeId ? this.state.brickTypeId : null,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========第一页即是最后一页，不加载=====");
            return;
        }
        this.loadInventoryDetailList();
    }

    loadInventoryDetailList=(customerId,contractId,positionId)=>{
        let url= "/biz/blockwork/inventory/detail";
        let loadRequest={
            "customerId":customerId ? customerId : this.state.customerId,
            "contractId":contractId ? contractId : this.state.contractId,
            "positionId":positionId ? positionId : this.state.positionId,
        };
        httpPost(url, loadRequest, this.loadStorageInListCallBack);
    }

    loadStorageInListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteStorageIn =(storageInId)=> {
        console.log("=======delete=storageInId", storageInId);
        let url= "/biz/storage/in/delete";
        let requestParams={'storageInId':storageInId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.storageInId} style={[styles.innerViewStyle,item.storageInSetNumber ? {backgroundColor:'rgba(255,0,0,0.2)'} : {backgroundColor:'rgba(0,255,0,0.2)'}]}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>库存套数：{item.beforeInventorySetNumber}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.storageInSetNumber ? "入库套数" : "出库套数"}：{item.storageInSetNumber ? item.storageInSetNumber : item.storageOutSetNumber}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>结余库存：{item.afterInventorySetNumber}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>库位：{item.locationName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>操作人：{item.operatorName}</Text>
                </View>
                
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>更新时间：{item.gmtCreated}</Text>
                </View>
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} >
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                }}  >
                    {/* <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image> */}
                </TouchableOpacity>
            </View>
        )
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal:16}}/>)
    }

    brickTypeNameLayout=(event)=> {
        this.setState({
            brickTypeNameLayoutHeight: event.nativeEvent.layout.height
        })

    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='库存明细'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                 <View style={[styles.innerViewStyle,{marginTop:0, index:1000}]} onLayout={this.brickTypeNameLayout.bind(this)}>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, marginRight:100}]}>客户名称：{this.state.customerName ? this.state.customerName : "-"}</Text>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, marginRight:100}]}>合同名称：{this.state.contractName ? this.state.contractName : "-"}</Text>
                     <Text style={[styles.titleTextStyle,{marginLeft:10, marginRight:100}]}>部位名称：{this.state.positionName ? this.state.positionName : "-"}</Text>

                    <View style={{position:'absolute', backgroundColor:'rgba(255,0,0,0.2)', right:50, top:-5, height:30, padding:5}}>
                         <Text>入库</Text>
                     </View>
                     <View style={{position:'absolute', backgroundColor:'rgba(0,255,0,0.2)', right:10, top:-5, height:30, padding:5}}>
                         <Text>出库</Text>
                     </View>
                </View>

                
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.brickTypeNameLayoutHeight)}]}>
                    {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}> */}
                        <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:10, borderBottomColor:'#F4F7F9'}}>
                        </View> 
                        <FlatList 
                            data={this.state.dataSource}
                            renderItem={({item,index}) => this.renderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                            ItemSeparatorComponent={this.space}
                            // 自定义下拉刷新
                            refreshControl={
                                <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                />
                            }
                            // 底部加载
                            ListFooterComponent={()=>this.flatListFooterComponent()}
                            onEndReached={()=>this._loadNextData()}
                            />
                    {/* </ScrollView> */}
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});