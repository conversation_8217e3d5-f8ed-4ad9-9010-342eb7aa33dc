import React, { Component } from 'react';
import {
    View, Text, StyleSheet, TouchableWithoutFeedback, TouchableOpacity, Dimensions, Image,FlatList,
    ActivityIndicator, StatusBar
} from 'react-native';

import AsyncStorage from '@react-native-community/async-storage';
//导入Video组件
import Video from 'react-native-video';
// 导入 Silder组件
import Slider from '@react-native-community/slider';
// 屏幕方向锁定: 他需要改变 原来Android文件代码，当然适配两端的话，IOS也是需要更改的。
// import Orientation from 'react-native-orientation-locker';

import CommonHeadScreen from '../../../component/CommonHeadScreen';
import { Modal } from 'react-native-paper';
var CommonStyle = require('../../../assets/css/CommonStyle');
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
let screenWidth = Dimensions.get('window').width;
let screenHeight = Dimensions.get('window').height;
export default class CourseVidioList extends Component {

    constructor(props) {
        super(props);

        this.changePausedState = this.changePausedState.bind(this);
        this.customerSliderValue = this.customerSliderValue.bind(this);
        this.enterFullScreen = this.enterFullScreen.bind(this);
        this._changePauseSliderFullState = this._changePauseSliderFullState.bind(this);
        this._onStartShouldSetResponder = this._onStartShouldSetResponder.bind(this);
        
        this.state = {
            videoId: null,
            videoTitle: null,
            videoUrl: null,
            videoImage: null,
            videoDesc: null,
            courseName: null,
            courseLevelName: null,
            courseSort: null,
            courseTypeName: null,
            userName: "",
            gmtCreated: null,
            imageHeight: null,
            isPaused: true,  //是暂停
            duration: 0,      //总时长
            currentTime: 0, //当前播放时间
            sliderValue: 0,   //进度条的进度
            //用来控制进入全屏的属性
            videoWidth: screenWidth,
            videoHeight: 226,
            isFullScreen: false,
            isVisiblePausedSliderFullScreen: false,
            documentId: null,
            documentName: null,
            loading: null,
            isEnd: false,
            vidioDataSource:[]
        }

    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');

        const { route, navigation } = this.props;
        if (route && route.params) {
            const {dataItem} = route.params;
            this.setState(
                dataItem
            )
        }
        console.log('=====this.props:', this.props);

        // 读本地储存, 查看是否同意过隐私政策
        AsyncStorage.getItem("userAgreeIsChecked", (err, result) => {
            if (err) {
                console.log('userAgreeIsChecked error' + err);
                return;
            }
            let userAgreeIsChecked = result;
            if (userAgreeIsChecked != null && userAgreeIsChecked === 'Y') {

            }
            return result;
        });

    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View />
        )
    }

    changePausedState() { //控制按钮显示播放，要显示进度条3秒钟，之后关闭显示
        this.setState({
            isPaused: this.state.isPaused ? false : true,
            isVisiblePausedSliderFullScreen: true
        })
        //这个定时调用失去了this指向
        let that = this;
        setTimeout(function () {
            that.setState({
                isVisiblePausedSliderFullScreen: false
            })
        }, 3000)

        if (this.state.isEnd) {
            this.player.seek(0);
        }
    }

    // 播放结束可将暂停变量设置为true
    onEnd() {
        this.setState({
            isPaused: true,
            isEnd: true
        });
    }

    // 缓冲，loading变量可控制缓冲圈显示
    onBuffer({ isBuffering }) {
        this.setState({ loading: isBuffering });
    }


    _changePauseSliderFullState() { // 单击事件，是否显示 “暂停、进度条、全屏按钮 盒子”
        let flag = this.state.isVisiblePausedSliderFullScreen ? false : true;
        this.setState({
            isVisiblePausedSliderFullScreen: flag
        })
        //这个定时调用失去了this指向
        let that = this;
        setTimeout(function () {
            that.setState({
                isVisiblePausedSliderFullScreen: false
            })
        }, 3000)
    }
    //格式化音乐播放的时间为0：00。借助onProgress的定时器调用，更新当前时间
    formatMediaTime(time) {
        let minute = Math.floor(time / 60);
        let second = parseInt(time - minute * 60);
        minute = minute >= 10 ? minute : "0" + minute;
        second = second >= 10 ? second : "0" + second;
        return minute + ":" + second;

    }
    //加载视频调用，主要是拿到 “总时间”，并格式化
    customerOnload(e) {
        let time = e.duration;
        this.setState({
            duration: time
        })
    }
    // 获得当前的，播放时间数，但这个数是0.104，需要处理
    customerOnprogress(e) {
        let time = e.currentTime;   // 获取播放视频的秒数
        this.setState({
            currentTime: time,
            sliderValue: time
        })
    }
    // 移动滑块，改变视频播放进度
    customerSliderValue(value) {
        this.player.seek(value);
        // this.setState({
        //     isPaused: true,
        // });
    }

    // sliderComplete(value){
    //     this.player.seek(value);
    //     this.setState({
    //         isPaused: false,
    //     });

    // }

    enterFullScreen() { //1.改变宽高  2.允许进入全屏模式  3.如何配置屏幕旋转,不需要改变进度条盒子的显示和隐藏
        // const Orientation = require('react-native-orientation-locker').default;
        // if (!this.state.isFullScreen) {
        //     this.setState({
        //         videoWidth: screenHeight,
        //         videoHeight: screenWidth,
        //         isFullScreen: true
        //     })
        //     // 改为水平方向
        //     Orientation.lockToLandscapeLeft();
        // }
        // else {
        //     this.setState({
        //         videoWidth: screenWidth,
        //         videoHeight: 226,
        //         isFullScreen: false
        //     })
        //     // 改为竖直方向
        //     Orientation.lockToPortrait();
        // }
    }
    _onStartShouldSetResponder(e) {
        console.log(e);
    }
    componentDidMount() {
        // const Orientation = require('react-native-orientation-locker').default;
        // var initial = Orientation.getInitialOrientation();
        // if (initial === 'PORTRAIT') {
        //     console.log('是竖屏');
        // } else {
        //     console.log('如果是横屏，就将其旋转过来');
        //     Orientation.lockToPortrait();
        // }
    }
    vidioRenderRow = (item, index) => {
        return (
            <View>
            <View key={item.vidioId+"vidioId"} style={{
                backgroundColor:"white",
                marginTop:2,
                height:80,
                flexDirection: 'row',
                alignItems:"center"
                }}>
                <View style={{
                    // backgroundColor:"green",
                    //外边距
                    marginLeft: 14,
                    marginRight: 0,
                    marginTop: 0,
                    marginBottom: 0,
                    height:48,
                    width:48,
                    justifyContent: 'center',
                    alignItems:"center"
                }}>
                    <Text>{index+1}</Text>
                </View>
                <View style={{
                    // backgroundColor:"red",
                    //外边距
                    marginLeft: 11,
                    marginRight: 0,
                    marginTop: 0,
                    marginBottom: 0,
                    height:48,
                    width:screenWidth-48-14-11-16-24,
                    justifyContent: 'space-between',
                }}>
                    <View style={{
                        // backgroundColor:"green",
                        height:23,justifyContent: 'center',
                    }}>
                        <Text style={{fontSize:16}}>{item.documentName}</Text>
                    </View>
                    <View style={{
                        // backgroundColor:"green",
                        height:23,flexDirection: 'row',alignItems:"center"
                    }}>
                        <View style={{
                            width:42,
                            height:20,
                            borderColor:"#ECEEF2",
                            borderRadius:2,
                            borderWidth:1,
                            justifyContent: 'center',
                            alignItems:"center"
                        }}>
                            <Text style={{ fontSize: 12}}>视频</Text>
                        </View>
                        {/* <Image style={{ height: 13 , width: 12, marginLeft: 10}} source={require('../../../assets/icon/iconfont/clock.png')}></Image>
                        <Text style={{ fontSize:12,marginLeft: 10}}>45分     已学完</Text> */}
                    </View>
                </View>
                <TouchableOpacity onPress={
                    ()=>{
                        console.log("播放",JSON.stringify(item, null, 6))
                        this.setState(item)
                    }
                }>
                    <View style={{
                        // backgroundColor:"green",
                        //外边距
                        marginLeft: 0,
                        marginRight: 16,
                        marginTop: 0,
                        marginBottom: 0,
                        height:24,
                        width:24
                    }}>
                        <Image style={{ height: 24 , width: 24}} source={require('../../../assets/icon/iconfont/play_new.png')}></Image>
                    </View>
                </TouchableOpacity>
            </View>
            <View style={styles.lineViewStyle}/>
            </View>
        )
    }
    renderTitleItem=()=>{
        return (
            <Text style={styles.headCenterTitleText}>{
                this.state.documentName.slice(0, this.state.documentName.length-4)
                // "视频"
            }</Text>
        )
    }

    render() {
        // 播放按钮组件：是否显示
        let playButtonComponent = (
            <TouchableWithoutFeedback
                onPress={this.changePausedState}
            >
                <View style={styles.playBtn}>
                    <Image source={
                        require('../../../assets/icon/iconfont/play.png')
                    } style={{ width: 25, height: 25 }} />
                </View>
            </TouchableWithoutFeedback>
        );
        let pausedBtn = this.state.isPaused ? playButtonComponent : null;
        // 暂停按钮、进度条、全屏按钮 是否显示
        let pausedSliderFullComponent = (
            <View style={{ position: "absolute", bottom: 0, right: 0, left: 0, backgroundColor: '#dbdbdb', opacity: 0.7 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={{ flex: 1, alignItems: 'center' }}>
                        <TouchableOpacity onPress={this.changePausedState}>
                            <Image source={
                                this.state.isPaused
                                    ? require('../../../assets/icon/iconfont/play.png')
                                    : require('../../../assets/icon/iconfont/pause.png')
                            } style={{ width: 15, height: 15 }} />
                        </TouchableOpacity>
                    </View>

                    {/* 进度条按钮     */}
                    <View style={styles.sliderBox}>
                        <Text style={{ width: 40 }}>{this.formatMediaTime(this.state.currentTime)}</Text>
                        <Slider
                            style={{ flex: 1, height: 40 }}
                            value={this.state.sliderValue}
                            minimumValue={0}
                            maximumValue={this.state.duration}
                            thumbTintColor="#000" //开关夹点的yanse
                            minimumTrackTintColor="#e7e7e7"
                            maximumTrackTintColor="#808080"
                            step={1}
                            onValueChange={this.customerSliderValue}
                        // onSlidingComplete={(value)=>this.sliderComplete(value)}
                        />
                        <Text>{this.formatMediaTime(this.state.duration)}</Text>
                    </View>
                    {/* 全屏按钮 */}
                    <View style={{ flex: 1, alignItems: 'center' }}>
                        <TouchableOpacity
                            onPress={this.enterFullScreen}
                        >
                            <Image source={!this.state.isFullScreen ? require('../../../assets/icon/iconfont/fullScreen.png') : require('../../../assets/icon/iconfont/cancelFullScreen.png')} style={!this.state.isFullScreen ? { width: 18, height: 18 } : { width: 20, height: 20 }} />
                        </TouchableOpacity>
                    </View>


                </View>
            </View>
        );

        let returnSliderFullComponent = (
            <View style={{ position: "absolute", height: 30, top: 0, right: 0, left: 0, backgroundColor: '#dbdbdb', opacity: 0.7, zIndex: 1000 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                    <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', height: 30 }}>
                        <TouchableOpacity onPress={this.enterFullScreen}>
                            <Image source={
                                require('../../../assets/icon/iconfont/backWhite.png')
                            } style={{ width: 20, height: 20 }} />
                        </TouchableOpacity>
                    </View>
                    <View style={{ flex: 9, alignItems: 'center' }}>
                    </View>
                </View>
            </View>
        );
        let pausedSliderFull = this.state.isVisiblePausedSliderFullScreen ? pausedSliderFullComponent : null;
        let returnSliderFull = (this.state.isVisiblePausedSliderFullScreen && this.state.isFullScreen) ? returnSliderFullComponent : null;

        return (
            <View>
                <StatusBar
                    // animated={true} //指定状态栏的变化是否应以动画形式呈现。目前支持这几种样式：backgroundColor, barStyle和hidden  
                    hidden={true}  //是否隐藏状态栏。
                    backgroundColor={'#FFFFFF'} //状态栏的背景色  
                    // translucent={true}//指定状态栏是否透明。设置为true时，应用会在状态栏之下绘制（即所谓“沉浸式”——被状态栏遮住一部分）。常和带有半透明背景色的状态栏搭配使用。  
                    barStyle={'dark-content'} // enum('default', 'light-content', 'dark-content')   
                >
                </StatusBar>
                {!this.state.isFullScreen && <CommonHeadScreen 
                    // title={this.state.documentName.slice(0, this.state.documentName.length-4)}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    titleItem={() =>this.renderTitleItem()}
                />}

                {
                    this.state.loading ? <View style={{}}>
                        <ActivityIndicator size='large' color='lightgray' />
                    </View> : null
                }

                <View >

                    {/* 返回按钮，全屏点击时显示 */}
                    {returnSliderFull}

                    <TouchableWithoutFeedback
                        onPress={this._changePauseSliderFullState}
                        onResponderMove={this._onStartShouldSetResponder}
                    >
                        <Video source={{ uri: constants.service_addr + "/biz/view/playMp4?fileId=" + this.state.documentId }}
                            ref={(ref) => {
                                this.player = ref
                            }}
                            style={{ width: this.state.videoWidth, height: this.state.videoHeight, backgroundColor: "#000000" }}
                            allowsExternalPlayback={false} // 不允许导出 或 其他播放器播放
                            paused={this.state.isPaused} // 控制视频是否播放
                            poster={this.state.videoImage}
                            onRestoreUserInterfaceForPictureInPictureStop
                            volume={1}
                            resizeMode="cover"
                            posterResizeMode="cover"
                            onLoad={(e) => this.customerOnload(e)}
                            onEnd={() => {
                                this.onEnd(); // 播放结束时执行
                            }}
                            onBuffer={data => this.onBuffer(data)}
                            onProgress={(e) => this.customerOnprogress(e)}
                            fullscreen={this.state.isFullScreen}
                        />
                    </TouchableWithoutFeedback>
                    {/* 播放的按钮：点击之后需要消失 */}
                    {pausedBtn}
                    {/* 暂停按钮，进度条，全屏按钮 */}
                    {pausedSliderFull}
                </View>
                {/*this.state.videoUrl*/}
                {/* <Video source={{uri: "http://lmz-beijing.oss-cn-beijing.aliyuncs.com/video/1.mp4"}}
                        ref={(ref) => {
                            this.player = ref
                        }}                                      // Store reference
                        onBuffer={this.onBuffer}                // Callback when remote video is buffering
                        onError={this.videoError}               // Callback when video cannot be loaded
                        style={{position: 'absolute', width:screenWidth, top:40, bottom:0, left:0, right:0}}
                        /> */}
                <View style={CommonStyle.contentViewStyle}>

                    <View style={{ flexDirection: 'column', paddingTop: 1, marginLeft: 14, marginRight: 6 }}>
                        <View style={[styles.titleViewStyle, { marginTop: 10,marginLeft: 0 }]}>
                            <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16, fontWeight: '400', width: screenWidth - 28 }} >{this.state.documentName.slice(0, this.state.documentName.length-4)}</Text>
                        </View>
                        <View style={{backgroundColor:"rgba(30, 110, 250, 1)",marginTop: 10,borderRadius:2,width:42,height:20,justifyContent:"center",alignItems:"center"}}>
                            <Text style={{fontSize:12,color:"white"}}>视频</Text>
                        </View>
                    </View>

                    <View style={[styles.lineViewStyle,{marginBottom:0}]}></View>
                        <View style={{height:40,paddingLeft:14,flexDirection:"row",alignItems:"flex-end"}}>
                            <Text style={{fontSize:18}}>视频</Text>
                            <Text style={{color:"rgba(0, 10, 32, 0.65)"}}>{"("+this.state.vidioDataSource.length+")"}</Text>
                        </View>
                    <View style={[styles.lineViewStyle,{marginTop:10}]}></View>
                    {/* 列表 */}
                    <View 
                    style={{height:ifIphoneXContentViewDynamicHeight(screenWidth*0.637-28+60+12+16)}}
                    >
                        <FlatList
                            data={this.state.vidioDataSource}
                            renderItem={({ item, index }) => this.vidioRenderRow(item, index)}
                            ListEmptyComponent={this.emptyComponent}
                        />
                    </View>

                </View >
            </View >
        )
    }
}
var styles = StyleSheet.create({
    myVideo: {
        width: 340,
        height: 240
    },
    playBtn: {
        width: 50,
        height: 50,
        // backgroundColor:'red',
        borderRadius: 50,
        position: "absolute",
        top: "50%",
        left: "50%",
        marginLeft: -25,
        marginTop: -25,
        zIndex: 999
    },
    sliderBox: {
        flex: 8,
        // marginLeft:10,
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    lineViewStyle: {
        height: 1,
        marginLeft: 13,
        marginRight: 13,
        // marginTop: 15,
        // marginBottom: 22,
        borderBottomWidth: 0.5,
        borderColor: '#E8E9EC'
    },
    innerViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16
    },
    titleTextStyle: {
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
});
