import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Linking,Clipboard,
    FlatList,RefreshControl,Image,Modal
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import BottomScrollSelect from '../../../component/BottomScrollSelect';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
import ImageViewer from 'react-native-image-zoom-viewer';
import { saveImage } from '../../../utils/CameraRollUtils';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;

var screenHeight = Dimensions.get('window').height;
export default class MyAskQuestionsList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            selAskQuestionsStateCode:'all',
            qryStartTime:null,
            selectedQryStartDate:[],
            compressFileList:[],
            urls:[],
            isShowImage: false,
            pictureIndex:0
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let askQuestionsState = [
            {
                stateCode:'all',
                stateName:'全部',
            },
            {
                stateCode:'0AA',
                stateName:'待解决',
            },
            {
                stateCode:'0BB',
                stateName:'解决中',
            },
            {
                stateCode:'0CC',
                stateName:'已解决',
            },
        ]
        this.setState({
            askQuestionsState:askQuestionsState,
        })
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectedQryStartDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            // qryStartTime:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })
        this.loadMyAskQuestionsList();
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/portal/ask/questions/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "askQuestionsUserId":constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
            "askQuestionsState": this.state.selAskQuestionsStateCode === 'all' ? null : this.state.selAskQuestionsStateCode,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/portal/ask/questions/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "askQuestionsUserId":constants.loginUser.userId,
            "qryStartTime": this.state.qryStartTime,
            "askQuestionsState": this.state.selAskQuestionsStateCode === 'all' ? null : this.state.selAskQuestionsStateCode,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadMyAskQuestionsList();
    }

    loadMyAskQuestionsList=()=>{
        let url= "/biz/portal/ask/questions/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "askQuestionsUserId":constants.loginUser.userId,
            "askQuestionsState": this.state.selAskQuestionsStateCode === 'all' ? null : this.state.selAskQuestionsStateCode,
            "qryStartTime": this.state.qryStartTime,
        };
        httpPost(url, loadRequest, this.loadMyAskQuestionsListCallBack);
    }


    loadMyAskQuestionsListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            let list = dataAll;
            let listNew = []
            list.map((item, index) => {
                listNew.push(Object.assign({}, item, { pictureDisplay: "N"}))
            })
            this.setState({
                dataSource:listNew,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteMyAskQuestions =(askQuestionsId)=> {
        console.log("=======delete=askQuestionsId", askQuestionsId);
        let url= "/biz/portal/ask/questions/delete";
        let requestParams={'askQuestionsId':askQuestionsId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderAskQuestionsStateRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    let selAskQuestionsStateCode = item.stateCode;
                    this.setState({
                        "selAskQuestionsStateCode":selAskQuestionsStateCode
                    })
                    let loadUrl= "/biz/portal/ask/questions/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "askQuestionsUserId":constants.loginUser.userId,
                        "askQuestionsState": selAskQuestionsStateCode === 'all' ? null : selAskQuestionsStateCode,
                        "qryStartTime": this.state.qryStartTime,
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.stateCode} style={[CommonStyle.tabItemViewStyle]}>
                        <Text style={[item.stateCode === this.state.selAskQuestionsStateCode ?
                            [CommonStyle.selectedtabItemTextStyle]
                            :
                            [CommonStyle.tabItemTextStyle]
                        ]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    openQryStartDate(){
        this.refs.SelectQryStartDate.showDate(this.state.selectedQryStartDate)
    }
    
    callBackSelectQryStartDateValue(value){
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedQryStartDate:value
        })
        if (value && value.length) {
            var qryStartTime = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    qryStartTime += vartime;
                }
                else{
                    qryStartTime += "-" + vartime;
                }
            }
            this.setState({
                qryStartTime:qryStartTime
            })

            let loadUrl= "/biz/portal/ask/questions/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "askQuestionsUserId":constants.loginUser.userId,
                "askQuestionsState": this.state.selAskQuestionsStateCode === 'all' ? null : this.state.selAskQuestionsStateCode,
                "qryStartTime": this.state.qryStartTime,
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }
    }

    resetQry(){
        this.setState({
            qryStartTime:null,
            selAskQuestionsStateCode:"all",
        })
        let loadUrl= "/biz/portal/ask/questions/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "askQuestionsUserId":constants.loginUser.userId,
            "askQuestionsState": null,
            "qryStartTime": null,
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    exportPdfFile=()=> {
        console.log("=======exportPdfFile");
        let url= "/biz/generate/pdf/ask_questions";
        let requestParams={
            "currentPage": 1,
            "pageSize": 1000,
            "askQuestionsUserId": constants.loginUser.userId,
            "qryStartTime":this.state.qryStartTime,
            "askQuestionsState": this.state.selAskQuestionsStateCode === 'all' ? null : this.state.selAskQuestionsStateCode,
        };
        httpPost(url, requestParams, (response)=>{
            if (response.code == 200 && response.data) {
                Clipboard.setString(response.data); 
                WToast.show({data:"导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n" + response.data});
                Alert.alert('确认','导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',[
                    {
                        text:"不打开", onPress:()=>{
                        WToast.show({data:'点击了不打开'});
                        }
                    },
                    {
                        text:"打开", onPress:()=>{
                            WToast.show({data:'点击了打开'});
                            // 直接打开外网链接 
                            Linking.openURL(response.data)
                        }
                    }
                ]);
            }
        });
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.askQuestionsId} style={[CommonStyle.innerViewStyle]}>                
                <View style={[styles.titleViewStyle,{}]}>
                    <Text style={[styles.titleTextStyle,{marginRight:50,marginLeft: 15,marginTop:10,fontWeight: 'bold',fontSize: 20,color: '#404956'}]}>{item.askQuestionsTitle}</Text>
                    {item.askQuestionsState !== '0AA' ? (
                    <View style={{position: 'absolute', right: 0, top: 10}}>
                        {item.askQuestionsState === '0BB' ? (
                        <Text style={{color: 'green'}}>解决中</Text>
                        ) : (
                        <Text style={{color: '#CB4139'}}>已解决</Text>
                        )}
                    </View>
                    ) : (
                    <View />
                    )}
                </View>
                <View style={[styles.titleViewStyle,{}]}>
                    <Text style={styles.titleTextStyle}>{item.askQuestionsContent}</Text>
                </View>
                {
                    item.compressFileList && item.compressFileList.length > 0 ?
                    (
                        <View>
                            {
                                item.pictureDisplay === "N"?
                                    <View style={[[styles.titleViewStyle,{}], { justifyContent: 'flex-start', flexWrap: 'wrap' }]}>
                                        <Text style={styles.titleTextStyle}>附件：</Text>
                                        <TouchableOpacity onPress={() => {
                                            var urls = [];
                                            if(item.compressFileList && item.compressFileList.length > 0){
                                                for(var i=0;i<item.compressFileList.length;i++){
                                                    var url = {
                                                        url:constants.image_addr + '/' +  item.compressFileList[i].compressFile
                                                    }
                                                    urls=urls.concat(url)
                                                    console.log(url)
                                                }
                                            }
                                            this.setState({
                                                urls:urls
                                            })
                                            let list = this.state.dataSource;
                                            list.map((elem, index) => {
                                                if(elem.askQuestionsId == item.askQuestionsId){
                                                    elem.pictureDisplay = "Y"
                                                }
                                            })
                                            this.setState({
                                                dataSource:list
                                            })
                                            // console.log("==============",list)
                                        }}>
                                                <Text style={[styles.titleTextStyle,{color:"#CB4139"}]}>点击展开</Text>
                                        </TouchableOpacity>
                                    </View>
                                :
                                <View>
                                    <View style={[styles.titleViewStyle,]}>
                                        <Text style={styles.titleTextStyle}>附件：</Text>
                                    </View>
                                    <View style={[{flexDirection:'row',flexWrap:'wrap'}]}>
                                        {
                                            item.compressFileList.map((item,index) =>{
                                            return(
                                                <View style={[{ width: 120,height:150,marginLeft:10,marginBottom:10,display:'flex'}]}>

                                                <TouchableOpacity onPress={() => {
                                                    this.setState({
                                                        isShowImage:true,
                                                        pictureIndex:index
                                                    })
                                                }}>
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.compressFile) }} style={{ height: 150, width:120 }} />
                                                </TouchableOpacity>
                                                <Modal visible={this.state.isShowImage} transparent={true}>
                                                    <ImageViewer onClick={()=>{this.setState({isShowImage:false})}} index={this.state.pictureIndex}
                                                    enableSwipeDown menuContext={{ saveToLocal: '保存到本地', cancel: '取消' }}
                                                    onSwipeDown={() => {this.setState({isShowImage:false})}} imageUrls={this.state.urls}
                                                    onSave={()=>{
                                                        saveImage( this.state.urls[this.state.pictureIndex].url)
                                                    }}/>
                                                </Modal>
                                            </View>
                                            )
                                            })
                                        }
                                    </View>
                                    <View style={[[styles.titleViewStyle,{}],{justifyContent:'center'}]}>
                                        {
                                            item.pictureDisplay === "Y"?
                                            <TouchableOpacity onPress={() => {
                                                this.setState({
                                                    urls:[]
                                                })
                                                let list = this.state.dataSource;
                                                list.map((elem, index) => {
                                                    if(elem.askQuestionsId == item.askQuestionsId){
                                                        elem.pictureDisplay = "N"
                                                    }
                                                })
                                                this.setState({
                                                    dataSource:list
                                                })
                                                // console.log("==============",list)
                                            }}>
                                                    <Text style={[styles.titleTextStyle,{color:"#CB4139",textAlign:'center'}]}>点击收起</Text>
                                            </TouchableOpacity>
                                            :
                                            <View/>
                                        }
                                    </View>
                                </View>
                            }
                        </View>
                    ):
                    null
                }
                <View style={[styles.titleViewStyle,{}]}>
                    <Text style={styles.titleTextStyle}>提问人：{item.askQuestionsUserName}</Text>
                </View>
                {
                    item.askQuestionsState === '0BB' ?
                    <View style={[styles.titleViewStyle,{}]}>
                        <Text style={styles.titleTextStyle}>认领人：{item.askQuestionsToClaimUserName? item.askQuestionsToClaimUserName:"无"}</Text>
                    </View>
                    :
                    <View>
                    {
                        item.askQuestionsState === '0CC' ?
                        <View style={[styles.titleViewStyle,{}]}>
                            <Text style={styles.titleTextStyle}>解决人：{item.askQuestionsToClaimUserName? item.askQuestionsToClaimUserName:"无"}</Text>
                        </View>
                        :
                        <View/>
                    }
                    </View>
                }
                <View style={[styles.titleViewStyle,{}]}>
                    <Text style={styles.titleTextStyle}>提问时间：{item.gmtCreated}</Text>
                </View>
                {
                    item.askQuestionsState == "0CC" ?
                    <View style={[styles.titleViewStyle,{}]}>
                        <Text style={styles.titleTextStyle}>解决时间：{item.gmtModified}</Text>
                    </View>               
                    :
                    <View/>
                }
                <View style={[CommonStyle.itemBottomBtnStyle,{ flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                    <TouchableOpacity onPress={()=>{
                        this.props.navigation.navigate("AskQuestionsSolveTrackingList",{
                            "askQuestionsId":item.askQuestionsId,
                            "listTitleName":"解决进展",
                        })
                    }}>
                        <View style={[CommonStyle.itemBottomStudyGreyBtnViewStyle,{width:64}]}>
                            <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../../assets/icon/iconfont/progress.png')}></Image>
                            <Text style={[{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }]}>进展</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该提问吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteMyAskQuestions(item.askQuestionsId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteGreyBtnViewStyle, { width: 64 }]}>
                            <Image style={{ width: 24, height: 24, marginRight: 0.5 }} source={require('../../../assets/icon/iconfont/newDelete.png')}></Image>
                            <Text style={[{ color: 'rgba(145, 147, 152, 1)', fontSize: 14, lineHeight: 20 }]}>删除</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("MyAskQuestionsAdd", 
                            {
                                // 传递参数
                                askQuestionsId:item.askQuestionsId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[CommonStyle.itemBottomEditBlueBtnViewStyle, { width: 64 }]}>
                            <Image style={{ width: 17, height: 17, marginRight: 3 }} source={require('../../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={{ color: '#F0F0F0', fontSize: 14, lineHeight: 20 }}>编辑</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("MyAskQuestionsAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                {/* <Text style={CommonStyle.headRightText}>新增</Text> */}
                <Image style={{ width:27, height:27 }} source={require('../../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='我的提问'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />

                    <View style={[CommonStyle.headViewStyle, { borderLeftWidth: 0, borderRightWidth: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                    
                    <View style={{ 
                        width: '100%', flexWrap: 'wrap', flexDirection: 'row' ,
                        }}>
                    {
                        (this.state.askQuestionsState && this.state.askQuestionsState.length > 0) 
                        ? 
                        this.state.askQuestionsState.map((item, index)=>{
                            return this.renderAskQuestionsStateRow(item)
                        })
                        : <View/>
                    }  
                    </View>

                    <View style={[CommonStyle.searchBoxAndExport, {}]}>
                        <View style={CommonStyle.searchTimeBoxWithExport}>
                            <TouchableOpacity onPress={() => this.openQryStartDate()}>
                                <View style={{ alignItems: 'center', flexDirection: 'row', width: screenWidth / 1.9, borderRadius: 80 }}>
                                    <Image style={{ width: 16, height: 16, marginLeft: 7 }} source={require('../../../assets/icon/iconfont/search.png')}></Image>
                                    <Text style={{ color: 'rgba(rgba(0, 10, 32, 0.45))', fontSize: 14, marginLeft: 15 }}>
                                        {!this.state.qryStartTime ? "提交日期" : this.state.qryStartTime}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => this.resetQry()}>
                                <View style={[CommonStyle.resetBtnViewStyle, { width: 10, borderWidth: 0, backgroundColor: 'rgba(0,0,0,0)', borderRadius: 20 }]}>
                                    <Image style={{ width: 16, height: 16 }} source={require('../../../assets/icon/iconfont/replace.png')}></Image>
                                </View>
                            </TouchableOpacity>
                        </View>

                        <TouchableOpacity onPress={() => {
                            Alert.alert('确认', '您确定要将查询的日报导出为PDF文件吗？', [
                                {
                                    text: "取消", onPress: () => {
                                        WToast.show({ data: '点击了取消' });
                                    }
                                },
                                {
                                    text: "确定", onPress: () => {
                                        WToast.show({ data: '点击了确定' });
                                        this.exportPdfFile()
                                    }
                                }
                            ]);
                        }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle,
                            {
                                margin: 0,
                                alignItems: 'center',
                                width: 64,
                                backgroundColor: '#1E6EFA',
                                height: 32,
                                borderRadius: 20
                            }]}>
                                {/* <View style={[ CommonStyle.itemBottomDetailBtnViewStyle,{ width: 64, height: 32, backgroundColor: "#1E6EFA", flexDirection: "row", borderRadius: 20,alignItems: 'center' }]}> */}
                                {/* <Image style={{ width: 20, height: 20, marginRight: 5 }} source={require('../../assets/icon/iconfont/output.png')}></Image> */}
                                <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { fontSize: 14 }]}>导出</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </View> 
                
                <View style={[CommonStyle.contentViewStyle,{ height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
                <BottomScrollSelect 
                    ref={'SelectQryStartDate'} 
                    callBackDateValue={this.callBackSelectQryStartDateValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    innerHeadViewStyle:{
        borderColor:"#ffffff",
        borderWidth:4,
        backgroundColor:"#ffffff"
    },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:4,

        // borderColor:"red",
        // borderWidth:4
    },

    // 分段器样式
    blockItemViewStyle: {
        margin: 5,
        width: 60, 
        borderRadius: 0,
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
    },
    selectedBlockItemViewStyle: {
        margin: 5,
        width: 60, borderRadius: 0, 
        paddingTop: 2 ,paddingBottom:0,
        paddingLeft: 2, paddingRight: 2, 
        justifyContent: 'center',
        backgroundColor: "#FFFFFF", 
    },




    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16,
        marginLeft:15
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    inputRowStyle: {
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },
});