import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, TouchableOpacity, Dimensions, KeyboardAvoidingView, Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'

import CommonHeadScreen from '../../../component/CommonHeadScreen';
var CommonStyle = require('../../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
export default class CourseLevelMgrAdd extends Component {
    constructor() {
        super()
        this.state = {
            operate: "",
            courseLevelId: "",
            courseLevelName: "",
            courseLevelDesc: "",
            courseLevelSort: 0,
            noRightTip:"",
            courseLevelNameArrays: []
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { courseLevelId } = route.params;
            if (courseLevelId) {
                console.log("========Edit==courseLevelId:", courseLevelId);
                this.setState({
                    courseLevelId: courseLevelId,
                    operate: "编辑"
                })
                let loadTypeUrl = "/biz/course/level/get";
                let loadRequest = { 'courseLevelId': courseLevelId };
                httpPost(loadTypeUrl, loadRequest, this.loadCourseLevelDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
                this.loadCourseLevelList();
            }
        }
    }

    loadCourseLevelList = () => {
        let url = "/biz/course/level/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 10000,
        };
        httpPost(url, loadRequest, this.loadCourseLevelListCallBack);
    }

    loadCourseLevelListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var data = response.data.dataList;
            let arr = [];
            data.forEach((element) => {
                arr.push(element.courseLevelName);
            });
            console.log("@__courseLevelNameArrays__@", JSON.stringify(arr, null, 6));
            this.setState({
                courseLevelNameArrays: arr
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadCourseLevelDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                courseLevelId: response.data.courseLevelId,
                courseLevelName: response.data.courseLevelName,
                courseLevelDesc: response.data.courseLevelDesc,
                courseLevelSort: response.data.courseLevelSort,
                noRightTip:response.data.noRightTip,
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                <Image style={{ width: 22, height: 22 }} source={require('../../../assets/icon/iconfont/backnew.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    saveCourseLevel = () => {
        console.log("=======saveCourseLevel");
        let toastOpts;
        if (!this.state.courseLevelName) {
            toastOpts = getFailToastOpts("请填写职级类型名称");
            WToast.show(toastOpts)
            return;
        }

        if (this.state.courseLevelNameArrays) {
            for (let item of this.state.courseLevelNameArrays) {
                if (this.state.courseLevelName == item) {
                    toastOpts = getFailToastOpts("“" + this.state.courseLevelName + "”" + "已被使用，请换个名称重试");
                    WToast.show(toastOpts)
                    return;
                }
            }
        }
        let url = "/biz/course/level/add";
        if (this.state.courseLevelId) {
            console.log("=========Edit===courseLevelId", this.state.courseLevelId)
            url = "/biz/course/level/modify";
        }

        let requestParams = {
            courseLevelId: this.state.courseLevelId,
            courseLevelName: this.state.courseLevelName,
            courseLevelDesc: this.state.courseLevelDesc,
            courseLevelSort: this.state.courseLevelSort,
            noRightTip:this.state.noRightTip,
        };
        httpPost(url, requestParams, this.saveCourseLevelCallBack);
    }

    saveCourseLevelCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    render() {
        return (
            <View >
                <CommonHeadScreen title={this.state.operate + '职级'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{height:ifIphoneXContentViewDynamicHeight(45+10+10-2)}}>
                    <View style={{backgroundColor:"white",paddingBottom:10}}>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>                            
                            <Text style={styles.leftLabNameTextStyle}>职级名称</Text>
                        </View>
                        <TextInput 
                            maxLength={7}
                            style={[CommonStyle.inputTextStyleTextStyle, {borderWidth: 0,color: '#A0A0A0', fontSize: 15}]}
                            placeholder={'请填写职级名称'}
                            onChangeText={(text) => this.setState({ courseLevelName: text })}
                        >
                            {this.state.courseLevelName}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            <Text style={styles.leftLabNameTextStyle}>无权限提示</Text>
                        </View>
                    </View>
                    <View style={[styles.inputRowStyle, { height: 100 }]}>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText, { height: 100, borderWidth: 0 }]}
                            placeholder={'请输入无权限提示'}
                            onChangeText={(text) => this.setState({ noRightTip: text })}
                        >
                            {this.state.noRightTip}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>                            
                            <Text style={styles.leftLabNameTextStyle}>排序</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[CommonStyle.inputTextStyleTextStyle, {borderWidth: 0,color: '#A0A0A0', fontSize: 15}]}
                            placeholder={'请输入排序'}
                            onChangeText={(text) => this.setState({ courseLevelSort: text })}
                        >
                            {this.state.courseLevelSort}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}> </Text>                            
                            <Text style={styles.leftLabNameTextStyle}>备注</Text>
                        </View>
                        <TextInput 
                            style={[CommonStyle.inputTextStyleTextStyle, {borderWidth: 0,color: '#A0A0A0', fontSize: 15}]}
                            placeholder={'请填写备注'}
                            onChangeText={(text) => this.setState({ courseLevelDesc: text })}
                        >
                            {this.state.courseLevelDesc}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                </View>
                </View>

                <View style={[CommonStyle.btnRowStyle, {width: screenWidth, margin: 0,backgroundColor:"white"}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginBottom:15,marginLeft: 20, width: (screenWidth - 56)/2}]} >
                            {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveCourseLevel.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, {marginBottom:15,marginRight: 20, width: (screenWidth - 56)/2}]}>
                            {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>确认</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
}

let styles = StyleSheet.create({
    contentViewStyle: {
        height: screenHeight - 90,
    },
    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 30),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },

    titleTextStyle: {
        fontSize: 16
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    inputLineViewStyle: {
        height:1,
        marginLeft: 13,
        marginRight: 13,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
})
