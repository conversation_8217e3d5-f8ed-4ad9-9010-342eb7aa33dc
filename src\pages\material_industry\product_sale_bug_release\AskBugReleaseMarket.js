import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Linking,
    FlatList,RefreshControl,Image,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import ProductEmptyRowViewComponent from '../../../component/ProductEmptyRowViewComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
var CommonStyle = require('../../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class AskBugReleaseMarket extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            searchKeyWord:"",
            productTypeList:[
                {
                    "typeId":0,
                    "typeName":"耐火制品",
                    "typeCode":"P"
                },
                {
                    "typeId":1,
                    "typeName":"耐火原料",
                    "typeCode":"M"
                }
            ],
            productType:"P",
        }
    }

    getNowTime(){
        var date = new Date();
        //获取当前时间的毫秒数
        var nowMilliSeconds = date.getTime();
        // 用获取毫秒数 加上30天的毫秒数 赋值给SevenDaysLast对象（一天有86400000毫秒）
        var nowTime = new Date(nowMilliSeconds + ( 8 * 3600000));
        //年 getFullYear()：四位数字返回年份
        var year = nowTime.getFullYear();  //getFullYear()代替getYear()
        //月 getMonth()：0 ~ 11
        var month = nowTime.getMonth() + 1;
        //日 getDate()：(1 ~ 31)
        var day = nowTime.getDate();
        //时 getHours()：(0 ~ 23)
        var hour = nowTime.getHours();
        //分 getMinutes()： (0 ~ 59)
        var minute = nowTime.getMinutes();
        //秒 getSeconds()：(0 ~ 59)
        var second = nowTime.getSeconds();
    
        var time =  year + '-' + this.addZero(month) + '-' + this.addZero(day) + ' ' + this.addZero(hour) + ':' + this.addZero(minute) + ':' + this.addZero(second);
        return time;
    }
        
    addZero(s){
        return s < 10 ? ('0'+s):s;
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        this.loadAskBugReleaseList()
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": "P",
            "searchKeyWord":this.state.searchKeyWord,
            "productType":this.state.productType,
            "auditState":"3",
            "expDate":this.getNowTime()
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": "P",
            "searchKeyWord":this.state.searchKeyWord,
            "productType":this.state.productType,
            "auditState":"3",
            "expDate":this.getNowTime()
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadAskBugReleaseList();
    }

    loadAskBugReleaseList=()=>{
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "releaseType": "P",
            "searchKeyWord":this.state.searchKeyWord,
            "productType":this.state.productType,
            "auditState":"3",
            "expDate":this.getNowTime()
        };
        httpPost(url, loadRequest, this.loadAskBugReleaseListCallBack);
    }


    loadAskBugReleaseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            console.log(dataNew)
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/product/release/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": "P",
            "searchKeyWord": this.state.searchKeyWord,
            "productType":this.state.productType,
            "auditState":"3",
            "expDate":this.getNowTime()
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.releaseId} style={styles.innerViewStyle}>
                <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                    <Text style={styles.titleTextStyle}>{item.productName}</Text>
                </View>
                <View style={{position:'absolute',right:-10,top:10}}>
                    {
                        item.auditMarking == '1' ?
                        <View style={{position:'absolute',right:0,top:0}}>
                            <Image style={{width:85,height:25}} source={require('../../../assets/image/recommend.png')}/>
                        </View>
                        :
                        (
                            item.auditMarking == '2' ?
                            <View style={{position:'absolute',right:0,top:0}}>
                                <Image style={{width:50,height:23}} source={require('../../../assets/image/selfSupport.png')}/>
                            </View>
                            :
                            null
                        )
                    }
                    
                </View>
                
                <View style={{flexDirection:'row',display:'flex'}}>

                    <View style={{flexDirection:'column',marginTop:10}}>
                        <View style={{flexDirection:'row',marginBottom:5}}>
                            {
                                item.productModel ? 
                                <Text style={styles.blackTextStyle}>型号：{item.productModel}</Text>
                                : <View/>
                            }
                            {/* <Text style={[item.productType == 'P' ?  [styles.blackTextStyle,{marginLeft:30}] :  [styles.blackTextStyle,{marginLeft:0}]]}>数量：{item.productWeight}吨</Text> */}
                        </View>
                        {
                            item.productMaterial ?
                            <View style={{marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>材质：{item.productMaterial}</Text>
                            </View>
                            : <View/>
                        }
                        <View style={{flexDirection:'row',marginBottom:5}}>
                            <Text style={[styles.blackTextStyle,{marginLeft:0}]}>数量：{item.productWeight}吨</Text>
                        </View>
                        <View>
                            <Text style={styles.blackTextStyle}>期望产地：{item.productionAddr}</Text>
                        </View>

                    </View>

                    

                    <View style={[item.productType=='P' ? {position:'absolute',right:10,alignItems:'center',top:26} : {position:'absolute',right:10,alignItems:'center',top:15}]  }>
                        <TouchableOpacity  onPress={() => {
                            let phone = item.salePersonTel;
                            if(phone == null){
                                WToast.show({data:'暂未添加联系人电话，请联系管理员！'});
                                return;
                            }
                            const url = `tel:${phone}`;
                            Linking.canOpenURL(url)
                            .then(supported => {
                                if (!supported) {
                                return Alert.alert('提示', `您的设备不支持该功能，请手动拨打 ${phone}`, [
                                    { text: '确定' }
                                ]);
                                }
                                return Linking.openURL(url);
                            })
                            .catch(err => WToast.show({data:`出错了：${err}`}));
                        }}>
                            <Image  style={{width:40, height:40}} source={require('../../../assets/icon/iconfont/tel.png')}></Image>
                        </TouchableOpacity>
                    </View>
                
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.blackTextStyle}>说明：{item.productExplain?item.productExplain:"无"}</Text>
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.blackTextStyle}>发布日期：{item.releaseDate}</Text>
                </View>

                {/* <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>发布企业：{item.tenantAbbreviation ? item.tenantAbbreviation : item.tenantName}</Text>
                </View> */}

                {/* <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>联系人：{item.contactPerson}</Text>
                </View>

                <View style={{marginTop:5}}>
                    <Text style={styles.greyTextStyle}>联系电话：{item.contactTel}</Text>
                </View> */}

            </View>
        )
    }

    productTypeRow=(item, index)=>{
        return (
            <View key={item.typeId} >
                <TouchableOpacity onPress={()=>{
                    var typeCode = item.typeCode;
                    this.setState({
                        productType:typeCode
                    })

                    let url= "/biz/product/release/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "releaseType": "P",
                        "searchKeyWord":this.state.searchKeyWord,
                        "productType":typeCode,
                        "auditState":"3",
                        "expDate":this.getNowTime()
                    };
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.typeCode} style={[item.typeCode===this.state.productType? [CommonStyle.selectedBlockItemViewStyle,{backgroundColor:'#FFF',borderBottomWidth:2,borderBottomColor:'#FC783D',borderRadius:0,paddingBottom:0,marginBottom:0}] : CommonStyle.blockItemViewStyle,{paddingLeft:8,backgroundColor:'#FFF',paddingBottom:0,marginBottom:0.8}]}>
                        <Text style={[item.typeCode===this.state.productType? styles.selectedBlockItemTextStyle : styles.blockItemTextStyle]}>
                            {item.typeName}
                        </Text>
                    </View>
                </TouchableOpacity>
        </View>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent(height) {
        return <ProductEmptyRowViewComponent height={height} />
    }

    render(){
        return(
            <View style={{backgroundColor:'#FFF'}}>
            <CommonHeadScreen title='求购查询'
                leftItem={() => this.renderLeftItem()}
                rightItem={() => this.renderRightItem()}
            />
            

            <View style={[{marginTop:0,backgroundColor:'#FFF'}]} onLayout={this.topBlockLayout.bind(this)}>

            <View style={{ marginTop:5,backgroundColor:'#FFF',marginLeft:10}}>
                <Image  style={[{height:100 ,borderRadius:10,width:screenWidth - 20}]} source={require('../../../assets/image/askForPurchaseBanner.jpg')} />                                
            </View>

                <View style={{ marginTop: 5,marginBottom:5, index: 1000, flexWrap: 'wrap', flexDirection: 'row' ,borderBottomColor:'#33333333',borderBottomWidth:1,alignItems:'flex-end'}}>
                    {
                        (this.state.productTypeList && this.state.productTypeList.length > 0)
                            ?
                            this.state.productTypeList.map((item, index) => {
                                return this.productTypeRow(item)
                            })
                            : <View />
                    }
                </View>

                    <View style={{borderColor:'#33333333',borderWidth:1,height:40,borderRadius:5,marginTop:10,width:screenWidth -40,marginLeft:20,marginTop:5}}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Image  style={{width:25, height:25}} source={require('../../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'搜索名称/型号/材质/产地'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>

                </View>
            <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                <FlatList 
                    data={this.state.dataSource}
                    renderItem={({item,index}) => this.renderRow(item, index)}
                    ListEmptyComponent={this.emptyComponent(ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight))}
                    // 自定义下拉刷新
                    refreshControl={
                        <RefreshControl
                        tintColor="#FF0000"
                        title="loading"
                        colors={['#FF0000', '#00FF00', '#0000FF']}
                        progressBackgroundColor="#FFFF00"
                        refreshing={this.state.refreshing}
                        onRefresh={()=>{
                            this._loadFreshData()
                        }}
                        />
                    }
                    // 底部加载
                    ListFooterComponent={()=>this.flatListFooterComponent()}
                    onEndReached={()=>this._loadNextData()}
                    />

            </View>
        </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        paddingLeft: 5,
        height: 35,
        flexDirection: 'row',
        backgroundColor:"#FFFFFF",
        alignContent:'center'
    },
    titleTextStyle: {
        fontSize: 18,
        color:'#333333',
        backgroundColor:'#FFF',
        fontWeight:'bold',
        marginLeft:0,
    },
    greyTextStyle: {
        fontSize:14,
        color:'#33333399',
        backgroundColor:'#FFF',
    },
    blackTextStyle: {
        fontSize:15,
        color:'#333333',
    },
    redTextStyle: {
        fontSize:16,
        color:'#E41F00',
        fontWeight:'bold',
    },
    leftLabView: {
        paddingLeft: 10,
        alignItems:'center',
        marginTop:5
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth -100,
        color: '#33333399',
        fontSize: 16,
        marginLeft: 10,
        paddingBottom: 0,
        paddingTop:4
    },
    innerViewStyle:{
        width:screenWidth - 20,
        marginLeft:10,
        paddingLeft:10,
        paddingTop:10,
        marginTop:10,
        backgroundColor:'#FFF',
        borderBottomColor:'#33333315',
        borderBottomWidth:0.8,
        paddingBottom:10
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    selectedBlockItemTextStyle: {
        fontSize:19,
        color:'#FC783D',
        marginBottom:5
    },
    blockItemTextStyle: {
        fontSize:17,
        color:'#33333399',
        marginBottom:5
    }

});