import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,Image,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CrHeadScreen from '../../component/CrHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import _ from 'lodash'
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
export default class NewItem extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,  
            topBlockLayoutHeight: 0,          
            
            selExtList:[],
            extDataSource:[],
            extTitle0:"",
            extContent0:"",
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
            console.log("==========" + constants.loginUser.tenantName);
            console.log("==========" + constants.loginUser.staffId);
            var staffId = constants.loginUser.staffId;

            let url= "/biz/cr/staff/get";
            let loadRequest={"staffId":staffId};
             httpPost(url, loadRequest, this.loadNewItemCallBack);
             this.loadAdjustFactor();
        }
    }

    loadNewItemCallBack = (response) => {
        if (response.code == 200 && response.data) {

            console.log("==========" + response.data.staffName);
            this.setState({                
                extDataSource:response.data.crStaffExtDTOList,               
            })

            if (response.data.crStaffExtDTOList && response.data.crStaffExtDTOList.length > 0) {
                // 遍历拓展字段详情
                response.data.crStaffExtDTOList.forEach((item)=>{
                    var varExtCause={
                        extId:item.extId,
                        extTitle:item.extTitle,
                        extContent:item.extContent,
                    };
                    this.setState({
                       selExtList :this.state.selExtList.concat(varExtCause)
                    })
                })
            }
            console.log(this.state.selExtList)

        }
    }

    loadAdjustFactor=()=>{
        let url = "/biz/tenant/get";
        let loadRequest = {
            "operateTenantId":constants.loginUser.tenantId
        };
        httpPost(url, loadRequest, this.loadAdjustFactorCallBack);
    }

    loadAdjustFactorCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                adjustFactor: response.data.adjustFactor,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:24, height:24}} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("TemplateMgrAdd", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}></Text>
            </TouchableOpacity>
        )
    }

    saveMyInterView=()=>{
        console.log("=======saveMyInterView");
        let toastOpts;
        var ex = 0;
        let url= "/biz/cr/staff/modify";
        let _spExtDTOList = [];
        this.state.selExtList.map((elem, index)=>{
            var extDTO = {
                // "extId": elem.extId,
                "extTitle": elem.extTitle,
                "extContent":elem.extContent,
            }
            if (!elem.extTitle) {
                toastOpts = getFailToastOpts("请输入项目名称");
                WToast.show(toastOpts)
                ex = 1;
                return;
            }
            if (!elem.extContent) {
                toastOpts = getFailToastOpts("请输入项目内容");
                WToast.show(toastOpts)
                ex = 1;
                return;
            }
            _spExtDTOList.push(extDTO);
        })
        if (ex ==1 ) {
            return;
        }
        let requestParams={
            "staffId":constants.loginUser.staffId,
            "crStaffExtDTOList": _spExtDTOList,
            "userId":constants.loginUser.userId,
            "ext":'Y',
        };
        console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.saveMyInterViewCallBack);
    }

    saveMyInterViewCallBack=(response) =>{
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('更新完成');
                WToast.show(toastOpts)
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
        }
    }
    render(){
        // 动态显示拓展字段数据
        var pages = [];
        for (var i = 0; i < this.state.selExtList.length; i++) {
            const _selExtList = _.cloneDeep(this.state.selExtList);
            _selExtList.map((elem, index)=>{
                elem._index = i;
                return elem;
            })
            pages.push(        
                <View key={"view_" +this.state.selExtList[i].extId+"_"+i}>

                    {/* <View style={[CommonStyle.rowLabView,{flexDirection:"column",height:450}]}> */}


                        <View style={styles.titleViewStyle}>
                            <Text style={[styles.titleTextStyle]}>项目名称</Text>
                            <TouchableOpacity
                                style={{marginLeft:-145,zIndex:1000,marginTop:10}}
                                onPress={() => {
                                    Alert.alert('确认','您确定要删除该项目吗？',[
                                        {
                                            text:"取消", onPress:()=>{
                                            WToast.show({data:'点击了取消'});
                                            // this在这里可用，传到方法里还有问题
                                            // this.props.navigation.goBack();
                                            }
                                        },
                                        {
                                            text:"确定", onPress:()=>{
                                                WToast.show({data:'点击了确定' + 1});
                                                console.log("i++++++",i)
                                                console.log("extid",_selExtList[i - 1].extId);
                                                var urls = _selExtList;
                                                var index = _selExtList[i - 1].extId;
                                                urls.splice(index -1 , 1);  
                                                _selExtList = urls;                 
                                                console.log(urls);
                                                this.setState({
                                                    selExtList:_selExtList,
                                                    // _selExtList:urls,
                                                })
                                            }
                                        }
                                    ]);
                            }}>
                
                            <View style={styles.btnDeleteView}>
                                <Image style={{width:24, height:24}} source={require('../../assets/icon/iconfont/crDelete.png')}></Image>
                            </View>    
                            </TouchableOpacity>   
                        </View>
                        <View style={[{height:40},{marginTop:5,backgroundColor:'#FFFFFF',width:screenWidth - 40}]}>
                            <TextInput 
                                multiline={true}
                                textAlignVertical="top"
                                placeholder={'请输入项目名称'}
                                placeholderTextColor={'#000000D9'}
                                style={[styles.inputRowText1,{height:50}]}
                                extId={this.state.selExtList[i].extId}
                                onChange={(event) => {
                                    // 通过回调事件查看控件属性
                                    // var orderId = event.target._internalFiberInstanceHandleDEV.memoizedProps.orderId;
                                    var extId = event._dispatchInstances.memoizedProps.extId;
                                    var text = event.nativeEvent.text;
                                    var varselExt;
                                    for(var index=0; index<this.state.selExtList.length;index++){
                                        varselExt = this.state.selExtList[index];
                                        console.log(this.state.selExtList);
                                        
                                        if (extId === varselExt.extId) {
                                            varselExt.extTitle = text;
                                            this.state.selExtList[index] = varselExt;
                                            console.log("==数据更新==this.state.selExtList", this.state.selExtList);
                                        }
                                    }
                                }}
                            >
                                {this.state.selExtList[i].extTitle}
                            </TextInput>
                            </View>


                        <View style={styles.titleViewStyle}>
                            <Text style={[styles.titleTextStyle]}>项目内容</Text>
                        </View>
                        <View  style={[{height:150},{marginTop:5,backgroundColor:'#FFFFFF',width:screenWidth - 40}]}>
                            <TextInput 
                                multiline={true}
                                textAlignVertical="top"
                                placeholder={'请输入项目内容'}
                                placeholderTextColor={'#000000D9'}
                                style={[styles.inputRowText1,{height:150}]}
                                extId={this.state.selExtList[i].extId}
                                onChange={(event) => {
                                    // 通过回调事件查看控件属性
                                    // var orderId = event.target._internalFiberInstanceHandleDEV.memoizedProps.orderId;
                                    var extId = event._dispatchInstances.memoizedProps.extId;
                                    var text = event.nativeEvent.text;
                                    var varselExt;
                                    for(var index=0; index<this.state.selExtList.length;index++){
                                        varselExt = this.state.selExtList[index];
                                        if (extId === varselExt.extId) {
                                            varselExt.extContent = text;
                                            this.state.selExtList[index] = varselExt;
                                            console.log("==数据更新==this.state.selExtList", this.state.selExtList);
                                        }
                                    }
                                }}
                            >
                                {this.state.selExtList[i].extContent}
                            </TextInput>
                        </View>

                        
                    {/* </View> */}
                </View>
            );
        }

        return(
            <View>
                <CrHeadScreen title='新增项目'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={[CommonStyle.contentViewStyle]}>
                    
                    {/* {
                        this.state.extDataSource.length == 0 
                        ?
                        <View>
                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle]}>项目名称</Text>   
                            </View>
                            <View style={[{height:40},{marginTop:5,backgroundColor:'#FFFFFF',width:screenWidth - 40}]}>
                                <TextInput 
                                    multiline={true}
                                    textAlignVertical="top"
                                    placeholder={'请输入项目名称'}
                                    placeholderTextColor={'#333333'}
                                    style={[styles.inputRowText1,{height:50}]}
                                    extId={0}
                                    onChange={(event) => {
                                        // 通过回调事件查看控件属性
                                        // var orderId = event.target._internalFiberInstanceHandleDEV.memoizedProps.orderId;
                                        var extId = event._dispatchInstances.memoizedProps.extId;
                                        var text = event.nativeEvent.text;
                                        var varselExt;
                                        for(var index=0; index<this.state.selExtList.length;index++){
                                            varselExt = this.state.selExtList[index];
                                            console.log(this.state.selExtList);
                                            
                                            if (extId === varselExt.extId) {
                                                varselExt.extTitle = text;
                                                this.state.selExtList[index] = varselExt;
                                                console.log("==数据更新==this.state.selExtList", this.state.selExtList);
                                            }
                                        }
                                    }}
                                >
                                    {extTitle0}
                                </TextInput>
                            </View>


                            <View style={styles.titleViewStyle}>
                                <Text style={[styles.titleTextStyle]}>项目内容</Text>
                            </View>
                            <View  style={[{height:150},{marginTop:5,backgroundColor:'#FFFFFF',width:screenWidth - 40}]}>
                                <TextInput 
                                    multiline={true}
                                    textAlignVertical="top"
                                    placeholder={'请输入项目内容'}
                                    placeholderTextColor={'#333333'}
                                    style={[styles.inputRowText1,{height:150}]}
                                    extId={0}
                                    onChange={(event) => {
                                        // 通过回调事件查看控件属性
                                        // var orderId = event.target._internalFiberInstanceHandleDEV.memoizedProps.orderId;
                                        var extId = event._dispatchInstances.memoizedProps.extId;
                                        var text = event.nativeEvent.text;
                                        var varselExt;
                                        for(var index=0; index<this.state.selExtList.length;index++){
                                            varselExt = this.state.selExtList[index];
                                            if (extId === varselExt.extId) {
                                                varselExt.extContent = text;
                                                this.state.selExtList[index] = varselExt;
                                                console.log("==数据更新==this.state.selExtList", this.state.selExtList);
                                            }
                                        }
                                    }}
                                >
                                    {extContent0}
                                </TextInput>
                            </View>
                        </View>
                        : <View/>

                    } */}

                    <View>
                        {
                            pages.map((elem, index) => {
                                return elem;
                            })
                        }
                    </View>
                        

                    <View style={styles.btnRowView}>
                        <TouchableOpacity onPress={()=>{
                                console.log("==========this.state.selExtList.length:", this.state.selExtList.length);
                                if (this.state.selExtList.length >= 0) {
                                    var selIndex = this.state.selExtList.length;
                                    var varExtType={
                                        index:selIndex,
                                        extId:this.state.selExtList.length == 0 ?1:this.state.selExtList[selIndex - 1].extId + 1,
                                        extTitle:"",
                                        extContent:""
                                    };
                                    this.setState({
                                        selExtList:this.state.selExtList.concat(varExtType)
                                    })
                                    console.log("======selExtList:", this.state.selExtList.concat(varExtType))
                                }
                        }}>
                            <View style={styles.btnAddView}>
                                <Text style={[styles.btnAddText,{ flexDirection: 'row',width: 280, height: 40, marginRight: 20, marginLeft : 20, marginTop: 15 }]}>+新增项目</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={CommonStyle.btnRowStyle,{alignItems:'center',justifyContent:'center'}}>
                        <TouchableOpacity onPress={this.saveMyInterView.bind(this)}>
                            <View style={[styles.btnRowRightSaveBtnView1, { flexDirection: 'row',width: 343, height: 40, marginRight: 35, marginLeft : 35, marginTop: 15 }] }>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({

    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    titleTextStyle:{
        fontSize:14,
        marginTop:15,
        color:'#00000073',
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:20,
        marginRight:10,
        marginTop:20,
        marginBottom:5,
    },
    inputRowStyle: {
        height: 45,
        // flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputTextStyleTextStyle: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        height: 45,
        justifyContent: 'center'
    },
    inputTextStyleTextStyleAutoHeight: {
        width: screenWidth - (leftLabWidth + 15),
        borderRadius: 0,
        borderColor: 'red',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10,
        justifyContent: 'center'
    },
    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:33
    },
    btnAddView:{
        borderColor:'#02A7F0', justifyContent:'center', height:35, paddingLeft:10,borderWidth:1,paddingRight:10, justifyContent:'center',borderRadius:30
    },
    btnAddText:{
        color:'#02A7F0', fontSize:18, textAlign:'center' ,justifyContent:'space-between'
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },
    btnRowRightSaveBtnView1:{
        backgroundColor:'#3366FF',
        alignItems:'center',
        // alignContent:'center',
        justifyContent:'center',
        borderRadius:30,
        flexDirection:'row',
        width:130,height:40,
        marginRight:35,
        marginTop:15
    },
    inputRowText1:{
        width:screenWidth - 40,
        borderRadius:5,
        borderBottomColor:'#EEEEEE',
        borderBottomWidth:1,
        marginRight:5,
        color:'#000000D9',
        fontSize:18,
        marginLeft:20
    },

});