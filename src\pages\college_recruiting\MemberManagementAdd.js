import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,KeyboardAvoidingView,
    FlatList, RefreshControl, ScrollView, TextInput,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import { uploadImageLibrary } from '../../utils/UploadImageUtils';
const leftLabWidth = 130;
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

export default class MemberManagementAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate: "",
            staffId: "",
            staffName:  "",
            staffTel:  "",
            staffSort:  "",
            className:  "",
            selectedClass: [],
            address: "",
            graduateInstitutions: "",
            electronicPhotos: "",
            personalHonor:  "",
            collegeEvaluation:  "",
            enterpriseDemand:"",
            mainProducts:"",
            professionalName:"",
            nativePlace:"",
            portraitName:"",
            portraitIdListNew:[],
            portraitIdList:[],
            portraitId:"",

            userPhotoUrl:"",
            userPhoto:"",

            staffPosition:""
        }
    }


    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        // 加载班级列表
        this.loadClassList();
  

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { staffId } = route.params;
            this.loadPortraitList(staffId);
            if (staffId) {
                // console.log("=============courseId" + courseId + "");
                this.setState({
                    staffId: staffId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/cr/member/get";
                loadRequest = { 'staffId': staffId,staffType:"M",sign:true };
                httpPost(loadTypeUrl, loadRequest, this.loadMemberCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
        }
    }

        // 通过shiftId获取员工列表
    loadPortraitList=(staffId)=>{
        let loadUrl= "/biz/member/portrait/list";
        let loadRequest={'currentPage':1,'pageSize':50};
        console.log("==========loadRequest", loadRequest)
        httpPost(loadUrl, loadRequest, this.loadPortraitListCallBack);
    }

    loadPortraitListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                portraitDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    compare=(portraitTypeId)=>{
        for(var i=0;i<this.state.portraitIdListNew.length;i++){
            if(this.state.portraitIdListNew[i] == portraitTypeId){
                return true;
            }
        }
        return false;
    }

      // 画像单项渲染
      portraitRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                var portraitIdList = this.state.portraitIdListNew;
                if (this.compare(item.portraitTypeId)) {
                    arrayRemoveItem(portraitIdList, item.portraitTypeId);
                }
                else {
                    portraitIdList = portraitIdList.concat(item.portraitTypeId)
                }
                this.setState({
                    portraitIdListNew:portraitIdList,
                })
                WToast.show({data:'点击了' + item.portraitTypeName});
                console.log("======portraitIdList:", portraitIdList)
            }}>
                <View key={item.portraitTypeId} style={this.compare(item.portraitTypeId) ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={this.compare(item.portraitTypeId) ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.portraitTypeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    loadMemberCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                staffId: response.data.staffId,
                staffName: response.data.staffName,
                staffTel: response.data.staffTel,
                staffSort: response.data.staffSort,
                classId:response.data.classId,
                className: response.data.className,
                selectedClass: [response.data.className],   
                address:response.data.address,
                graduateInstitutions:response.data.graduateInstitutions,
                electronicPhotos:response.data.electronicPhotos,
                personalHonor: response.data.personalHonor,
                collegeEvaluation: response.data.collegeEvaluation,
                professionalName:response.data.professionalName,
                mainProducts:response.data.mainProducts,
                enterpriseDemand:response.data.enterpriseDemand,
                nativePlace:response.data.nativePlace,
                portraitIdListNew:response.data.portraitIdList ? response.data.portraitIdList:[],
                userPhoto : response.data.electronicPhotos,
                userPhotoUrl: constants.image_addr + '/' +  response.data.electronicPhotos,
                staffPosition:response.data.staffPosition,

            })

            var list = [];
            if(response.data.portraitIdList){
                
                var portraitIdListNew = response.data.portraitIdList.split(",")
                console.log("====portraitIdList===="+portraitIdListNew)
                for(var i=0; i< portraitIdListNew.length; i++){
                  list =  list.concat(portraitIdListNew[i])
                }
                console.log(list)
                this.setState({
                    portraitIdListNew:list
                })
            }
        }

    }

    loadClassList = () => {
        let url = "/biz/college/class/grades/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadClassListCallBack);
    }

    loadClassListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                classDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                 <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("MemberManagementList",
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction
                    })
            }}>
                <Text style={CommonStyle.headRightText}>会员管理</Text>
            </TouchableOpacity>
        )
    }

    saveStaff = () => {
        console.log("=======editMember1");
        let toastOpts;
        if (!this.state.staffName) {
            toastOpts = getFailToastOpts("请输入会员姓名");
            WToast.show(toastOpts); // 显示提示信息
            return; // 中止保存操作
        }
        if (!this.state.staffTel) {
            toastOpts = getFailToastOpts("请输入联系电话");
            WToast.show(toastOpts); // 显示提示信息
            return;
        }
        if (!this.state.electronicPhotos) {
            // 如果形象照字段为空，显示错误消息或阻止提交
            // alert('请上传形象照');
            toastOpts = getFailToastOpts("请上传形象照");
            WToast.show(toastOpts); // 显示提示信息
            return;
        }
        if (!this.state.graduateInstitutions) {
            toastOpts = getFailToastOpts("请输入公司/组织");
            WToast.show(toastOpts); // 显示提示信息
            return; // 中止保存操作
        }
        if (!this.state.staffPosition) {
            toastOpts = getFailToastOpts("请输入职位");
            WToast.show(toastOpts); // 显示提示信息
            return; // 中止保存操作
        }
        if (!this.state.address) {
            toastOpts = getFailToastOpts("请输入所在城市");
            WToast.show(toastOpts); // 显示提示信息
            return; // 中止保存操作
        }
        if (!this.state.collegeEvaluation) {
            toastOpts = getFailToastOpts("请输入业务简介");
            WToast.show(toastOpts); // 显示提示信息
            return; // 中止保存操作
        }
        if (!this.state.personalHonor) {
            toastOpts = getFailToastOpts("请输入会员能提供的资源");
            WToast.show(toastOpts); // 显示提示信息
            return; // 中止保存操作
        }

        // if (!this.state.classId) {
        //     toastOpts = getFailToastOpts("请选择会员班级");
        //     return;
        // }


        console.log("===1===saveMember");
        let url = "";
        let requestParams = {};

        if (this.state.staffId) {
            console.log("=========Edit===memberId", this.state.staffId)
             url = "/biz/cr/member/modify";
            requestParams = {
                staffId: this.state.staffId,
                staffName: this.state.staffName,
                staffType:"M",
                staffTel: this.state.staffTel,
                address:this.state.address,
                nativePlace:this.state.nativePlace,
                graduateInstitutions:this.state.graduateInstitutions,
                staffSort:this.state.staffSort ? this.state.staffSort : 0,
                personalHonor: this.state.personalHonor,
                collegeEvaluation: this.state.collegeEvaluation,
                classId:this.state.classId,
                enterpriseDemand:this.state.enterpriseDemand,
                mainProducts:this.state.mainProducts,
                crProfessionalName:this.state.professionalName,
                nativePlace:this.state.nativePlace,
                portraitIdList:this.state.portraitIdListNew.toString(),
                electronicPhotos:this.state.electronicPhotos,
                staffPosition:this.state.staffPosition
            };
        }

        else {
            console.log("=========Add===member")
            url = "/biz/cr/member/add_by_admin";
            requestParams = {
                staffId: this.state.staffId,
                staffName: this.state.staffName,
                staffType:"M",
                staffTel: this.state.staffTel,
                address:this.state.address,
                nativePlace:this.state.nativePlace,
                graduateInstitutions:this.state.graduateInstitutions,
                staffSort:this.state.staffSort ? this.state.staffSort : 0,
                personalHonor: this.state.personalHonor,
                collegeEvaluation: this.state.collegeEvaluation,
                classId:this.state.classId,
                enterpriseDemand:this.state.enterpriseDemand,
                mainProducts:this.state.mainProducts,
                professionalName:this.state.professionalName,
                nativePlace:this.state.nativePlace,
                portraitIdList:this.state.portraitIdListNew.toString(),
                electronicPhotos:this.state.electronicPhotos,
                staffPosition:this.state.staffPosition,
            };
        }
       

        console.log("============requestParams", requestParams)
        httpPost(url, requestParams, this.saveStaffCallBack);
    }

    // 保存回调函数
    saveStaffCallBack = (response) => {
        let toastOpts;
        if (!this.state.staffName) {
            toastOpts = getFailToastOpts("请输入会员姓名");
            WToast.show(toastOpts); // 显示提示信息
            return; // 中止保存操作
        }
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    openClass() {
        if (!this.state.classDataSource || this.state.classDataSource.length < 1) {
            WToast.show({data:"请先添加班级"});
            return
        }
        this.refs.SelectClass.showClass(this.state.selectedClass, this.state.classDataSource);
    }

    callBackSelectClassValue(value) {
        console.log("==========班级选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedClass: value
        })
        var className = value.toString();
        this.setState({
            className: className
        })
        let loadUrl = "/biz/college/class/grades/getClassByName";
        let loadRequest = {
            "className": className
        };
        httpPost(loadUrl, loadRequest, (response) => {
            if (response.code == 200 && response.data) {
                this.setState({
                    roleId: response.data.roleId,
                    classId: response.data.classId,
                })
            }
            else if (response.code == 401) {
                WToast.show({ data: response.message });
                this.props.navigation.navigate("LoginView");
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }

    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '会员'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                    <ScrollView style={[CommonStyle.formContentViewStyle]}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>会员姓名</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
{/*                             <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入会员姓名'}
                                onChangeText={(text) => this.setState({ staffName: text })}
                            >
                                {this.state.staffName}
                            </TextInput> */}
                            <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入会员姓名'}
                                onChangeText={(text) => this.setState({ staffName: text })}
                                // required={true} //改动 添加此行将姓名字段设置为必填字段
                            >
                                {this.state.staffName}
                            </TextInput>

                        </View>
                        <View style={styles.inputLineViewStyle}/>

                        <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            keyboardType='numeric'
                            placeholder={'请输入会员联系电话'}
                            onChangeText={(text) => this.setState({ staffTel: text })}
                            // required={true} //改动 添加此行将姓名字段设置为必填字段
                        >
                            {this.state.staffTel}
                        </TextInput>
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                        <View style={[styles.inputRowStyle,{height:150}]}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>形象照</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                            <View style={[{ width: 120,height:150,marginLeft:0,marginBottom:10,display:'flex',justifyContent:'center',
                                alignItems:'center'},{borderColor:'#AAAAAA' ,borderWidth:1,borderStyle:'dashed',borderRadius:5}]}>
                                <TouchableOpacity 
                                    onPress={() => {
                                        uploadImageLibrary(this.state.userPhotoUrl, "user_header", (imageUploadResponse) => {
                                            console.log("========imageUploadResponse", imageUploadResponse)
                                            if (imageUploadResponse.code === 200) {
                                                WToast.show({ data: "成功上传" });
                                                let { compressFile } = imageUploadResponse.data
                                                this.setState({
                                                    //goodsImageUrl:服务器地址+图片存储地址
                                                    userPhotoUrl: constants.image_addr + '/' + compressFile,
                                                    electronicPhotos:compressFile,
                                                })
                                            }
                                            else {
                                                WToast.show({ data: imageUploadResponse.message });
                                            }
                                        });
                                }}>
                                    {
                                        this.state.electronicPhotos ?
                                        <Image source={{ uri: this.state.userPhotoUrl }} style={{width:120,height:150,justifyContent:'center',alignItems:'center'}} />
                                        :
                                        <Image source ={require('../../assets/icon/iconfont/addPhoto.png')} style ={{width:24,height:24}}></Image>
                                    }
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={[styles.inputLineViewStyle, {marginTop: 5}]}/>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>公司/组织</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入公司/组织'}
                                onChangeText={(text) => this.setState({ graduateInstitutions: text })}
                            >
                                {this.state.graduateInstitutions}
                            </TextInput>
                        </View>
                        <View style={styles.inputLineViewStyle}/>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>职位</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入公司职位'}
                                onChangeText={(text) => this.setState({ staffPosition: text })}
                            >
                                {this.state.staffPosition}
                            </TextInput>
                        </View>
                        <View style={styles.inputLineViewStyle}/>


                        {/* <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>所属班级</Text>
                            </View>
                            <TouchableOpacity onPress={() => this.openClass()}>
                                <View style={CommonStyle.inputTextStyleTextStyle}>
                                    <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                        {!this.state.className ? "请选择所属班级" : this.state.className}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View> */}

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>所在城市</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入会员所在城市'}
                                onChangeText={(text) => this.setState({ address: text })}
                            >
                                {this.state.address}
                            </TextInput>
                        </View>
                        <View style={styles.inputLineViewStyle}/>
{/* 
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>会员家乡</Text>
                            </View>
                            <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入会员的家乡'}
                                onChangeText={(text) => this.setState({ nativePlace: text })}
                            >
                                {this.state.nativePlace}
                            </TextInput>
                        </View> */}

                        {/* <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>公司</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入公司名称'}
                                onChangeText={(text) => this.setState({ graduateInstitutions: text })}
                            >
                                {this.state.graduateInstitutions}
                            </TextInput>
                        </View>
                        <View style={styles.inputLineViewStyle}/> */}

                        

                        {/* <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>项目名称</Text>
                            </View>
                            <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入项目名称'}
                                onChangeText={(text) => this.setState({ professionalName: text })}
                            >
                                {this.state.professionalName}
                            </TextInput>
                        </View> */}

                            <View style={[styles.inputRowStyle]}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>所属行业</Text>
                                </View>
                            </View>
                            <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                                {
                                    (this.state.portraitDataSource && this.state.portraitDataSource.length > 0) 
                                    ? 
                                    this.state.portraitDataSource.map((item, index)=>{
                                        return this.portraitRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                        
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>业务简介</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle, { height: 150 }]}>
                            <TextInput
                                multiline={true}
                                textAlignVertical="top"
                                style={[CommonStyle.inputRowText, { height: 150, borderWidth: 0 }]}
                                placeholder={'请输入业务简介'}
                                onChangeText={(text) => this.setState({ collegeEvaluation: text })}
                            >
                                {this.state.collegeEvaluation}
                            </TextInput>
                        </View>
                        <View style={styles.inputLineViewStyle}/>
                        {/* <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>主要产品</Text>
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle, { height: 150 }]}>
                            <TextInput
                                multiline={true}
                                textAlignVertical="top"
                                style={[CommonStyle.inputRowText, { height: 150 }]}
                                placeholder={'请输入主要产品'}
                                onChangeText={(text) => this.setState({ mainProducts: text })}
                            >
                                {this.state.mainProducts}
                            </TextInput>
                        </View> */}
                        <View style={styles.inputRowStyle}>
                            <View style={[styles.leftLabView]}>
                                <Text style={styles.leftLabNameTextStyle}>会员能提供的资源</Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle, { height: 150 }]}>
                            <TextInput
                                multiline={true}
                                textAlignVertical="top"
                                style={[CommonStyle.inputRowText, { height: 150, borderWidth: 0 }]}
                                placeholder={'请输入会员能提供的资源'}
                                onChangeText={(text) => this.setState({ personalHonor: text })}
                            >
                                {this.state.personalHonor}
                            </TextInput>
                        </View>
                        <View style={styles.inputLineViewStyle}/>

                        <View style={styles.inputRowStyle}>
                            <View style={[styles.leftLabView]}>
                                <Text style={styles.leftLabNameTextStyle}>期望对接的资源</Text>
                            </View>
                        </View>
                        <View style={[styles.inputRowStyle, { height: 150 }]}>
                            <TextInput
                                multiline={true}
                                textAlignVertical="top"
                                style={[CommonStyle.inputRowText, { height: 150, borderWidth: 0 }]}
                                placeholder={'请输入期望对接的资源'}
                                onChangeText={(text) => this.setState({ enterpriseDemand: text })}
                            >
                                {this.state.enterpriseDemand}
                            </TextInput>
                        </View>
                        <View style={styles.inputLineViewStyle}/>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>会员排序</Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                            <TextInput
                                style={styles.inputRightText}
                                placeholder={'请输入排序序号'}
                                onChangeText={(text) => this.setState({ staffSort: text })}
                            >
                                {this.state.staffSort}
                            </TextInput>
                        </View>
                        <View style={styles.inputLineViewStyle}/>

                        <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0, marginTop: 6}]}>
                            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                                <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginLeft: 20, width: (screenWidth - 56)/2}]} >
                                    <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={this.saveStaff.bind(this)}>
                            <View style={[CommonStyle.btnRowRightSaveBtnView, {marginRight: 20, width: (screenWidth - 56)/2}]}>
                                    <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <BottomScrollSelect
                            ref={'SelectClass'}
                            callBackClassValue={this.callBackSelectClassValue.bind(this)}
                        />
                    </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}


let styles = StyleSheet.create({

    headRightText: {
        color: '#A0A0A0',
        fontSize: 14,
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,

    },
    leftLabView: {
        //width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,

    },
    leftLabNameTextStyle: {
        fontSize: 18,

    },
    leftLabRedTextStyle: {
        color: '#FD6645',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 0,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputLineViewStyle: {
        height:1,
        marginLeft: 13,
        marginRight: 13,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
})