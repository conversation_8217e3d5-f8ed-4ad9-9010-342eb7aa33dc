import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, KeyboardAvoidingView, TouchableOpacity, Dimensions, Alert, Modal, Image } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 160;

export default class StorageInAdd extends Component {
    constructor() {
        super()
        this.state = {
            storageInId: '',
            orderDataSource: [],
            storageLocationAreaDataSource: [],
            storageLocationDataSource: [],
            selBrickTypeId: 0,
            selOrderId: 0,
            selOrderName: "",
            selLocationAreaId: 0,
            selLocationId: "",
            inAmount: "",
            actualSingleWeight: "",
            actualTotalWeight: "",
            actualSize: "",
            location: "",
            productionLineDataSource: [],
            selProductionLineId: null,
            selPackageAmount: "",
            amountPerPackage: "",
            modal: false,
            cusModal:false,
            _customerDataSource: [],
            selCustomerId:"",
            selCustomerName:"",

            searchKeyWord: null,
            selectedStorageInDate: [],
            storageInTime: "",
            contractName: "",
            contractDataSource: [],
            selectContract: [],
            customerContractOrderTree: [],
            customerName: "",
            selectCustomer: [],
            _orderDataSource: [],
            customerId:"",
            contractId:""
        }
    }

    UNSAFE_componentWillMount() {
        console.log('==StorageInAdd==componentWillMount');
        // this.loadContractList();

        // 加载客户
        if(constants.loginUser.tenantId == 66) {
            this.loadCustomerData();
        }

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageInId, productionLineId, locationAreaId } = route.params;
            if (productionLineId) {
                this.setState({
                    selProductionLineId: productionLineId
                })
            }
            if (storageInId) {
                console.log("========Edit==storageInId:", storageInId);
                this.setState({
                    storageInId: storageInId,
                })
                loadTypeUrl = "/biz/storage/in/get";
                loadRequest = { 'storageInId': storageInId };
                httpPost(loadTypeUrl, loadRequest, this.loadStorageInDataCallBack);
            }
            // 当前时间
            var currentDate = new Date();
            var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
            var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
            this.setState({
                selectedStorageInDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                storageInTime: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
            })
        }
        this.loadInitData();

        loadTypeUrl = "/biz/production/line/list";
        loadRequest = { 'currentPage': 1, 'pageSize': 1000 };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadProductionLine);
    }

    loadCustomerData=()=>{
            let loadTypeUrl = "/biz/tenant/customer/list";
            let loadRequest = {
            "currentPage":1,
            "pageSize":10000
        };
        httpPost(loadTypeUrl, loadRequest, this.callBackLoadCustomerData);    
    }
    callBackLoadCustomerData = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                customerContractOrderTree: response.data.dataList
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    loadCustomer = () => {
        var _customerDataSource = copyArr(this.state.customerContractOrderTree);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _customerDataSource = _customerDataSource.filter(item => item.customerName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _customerDataSource: _customerDataSource,
        })
    }

    componentWillUnmount() {
        console.log('==StorageInAdd==componentWillUnmount');
    }

    callBackLoadProductionLine = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            let productionLineDataSource = response.data.dataList;
            let selProductionLineId = response.data.dataList[0].productionLineId;
            // console.log("======现在的iD" + this.state.selProductionLineId);

            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                selProductionLineId = constants.loginUser.spUserExtDTO.productionLineId;
            }
            if (this.state.selProductionLineId) {
                selProductionLineId = this.state.selProductionLineId;
            }
            // console.log("===-=设置之前的id===" + this.state.selproductionLineId);
            this.setState({
                productionLineDataSource: productionLineDataSource,
                selProductionLineId: selProductionLineId,
            })
            var currentTime = new Date();
            var qryEndTime = new Date(currentTime.getTime() + 8 * 3600000);
            var qryStartTime = new Date(qryEndTime.getTime() - 30 * 86400000);
            if(constants.loginUser.tenantId != 66) {
                let loadUrl = "/biz/order/list";
                let loadRequest = {
                    "currentPage":1,
                    "pageSize":1000,
                    // "contractId": this.state.contractId,
                    "display": "Y",
                    "qryContent":"order",
                    "productionLineId":selProductionLineId,
                    "excludeOrderStateList": [
                        "A", "K"
                    ],
                    "qryStartTime":qryStartTime,
                    "qryEndTime":qryEndTime,
                    // 'encastage':"Y"
                };
                httpPost(loadUrl, loadRequest, this.loadOrderListByContractIdCallBack);    
            }

            // console.log("设置后的sel=" + selProductionLineId);
            // console.log("===-=设置之后的id===" + this.state.selProductionLineId);
            // this.loadOrder();
        }
    }

    loadStorageInDataCallBack = (response) => {
        console.log("=========loadStorageInDataCallBack===response:", response);
        if (response.code == 200 && response.data) {
            var selectedStorageInDate = response.data.storageInTime.split("-");
            this.setState({
                storageInId: response.data.storageInId,
                selBrickTypeId: response.data.brickTypeId,
                selOrderId: response.data.orderId,
                selOrderName: response.data.orderName,
                inAmount: response.data.inAmount,
                selPackageAmount: response.data.packageAmount,
                amountPerPackage:response.data.packageAmount ? (response.data.inAmount / response.data.packageAmount) : "",
                actualSingleWeight: response.data.actualSingleWeight,
                actualTotalWeight: (response.data.actualTotalWeight / 1000).toFixed(2),
                actualSize: response.data.actualSize,
                selLocationAreaId: response.data.locationAreaId,
                selLocationId: response.data.locationId,
                location: response.data.location,
                storageInTime: response.data.storageInTime,
                selectedStorageInDate: selectedStorageInDate,
                selCustomerName: response.data.customerName,
                contractName: response.data.contractName
            })
        }
    }

    loadInitData = () => {
        // 加载库位列表
        let url = "/biz/storage/location/area/list";
        let loadRequest = { 'currentPage': 1, 'pageSize': 1000 };
        httpPost(url, loadRequest, this.callBackLoadStorageLocationArea);
    }

    loadOrder = () => {
        var _orderDataSource = copyArr(this.state.orderDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _orderDataSource = _orderDataSource.filter(item => item.orderName.indexOf(this.state.searchKeyWord) > 0);
        }
        this.setState({
            _orderDataSource: _orderDataSource,
        })
    }

    // 订单回调加载
    callBackLoadOrder = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                WToast.show({ data: "没有可以入库的订单" });
                this.setState({
                    orderDataSource: []
                })
                return;
            }
            this.setState({
                orderDataSource: response.data.dataList,
                // selBrickTypeId:response.data.dataList[0] ? response.data.dataList[0].brickTypeId : 0,
                // selOrderId:response.data.dataList[0] ? response.data.dataList[0].orderId : 0,
                actualSingleWeight: response.data.dataList[0].standardWeight,
                actualSize: response.data.dataList[0].standardSize,
                inAmount: "",
            })
            // let loadTypeUrl;
            // let loadRequest;
            // const { route, navigation } = this.props;
            // if (route && route.params) {
            //     const { storageInId, productionLineId } = route.params;
            //     if (storageInId) {
            //         console.log("========Edit==storageInId:", storageInId);
            //         this.setState({
            //             storageInId: storageInId,

            //         })
            //         loadTypeUrl = "/biz/storage/in/get";
            //         loadRequest = { 'storageInId': storageInId };
            //         httpPost(loadTypeUrl, loadRequest, this.loadStorageInDataCallBack);
            //     }
            //     // if(productionLineId) {
            //     //     this.setState({
            //     //         selProductionLineId:productionLineId
            //     //     })
            //     // }
            // }
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 库区回调加载
    callBackLoadStorageLocationArea = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库区");
                WToast.show(toastOpts);
                return;
            }
            this.setState({
                storageLocationAreaDataSource: response.data.dataList,
            })
            const { route, navigation } = this.props;
            if (route && route.params) {
                const { storageInId, locationAreaId } = route.params;
                if (locationAreaId) {
                    this.setState({
                        selLocationAreaId: locationAreaId,
                    })
                    this.loadStorageLocationByAreaId(locationAreaId)
                }
                else if (response.data.dataList.length > 0) {
                    this.setState({
                        selLocationAreaId: response.data.dataList[0].locationAreaId
                    })
                    this.loadStorageLocationByAreaId(response.data.dataList[0].locationAreaId);
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // 加载对应库区的库位信息
    loadStorageLocationByAreaId = (locationAreaId) => {
        let url = "/biz/storage/location/list";
        let loadRequest = {
            'currentPage': 1,
            'pageSize': 1000,
            'locationAreaId': locationAreaId
        };
        httpPost(url, loadRequest, this.callBackLoadStorageLocationByAreaId);
    }

    // 库位信息回调
    callBackLoadStorageLocationByAreaId = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库位");
                WToast.show(toastOpts);
                this.setState({
                    storageLocationDataSource: []
                })
                return;
            }
            this.setState({
                storageLocationDataSource: response.data.dataList,
            })
            if (this.state.storageInId && this.state.selLocationId != 0) {
                this.setState({
                    selLocationId: this.state.selLocationId,
                })
            }
            else if (response.data.dataList.length > 0) {
                this.setState({
                    selLocationId: response.data.dataList[0].locationId
                })
            }
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    renderProductLineRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.storageInId) {
                    return;
                }
                // var selMachineId = null;
                // 切换生产车间时，下面的机台也要跟着变，机台默认选择第一个
                this.setState({
                    selProductionLineId: item.productionLineId,
                    selOrderId: "",
                    selOrderName: "",
                    searchKeyWord: ""
                    // machineDataSource:item.machineDTOList,
                })
                var currentTime = new Date();
                var qryEndTime = new Date(currentTime.getTime() + 8 * 3600000);
                var qryStartTime = new Date(qryEndTime.getTime() - 30 * 86400000);
                if (this.state.customerName && this.state.contractName) {
                    // for (var i = 0; i < this.state.contractDataSource.length; i++) {
                    //     if (this.state.contractName == this.state.contractDataSource[i].contractName) {
                    //         var orderList = [];
                    //         var orderDTOList = this.state.contractDataSource[i].orderDTOList;
                    //         if (orderDTOList != null && orderDTOList.length > 0) {
                    //             for (var m = 0; m < orderDTOList.length; m++) {
                    //                 if (orderDTOList[m].orderState != "A" || orderDTOList[m].orderState != "K") {
                    //                     if (orderDTOList[m].productionLineId === item.productionLineId) {
                    //                         orderList = orderList.concat(orderDTOList[m])
                    //                     }
                    //                 }
                    //             }
                    //         }
                    //         this.setState({
                    //             orderDataSource: orderList,
                    //         })
                    //     }
                    // }
                    let loadUrl = "/biz/order/list";
                    let loadRequest = {
                        "currentPage":1,
                        "pageSize":1000,
                        "contractId": this.state.contractId,
                        "qryContent":"order",
                        "display": "Y",
                        "productionLineId":item.productionLineId,
                        "excludeOrderStateList": [
                            "A", "K"
                        ],
                        "qryStartTime":qryStartTime,
                        "qryEndTime":qryEndTime,
                        // 'encastage':"Y"
                    };
                    httpPost(loadUrl, loadRequest, this.loadOrderListByContractIdCallBack);
                }
                if(constants.loginUser.tenantId != 66) {
                    let loadUrl = "/biz/order/list";
                    let loadRequest = {
                        "currentPage":1,
                        "pageSize":1000,
                        // "contractId": this.state.contractId,
                        "qryContent":"order",
                        "display": "Y",
                        "productionLineId":item.productionLineId,
                        "excludeOrderStateList": [
                            "A", "K"
                        ],
                        "qryStartTime":qryStartTime,
                        "qryEndTime":qryEndTime,
                        // 'encastage':"Y"
                    };
                    httpPost(loadUrl, loadRequest, this.loadOrderListByContractIdCallBack);    
                }
                // let url = "/biz/order/list";
                // let loadRequest = {
                //     'currentPage': 1,
                //     'pageSize': 100,
                //     "display": "Y",
                //     "excludeOrderStateList": [
                //         "A", "K"
                //     ],
                //     'productionLineId': item.productionLineId,
                //     "qryContent": "order",
                // };
                // httpPost(url, loadRequest, this.callBackLoadOrder);
                // if (item.machineDTOList && item.machineDTOList.length > 0) {
                //     selMachineId = item.machineDTOList[0].machineId;
                // }
                // this.setState({
                //     selMachineId:selMachineId,
                // })
            }}>
                <View key={item.productionLineId} style={[item.productionLineId === this.state.selProductionLineId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/2,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.productionLineId === this.state.selProductionLineId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.productionLineName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // loadContractData = (response) => {
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         this.setState({
    //             contractDataSource: response.data.dataList
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("StorageInList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>入库管理</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>入库管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.storageInId) {
                    return;
                }
                this.setState({
                    selBrickTypeId: item.brickTypeId,
                    selOrderId: item.orderId,
                    selOrderName: item.orderName,
                    actualSingleWeight: item.standardWeight,
                    actualSize: item.standardSize,
                    inAmount: "",
                    customerName: item.customerName,
                })
            }}>
                <View key={item.orderId} style={[item.orderId === this.state.selOrderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.orderId === this.state.selOrderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.orderName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    saveStorageIn = () => {
        console.log("=======saveStorageIn");
        let toastOpts;
        if(constants.loginUser.tenantId == 66) {
            if (!this.state.selCustomerName) {
                toastOpts = getFailToastOpts("请选择客户");
                WToast.show(toastOpts)
                return;
            }
            if (!this.state.contractName) {
                toastOpts = getFailToastOpts("请选择合同");
                WToast.show(toastOpts)
                return;
            }
    
        }
        if (!this.state.selBrickTypeId || this.state.selBrickTypeId === 0) {
            toastOpts = getFailToastOpts("请选择要入库的产品");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selOrderId || this.state.selOrderId === 0) {
            toastOpts = getFailToastOpts("请选择要入库的产品.");
            WToast.show(toastOpts)
            return;
        }
        
        if (constants.loginUser.tenantId != 66) {
            if (!this.state.selPackageAmount || this.state.selPackageAmount === "0") {
                WToast.show({ data: "请输入件数" });
                return;
            }
        }
        // if (!this.state.amountPerPackage || this.state.amountPerPackage === "0") {
        //     WToast.show({data:"请输入块/件"});
        //     return;
        // }
        if (!this.state.inAmount || this.state.inAmount === 0) {
            toastOpts = getFailToastOpts("请输入数量");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selLocationAreaId || this.state.selLocationAreaId === 0) {
            toastOpts = getFailToastOpts("请选择库区");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selLocationId || this.state.selLocationId === 0) {
            toastOpts = getFailToastOpts("请选择库位");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.actualTotalWeight) {
            toastOpts = getFailToastOpts("请输入实际总重");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/storage/in/add";
        if (this.state.storageInId) {
            console.log("=========Edit===storageInId", this.state.storageInId)
            url = "/biz/storage/in/modify";
        }
        let requestParams = {
            "storageInId": this.state.storageInId,
            "brickTypeId": this.state.selBrickTypeId,
            "orderId": this.state.selOrderId,
            "inAmount": this.state.inAmount,
            "packageAmount": this.state.selPackageAmount,
            "actualSingleWeight": this.state.actualSingleWeight,
            "actualTotalWeight": this.state.actualTotalWeight * 1000,
            "actualSize": this.state.actualSize,
            "locationId": this.state.selLocationId,
            "location": this.state.location,
            "operator": constants.loginUser.userName,
            "storageInTime": this.state.storageInTime
        };
        // console.log(requestParams)
        httpPost(url, requestParams, this.saveStorageInCallBack);
    }

    // 保存回调函数
    saveStorageInCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    // 库区
    renderLocationAreaRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.storageInId) {
                    let toastOpts = getFailToastOpts('不能编辑');
                    WToast.show(toastOpts);
                    return;
                }
                this.setState({
                    selLocationAreaId: item.locationAreaId
                })
                this.loadStorageLocationByAreaId(item.locationAreaId);
            }}>
                <View key={item.locationAreaId} style={[this.state.storageInId ? CommonStyle.disableViewStyle : null,item.locationAreaId === this.state.selLocationAreaId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.locationAreaId === this.state.selLocationAreaId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.locationAreaName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 库位
    renderLocationRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.storageInId) {
                    let toastOpts = getFailToastOpts('不能编辑');
                    WToast.show(toastOpts);
                    return;
                }
                this.setState({
                    selLocationId: item.locationId
                })
            }}>
                <View key={item.locationId} style={[this.state.storageInId ? CommonStyle.disableViewStyle : null,item.locationId === this.state.selLocationId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.locationId === this.state.selLocationId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.locationName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    openStorageInDate() {
        if (this.state.storageInId) {
            return;
        }
        this.refs.SelectStorageInDate.showDate(this.state.selectedStorageInDate)
    }

    callBackSelectStorageInDateValue(value) {
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedStorageInDate: value
        })
        if (value && value.length) {
            var storageInTime = "";
            var vartime;
            for (var index = 0; index < value.length; index++) {
                vartime = value[index];
                if (index === 0) {
                    storageInTime += vartime;
                }
                else {
                    storageInTime += "-" + vartime;
                }
            }
            this.setState({
                storageInTime: storageInTime
            })
        }
    }

    // 渲染客户底部滚动数据
    // openCustomerSelect() {
    //     if (!this.state.customerContractOrderTree || this.state.customerContractOrderTree.length < 1) {
    //         WToast.show({ data: "请先添加客户" });
    //         return
    //     }
    //     this.setState({
    //         contractDataSource: [],
    //         orderDataSource: []
    //     })
    //     this.refs.SelectCustomer.showCustomer(this.state.selectCustomer, this.state.customerContractOrderTree)
    // }
    // callBackCustomerValue(value) {
    //     console.log("==========客户选择结果：", value)
    //     if (!value) {
    //         return;
    //     }
    //     var customerName = value.toString();
    //     this.setState({
    //         selectCustomer: value,
    //         selectContract: [],
    //         contractName: "",
    //         customerName: customerName,
    //         selOrderId: "",
    //         selOrderName: ""
    //     })
    //     let loadUrl = "/biz/tenant/customer/getCustomerByName";
    //     let loadRequest = {
    //         "customerName": customerName
    //     };
    //     httpPost(loadUrl, loadRequest, this.callBackLoadCustomerDetailData);
    //     // this.loadContractList(customerName);
    // }

    // loadContractList = (customerName) => {
    //     for (var i = 0; i < this.state.customerContractOrderTree.length; i++) {
    //         if (customerName == this.state.customerContractOrderTree[i].customerName) {
    //             this.setState({
    //                 contractDataSource: this.state.customerContractOrderTree[i].contractList
    //             })
    //         }
    //     }
    // }

    loadContractList =(customerId)=>{
        let loadUrl = "/biz/contract/list";
        let loadRequest = {
            "currentPage":1,
            "pageSize":1000,
            "partyA": customerId,
            "qryContent":"contract"
        };
        httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
    }

    loadContractListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                contractDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // callBackLoadCustomerDetailData = (response) => {
    //     if (response.code == 200 && response.data) {
    //         this.setState({
    //             customerName: response.data.customerName,
    //             customerId : response.data.customerId,
    //         })
    //         this.loadContractList(response.data.customerId);
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({ data: response.message });
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    openContractSelect() {
        if (this.state.selCustomerName) {
            if (!this.state.contractDataSource || this.state.contractDataSource.length < 1) {
                WToast.show({ data: "请先添加合同" });
                return;
            }
            this.setState({
                orderDataSource: []
            })
            this.refs.SelectContract.showContract(this.state.selectContract, this.state.contractDataSource)
        }
        else {
            WToast.show({ data: "请先添加客户" });
            return;
        }
    }

    callBackContractValue(value) {
        console.log("==========合同选择结果：", value)
        if (!value) {
            return;
        }
        var contractName = value.toString();
        this.setState({
            selectContract: value,
            contractName: contractName,
            selOrderId: "",
            selOrderName: ""
        })
        let loadUrl = "/biz/contract/getContractByName";
        let loadRequest = {
            "contractName": contractName
        };
        httpPost(loadUrl, loadRequest, this.loadContractDetailCallBack);
        // this.loadOrderList(contractName);
    }

    loadContractDetailCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                contractId : response.data.contractId,
            })
            this.loadOrderListByContractId(response.data.contractId);
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadOrderListByContractId=(contractId)=>{
        var currentTime = new Date();
        var qryEndTime = new Date(currentTime.getTime() + 8 * 3600000);
        var qryStartTime = new Date(qryEndTime.getTime() - 30 * 86400000);
        let loadUrl = "/biz/order/list";
        let loadRequest = {
            "currentPage":1,
            "pageSize":1000,
            "contractId": contractId,
            "display": "Y",
            "qryContent":"order",
            "productionLineId":this.state.selProductionLineId,
            "excludeOrderStateList": [
                "A", "K"
            ],
            "qryStartTime":qryStartTime,
            "qryEndTime":qryEndTime,
            // 'encastage':"Y"
        };
        httpPost(loadUrl, loadRequest, this.loadOrderListByContractIdCallBack);
    }

    loadOrderListByContractIdCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                orderDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    // loadOrderList = (contractName) => {
    //     for (var i = 0; i < this.state.contractDataSource.length; i++) {
    //         if (contractName == this.state.contractDataSource[i].contractName) {
    //             var orderList = [];
    //             var orderDTOList = this.state.contractDataSource[i].orderDTOList;
    //             if (orderDTOList != null && orderDTOList.length > 0) {
    //                 for (var m = 0; m < orderDTOList.length; m++) {
    //                     if (orderDTOList[m].orderState != "A" || orderDTOList[m].orderState != "K") {
    //                         if (orderDTOList[m].productionLineId === this.state.selProductionLineId) {
    //                             orderList = orderList.concat(orderDTOList[m])
    //                         }
    //                     }
    //                 }
    //             }
    //             this.setState({
    //                 orderDataSource: orderList,
    //             })
    //         }
    //     }
    // }
    renderCustomerRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.storageOutId) {
                    return;
                }
                this.setState({
                    selCustomerId: item.customerId,
                    selCustomerName: item.customerName,
                })
            }}>
                <View key={item.customerId} style={[item.customerId === this.state.selCustomerId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.customerId === this.state.selCustomerId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.customerName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title='成品入库'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: -2 }} />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>入库日期</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openStorageInDate()}>
                            <View style={[CommonStyle.inputTextStyleTextStyle, { borderWidth: 0, width: screenWidth - (leftLabWidth + 25) }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.storageInTime ? "请选择入库日期" : this.state.storageInTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>生产车间</Text>
                    </View>
                    <View style={{ width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15 }}>
                        {
                            (this.state.productionLineDataSource && this.state.productionLineDataSource.length > 0)
                                ?
                                this.state.productionLineDataSource.map((item, index) => {
                                    return this.renderProductLineRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    {
                        constants.loginUser.tenantId == 66
                        ?
                        <View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>客户</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <View style={[(!this.state.customerContractOrderTree || this.state.customerContractOrderTree.length === 0 || this.state.storageInId) ? CommonStyle.disableViewStyle : null]}>
                                    <TouchableOpacity onPress={() => {
                                        if (this.state.storageInId) {
                                            WToast.show({ data: "只能编辑件数、块/件、数量、单重、总重" })
                                            return;
                                        }
                                        if (this.state.customerContractOrderTree && this.state.customerContractOrderTree.length > 0) {
                                            this.setState({
                                                _customerDataSource: copyArr(this.state.customerContractOrderTree),
                                            })
                                        }
                                        this.setState({
                                            cusModal: true,
                                            searchKeyWord: ""
                                        })

                                        if (!this.state.selCustomerId && this.state.customerContractOrderTree && this.state.customerContractOrderTree.length > 0) {
                                            this.setState({
                                                selCustomerId: this.state.customerContractOrderTree[0].customerId,
                                                selCustomerName: this.state.customerContractOrderTree[0].customerName,
                                                // contractId:"",
                                                // contractName:"",
                                                // selectContract:[],
                                            })
                                        }
                                    }}>
                                        <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, {height:40, flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                            {this.state.selCustomerName ? 
                                                <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                                {this.state.selCustomerName}
                                                </Text>
                                                :
                                                <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                                选择客户
                                                </Text>
                                            }
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>合同</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <TouchableOpacity onPress={() => {
                                    if (this.state.storageInId) {
                                        WToast.show({ data: "只能编辑件数、块/件、数量、单重、总重" })
                                    }
                                    else {
                                        this.openContractSelect()
                                    }
                                }}>
                                    <View style={[CommonStyle.inputTextStyleTextStyle, { width: screenWidth - (leftLabWidth + 25) }]}>
                                        <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                            {!this.state.contractName ? "请选择合同" : this.state.contractName}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>产品</Text>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                </View>
                                <View style={[(!this.state.orderDataSource || this.state.orderDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                                    <TouchableOpacity onPress={() => {
                                        if (this.state.storageInId) {
                                            return;
                                        }
                                        if (!this.state.orderDataSource || this.state.orderDataSource.length === 0) {
                                            let errorMsg = '暂无需要入库的产品';
                                            if (!this.state.selCustomerName || this.state.selCustomerName.length === 0) {
                                                errorMsg = "请先选择客户";
                                            }
                                            else if (!this.state.contractName || this.state.contractName.length === 0) {
                                                errorMsg = "请先选择合同";
                                            }
                                            Alert.alert('确认', errorMsg, [
                                                {
                                                    text: "确定", onPress: () => {
                                                        WToast.show({ data: '点击了确定' });
                                                    }
                                                }
                                            ]);
                                            return;
                                        }
                                        if (this.state.orderDataSource && this.state.orderDataSource.length > 0) {
                                            this.setState({
                                                _orderDataSource: copyArr(this.state.orderDataSource),
                                            })
                                        }

                                        this.setState({
                                            modal: true,
                                            searchKeyWord: ""
                                        })

                                        if (!this.state.selOrderId && this.state.orderDataSource && this.state.orderDataSource.length > 0) {
                                            this.setState({
                                                selBrickTypeId: this.state.orderDataSource[0].brickTypeId,
                                                selBrickTypeName: this.state.orderDataSource[0].brickTypeName,
                                                selOrderId: this.state.orderDataSource[0].orderId,
                                                selOrderName: this.state.orderDataSource[0].orderName,
                                                brickTypeId: this.state.orderDataSource[0].brickTypeId,
                                                brickTypeName: this.state.orderDataSource[0].brickTypeName,
                                                orderId: this.state.orderDataSource[0].orderId,
                                                orderName: this.state.orderDataSource[0].orderName,
                                                customerName: this.state.orderDataSource[0].customerName,
                                            })
                                        }
                                    }}>
                                        <View style={[this.state.selOrderId && this.state.selOrderName ?
                                            {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                            :
                                            {backgroundColor: '#F2F5FC'}
                                            ,
                                            {
                                                marginRight: 8,
                                                marginTop: 4,
                                                marginBottom: 4,
                                                borderRadius: 4,
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                                height: 36,
                                                paddingLeft:6,
                                                paddingRight:6,
                                                // width: (screenWidth - 54)/2,
                                                borderRadius: 4,
                                            }
                                        ]}>
                                            <Text style={[this.state.selOrderId && this.state.selOrderName ?
                                                {
                                                    color: '#1E6EFA'
                                                }
                                                :
                                                {
                                                    color: '#404956'
                                                }
                                                ,
                                            {
                                                fontSize: 16, textAlign : 'center'
                                            }
                                            ]}>
                                                
                                                {this.state.selOrderId && this.state.selOrderName ? ("" + this.state.selOrderName) : "选择产品"}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                        :
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                                <Text style={styles.leftLabNameTextStyle}>产品</Text>
                                {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                            </View>
                            <View style={[(!this.state.orderDataSource || this.state.orderDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                                <TouchableOpacity onPress={() => {
                                    if (this.state.storageInId) {
                                        return;
                                    }
                                    if (!this.state.orderDataSource || this.state.orderDataSource.length === 0) {
                                        let errorMsg = '暂无需要入库的产品';
                                        Alert.alert('确认', errorMsg, [
                                            {
                                                text: "确定", onPress: () => {
                                                    WToast.show({ data: '点击了确定' });
                                                }
                                            }
                                        ]);
                                        return;
                                    }
                                    if (this.state.orderDataSource && this.state.orderDataSource.length > 0) {
                                        this.setState({
                                            _orderDataSource: copyArr(this.state.orderDataSource),
                                        })
                                    }

                                    this.setState({
                                        modal: true,
                                        searchKeyWord: ""
                                    })

                                    if (!this.state.selOrderId && this.state.orderDataSource && this.state.orderDataSource.length > 0) {
                                        this.setState({
                                            selBrickTypeId: this.state.orderDataSource[0].brickTypeId,
                                            selBrickTypeName: this.state.orderDataSource[0].brickTypeName,
                                            selOrderId: this.state.orderDataSource[0].orderId,
                                            selOrderName: this.state.orderDataSource[0].orderName,
                                            brickTypeId: this.state.orderDataSource[0].brickTypeId,
                                            brickTypeName: this.state.orderDataSource[0].brickTypeName,
                                            orderId: this.state.orderDataSource[0].orderId,
                                            orderName: this.state.orderDataSource[0].orderName,
                                            customerName: this.state.orderDataSource[0].customerName,
                                        })
                                    }
                                }}>
                                    <View style={[this.state.selOrderId && this.state.selOrderName ?
                                            {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                            :
                                            {backgroundColor: '#F2F5FC'}
                                            ,
                                            {
                                                marginRight: 8,
                                                marginTop: 4,
                                                marginBottom: 4,
                                                borderRadius: 4,
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                                height: 36,
                                                paddingLeft:6,
                                                paddingRight:6,
                                                // width: (screenWidth - 54)/2,
                                                borderRadius: 4,
                                            }
                                        ]}>
                                            <Text style={[this.state.selOrderId && this.state.selOrderName ?
                                                {
                                                    color: '#1E6EFA'
                                                }
                                                :
                                                {
                                                    color: '#404956'
                                                }
                                                ,
                                            {
                                                fontSize: 16, textAlign : 'center'
                                            }
                                            ]}>
                                                
                                                {this.state.selOrderId && this.state.selOrderName ? ("" + this.state.selOrderName) : "选择产品"}
                                            </Text>
                                        </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    }
                    <Modal      
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.cusModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadCustomer();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._customerDataSource && this.state._customerDataSource.length > 0)
                                                ?
                                                this.state._customerDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderCustomerRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            cusModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selCustomerId) {
                                            let toastOpts = getFailToastOpts("您还没有选择客户");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        let loadUrl = "/biz/contract/list";
                                        let loadRequest = {
                                            "currentPage":1,
                                            "pageSize":1000,
                                            "partyA": this.state.selCustomerId,
                                            "qryContent":"contract"                                
                                        };
                                        httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
                                        this.setState({
                                            cusModal: false,
                                            contractName:"",
                                            contractId:""
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    {/* <View style={CommonStyle.rowLabLeftView}>
                                        <Text style={CommonStyle.rowLabTextStyle}>关键字</Text>
                                    </View> */}
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadOrder();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._orderDataSource && this.state._orderDataSource.length > 0)
                                                ?
                                                this.state._orderDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selOrderId) {
                                            let toastOpts = getFailToastOpts("您还没有选择产品");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        this.setState({
                                            modal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    {/* <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                        {
                            (this.state.orderDataSource && this.state.orderDataSource.length > 0) 
                            ? 
                            this.state.orderDataSource.map((item, index)=>{
                                return this.renderRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View> */}
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>实际尺寸</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入实际尺寸'}
                            onChangeText={(text) => this.setState({actualSize:text})}
                        >
                            {this.state.actualSize}
                        </TextInput>
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            {
                                (constants.loginUser.tenantId != 66) ?
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    : <View />
                            }
                            <Text style={styles.leftLabNameTextStyle}>件数</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            placeholder={'请输入'}
                            onChangeText={
                                (text) => {
                                    this.setState({ selPackageAmount: text })
                                    if (this.state.amountPerPackage) {
                                        this.setState({
                                            inAmount: text * this.state.amountPerPackage
                                        })
                                        if (this.state.actualSingleWeight) {
                                            this.setState({
                                                actualTotalWeight: (text * this.state.amountPerPackage * this.state.actualSingleWeight / 1000).toFixed(2)
                                            })
                                        }
                                    }
                                }
                            }
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 25) }]}>
                            {this.state.selPackageAmount}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>块/件</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            placeholder={'请输入'}
                            onChangeText={
                                (text) => {
                                    this.setState({ amountPerPackage: text })
                                    if (this.state.selPackageAmount) {
                                        this.setState({
                                            inAmount: text * this.state.selPackageAmount
                                        })
                                        if (this.state.actualSingleWeight) {
                                            this.setState({
                                                actualTotalWeight: (text * this.state.selPackageAmount * this.state.actualSingleWeight / 1000).toFixed(2)
                                            })
                                        }
                                    }
                                    else {
                                        WToast.show({ data: "请输入件数" });
                                    }
                                }
                            }
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 25) }]}>
                            {this.state.amountPerPackage}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>数量</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={[styles.inputRightText,{width: screenWidth - (leftLabWidth + 25) }]}
                            placeholder={'请输入'}
                            onChangeText={(text) => {
                                if (this.state.actualSingleWeight) {
                                    this.setState({
                                        actualTotalWeight: (text * this.state.actualSingleWeight / 1000).toFixed(2)
                                    })
                                }
                                this.setState({ inAmount: text })
                            }}
                        >
                            {this.state.inAmount}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>库区</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={{ width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15 }}>
                        {
                            (this.state.storageLocationAreaDataSource && this.state.storageLocationAreaDataSource.length > 0)
                                ?
                                this.state.storageLocationAreaDataSource.map((item, index) => {
                                    return this.renderLocationAreaRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>库位</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={{ width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15 }}>
                        {
                            (this.state.storageLocationDataSource && this.state.storageLocationDataSource.length > 0)
                                ?
                                this.state.storageLocationDataSource.map((item, index) => {
                                    return this.renderLocationRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>备注</Text>
                        </View>
                        <TextInput
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ location: text })}
                        >
                            {this.state.location}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>实际单重(Kg)</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => {
                                if (this.state.inAmount) {
                                    this.setState({
                                        actualTotalWeight: (text * this.state.inAmount / 1000).toFixed(2)
                                    })
                                }
                                this.setState({ actualSingleWeight: text })
                            }}
                        >
                            {this.state.actualSingleWeight}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>实际总重(吨)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ actualTotalWeight: text })}
                        >
                            {this.state.actualTotalWeight}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:16}} />

                    <View style={[CommonStyle.blockAddCancelSaveStyle]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveStorageIn.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <BottomScrollSelect
                        ref={'SelectStorageInDate'}
                        callBackDateValue={this.callBackSelectStorageInDateValue.bind(this)}
                    />
                    {/* <BottomScrollSelect
                        ref={'SelectCustomer'}
                        callBackCustomerValue={this.callBackCustomerValue.bind(this)}
                    /> */}
                    <BottomScrollSelect
                        ref={'SelectContract'}
                        callBackContractValue={this.callBackContractValue.bind(this)}
                    />
                </ScrollView>
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:6,
        marginRight:3,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        // borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
})