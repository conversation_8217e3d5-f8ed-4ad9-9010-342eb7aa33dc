import React, { Component } from 'react';
import { View, ScrollView, Text, TextInput, StyleSheet, FlatList, Alert, TouchableOpacity, Dimensions, Image, KeyboardAvoidingView } from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class HarvestMgrAdd extends Component {
    constructor() {
        super()
        this.state = {
            operate: "",
            harvestId: "",
            harvestTitle: "",
            harvestContent: "",
            harvestState: "",
            visible: "Y",
        }
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');

        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { harvestId } = route.params;
            if (harvestId) {
                console.log("========Edit==harvestId:", harvestId);
                this.setState({
                    harvestId: harvestId,
                    operate: "编辑"
                })
                loadTypeUrl = "/biz/harvest/get";
                loadRequest = { 'harvestId': harvestId };
                httpPost(loadTypeUrl, loadRequest, this.loadHarvestDataCallBack);
            }
            else {
                this.setState({
                    operate: "新增"
                })
                // 当前时间
                var currentDate = new Date();
                var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
                var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
                this.setState({
                    selectedHarvestDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
                    dailyDate: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
                })
            }
        }
    }
    loadHarvestDataCallBack = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                harvestId: response.data.harvestId,
                harvestTitle: response.data.harvestTitle,
                harvestContent: response.data.harvestContent,
                visible: response.data.visible,
                harvestState: response.data.harvestState
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }

    // // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("HarvestMgrList")
            // }}>
            //     <Text style={CommonStyle.headRightBlackText}>我的成果</Text>
            // </TouchableOpacity>
            <View></View>
        )
    }

    // saveHarvest =()=> {
    //     console.log("=======saveHarvest");
    //     let toastOpts;
    //     if (!this.state.harvestTitle) {
    //         toastOpts = getFailToastOpts("请输入标题");
    //         WToast.show(toastOpts)
    //         return;
    //     }
    //     if (!this.state.harvestContent) {
    //         toastOpts = getFailToastOpts("请输入内容");
    //         WToast.show(toastOpts)
    //         return;
    //     }
    //     let url= "/biz/harvest/add";
    //     if (this.state.harvestId) {
    //         console.log("=========Edit===harvestId", this.state.harvestId)
    //         url= "/biz/harvest/modify";
    //     }
    //     let requestParams={
    //         harvestId:this.state.harvestId,
    //         harvestTitle: this.state.harvestTitle,
    //         harvestContent: this.state.harvestContent,
    //         visible: this.state.visible,
    //         harvestState:"0AA"
    //     };
    //     httpPost(url, requestParams, this.saveHarvestCallBack);
    // }

    // // 保存回调函数
    // saveHarvestCallBack=(response)=>{
    //     let toastOpts;
    //     switch (response.code) {
    //         case 200:
    //             if (this.props.route.params.refresh) {
    //                 this.props.route.params.refresh();
    //             }
    //             toastOpts = getSuccessToastOpts('保存完成');
    //             WToast.show(toastOpts);
    //             this.props.navigation.goBack()
    //             break;
    //         default:
    //             toastOpts = getFailToastOpts(response.message);
    //             WToast.show({data:response.message})
    //       }
    // }
    holdHarvest = () => {
        console.log("======= holdHarvest");
        let toastOpts;
        let url = "/biz/harvest/add";
        if (this.state.harvestId) {
            console.log("=========Edit===harvestId", this.state.harvestId)
            url = "/biz/harvest/modify";
        }
        let requestParams = {
            harvestId: this.state.harvestId,
            harvestTitle: this.state.harvestTitle,
            harvestContent: this.state.harvestContent,
            visible: this.state.visible,
            harvestState: "0BB"
        };
        httpPost(url, requestParams, this.holdHarvestCallBack);
    }

    // 暂存回调函数
    holdHarvestCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('暂存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }
    render() {
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '成果'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>标题</Text>
                        </View>

                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <TextInput
                            style={[CommonStyle.inputRowText]}
                            placeholder={'请输入标题'}
                            onChangeText={(text) => this.setState({ harvestTitle: text })}
                        >
                            {this.state.harvestTitle}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>内容</Text>
                        </View>
                    </View>

                    <View style={[styles.inputRowStyle, { height: 255 }]}>
                        <TextInput
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText, { height: 255 }]}
                            placeholder={'请输入内容'}
                            onChangeText={(text) => this.setState({ harvestContent: text })}
                        >
                            {this.state.harvestContent}
                        </TextInput>
                    </View>
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>谁可以看</Text>
                        </View>
                        <View style={[styles.selectViewItem, (this.state.visible === 'Y') ? {
                            backgroundColor: 'rgba(83,100,255,0.1)'
                        }
                            :
                            {
                                backgroundColor: "rgba(246,246,246,1)"
                            },
                        {
                            //外边距
                            marginLeft: 6,
                            marginRight: 6,
                            marginTop: 8,
                            marginBottom: 0,
                            //内边距
                            paddingTop: 5, paddingBottom: 5,
                            paddingLeft: 15, paddingRight: 15,
                            borderRadius: 4,
                            justifyContent: 'center',
                            height: 30,
                            borderRadius: 4
                        }]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    visible: "Y",
                                })
                            }}>
                                <View style={styles.selectViewItem}>
                                    <Text style={[styles.selectTextItem, (this.state.visible === 'Y') ? {
                                        color: 'rgba(30,110,250,1)'
                                    }
                                        :
                                        {
                                            color: 'rgba(0,10,32,0.45)'
                                        }]}>公开</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={[styles.selectViewItem, (this.state.visible === 'N') ? {
                            backgroundColor: 'rgba(83,100,255,0.1)'
                        }
                            :
                            {
                                backgroundColor: "rgba(246,246,246,1)"
                            },
                        {
                            //外边距
                            marginLeft: 6,
                            marginRight: 6,
                            marginTop: 8,
                            marginBottom: 0,
                            //内边距
                            paddingTop: 5, paddingBottom: 5,
                            paddingLeft: 15, paddingRight: 15,
                            borderRadius: 4,
                            justifyContent: 'center',
                            height: 30,
                            borderRadius: 4
                        }]}>
                            <TouchableOpacity onPress={() => {
                                if (this.state.harvestId && this.state.visible === 'Y' && this.state.harvestState != "0BB") {
                                    WToast.show({ data: "提交后不允许设为私密" });
                                    return
                                }
                                this.setState({
                                    visible: "N",
                                })
                            }}>
                                <View style={styles.selectViewItem}>
                                    <Text style={[styles.selectTextItem, (this.state.visible === 'N') ? {
                                        color: 'rgba(30,110,250,1)'
                                    }
                                        :
                                        {
                                            color: 'rgba(0,10,32,0.45)'
                                        }]}>私密</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={CommonStyle.rowSplitViewStyle}></View>
                    {
                        this.state.harvestState != "0AA" ?
                            <View style={styles.btnRowView}>
                                <TouchableOpacity onPress={this.holdHarvest.bind(this)}>
                                    <View style={styles.holdbtnView}>
                                        <Text style={styles.holdBtnText}>暂存</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            :
                            <View></View>
                    }
                    <View style={CommonStyle.btnRowStyle}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnRowLeftCancelBtnView} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => {
                            let toastOpts;
                            if (!this.state.harvestTitle) {
                                toastOpts = getFailToastOpts("请输入标题");
                                WToast.show(toastOpts)
                                return;
                            }
                            if (!this.state.harvestContent) {
                                toastOpts = getFailToastOpts("请输入内容");
                                WToast.show(toastOpts)
                                return;
                            }
                            Alert.alert('确认', '提交后不可修改，您确定要提交吗？', [
                                {
                                    text: "取消", onPress: () => {
                                        WToast.show({ data: '点击了取消' });
                                    }
                                },
                                {
                                    text: "确定", onPress: () => {
                                        let url = "/biz/harvest/add";
                                        if (this.state.harvestId) {
                                            console.log("=========Edit===harvestId", this.state.harvestId)
                                            url = "/biz/harvest/modify";
                                        }
                                        let requestParams = {
                                            harvestId: this.state.harvestId,
                                            harvestTitle: this.state.harvestTitle,
                                            harvestContent: this.state.harvestContent,
                                            visible: this.state.visible,
                                            harvestState: "0AA"
                                        };
                                        httpPost(url, requestParams, (response) => {
                                            let toastOpts;
                                            switch (response.code) {
                                                case 200:
                                                    if (this.props.route.params.refresh) {
                                                        this.props.route.params.refresh();
                                                    }
                                                    toastOpts = getSuccessToastOpts('保存完成');
                                                    WToast.show(toastOpts);
                                                    this.props.navigation.goBack()
                                                    break;
                                                default:
                                                    toastOpts = getFailToastOpts(response.message);
                                                    WToast.show({ data: response.message })
                                            }
                                        });
                                    }
                                }
                            ]);
                        }}>
                            <View style={CommonStyle.btnRowRightSaveBtnView}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>提交</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    selectViewItem: {
        width: 64, justifyContent: 'center', alignItems: 'center'
    },
    selectTextItem: {
        fontSize: 16,
        fontWeight: 'bold'
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    btnAddView: {
        backgroundColor: '#CE3B25', height: 35, paddingLeft: 10, paddingRight: 10, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnAddText: {
        color: '#FFFFFF', fontSize: 15
    },
    btnDeleteView: {
        backgroundColor: '#FFFFFF', height: 35, borderColor: '#999999', borderWidth: 1, paddingLeft: 20, paddingRight: 20, marginRight: 15, justifyContent: 'center', borderRadius: 3
    },
    btnDeleteText: {
        color: '#999999', fontSize: 15
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        borderColor: '#F1F1F1',
        borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    btnRowView: {
        flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10, paddingRight: 10
    },
    holdbtnView: {
        fontSize: 16, width: 60, height: 30,
        borderWidth: 1,
        borderColor: 'rgba(30, 110, 250, 1)',
        justifyContent: 'center',
        alignItems: 'center',
        margin: 5,
        borderRadius: 4,
        flexDirection: 'row'
    },
    holdBtnText: {
        color: 'rgba(83, 106, 247, 1)', fontSize: 16
    },
})