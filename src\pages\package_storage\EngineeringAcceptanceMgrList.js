import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class EngineeringAcceptanceMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            orderId:"",
            acceptanceId:"",
            customerName:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        
        console.log("========这是刚进来的样子========");

        this.loadEngineeringAcceptanceList();

        console.log("========load执行了嘛=============");
    }

    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/engineering/acceptance/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        console.log(loadRequest)
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=(contractId)=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/engineering/acceptance/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    _loadNextData=()=>{
      if ((this.state.currentPage-1) >= this.state.totalPage) {
          WToast.show({data:"已经是最后一页了，我们也是有底线的"});
          return;
      }
      this.setState({
          refreshing:true
      })
      this.loadEngineeringAcceptanceList();
    }

    // 加载收款计划列表
    loadEngineeringAcceptanceList=()=>{
        let url= "/biz/engineering/acceptance/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
        };
        httpPost(url, loadRequest, this.loadEngineeringAcceptanceListCallBack);
    }

    loadEngineeringAcceptanceListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // console.log(response.data)
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 列表底部组件
    flatListFooterComponent=()=>{
      return(
          <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
      )
    }

    deleteEngineeringAcceptance=(acceptanceId)=>{
        console.log("=======delete=acceptanceId", acceptanceId);
        // 编写删除的请求
        let url= "/biz/engineering/acceptance/delete";
          let requestParams={'acceptanceId':acceptanceId};
          httpDelete(url, requestParams, this.deleteCallBack);
      }
  
      // 删除回调
      deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
          WToast.show({data:"删除完成"});
          this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
      }
  


    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnListLeftBtn ]}>
                    <Image  style={ CommonStyle.btnListLeftBtnImage } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnListLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("EngineeringAcceptanceMgrAdd", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //    <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewListRightViewStyle }>
                <TouchableOpacity onPress={() => { 
                    this.props.navigation.navigate("EngineeringAcceptanceMgrAdd", 
                    {
                        // 传递回调函数
                        refresh: this.callBackFunction 
                    });
                }}  >
                    <Image style={ CommonStyle.btnListRightBtnImage} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }

    renderRow=(item, index)=>{
        return(
          <View key={item.acceptanceId} style={styles.innerViewStyle}>
            {
                index == 0 ?
                    <View style={{ width: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: '#FFFFFF', borderBottomWidth: 10, borderBottomColor: '#F4F7F9' }}>
                    </View>
                    :
                    <View></View>
            }
              <View style={CommonStyle.titleViewStyleSpecial}>
                <Text style={CommonStyle.titleTextStyleSpecial}>{item.customerName}</Text>
                {/* <Text style={styles.titleTextStyle}>客户名称：{item.customerName}</Text> */}
              </View>
              <View style={CommonStyle.titleViewStyle}>
                <Text style={CommonStyle.titleTextStyle}>合同名称：{item.contractName}</Text>
              </View>
              <View style={CommonStyle.titleViewStyle}>
                <Text style={CommonStyle.titleTextStyle}>砖型：{item.orderName}</Text>
              </View>
              <View style={CommonStyle.titleViewStyle}>
                <Text style={CommonStyle.titleTextStyle}>部位：{item.positionName?item.positionName:"无"}</Text>
              </View>
              <View style={CommonStyle.titleViewStyle}>
                <Text style={CommonStyle.titleTextStyle}>接收数量(块)：{item.acceptanceAmount}</Text>
              </View>
              <View style={CommonStyle.titleViewStyle}>
                <Text style={CommonStyle.titleTextStyle}>单重(Kg)：{item.pieceWeight}</Text>
              </View>
              <View style={CommonStyle.titleViewStyle}>
                <Text style={CommonStyle.titleTextStyle}>总重(吨)：{item.totalWeight}</Text>
              </View>
              <View style={CommonStyle.titleViewStyle}>
                <Text style={CommonStyle.titleTextStyle}>创建时间：{item.gmtCreated}</Text>
              </View>
  
              <View style={[CommonStyle.blockTwoEditDelStyle,{marginRight:15}]}>
                <TouchableOpacity onPress={()=>{
                  Alert.alert('确认','您确定要删除该接收记录吗？',[
                    {
                        text:"取消", onPress:()=>{
                        WToast.show({data:'点击了取消'});
                        // this在这里可用，传到方法里还有问题
                        // this.props.navigation.goBack();
                        }
                    },
                    {
                        text:"确定", onPress:()=>{
                            WToast.show({data:'点击了确定'});
                            this.deleteEngineeringAcceptance(item.acceptanceId)
                        }
                    }
                  ]);
                }}>
                  <View style={[CommonStyle.btnTwoDeleteBtnView]}>
                    <Image style={CommonStyle.btnTwoDeleteBtnImage} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                    <Text style={CommonStyle.btnTwoDeleteBtnText}>删除</Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={()=>{
                    this.props.navigation.navigate("EngineeringAcceptanceMgrAdd", 
                    {
                        //传递参数
                        acceptanceId:item.acceptanceId,
                        dataSource: this.state.dataSource,
                        orderId:item.orderId,
                        customerName:item.customerName,
                        positionName:item.positionName,
                        contractName:item.contractName,
                        //传递回调函数
                        refresh: this.callBackFunction 
                    })
                }}>
                  <View style={[CommonStyle.btnTwoEditBtnView]}>
                    <Image style={CommonStyle.btnTwoEditBtnImage} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                    <Text style={CommonStyle.btnTwoEditBtnText}>编辑</Text>
                  </View>
                </TouchableOpacity>
              </View>
          </View>
        )
      }

      space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0',marginHorizontal:16}}/>)
      }
  
      emptyComponent() {
          return <EmptyListComponent/>
      }

    render(){
        return(
            <View>
                <CommonHeadScreen title='工程接收'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        ItemSeparatorComponent={this.space}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
                    
                </View>
        )
    }
}
const styles = StyleSheet.create({

    innerViewStyle:{
        marginTop:10,
        // marginLeft:20
        // borderColor:"#F4F4F4",
        // borderWidth:14,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    }

});