import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class HLStorageOutMList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            storageOutFlag:'M',
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            completionStateDataSource:[],
             // 出库科室
             departmentId:"",
             toDepartmentId:"",
             selDepartmentId:null,
             selectDepartmentName:[],
             selDepartmentName:"",
             departmentDataSource:[],
             searchKeyWord:"",
             storageOutTypeDataSource:[
                {
                    typeCode:'all',
                    typeName:'全部',
                },
                {
                    typeCode:'use',
                    typeName:'领用出库',
                },
                {
                    typeCode:'other',
                    typeName:'其他',
                }
             ],
            selStorageOutTypeCode:"all"
 
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');        
        this.loadHlStorageOutList();
        this.loadDepartmentList();
    }
    loadDepartmentList=()=>{
        let url= "/biz/hl/department/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, (response)=>{
            if (response.code == 200 && response.data && response.data.dataList) {
                this.setState({
                    departmentDataSource: response.data.dataList,
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/hl/storage/out/storageOutList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "operator":constants.loginUser.userName,
            "userId":constants.loginUser.userId,
            "storageOutFlag":this.state.storageOutFlag,
            "searchKeyWord":this.state.searchKeyWord,
            "selStorageOutTypeCode":this.state.selStorageOutTypeCode === 'all' ? null : this.state.selStorageOutTypeCode,
            "toDepartmentId":this.state.selDepartmentId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/hl/storage/out/storageOutList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "operator":constants.loginUser.userName,
            "userId":constants.loginUser.userId,
            "storageOutFlag":this.state.storageOutFlag,
            "searchKeyWord":this.state.searchKeyWord,
            "selStorageOutTypeCode":this.state.selStorageOutTypeCode === 'all' ? null : this.state.selStorageOutTypeCode,
            "toDepartmentId":this.state.selDepartmentId
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadHlStorageOutList();
    }

    loadHlStorageOutList=()=>{
        let url= "/biz/hl/storage/out/storageOutList";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "operator":constants.loginUser.userName,
            "userId":constants.loginUser.userId,
            "storageOutFlag":this.state.storageOutFlag,
            "searchKeyWord":this.state.searchKeyWord,
            "toDepartmentId":this.state.selDepartmentId,
            "selStorageOutTypeCode":this.state.selStorageOutTypeCode === 'all' ? null : this.state.selStorageOutTypeCode
        };
        httpPost(url, loadRequest, this.loadHlStorageOutListCallBack);
    }

    loadHlStorageOutListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    deleteHlStorageOut =(storageOutId)=> {
        console.log("=======delete=storageOutId", storageOutId);
        let url= "/biz/hl/storage/out/delete";
        let requestParams={'storageOutId':storageOutId};
        httpDelete(url, requestParams, this.deleteCallBack);
    }

    // 删除操作的回调操作
    deleteCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"删除完成"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.storageOutId} style={styles.innerViewStyle}>

                {/* <View style={[CommonStyle.rightTop50FloatingBlockView,this.state.gmtCreated 
                    ? {borderRadius:3, width:null,height: 40, marginTop: 10,paddingLeft:15, paddingRight:15, opacity:0.5} : {}]}>
                    <TouchableOpacity onPress={()=>this.openGmtCreated()}>
                        <Text style={CommonStyle.rightTop50FloatingBlockText}>
                        {!this.state.gmtCreated ? "时间" : this.state.gmtCreated}
                        </Text>
                    </TouchableOpacity>
                </View> */}
                {/* <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.completionStateDataSource && this.state.completionStateDataSource.length > 0)
                                ?
                                this.state.completionStateDataSource.map((item, index) => {
                                    return this.chooseStateRow(item)
                                })
                                : <View />
                        }
                    </View>
                    <View style={{}}>
                        <View style={styles.inputOutsideText}>
                            <View style={styles.inputInsideText}>
                                <Image  style={{width:30, height:30,marginBottom:5}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'物资目录'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>                    
                </View> */}
                {/* <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        ItemSeparatorComponent={this.space}
                        ListEmptyComponent={this.emptyComponent}
                        renderItem={({ item }) => this.renderRow(item)}
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View> */}

                {/* <View style={styles.titleViewStyle}>
                    <Text style={[styles.titleTextStyle,constants.loginUser.userName == item.operator ? null : {width:screenWidth - 110}]}>原料类别：{item.parentClassifyName}</Text>
                    {
                        constants.loginUser.userName == item.operator ? 
                        null
                        :
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5,height:23, borderRadius:12, backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>
                            抄送
                        </Text>
                        
                    }
                </View>  */}

                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>出库科室：{item.departmentName}</Text>
                    <View style={{position:'absolute',right:0,top:0}}>
                        <Text style={{paddingTop:3, paddingBottom:3, paddingLeft:5, paddingRight:5, borderRadius:12, 
                            backgroundColor:'rgba(255,0,0,0.4)', color:'#FFFFFF'}}>{item.storageOutTypeName}</Text>
                    </View>
                </View>
                <View style={[styles.titleViewStyle]}>
                    <Text style={styles.titleTextStyle}>出库日期：{item.storageOutDate}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>经办人：{item.operator}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>运送人：{item.carrier}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>领用人：{item.recipient}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>单据备注：{item.receiptRemark?item.receiptRemark:"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>记账人：{item.bookKeeper?item.bookKeeper:"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>记账日期：{item.bookKeeper?(item.bookKeepingDate?item.bookKeepingDate:"无"):"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>审核人：{item.auditUserName?item.auditUserName : "无"}</Text>
                    <View>
                        {
                            item.auditState == "发起审核" || item.auditState == "审核中" ? 
                            <Text style={{color:'#FFB800'}}>{item.auditState}</Text> 
                            :
                            <View>
                            {
                                item.auditState == "审核通过" ? 
                                <Text style={{color:'green'}}>{item.auditState}</Text>
                                :
                                <Text style={{color:'#CB4139'}}>{item.auditState}</Text> 
                            }
                            </View>
                        }
                    </View>
                </View>
                {
                    item.auditState == "发起审核" ?
                    <View/>
                    :
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>审核日期：{item.auditDate}</Text>
                    </View>
                }
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>审核状态：{item.auditState}</Text>
                </View> */}
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                {
                        (item.auditState ==="审核通过" && constants.loginUser.userName == item.operator)?
                        <TouchableOpacity onPress={()=>{
                            if(item.storageOutState === '0AB'){
                                return
                            }
                            let url= "/biz/hl/storage/out/returnMAdd";
                            let loadRequest={
                                "storageOutId": item.storageOutId,
                                "sourceId": 2,
                                "operator":constants.loginUser.userName,
                            };
                            httpPost(url, loadRequest, (response)=>{
                                if (response.code == 200 && response.data) {
                                    this.callBackFunction()
                                }
                                else if (response.code == 401) {
                                    WToast.show({data:response.message});
                                    this.props.navigation.navigate("LoginView");
                                }
                            });
                        }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:95,marginLeft:0 ,flexDirection:'row'},(item.storageOutState === '0AB')? CommonStyle.disableViewStyle : ""]}>
                            <Image  style={{width:22, height:22,marginRight:1}} source={require('../../assets/icon/iconfont/giveBack.png')}></Image>
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>{item.storageOutState === '0AB'?"已归还":"归还入库"}</Text>
                            </View>
                        </TouchableOpacity>
                    :<View/>
                    }
                    <TouchableOpacity onPress={()=>{
                        this.props.navigation.navigate("HLStorageOutListDetail", 
                        {
                            // 传递参数
                            storageOutId:item.storageOutId,
                            
                            // 传递回调函数
                            refresh: this.callBackFunction 
                        })
                        }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {backgroundColor:"#3ab240",marginLeft:0,width: 70 ,flexDirection:"row"}]}>
                            <Image  style={{width:25, height:25,marginRight:2}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                            <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>明细</Text>
                        </View>
                    </TouchableOpacity>
                
                {
                    constants.loginUser.userName == item.operator?
                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                        
                        {
                            item.auditState == "发起审核"?
                            <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                                <TouchableOpacity onPress={()=>{
                                    this.props.navigation.navigate("HLStorageOutAuditDetail", 
                                    {
                                        // 传递参数
                                        auditItemId: item.storageOutId,
                                        // 传递回调函数
                                        refresh: this.callBackFunction 
                                    })
                                }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 70,marginLeft:0,flexDirection:"row"}]}>
                                    <Image  style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={()=>{
                                    Alert.alert('确认','您确定要删除该条入库记录吗？',[
                                        {
                                            text:"取消", onPress:()=>{
                                            WToast.show({data:'点击了取消'});
                                            // this在这里可用，传到方法里还有问题
                                            // this.props.navigation.goBack();
                                            }
                                        },
                                        {
                                            text:"确定", onPress:()=>{
                                                WToast.show({data:'点击了确定'});
                                                this.deleteHlStorageOut(item.storageOutId)
                                            }
                                        }
                                    ]);
                                }}>
                                    <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,{width:70,marginLeft:0,flexDirection:'row'}]}>
                                        <Image style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/delete.png')}></Image>
                                        <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={()=>{
                                        this.props.navigation.navigate("HLStorageOutMAdd", 
                                        {
                                            // 传递参数
                                            storageOutId:item.storageOutId,
                                            // 传递回调函数
                                            refresh: this.callBackFunction 
                                        })
                                    }}>
                                    <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:70,marginLeft:0,flexDirection:'row'}]}>
                                        <Image style={{width:20, height:20,marginRight:2}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                                        <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                                    </View>
                                </TouchableOpacity>

                            </View>
                            :
                            <TouchableOpacity onPress={()=>{
                                this.props.navigation.navigate("HLStorageOutAuditDetail", 
                                {
                                    // 传递参数
                                    auditItemId: item.storageOutId,
                                    // 传递回调函数
                                    refresh: this.callBackFunction 
                                })
                            }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 75,marginLeft:0,flexDirection:"row"}]}>
                                <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                    <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                                </View>
                            </TouchableOpacity>
                        }
                    </View>
                    :
                    <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                        <TouchableOpacity onPress={()=>{
                            this.props.navigation.navigate("HLStorageOutAuditDetail", 
                            {
                                // 传递参数
                                auditItemId: item.storageOutId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { width: 75 ,flexDirection:"row"}]}>
                                <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/detail.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={()=>{
                                let url= "/biz/audit/cc/record/modify";
                                let loadRequest={
                                    "recordId": item.ccRecordId,
                                    "ccRecordState":item.ccRecordState == '0AA'?"0AB":"0AA" ,
                                };
                                httpPost(url, loadRequest, (response)=>{
                                    if (response.code == 200 && response.data) {
                                        WToast.show({data:response.data.ccRecordState == '0AA'?"成功标为未读":"成功标为已读"});
                                        this.callBackFunction();
                                    }
                                    else if (response.code == 401) {
                                        WToast.show({data:response.message});
                                        this.props.navigation.navigate("LoginView");
                                    }
                                    else {
                                        WToast.show({data:response.message});
                                    }
                                });
                            }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:'row'},item.ccRecordState == '0AA'?{backgroundColor:'#FA353F'}:{backgroundColor:'#FFB800'}]}>
                                {
                                    item.ccRecordState == '0AA'?
                                    <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/unread.png')}></Image>
                                    :
                                    <Image style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/read.png')}></Image>
                                }
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>{item.ccRecordState == '0AA'?"未读":"已读"}</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                }
                </View>
                
            </View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLStorageOutMAdd", 
                {
                    storageOutId:this.state.storageOutId,
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                {/* <Text style={CommonStyle.headRightText}>新增模版</Text> */}
                <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
            </TouchableOpacity>
        )
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }
    searchByKeyWord=()=>{
        let loadUrl= "/biz/hl/storage/out/storageOutList";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "operator":constants.loginUser.userName,
            "userId":constants.loginUser.userId,
            "storageOutFlag":this.state.storageOutFlag,
            "searchKeyWord":this.state.searchKeyWord,
            "toDepartmentId":this.state.selDepartmentId,
            "selStorageOutTypeCode":this.state.selStorageOutTypeCode === 'all' ? null : this.state.selStorageOutTypeCode
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    // 渲染客户底部滚动数据
    openDepartmentName() {
        if (!this.state.departmentDataSource || this.state.departmentDataSource.length < 1) {
            WToast.show({ data: "请先添加科室" });
            return
        }
        this.refs.SelectDepartmentName.showDepartmentName(this.state.selectDepartmentName, this.state.departmentDataSource)
    }

    callBackSelectDepartmentNameValue(value) {
        console.log("==========科室选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectDepartmentName: value,
            // selDepartmentName:value.toString()
        })
        var departmentName = value.toString();
        let loadUrl = "/biz/hl/department/getDepartmentByName";
        let loadRequest = {
            "departmentName": departmentName
        };
        httpPost(loadUrl, loadRequest, this.callBackLoadDepartmentDetailData);
    }

    callBackLoadDepartmentDetailData = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                selDepartmentName: response.data.departmentName,
                selDepartmentId:response.data.departmentId,
            })
            let loadUrl= "/biz/hl/storage/out/storageOutList";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "operator":constants.loginUser.userName,
                "userId":constants.loginUser.userId,
                // "storageOutId": this.state.storageOutId,
                "storageOutFlag":this.state.storageOutFlag,
                "searchKeyWord":this.state.searchKeyWord,
                "toDepartmentId":response.data.departmentId,
                "selStorageOutTypeCode":this.state.selStorageOutTypeCode === 'all' ? null : this.state.selStorageOutTypeCode
            };
            httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({ data: response.message });
            this.setState({
                selDepartmentName: '',
                selDepartmentId: '',
            })
        }
    }

    renderStorageOutTypeRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    let selStorageOutTypeCode = item.typeCode;
                    this.setState({
                        selStorageOutTypeCode:selStorageOutTypeCode
                    })

                    let loadUrl= "/biz/hl/storage/out/storageOutList";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "operator":constants.loginUser.userName,
                        "userId":constants.loginUser.userId,
                        // "storageOutId": this.state.storageOutId,
                        "storageOutFlag":this.state.storageOutFlag,
                        "searchKeyWord":this.state.searchKeyWord,
                        "toDepartmentId":this.state.selDepartmentId,
                        "selStorageOutTypeCode":selStorageOutTypeCode === 'all' ? null : selStorageOutTypeCode
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.typeCode} style={[item.typeCode===this.state.selStorageOutTypeCode? [CommonStyle.selectedBlockItemViewStyle,{borderBottomWidth:2,borderBottomColor:"#CB4139"}] : [CommonStyle.blockItemViewStyle,{}],{paddingLeft:8,paddingRight:8}] }>
                        <Text style={[item.typeCode===this.state.selStorageOutTypeCode? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                            {item.typeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='物资出库'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.rightTop50FloatingBlockView,{top: 100,height:40,width:80,top:screenHeight/13,borderRadius:3 },this.state.selDepartmentName ? {borderRadius:3, width:null, paddingLeft:15, paddingRight:15, opacity:0.6} : {}]}>
                    <TouchableOpacity onPress={()=>this.openDepartmentName()}>
                        <Text style={CommonStyle.rightTop50FloatingBlockText}>
                        {!this.state.selDepartmentName ? "出库科室" : this.state.selDepartmentName}
                        </Text>
                    </TouchableOpacity>
                </View>
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>

                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.storageOutTypeDataSource && this.state.storageOutTypeDataSource.length > 0)
                            ?
                            this.state.storageOutTypeDataSource.map((item, index) => {
                                return this.renderStorageOutTypeRow(item)
                            })
                            : <View />
                        }
                    </View>
                    
                    <View style={{}}>
                        <View style={styles.inputOutsideText}>
                            <View style={styles.inputInsideText}>
                            <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'物资目录'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, {height:ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight)}]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
                <BottomScrollSelect 
                        ref={'SelectDepartmentName'} 
                        callBackDepartmentNameValue={this.callBackSelectDepartmentNameValue.bind(this)}
                    />
            </View>
        )
    }
}
const styles = StyleSheet.create({

    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },

    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        marginTop:5,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    inputOutsideText:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },
    inputInsideText:{
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    }

});