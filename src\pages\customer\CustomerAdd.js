import React,{Component} from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,TouchableOpacity,Dimensions,Picker,Image,KeyboardAvoidingView} from 'react-native';
import {WToast} from 'react-native-smart-tip'
import CommonHeadScreen from '../../component/CommonHeadScreen';
var CommonStyle = require('../../assets/css/CommonStyle');
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import CustomerList from './CustomerList';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
class CustomerAdd extends Component {
    constructor(props) {
        super(props);
        this.state ={
            operate:"",
            customerId:'',
            customerName:'',
            customerAddr:'',
            customerConcat:'',
            customerTel:'',
            tin:'',
            bankOfDeposit:'',
            bankAccount:'',
            bankAccountHolder:'',
        }
    }
    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { customerId } = route.params;
            if (customerId) {
                console.log("========Edit==customerId:", customerId);
                this.setState({
                    operate:"编辑",
                    customerId:customerId
                })
                loadTypeUrl= "/biz/tenant/customer/get";
                loadRequest={'customerId':customerId};
                httpPost(loadTypeUrl, loadRequest, this.callBackLoadCustomerData);
            }
            else{
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    callBackLoadCustomerData=(response)=>{
        if (response.code == 200 && response.data) {
            console.log("======load==edit=obj=", response.data);
            this.setState({
                customerId:response.data.customerId,
                customerName:response.data.customerName,
                customerAddr:response.data.customerAddr,
                customerConcat:response.data.customerConcat,
                customerTel:response.data.customerTel,
                tin:response.data.tin,
                bankOfDeposit:response.data.bankOfDeposit,
                bankAccount:response.data.bankAccount,
                bankAccountHolder:response.data.bankAccountHolder,
            })
            console.log("=======this.state", this.state);
        }
    }
    //头部左侧
    renderLeftItem() {
        return (
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={{ marginBottom: 1.5, flexDirection: 'row', alignItems: 'center'}}>
                    <Image style={{ width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')} />
                    <Text style={{ color: '#3C6CDE', marginLeft: 3, fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
                    {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                {/* <Text style={{ color: '#3C6CDE', marginLeft: 3, fontWeight:'bold'}}>返回</Text> */}
            </View>
        )
    }
    // 头部中间
    renderTitleItem() {
        return (
            <TouchableOpacity onPress={() => {  }}>
                <View>
                    <Text style={{fontWeight:'600'}}>新增订单排产</Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {}}>
            {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>客户管理</Text>
                {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
                </TouchableOpacity>
            </View>
        )
    }
    saveCustomer =()=> {
        console.log("=======saveCustomer");
        let toastOpts;
        if (!this.state.customerName) {
            toastOpts = getFailToastOpts("请输客户名称");
            WToast.show(toastOpts)
            return;
        }
        // if (!this.state.customerConcat) {
        //     toastOpts = getFailToastOpts("请输入联系人");
        //     WToast.show(toastOpts)
        //     return;
        // }
        // if (!this.state.customerTel) {
        //     toastOpts = getFailToastOpts("请输入联系电话");
        //     WToast.show(toastOpts)
        //     return;
        // }
        let url= "/biz/tenant/customer/add";
        if(this.state.customerId) {
            console.log("=========Edit===customerId", this.state.customerId)
            url= "/biz/tenant/customer/modify";
        }
        let requestParams={
            customerId:this.state.customerId,
            customerName:this.state.customerName,
            customerAddr:this.state.customerAddr,
            customerConcat:this.state.customerConcat,
            customerTel:this.state.customerTel,
            tin:this.state.tin,
            bankOfDeposit:this.state.bankOfDeposit,
            bankAccount:this.state.bankAccount,
            bankAccountHolder:this.state.bankAccountHolder,
        };
        httpPost(url, requestParams, this.saveCustomer_call_back);
    }

    // 保存回调函数
    saveCustomer_call_back=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
       return(
        <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]}  behavior="padding">
            <CommonHeadScreen title={this.state.operate + '客户'} titleStyle={{color: 'rgba(43, 51, 63, 1)', fontSize: 20, 
                                 fontWeight:'bold'}}
                leftItem={() => this.renderLeftItem()}
                rightItem={() => this.renderRightItem()}
            />
            <View style={CommonStyle.lineHeadBorderStyle} />
            <ScrollView style={CommonStyle.formContentViewStyle}>
            <View style={styles.inputRowStyle}>
                <View style={styles.leftLabView}>
                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>
                        客户名称
                    </Text>
                </View>
                <TextInput 
                    style={styles.inputRightText}
                    placeholder={'请输入'}
                    onChangeText={(text) => this.setState({customerName:text})}
                >
                    {this.state.customerName}
                </TextInput>
            </View>
            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
            <View style={styles.inputRowStyle}>
                <View style={styles.leftLabView}>
                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                    {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                </View>
                <TextInput 
                    style={styles.inputRightText}
                    placeholder={'请输入'}
                    onChangeText={(text) => this.setState({customerConcat:text})}
                >
                    {this.state.customerConcat}
                </TextInput>
            </View>
            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
            <View style={styles.inputRowStyle}>
                <View style={styles.leftLabView}>
                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>联系人电话</Text>
                    {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                </View>
                <TextInput 
                    keyboardType='numeric'
                    style={styles.inputRightText}
                    placeholder={'请输入'}
                    onChangeText={(text) => this.setState({customerTel:text})}
                >
                    {this.state.customerTel}
                </TextInput>
            </View>
            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
            <View style={styles.inputRowStyle}>
                <View style={styles.leftLabView}>
                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>地址</Text>
                    {/* <Text style={styles.leftLabRedTextStyle}></Text> */}
                </View>
                <TextInput 
                    style={styles.inputRightText}
                    placeholder={'请输入'}
                    onChangeText={(text) => this.setState({customerAddr:text})}
                >
                    {this.state.customerAddr}
                </TextInput>
            </View>
            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
            <View style={styles.inputRowStyle}>
                <View style={styles.leftLabView}>
                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>纳税人识别号</Text>
                </View>
                <TextInput 
                    style={styles.inputRightText}
                    placeholder={'请输入'}
                    onChangeText={(text) => this.setState({tin:text})}
                >
                    {this.state.tin}
                </TextInput>
            </View>
            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
            <View style={styles.inputRowStyle}>
                <View style={styles.leftLabView}>
                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>开户名</Text>
                </View>
                <TextInput 
                    style={styles.inputRightText}
                    placeholder={'请输入'}
                    onChangeText={(text) => this.setState({bankAccountHolder:text})}
                >
                    {this.state.bankAccountHolder}
                </TextInput>
            </View>
            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
            <View style={styles.inputRowStyle}>
                <View style={styles.leftLabView}>
                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>开户银行</Text>
                </View>
                <TextInput 
                    style={styles.inputRightText}
                    placeholder={'请输入'}
                    onChangeText={(text) => this.setState({bankOfDeposit:text})}
                >
                    {this.state.bankOfDeposit}
                </TextInput>
            </View>
            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0, marginLeft:15}} />
            <View style={styles.inputRowStyle}>
                <View style={styles.leftLabView}>
                    <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                    <Text style={styles.leftLabNameTextStyle}>账号</Text>
                </View>
                <TextInput 
                    keyboardType='numeric'
                    style={styles.inputRightText}
                    placeholder={'请输入'}
                    onChangeText={(text) => this.setState({bankAccount:text})}
                >
                    {this.state.bankAccount}
                </TextInput>
            </View>
            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: 0}} />
            <View style={{height:ifIphoneXContentViewHeight()-432-85, backgroundColor:'#F2F5FC'}}>
                        {/* <TextInput 
                            multiline={true}
                            textAlignVertical="top"
                            style={[CommonStyle.inputRowText,{height:100}]}
                        >
                        </TextInput> */}
            </View>
            <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                    <View style={CommonStyle.btnAddCancelBtnView} >
                    {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                        <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={this.saveCustomer.bind(this)}>
                    <View style={CommonStyle.btnAddSaveBtnView}>
                    {/* <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                        <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                    </View>
                </TouchableOpacity>
            </View>
        </ScrollView>
        </KeyboardAvoidingView>
       )

    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     backgroundColor:'#FFFFFF',
    //     height:screenHeight - 140
    // },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        // paddingTop:5,
        // paddingBottom:5,
        marginTop:4,
        marginBottom:4,
        marginLeft:15, 
        // borderTopWidth:1,
        // borderTopColor:'#F1F1F1',
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    inputRowStyle1:{
        height:45,
        flexDirection:'row',
        // marginTop:5,
        marginLeft:15, 
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:0,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'#E63633',
        marginLeft:4,
        marginRight:3
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:4,
        marginRight:3,
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#FFFFFF',
        // borderWidth:1,
        // borderBottomWidth: 1,
        // borderBottomColor: '#F1F1F1',
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
    },
    borderBlank:{
        borderWidth: 1, 
        borderColor: '#CCCCCC', 
        borderRadius: 5, 
        padding: 10, 
        margin: 10, 
    },
    textCertain: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#FFFFFF',
        lineHeight: 24,
        marginTop:10,
        textAlign: 'center',
        // fontStyle: 'normal',
    },
    textCancel: {
        // width: 34,
        // height: 24,
        // fontFamily: 'PingFangSC',
        // fontWeight: '400',
        fontSize: 18,
        color: '#404956',
        lineHeight: 24,
        marginTop:10,
        textAlign:'center'
        // fontStyle: 'normal',
    },
    textContainerCertain: {
        width: 180,
        height: 48,
        marginRight:8,
        backgroundColor: '#255BDA',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    textContainerCancel: {
        width: 180,
        height: 48,
        marginLeft:8,
        backgroundColor: '#FFFFFF',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#DFE3E8',
    },
    btnRowStyle: {
        flexDirection: 'row',
        margin: 10,
        justifyContent: 'space-between',
    },
})
module.exports = CustomerAdd;