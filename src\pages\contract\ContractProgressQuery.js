import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  Linking,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class ContractProgressQuery extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 6,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      searchKeyWord: '',
      topBlockLayoutHeight: 0,
      year: null,
      selYearsChooseName: '全部',
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    var currentDate = new Date();
    var year = currentDate.getFullYear();
    this.setState({
      year: year,
    });

    let yearsChooseDataSource = [
      {
        chooseCode: 'all',
        chooseName: '全部',
      },
      {
        chooseCode: 1,
        chooseName: year,
      },
      {
        chooseCode: 2,
        chooseName: year - 1,
      },
      {
        chooseCode: 3,
        chooseName: year - 2,
      },
      {
        chooseCode: 4,
        chooseName: year - 3,
      },
      // {
      //     chooseCode:5,
      //     chooseName:year-4,
      // }
    ];
    this.setState({
      yearsChooseDataSource: yearsChooseDataSource,
    });

    const {route, navigation} = this.props;
    if (route && route.params) {
      const {tenantId} = route.params;
      if (tenantId) {
        console.log('=============tenantId' + tenantId + '');
      }
    }
    this.loadContractProgressList();
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/contract/progress/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      qryStartYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.state.selYearsChooseName,
      qryEndYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.addOneYear(this.state.selYearsChooseName),
      searchKeyWord: this.state.searchKeyWord,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      searchKeyWord: null,
    });
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/contract/progress/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      qryStartYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.state.selYearsChooseName,
      qryEndYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.addOneYear(this.state.selYearsChooseName),
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      // var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      // var dataAll = [...dataNew];
      this.setState({
        dataSource: response.data.dataList,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  loadContractProgressList = () => {
    let url = '/biz/contract/progress/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      qryStartYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.state.selYearsChooseName,
      qryEndYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.addOneYear(this.state.selYearsChooseName),
    };
    httpPost(url, loadRequest, this.loadContractProgressListCallBack);
  };

  loadContractProgressListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadContractProgressList();
  };

  searchByKeyWord = () => {
    // let toastOpts;
    // if (!this.state.searchKeyWord) {
    //     toastOpts = getFailToastOpts("请输入客户名称或合同名称");
    //     WToast.show(toastOpts)
    //     return;
    // }
    // this.setState({
    //     brickTypeName:"",
    //     brickTypeId:null,
    // })

    let loadUrl = '/biz/contract/progress/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      searchKeyWord: this.state.searchKeyWord,
      qryStartYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.state.selYearsChooseName,
      qryEndYear:
        this.state.selYearsChooseName === '全部'
          ? null
          : this.addOneYear(this.state.selYearsChooseName),
    };
    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
  };

  exportPdfFile = (contractId) => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/contract_progress_detail';
    let requestParams = {
      contractId: contractId,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };
  exportPdfFile1 = (contractId) => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/contract_progress_detail_column';
    let requestParams = {
      contractId: contractId,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  renderRow = (item, index) => {
    return (
      <View key={item.contractId} style={styles.innerViewStyle}>
        <View style={styles.titleViewStyle}>
          <Text
            style={[
              styles.titleTextStyle,
              item.enterpriseName ? {width: screenWidth - 110} : null,
            ]}>
            合同名称：{item.contractName}
          </Text>
          {item.enterpriseName ? (
            <Text
              style={{
                paddingTop: 3,
                paddingBottom: 3,
                paddingLeft: 5,
                paddingRight: 5,
                height: 23,
                borderRadius: 12,
                backgroundColor: 'rgba(255,0,0,0.4)',
                color: '#FFFFFF',
              }}>
              {item.enterpriseAbbreviation
                ? item.enterpriseAbbreviation
                : item.enterpriseName}
            </Text>
          ) : null}
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            客户名称：{item.customerName}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            合同编号：{item.contractCode}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            合同重量：{item.contractWeight ? item.contractWeight + '吨' : '无'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            实结重量：{item.actualWeight ? item.actualWeight + '吨' : '无'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            合同金额：{item.contractAmount ? item.contractAmount : '无'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            实结金额：{item.actualAmount ? item.actualAmount : '无'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            已开票金额：
            {item.actualInvoiceTotalAmount
              ? item.actualInvoiceTotalAmount
              : '无'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            未开票金额：
            {item.contractAmount
              ? item.owedInvoiceTotalAmount
                ? item.owedInvoiceTotalAmount
                : '无'
              : '请添加合同金额'}
          </Text>
        </View>
        {item.materialInfoList != null && item.materialInfoList.length > 0 ? (
          <View style={[styles.titleViewStyle, {alignItems: 'center'}]}>
            <View style={{flexDirection: 'column'}}>
              {item.materialInfoList.map((elem, index) => {
                return (
                  <View
                    key={elem.detailId}
                    style={{
                      flexDirection: 'row',
                      width: screenWidth - 134 + 90,
                      marginBottom: 5,
                    }}>
                    <View style={{width: screenWidth - 230 + 40}}>
                      <Text style={CommonStyle.bodyTextStyle}>
                        产品材质：{elem.materialName}
                      </Text>
                    </View>
                    <View
                      style={{
                        width: 90 + 50,
                        marginLeft: 5,
                        justifyContent: 'center',
                      }}>
                      <Text style={CommonStyle.bodyTextStyle}>
                        所属重量(吨)：{(elem.materialWeight * 1).toFixed(2)}
                      </Text>
                    </View>
                  </View>
                );
              })}
            </View>
          </View>
        ) : (
          <View />
        )}

        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            已生产吨位：{item.semiFinishedWeight ? item.semiFinishedWeight : 0}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            正品吨位：{item.goodsWeight ? item.goodsWeight : 0}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            发运吨位：{item.storageOutWeight ? item.storageOutWeight : 0}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            未发运吨位：
            {item.contractWeight
              ? item.storageOutOwedWeight
                ? item.storageOutOwedWeight
                : 0
              : '请添加合同重量'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            未生产吨位：
            {item.contractWeight
              ? item.semiFinishedOwedWeight
                ? item.semiFinishedOwedWeight
                : 0
              : '请添加合同重量'}
          </Text>
        </View>
        <View style={[styles.titleViewStyle]}>
          <Text style={styles.titleTextStyle}>
            签订时间：{item.signingTime ? item.signingTime : '无'}
          </Text>
        </View>

        <View style={[CommonStyle.itemBottomBtnStyle, {flexWrap: 'wrap'}]}>
          <TouchableOpacity
            onPress={() => {
              this.props.navigation.navigate('ContractProgressDetail', {
                contractId: item.contractId,
              });
            }}>
            <View
              style={[
                CommonStyle.itemBottomDetailBtnViewStyle,
                {backgroundColor: '#3ab240', width: 75, flexDirection: 'row'},
              ]}>
              <Image
                style={{width: 25, height: 25, marginRight: 3}}
                source={require('../../assets/icon/iconfont/detail1.png')}></Image>
              <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              Alert.alert('确认', '您确定要导出PDF文件吗？', [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.exportPdfFile(item.contractId);
                  },
                },
              ]);
            }}>
            <View
              style={[
                styles.itemOutputPdfStyle,
                {backgroundColor: '#F2C16D', width: 85, flexDirection: 'row'},
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 5}}
                source={require('../../assets/icon/iconfont/output.png')}></Image>
              <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                进度单
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              Alert.alert('确认', '您确定要导出PDF文件吗？', [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.exportPdfFile1(item.contractId);
                  },
                },
              ]);
            }}>
            <View
              style={[
                styles.itemOutputDetailPdfStyle,
                {
                  backgroundColor: '#F2C16D',
                  width: 85,
                  flexDirection: 'row',
                  marginLeft: 0,
                },
              ]}>
              <Image
                style={{width: 20, height: 20, marginRight: 5}}
                source={require('../../assets/icon/iconfont/output.png')}></Image>
              <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>
                明细表
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };

  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  // 头部左侧
  renderLeftItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.goBack();
        }}
        style={[{marginBottom: 1.5}]}>
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        <Image
          style={{width: 22, height: 22}}
          source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      </TouchableOpacity>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('ContractProgressDetail', {
            // 传递回调函数
            refresh: this.callBackFunction,
          });
        }}>
        <Text style={CommonStyle.headRightText}></Text>
      </TouchableOpacity>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  addOneYear = (year) => {
    var date = new Date(year, 10, 24, 10, 33, 0, 0);
    var addYear = date.getFullYear() + 1;
    return addYear;
  };

  yearsChooseStateRow = (item, index) => {
    return (
      <View key={item.chooseCode}>
        <TouchableOpacity
          onPress={() => {
            var selYearsChooseName = item.chooseName;
            this.setState({
              selYearsChooseName: selYearsChooseName,
            });

            let url = '/biz/contract/progress/list';
            let loadRequest = {
              currentPage: 1,
              pageSize: this.state.pageSize,
              searchKeyWord: this.state.searchKeyWord,
              qryStartYear:
                selYearsChooseName === '全部' ? null : selYearsChooseName,
              qryEndYear:
                selYearsChooseName === '全部'
                  ? null
                  : this.addOneYear(selYearsChooseName),
            };
            // console.log("selYearsChooseName+1:"+ this.addOneYear(selYearsChooseName))
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
          }}>
          <View
            key={item.chooseCode}
            style={[
              item.chooseName === this.state.selYearsChooseName
                ? CommonStyle.selectedBlockItemViewStyle
                : CommonStyle.blockItemViewStyle,
              {paddingLeft: 8, paddingRight: 8},
            ]}>
            <Text
              style={[
                item.chooseName === this.state.selYearsChooseName
                  ? CommonStyle.selectedBlockItemTextStyle16
                  : CommonStyle.blockItemTextStyle16,
                {fontWeight: 'bold'},
              ]}>
              {item.chooseName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="合同进度"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[styles.innerViewStyle, {marginTop: 0}]}
          onLayout={this.topBlockLayout.bind(this)}>
          <View
            style={{
              marginTop: 0,
              index: 1000,
              flexWrap: 'wrap',
              flexDirection: 'row',
            }}>
            {this.state.yearsChooseDataSource &&
            this.state.yearsChooseDataSource.length > 0 ? (
              this.state.yearsChooseDataSource.map((item, index) => {
                return this.yearsChooseStateRow(item);
              })
            ) : (
              <View />
            )}
          </View>

          <View style={{}}>
            <View style={styles.inputRowStyle}>
              <View style={styles.leftLabView}>
                <Image
                  style={{width: 25, height: 25}}
                  source={require('../../assets/icon/iconfont/search.png')}></Image>
              </View>
              <TextInput
                style={[styles.searchInputText, {}]}
                returnKeyType="search"
                returnKeyLabel="搜索"
                onSubmitEditing={(e) => {
                  this.searchByKeyWord();
                }}
                placeholder={'客户/合同/签订时间'}
                onChangeText={(text) => this.setState({searchKeyWord: text})}>
                {this.state.searchKeyWord}
              </TextInput>
            </View>
          </View>
        </View>
        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.contractId}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  inputRowStyle: {
    paddingLeft: 5,
    height: 40,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    backgroundColor: '#FFFFFF',
    borderRadius: 5,
    marginTop: 5,
  },

  leftLabView: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  leftLabNameTextStyle: {
    fontSize: 18,
  },
  searchInputText: {
    width: screenWidth - 100,
    borderColor: '#000000',
    // borderBottomWidth: 1,
    marginRight: 5,
    color: '#A0A0A0',
    fontSize: 16,
    marginLeft: 10,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 0,
    paddingTop: 0,
  },

  innerViewStyle: {
    marginTop: 10,
    borderColor: '#F4F4F4',
    borderWidth: 14,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  // itemContentViewStyle:{
  //     flexDirection:'row',
  //     justifyContent:'space-between',
  //     marginLeft:25
  // },
  itemContentChildViewStyle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  itemOutputPdfStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 0,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: '#5F9EA0',
    borderRadius: 4,
  },
  itemOutputDetailPdfStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 0,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: '#DEB887',
    borderRadius: 4,
  },
});
