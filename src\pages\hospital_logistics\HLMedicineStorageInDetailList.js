import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class HLMedicineStorageInDetailList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1
        }
    }
    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }
    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
        }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageInId } = route.params;
            if (storageInId) {
                this.loadHLMedicineStorageInDetailList(storageInId);
                console.log("=============storageInId" + storageInId + "");
                this.setState({
                    storageInId:storageInId, 
                })
            }
        }
    }
    loadHLMedicineStorageInDetailList=(storageInId)=>{
        let loadUrl= "/biz/hl/medicine/storage/in/detail/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "storageInId": storageInId ? storageInId : this.state.storageInId,
            
        };
        console.log("===========loadRequestssss:", loadRequest);
        httpPost(loadUrl, loadRequest, this.loadHLMedicineStorageInDetailListCallBack);
    }
    loadHLMedicineStorageInDetailListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    // 回调函数
    /** 
    callBackFunction=()=>{
        let loadUrl= "/biz/hl/medicine/storage/in/detail/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "storageInId": this.state.storageInId,
            "storageInId":this.state.storageInId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }*/

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/hl/storage/in/detail/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "storageInId": this.state.storageInId,
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }
    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadStorageInListDetail();
    }
    renderRow=(item, index)=>{
        return (
            <View key={item.storageInId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>名称：{item.medicineName}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>规格型号：{item.medicineSpec ? item.medicineSpec : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>单位：{item.unitName ? item.unitName : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>数量：{item.amount}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>指导总价：{item.guidingPrice}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>零售总价：{item.salePrice ? item.salePrice : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>送货单号：{item.deliveryNumber ? item.deliveryNumber : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>资金来源：{item.fundSource ? item.fundSource: "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>发票号：{item.invoiceNo ? item.invoiceNo : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>发票日期：{item.invoiceDate}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>生产地址：{item.productionAddr ? item.productionAddr : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>生产批号：{item.productionBatchNumber ? item.productionBatchNumber : "无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>生产日期：{item.productDate}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>失效日期：{item.productExpDate}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>灭菌批号：{item.sterilizationBatchNumber ? item.sterilizationBatchNumber : "无"}</Text>
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle, {flexWrap:'wrap'}]}>
                    {/* <TouchableOpacity onPress={()=>{
                        Alert.alert('确认','您确定要删除该状态吗？',[
                            {
                                text:"取消", onPress:()=>{
                                WToast.show({data:'点击了取消'});
                                }
                            },
                            {
                                text:"确定", onPress:()=>{
                                    WToast.show({data:'点击了确定'});
                                    this.deleteStateRecord(item.recordId)
                                }
                            }
                        ]);
                    }}>
                        <View style={[CommonStyle.itemBottomDeleteBtnViewStyle
                        // ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ]}>
                            <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>删除</Text>
                        </View>
                    </TouchableOpacity> */}
                    {/* <TouchableOpacity onPress={()=>{
                            // if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                            //     return;
                            // }
                            this.props.navigation.navigate("EquipmentStateMgrAdd", 
                            {
                                // 传递参数
                                recordId:item.recordId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                         <View style={[CommonStyle.itemBottomEditBtnViewStyle,{width:80,flexDirection:"row"}]}>
                        <Image  style={{width:20, height:20,marginRight:5}} source={require('../../assets/icon/iconfont/edit.png')}></Image>
                            <Text style={CommonStyle.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity> */}
                </View>
            </View>
        )
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} >
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }
    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }
    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title='入库明细'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
        //     height:screenHeight - 70,
        //     backgroundColor:'#FFFFFF'
        // },
        innerViewStyle:{
            marginTop:10,
            borderColor:"#F4F4F4",
            borderWidth:14,
        },
        titleViewStyle:{
            flexDirection:'row',
            justifyContent:'space-between',
            marginLeft:10,
            marginRight:10,
            marginBottom:5,
            marginTop:5,
        },
        titleTextStyle:{
            fontSize:16
        },
        itemContentStyle:{
            flexDirection:'row',
            alignItems:'center'
        },
        itemContentImageStyle:{
            width:120,
            height:120
        },
        itemContentViewStyle:{
            flexDirection:'row',
            justifyContent:'space-between',
            marginLeft:25
        },
        itemContentChildViewStyle:{
            flexDirection:'column'
        },
        itemContentChildTextStyle:{
            marginLeft:10,
            marginTop:15,
            fontSize:16
        },
    });