import moment from 'moment/moment';
import React from 'react';
import {Image, Platform} from 'react-native';
import {WToast} from 'react-native-smart-tip';

constants = {
  // 商用环境接口服务
  service_addr: 'http://jznc.njjzgk.com',
  // 测试环境
  // service_addr:'http://open.api.airclub.xin',
  //   service_addr: 'http://**************:8085',
  //   service_addr: 'http://*************:8085',
  // service_addr:'http://************:8085',
  // 图片服务器
  image_addr: 'http://image.njjzgk.com',
  // 登录信息
  loginUser: null,
  // 登录人的角色信息
  roleInfo: null,
  // 当前时间
  currentTime: null,
  // 几小时之内能编辑&删除，单位小时，默认68小时
  editDeleteTimeLimit: 168,
  // 租户定制属性值JSON
  tenantExtAttrJSON: null,
  // 当前版本号
  currentVersion: '1.0.11',
  // App应用名称
  appName: '极致耐材',
  // 隐私服务地址
  privacyUrl: 'http://jznc.njjzgk.com/html/privacy/jznc_privacy.html',
  // 用户服务协议
  userAgreementUrl:
    'http://jznc.njjzgk.com/html/user-agreement/jznc_user_agreement.html',
  // 微信公共平台AppId-极致耐材
  wechatShareAppId: 'wxce74d29c167a7494',
  wechatUniversalLink: 'https://jznc.njjzgk.com/openapp/',
  // 私域运营服务器
  websit_server_Addr: 'https://jzxs.njjzgk.com/html/memberQuery',
};

// 判断当前平台：ios/android
platformos = Platform.OS;

// POST请求-请求参数：JSON
httpPost = (url, data, callback) => {
  var fetchOptions = {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      //json形式
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  };
  fetch(constants.service_addr + url, fetchOptions)
    .then((response) => response.text())
    .then((responseText) => {
      console.log('====responseText:', responseText);
      // return responseText;
      callback(JSON.parse(responseText));
    })
    .catch((error) => {
      // console.log("====error.name", error.name);  //访问错误类型
      // console.log("=====error.message", error.message);  //访问错误详细信息
      if (error.message === 'Network request failed') {
        if (
          url === '/biz/user/get_latest_login_record' ||
          url === '/biz/version/get_latest_version'
        ) {
          console.warn('========', '网络异常，请稍后再试...');
        } else {
          toastOpts = getFailToastOpts('网络异常，请稍后再试...');
          WToast.show(toastOpts);
        }
      } else {
        toastOpts = getFailToastOpts(
          '接口返回失败，请稍后再试......' + error.message,
        );
        WToast.show(toastOpts);
      }
    })
    .done();
};

// Delete请求-请求参数：JSON
httpDelete = (url, data, callback) => {
  var fetchOptions = {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      //json形式
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  };
  fetch(constants.service_addr + url, fetchOptions)
    .then((response) => response.text())
    .then((responseText) => {
      console.log('====responseText:', responseText);
      // return responseText;
      callback(JSON.parse(responseText));
    })
    .catch((error) => {
      if (error.message === 'Network request failed') {
        toastOpts = getFailToastOpts('网络异常，请稍后再试...');
        WToast.show(toastOpts);
      } else {
        toastOpts = getFailToastOpts(
          '接口返回失败，请稍后再试......' + error.message,
        );
        WToast.show(toastOpts);
      }
    })
    .done();
};

// GET请求
httpGet = (url, callback) => {
  // url = constants.service_addr + url;
  fetch(constants.service_addr + url, {method: 'GET'})
    .then((response) => response.text())
    .then((responseText) => {
      console.log('====responseText:', responseText);
      // return responseText;
      callback(JSON.parse(responseText));
    })
    .catch((error) => {
      if (error.message === 'Network request failed') {
        toastOpts = getFailToastOpts('网络异常，请稍后再试...');
        WToast.show(toastOpts);
      } else {
        toastOpts = getFailToastOpts(
          '接口返回失败，请稍后再试......' + error.message,
        );
        WToast.show(toastOpts);
      }
    })
    .done();
};

// 成功样式的消息提示
getSuccessToastOpts = (message) => {
  const toastOpts = {
    data: message,
    textColor: '#ffffff',
    backgroundColor: '#444444',
    duration: WToast.duration.LONG, //1.SHORT 2.LONG
    position: WToast.position.CENTER, // 1.TOP 2.CENTER 3.BOTTOM
    icon: (
      <Image
        source={require('../assets/icon/success.png')}
        style={{width: 32, height: 32, resizeMode: 'contain'}}
      />
    ),
  };
  return toastOpts;
};

// 失败样式的消息提示
getFailToastOpts = (message) => {
  const toastOpts = {
    data: message,
    textColor: '#ffffff',
    backgroundColor: '#444444',
    duration: WToast.duration.LONG, //1.SHORT 2.LONG
    position: WToast.position.CENTER, // 1.TOP 2.CENTER 3.BOTTOM
    icon: (
      <Image
        source={require('../assets/icon/error.png')}
        style={{width: 20, height: 20, resizeMode: 'contain'}}
      />
    ),
  };
  return toastOpts;
};

// 必填项样式的消息提醒
requiredReminder = (message) => {
  const toastOpts = {
    data: message,
    textColor: 'rgba(255, 255, 255, 1)',
    backgroundColor: '#444444',
    duration: WToast.duration.LONG, //1.SHORT 2.LONG
    position: WToast.position.CENTER, // 1.TOP 2.CENTER 3.BOTTOM
    borderRadius: 10,
    width: 290,
    // resizeMode: 'contain',
  };
  return toastOpts;
};

isNumber = (val) => {
  var regPos = /^\d+$/; // 非负整数
  var regNeg = /^\-[1-9][0-9]*$/; // 负整数
  if (regPos.test(val) || regNeg.test(val)) {
    return true;
  } else {
    return false;
  }
};

isMobileNumber = (val) => {
  //11位手机号码正则
  var reg_tel =
    /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
  if (reg_tel.test(val)) {
    return true;
  } else {
    return false;
  }
};

arrayItemIndexOf = (array, item) => {
  for (var i = 0; i < array.length; i++) {
    if (array[i] == item) return i;
  }
  return -1;
};
// 从数组中移除某个子项
arrayRemoveItem = (array, item) => {
  var index = arrayItemIndexOf(array, item);
  if (index > -1) {
    array.splice(index, 1);
  }
};
// Copy一个数组对象生成
copyArr = (varArray) => {
  let res = [];
  for (let i = 0; i < varArray.length; i++) {
    res.push(varArray[i]);
  }
  return res;
};
// 是否包含中文
contantChinese = (valueString) => {
  if (/.*[\u4e00-\u9fa5]+.*$/.test(valueString)) {
    return true;
  }
  return false;
};

/***
 * 计算两个时间相差多少小时
 * 示例
 * sDate1：2019-11-20 17:22:23
 * sDate2：2019-11-20 19:22:23
 * 返回 2
 */
dateDiffHours = (sDate1, sDate2) => {
  var timeSpan = dateDiff(sDate1, sDate2);
  if (timeSpan == null || timeSpan.Hours == null) {
    return 0;
  }
  return timeSpan.TotalHours;
};
// sDate1和sDate2是字符串 yyyy-MM-dd HH:mm:ss 格式
dateDiff = (sDate1, sDate2) => {
  var oDate1, oDate2;
  // oDate1 = new Date(sDate1).getTime();
  // oDate2 = new Date(sDate2).getTime();
  oDate1 = moment(sDate1).toDate().getTime();
  oDate2 = moment(sDate2).toDate().getTime();
  var timeSpan = {};
  //相差的毫秒数
  var TotalMilliseconds = 0;
  if (oDate1 > oDate2) {
    TotalMilliseconds = oDate1 - oDate2;
  } else {
    TotalMilliseconds = oDate2 - oDate1;
  }
  if (isNaN(TotalMilliseconds) || TotalMilliseconds < 0) {
    return null;
  }
  timeSpan.Days = parseInt(TotalMilliseconds / 1000 / 60 / 60 / 24);
  timeSpan.TotalHours = parseInt(TotalMilliseconds / 1000 / 60 / 60) + '';
  timeSpan.Hours = timeSpan.TotalHours % 24;
  timeSpan.TotalMinutes = parseInt(TotalMilliseconds / 1000 / 60);
  timeSpan.Minutes = (timeSpan.TotalMinutes % 60) + '';
  timeSpan.TotalSeconds = parseInt(TotalMilliseconds / 1000);
  timeSpan.Seconds = timeSpan.TotalSeconds % 60;
  timeSpan.TotalMilliseconds = TotalMilliseconds;
  timeSpan.Milliseconds = TotalMilliseconds % 1000;
  return timeSpan;
};

double2StringFormat = (doubleValue) => {
  let doubleString = '';
  if (doubleValue == null) {
    return doubleString;
  }
  doubleString = doubleValue.toFixed(2);
  while (stringEndWith(doubleString, '0')) {
    doubleString = doubleString.substring(0, doubleString.length - 1);
  }
  while (stringEndWith(doubleString, '.')) {
    doubleString = doubleString.substring(0, doubleString.length - 1);
  }
  return doubleString;
};

stringEndWith = (stringValue, endParam) => {
  if (
    endParam == null ||
    endParam == '' ||
    endParam.length == 0 ||
    endParam.length > stringValue.length
  ) {
    return false;
  } else if (
    stringValue.substring(stringValue.length - endParam.length) == endParam
  ) {
    return true;
  } else {
    return false;
  }
};
