import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Image,Clipboard,Linking,
    FlatList,RefreshControl
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WebView } from 'react-native-webview';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class HelpCenterList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='帮助中心'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                <TouchableOpacity onPress={()=>{
                        let netWorkShow = constants.privacyUrl;
                        let netWork = netWorkShow;
                        // if (contantChinese(netWorkShow)) {
                        //     netWork = netWorkShow + '&contractXY=' + item.contractId;
                        // }
                        Clipboard.setString(netWork);
                        Linking.openURL(netWork)
                    }}>
                        <View style={[styles.innerViewStyle,]}>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>隐私服务</Text>
                            </View>
                            <View style={{width: 30, height: 30, marginBottom:5,backgroundColor: 'rgba(255,0,0,0.0)', 
                                position:'absolute', alignItems:'center',justifyContent:'center', right: 20,bottom:0}}>
                                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/enter4.png')}></Image>
                            </View>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>{
                        let netWorkShow = constants.userAgreementUrl;
                        let netWork = netWorkShow;
                        // if (contantChinese(netWorkShow)) {
                        //     netWork = netWorkShow + '&contractXY=' + item.contractId;
                        // }
                        Clipboard.setString(netWork);
                        Linking.openURL(netWork)
                    }}>
                        <View style={[styles.innerViewStyle,]}>
                            <View style={styles.titleViewStyle}>
                                <Text style={styles.titleTextStyle}>用户协议</Text>
                            </View>
                            <View style={{width: 30, height: 30, marginBottom:5,backgroundColor: 'rgba(255,0,0,0.0)', 
                                position:'absolute', alignItems:'center',justifyContent:'center', right: 20,bottom:0}}>
                                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/enter4.png')}></Image>
                            </View>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:10,
        marginTop:10,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});
