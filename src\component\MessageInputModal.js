import React, {Component} from 'react';
import {
  KeyboardAvoidingView,
  Modal,
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import CommonStyle from '../assets/css/CommonStyle';

export default class MessageInputModal extends Component {
  render() {
    const {visible, onClose, onSend, messageContent, onChangeMessageContent} =
      this.props;
    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={visible}
        onRequestClose={onClose}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{flex: 1}}
          keyboardVerticalOffset={Platform.OS == 'ios' ? 0 : 25}>
          <TouchableOpacity
            style={{flex: 1, position: 'relative'}}
            onPress={onClose}>
            <View
              style={{
                backgroundColor: '#FFFFFF',
                flexDirection: 'row',
                alignItems: 'center',
                position: 'absolute',
                width: '100%',
                left: 0,
                bottom: 0,
                padding: 5,
              }}>
              <TextInput
                autoFocus={true}
                multiline={true}
                placeholder="小小鼓励，让团队更凝聚"
                style={{
                  backgroundColor: '#F2F5FC',
                  flex: 5,
                  borderRadius: 15,
                  height: 60,
                  margin: 5,
                  paddingLeft: 10,
                  textAlignVertical: 'top',
                }}
                value={messageContent}
                onChangeText={onChangeMessageContent}
              />
              <TouchableOpacity onPress={onSend}>
                <View
                  style={[
                    CommonStyle.itemBottomDetailBtnViewStyle,
                    {
                      flex: 1,
                      width: 64,
                      height: 32,
                      backgroundColor: '#1E6EFA',
                      borderRadius: 20,
                    },
                    messageContent ? '' : CommonStyle.disableViewStyle,
                  ]}>
                  <Text
                    style={[
                      CommonStyle.itemBottomDetailBtnTextStyle,
                      {textAlign: 'center', fontSize: 14},
                    ]}>
                    发送
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </KeyboardAvoidingView>
      </Modal>
    );
  }
}
