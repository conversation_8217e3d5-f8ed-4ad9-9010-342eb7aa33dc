import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,Linking,
    FlatList,RefreshControl,Image,TextInput,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../../component/CommonHeadScreen';
import EmptyListComponent from '../../../component/EmptyListComponent';
import CustomListFooterComponent from '../../../component/CustomListFooterComponent';
import ProductEmptyRowViewComponent from '../../../component/ProductEmptyRowViewComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../../utils/ScreenUtil';
var CommonStyle = require('../../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
export default class ProductReleaseAskBuyAuditList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态',
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            searchKeyWord:"",
            productTypeList:[
                {
                    "typeId":0,
                    "typeName":"耐火制品",
                    "typeCode":"P"
                },
                {
                    "typeId":1,
                    "typeName":"耐火原料",
                    "typeCode":"M"
                }
            ],
            productType:"P",
            auditModal:false,
            auditDataSource:[],
            selAuditType:"all"

        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        let auditDataSource = [
            {
                auditType: 'all',
                auditTypeName: '全部',
            },
            {
                auditType: '2',
                auditTypeName: "待审核",
            },
            {
                auditType: '3',
                auditTypeName: "通过",
            },
            {
                auditType: '4',
                auditTypeName: "驳回"
            }
        ]
        this.setState({
            auditDataSource: auditDataSource,
        })

        this.loadAskBugReleaseList()
    }

    // 回调函数
    callBackFunction=()=>{
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": "P",
            "searchKeyWord":this.state.searchKeyWord,
            "productType":this.state.productType,
            "auditState":this.state.selAuditType,
            "excludeAuditState":"1",
            "searchAllTenant":"Y"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage:1
        })
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": "P",
            "searchKeyWord":this.state.searchKeyWord,
            "productType":this.state.productType,
            "auditState":this.state.selAuditType,
            "excludeAuditState":"1",
            "searchAllTenant":"Y"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadAskBugReleaseList();
    }

    loadAskBugReleaseList=()=>{
        let url= "/biz/product/release/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "releaseType": "P",
            "searchKeyWord":this.state.searchKeyWord,
            "productType":this.state.productType,
            "auditState":this.state.selAuditType,
            "excludeAuditState":"1",
            "searchAllTenant":"Y"
        };
        httpPost(url, loadRequest, this.loadAskBugReleaseListCallBack);
    }


    loadAskBugReleaseListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            console.log(dataNew)
            var dataOld = this.state.dataSource;
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    searchByKeyWord = () => {
        let loadUrl = "/biz/product/release/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "releaseType": "P",
            "searchKeyWord": this.state.searchKeyWord,
            "productType":this.state.productType,
            "auditState":this.state.selAuditType,
            "excludeAuditState":"1",
            "searchAllTenant":"Y"
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    setGood =(goodItem)=> {
        let requestUrl= "/biz/product/release/modify";
        let requestParams={
            'releaseId':goodItem.releaseId,
            'auditMarking':goodItem.auditMarking === "1" ? "3" : "1",
            'auditMarkingUserId':constants.loginUser.userId,
        };
        httpPost(requestUrl, requestParams, (response)=>{
            if (response.code == 200) {
                // 更新页面上显示
                goodItem.auditMarking = (goodItem.auditMarking === "1" ? "3" : "1");
                let newDataSource = this.state.dataSource;
                // JS 数组遍历
                newDataSource.forEach((obj)=>{
                    if (obj.releaseId === goodItem.releaseId) {
                        obj.auditMarking = goodItem.auditMarking;
                        WToast.show({data: "已"+(goodItem.auditMarking === "3" ? '取消精选' :'设为精选推荐')});
                    }
                })
                this.setState({
                    dataSource:newDataSource,
                })
            }
            else {
                WToast.show({data:response.message});
            }
        });    
        console.log("打标人：：：：："+this.state.auditMarkingUserId)
    }

    setSelf =(selfItem)=> {
        let requestUrl= "/biz/product/release/modify";
        let requestParams={
            'releaseId':selfItem.releaseId,
            'auditMarking':selfItem.auditMarking === "2" ? "3" : "2",
            'auditMarkingUserId':constants.loginUser.userId,
        };
        httpPost(requestUrl, requestParams, (response)=>{
            if (response.code == 200) {
                // 更新页面上显示
                selfItem.auditMarking = (selfItem.auditMarking === "2" ? "3" : "2");
                let newDataSource = this.state.dataSource;
                // JS 数组遍历
                newDataSource.forEach((obj)=>{
                    if (obj.releaseId === selfItem.releaseId) {
                        obj.auditMarking = selfItem.auditMarking;
                        WToast.show({data: "已"+(selfItem.auditMarking === "3" ? '取消自营设置' :'设为自营')});
                    }
                })
                this.setState({
                    dataSource:newDataSource,
                })
            }
            else {
                WToast.show({data:response.message});
            }
        });    
        // console.log("=======set自营=------Item", this.state.auditMarking);
        console.log("打标人：：：：："+ this.state.auditMarkingUserId)
    }

    renderRow=(item, index)=>{
        return (
            <View key={item.releaseId} style={styles.innerViewStyleOut}>
                {
                    index == 0 ?
                        <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>
                        :
                        <View></View>
                }
                <View key={item.releaseId} style={styles.innerViewStyle}>
                <View style={{display:'flex',flexDirection:'row',backgroundColor:'#FFF',alignItems:'center'}}>
                    <Text style={styles.titleTextStyle}>{item.productName}</Text>
                </View>

                <View style={{flexDirection:'row',display:'flex'}}>

                    <View style={{flexDirection:'column',marginTop:10}}>
                        <View style={{flexDirection:'row',marginBottom:5}}>
                            {
                                item.productModel ?
                                <Text style={styles.blackTextStyle}>型号：{item.productModel}</Text>
                                : <View/>
                            }
                            {/* <Text style={[item.productType == 'P' ?  [styles.blackTextStyle,{marginLeft:30}] :  [styles.blackTextStyle,{marginLeft:0}]]}>数量：{item.productWeight}吨</Text> */}
                        </View>
                        {
                            item.productMaterial ?
                            <View style={{marginBottom:5}}>
                                <Text style={styles.blackTextStyle}>材质：{item.productMaterial}</Text>
                            </View>
                            : <View/>
                        }
                        <View style={{flexDirection:'row',marginBottom:5}}>
                            <Text style={[styles.blackTextStyle,{marginLeft:0}]}>数量：{item.productWeight}吨</Text>
                        </View>
                        <View>
                            <Text style={styles.blackTextStyle}>期望产地：{item.productionAddr}</Text>
                        </View>

                    </View>

                    <View style={[item.productType=='P' ? {position:'absolute',right:10,alignItems:'center',top:26} : {position:'absolute',right:10,alignItems:'center',top:15}]  }>
                        <TouchableOpacity  onPress={() => {
                            let phone = item.salePersonTel;
                            if(phone == null){
                                WToast.show({data:'暂未添加联系人电话，请联系管理员！'});
                                return;
                            }
                            const url = `tel:${phone}`;
                            Linking.canOpenURL(url)
                            .then(supported => {
                                if (!supported) {
                                return Alert.alert('提示', `您的设备不支持该功能，请手动拨打 ${phone}`, [
                                    { text: '确定' }
                                ]);
                                }
                                return Linking.openURL(url);
                            })
                            .catch(err => WToast.show({data:`出错了：${err}`}));
                        }}>
                            <Image  style={{width:40, height:40}} source={require('../../../assets/icon/iconfont/tel.png')}></Image>
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={{marginTop:5}}>
                    <Text style={styles.blackTextStyle}>说明：{item.productExplain?item.productExplain:"无"}</Text>
                </View>
                <View style={{marginTop:5}}>
                    <Text style={styles.blackTextStyle}>发布日期：{item.releaseDate}</Text>
                </View>
                {
                    constants.loginUser.managerFlag == 'Y'?
                    <View>
                        <View style={{marginTop:5}}>
                            <Text style={styles.blackTextStyle}>企业名称：{item.enterpriseName ? item.enterpriseName : "无"}</Text>
                        </View>
                        <View style={{marginTop:5}}>
                            <Text style={styles.blackTextStyle}>接口人：{item.salePersonName ? item.salePersonName : "无"}</Text>
                        </View>
                        <View style={{marginTop:5}}>
                            <Text style={styles.blackTextStyle}>联系电话：{item.salePersonTel ? item.salePersonTel : "无"}</Text>
                        </View>
                    </View>
                    :
                    null
                }

                {
                    item.auditState == '3' || item.auditState == '4' ?
                    <View>
                        <View style={{marginTop:5}}>
                            <Text style={styles.blackTextStyle}>审核人：{item.auditUserName?item.auditUserName:"无"}</Text>
                        </View>
                        <View style={{marginTop:5}}>
                            <Text style={styles.blackTextStyle}>审核日期：{item.auditDate?item.auditDate:"无"}</Text>
                        </View>

                    </View>
                    :
                    null
                }
                <View style={{marginTop:5}}>
                    <Text style={[styles.blackTextStyle]}>审核结果：<Text style={{color:item.auditState == '3' ? "#3ab240" : (item.auditState == '4' ? '#CB4139' : '#559ff3')}}>{item.auditState == '3'? '通过' : (item.auditState == '4' ? '驳回' : '待审核')}</Text></Text>
                </View>
                {
                    item.auditMarking==="1" || item.auditMarking==="2"?
                    <View style={{marginTop:5}}>
                        <Text style={styles.blackTextStyle}>打标人：{item.auditMarkingUserName}</Text>
                    </View>
                    :
                    null
                }
                <View style={{width: 40, height: 40, 
                    backgroundColor: 'rgba(255,0,0,0.0)', 
                    position:'absolute', 
                    alignItems:'center',
                    justifyContent:'center',
                    right: 10,
                    top:250,
                    }}>
                    {
                        item.auditMarking==="1" ? 
                        <Image style={[{width:90, height:25}]} source={require('../../../assets/icon/iconfont/goodRecommend.png')}></Image>
                        :
                        <View>
                        {
                            item.auditMarking==="2" ?
                            <Image style={[{width:55, height:25}]} source={require('../../../assets/icon/iconfont/selfSupport.png')}></Image>
                            :
                            null
                        }
                        </View>
                    }
                </View>
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                    {
                        item.auditState != "3" ?
                        null
                        :
                        <TouchableOpacity onPress={()=>{this.setGood(item);}}>
                            {
                                item.auditMarking==="2"?
                                <View style={ [CommonStyle.itemBottomDeleteBtnViewStyle,CommonStyle.disableViewStyle,{width:80 ,flexDirection:"row"}] }>
                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>精选推荐</Text>
                                </View>
                                :
                                <View style={[CommonStyle.itemAgreeBtnViewStyle,{borderColor:"#FFC8AA",width:80 ,flexDirection:"row"}]}>
                                    <Text style={[CommonStyle.itemBottomDetailBtnTextStyle,{color:"#FFC8AA"}]}>{item.auditMarking==="3" || item.auditMarking==="2" ? "精选推荐" : "取消精选"}</Text>
                                </View>
                            }
                        </TouchableOpacity>
                    }
                    {
                        item.auditState != "3" ?
                        null
                        :
                        <TouchableOpacity onPress={()=>{this.setSelf(item);}}>
                            {
                                item.auditMarking==="1"?
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle,CommonStyle.disableViewStyle,{width:80 ,flexDirection:"row"}]}>
                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>自营</Text>
                                </View>
                                :
                                <View style={[CommonStyle.itemAgreeBtnViewStyle,{borderColor:"#EB825A",width:80 ,flexDirection:"row"}]}>
                                    <Text style={[CommonStyle.itemBottomDetailBtnTextStyle,{color:"#EB825A"}]}>{item.auditMarking==="3"|| item.auditMarking==="1"  ? "自营" : "取消自营"}</Text>
                                </View>
                            }
                        </TouchableOpacity>
                    }
                    
                    
                    {
                        (item.auditState === "3" || item.auditState === "4") ?
                        null
                        :
                        <TouchableOpacity onPress={()=>{
                            if(item.auditState === "3" || item.auditState === "4"){
                                WToast.show({data:'已审核，无法修改！'});
                                return;
                            }
                            else{
                                Alert.alert('确认','您确定通过此条求购请求吗？',[
                                    {
                                        text:"取消", onPress:()=>{
                                        WToast.show({data:'点击了取消'});
                                        }
                                    },
                                    {
                                        text:"确定", onPress:()=>{
                                            WToast.show({data:'点击了确定'});
                                            this.acceptItem(item)
                                        }
                                    }
                                ])
                            }
                        }}>
                            <View style={[(item.auditState === "3" || item.auditState === "4") ? [CommonStyle.itemBottomDeleteBtnViewStyle,CommonStyle.disableViewStyle] : [CommonStyle.itemAgreeBtnViewStyle],{width: 60 ,flexDirection:"row"}]}>
                                <Text style={(item.auditState === "3" || item.auditState === "4") ? CommonStyle.itemBottomDeleteBtnTextStyle : [CommonStyle.itemBottomDetailBtnTextStyle,{color:"#3ab240"}]}>通过</Text>
                            </View>
                        </TouchableOpacity>
                    }
                    {
                        (item.auditState === "3" || item.auditState === "4") ?
                        null
                        :
                        <TouchableOpacity onPress={()=>{
                            if(item.auditState === "3" || item.auditState === "4"){
                                WToast.show({data:'已审核，无法修改！'});
                                return;
                            }
                            else{
                                Alert.alert('确认','您确定驳回此条求购请求吗？',[
                                    {
                                        text:"取消", onPress:()=>{
                                        WToast.show({data:'点击了取消'});
                                        }
                                    },
                                    {
                                        text:"确定", onPress:()=>{
                                            WToast.show({data:'点击了确定'});
                                            this.refuseItem(item)
                                        }
                                    }
                                ])
                            }
                        }}>
                            <View style={[(item.auditState === "3" || item.auditState === "4") ? [CommonStyle.itemBottomDeleteBtnViewStyle,CommonStyle.disableViewStyle] : [CommonStyle.itemRejectBtnViewStyle],{width:60 ,flexDirection:"row"}]}>
                                <Text style={(item.auditState === "3" || item.auditState === "4") ? CommonStyle.itemBottomDeleteBtnTextStyle : [CommonStyle.itemBottomEditBtnTextStyle,{color:'#CB4139'}]}>驳回</Text>
                            </View>
                        </TouchableOpacity>
                    }
                    
                    
                </View>
                </View>
            </View>
        )
    }

    productTypeRow=(item, index)=>{
        return (
            <View key={item.typeId} >
                <TouchableOpacity onPress={()=>{
                    var typeCode = item.typeCode;
                    this.setState({
                        productType:typeCode
                    })

                    let url= "/biz/product/release/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "releaseType": "P",
                        "searchKeyWord":this.state.searchKeyWord,
                        "productType":typeCode,
                        "auditState":this.state.selAuditType,
                        "excludeAuditState":"1",
                        "searchAllTenant":"Y"
                    };
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.typeCode} style={[item.typeCode===this.state.productType? [CommonStyle.selectedBlockItemViewStyle,{backgroundColor:'#FFF',borderBottomWidth:2,borderBottomColor:'#255BDA',borderRadius:0,paddingBottom:0,marginBottom:0}] : CommonStyle.blockItemViewStyle,{paddingLeft:8,backgroundColor:'#FFF',paddingBottom:0,marginBottom:0.8}]}>
                        <Text style={[item.typeCode===this.state.productType? styles.selectedBlockItemTextStyle : styles.blockItemTextStyle]}>
                            {item.typeName}
                        </Text>
                    </View>
                </TouchableOpacity>
        </View>
        )
    }

    acceptItem=(item)=>{
        if(item.auditState === '3'){
            return;
        }
        else{
            let url= "/biz/product/release/modify";
            var currentDate = new Date();
            var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
            var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
            let requestParams={
                releaseId:item.releaseId,
                auditState:"3",
                auditDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                currentAuditUserId:constants.loginUser.userId,
            };
            httpPost(url, requestParams, this.saveCollectionCallBack);
        }
        console.log("审核状态：：：：："+item.auditState)
    }

    refuseItem=(item)=>{
        if(item.auditState === '4'){
            return;
        }
        else{
            let url= "/biz/product/release/modify";
            var currentDate = new Date();
            var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
            var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
            let requestParams={
                releaseId:item.releaseId,
                auditState:"4",
                auditDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
                currentAuditUserId:constants.loginUser.userId
            };
            httpPost(url, requestParams, this.saveCollectionCallBack);
        }
        console.log("审核状态：：：：："+item.auditState)
    }

    // 保存回调函数
    saveCollectionCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            WToast.show({data:"操作成功"});
            this.callBackFunction();
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    auditTypeChooseStateRow = (item, index) => {
        return (
            <View key={item.auditType} >
                <TouchableOpacity onPress={() => {
                    var selAuditType = item.auditType;
                    console.log("selAuditType",selAuditType);
                    this.setState({
                        selAuditType: selAuditType
                    })

                    let url= "/biz/product/release/list";
                    let loadRequest={
                        "currentPage": 1,
                        "pageSize": 15,
                        "releaseType": "P",
                        "searchKeyWord":this.state.searchKeyWord,
                        "productType":this.state.productType,
                        "auditState":selAuditType,
                        "excludeAuditState":"1",
                        "searchAllTenant":"Y"
                    };
                    // console.log("selYearsChooseName+1:"+ this.addOneYear(selYearsChooseName))
                    httpPost(url, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View key={item.auditType} style={[item.auditType === this.state.selAuditType ? [CommonStyle.selectedBlockItemViewStyle,{backgroundColor:"#FC783D"}] : [CommonStyle.blockItemViewStyle,{}], { paddingLeft:0, paddingRight:0 ,borderRadius:0,width: screenWidth/4.7 ,  flexDirection: 'row', justifyContent: 'center'}]}>
                        <Text style={[item.auditType === this.state.selAuditType ? [{color:"#ffffff",fontSize:18,textAlign:'center'}] : [{color:"#000000",fontSize:18,textAlign:'center'}], { fontWeight: 'bold' }]}>
                            {item.auditTypeName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent(height) {
        return <ProductEmptyRowViewComponent height={height} />
    }

    render(){
        return(
            <View style={{backgroundColor:'#FFF'}}>
            <CommonHeadScreen title='求购审核'
                leftItem={() => this.renderLeftItem()}
                rightItem={() => this.renderRightItem()}
            />

            <View style={[{marginTop:0,backgroundColor:'#FFF'}]} onLayout={this.topBlockLayout.bind(this)}>
                <View style={{ marginTop:5,backgroundColor:'#FFF',marginLeft:10}}>
                    <Image  style={[{height:100 ,borderRadius:10,width:screenWidth - 20}]} source={require('../../../assets/image/askForPurchaseBanner.jpg')} />
                </View>
                <View style={{backgroundColor:'#ffffff',height:50,marginTop:3,marginBottom:3}}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Image  style={{width:25, height:25}} source={require('../../../assets/icon/iconfont/search.png')}></Image>
                        </View>
                        <TextInput
                            style={[styles.searchInputText]}
                            returnKeyType="search"
                            returnKeyLabel="搜索"
                            onSubmitEditing={e => {
                                this.searchByKeyWord();
                            }}
                            placeholder={'搜索名称/型号/材质/产地'}
                            onChangeText={(text) => this.setState({ searchKeyWord: text })}
                        >
                            {this.state.searchKeyWord}
                        </TextInput>
                        {/* | {this.state.currentPage} | {this.state.totalPage} | {this.state.totalRecord} | {this.state.refreshing ? "Y":"N"} | {(this.state.currentPage) < this.state.totalPage ? "YY":"NN"} */}
                    </View>
                </View>
                <View style={{ index: 1000, flexWrap: 'wrap', flexDirection: 'row' ,borderBottomColor:'#33333333',borderBottomWidth:2,alignItems:'flex-end',borderTopWidth:7,borderTopColor:'#F4F7F9'}}>
                    {
                        (this.state.productTypeList && this.state.productTypeList.length > 0)
                            ?
                            this.state.productTypeList.map((item, index) => {
                                return this.productTypeRow(item)
                            })
                            : <View />
                    }
                </View>
                <View style={[styles.innerTabViewStyle, { marginLeft:0,marginTop:0,marginBottom:0,paddingBottom:0,borderBottomWidth:0 }]}>
                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.auditDataSource && this.state.auditDataSource.length > 0)
                                ?
                                this.state.auditDataSource.map((item, index) => {
                                    return this.auditTypeChooseStateRow(item)
                                })
                                :
                            <View />
                        }
                    </View>
                </View>
            </View>
            <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}>
                    <View style={{width:'100%',justifyContent: 'center', alignItems: 'center',backgroundColor:'#FFFFFF',borderBottomWidth:7, borderBottomColor:'#F4F7F9'}}>
                    </View> */}
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent(ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight))}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData()}
                        />
                {/* </ScrollView> */}
            </View>
        </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle: {
        height: 40,
        width:screenWidth -40,
        marginTop:5,
        marginLeft:20,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#F2F5FC",
        backgroundColor:"#F2F5FC",
        borderRadius:5,
    },
    titleTextStyle: {
        fontSize: 18,
        color:'#333333',
        backgroundColor:'#FFF',
        fontWeight:'bold',
        marginLeft:0,
    },
    greyTextStyle: {
        fontSize:14,
        color:'#33333399',
        backgroundColor:'#FFF',
    },
    blackTextStyle: {
        fontSize:15,
        color:'#333333',
    },
    redTextStyle: {
        fontSize:16,
        color:'#E41F00',
        fontWeight:'bold',
    },
    leftLabView: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft:15
    },
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    searchInputText: {
        width: screenWidth -100,
        color: '#33333399',
        fontSize: 16,
        marginLeft: 10,
        paddingBottom: 0,
        paddingTop:4
    },
    innerViewStyle:{
        width:screenWidth - 20,
        marginLeft:10,
        paddingLeft:10,
        paddingTop:10,
        marginTop:10,
        backgroundColor:'#FFF',
        borderBottomColor:'#33333315',
        borderBottomWidth:0.8,
        paddingBottom:10
    },
    innerViewStyleOut:{
        // width:screenWidth - 20,
        // marginLeft:10,
        // paddingLeft:10,
        // paddingTop:10,
        // marginTop:10,
        // backgroundColor:'#FFF',
        // borderBottomColor:'#33333315',
        // borderBottomWidth:0.8,
        // paddingBottom:10
    },
    innerTabViewStyle: {
        backgroundColor: "#ffffff",
        borderColor: "#ffffff",
        width:screenWidth,
        alignItems: 'center',
        // borderWidth: 8
    },
    titleViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 10,
        marginRight: 10,
        marginBottom: 5,
        marginTop: 5,
    },
    itemContentStyle: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    selectedBlockItemTextStyle: {
        fontSize: 17,
        // fontFamily: "PingFangSC-Medium,PingFang SC",
        fontWeight: 'bold',
        color: '#255BDA',
        marginBottom:5
    },
    blockItemTextStyle: {
        fontSize:17,
        color:'#000000',
        marginBottom:5
    },

});
