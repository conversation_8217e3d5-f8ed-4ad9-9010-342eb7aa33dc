import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,Image,TouchableOpacity,Dimensions,Keyboard} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'

import CommonHeadScreen from '../../component/CommonHeadScreen';

var CommonStyle = require('../../assets/css/CommonStyle');
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
var cols = 5;
var cellWH = screenWidth / cols;
var vMargin = (screenWidth - cellWH * cols) / (cols + 1);

class SinteringAdd extends Component{
    constructor(props){
        super(props);
        this.state = {
            // 软键盘高度
            keyboardHeight:0,
            inKilnCarDataSource:[],
            selInKilnCarId:0,
            outKilnCarDataSource:[],
            selOutKilnCarId:0,
            productionLineDataSource:[],
            selProductionLineId:null,
            kilnRoadDataSource:[],
            selKilnRoadId:""
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadProductLineAndSinteringKilnCarList();
        // this.loadInKilnCarList();
        // this.loadOutKilnCarList();
    }

    // 生产车间 & 窑车
    loadProductLineAndSinteringKilnCarList=()=>{
        let url= "/biz/production/line/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 200,
            "qryIncludeContent":"sintering",
        };
        httpPost(url, loadRequest, this.callBackLoadProductLineAndSinteringKilnCarList);
    }

    callBackLoadProductLineAndSinteringKilnCarList=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            
            let productionLineDataSource = response.data.dataList;

            //获取窑道数据
            let kilnRoadDataSource = [];
            var i=0;
            for(i; i<productionLineDataSource.length;i++){
                if(productionLineDataSource[i].kilnRoadName){
                    kilnRoadDataSource = kilnRoadDataSource.concat(productionLineDataSource[i])
                }
            }
            this.setState({
                kilnRoadDataSource:kilnRoadDataSource
            })

            //生产车间
            let selProductionLineId = response.data.dataList[0].productionLineId;
            if (constants.loginUser && constants.loginUser.spUserExtDTO) {
                selProductionLineId = constants.loginUser.spUserExtDTO.productionLineId;
            }
            this.setState({
                productionLineDataSource:productionLineDataSource,
                selProductionLineId:selProductionLineId,
            })

            //判断所在车间是否设置窑道名称，若有则选中该窑道，没有则选中第一个窑道
            if(kilnRoadDataSource && kilnRoadDataSource.length>0){
                kilnRoadDataSource.forEach(element => {
                    if(element.productionLineId == selProductionLineId){
                        this.setState({
                            // 窑道
                            selKilnRoadId:selProductionLineId
                        })
                    }
                });
                if(!this.state.selKilnRoadId){
                    this.setState({
                        // 窑道
                        selKilnRoadId:kilnRoadDataSource[0].productionLineId
                    })
                }
            }
            else{
                this.setState({
                    // 窑道
                    selKilnRoadId:selProductionLineId
                })
            }
            

            if (productionLineDataSource.length == 1) {
                let stateRKilnCarDTOList = productionLineDataSource[0].stateRKilnCarDTOList;
                let stateWKilnCarDTOList = productionLineDataSource[0].stateWKilnCarDTOList;
                this.setState({
                    inKilnCarDataSource:stateRKilnCarDTOList,
                    outKilnCarDataSource:stateWKilnCarDTOList,
                })
                if (stateRKilnCarDTOList && stateRKilnCarDTOList.length > 0) {
                    let selInKilnCarId = stateRKilnCarDTOList[0].kilnCarId;
                    this.setState({
                        selInKilnCarId:selInKilnCarId,
                    })
                }
            }
            else {
                // JS 数组遍历
                productionLineDataSource.forEach((obj)=>{
                    if (selProductionLineId && obj.productionLineId === selProductionLineId) {
                        let stateRKilnCarDTOList = obj.stateRKilnCarDTOList;
                        let stateWKilnCarDTOList = obj.stateWKilnCarDTOList;
                        this.setState({
                            inKilnCarDataSource:stateRKilnCarDTOList,
                            outKilnCarDataSource:stateWKilnCarDTOList,
                        })
                        if (stateRKilnCarDTOList && stateRKilnCarDTOList.length > 0) {
                            let selInKilnCarId = stateRKilnCarDTOList[0].kilnCarId;
                            this.setState({
                                selInKilnCarId:selInKilnCarId,
                            })
                        }
                    }
                })

            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    

    // loadInKilnCarList=()=>{
    //     let url= "/biz/kiln/car/list";
    //     let loadRequest={
    //         "kilnCarState":"R",
    //         "currentPage": 1,
    //         "pageSize": 200,
    //     };
    //     httpPost(url, loadRequest, this.loadInKilnCarListCallBack);
    // }

    // loadInKilnCarListCallBack=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         this.setState({
    //             inKilnCarDataSource:response.data.dataList,
    //             selInKilnCarId:response.data.dataList[0] ? response.data.dataList[0].kilnCarId : 0,
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    // loadOutKilnCarList=()=>{
    //     let url= "/biz/kiln/car/list";
    //     let loadRequest={
    //         "kilnCarState":"W",
    //         "currentPage": 1,
    //         "pageSize": 200,
    //     };
    //     httpPost(url, loadRequest, this.loadOutKilnCarListCallBack);
    // }

    // loadOutKilnCarListCallBack=(response)=>{
    //     if (response.code == 200 && response.data && response.data.dataList) {
    //         this.setState({
    //             outKilnCarDataSource:response.data.dataList,
    //         })
    //     }
    //     else if (response.code == 401) {
    //         WToast.show({data:response.message});
    //         this.props.navigation.navigate("LoginView");
    //     }
    // }

    //键盘弹起后执行
    _keyboardDidShow(e){
        console.log("=======e", e)
        this.setState({
            keyboardHeight:e.endCoordinates.height
        })
    }

    //键盘收起后执行
    _keyboardDidHide(e){
        this.setState({
            keyboardHeight:0
        })
    }

    componentWillMount() {
        // 监听键盘
        this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow.bind(this));
        this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide.bind(this));
    }
    componentWillUnmount(){
        this.keyboardWillShowListener && this.keyboardWillShowListener.remove();
        this.keyboardWillHideListener && this.keyboardWillHideListener.remove();
    }

    // 分隔线
    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0',marginBottom:10,marginTop:10}}/>)
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("SinteringList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>烧结管理</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>烧结管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    // 窑道名称 
    renderProductLineRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                // 切换生产车间时，下面的机台也要跟着变，机台默认选择第一个
                this.setState({
                    selKilnRoadId:item.productionLineId,
                    inKilnCarDataSource:item.stateRKilnCarDTOList,
                    outKilnCarDataSource:item.stateWKilnCarDTOList,
                }) 
                if (item.stateRKilnCarDTOList && item.stateRKilnCarDTOList.length > 0) {
                    let selInKilnCarId = item.stateRKilnCarDTOList[0].kilnCarId;
                    this.setState({
                        selInKilnCarId:selInKilnCarId,
                    })
                }
            }}>
                <View key={item.productionLineId} style={[item.productionLineId === this.state.selKilnRoadId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.productionLineId === this.state.selKilnRoadId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.kilnRoadName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 入窑车号单项渲染
    renderInKilnCarRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selInKilnCarId:item.kilnCarId,
                })
             }}>
                <View key={item.kilnCarId} style={[item.kilnCarId === this.state.selInKilnCarId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.kilnCarId === this.state.selInKilnCarId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.kilnCarName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 出窑车号单项渲染
    renderOutKilnCarRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selOutKilnCarId:item.kilnCarId,
                })
             }}>
                <View key={item.kilnCarId} style={[item.kilnCarId === this.state.selOutKilnCarId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.kilnCarId === this.state.selOutKilnCarId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.kilnCarName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    saveSintering =()=> {
        console.log("=======saveSintering");
        let toastOpts;
        if (!this.state.selInKilnCarId || this.state.selInKilnCarId === 0) {
            toastOpts = getFailToastOpts("数据不全，不合法，入窑车号必选");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/sintering/record/add";
        let requestParams={
            "inKilnCarId":this.state.selInKilnCarId,
            "outKilnCarId":this.state.selOutKilnCarId
        };
        console.log("=========url:", url)
        console.log("=========requestParams:", requestParams)
        httpPost(url, requestParams, this.saveSinteringCallBack);
    }

    // 保存回调函数
    saveSinteringCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='新增烧结'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}/>
                <ScrollView style={CommonStyle.contentViewStyle}>

                    {
                        this.state.productionLineDataSource.length == 1 ?
                        <View>
                            <View style={CommonStyle.addItemSplitRowView}>
                                <Text style={CommonStyle.addItemSplitRowText}>入窑车号</Text>
                            </View>
                            <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                                {
                                    (this.state.inKilnCarDataSource && this.state.inKilnCarDataSource.length > 0) 
                                    ? 
                                    this.state.inKilnCarDataSource.map((item, index)=>{
                                        return this.renderInKilnCarRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                            <View style={CommonStyle.addItemSplitRowView}>
                                <Text style={CommonStyle.addItemSplitRowText}>出窑车号</Text>
                            </View>
                            <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                                {
                                    (this.state.outKilnCarDataSource && this.state.outKilnCarDataSource.length > 0) 
                                    ? 
                                    this.state.outKilnCarDataSource.map((item, index)=>{
                                        return this.renderOutKilnCarRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                        </View>
                        :
                        <View>
                            <View style={CommonStyle.rowLabView}>
                                <Text style={CommonStyle.rowLabTextStyle}>窑道名称</Text>
                            </View>
                            <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                                {
                                    (this.state.kilnRoadDataSource && this.state.kilnRoadDataSource.length > 0) 
                                    ? 
                                    this.state.kilnRoadDataSource.map((item, index)=>{
                                        return this.renderProductLineRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                            <View style={[CommonStyle.addItemSplitRowView]}>
                                <Text style={CommonStyle.addItemSplitRowText}>入窑车号</Text>
                            </View>
                            <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                                {
                                    (this.state.inKilnCarDataSource && this.state.inKilnCarDataSource.length > 0) 
                                    ? 
                                    this.state.inKilnCarDataSource.map((item, index)=>{
                                        return this.renderInKilnCarRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                            <View style={CommonStyle.addItemSplitRowView}>
                                <Text style={CommonStyle.addItemSplitRowText}>出窑车号</Text>
                            </View>
                            <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                                {
                                    (this.state.outKilnCarDataSource && this.state.outKilnCarDataSource.length > 0) 
                                    ? 
                                    this.state.outKilnCarDataSource.map((item, index)=>{
                                        return this.renderOutKilnCarRow(item)
                                    })
                                    : <EmptyRowViewComponent/> 
                                }
                            </View>
                        </View>
                    }
                    <View style={{height:ifIphoneXContentViewHeight()-405, backgroundColor:'#F2F5FC'}}>
                    </View>

                    <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveSintering.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    brickTypeNameTextStyle:{
        width:150,
        flexWrap:"wrap", 
        fontSize:15
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    wasteAmountTextStyle:{
        width:100,
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})
module.exports = SinteringAdd;