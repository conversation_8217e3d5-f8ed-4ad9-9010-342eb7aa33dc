import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,FlatList,Image,TouchableOpacity,Dimensions} from 'react-native';
// import { TOUCHABLE_STATE } from 'react-native-gesture-handler/lib/typescript/components/touchables/GenericTouchable';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class JobMgrAdd extends Component {
    constructor(){
        super()
        this.state = {
            departmentId:null,
            jobId:"",
            seriesName:"",
            operate:"",
            selectedPowerLevel:[],
            powerLevelDataSource:[],
            powerLevel:"",
            saleFlag:"Y"
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        let loadTypeUrl;
        let loadRequest;

        let powerLevelDataSource =[
            {
                powerLevelId:"1",
            },
            {
                powerLevelId:"2",
            },
            {
                powerLevelId:"3",
            },
            {
                powerLevelId:"4",
            },
            {
                powerLevelId:"5",
            },
            {
                powerLevelId:"6",
            },
            {
                powerLevelId:"7",
            },
            {
                powerLevelId:"8",
            },
            {
                powerLevelId:"9",
            },
            {
                powerLevelId:"10",
            },
        ]

    
        this.setState({
            powerLevelDataSource:powerLevelDataSource,
        })


        const { route, navigation } = this.props;
        if (route && route.params) {
            const { jobId, departmentId } = route.params;
            if (departmentId) {
                this.setState({
                    departmentId:departmentId,
                })
            }
            if (jobId) {
                console.log("========Edit==jobId:", jobId);
                this.setState({
                    jobId:jobId,
                    operate:"编辑"
                })
                loadTypeUrl= "/biz/job/get";
                loadRequest={'jobId':jobId};
                httpPost(loadTypeUrl, loadRequest, this.loadEditJobDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    loadEditJobDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            let as = response.data.powerLevel;
            this.setState({
                jobId:response.data.jobId,
                jobName:response.data.jobName,
                jobAmount:response.data.jobAmount,
                departmentId:response.data.departmentId,
                powerLevel:response.data.powerLevel,
                selectedPowerLevel:[response.data.powerLevel],
                saleFlag:response.data.saleFlag,
            })
            console.log("response.data.powerLevel" ,as);
        }
        
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
        //     <TouchableOpacity onPress={() => { 
        //         this.props.navigation.navigate("JobMgrList")
        //     }}>
        //         <Text style={CommonStyle.headRightText}>职位管理</Text>
        //     </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>职位管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    openPowerLevel(){      
        this.refs.SelectPowerLevel.showPowerLevel(this.state.selectedPowerLevel,this.state.powerLevelDataSource)
    }

    callBackSelectPowerLevelValue(value){
        console.log("==========职位权重选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedPowerLevel:value
        })
        var PowerLevel = value.toString();
        this.setState({
            powerLevel:PowerLevel
        })
        console.log("value+++" , value);
    }

    saveJob =()=> {
        console.log("=======saveJob");
        let toastOpts;
        if (!this.state.jobName) {
            toastOpts = getFailToastOpts("请输入职位名称");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.jobAmount) {
            toastOpts = getFailToastOpts("请输入职位个数");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selectedPowerLevel) {
            toastOpts = getFailToastOpts("请选择职位权重");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/job/add";
        if (this.state.jobId) {
            console.log("=========Edit===jobId", this.state.jobId)
            url= "/biz/job/modify";
        }
        let requestParams={
            "departmentId":this.state.departmentId,
            "jobId":this.state.jobId,
            "jobName":this.state.jobName,
            "jobAmount":this.state.jobAmount,
            "powerLevel":this.state.powerLevel,
            "saleFlag":this.state.saleFlag,
        };
        httpPost(url, requestParams, this.saveJobCallBack);
    }
    
    // 保存回调函数
    saveJobCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    render(){
        return (
            <View style={{backgroundColor: 'rgba(242, 245, 252, 1)'}}>
                <CommonHeadScreen title={this.state.operate + '职位'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <ScrollView style={{backgroundColor: '#FFFFFF',borderBottomWidth:1,borderBottomColor:'#E8E9EC'}}>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>职位名称</Text>                         
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入职位名称'}
                            onChangeText={(text) => this.setState({jobName:text})}
                        >
                            {this.state.jobName}
                        </TextInput>
                    </View>
                    <View style={styles.lineViewStyle}></View>  
                    {/* <View style={styles.lineViewStyle}></View>   */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>职位个数</Text>      
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入职位个数'}
                            onChangeText={(text) => this.setState({jobAmount:text})}
                        >
                            {this.state.jobAmount}
                        </TextInput>
                    </View>
                    <View style={styles.lineViewStyle}></View>  
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>职位权重</Text>
                        </View>
                        <TouchableOpacity onPress={()=>this.openPowerLevel()}>
                            <View style={styles.inputTextStyleTextStyle}>
                                {
                                    !this.state.powerLevel ?
                                    <Text style={{color:'#A0A0A0', fontSize:15}}>请选择职位权重</Text>
                                    :
                                    <Text style={{color:'rgba(43, 51, 63, 1)', fontSize:15}}>{this.state.powerLevel}</Text> 

                                }
                                
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.lineViewStyle}></View>  
                    <View style={[styles.inputRowStyle]}>
                        <View style={styles.leftLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>销售岗位</Text>
                        </View>
                        <View style={styles.selectViewItem}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    saleFlag: "Y",
                                })
                            }}>
                                {
                                    (this.state.saleFlag === 'Y') ? 
                                    <Image style={{ width: 20, height: 20 }} source={require('../../assets/image/selected.png')}></Image>
                                    :
                                    <Image style={{ width: 20, height: 20 }} source={require('../../assets/image/unselected.png')}></Image>
                                }
                            </TouchableOpacity>
                            <Text style={styles.selectTextItem}>是</Text>
                        </View>
                       
                        <View style={[styles.selectViewItem,{marginLeft:15}]}>
                            <TouchableOpacity onPress={() => {
                                this.setState({
                                    saleFlag: "N",
                                })
                            }}>
                                {
                                    (this.state.saleFlag === 'N' || this.state.saleFlag == null) ?
                                    <Image style={{ width: 20, height: 20 }} source={require('../../assets/image/selected.png')}></Image>
                                    :
                                    <Image style={{ width: 20, height: 20 }} source={require('../../assets/image/unselected.png')}></Image>
                                }
                                
                            </TouchableOpacity>
                            <Text style={styles.selectTextItem}>否</Text>
                        </View>
                    </View>
                    
                    <BottomScrollSelect 
                        ref={'SelectPowerLevel'} 
                        callBackPowerLevelValue={this.callBackSelectPowerLevelValue.bind(this)}
                    />
                </ScrollView>
                <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0, marginTop: screenHeight-435,backgroundColor:'rgba(255, 255, 255, 1)',height:66,}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginLeft: 20,marginTop:8, width: (screenWidth - 56)/2}]} >
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.saveJob.bind(this)}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, {marginRight: 20, marginTop:8, width: (screenWidth - 56)/2}]}>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        // color:'#A0A0A0',
        color:'rgba(43, 51, 63, 1)',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputTextStyleTextStyle:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        height:45,
        justifyContent:'center'
    },
    selectViewItem: {
        width: 50, justifyContent: 'center', alignItems: 'center',flexDirection: 'row',marginLeft:6
    },
    selectTextItem: {
        fontSize: 14,
        fontWeight: '400',
        marginLeft:5,
        fontFamily:'PingFangSC',
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 8,
        marginBottom: 5,
        borderBottomWidth: 1,
        borderColor:'#E8E9EC'
    },
})