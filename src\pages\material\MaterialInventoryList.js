import React, {Component} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import {ifIphoneXContentViewDynamicHeight} from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
export default class MaterialInventoryList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      io: null,
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 30,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
      topBlockLayoutHeight: 0,
      locationId: '',
      locationName: '',
      classifyId: '',
      classifyName: '',
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    const {route, navigation} = this.props;
    if (route && route.params) {
      const {classifyId, classifyName, locationId, locationName} = route.params;
      if (classifyId) {
        this.setState({
          classifyId: classifyId,
        });
      }
      if (classifyName) {
        this.setState({
          classifyName: classifyName,
        });
      }
      if (locationId) {
        this.setState({
          locationId: locationId,
        });
      }
      if (locationName) {
        this.setState({
          locationName: locationName,
        });
      }
      this.loadStorageList(classifyId, locationId);
    } else {
      this.loadStorageList();
    }
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/material/inventory/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      classifyId: this.state.classifyId,
      io: this.state.io,
      searchInventory: 'Y',
      locationId: this.state.locationId == 0 ? null : this.state.locationId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========不刷新=====');
      return;
    }
    this.setState({
      currentPage: 1,
    });
    let url = '/biz/material/inventory/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      classifyId: this.state.classifyId,
      io: this.state.io,
      searchInventory: 'Y',
      locationId: this.state.locationId == 0 ? null : this.state.locationId,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    this.loadStorageList();
  };

  loadStorageList = (classifyId, locationId) => {
    let url = '/biz/material/inventory/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      classifyId: classifyId ? classifyId : this.state.classifyId,
      io: this.state.io,
      searchInventory: 'Y',
      locationId: locationId == 0 ? null : locationId,
    };
    httpPost(url, loadRequest, this.loadStorageListCallBack);
  };

  loadStorageListCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteStorage = (inventoryId) => {
    console.log('=======delete=inventoryId', inventoryId);
    let url = '/biz/material/inventory/delete';
    let requestParams = {inventoryId: inventoryId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  renderRow = (item, index) => {
    return (
      <View
        key={item.inventoryId}
        style={[
          styles.innerViewStyle,
          item.io === 'I'
            ? {backgroundColor: 'rgba(255,0,0,0.2)'}
            : {backgroundColor: 'rgba(0,255,0,0.2)'},
        ]}>
        {/* <View style={{position:'absolute',right:10,top:5}}>
                    <Text style={styles.titleTextStyle}>库位已调换</Text>
                </View> */}
        <View style={[styles.titleViewStyle, {flexDirection: 'row'}]}>
          <Text style={styles.titleTextStyle}>
            库存重量：{item.beforeInventoryWeight.toFixed(2)}
          </Text>
          {item.newLocationName ? (
            <Text style={[styles.titleTextStyle, {color: '#44b7eb'}]}>
              库位已调至:{item.newLocationName}
            </Text>
          ) : null}
        </View>
        <View style={[styles.titleViewStyle]}>
          <Text style={styles.titleTextStyle}>
            {item.io === 'I' ? '入库' : '出库'}重量：{item.weight}
          </Text>
        </View>
        <View style={[styles.titleViewStyle]}>
          <Text style={styles.titleTextStyle}>
            结余库存：{item.afterInventoryWeight.toFixed(2)}
            {item.newLocationName ? (
              <Text style={[{color: 'red'}]}>[库位已调换]</Text>
            ) : null}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            {item.io === 'I' ? '入库' : '出库'}时间：{item.ioDate}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>经办人：{item.operator}</Text>
        </View>

        {/* {
                    item.io === "I" ? <View/>
                    :
                    <View style={[styles.titleViewStyle]}>
                        <Text style={styles.titleTextStyle}>客户名称：{item.customerName}</Text>
                    </View>
                } */}
        {item.io === 'I' ? (
          <View />
        ) : (
          <View style={[styles.titleViewStyle]}>
            <Text style={styles.titleTextStyle}>
              需求产品：{item.seriesName}
            </Text>
          </View>
        )}
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>
            所属库位：{item.locationId ? item.locationName : '无'}
          </Text>
        </View>
        <View style={styles.titleViewStyle}>
          <Text style={styles.titleTextStyle}>提交时间：{item.gmtCreated}</Text>
        </View>
      </View>
    );
  };
  space() {
    return <View style={{height: 1, backgroundColor: '#F0F0F0'}} />;
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }
  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={styles.navLeft}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
      // </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={{
            marginBottom: 1.5,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}
          />
          <Text style={{color: '#3C6CDE', marginLeft: 3, fontWeight: 'bold'}}>
            返回
          </Text>
        </TouchableOpacity>
        {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
        {/* <Text style={{ color: '#3C6CDE', marginLeft: 3, fontWeight:'bold'}}>返回</Text> */}
      </View>
    );
  }
  // 头部右侧
  renderRightItem() {
    return (
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity onPress={() => {}}>
          {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
          <Text style={{color: '#FFFFFF'}}>原料库存</Text>
          {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
        </TouchableOpacity>
      </View>
    );
  }

  topBlockLayout = (event) => {
    this.setState({
      topBlockLayoutHeight: event.nativeEvent.layout.height,
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="原料库存"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        <View
          style={[styles.innerViewStyle, {marginTop: 0, index: 1000}]}
          onLayout={this.topBlockLayout.bind(this)}>
          <Text
            style={[
              styles.titleTextStyle,
              {marginLeft: 10, fontWeight: 'bold', marginRight: 10},
            ]}>
            原料名称：{this.state.classifyName}
          </Text>
          <Text
            style={[
              styles.titleTextStyle,
              {marginLeft: 10, fontWeight: 'bold', marginRight: 10},
            ]}>
            重量单位：吨
          </Text>
          <Text
            style={[
              styles.titleTextStyle,
              {marginLeft: 10, fontWeight: 'bold', marginRight: 10},
            ]}>
            所属库位：{this.state.locationName}
          </Text>
          <View
            style={{
              position: 'absolute',
              backgroundColor: 'rgba(255,0,0,0.2)',
              right: 50,
              top: -5,
              height: 30,
              padding: 5,
            }}>
            <Text>入库</Text>
          </View>
          <View
            style={{
              position: 'absolute',
              backgroundColor: 'rgba(0,255,0,0.2)',
              right: 10,
              top: -5,
              height: 30,
              padding: 5,
            }}>
            <Text>出库</Text>
          </View>
        </View>
        <View
          style={[
            CommonStyle.contentViewStyle,
            {
              height: ifIphoneXContentViewDynamicHeight(
                this.state.topBlockLayoutHeight,
              ),
            },
          ]}>
          <FlatList
            data={this.state.dataSource}
            renderItem={({item, index}) => this.renderRow(item, index)}
            keyExtractor={(item) => item.inventoryId}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
          />
        </View>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    marginTop: 10,
    borderColor: '#F4F4F4',
    borderWidth: 14,
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'column',
  },
  itemContentChildTextStyle: {
    marginLeft: 10,
    marginTop: 15,
    fontSize: 16,
  },
});
