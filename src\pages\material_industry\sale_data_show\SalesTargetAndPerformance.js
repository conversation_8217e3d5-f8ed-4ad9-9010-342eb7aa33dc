import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,Image,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import NaicaiGuidBugHeadScreen from '../../../component/NaicaiGuidBugHeadScreen';
import ProductEmptyRowViewComponent from '../../../component/ProductEmptyRowViewComponent';
const { ifIphoneXBodyViewHeight,ifIphoneXContentViewDynamicHeight} = require('../../../utils/ScreenUtil');
import moment from 'moment';
var CommonStyle = require('../../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
var currentYear= (moment(new Date()).format('YYYY'));
var currentMonth= (moment(new Date()).format('MM'));
export default class SalesTargetAndPerformance extends Component {
    constructor(props) {
        super(props);
        this.state = {
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            salesTypeList:[
                {
                    typeName:"月度",
                    typeCode:"month"
                },
                {
                    typeName:"季度",
                    typeCode:"quarter"
                },
                {
                    typeName:"年度",
                    typeCode:"year"
                }
            ],
            selectType:"month",
            salesMonthList:[],
            selectMonth:"",
            salesQuarterList:[],
            selectQuarter:"",
            salesYearList:[],
            selectYear:"",
            topBlockLayoutHeight:0,
            salesDataSource:[],
            sort:"rate",
            dataSourceByAmount:[],
            dataSourceByRate:[],
            dataSource:[],
            year:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { monthCode, yearCode} = route.params;
            if (monthCode && yearCode) {
                console.log("=============monthCode:" + monthCode);
                this.setState({
                    selectYear:yearCode,
                    selectMonth:monthCode,
                    year:yearCode
                })
                this.loadSalesData(yearCode,monthCode);
                // 获取当前月份及之前月份
                var maxMonth = currentMonth.replace(/\b(0+)/gi,"");
                // 非本年时设置月份最大为12
                if(currentYear != yearCode){
                    var maxMonth = 12;
                }
                
                var salesMonthList = [];
                var salesMonthDTO = {};
                for(var i = 1; i < (maxMonth * 1 + 1); i++) {
                    salesMonthDTO ={
                        "monthName":i + "月",
                        "monthCode":i < 10 ? ("0"+i) : i.toString()
                    };
                    salesMonthList = salesMonthList.concat(salesMonthDTO)
                }
                console.log(salesMonthList)
                this.setState({
                    salesMonthList:salesMonthList.reverse()
                })

                //获取当前季度及之前季度
                var salesQuarterList = [
                    {
                        "quarterName":"第四季度",
                        "quarterCode":"Q4"
                    },
                    {
                        "quarterName":"第三季度",
                        "quarterCode":"Q3"
                    },
                    {
                        "quarterName":"第二季度",
                        "quarterCode":"Q2"
                    },
                    {
                        "quarterName":"第一季度",
                        "quarterCode":"Q1"
                    }
                ];
                var currentMonthNumber = monthCode.replace(/\b(0+)/gi,"");
                console.log("currentMonthNumber==",currentMonthNumber)
                var currentQuarter = "Q1"
                if(currentMonthNumber > 9 && currentMonthNumber < 13){
                    currentQuarter = "Q4";
                }
                if ((currentMonthNumber > 6 && currentMonthNumber < 10)){
                    salesQuarterList.shift();
                    currentQuarter = "Q3";
                }
                if (currentMonthNumber > 3 && currentMonthNumber < 7){
                    salesQuarterList.shift();
                    salesQuarterList.shift();
                    currentQuarter = "Q2";
                }
                if (currentMonthNumber > 0 && currentMonthNumber < 4) {
                    salesQuarterList.shift();
                    salesQuarterList.shift();
                    salesQuarterList.shift();
                    currentQuarter = "Q1";
                }
                this.setState({
                    salesQuarterList:salesQuarterList,
                    selectQuarter:currentQuarter
                })

                // 非本年时设置季度列表
                if(currentYear != yearCode){
                    this.setState({
                        salesQuarterList:[
                            {
                                "quarterName":"第四季度",
                                "quarterCode":"Q4"
                            },
                            {
                                "quarterName":"第三季度",
                                "quarterCode":"Q3"
                            },
                            {
                                "quarterName":"第二季度",
                                "quarterCode":"Q2"
                            },
                            {
                                "quarterName":"第一季度",
                                "quarterCode":"Q1"
                            }
                        ]
                    })
                }
                
                //获取近三年
                var salesYearList = [
                    {
                        "yearName":currentYear + "年",
                        "yearCode":currentYear.toString()
                    },
                    {
                        "yearName":(currentYear - 1) + "年",
                        "yearCode":(currentYear - 1).toString()
                    },
                    {
                        "yearName":(currentYear - 2) + "年",
                        "yearCode":(currentYear - 2).toString()
                    }
                ];
                this.setState({
                    salesYearList:salesYearList,
                })
            }
        }
    }

    loadSalesData=(year,month)=>{
        let url= "/biz/sales/kanban/getSales";
        let loadRequest={
            "searchYear":year,
            "searchMonth":month
        };
        httpPost(url, loadRequest, this.loadSalesDataCallBack);
    }

    loadSalesDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                salesDataSource:response.data,
                dataSourceByAmount:response.data?.spSalesUserDTOListRankByAmount,
                dataSourceByRate:response.data?.spSalesUserDTOListRankByRate,
                dataSource:response.data?.spSalesUserDTOListRankByAmount
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../../assets/icon/iconfont/backBlack.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }

    renderRankRow=(item,index)=>{
        return(
            <View>
                <View style={{flexDirection:'row',marginLeft:0}}>
                    {/* <View style={{margin:5,width:screenWidth/6,justifyContent:'center',alignItems:'center'}}>
                        {
                            (index + 1) == 1
                            ?
                            <Image source={require('../../../assets/icon/FirstRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) == 2
                            ?
                            <Image source={require('../../../assets/icon/SecondRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) == 3
                            ?
                            <Image source={require('../../../assets/icon/ThirdRank.png')} style={{width:20,height:20, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :null
                        }
                        {
                            (index + 1) != 1 && (index + 1) != 2 && (index + 1) != 3
                            ?
                            <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{index + 1}</Text>
                            :null
                        }
                    </View> */}
                    <View style={{margin:5,width:screenWidth/6.5,justifyContent:'center',alignItems:'center'}}>
                        {
                            item.userPhoto?
                            <Image source={{ uri: (constants.image_addr + '/' + item.userPhoto) }} style={{width:40,height:40, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                            :
                            <Image source={require('../../../assets/image/userPhoto.png')} style={{width:40,height:40, borderRadius:50,justifyContent:'center',alignItems:'center'}} />
                        }
                    </View>
                    <View style={{margin:5,width:screenWidth/6.5,justifyContent:'center',alignItems:'center'}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{item.userName}</Text>
                    </View>
                    <View style={{margin:5,width:screenWidth/5.5,justifyContent:'center',alignItems:'center'}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{this.state.selectType == 'month'?(item.monthTargetSales?parseFloat(item.monthTargetSales):"0"):(this.state.selectType == 'quarter'?(item.quarterTargetSales?parseFloat(item.quarterTargetSales):"0"):(item.yearTargetSales?parseFloat(item.yearTargetSales):"0"))}</Text>
                    </View>
                    <View style={{margin:5,width:screenWidth/5.5,justifyContent:'center',alignItems:'center'}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{this.state.selectType == 'month'?item.monthSales:(this.state.selectType == 'quarter'?item.quarterSales:item.yearSales)}</Text>
                    </View>
                    <View style={{margin:5,width:screenWidth/6,justifyContent:'center',alignItems:'center'}}>
                        <Text style={[styles.titleTextStyle,{fontSize: 14}]}>{this.state.selectType == 'month'?item.monthSaleFinishRate:(this.state.selectType == 'quarter'?item.quarterSaleFinishRate:item.yearSaleFinishRate)}%</Text>
                    </View>
                </View>
            </View>
        )
    }

    render(){
        return(
            <View style={{backgroundColor:'#f3f6f8',height:screenHeight}}>
                <NaicaiGuidBugHeadScreen title='销售目标及业绩'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[{backgroundColor:'#f3f6f8',height:ifIphoneXBodyViewHeight()}]}  >
                    <View onLayout={this.topBlockLayout.bind(this)}>
                        <View style={[styles.containerBox,{justifyContent:'space-evenly',flexDirection:'row'}]}>
                            {
                                this.state.salesTypeList.map(item =>{
                                    return(
                                        <TouchableOpacity onPress={() => { 
                                            console.log(item.typeCode)
                                            this.setState({
                                                selectType:item.typeCode,
                                                sort:'rate'
                                            })
                                            if(item.typeCode == 'month'){
                                                let url= "/biz/sales/kanban/getSales";
                                                let loadRequest={
                                                    "searchYear":this.state.year,
                                                    "searchMonth":this.state.selectMonth
                                                };
                                                httpPost(url, loadRequest, this.loadSalesDataCallBack);
                                            }
                                            if(item.typeCode == 'quarter'){
                                                let url= "/biz/sales/kanban/getSales";
                                                let loadRequest={
                                                    "searchYear":this.state.year,
                                                    "searchQuarter":this.state.selectQuarter
                                                };
                                                httpPost(url, loadRequest, this.loadSalesDataCallBack);
                                            }
                                            if(item.typeCode == 'year'){
                                                let url= "/biz/sales/kanban/getSales";
                                                let loadRequest={
                                                    "searchYear":this.state.selectYear
                                                };
                                                httpPost(url, loadRequest, this.loadSalesDataCallBack);
                                            }
                                        }}  style={{alignItems:'center'}}>
                                            <View style={[styles.salesType,{backgroundColor:(item.typeCode  ==  this.state.selectType)?"#09c119":null}]}>
                                                <Text style={{color:(item.typeCode  ==  this.state.selectType)?'#f0f0f0':'#00000085'}}>{item.typeName}</Text>
                                            </View>
                                        </TouchableOpacity>
                                    )
                                })
                            }
                        </View>
                        <View  style={[styles.containerBox,{flexDirection:'row',flexWrap:'wrap',backgroundColor:null,marginLeft:25}]}>
                            {
                                this.state.selectType == "month" ?
                                this.state.salesMonthList.map(item =>{
                                    return(
                                        <TouchableOpacity onPress={() => { 
                                            console.log(this.state.topBlockLayoutHeight)
                                            this.setState({
                                                selectMonth:item.monthCode,
                                                sort:'rate'
                                            })
                                            this.loadSalesData(this.state.year,item.monthCode)
                                        }}  style={{alignItems:'center'}}>
                                            <View style={[styles.salesMonth,{borderWidth:1,borderColor:(item.monthCode  ==  this.state.selectMonth)?"#09c119":'#00000055'}]}>
                                                <Text style={{color:(item.monthCode  ==  this.state.selectMonth)?'#09c119':'#00000055'}}>{item.monthName}</Text>
                                            </View>
                                        </TouchableOpacity>
                                    )
                                })
                                :null
                            }
                            {
                                this.state.selectType == "quarter" ?
                                this.state.salesQuarterList.map(item =>{
                                    return(
                                        <TouchableOpacity onPress={() => { 
                                            console.log(item.quarterCode)
                                            this.setState({
                                                selectQuarter:item.quarterCode,
                                                sort:'rate'
                                            })
                                            let url= "/biz/sales/kanban/getSales";
                                            let loadRequest={
                                                "searchYear":this.state.year,
                                                "searchQuarter":item.quarterCode
                                            };
                                            httpPost(url, loadRequest, this.loadSalesDataCallBack);
                                        }}  style={{alignItems:'center'}}>
                                            <View style={[styles.salesMonth,{borderWidth:1,borderColor:(item.quarterCode  ==  this.state.selectQuarter)?"#09c119":'#00000055'}]}>
                                                <Text style={{color:(item.quarterCode  ==  this.state.selectQuarter)?'#09c119':'#00000055'}}>{item.quarterName}</Text>
                                            </View>
                                        </TouchableOpacity>
                                    )
                                })
                                :null
                            }
                            {
                                this.state.selectType == "year" ?
                                this.state.salesYearList.map(item =>{
                                    return(
                                        <TouchableOpacity onPress={() => { 
                                            console.log(item.yearCode)
                                            this.setState({
                                                selectYear:item.yearCode,
                                                sort:'rate'
                                            })
                                            let url= "/biz/sales/kanban/getSales";
                                            let loadRequest={
                                                "searchYear":item.yearCode,
                                            };
                                            httpPost(url, loadRequest, this.loadSalesDataCallBack);
                                        }}  style={{alignItems:'center'}}>
                                            <View style={[styles.salesMonth,{borderWidth:1,borderColor:(item.yearCode  ==  this.state.selectYear)?"#09c119":'#00000055'}]}>
                                                <Text style={{color:(item.yearCode  ==  this.state.selectYear)?'#09c119':'#00000055'}}>{item.yearName}</Text>
                                            </View>
                                        </TouchableOpacity>
                                    )
                                })
                                :null
                            }
                        </View>
                        
                        <View style={[{backgroundColor:'#ffffff',margin:10,marginBottom:0}]}>
                            <View style={[styles.salesTitle]}>
                                <Text style={{fontWeight:'bold'}}>销售目标及业绩</Text>
                            </View>
                            <View style={{flexDirection:'row',marginLeft:0}}>
                                {/* <View style={{margin:5,width:screenWidth/6,justifyContent:'center',alignItems:'center'}}>
                                    <Text style={[styles.titleTextStyle,{fontSize: 14,fontWeight: 'bold'}]}>排名</Text>
                                </View> */}
                                <View style={{margin:5,width:screenWidth/6.5,justifyContent:'center',alignItems:'center'}}>
                                    <Text style={[styles.titleTextStyle,{fontSize: 14,fontWeight: 'bold'}]}>员工</Text>
                                </View>
                                <View style={{margin:5,width:screenWidth/6.5,justifyContent:'center',alignItems:'center'}}>
                                    <Text style={[styles.titleTextStyle,{fontSize: 14,fontWeight: 'bold'}]}>姓名</Text>
                                </View>
                                <View style={{margin:5,width:screenWidth/5.5,justifyContent:'center',alignItems:'center'}}>
                                    <Text style={[styles.titleTextStyle,{fontSize: 14,fontWeight: 'bold'}]}>目标销售额</Text>
                                </View>
                                <View style={{margin:5,width:screenWidth/5.5,justifyContent:'center',alignItems:'center',flexDirection:'row'}}>
                                    <Text style={[styles.titleTextStyle,{fontSize: 14,fontWeight: 'bold'}]}>完成销售额</Text>
                                    <TouchableOpacity onPress={()=>{
                                        if(this.state.sort == 'amount') {
                                            this.setState({
                                                dataSource:this.state.dataSourceByRate?.reverse()
                                            })
                                        }
                                        else{
                                            this.setState({
                                                sort:'amount',
                                                dataSource:this.state.dataSourceByRate
                                            })
                                        }
                                    }}>
                                        {
                                            this.state.sort == 'amount' ?
                                            <Image style={{ width: 18, height: 18}} source={require('../../../assets/icon/sortArrow.png')}></Image>
                                            :
                                            <Image style={{ width: 18, height: 18}} source={require('../../../assets/icon/sortArrowUnclick.png')}></Image>
                                        }
                                    </TouchableOpacity>
                                </View>
                                <View style={{margin:5,width:screenWidth/6,justifyContent:'center',alignItems:'center',flexDirection:'row'}}>
                                    <Text style={[styles.titleTextStyle,{fontSize: 14,fontWeight: 'bold'}]}>完成率</Text>
                                    <TouchableOpacity onPress={()=>{
                                        if(this.state.sort == 'rate') {
                                            this.setState({
                                                dataSource:this.state.dataSourceByRate?.reverse()
                                            })
                                        }
                                        else{
                                            this.setState({
                                                sort:'rate',
                                                dataSource:this.state.dataSourceByRate
                                            })
                                        }
                                    }}>
                                        {
                                            this.state.sort == 'rate' ?
                                            <Image style={{ width: 18, height: 18}} source={require('../../../assets/icon/sortArrow.png')}></Image>
                                            :
                                            <Image style={{ width: 18, height: 18}} source={require('../../../assets/icon/sortArrowUnclick.png')}></Image>
                                        }
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </View>
                    <ScrollView style={[{backgroundColor:'#ffffff',margin:10,marginTop:0}, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) + 60 }]}>
                            {
                                this.state.dataSource && this.state.dataSource.length > 0
                                ?
                                (
                                    this.state.dataSource.map((item,index)=>{
                                        return this.renderRankRow(item,index)
                                    })
                                )
                                :
                                <ProductEmptyRowViewComponent height={350} />
                            }
                    </ScrollView>
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    containerBox:{
        width:screenWidth - 20,
        marginLeft:10,
        marginTop:10,
        backgroundColor:"#FFFFFF"
    },
    salesType:{
        marginBottom:10,
        marginTop:10,
        paddingBottom:5,
        paddingTop:5,
        paddingLeft:40,
        paddingRight:40,
        borderRadius:5
    },
    salesMonth:{
        marginBottom:5,
        marginTop:5,
        marginRight:8,
        paddingBottom:5,
        paddingTop:5,
        paddingLeft:10,
        paddingRight:10,
        borderRadius:5
    },
    salesTitle:{
        backgroundColor:'#d6e9f6',
        margin:10,
        paddingLeft:10,
        paddingRight:10,
        paddingTop:5,
        paddingBottom:5
    },
    titleTextStyle: {
        fontSize: 16,
        alignItems:"center",
        // textAlign:"center"
    },
    imgContentStyle: {
        marginTop:8,
        marginBottom:8,
        marginLeft:0,
        marginRight:0,
        width:screenWidth/6
    },
});