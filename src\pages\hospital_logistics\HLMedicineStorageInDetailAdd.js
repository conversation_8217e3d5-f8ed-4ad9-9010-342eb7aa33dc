import React,{Component} from 'react';
import {
    Alert,
    View, 
    ScrollView, Image,Text,TextInput,Modal, StyleSheet, FlatList,TouchableOpacity, Dimensions
} from 'react-native';

import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class HLMedicineStorageInDetailAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            detailId:'',
            searchKeyWord:"",

            selDetailId:0,
            medicineId:'',

            selMedicineId:"",
            selMedicineName:"",

            medicineDataSource:[],
            _medicineDataSource:[],

            medicineSpec:'',
            registrationNumber:'',
            productionAddr:'',
            productionBatchNumber:'',

            sterilizationBatchNumber:'',
            deliveryNumber:'',
            fundSource:'',
            invoiceNo:'',
            unitId:'',
            unitName:'',
            amount:0,

            //生产时间
            productDate:"",
            selectProductDate:[],
            //失效时间
            productExpDate:"",
            selectProductExpDate:[],
            //发票时间
            invoiceDate:"",
            selectInvoiceDate:[],

            salePrice:0,//零售单价
            saleTotalPrice:0,
            guidingPrice:0,//指导单价
            guidingTotalPrice:0,

            detailState:'',
            storageInId:'',
            modal:false,
            hlMedicineStorageInDetailDTOList:[],

            hospitalId:"",
            pharmacyId:""
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { storageInId,pharmacyId,hospitalId,hlMedicineStorageInDetailDTOList } = route.params;
            if (storageInId) {
                console.log("=============storageInId" + storageInId + "");
                this.setState({
                    storageInId:storageInId,
                })
            }
            if (hospitalId) {
                console.log("=========hospitalId:", hospitalId);
                this.setState({
                    hospitalId:hospitalId
                })
            }
            if (pharmacyId) {
                console.log("=========pharmacyId:", pharmacyId);
                this.setState({
                    pharmacyId:pharmacyId
                })
            }
            if (hlMedicineStorageInDetailDTOList) {
                console.log("=========hlMedicineStorageInDetailDTOList:", hlMedicineStorageInDetailDTOList);
                this.setState({
                    hlMedicineStorageInDetailDTOList:hlMedicineStorageInDetailDTOList
                })
            }
        }
        this.loadMedicineData();
         // 当前时间
         var currentDate = new Date();
         var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
         var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
         this.setState({
            selectProductDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            productDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
            selectProductExpDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            productExpDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay,
            selectInvoiceDate:[currentDate.getFullYear(), currentDateMonth, currentDateDay],
            invoiceDate:currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
         })
    }
    //加载药品数据
    loadMedicineData=()=>{
        let loadUrl= "/biz/hl/medicine/list";
        let loadRequest={'currentPage':1,'pageSize':100};
        httpPost(loadUrl, loadRequest, this.loadMedicineDataCallBack);
    }
    loadMedicineDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                medicineDataSource: response.data.dataList,
                medicineId:response.data.materialId
            })
            // this.loadBrickSeriesList();
            // console.log("===-=设置之后的id===" + this.state.brickClassId); 

        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    //加载物资
    loadMedicine = () => {
        var _medicineDataSource = copyArr(this.state.medicineDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _medicineDataSource = _medicineDataSource.filter(item => item.medicineName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _medicineDataSource: _medicineDataSource,
        })
    }

    loadProtalMedicineListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                medicineDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    //生产日期渲染
    openSelectProductDate(){
        this.refs.SelectProductDate.showDate(this.state.selectProductDate)
    }
    //生产日期
    callBackSelectProductDate(value){
        console.log("==========提交生产日期时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectProductDate:value
        })
        if (value && value.length) {
            var productDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    productDate += vartime;
                }
                else{
                    productDate += "-" + vartime;
                }
            }
            this.setState({
                productDate:productDate
            })
        }
    }

    //失效日期渲染
    openSelectProductExpDate(){
        this.refs.SelectProductExpDate.showDate(this.state.selectProductExpDate)
    }
    //失效日期
    callBackSelectProductExpDate(value){
        console.log("==========提交失效日期时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectProductExpDate:value
        })
        if (value && value.length) {
            var productExpDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    productExpDate += vartime;
                }
                else{
                    productExpDate += "-" + vartime;
                }
            }
            this.setState({
                productExpDate:productExpDate
            })
        }
    }

    //发票日期渲染
    openSelectInvoiceDate(){
        this.refs.SelectInvoiceDate.showDate(this.state.selectInvoiceDate)
    }
    //发票日期
    callBackSelectInvoiceDate(value){
        console.log("==========提交发票日期时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectInvoiceDate:value
        })
        if (value && value.length) {
            var invoiceDate = "";
            var vartime;
            for(var index=0;index<value.length;index++) {
                vartime = value[index];
                if (index===0) {
                    invoiceDate += vartime;
                }
                else{
                    invoiceDate += "-" + vartime;
                }
            }
            this.setState({
                invoiceDate:invoiceDate
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh(this.state.hlMedicineStorageInDetailDTOList)
                }
                this.props.navigation.navigate("HLMedicineStorageInAdd")                
            }}>
                <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                {/* <Text style={CommonStyle.headRightText}>完成</Text> */}
            </TouchableOpacity>
        )
    }
    // 渲染订单底部滚动数据
    openOrderSelect(){
        this.refs.SelectOrder.showOrder(this.state.selectOrder, this.state.orderDataSource)
    }
    renderMedicineItem=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.detailId) {
                    return;
                }
                this.setState({
                    selMedicineId:item.medicineId,
                    selMedicineName:item.medicineName,
                    unitId:item.unitId,
                    unitName:item.unitName,
                    medicineSpec:item.medicineSpec,
                    guidingPrice:item.guidingPrice,
                    salePrice:item.salePrice,
                })
            }}>
                <View key={item.medicineId} style={item.medicineId===this.state.selMedicineId? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.medicineId===this.state.selMedicineId? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.medicineName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    render(){
        return(
            <View>
                <CommonHeadScreen title={'新增明细'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}> 
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>药品</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[(!this.state.medicineDataSource || this.state.medicineDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={()=>{
                            if (this.state.medicineDataSource && this.state.medicineDataSource.length > 0) {
                                this.setState({
                                    _medicineDataSource: copyArr(this.state.medicineDataSource),
                                })
                            }
                            if (!this.state.medicineDataSource || this.state.medicineDataSource.length === 0) {
                                
                                let errorMsg = '暂无药品';
                                Alert.alert('确认', errorMsg, [
                                    {
                                        text: "确定", onPress: () => {
                                            WToast.show({ data: '点击了确定' });
                                        }
                                    }
                                ]);
                                return;
                            }

                            this.setState({
                                modal:true,
                                searchKeyWord: ""
                            })

                            if (!this.state.selMedicineId && this.state.medicineDataSource && this.state.medicineDataSource.length > 0) {
                                this.setState({
                                    selMedicineId:this.state.medicineDataSource[0].medicineId,
                                    selMedicineName:this.state.medicineDataSource[0].medicineName,
                                    unitName:this.state.medicineDataSource[0].unitName,
                                    unitId:this.state.medicineDataSource[0].unitId,
                                    medicineSpec:this.state.medicineDataSource[0].medicineSpec,
                                    guidingPrice:this.state.medicineDataSource[0].guidingPrice,
                                    salePrice:this.state.medicineDataSource[0].salePrice,
                                    productionAddr:this.state.medicineDataSource[0].productionAddr,
                                    registrationNumber:this.state.medicineDataSource[0].registrationNumber
                                })
                            }
                        }}>
                            <View style={[CommonStyle.blockItemViewStyle,{backgroundColor:'rgba(178,178,178,0.5)', padding:10, margin:5}]}>
                                <Text style={[CommonStyle.blockItemTextStyle16,{fontWeight:'bold'}]}>
                                    {this.state.selMedicineId && this.state.selMedicineName ? (this.state.selMedicineName) : "选择药品"}
                                </Text>
                            </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.modal}>
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={CommonStyle.modalContentViewStyle}>
                            <View style={CommonStyle.rowLabView}>
                                <View style={styles.leftLabViewImage}>
                                    <Image style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                                    <TextInput
                                        style={[styles.searchInputText]}
                                        returnKeyType="search"
                                        returnKeyLabel="搜索"
                                        onSubmitEditing={e => {
                                            this.loadMedicine();
                                    }}        
                                        placeholder={'药品名称'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                </View>
                                {/* <TouchableOpacity onPress={()=>{
                                    this.loadMedicine();
                                    }}>
                                    <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                        <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                    </View>
                                </TouchableOpacity> */}
                            </View>
                            <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._medicineDataSource && this.state._medicineDataSource.length > 0)
                                                ?
                                                this.state._medicineDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderMedicineItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                            </ScrollView>
                            <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                <TouchableOpacity onPress={() => { 
                                    this.setState({
                                        modal:false,
                                    }) 
                                }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                    <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => {
                                    if (!this.state.selMedicineId) {
                                        let toastOpts = getFailToastOpts("您还没有选择药品");
                                        WToast.show(toastOpts);
                                        return;
                                    }
                                    /**let loadUrl = "/biz/protal/supplier/list";
                                        let loadRequest = {
                                            "currentPage":1,
                                            "pageSize":1000,
                                            // 此处可能存在问题 yty
                                            "material":this.state.selMedicineId,
                                            // "partyA": this.state.selCustomerId,
                                        };
                                        httpPost(loadUrl, loadRequest, this.loadProtalMaterialListCallBack);*/
                                        this.setState({
                                            modal: false,
                                            amount:0,
                                            //materialTotalPrice:0
                                        })
                                }}>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                    <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                        <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>确定</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>规格型号</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle,, { width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.medicineSpec ? "请先选择药品" : this.state.medicineSpec}
                            </Text>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>注册证号</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.registrationNumber ? "请先选择药品" : this.state.registrationNumber}
                            </Text>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>送货单号</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            placeholder={'请输入送货单号'}
                            style={[styles.inputRightText]}
                            onChangeText={(text) => {
                                this.setState({deliveryNumber:text})
                            }}
                            >
                                {this.state.deliveryNumber}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>资金来源</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            placeholder={'请输入资金来源'}
                            style={[styles.inputRightText]}
                            onChangeText={(text) => {
                                this.setState({fundSource:text})
                            }}
                            >
                                {this.state.fundSource}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>发票号</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            placeholder={'请输入发票号'}
                            style={[styles.inputRightText]}
                            onChangeText={(text) => {
                                this.setState({invoiceNo:text})
                            }}
                            >
                                {this.state.invoiceNo}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                            发票日期
                            </Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>                     
                        <TouchableOpacity onPress={()=>this.openSelectInvoiceDate()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30)}]}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.invoiceDate ? "请选择发票日期" : this.state.invoiceDate}
                            </Text>
                        </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>生产地址</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.productionAddr ? "请先选择药品" : this.state.productionAddr}
                            </Text>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>生产批号</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            placeholder={'请输入生产批号'}
                            style={[styles.inputRightText]}
                            onChangeText={(text) => {
                                this.setState({productionBatchNumber:text})
                            }}
                            >
                                {this.state.productionBatchNumber}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                            生产日期
                            </Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>                     
                        <TouchableOpacity onPress={()=>this.openSelectProductDate()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.productDate ? "请选择生产日期" : this.state.productDate}
                            </Text>
                        </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>
                            失效日期
                            </Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>                     
                        <TouchableOpacity onPress={()=>this.openSelectProductExpDate()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{color:'#A0A0A0', fontSize:15}}>
                                {!this.state.productExpDate ? "请选择失效日期" : this.state.productExpDate}
                            </Text>
                        </View>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>灭菌批号</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            placeholder={'请输入灭菌批号'}
                            style={[styles.inputRightText]}
                            onChangeText={(text) => {
                                this.setState({sterilizationBatchNumber:text})
                            }}
                            >
                                {this.state.sterilizationBatchNumber}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>单位</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.unitName ? "请先选择药品" : this.state.unitName}
                            </Text>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>数量</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入数量'}
                            style={[styles.inputRightText]}
                            onChangeText={(text) => {
                                if (this.state.guidingPrice) {
                                    this.setState({
                                        guidingTotalPrice:(text*this.state.guidingPrice).toFixed(2)
                                    })
                                }
                                if (this.state.salePrice) {
                                    this.setState({
                                        saleTotalPrice:(text*this.state.salePrice).toFixed(2)
                                    })
                                }
                                this.setState({amount:text})
                            }}
                            >
                                {this.state.amount}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>指导价格</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[CommonStyle.inputTextStyleTextStyle,{ width: screenWidth - (leftLabWidth + 30) }]}>
                            <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                {!this.state.guidingPrice ? "请先选择药品" : this.state.guidingPrice}
                            </Text>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>零售价格</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入零售价格'}
                            style={[styles.inputRightText]}
                            onChangeText={(text) => {
                                if (this.state.salePrice||this.state.amount) {
                                    this.setState({
                                        saleTotalPrice:(text*this.state.amount).toFixed(2)
                                    })
                                }
                                this.setState({salePrice:text})
                            }}
                            >
                                {this.state.salePrice}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>指导总价：</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            editable= {false} 
                            //keyboardType='text'
                            placeholder={'请输入指导总价'}
                            // onChangeText={(text) => this.setState({materialTotalPrice:text})}
                            onChangeText={(text) => this.setState({guidingTotalPrice: guidingTotalPrice})}
                            style={[styles.inputRightText]}>
                            {this.state.guidingTotalPrice}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>零售总价：</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            editable = {false}
                            //keyboardType='text'
                            placeholder={'请输入总零售总价'}
                            // onChangeText={(text) => this.setState({materialTotalPrice:text})}
                            onChangeText={(text) => this.setState({saleTotalPrice: saleTotalPrice})}
                            style={[styles.inputRightText]}>
                            {this.state.saleTotalPrice}
                        </TextInput>
                    </View>
                    <View style={styles.btnRowView}>
                     <TouchableOpacity onPress={()=>{
                        if (!this.state.selMedicineId) {
                            WToast.show({data:"请选择药品"});
                            return;
                        }
                        if (!this.state.amount || this.state.amount === "0") {
                            WToast.show({data:"请输入数量"});
                            return;
                        }
                        var hlMedicineStorageInDetailDTO = {
                            _index:this.state.hlMedicineStorageInDetailDTOList.length,
                            medicineId: this.state.selMedicineId,
                            medicineName: this.state.selMedicineName,
                            medicineSpec:this.state.medicineSpec,
                            registrationNumber:this.state.registrationNumber,
                            deliveryNumber:this.state.deliveryNumber,
                            fundSource:this.state.fundSource,
                            invoiceNo:this.state.invoiceNo,
                            invoiceDate:this.state.invoiceDate,
                            productionAddr:this.state.productionAddr,
                            productionBatchNumber:this.state.productionBatchNumber,
                            productDate:this.state.productDate,
                            productExpDate:this.state.productExpDate,
                            sterilizationBatchNumber:this.state.sterilizationBatchNumber,
                            unitName:this.state.unitName,
                            unitId:this.state.unitId,
                            amount:this.state.amount,
                            guidingPrice: this.state.guidingTotalPrice,
                            salePrice: this.state.saleTotalPrice,
                            
                        }
                        var _hlMedicineStorageInDetailDTOList = this.state.hlMedicineStorageInDetailDTOList;
                        _hlMedicineStorageInDetailDTOList = _hlMedicineStorageInDetailDTOList.concat(hlMedicineStorageInDetailDTO);
                        this.setState({
                            hlMedicineStorageInDetailDTOList:_hlMedicineStorageInDetailDTOList
                        })
                        this.setState({
                            selMedicineId:"",
                            selMedicineName:"",
                            // materialDataSource:[],
                            medicineId: "",
                            medicineName: "",
                            registrationNumber:"",
                            deliveryNumber:"",
                            fundSource:"",
                            invoiceNo:"",
                            // invoiceDate:"",
                            productionAddr:"",
                            productionBatchNumber:"",
                            unitName:"",
                            medicineSpec:"",
                            unitId:'',
                            amount:0,
                            salePrice:0,
                            guidingPrice:0,
                            guidingTotalPrice:"",
                            saleTotalPrice:"",
                            sterilizationBatchNumber:""
                        })
                     }}>
                         <View style={{marginRight:screenWidth/8}}>
                         {/* <View style={[styles.btnAddView]}> */}
                             {/* <Text style={styles.btnAddText}>新增</Text> */}
                             <Image style={{ width:25, height:25,justifyContent:'center'}} source={require('../../assets/icon/iconfont/add1.png')}></Image>
                        </View>
                     </TouchableOpacity>
                 </View>
                 <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View>
                    <FlatList 
                    data={this.state.hlMedicineStorageInDetailDTOList}
                    renderItem={({item}) => 
                    <View key={item._index} style={styles.titleViewStyle}>
                        <View style={{ }}>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap",marginTop:10}]}>
                                名称：{item.medicineName}
                            </Text>
                        </View>
                        <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10,marginTop:10}]}>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>规格型号：{item.medicineSpec}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>单位：{item.unitName}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>数量：{item.amount}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>指导总价：{item.guidingPrice}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>零售总价：{item.salePrice}</Text>
                            </View>
                        </View>
                        <TouchableOpacity
                            style={{marginLeft:-145,zIndex:1000,marginTop:130}}
                            onPress={() => {
                                console.log("========deleteStorageInDetailDTO")
                                var urls = this.state.hlMedicineStorageInDetailDTOList;
                                urls.splice(item._index,1);                   
                                console.log(urls)
                                this.setState({
                                    hlMedicineStorageInDetailDTOList:urls,
                                })
                            }}
                        >
                            <View style={styles.btnDeleteView}>
                                <Text style={styles.btnDeleteText}>-删除</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    }
                    />
                </View>
                </ScrollView>
                <BottomScrollSelect 
                    ref={'SelectProductDate'} 
                    callBackDateValue={this.callBackSelectProductDate.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectProductExpDate'} 
                    callBackDateValue={this.callBackSelectProductExpDate.bind(this)}
                />
                <BottomScrollSelect 
                    ref={'SelectInvoiceDate'} 
                    callBackDateValue={this.callBackSelectInvoiceDate.bind(this)}
                />                
            </View>
        )
    }
}

const styles = StyleSheet.create({
    searchInputText: {
        width: screenWidth -100,
        // borderColor: '#000000',
        // borderBottomWidth: 1,
        // marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        // marginLeft: 0,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabViewImage: {
        height: 40,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        borderWidth:1,
        borderColor:"#E4E4E4",
        borderRadius:5,
        marginTop:5,
        // marginRight:5
    },   
    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', width:100, alignItems:'center', alignContent:'flex-end', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})