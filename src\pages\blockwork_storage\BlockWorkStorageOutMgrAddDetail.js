import React,{Component} from 'react';
import {
    Alert,
    View, 
    ScrollView, 
    Text, 
    TextInput, 
    StyleSheet, 
    FlatList ,
    TouchableOpacity,
    Dimensions,
    Image,
    Modal,
    KeyboardAvoidingView
} from 'react-native';

import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import _ from 'lodash';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import BottomScrollSelect from '../../component/BottomScrollSelect';

var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;

const leftLabWidth = 130;
export default class BlockWorkStorageOutMgrAddDetail extends Component {
    constructor(props) {
        super(props);
        this.state ={
            // 记录预砌出库的客户、合同名称
            customerId:"",
            customerName:"",
            contractId:"",
            contractName:"",

            checkOutDetailList:[],
            packageStyleEnumDataSource:[],
            orderPositionDataSource:[],
            _orderPositionDataSource:[],
            positionModal:false,
            searchKeyWord:"",
            searchCustomerKeyWord:"",
            // 详情数据
            selPositionId:"",
            selPositionName:"",
            blockworkName:"",
            outSetNumber:"",
            outWeight:"",
            selPackageStyle:"",
            selPackageStyleName:"",
            selPositionCurrentInventory:"",
            selCustomerId:"",
            selCustomerName:"",
            selContractId:"",
            selContractName:"",
            customerDataSource:[],
            _customerDataSource:[],
            contractDataSource:[],
            selectContract:[],
            modal:false,
            locationId:""
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        this.loadBlockworkLocation();
        this.loadCustomerData();
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { customerId,contractId, customerName, contractName} = route.params;
            if (customerId && customerName) {
                console.log("=============customerId" + customerId + "");
                this.setState({
                    customerId:customerId,
                    selCustomerId:customerId,
                    selCustomerName:customerName
                })
                let loadUrl = "/biz/contract/list";
                let loadRequest = {
                    "currentPage":1,
                    "pageSize":1000,
                    "partyA": customerId,
                };
                httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
            }
            if (contractId && contractName) {
                console.log("=============contractId" + contractId + "");
                this.setState({
                    contractId:contractId,
                    selContractId:contractId,
                    selContractName:contractName,
                    selectContract:[contractName]
                })
            }
        }
        var _packageStyleEnumDataSource = [{
            "code":"T",
            "name":"托盘"
        },{
            "code":"D",
            "name":"吨包"
        },{
            "code":"N",
            "name":"无"
        }];
        this.setState({
            packageStyleEnumDataSource:_packageStyleEnumDataSource,
            selPackageStyle:_packageStyleEnumDataSource[0].code,
            selPackageStyleName:_packageStyleEnumDataSource[0].name,
        })
        // 加载订单部位
        let url= "/biz/order/position/list";
        let loadRequest={
            "currentPage":1,
            "pageSize":10000
        };
        httpPost(url, loadRequest, this.loadOrderPositionListCallBack);
    }

    loadOrderPositionListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                orderPositionDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadBlockworkLocation=()=>{
        let loadUrl = "/biz/storage/location/getLocationByName";
        let loadRequest = {
            "locationName": "预砌库位",
        };
        httpPost(loadUrl, loadRequest, (response)=>{
            if (response.code == 200 && response.data) {
                this.setState({
                    locationId:response.data.locationId
                })
            }
            else if (response.code == 401) {
                WToast.show({data:response.message});
                this.props.navigation.navigate("LoginView");
            }
        });
    }

    // 搜索型号
    searchPosition = () => {
        var _orderPositionDataSource = copyArr(this.state.orderPositionDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _orderPositionDataSource = _orderPositionDataSource.filter(item => item.positionName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _orderPositionDataSource: _orderPositionDataSource,
        })
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} >
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => { 
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh(this.state.checkOutDetailList)
                }
                this.props.navigation.navigate("BlockWorkStorageOutMgrAdd") 
            }}>
                <Image style={{width:30, height:30}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                {/* <Text style={CommonStyle.headRightText}>完成</Text> */}
            </TouchableOpacity>
        )
    }

    // 运输方式
    renderPackageStyleRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { this.setState({
                selPackageStyle:item.code,
                selPackageStyleName:item.name
            }) }}>
                <View key={item.code} style={item.code===this.state.selPackageStyle? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle }>
                    <Text style={item.code===this.state.selPackageStyle? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.name}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    // 部位项
    renderPositionItem = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selPositionId: item.positionId,
                    selPositionName: item.positionName,
                })
                // 查询对应客户、合同、部位下的库存套数
                // let url= "/biz/brick/class/series/list";
                // let loadRequest={
                //     "currentPage":1,
                //     "pageSize":1000,
                //     "brickClassId":item.brickClassId,
                // };

                // httpPost(url, loadRequest, this.loadBrickSeriesListCallBack);
            }}>
                <View key={item.positionId} style={item.positionId === this.state.selPositionId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.positionId === this.state.selPositionId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.positionName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }
    loadCustomerData=()=>{
        let loadUrl= "/biz/tenant/customer/list";
        let loadRequest={'currentPage':1,'pageSize':100};
        httpPost(loadUrl, loadRequest, this.callBackLoadCustomerData);
    }

    callBackLoadCustomerData=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                customerDataSource:response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadContractListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                contractDataSource: response.data.dataList,
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    openContractSelect() {
        if (this.state.selOrderTypeList && this.state.selOrderTypeList.length > 0) {
            WToast.show({ data: "请先将产品删除再进行合同的选择" });
            return;
        }
        if (this.state.selCustomerId) {
            if (!this.state.contractDataSource || this.state.contractDataSource.length < 1) {
                WToast.show({ data: "请先添加合同" });
                return;
            }
            this.setState({
                orderDataSource: []
            })
            this.refs.SelectContract.showContract(this.state.selectContract, this.state.contractDataSource)
        }
        else {
            WToast.show({ data: "请先选择客户名称"});
            return;
        }
    }

    callBackContractValue(value) {
        console.log("==========合同选择的结果：", value)
        if (!value) {
            return;
        }
        var contractName = value.toString();
        this.setState({
            selectContract: value,
            selContractName: contractName,
            _orderDataSource:[]
        })
        // this.loadOrderList(contractName);
        let loadUrl= "/biz/contract/getContractByName";
        let loadRequest={
            "contractName":contractName
        };
        httpPost(loadUrl, loadRequest, this.loadContractDetailCallBack);
    }

    loadContractDetailCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                selContractId : response.data.contractId,
            })
            // this.loadOrderListByContractId(response.data.contractId);
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    loadCustomer = () => {
        var _customerDataSource = copyArr(this.state.customerDataSource);
        if (this.state.searchCustomerKeyWord && this.state.searchCustomerKeyWord.length > 0) {
            _customerDataSource = _customerDataSource.filter(item => item.customerName.indexOf(this.state.searchCustomerKeyWord) > -1);
        }
        this.setState({
            _customerDataSource: _customerDataSource,
        })
    }

    renderCustomerRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                if (this.state.storageOutId) {
                    return;
                }
                this.setState({
                    selCustomerId: item.customerId,
                    selCustomerName: item.customerName,
                    // selectContract:[],
                    // contractId:"",
                    // contractName:"",
                })
                // this.loadContractList()
                // let loadUrl = "/biz/contract/list";
                // let loadRequest = {
                //     "currentPage":1,
                //     "pageSize":1000,
                //     "customerId": item.customerId,
                // };
                // httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
            }}>
                <View key={item.customerId} style={[item.customerId === this.state.selCustomerId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}>
                    <Text style={item.customerId === this.state.selCustomerId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.customerName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title='新增出库部位'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>预砌名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            //keyboardType='text'
                            placeholder={'请输入预砌名称'}
                            onChangeText={(text) => this.setState({blockworkName:text})}
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                            {this.state.blockworkName}
                        </TextInput>
                    </View> */}
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>客户名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[(!this.state.customerDataSource || this.state.customerDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if (this.state.selOrderTypeList && this.state.selOrderTypeList.length > 0) {
                                    WToast.show({ data: "请先将产品删除再进行客户的选择" });
                                    return;
                                }
                                if (this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                                    this.setState({
                                        _customerDataSource: copyArr(this.state.customerDataSource),
                                    })
                                }
                                this.setState({
                                    modal: true,
                                    searchKeyWord: ""
                                })

                                if (!this.state.selCustomerId && this.state.customerDataSource && this.state.customerDataSource.length > 0) {
                                    this.setState({
                                        selCustomerId: this.state.customerDataSource[0].customerId,
                                        selCustomerName: this.state.customerDataSource[0].customerName,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, {height:40, flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                    {this.state.selCustomerId && this.state.selCustomerName ? 
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.selCustomerName}
                                        </Text>
                                        :
                                        <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        选择客户名称
                                        </Text>
                                    }
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>合同名称</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TouchableOpacity onPress={() => {this.openContractSelect()}}>
                            <View style={CommonStyle.inputTextStyleTextStyle}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.selContractName ? "请选择合同名称" : this.state.selContractName}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.modal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchCustomerKeyWord: text })}
                                    >
                                        {this.state.searchCustomerKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadCustomer();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._customerDataSource && this.state._customerDataSource.length > 0)
                                                ?
                                                this.state._customerDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderCustomerRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            modal: false,
                                            searchCustomerKeyWord:"",
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selCustomerId) {
                                            let toastOpts = getFailToastOpts("您还没有选择需货单位");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        let loadUrl = "/biz/contract/list";
                                        let loadRequest = {
                                            "currentPage":1,
                                            "pageSize":1000,
                                            "partyA": this.state.selCustomerId,
                                        };
                                        httpPost(loadUrl, loadRequest, this.loadContractListCallBack);
                                        this.setState({
                                            modal: false,
                                            searchCustomerKeyWord:"",
                                            selContractId:"",
                                            selContractName:"",
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>使用部位</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={[(!this.state.orderPositionDataSource || this.state.orderPositionDataSource.length === 0) ? CommonStyle.disableViewStyle : null]}>
                            <TouchableOpacity onPress={() => {
                                if(!this.state.selContractId){
                                    let toastOpts = getFailToastOpts("请选择合同名称");
                                    WToast.show(toastOpts);
                                    return;
                                }
                                if (!this.state._orderPositionDataSource || this.state._orderPositionDataSource.length === 0) {
                                    this.setState({
                                        _orderPositionDataSource: copyArr(this.state.orderPositionDataSource),
                                    })
                                }
                                this.setState({
                                    positionModal: true,
                                })

                                if (!this.state.selPositionId && this.state.orderPositionDataSource && this.state.orderPositionDataSource.length > 0) {
                                    this.setState({
                                        selPositionId: this.state.orderPositionDataSource[0].positionId,
                                        selPositionName: this.state.orderPositionDataSource[0].positionName,
                                    })
                                }
                            }}>
                                <View style={[CommonStyle.inputTextStyleTextStyleNoWidth, { height:40,flexWrap: 'wrap', backgroundColor: 'rgba(178,178,178,0.5)' }]}>
                                    <Text style={[CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                                        {this.state.selPositionId && this.state.selPositionName ? (this.state.selPositionName) : "选择使用部位"}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <Modal
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.positionModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.searchPosition();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._orderPositionDataSource && this.state._orderPositionDataSource.length > 0)
                                                ?
                                                this.state._orderPositionDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderPositionItem(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            positionModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        if (!this.state.selPositionId) {
                                            let toastOpts = getFailToastOpts("您还没有选择使用部位");
                                            WToast.show(toastOpts);
                                            return;
                                        }
                                        let url= "/biz/blockwork/inventory/list";
                                        let loadRequest={
                                            "customerId": this.state.selCustomerId,
                                            "contractId":this.state.selContractId,
                                            "positionId": this.state.selPositionId,
                                            "locationId":this.state.locationId
                                        };
                                        httpPost(url, loadRequest, (response)=>{
                                            if (response.code == 200 && response.data && response.data.dataList) {
                                                if(response.data.dataList.length > 0) {
                                                    var inventoryDto = response.data.dataList[0];
                                                    var setNumber = 0;
                                                    if(this.state.checkOutDetailList && this.state.checkOutDetailList.length > 0) {
                                                       var list =  this.state.checkOutDetailList.filter(item => {return item.customerId === this.state.selCustomerId 
                                                        && item.contractId === this.state.selContractId && item.positionId === this.state.selPositionId})
                                                       console.log("list===",list) 
                                                       list.forEach(item =>{
                                                            setNumber += item.outSetNumber*1
                                                        })
                                                    }
                                                    this.setState({
                                                        selPositionCurrentInventory:inventoryDto.storageInSetNumber - inventoryDto.storageOutSetNumber - setNumber
                                                    })    
                                                }
                                                else{
                                                    this.setState({
                                                        selPositionCurrentInventory:0
                                                    })
                                                }
                                            }
                                            else if (response.code == 401) {
                                                WToast.show({data:response.message});
                                                this.props.navigation.navigate("LoginView");
                                            }
                                        });
                                        this.setState({
                                            positionModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>库存套数</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            editable={false} 
                            keyboardType='numeric'
                            placeholder={'库存套数'}
                            onChangeText={(text) => this.setState({selPositionCurrentInventory:text})}
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                            {this.state.selPositionCurrentInventory}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>出库套数</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入出库套数'}
                            onChangeText={(text) => this.setState({outSetNumber:text})}
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                            {this.state.outSetNumber}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>重量(吨)</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入重量'}
                            onChangeText={(text) => this.setState({outWeight:text})}
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                            {this.state.outWeight}
                        </TextInput>
                    </View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>包装形式</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        <View style={{width:screenWidth - leftLabWidth - 10, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.packageStyleEnumDataSource && this.state.packageStyleEnumDataSource.length > 0) 
                                ? 
                                this.state.packageStyleEnumDataSource.map((item, index)=>{
                                    return this.renderPackageStyleRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>
                    <View style={styles.btnRowView}>
                        <TouchableOpacity onPress={()=>{
                            // if (!this.state.blockworkName) {
                            //     WToast.show({data:"请输入预砌名称"});
                            //     return;
                            // }
                            if (!this.state.selCustomerId || this.state.selCustomerId === "0") {
                                WToast.show({data:"请选择客户名称"});
                                return;
                            }
                            if (!this.state.selContractId || this.state.selContractId === "0") {
                                WToast.show({data:"请选择合同名称"});
                                return;
                            }
                            if (!this.state.selPositionId || this.state.selPositionId === "0") {
                                WToast.show({data:"请选择使用部位"});
                                return;
                            }
                            if (!this.state.outSetNumber || this.state.outSetNumber === "0") {
                                WToast.show({data:"请输入出库套数"});
                                return;
                            }
                            if (!this.state.outWeight || this.state.outWeight === "0") {
                                WToast.show({data:"请输入重量"});
                                return;
                            }
                            if (this.state.outSetNumber  > this.state.selPositionCurrentInventory) {
                                WToast.show({data:"出库套数不能大于当前库存套数[" + this.state.selPositionCurrentInventory + "]"});
                                return;
                            }
                            var storageOutDetailDTO = {
                                "_index":this.state.checkOutDetailList.length,
                                // "blockworkName": this.state.blockworkName,
                                "customerId": this.state.selCustomerId,
                                "customerName": this.state.selCustomerName,
                                "contractId": this.state.selContractId,
                                "contractName": this.state.selContractName,
                                "positionId": this.state.selPositionId,
                                "positionName": this.state.selPositionName,
                                "outSetNumber":this.state.outSetNumber,
                                "outWeight": this.state.outWeight,
                                "packageStyle":this.state.selPackageStyle,
                                "packageStyleName":this.state.selPackageStyleName,
                            }
                            var _checkOutDetailList = this.state.checkOutDetailList;
                            _checkOutDetailList = _checkOutDetailList.concat(storageOutDetailDTO);
                            this.setState({
                                checkOutDetailList:_checkOutDetailList
                            })
                            this.setState({
                                // blockworkName:"",
                                selCustomerId:"",
                                selCustomerName:"",
                                selContractId:"",
                                selContractName:"",
                                selPositionId:"",
                                selPositionName:"",
                                outSetNumber:"",
                                outWeight:"",
                                selPositionCurrentInventory:0,
                                selPackageStyle:this.state.packageStyleEnumDataSource[0].code,
                                selPackageStyleName:this.state.packageStyleEnumDataSource[0].name,
                            })
                        }}>
                            <View style={[styles.btnAddView]}>
                                <Text style={styles.btnAddText}>新增</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={CommonStyle.rowSplitViewStyle}></View>
                 <View>
                    <FlatList 
                    data={this.state.checkOutDetailList}
                    renderItem={({item}) => 
                    <View key={item._index} style={styles.titleViewStyle}>
                        <View style={{ }}>
                            {/* <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                预砌名称：{item.blockworkName}
                            </Text> */}
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                客户名称：{item.customerName}
                            </Text>
                            <Text style={[styles.titleTextStyle,{width:screenWidth * 0.5,flexWrap:"wrap"}]}>
                                合同名称：{item.contractName}
                            </Text>
                        </View>
                        <View style={[{width:screenWidth * 0.4,flexWrap:"wrap", marginLeft:5, marginRight:10}]}>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>使用部位：{item.positionName}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={styles.titleTextStyle}>出库套数：{item.outSetNumber}</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>重量：{item.outWeight}吨</Text>
                            </View>
                            <View style={[styles.itemContentChildViewStyle]}>
                                <Text style={[styles.titleTextStyle]}>包装形式：{item.packageStyleName}</Text>
                            </View>
                        </View>
                    </View>}
                    />
                    </View>
                </ScrollView>
                <BottomScrollSelect
                ref={'SelectContract'}
                callBackContractValue={this.callBackContractValue.bind(this)}
                />
            </KeyboardAvoidingView>
        )
    }
}

const styles = StyleSheet.create({

    contentViewStyle:{
        // backgroundColor:'yellow',
        height:screenHeight - 90,
        // marginBottom:60
    },
    headRightText:{
        color:'#A0A0A0',
        fontSize:14,
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },


    btnRowView:{
        flexDirection:'row', justifyContent:'flex-end', marginTop:10,paddingRight:10
    },
    btnAddView:{
        backgroundColor:'#CE3B25', height:35, paddingLeft:10, paddingRight:10, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnAddText:{
        color:'#FFFFFF', fontSize:15
    },
    btnDeleteView:{
        backgroundColor:'#FFFFFF', height:35, borderColor:'#999999', borderWidth:1,paddingLeft:20, paddingRight:20, marginRight:15, justifyContent:'center',borderRadius:3
    },
    btnDeleteText:{
        color:'#999999', fontSize:15
    },

    titleTextStyle:{
        fontSize:16
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
})