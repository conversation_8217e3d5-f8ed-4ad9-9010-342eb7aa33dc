import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert, Image,
    FlatList, RefreshControl,Modal,ImageBackground,TextInput,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class HarvestGoodMgrList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource: [],
            text: '初始状态',
            refreshing: false,
            pageSize: 15,
            currentPage: 1,
            totalPage: 1,
            totalRecord: 1,
            discussModal:false,
        }
    }

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout(() => {
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    //更新State
    _updateState(message, refresh) {
        this.setState({ text: message, refreshing: refresh });
    }

    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        this.loadHarvestList();
    }

    // 回调函数
    callBackFunction = () => {
        let url = "/biz/harvest/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "visible": "Y",
            "harvestState": "0AA"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url = "/biz/harvest/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "visible": "Y",
            "harvestState": "0AA"
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }
    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadHarvestList();
    }

    loadHarvestList = () => {
        let url = "/biz/harvest/list";
        let loadRequest = {
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "visible": "Y",
            "harvestState": "0AA"
        };
        httpPost(url, loadRequest, this.loadHarvestListCallBack);
    }

    loadHarvestListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {

            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }

    setGood = (harvestItem, index) => {
        console.log("=======setHarvestGood=harvestItem", harvestItem);
        let requestUrl = "/biz/harvest/modify_score";
        let requestParams = {
            'harvestId': harvestItem.harvestId,
            'score': harvestItem.score
        };
        httpPost(requestUrl, requestParams, (response) => {
            if (response.code == 200) {
                // 更新页面上显示
                harvestItem.score = (harvestItem.score === 1 ? 0 : 1);
                let harvestDataSource = this.state.dataSource;
                // JS 数组遍历
                harvestDataSource.forEach((obj) => {
                    if (obj.harvestId === harvestItem.harvestId) {
                        obj.score = harvestItem.score;
                        WToast.show({ data: (harvestItem.score === 1 ? '评优' : '取消评优') + "完成" });
                    }
                })
                this.setState({
                    dataSource: harvestDataSource,
                })
            }
            else {
                WToast.show({ data: response.message });
            }
        });
    }

        // 保存留言
        saveMessage =()=> {
            console.log("=======saveMessage");
            let toastOpts;
            if (!this.state.messageContent) {
                toastOpts = getFailToastOpts("请输入留言内容");
                WToast.show(toastOpts)
                return;
            }
            let url= "/biz/portal/message/board/add";
            let requestParams={
                messageContent:this.state.messageContent,
                messageFkId: this.state.harvestItem.harvestId,
                parentMessageId: this.state.parentMessageId,
                messageFkType:"C"
            };
            httpPost(url, requestParams, this.saveMessageCallBack);
        }
        
        // 保存留言的回调函数
        saveMessageCallBack=(response)=>{
            this.setState({
                messageContent: ""
            })
            let toastOpts;
            switch (response.code) {
                case 200:
                    WToast.show({ data: "留言发送成功" });
                    this.callBackFunction();
                default:
                    toastOpts = getFailToastOpts(response.message);
                    WToast.show({data:response.message})
                }
        }
    
    renderRow = (item, index) => {
        return (
            <View key={item.harvestId} style={[CommonStyle.innerViewStyle]}>
                {/* 成果顶部信息 */}
                <View style={{flexDirection: 'row', marginLeft: 14, marginTop: 11}}>
                    <View style={{height: 48,width:48,borderRadius:20, marginTop:4,paddingTop: 3,justifyContent: "center",alignItems: "center",backgroundColor:'#1E6EFA'}}>
                        {
                            item.userName.length <= 2 ? 
                            <Text style={{color:'rgba(255,255,255,1)',fontSize:17,fontFamily:'PingFangSC-Regular',fontWeight:"normal",textAlign:'center', lineHeight:20}}>
                                {item.userName}
                            </Text>
                            :
                            <Text style={{color:'rgba(255,255,255,1)',fontSize:17,fontFamily:'PingFangSC-Regular',fontWeight:"normal",textAlign:'center', lineHeight:20,}}>
                                {item.userName.slice(-2)}
                            </Text>
                        }
                    </View>
                    <View style={{marginLeft:11, flexDirection: 'column'}}>
                        <View style={{flexDirection: 'row', marginTop: 4 }}>
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={{ fontSize: 16 }}>{item.userName}的成果</Text>
                            </View>
                            {/* 草稿,优秀,私密,无 */}
                            {
                                    item.harvestState === "0BB" ?
                                    <View style={{ width: 38, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#E63633' }}>
                                        <Text style={{fontSize: 13, color: '#FFFFFF' }}>草稿</Text>
                                    </View>
                                    :
                                    <View></View>
                                }
                                {
                                    item.harvestState === "0AA" && item.visible === 'N' ?
                                    <View style={{ width: 38, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#B0B9BF' }}>
                                        <Text style={{fontSize: 13, color: '#FFFFFF' }}>私密</Text>
                                    </View>
                                        :
                                    <View></View>
                                }
                                {
                                    item.harvestState === "0AA" && item.visible === 'Y' && item.score ===1 ?
                                    <View style={{ width: 55, height: 20, marginLeft: 7, borderRadius: 2, flexDirection: 'row', justifyContent:'center', alignItems: 'center', backgroundColor:'#E63633' }}>
                                        <Image style={{ width: 16, height: 18, marginRight: 2, marginRight: 3 }} source={require('../../assets/icon/good.png')}></Image>
                                        <Text style={{fontSize: 13, color: '#FFFFFF' }}>优秀</Text>
                                    </View>
                                        :
                                        <View></View>
                                }
                        </View>
                        <View style={{flexDirection: 'row'}}>
                        <Image style={{ height: 13 , width: 12, marginTop: 5, marginLeft: 1, marginRight: 5}} source={require('../../assets/icon/iconfont/clock.png')}></Image> 
                            {
                                item.gmtModified == null ?
                                <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                                    <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)' }]}>{item.gmtCreated.slice(0,10)} 提交</Text>
                                </View>
                                :
                                <View style={{marginTop: 4, marginBottom: 3, marginRight: 4 }}>
                                    <Text style={[{fontSize: 12, color: 'rgba(0, 10, 32, 0.65)' }]}>{item.gmtModified.slice(0,10)} 提交</Text>
                                </View>
                            }
                        </View>
                    </View>
                </View>

                {/* 分隔线 */}
                <View style={styles.lineViewStyle}/>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={{fontSize:16}}>标题</Text>
                </View> */}
                <View style={styles.itemContentTextStyle}>
                    <Text style={[styles.itemContentStyle,{marginLeft: 15,marginTop:10,fontWeight: 'bold',fontSize: 20,color: '#404956'}]}>{item.harvestTitle}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>内容</Text>
                </View> */}
                <View style={styles.itemContentTextStyle}>
                    <Text style={[styles.itemContentStyle,{fontSize:16,marginLeft: 15,marginTop:5}]}>{item.harvestContent}</Text>
                </View>
                {/* <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>是否被评优：</Text>
                    <Text style={styles.itemContentStyle}>{item.score === 1 ? '已评优' : '未评优'}</Text>
                </View> */}
                
                {/* {
                    item.gmtModified == null ?
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>提交时间：</Text>
                            <Text style={styles.itemContentStyle}>{item.gmtCreated}</Text>
                        </View>
                        :
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>提交时间：</Text>
                            <Text style={styles.itemContentStyle}>{item.gmtModified}</Text>
                        </View>
                }
                {
                    item.scoreTime ?
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>评优时间：</Text>
                            <Text style={styles.itemContentStyle}>{item.scoreTime}</Text>
                        </View>
                        :
                        null
                } */}

                {/* <View style={{
                    width: 40, height: 40,
                    backgroundColor: 'rgba(255,0,0,0.0)',
                    position: 'absolute',
                    alignItems: 'center',
                    justifyContent: 'center',
                    right: 10,
                    top: 5,
                }}>
                    <TouchableOpacity onPress={() => {
                        this.setGood(item)
                    }}>
                        {
                            item.score ?
                                <Image style={{ width: 50, height: 50 }} source={require('../../assets/icon/iconfont/good-red.png')}></Image>
                                :
                                <Image style={{ width: 50, height: 50 }} source={require('../../assets/icon/iconfont/goodgrey2.png')}></Image>
                        }
                    </TouchableOpacity>
                </View> */}
                {
                    (item.messageList && item.messageList.length > 0) ?
                    <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap', marginLeft: 12, marginRight: 16 }]}>
                        {
                            item.harvestState === "0BB" ?
                                <View></View>
                                :
                                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            discussModal: true,
                                            harvestItem: item
                                        })
                                    }}>
                                        <View style={[{width: 78, height: 28, flexDirection: "row", alignItems: 'center', margin: 10, 
                                            marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                                        }]}>
                                            <Image style={{ width: 20, height: 20, marginRight: 8, marginLeft: 12 }} source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                                            <Text style={[{ color: 'rgba(0, 10, 32, 0.65)', fontSize: 14, lineHeight: 24 }]}>留言</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                        }
                    </View>
                    :
                    <View></View>
                }
                
                {/* 留言 */}
                {
                    (item.messageList && item.messageList.length > 0) ?
                        <View style={{backgroundColor:'rgba(242, 245, 252, 0.5)', borderRadius:10,width:screenWidth-24, marginLeft: 12, marginRight: 12, paddingTop: 5, marginBottom: 5}}>
                            {
                                item.messageList.map((item, index)=>{
                                    return(
                                        <View key={item.messageId} style={{ flexDirection: 'row', marginLeft: 10, marginTop: 10, marginBottom: 10}}>
                                            {
                                                item.operatorPhoto ?
                                                    <Image source={{ uri: (constants.image_addr + '/' + item.operatorPhoto) }} style={{ height: 36, width: 36, borderRadius: 50}} />
                                                    :
                                                    <ImageBackground source={require('../../assets/icon/iconfont/profilePicture.png')} style={{ height: 36, width: 36}}>
                                                        <View style={{height: 36, width: 36,justifyContent: "center",alignItems: "center"}}>
                                                            {
                                                                item.operatorName <= 2 ? 
                                                                <Text style={{color:'#FFFFFF',fontSize:13,fontFamily:'PingFangSC-Regular',fontWeight:"normal",textAlign:'center', lineHeight:20}}>
                                                                    {item.operatorName}
                                                                </Text>
                                                                :
                                                                <Text style={{color:'#FFFFFF',fontSize:13,fontFamily:'PingFangSC-Regular',fontWeight:"normal",textAlign:'center', lineHeight:20,}}>
                                                                    {item.operatorName.slice(-2)}
                                                                </Text>
                                                            }
                                                        </View>
                                                    </ImageBackground>
                                            }

                                            <View style={{ flexDirection: 'column', marginLeft: 10, flex: 1}}>
                                                <View style={{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'center', paddingTop: 4 }}>
                                                    <View style={{ flexDirection: 'row'}}>
                                                        <Text style={{ fontFamily: 'PFSC-Regular', fontSize: 16 }}>{item.operatorName}</Text>
                                                    </View>
                                                    <View style={{ flexDirection: 'row', marginLeft: 6}}>
                                                        <Text style={[{ fontSize: 12, color: 'rgba(0,10,32,0.45)' }]}>{item.gmtCreated.slice(0,16)}</Text>
                                                    </View>
                                                    
                                                </View>

                                                {
                                                    item.parentMessageId ?
                                                        <View style={[{flexDirection: 'column', justifyContent: 'flex-start'}]}>
                                                            <View style={[{flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'flex-start', marginLeft: 9, marginTop: 11}]}>
                                                                <Text style={[styles.itemContentStyle, {color:'rgba(0,10,32,0.45)'}]}>{"回复 "+ item.parentUserName + ": "+ item.parentMessageContent}</Text>
                                                            </View>
                                                            <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 8}]}>
                                                                <Text style={styles.itemContentStyle}>{item.messageContent}</Text>
                                                            </View>
                                                        </View>
                                                        :
                                                        <View style={[{ flexDirection: 'row', justifyContent:'flex-start', alignItems: 'flex-start', marginTop: 10}]}>
                                                            <Text style={styles.itemContentStyle}>{item.messageContent}</Text>
                                                        </View>
                                                }
                                            </View>
                                        </View>
                                    )                           
                                })
                            }
                        </View>
                        :
                        <View/>
                }
                <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                    {
                        (item.messageList && item.messageList.length > 0) ?
                        <View></View>
                        :
                        <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap', marginLeft: 12, marginRight: 1 }]}>
                            {
                                item.harvestState === "0BB" ?
                                    <View></View>
                                    :
                                    <View style={[CommonStyle.itemBottomBtnStyle, { flexWrap: 'wrap' }]}>
                                        <TouchableOpacity onPress={() => {
                                            this.setState({
                                                discussModal: true,
                                                harvestItem: item
                                            })
                                        }}>
                                            <View style={[{width: 78, height: 28, flexDirection: "row", alignItems: 'center', margin: 10, 
                                                marginRight: 0, //borderWidth: 0.85, borderRadius: 6
                                            }]}>
                                                <Image style={{ width: 20, height: 20, marginRight: 8, marginLeft: 12 }} source={require('../../assets/icon/iconfont/messageBlack.png')}></Image>
                                                <Text style={[{ color: 'rgba(0, 10, 32, 0.65)', fontSize: 14, lineHeight: 24 }]}>留言</Text>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                            }
                        </View>
                    }
                    <TouchableOpacity onPress={() => {
                        this.setGood(item);
                    }}>
                        {
                            item.score === 1 ?
                            <View style={{backgroundColor: "#FD4246", flexDirection: "row", marginRight: 16, borderRadius: 3,width: 95,height: 28,justifyContent: 'center',alignItems: 'center',margin: 10,}}>
                                <Image style={{ width: 16, height: 18, marginRight: 2, marginRight: 3,marginTop:3 }} source={require('../../assets/icon/noGood.png')}></Image>
                                <Text style={{color: '#F0F0F0',fontSize: 16}}>取消评优</Text>
                            </View>
                            :
                            <View style={{backgroundColor: "#FD4246", flexDirection: "row", marginRight: 16, borderRadius: 3,width: 65,height: 28,justifyContent: 'center',alignItems: 'center',margin: 10,}}>
                                <Image style={{ width: 16, height: 18, marginRight: 2, marginRight: 3 }} source={require('../../assets/icon/good.png')}></Image>
                                <Text style={{color: '#F0F0F0',fontSize: 16}}>评优</Text>
                            </View>
                        }
                    </TouchableOpacity>
                    
                    {/* {
                        item.score === 1 ?
                        <TouchableOpacity onPress={() => {
                            // item.score:0,
                            this.setGood(item);
                            }}>
                            <View style={{backgroundColor: "#FD4246", flexDirection: "row", marginRight: 16, borderRadius: 3,width: 80,height: 28,justifyContent: 'center',alignItems: 'center',margin: 10,}}>
                                <Image style={{ width: 16, height: 18, marginRight: 2, marginRight: 3, }} source={require('../../assets/icon/noGood.png')}></Image>
                                <Text style={{color: '#F0F0F0',fontSize: 16}}>不评优</Text>
                            </View>
                        </TouchableOpacity>
                        :
                        <TouchableOpacity onPress={() => {this.setGood(item);}}>
                            <View style={{backgroundColor: "#FD4246", flexDirection: "row", marginRight: 16, borderRadius: 3,width: 65,height: 28,justifyContent: 'center',alignItems: 'center',margin: 10,}}>
                                <Image style={{ width: 16, height: 18, marginRight: 2, marginRight: 3 }} source={require('../../assets/icon/good.png')}></Image>
                                <Text style={{color: '#F0F0F0',fontSize: 16}}>评优</Text>
                            </View>
                        </TouchableOpacity>
                    } */}
                    
                </View>

                {/* 留言输入框弹窗 */}
                <Modal
                    animationType='slide'
                    transparent={true}
                    visible={this.state.discussModal}
                >
                    <TouchableOpacity style={{flex: 1, position: 'relative'}}
                        onPress={() => {
                            this.setState({
                                discussModal: false,
                                messageContent: ""
                            })
                    }}>
                        <View style={{backgroundColor: '#FFFFFF', flexDirection: 'row', alignItems: 'center',
                            position: 'absolute', width: '100%', left: 0, bottom: 0, padding: 5
                        }}>
                            <TextInput 
                                autoFocus
                                multiline={true}
                                placeholder="小小鼓励，让团队更凝聚"
                                style={{backgroundColor: '#F2F5FC', flex: 5, borderRadius: 15, height: 40, marginLeft: 10, paddingLeft: 15}}
                                onChangeText={(text) => this.setState({ messageContent: text })}
                            />
                            <TouchableOpacity onPress={() => {
                                if (!this.state.messageContent) {
                                    return;
                                }
                                this.setState({
                                    discussModal: false,
                                })
                                this.saveMessage();
                            }}>
                                <View style={[CommonStyle.itemBottomDetailBtnViewStyle,{flex: 1,width: 64, height: 32, backgroundColor: '#1E6EFA', borderRadius: 20 }, 
                                    (this.state.messageContent) ? "" : CommonStyle.disableViewStyle]}>
                                    <Text style={[CommonStyle.itemBottomDetailBtnTextStyle, { textAlign: 'center', fontSize: 14 }]}>发送</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </TouchableOpacity>
                </Modal>
            </View >
        )
    }
    space() {
        return (<View style={{ height: 1, backgroundColor: '#F0F0F0' }} />)
    }
    emptyComponent() {
        return <EmptyListComponent />
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View />
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title='成果评优'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                {/* <View style={CommonStyle.rightTop50FloatingBlockView}>
                    <Text style={CommonStyle.rightTop50FloatingBlockText}>{this.state.dataSource.length}</Text>
                </View> */}
                <View style={CommonStyle.contentViewStyle}>
                    <FlatList
                        data={this.state.dataSource}
                        keyExtractor={(item) => item.harvestId}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 70,
    //     backgroundColor:'#FFFFFF'
    // },
    innerViewStyle: {
        // marginTop:10,
        borderColor: "#F4F4F4",
        borderWidth: 8
    },
    itemContentImageStyle: {
        width: 120,
        height: 120
    },
    itemContentViewStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 25
    },
    itemContentChildViewStyle: {
        flexDirection: 'column'
    },
    itemContentChildTextStyle: {
        marginLeft: 10,
        marginTop: 15,
        fontSize: 16
    },
    itemContentStyle: {
        fontSize: 14,
        lineHeight: 24,
        textAlign: 'left',
        textAlignVertical: 'top',
        color: 'rgba(0, 10, 32, 0.65)'
    },
    itemContentTextStyle: {
        marginLeft: 12,
        marginRight: 16,
        marginTop: 3,
        lineHeight: 24,
    },
    titleViewStyle: {
        flexDirection: 'row',
        marginLeft: 12,
        marginRight: 16,
        marginTop: 5,
    },
    titleTextStyle: {
        fontSize: 16
    },
    lineViewStyle:{
        height:1,
        marginLeft: 13,
        marginRight: 13,
        marginTop: 15,
        marginBottom: 6,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },
});