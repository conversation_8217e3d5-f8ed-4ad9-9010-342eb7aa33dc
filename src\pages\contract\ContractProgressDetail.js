import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
export default class ContractProgressDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            contractId:"",
            contractItem:{},
            planList:[],
            actualList:[],
            orderList:[],
        }
    }

    


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { contractId } = route.params;
            if (contractId) {
                console.log("=============contractId" + contractId + "");
                this.setState({
                    contractId:contractId
                })
                this.loadContractProgress(contractId);
            }
        }
    }

    loadContractProgress=(contractId)=>{
        let url= "/biz/contract/progress/get";
        let loadRequest={
            contractId:contractId
        };
        httpPost(url, loadRequest, this.loadContractProgressCallBack);

    }

    loadContractProgressCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                contractItem:response.data,
                planList:response.data.planList,
                actualList:response.data.actualList,
                orderList:response.data.orderList
            })
        }
    }

    changeTwoDecimal=(x)=>{
        var f_x = parseFloat(x);
        if (isNaN(f_x))
        {
            alert('function:changeTwoDecimal->parameter error');
            return false;
        }
        f_x = Math.round(f_x *100)/100;

        return f_x;
    }
    

    

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[{flexDirection: 'row', alignItems: 'center'}]}>
                    <Image  style={{width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={{ color: '#3C6CDE', fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View style={{ flexDirection: 'row-reverse', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { 

                }}  >
                    <Image style={{ width:22, height:22, marginVertical: 2}} source={require('../../assets/icon/iconfont/add.png')}></Image>
                </TouchableOpacity>
            </View>
        )
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='进度详情'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.innerViewStyle}>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>合同名称：{this.state.contractItem.contractName}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>客户名称：{this.state.contractItem.customerName}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>合同编号：{this.state.contractItem.contractCode}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>签订时间：{this.state.contractItem.signingTime}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>合同金额：{this.state.contractItem.contractAmount ? this.state.contractItem.contractAmount:"无"}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>合同重量：{this.state.contractItem.contractWeight ? (this.state.contractItem.contractWeight+"吨"):"无"}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>交付日期：{this.state.contractItem.deliveryDate ? this.state.contractItem.deliveryDate:"无"}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>实结重量（吨）：{this.state.contractItem.actualWeight ? this.state.contractItem.actualWeight : "无"}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>实结金额：{this.state.contractItem.actualAmount ? this.state.contractItem.actualAmount : "无" }</Text>
                        </View>
                        {/* <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>甲方联系人：{this.state.contractItem.partyAContact ? this.state.contractItem.partyAContact:"无"}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>甲方联系电话：{this.state.contractItem.partyATel ? this.state.contractItem.partyATel:"无"}</Text>
                        </View> */}
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>乙方联系人：{this.state.contractItem.partyBContact ? this.state.contractItem.partyBContact:"无"}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>乙方联系电话：{this.state.contractItem.partyBTel ? this.state.contractItem.partyBTel:"无"}</Text>
                        </View>
                        <View style={styles.bodyViewStyle}>
                            <Text style={[CommonStyle.bodyTextStyle,{}]}>创建时间：{this.state.contractItem.gmtCreated}</Text>
                        </View>
                    </View>
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>收款计划</Text>
                    </View>
                    <View>
                    {
                    (this.state.planList && this.state.planList.length > 0) 
                    ? 
                    this.state.planList.map((item, index)=>{
                        return(
                            <View key={item.planId} style={styles._innerViewStyle}>
                                <View style={[styles._titleViewStyle]}>
                                    <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:18}]}>{item.pointName}</Text>
                                </View>
                                <View style={styles._titleViewStyle}>
                                    <Text style={styles._titleTextStyle}>计划收款日期：{item.planDate}</Text>
                                </View>
                                <View style={styles._titleViewStyle}>
                                    <Text style={styles._titleTextStyle}>计划收款比例：{item.planProportion}</Text>
                                </View>
                                <View style={styles._titleViewStyle}>
                                    <Text style={styles._titleTextStyle}>计划收款金额：{item.planAmount}</Text>
                                </View>
                            </View>
                        )                           
                })
                    : <EmptyRowViewComponent/> 
                    
                    
                    }
                    </View>
                    
                    <View style={CommonStyle.addItemSplitRowView}>
                        <Text style={CommonStyle.addItemSplitRowText}>实际收款</Text>
                    </View>
                    <View>
                    {
                        (this.state.actualList && this.state.actualList.length > 0) 
                        ? 
                        this.state.actualList.map((item, index)=>{
                            return(
                                <View key={item.planId} style={styles._innerViewStyle}>
                                    <View style={[styles._titleViewStyle]}>
                                        <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:18}]}>{item.pointName}</Text>
                                    </View>
                                    <View style={styles._titleViewStyle}>
                                        <Text style={styles._titleTextStyle}>实际收款日期：{item.actualDate}</Text>
                                    </View>
                                    <View style={styles._titleViewStyle}>
                                        <Text style={styles._titleTextStyle}>实际收款比例：{item.actualProportion}</Text>
                                    </View>
                                    <View style={styles._titleViewStyle}>
                                        <Text style={styles._titleTextStyle}>实际收款金额：{item.actualAmount}</Text>
                                    </View>
                                </View>
                            )                           
                        })
                        : <EmptyRowViewComponent/> 
                    
                    }
                    </View>
                    <View>
                    {this.state.orderList.map((item, index)=>{
                                return(
                                    <View key={item.planId} style={styles._innerViewStyle}>
                                        <View style={CommonStyle.addItemSplitRowView}>
                                            <Text style={CommonStyle.addItemSplitRowText}>{item.orderName}执行进度情况</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>砖型：{item.brickTypeName}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>部位：{item.positionName ? item.positionName:"无"}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>数量：{item.brickAmount?item.brickAmount:"无"}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>重量：{item.orderWeight?item.orderWeight:"无"}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>当前状态：{item.orderState}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>创建时间：{item.gmtOrderCreated}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>生产单位：{item.outSourcingTenantName}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>计划生产数量：{item.plannedProductionQuantity ? item.plannedProductionQuantity : "无"}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>理论单重（Kg）：{item.theorySingleWeight ? item.theorySingleWeight : "无"}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>理论总重（吨）：{item.theoryTotalWeight ? item.theoryTotalWeight : "无"}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>预计生产时间：{item.schedulingProductionTime}</Text>
                                        </View>
                                        <View style={styles._titleViewStyle}>
                                            <Text style={styles._titleTextStyle}>预计完成时间：{item.schedulingCompletedTime}</Text>
                                        </View>
                                        <View>
                                        {item.orderStateList.map((item1, index)=>{
                                            if(item1.orderStateName == "开始加工半成品"){
                                                return(
                                                    <View key={item1.recordId} style={styles._innerViewStyle}>
                                                        <View style={[styles._titleViewStyle]}>
                                                            <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:18}]}>半成品加工</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>已完成数量：{item1.completeNumber}</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>已完成吨数：{item.theorySingleWeight ? this.changeTwoDecimal(item1.completeNumber*item.theorySingleWeight*0.96/1000) : "无"}</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>更新时间：{item1.gmtModified?item1.gmtModified : item1.gmtCreated}</Text>
                                                        </View>
                                                    </View>
                                                )                           
                                            }
                                            if(item1.orderStateName == "开始装窑"){
                                                return(
                                                    <View key={item1.recordId} style={styles._innerViewStyle}>
                                                        <View style={[styles._titleViewStyle]}>
                                                            <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:18}]}>装窑</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>已完成数量：{item1.completeNumber}</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>已完成吨数：{item.theorySingleWeight ? this.changeTwoDecimal(item1.completeNumber*item.theorySingleWeight/1000) : "无"}</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>更新时间：{item1.gmtModified?item1.gmtModified:item1.gmtCreated}</Text>
                                                        </View>
                                                    </View>
                                                )                           
                                            }
                                            if(item1.orderStateName == "开始烧制"){
                                                return(
                                                    <View key={item1.recordId} style={styles._innerViewStyle}>
                                                        <View style={[styles._titleViewStyle]}>
                                                            <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:18}]}>烧制</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>已完成数量：{item1.completeNumber}</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>已完成吨数：{item.theorySingleWeight ? this.changeTwoDecimal(item1.completeNumber*item.theorySingleWeight/1000) : "无"}</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>更新时间：{item1.gmtModified?item1.gmtModified:item1.gmtCreated}</Text>
                                                        </View>
                                                    </View>
                                                )                           
                                            }
                                            if(item1.orderStateName == "开始打包入库"){
                                                return(
                                                    <View key={item1.recordId} style={styles._innerViewStyle}>
                                                        <View style={[styles._titleViewStyle]}>
                                                            <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:18}]}>成品入库</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>已完成数量：{item1.completeNumber}</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>已完成吨数：{item.theorySingleWeight ? this.changeTwoDecimal(item1.completeNumber*item.theorySingleWeight/1000) : "无"}</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>未完成数量：{item.plannedProductionQuantity ? item.plannedProductionQuantity-item1.completeNumber : "无"}</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>未完成吨数：{item.theorySingleWeight ? this.changeTwoDecimal((item.plannedProductionQuantity-item1.completeNumber)*item.theorySingleWeight/1000) : "无"}</Text>
                                                        </View>
                                                        <View style={styles._titleViewStyle}>
                                                            <Text style={styles._titleTextStyle}>更新时间：{item1.gmtModified?item1.gmtModified:item1.gmtCreated}</Text>
                                                        </View>
                                                    </View>
                                                )                           
                                            }
                                            })
                                                
                                        }
                                        {
                                            (item.orderCheckOutAmount && item.orderCheckOutAmount > 0) ? 
                                            <View style={styles._innerViewStyle}>
                                                <View style={[styles._titleViewStyle]}>
                                                    <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:18}]}>装车发运</Text>
                                                </View>
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>已完成数量：{item.orderCheckOutAmount}</Text>
                                                </View>
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>已完成吨数：{item.theorySingleWeight ? this.changeTwoDecimal(item.orderCheckOutAmount * item.theorySingleWeight/1000) : "无"}</Text>
                                                </View>
                                                {/* <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>已完成吨数：{item.orderCheckOutWeight}</Text>
                                                </View> */}
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>发货下欠数量：{item.plannedProductionQuantity ? item.plannedProductionQuantity - item.orderCheckOutAmount : "无"}</Text>
                                                </View>
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>发货下欠吨数：{ item.theorySingleWeight ? this.changeTwoDecimal((item.plannedProductionQuantity - item.orderCheckOutAmount)*item.theorySingleWeight/1000) : "无"}</Text>
                                                </View>
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>更新时间：{item.orderGmtModified}</Text>
                                                </View>
                                            </View>
                                            :
                                            <View/>
                                        }
                                        {
                                            (item.acceptanceAmount && item.acceptanceAmount > 0) ? 
                                            <View style={styles._innerViewStyle}>
                                                <View style={[styles._titleViewStyle]}>
                                                    <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:18}]}>现场工程接收</Text>
                                                </View>
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>现场接收数量：{item.acceptanceAmount}</Text>
                                                </View>
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>现场退货吨数：{item.theorySingleWeight ? this.changeTwoDecimal(item.acceptanceAmount * item.theorySingleWeight/1000) : "无"}</Text>
                                                </View>
                                                {/* <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>现场接收吨数：{item.acceptanceTotalWeight}</Text>
                                                </View> */}
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>更新时间：{item.acceptanceGmtModified}</Text>
                                                </View>
                                            </View>
                                            :
                                            <View/>
                                        }
                                        {
                                            (item.backAmount && item.backAmount > 0) ? 
                                            <View style={styles._innerViewStyle}>
                                                <View style={[styles._titleViewStyle]}>
                                                    <Text style={[styles._titleTextStyle,{fontWeight:'bold', fontSize:18}]}>现场工程退货</Text>
                                                </View>
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>现场退货数量：{item.backAmount}</Text>
                                                </View>
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>现场退货吨数：{item.theorySingleWeight ? this.changeTwoDecimal(item.backAmount * item.theorySingleWeight/1000) : "无"}</Text>
                                                </View>
                                                {/* <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>现场退货吨数：{item.backTotalWeight}</Text>
                                                </View> */}
                                                <View style={styles._titleViewStyle}>
                                                    <Text style={styles._titleTextStyle}>更新时间：{item.backGmtModified}</Text>
                                                </View>
                                            </View>
                                            :
                                            <View/>
                                        }
                                        </View>
                                            
                                    </View>
                                )                           
                        })
                    }
                    </View>
                    
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    _innerViewStyle:{
        marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:0,
    },
    _titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:40,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    _titleTextStyle:{
        fontSize:16
    },

    innerViewStyle:{
        marginTop:10,
        backgroundColor:"#FFFFFF"
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10
    },

    titleTextStyle:{
        fontSize:18
    },
    bodyViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:40,
        marginRight:10,
        marginBottom:8,
        marginTop:8
    },
    bodyRowView:{
        flexDirection:'row', 
        // backgroundColor:'yellow'
    },
    bodyRowLeftView:{
        width:screenWidth/2-40, 
        flexDirection:'row', 
        // backgroundColor:'yellow'
    },
    bodyRowRightView:{
        // backgroundColor:'green', 
        flexDirection:'row', 
        alignItems:'flex-start',
        paddingLeft:10,
        marginRight:5, 
        // alignItems:'flex-start', 
        justifyContent:'flex-start',
        alignContent:'flex-start'
    },
});