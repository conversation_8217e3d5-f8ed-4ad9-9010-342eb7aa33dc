import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,KeyboardAvoidingView,
    FlatList,RefreshControl,Linking,Clipboard,Image,TextInput,Modal,ScrollView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
import BottomScrollSelect from '../../component/BottomScrollSelect';

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class LeadVisitList extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight: 0,
            //弹出框显示状态
            modal:false,
            detailModal:false,
            initGmtCreated: null,
            selectGmtCreated:null,
            //搜索关键词
            searchKeyWord:null,
            customerName:"",
            //拜访信息
            visitOperateId:"",
            visitContactPerson:"",
            visitJobTitle:"",
            visitContactTel:"",
            visitOperateResult:"",
            visitOperateOpinion:"",

            userId:"",
            leadId:"",
            tenantId:"",
            //操作的模式：add-新增；edit-编辑
            operate:"add",

            OperateResultDataSource:[],
            selOperateResultStateCode:"F",
            completionStateDataSource:[],
            selCompletionStateCode:"all",
            errorMsg:"",
            gmtCreated:null,
            detailDTO:{},
            hasOpportunity:false
        }
    }

    //保存函数
    saveVisit=()=>{
        console.log("=======saveVisit");
        let toastOpts;
        if (!this.state.visitContactPerson) {
            this.setState({
                errorMsg:"请输入联系人"
            })
            return;
        }
        
        if (!this.state.visitJobTitle) {
            this.setState({
                errorMsg:"请输入职务"
            })
            return;
        }
        
        if (!this.state.visitContactTel) {
            this.setState({
                errorMsg:"请输入联系电话"
            })
            return;
        }
        
        if (!this.state.selOperateResultStateCode) {
            this.setState({
                errorMsg:"请选择拜访结果"
            })
            return;
        }
        let url= "/biz/lead/operate/add";
        if(this.state.operate === 'edit'){
            url= "/biz/lead/operate/modify";
        }
        let requestParams={
            operateId:this.state.visitOperateId,
            contactPerson:this.state.visitContactPerson,
            jobTitle:this.state.visitJobTitle,
            contactTel:this.state.visitContactTel,
            operateResult:this.state.selOperateResultStateCode,
            operateOpinion:this.state.visitOperateOpinion,
            operateType:"V",
            userId:this.state.userId,
            leadId:this.state.leadId,
        };
        httpPost(url, requestParams, this.saveCallBack);
    }

    saveCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                //关闭弹窗
                this.setState({
                    modal:false,
                    errorMsg:"",
                    hasOpportunity:false
                })
                this.callBackFunction();
 
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }
    //更新State
    _updateState(message, refresh){
        this.setState({text:message,refreshing: refresh});
    }       

    //下拉视图开始刷新时调用
    _onRefresh() {
        if (this.state.refreshing === false) {
            this._updateState('正在刷新......', true);
            //5秒后结束刷新
            setTimeout( ()=>{
                this._updateState('结束状态', false)
            }, 2000)
        }
    }

    callBackFunction=()=>{
        let url= "/biz/lead/operate/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "operateType":"T",
            "operateResult":"F",
            "isVisit":this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
            "gmtCreated":this.state.gmtCreated,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { tenantId } = route.params;
            if (tenantId) {
                console.log("=============tenantId" + tenantId + "");
            }
        }
        let OperateResultDataSource = [
            {
                stateCode:'F',
                stateName:'跟进',
            },
            {
                stateCode:'W',
                stateName:'入库',
            },
            {
                stateCode:'G',
                stateName:'放弃',
            }
        ]
        this.setState({
            OperateResultDataSource:OperateResultDataSource,
        })
        let completionStateDataSource = [
            {
                stateCode:'all',
                stateName:'全部',
            },
            {
                stateCode:'Y',
                stateName:'已拜访',
            },
            {
                stateCode:'N',
                stateName:'未拜访',
            },
            {
                stateCode:'D',
                stateName:'待认领'
            }
        ]
        this.setState({
            completionStateDataSource:completionStateDataSource,
        })
        var _gmtCreated = this.initGmtCreated();
        this.loadLeadVisitList(_gmtCreated);
    }

    initGmtCreated=()=>{
        // 当前时间
        var currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 3);
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        var _gmtCreated = currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay;
        this.setState({
            selectGmtCreated: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            gmtCreated: _gmtCreated,
            initGmtCreated: _gmtCreated,
        })
        return _gmtCreated;
    }

    loadLeadVisitList=(_gmtCreated)=>{
        let url= "/biz/lead/operate/list";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            //查找操作类型为：电邀，操作结果为：跟进 的数据
            "operateType":"T",
            "operateResult":"F",
            "isVisit":this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
            "gmtCreated": _gmtCreated ? _gmtCreated : this.state.gmtCreated,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadLeadVisitListCallBack);
    }

    _loadLeadVisitListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld,...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 下拉触顶刷新到第一页
    _loadFreshData=()=>{
        if ((this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) && this.state.gmtCreated === this.state.initGmtCreated) {
            console.log("==========不刷新=====");
            return;
        }
        var _gmtCreated = this.initGmtCreated();
        this.setState({
            gmtCreated: _gmtCreated,
        })
        this.setState({
            currentPage:1
        })
        let url= "/biz/lead/operate/list";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "operateType":"T",
            "operateResult":"F",
            "isVisit":this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
            "gmtCreated": _gmtCreated,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    _loadFreshDataCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource:dataAll,
                currentPage:response.data.currentPage + 1,
                totalPage:response.data.totalPage,
                totalRecord:response.data.totalRecord,
                refreshing:false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }
    
    flatListFooterComponent=()=>{
        return(
            <CustomListFooterComponent isloading={(this.state.currentPage-1) < this.state.totalPage} />
        )
    }

    // 上拉触底加载下一页
    _loadNextData=()=>{
        if ((this.state.currentPage-1) >= this.state.totalPage) {
            WToast.show({data:"已经是最后一页了，我们也是有底线的"});
            return;
        }
        this.setState({
            refreshing:true
        })
        this.loadLeadVisitList();
    }

    emptyComponent() {
        return <EmptyListComponent/>
    }
    
    searchByKeyWord = () => {
        let loadUrl = "/biz/lead/operate/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "operateType":"T",
            "operateResult":"F",
            "isVisit":this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
            "gmtCreated":this.state.gmtCreated,
            "searchKeyWord":this.state.searchKeyWord
        };
        httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
    }

    renderOperateResultRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    if(this.state.hasOpportunity){
                        return;
                    }
                    this.setState({
                        selOperateResultStateCode:item.stateCode,
                        visitOperateResult:item.stateCode
                    })
                }}>
                    <View 
                        key={item.stateCode} 
                        style={[item.stateCode===this.state.selOperateResultStateCode? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle]}
                        >
                        <Text style={[item.stateCode===this.state.selOperateResultStateCode? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    renderCompletionStateRow=(item, index)=>{
        return (
            <View key={item.stateCode} >
                <TouchableOpacity onPress={()=>{
                    let selCompletionStateCode = item.stateCode;
                    this.setState({
                        selCompletionStateCode:selCompletionStateCode
                    })
                    let loadUrl = "/biz/lead/operate/list";
                    let loadRequest = {
                        "currentPage": 1,
                        "pageSize": this.state.pageSize,
                        "operateType":"T",
                        "operateResult":"F",
                        "isVisit":selCompletionStateCode === 'all' ? null : selCompletionStateCode,
                        "searchKeyWord":this.state.searchKeyWord,
                        "gmtCreated":this.state.gmtCreated
                    };
                    httpPost(loadUrl, loadRequest, this._loadFreshDataCallBack);
                }}>
                    <View 
                        key={item.stateCode} 
                        style={[item.stateCode===this.state.selCompletionStateCode? [CommonStyle.selectedBlockItemViewStyle,{borderBottomWidth:2,borderBottomColor:"#CB4139"}] : [CommonStyle.blockItemViewStyle,{}],{paddingLeft:8,paddingRight:8}]}
                        >
                        <Text style={[item.stateCode===this.state.selCompletionStateCode? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16, { fontWeight: 'bold' }]}>
                            {item.stateName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View>

            </View>
        )
    }
    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    //列表模块渲染函数
    renderRow=(item, index)=>{
        return (
            <View key={item.leadId} style={styles.innerViewStyle}>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>客户名称：{item.customerName}</Text>
                    {
                        item.visitOperateResult?
                        <View>
                        {
                            item.visitOperateResult==="放弃" ?
                            <Text style={{color:"#CB4139"}}>{item.visitOperateResult}</Text>
                            :
                            <Text style={{color:"#F2C16D"}}>{item.visitOperateResult?item.visitOperateResult:"未拜访"}</Text>
                        }
                        </View>
                        :
                        <Text style={{color:"#bfbfbf"}}>未拜访</Text>
                    }
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系人：{item.contactPerson}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>职务：{item.jobTitle}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>联系电话：{item.contactTel}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>电邀意见：{item.operateOpinion?item.operateOpinion:"无"}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>电邀提交人：{item.userName}</Text>
                </View>
                {
                    item.claimUserName
                    ?
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>拜访认领人：{item.claimUserName}</Text>
                    </View>
                    :<View/>
                }
                
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>{item.claimTime?"拜访认领时间":"电邀提交时间"}：{item.claimTime?item.claimTime:item.gmtCreated}</Text>
                </View>
                <View style={styles.titleViewStyle}>
                    <Text style={styles.titleTextStyle}>拜访结果：{item.visitOperateResult?item.visitOperateResult:"未拜访"}</Text>
                </View>
                {
                    item.visitOperateResult?
                    <View style={styles.titleViewStyle}>
                        <Text style={styles.titleTextStyle}>拜访意见：{item.visitOperateOpinion?item.visitOperateOpinion:"无"}</Text>
                    </View>
                    :
                    <View></View>
                }
                <View style={[CommonStyle.itemBottomBtnStyle,{flexWrap:'wrap'}]}>
                {
                    item.userId != constants.loginUser.userId && (item.visitOperateResult=='入库' || item.visitOperateResult=='放弃')? 
                    <TouchableOpacity onPress={()=>{
                        let loadUrl = "/biz/lead/operate/visitClaim";
                        let loadRequest = {
                            "operateId": item.operateId,
                            'claimUserId':constants.loginUser.userId,
                            'leadId':item.leadId
                        };
                        httpPost(loadUrl, loadRequest, (response)=>{
                            if (response.code == 200 && response.data) {
                                WToast.show({ data: '认领成功' });
                                this.callBackFunction()
                            }
                            else{
                                WToast.show({ data: response.message });
                                this.props.navigation.navigate("LoginView");
                            }
                    
                        });
                    }}>
                        <View style={[CommonStyle.itemBottomDetailBtnViewStyle, {backgroundColor:"#3ab240",width: 75 ,flexDirection:"row"}]}>
                        <Image  style={{width:25, height:25,marginRight:3}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>认领</Text>
                        </View>
                    </TouchableOpacity>
                    :<View/>
                }
                    {
                        (item.userId == constants.loginUser.userId && !item.claimUserId) || item.claimUserId == constants.loginUser.userId
                        ?
                        <TouchableOpacity onPress={()=>{
                                let loadUrl = "/biz/lead/operate/getList";
                                let loadRequest = {
                                    "currentPage": 1,
                                    "pageSize": this.state.pageSize,
                                    "leadId":item.leadId,
                                    "operateType":"V",
                                };
                                httpPost(
                                    loadUrl, 
                                    loadRequest, 
                                    (response)=>{
                                    if (response.code == 200 && response.data && response.data.dataList) {
                                        console.log(response.data.dataList)
                                        if(response.data.dataList && response.data.dataList.length > 0){
                                            if(item.visitOperateResult == '放弃'){
                                                this.setState({ 
                                                    hasOpportunity:true,
                                                    errorMsg:"已放弃，不可编辑"
                                                })
                                            }
                                            if(item.visitOperateResult == '入库'){
                                                this.setState({ 
                                                    hasOpportunity:true,
                                                    errorMsg:"已入库，不可编辑"
                                                })
                                            }
                                            //将item赋入state
                                            var leadOperateDto = response.data.dataList[0]
                                            this.setState({ 
                                                visitOperateId:leadOperateDto.operateId,
                                                selOperateResultStateCode:leadOperateDto.operateResult,
                                                customerName:leadOperateDto.customerName,
                                                visitContactPerson:leadOperateDto.contactPerson,
                                                visitJobTitle:leadOperateDto.jobTitle,
                                                visitContactTel:leadOperateDto.contactTel,
                                                visitOperateOpinion:leadOperateDto.operateOpinion,
                                                visitOperateResult:leadOperateDto.operateResult,
                                                userId:leadOperateDto.userId,
                                                leadId:leadOperateDto.leadId,
                                                operate:"edit",
                                            })
                                            loadUrl = "/biz/sale/opportunity/list";
                                            loadRequest = {
                                                "currentPage": 1,
                                                "pageSize": 10,
                                                "leadId":item.leadId,
                                            };
                                            httpPost(loadUrl, loadRequest, (response)=>{
                                                if (response.code == 200 && response.data && response.data.dataList) {
                                                    console.log(response.data.dataList)
                                                    if(response.data.dataList && response.data.dataList.length > 0){
                                                        this.setState({ 
                                                            hasOpportunity:true,
                                                            errorMsg:"已有商机，不可编辑"
                                                        })
                                                    }
                                                }
                                            })
                                        }
                                        else{
                                            this.setState({ 
                                                visitOperateId:"",
                                                selOperateResultStateCode:"F",
                                                // visitContactPerson:"",
                                                // visitJobTitle:"",
                                                // visitContactTel:"",
                                                visitOperateOpinion:"",
                                                visitOperateResult:"",
                                                userId:constants.loginUser.userId,
                                                leadId:item.leadId,
                                                operate:"add",
                                                errorMsg:"",
                                            })
                                            loadUrl = "/biz/lead/operate/getList";
                                            loadRequest = {
                                                "currentPage": 1,
                                                "pageSize": 10,
                                                "leadId":item.leadId,
                                                "operateType":"T",
                                            };
                                            httpPost(loadUrl, loadRequest, (response)=>{
                                                if (response.code == 200 && response.data && response.data.dataList) {
                                                    console.log(response.data.dataList)
                                                    if(response.data.dataList && response.data.dataList.length > 0){
                                                        var leadOperateDto = response.data.dataList[0]
                                                        this.setState({ 
                                                            visitJobTitle:leadOperateDto.jobTitle,
                                                            visitContactPerson:leadOperateDto.contactPerson,
                                                            visitContactTel:leadOperateDto.contactTel,
                                                        })
                                                    }
                                                }
                                            })
                                        }
                                        this.setState({
                                            modal:true
                                        })
                                    }
                                    else if (response.code == 401) {
                                        WToast.show({data:response.message});
                                        this.props.navigation.navigate("LoginView");
                                    }
                                    }
                                );
                            
                            }}>
                            <View style={[CommonStyle.itemBottomEditBtnViewStyle,{backgroundColor:"#ff8c1a",width:75,flexDirection:"row"}
                                ,item.auditScore ? CommonStyle.disableViewStyle : ""]}>
                                    <Image  style={{width:25, height:25,marginRight:3}} source={require('../../assets/icon/iconfont/handshake.png')}></Image>
                                <Text style={CommonStyle.itemBottomEditBtnTextStyle}>结果</Text>
                            </View>
                        </TouchableOpacity>

                        :
                        <View/>
                    }
                    <TouchableOpacity onPress={()=>{
                        let loadUrl = "/biz/customer/lead/detail";
                        let loadRequest = {
                            "leadId": item.leadId,
                        };
                        httpPost(loadUrl, loadRequest, (response)=>{
                            if (response.code == 200 && response.data) {
                                this.setState({
                                    detailDTO: response.data,
                                    detailModal:true

                                })
                            }
                    
                        });
                        
                    }}>
                            <View style={[CommonStyle.itemBottomDetailBtnViewStyle, { backgroundColor:"#3ab240",width: 75 ,flexDirection:"row"}]}>
                            <Image  style={{width:25, height:25,marginRight:3}} source={require('../../assets/icon/iconfont/detail1.png')}></Image>
                                <Text style={CommonStyle.itemBottomDetailBtnTextStyle}>详情</Text>
                            </View>
                        </TouchableOpacity>
                </View>
            </View>
        )
    }

    openGmtCreated(){
        this.refs.SelectGmtCreated.showDate(this.state.selectGmtCreated)
    }

    callBackSelectGmtCreatedValue(value){
        console.log("==========时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectGmtCreated:value
        })
        if (this.state.selectGmtCreated && this.state.selectGmtCreated.length) {
            var _gmtCreated = "";
            var vartime;
            for(var index=0;index<this.state.selectGmtCreated.length;index++) {
                vartime = this.state.selectGmtCreated[index];
                if (index===0) {
                    _gmtCreated += vartime;
                }
                else if (index < 3){
                    _gmtCreated += "-" + vartime;
                }
                else if (index===3){
                    _gmtCreated += " " + vartime;
                }
                else {
                    _gmtCreated += ":" + vartime;
                }
            }
            this.setState({
                currentPage: 1,
                gmtCreated:_gmtCreated
            })
            let url= "/biz/lead/operate/list";
            let loadRequest={
                "currentPage": 1,
                "pageSize": this.state.pageSize,
                "operateType":"T",
                "operateResult":"F",
                "isVisit":this.state.selCompletionStateCode === 'all' ? null : this.state.selCompletionStateCode,
                "gmtCreated":_gmtCreated,
                "searchKeyWord":this.state.searchKeyWord            };
            httpPost(url, loadRequest, this._loadFreshDataCallBack);
        }
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title='拜访管理'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[CommonStyle.rightTop50FloatingBlockView,this.state.gmtCreated 
                    ? {borderRadius:3, width:null,height: 40, marginTop: 50,paddingLeft:15, paddingRight:15, opacity:0.5} : {}]}>
                    <TouchableOpacity onPress={()=>this.openGmtCreated()}>
                        <Text style={CommonStyle.rightTop50FloatingBlockText}>
                        {!this.state.gmtCreated ? "时间" : this.state.gmtCreated}
                        </Text>
                    </TouchableOpacity>
                </View>
                <View style={[styles.innerViewStyle,{marginTop:0}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={{ marginTop: 0, index: 1000, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.completionStateDataSource && this.state.completionStateDataSource.length > 0)
                                ?
                                this.state.completionStateDataSource.map((item, index) => {
                                    return this.renderCompletionStateRow(item)
                                })
                                : <View />
                        }
                    </View>
    
                    <View style={{}}>
                        <View style={styles.inputOutsideText}>
                            <View style={styles.inputInsideText}>
                            <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'客户名称/电邀提交人'}
                                onChangeText={(text) => this.setState({ searchKeyWord: text })}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                        </View>
                    </View>

                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList 
                        data={this.state.dataSource}
                        renderItem={({item,index}) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                            tintColor="#FF0000"
                            title="loading"
                            colors={['#FF0000', '#00FF00', '#0000FF']}
                            progressBackgroundColor="#FFFF00"
                            refreshing={this.state.refreshing}
                            onRefresh={()=>{
                                this._loadFreshData()
                            }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={()=>this.flatListFooterComponent()}
                        onEndReached={()=>this._loadNextData}
                    />
                </View>
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.modal}
                >
                    <KeyboardAvoidingView style={CommonStyle.fullScreenKeepOut} behavior="padding">
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle,{height:40, backgroundColor:'#ff8c1a',borderRadius:5, justifyContent:'center',alignItems:'center',marginTop:10}]}>
                                <Text style={[styles.titleTextStyle,{fontSize:18,fontWeight:'bold',color:"#ffffff"}]}>拜访结果</Text>
                            </View>
                            <ScrollView style={[CommonStyle.formContentViewStyle]}>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    </View>                                
                                    <TextInput
                                        editable={!this.state.hasOpportunity}
                                        style={styles.inputRightText}
                                        placeholder={'请输入联系人'}
                                        onChangeText={(text) => this.setState({visitContactPerson:text})}
                                    >
                                        {this.state.visitContactPerson}
                                    </TextInput>
                                </View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>职务</Text>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    </View>                                
                                    <TextInput 
                                        editable={!this.state.hasOpportunity}
                                        style={styles.inputRightText}
                                        placeholder={'请输入职务'}
                                        onChangeText={(text) => this.setState({visitJobTitle:text})}
                                    >
                                        {this.state.visitJobTitle}
                                    </TextInput>
                                </View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    </View>                                
                                    <TextInput 
                                        editable={!this.state.hasOpportunity}
                                        style={styles.inputRightText}
                                        placeholder={'请输联系电话'}
                                        onChangeText={(text) => this.setState({visitContactTel:text})}
                                    >
                                        {this.state.visitContactTel}
                                    </TextInput>
                                </View>   
                               
                                <View>
                                    {
                                        (this.state.OperateResultDataSource && this.state.OperateResultDataSource.length > 0)
                                            ?
                                            <View>
                                                <View style={styles.inputRowStyle}>
                                                    <View style={styles.leftLabView}>
                                                        <Text style={styles.leftLabNameTextStyle}>拜访结果</Text>
                                                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                                                    </View>
                                                </View>
                                                <View style={{ width: screenWidth-40, flexWrap: 'wrap', flexDirection: 'row' }}>
                                                    {
                                                        (this.state.OperateResultDataSource && this.state.OperateResultDataSource.length > 0)
                                                            ?
                                                            this.state.OperateResultDataSource.map((item, index) => {
                                                                return this.renderOperateResultRow(item)
                                                            })
                                                            : <EmptyRowViewComponent />
                                                    }
                                                </View>
                                            </View>
                                            : <View />
                                    }
                                </View>
                                <View style={styles.inputRowStyle}>
                                    <View style={styles.leftLabView}>
                                        <Text style={styles.leftLabNameTextStyle}>拜访意见</Text>
                                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                    </View>                                
                                    {/* <TextInput 
                                        style={styles.inputRightText}
                                        placeholder={'请输入拜访意见'}
                                        onChangeText={(text) => this.setState({visitOperateOpinion:text})}
                                    >
                                        {this.state.visitOperateOpinion}
                                    </TextInput> */}
                                </View>
                                <View style={[styles.inputRowStyle,{height:105}]}>
                                    <TextInput 
                                        editable={!this.state.hasOpportunity}
                                        multiline={true}
                                        textAlignVertical="top"
                                        style={[CommonStyle.inputRowText,{height:100,width:screenWidth - 70}]}
                                        placeholder={'请输入拜访意见'}
                                        onChangeText={(text) => this.setState({visitOperateOpinion:text})}
                                    >
                                        {this.state.visitOperateOpinion}
                                    </TextInput>
                                </View>
                            </ScrollView>
                            <View style={CommonStyle.alignCenterStyle}>
                                <Text style={[CommonStyle.rowLabRedTextStyle, CommonStyle.boldTextStyle]}>{this.state.errorMsg}</Text>
                            </View>
                            <View style={[CommonStyle.btnRowStyle,{justifyContent:'center'}]}>
                                <TouchableOpacity onPress={() => { 
                                    this.setState({
                                        modal:false,
                                        errorMsg:"",
                                        hasOpportunity:false
                                    }) 
                                }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView,{width:screenWidth/2 - 100, marginRight:20}]} >
                                        <Image  style={{width:22, height:22,marginRight:10}} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={[CommonStyle.btnRowLeftCancelBtnText,{fontWeight:'bold'}]}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={
                                    this.saveVisit.bind(this)
                                    }>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView,{width:screenWidth/2 - 100, marginLeft:20}]}>
                                        <Image  style={{width:25, height:25,marginRight:10}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                        <Text style={[CommonStyle.btnRowRightSaveBtnText,{fontWeight:'bold'}]}>保存</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>      
                    </KeyboardAvoidingView>
                </Modal>
                <Modal
                    animationType={'slide'}
                    transparent={true}
                    onRequestClose={() => console.log('onRequestClose...')}
                    visible={this.state.detailModal}
                >
                    <View style={CommonStyle.fullScreenKeepOut}>
                        <View style={[CommonStyle.modalContentViewStyle]}>
                            <View style={[styles.titleViewStyle,{height:40,backgroundColor:"#3ab240",borderRadius:5, justifyContent:'center',alignItems:'center',marginTop:10}]}>
                                <Text style={[styles.titleTextStyle,{fontSize:18,fontWeight:'bold',color:"#ffffff"}]}>线索详情</Text>
                            </View>
                            <ScrollView>
                                {
                                    this.state.detailDTO.customerLeadDTOList && this.state.detailDTO.customerLeadDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>线索</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>客户名称：{this.state.detailDTO.customerName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.customerLeadDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职务：{this.state.detailDTO.customerLeadDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.customerLeadDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>客户简介：{this.state.detailDTO.customerLeadDTOList[0].customerSummary?this.state.detailDTO.customerLeadDTOList[0].customerSummary:"无"}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>备注说明：{this.state.detailDTO.customerLeadDTOList[0].remark?this.state.detailDTO.customerLeadDTOList[0].remark:"无"}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.customerLeadDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.customerLeadDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                {
                                    this.state.detailDTO.auditLeadOperateDTOList && this.state.detailDTO.auditLeadOperateDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>审核</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.auditLeadOperateDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职务：{this.state.detailDTO.auditLeadOperateDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.auditLeadOperateDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>审核结果：{this.state.detailDTO.auditLeadOperateDTOList[0].operateResultName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>审核意见：{this.state.detailDTO.auditLeadOperateDTOList[0].operateOpinion}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.auditLeadOperateDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.auditLeadOperateDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                {
                                    this.state.detailDTO.telLeadOperateDTOList && this.state.detailDTO.telLeadOperateDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>电邀</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.telLeadOperateDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职务：{this.state.detailDTO.telLeadOperateDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.telLeadOperateDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>电邀结果：{this.state.detailDTO.telLeadOperateDTOList[0].operateResultName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>电邀意见：{this.state.detailDTO.telLeadOperateDTOList[0].operateOpinion}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.telLeadOperateDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.telLeadOperateDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                {
                                    this.state.detailDTO.visitLeadOperateDTOList && this.state.detailDTO.visitLeadOperateDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>拜访</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系人：{this.state.detailDTO.visitLeadOperateDTOList[0].contactPerson}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>职务：{this.state.detailDTO.visitLeadOperateDTOList[0].jobTitle}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>联系电话：{this.state.detailDTO.visitLeadOperateDTOList[0].contactTel}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>拜访结果：{this.state.detailDTO.visitLeadOperateDTOList[0].operateResultName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>拜访意见：{this.state.detailDTO.visitLeadOperateDTOList[0].operateOpinion}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.visitLeadOperateDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.visitLeadOperateDTOList[0].gmtCreated}</Text>
                                        </View>
                                        <View style={{height:20}}>
                                        </View>
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }
                                
                                {
                                    this.state.detailDTO.slSaleOpportunityDTOList && this.state.detailDTO.slSaleOpportunityDTOList.length > 0
                                    ?
                                    <View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16,fontWeight:'bold'}]}>商机</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>商机名称：{this.state.detailDTO.slSaleOpportunityDTOList[0].opportunityName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交人：{this.state.detailDTO.slSaleOpportunityDTOList[0].userName}</Text>
                                        </View>
                                        <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                            <Text style={[styles.itemContentChildTextStyle,{fontSize:16}]}>提交时间：{this.state.detailDTO.slSaleOpportunityDTOList[0].gmtCreated}</Text>
                                        </View>
                                        {
                                            this.state.detailDTO.slSaleOpportunityDTOList[0].slSaleOpportunityTrackDTOList && this.state.detailDTO.slSaleOpportunityDTOList[0].slSaleOpportunityTrackDTOList.length > 0
                                            ?
                                            <View>
                                                {
                                                    this.state.detailDTO.slSaleOpportunityDTOList[0].slSaleOpportunityTrackDTOList.map((trackItem,index)=>{
                                                        return(
                                                            <View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0,fontWeight:'bold'}]}>进展{index + 1}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>联系人：{trackItem.contactPerson}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>职务：{trackItem.contactPerson}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>联系电话：{trackItem.contactTel}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>进展说明：{trackItem.trackExplain}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>提交人：{trackItem.userName}</Text>
                                                                </View>
                                                                <View style={[styles.itemContentLeftChildViewStyle,{}]}>
                                                                    <Text style={[styles.itemContentChildTextStyle,{fontSize:16,paddingLeft:0}]}>提交时间：{trackItem.gmtCreated}</Text>
                                                                </View>
                                                            </View>
                                                        )
                                                    })
                                                }
                                                
                                            </View>
                                            :
                                            <View>
                                            </View>
                                        }
                                        
                                    </View>
                                    :
                                    <View>
                                    </View>
                                }


                            </ScrollView>
                            <View>
                                <TouchableOpacity onPress={() => {
                                    
                                    this.setState({
                                        detailModal:false
                                    })
                                }}>
                                <View style={[styles.btnRowLeftCancelBtnView]} >
                                {/* <Image  style={{width:20, height:20,marginRight:10}} source={require('../../assets/icon/iconfont/revoke-grey.png')}></Image> */}
                                    <Text  style={[styles.titleTextStyle,{ fontWeight: 'bold',fontSize:18,color:'#a1a1a1'}]}>返       回</Text>
                                </View>
                                </TouchableOpacity>  
                            </View>
                        </View>
                    </View>
                </Modal>
                <BottomScrollSelect 
                    ref={'SelectGmtCreated'} 
                    callBackDateValue={this.callBackSelectGmtCreatedValue.bind(this)}
                />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8
    },
    btnRowLeftCancelBtnView:{
        flexDirection:'row',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
        alignItems:'center',
        justifyContent:'center',
        borderWidth:1,
        borderColor:'#a1a1a1',
        borderRadius:5,
        height:40,        
    },
    fullScreenKeepOut:{
        height:screenHeight, 
        width:screenWidth,
        backgroundColor:'rgba(169,169,169,0.95)',
        alignItems:'center',
        justifyContent:'center',
    },
    
    searchInputText: {
        width: screenWidth -100,
        borderColor: '#000000',
        // borderBottomWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 16,
        marginLeft: 10,
        marginTop:5,
        paddingLeft: 10,
        paddingRight: 10,
        paddingBottom: 0,
        paddingTop:0
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentLeftChildViewStyle:{
        flexDirection:'column',
        // alignContent:'flex-start',
        // justifyContent:'flex-start',
        // alignItems:'flex-start',
        // width:screenWidth - 180,
        marginLeft:20
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 65),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    },
    inputOutsideText:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5,
        marginTop:5
    },
    inputInsideText:{
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
    }
});