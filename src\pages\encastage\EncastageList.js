import React, {Component} from 'react';
import {
  Alert,
  Clipboard,
  Dimensions,
  FlatList,
  Image,
  Linking,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import BottomScrollSelect from '../../component/BottomScrollSelect';

// import TopScreen from '../../component/TopScreen';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import EmptyListComponent from '../../component/EmptyListComponent';

// 引入公共样式
var CommonStyle = require('../../assets/css/CommonStyle');

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
class EncastageList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      initGmtCreated: null,
      gmtCreated: null,
      selectGmtCreated: null,
      dataSource: [],
      text: '初始状态',
      refreshing: false,
      pageSize: 15,
      currentPage: 1,
      totalPage: 1,
      totalRecord: 1,
    };
  }

  //下拉视图开始刷新时调用
  _onRefresh() {
    if (this.state.refreshing === false) {
      this._updateState('正在刷新......', true);
      //5秒后结束刷新
      setTimeout(() => {
        this._updateState('结束状态', false);
      }, 2000);
    }
  }

  //更新State
  _updateState(message, refresh) {
    this.setState({text: message, refreshing: refresh});
  }

  initGmtCreated = () => {
    // 当前时间
    var currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth());
    var currentDateMonth = ('0' + (currentDate.getMonth() + 1)).slice(-2);
    var currentDateDay = ('0' + currentDate.getDate()).slice(-2);
    var _gmtCreated =
      currentDate.getFullYear() + '-' + currentDateMonth + '-' + currentDateDay;
    this.setState({
      selectGmtCreated: [
        currentDate.getFullYear(),
        currentDateMonth,
        currentDateDay,
      ],
      gmtCreated: _gmtCreated,
      initGmtCreated: _gmtCreated,
    });
    return _gmtCreated;
  };

  UNSAFE_componentWillMount() {
    console.log('componentWillMount');
    var _gmtCreated = this.initGmtCreated();
    this.loadEncastageList(_gmtCreated);
  }

  // 回调函数
  callBackFunction = () => {
    let url = '/biz/encastage/record/list';
    let loadRequest = {
      encastageTime: this.state.gmtCreated,
      currentPage: 1,
      pageSize: this.state.pageSize,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  // 下拉触顶刷新到第一页
  _loadFreshData = () => {
    if (
      (this.state.currentPage == 1 ||
        this.state.totalRecord <= this.state.pageSize) &&
      this.state.gmtCreated == this.state.initGmtCreated
    ) {
      console.log('==========不刷新=====');
      return;
    }
    var _gmtCreated = this.initGmtCreated();
    this.setState({
      gmtCreated: _gmtCreated,
      currentPage: 1,
    });
    let url = '/biz/encastage/record/list';
    let loadRequest = {
      currentPage: 1,
      pageSize: this.state.pageSize,
      encastageTime: _gmtCreated,
    };
    httpPost(url, loadRequest, this._loadFreshDataCallBack);
  };

  _loadFreshDataCallBack = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataNew];
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  flatListFooterComponent = () => {
    return (
      <CustomListFooterComponent
        isloading={this.state.currentPage - 1 < this.state.totalPage}
      />
    );
  };
  // 上拉触底加载下一页
  _loadNextData = () => {
    if (this.state.currentPage - 1 >= this.state.totalPage) {
      WToast.show({data: '已经是最后一页了，我们也是有底线的'});
      return;
    }
    this.setState({
      refreshing: true,
    });
    if (
      this.state.currentPage == 1 ||
      this.state.totalRecord <= this.state.pageSize
    ) {
      console.log('==========第一页即是最后一页，不加载=====');
      return;
    }
    this.loadEncastageList();
  };

  loadEncastageList = (_gmtCreated) => {
    let url = '/biz/encastage/record/list';
    let loadRequest = {
      currentPage: this.state.currentPage,
      pageSize: this.state.pageSize,
      encastageTime: _gmtCreated ? _gmtCreated : this.state.gmtCreated,
    };
    httpPost(url, loadRequest, this.callBackLoadEncastageList);
  };

  callBackLoadEncastageList = (response) => {
    if (response.code == 200 && response.data && response.data.dataList) {
      var dataNew = response.data.dataList;
      var dataOld = this.state.dataSource;
      // dataOld.unshift(dataNew);
      var dataAll = [...dataOld, ...dataNew];
      if (dataAll.length > response.data.totalRecord) {
        this.setState({
          refreshing: false,
        });
        console.log(
          '=====数据错误了========' +
            dataAll.length +
            '/' +
            response.data.totalRecord,
        );
        return;
      }
      this.setState({
        dataSource: dataAll,
        currentPage: response.data.currentPage + 1,
        totalPage: response.data.totalPage,
        totalRecord: response.data.totalRecord,
        refreshing: false,
      });
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    }
  };

  deleteEncastage = (encastageId) => {
    console.log('=======delete=encastageId', encastageId);
    let url = '/biz/encastage/record/delete';
    let requestParams = {encastageId: encastageId};
    httpDelete(url, requestParams, this.deleteCallBack);
  };

  // 删除操作的回调操作
  deleteCallBack = (response) => {
    if (response.code == 200 && response.data) {
      WToast.show({data: '删除完成'});
      this.callBackFunction();
    } else if (response.code == 401) {
      WToast.show({data: response.message});
      this.props.navigation.navigate('LoginView');
    } else {
      WToast.show({data: response.message});
    }
  };

  // 头部左侧
  renderLeftItem() {
    return (
      // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
      //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
      //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
      //     <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
      // </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.goBack();
          }}
          style={[{flexDirection: 'row', alignItems: 'center'}]}>
          <Image
            style={{
              width: 22,
              height: 22,
              marginVertical: 2,
              tintColor: '#3C6CDE',
            }}
            source={require('../../assets/icon/iconfont/back.png')}></Image>
          <Text style={{color: '#3C6CDE', fontWeight: 'bold'}}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // 头部右侧
  renderRightItem() {
    return (
      // <TouchableOpacity onPress={() => {
      //     this.props.navigation.navigate("EncastageAdd", {
      //         // 传递回调函数
      //         refresh: this.callBackFunction
      //     })
      // }}>
      //     <Image style={{ width:27, height:27 }} source={require('../../assets/icon/iconfont/add.png')}></Image>
      // </TouchableOpacity>
      <View
        style={{flexDirection: 'row-reverse', alignItems: 'center', width: 70}}>
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('EncastageAdd', {
              // 传递回调函数
              refresh: this.callBackFunction,
            });
          }}>
          <Image
            style={{width: 22, height: 22, marginVertical: 2}}
            source={require('../../assets/icon/iconfont/add.png')}></Image>
        </TouchableOpacity>
      </View>
    );
  }

  renderRowNew = (item, index) => {
    return (
      <View style={styles.innerViewStyle}>
        {index === 0 ? (
          <View style={CommonStyle.lineListHeadRenderRowStyle}></View>
        ) : (
          <View></View>
        )}
        <View style={CommonStyle.titleViewStyleSpecial}>
          <Text style={CommonStyle.titleTextStyleSpecial}>
            {item.encastageTime ? item.encastageTime : '无'}
          </Text>
          {/* <Text style={CommonStyle.titleTextStyleSpecial}>装窑日期：{item.encastageTime ? item.encastageTime : "无"}</Text> */}
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            窑车：{item.kilnCarName}
          </Text>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            窑道名称：{item.kilnRoadName}
          </Text>
        </View>
        <View style={[CommonStyle.titleViewStyle, {alignItems: 'center'}]}>
          <View style={{flexDirection: 'column'}}>
            {item.spEncastageDetailDTOList.map((elem, index) => {
              return (
                <View>
                  <View
                    style={{
                      flexDirection: 'row',
                      width: screenWidth - 134 + 90,
                      marginBottom: 5,
                    }}>
                    <View style={{width: screenWidth - 230 + 80}}>
                      <Text style={CommonStyle.titleTextStyle}>
                        砖型：{elem.orderName}
                      </Text>
                    </View>
                    <View
                      style={{
                        width: 90 + 10,
                        marginLeft: 5,
                        justifyContent: 'center',
                        alignItems: 'flex-end',
                        marginTop: 25,
                      }}>
                      <Text style={CommonStyle.titleTextStyle}>
                        数量：{elem.brickAmount}
                      </Text>
                    </View>
                  </View>
                  <View>
                    <View
                      style={{
                        flexDirection: 'row',
                        width: screenWidth - 134 + 90,
                        marginBottom: 5,
                      }}>
                      <View style={{width: screenWidth - 230 + 50}}>
                        {/* <Text style={CommonStyle.bodyTextStyle}>砖型：{elem.orderName}</Text> */}
                      </View>
                      <View
                        style={{
                          width: 90 + 40,
                          marginLeft: 5,
                          justifyContent: 'center',
                          alignItems: 'flex-end',
                        }}>
                        <Text style={CommonStyle.titleTextStyle}>
                          合同数量：{elem.orderBrickAmount}
                        </Text>
                      </View>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        width: screenWidth - 134 + 90,
                        justifyContent: 'center',
                        alignItems: 'flex-end',
                        marginBottom: 5,
                      }}>
                      <View style={{width: screenWidth - 230}}>
                        {/* <Text style={CommonStyle.bodyTextStyle}>砖型：{elem.orderName}</Text> */}
                      </View>
                      <View
                        style={{
                          width: 90 + 90,
                          marginLeft: 5,
                          justifyContent: 'center',
                          alignItems: 'flex-end',
                        }}>
                        <Text style={CommonStyle.titleTextStyle}>
                          累计装窑数量：{elem.accumulateBrickAmount}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              );
            })}
          </View>
        </View>
        <View style={CommonStyle.titleViewStyle}>
          <Text style={CommonStyle.titleTextStyle}>
            合计重量：{item.totalWeight}吨
          </Text>
        </View>

        <View style={[CommonStyle.itemBottomBtnStyle, {marginRight: 28}]}>
          <TouchableOpacity
            onPress={() => {
              if (
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
              ) {
                return;
              }
              Alert.alert('确认', '您确定要删除该条装窑记录吗？', [
                {
                  text: '取消',
                  onPress: () => {
                    WToast.show({data: '点击了取消'});
                    // this在这里可用，传到方法里还有问题
                    // this.props.navigation.goBack();
                  },
                },
                {
                  text: '确定',
                  onPress: () => {
                    WToast.show({data: '点击了确定'});
                    this.deleteEncastage(item.encastageId);
                  },
                },
              ]);
            }}>
            <View
              style={[
                CommonStyle.btnTwoDeleteBtnView,
                dateDiffHours(constants.nowDateTime, item.gmtCreated) >
                constants.editDeleteTimeLimit
                  ? CommonStyle.disableViewStyle
                  : '',
              ]}>
              <Image
                style={CommonStyle.btnTwoDeleteBtnImage}
                source={require('../../assets/icon/iconfont/delete.png')}></Image>
              <Text style={CommonStyle.btnTwoDeleteBtnText}>删除</Text>
            </View>
          </TouchableOpacity>

          {/*  
                    窑车状态变化不能编辑                   
                    <TouchableOpacity onPress={()=>{
                        if (item.canEdit != "Y") {
                            return
                        }
                        if (dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit) {
                            return;
                        }
                            this.props.navigation.navigate("EncastageAdd", {
                                encastageId:item.encastageId,
                                // 传递回调函数
                                refresh: this.callBackFunction 
                            })
                        }}>
                        <View style={[styles.itemBottomEditBtnViewStyle, item.canEdit != "Y" ? CommonStyle.disableViewStyle : ''
                        ,dateDiffHours(constants.nowDateTime, item.gmtCreated) > constants.editDeleteTimeLimit ? CommonStyle.disableViewStyle : ""
                        ]}>
                            <Text style={styles.itemBottomEditBtnTextStyle}>编辑</Text>
                        </View>
                    </TouchableOpacity> */}
        </View>
      </View>
    );
  };

  // 分隔线
  space() {
    return (
      <View
        style={{height: 1, backgroundColor: '#F0F0F0', marginHorizontal: 16}}
      />
    );
  }
  emptyComponent() {
    return <EmptyListComponent />;
  }

  openGmtCreated() {
    this.refs.SelectGmtCreated.showDate(this.state.selectGmtCreated);
  }
  callBackSelectGmtCreatedValue(value) {
    console.log('==========时间选择结果：', value);
    if (!value) {
      return;
    }
    this.setState({
      selectGmtCreated: value,
    });
    if (this.state.selectGmtCreated && this.state.selectGmtCreated.length) {
      var _gmtCreated = '';
      var vartime;
      for (var index = 0; index < this.state.selectGmtCreated.length; index++) {
        vartime = this.state.selectGmtCreated[index];
        if (index === 0) {
          _gmtCreated += vartime;
        } else if (index < 3) {
          _gmtCreated += '-' + vartime;
        } else if (index === 3) {
          _gmtCreated += ' ' + vartime;
        } else {
          _gmtCreated += ':' + vartime;
        }
      }
      this.setState({
        currentPage: 1,
        gmtCreated: _gmtCreated,
      });

      let url = '/biz/encastage/record/list';
      let loadRequest = {
        currentPage: 1,
        pageSize: this.state.pageSize,
        encastageTime: _gmtCreated,
      };
      httpPost(url, loadRequest, this._loadFreshDataCallBack);

      // this.loadEncastageList(_gmtCreated);
    }
  }
  exportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/encastage';
    let requestParams = {
      gmtCreated: this.state.gmtCreated,
      currentPage: 1,
      pageSize: 1000,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的PDF访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      }
    });
  };

  exportReportPdfFile = () => {
    console.log('=======exportPdfFile');
    let url = '/biz/generate/pdf/encastage_report';
    let requestParams = {
      encastageTime: this.state.gmtCreated,
      currentPage: 1,
      pageSize: 1000,
    };
    httpPost(url, requestParams, (response) => {
      if (response.code == 200 && response.data) {
        Clipboard.setString(response.data);
        WToast.show({
          data:
            '导出的报表文件访问路径:已经复制到粘贴板,您可以在浏览器中直接粘贴访问\n' +
            response.data,
        });
        Alert.alert(
          '确认',
          '导出地址已复制到粘贴板，使用浏览器打开:\n' + response.data + ' ?',
          [
            {
              text: '不打开',
              onPress: () => {
                WToast.show({data: '点击了不打开'});
              },
            },
            {
              text: '打开',
              onPress: () => {
                WToast.show({data: '点击了打开'});
                // 直接打开外网链接
                Linking.openURL(response.data);
              },
            },
          ],
        );
      } else {
        WToast.show({data: response.message});
      }
    });
  };

  render() {
    return (
      <View>
        <CommonHeadScreen
          title="装窑管理"
          do="encastage"
          leftItem={() => this.renderLeftItem()}
          rightItem={() => this.renderRightItem()}
        />
        {/* <View style={[CommonStyle.rightTop50FloatingBlockView]}>
                    <Text style={CommonStyle.rightTop50FloatingBlockText}>{this.state.dataSource.length}/{this.state.totalRecord}</Text>
                </View> */}
        <View style={[CommonStyle.rightAbsoluteButtonContainer]}>
          <View style={[CommonStyle.rightAbsoluteButtonView]}>
            <TouchableOpacity onPress={() => this.openGmtCreated()}>
              <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>
                {!this.state.gmtCreated ? '时间' : this.state.gmtCreated}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={[CommonStyle.rightAbsoluteButtonView, {width: 90}]}>
            <TouchableOpacity
              onPress={() => {
                Alert.alert('确认', '您确定要导出PDF文件吗？', [
                  {
                    text: '取消',
                    onPress: () => {
                      WToast.show({data: '点击了取消'});
                    },
                  },
                  {
                    text: '确定',
                    onPress: () => {
                      WToast.show({data: '点击了确定'});
                      this.exportPdfFile();
                    },
                  },
                ]);
              }}>
              <View style={[CommonStyle.rightAbsoluteButtonBoxView]}>
                <Image
                  style={[CommonStyle.rightAbsoluteButtonIconView]}
                  source={require('../../assets/icon/iconfont/output.png')}></Image>
                <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>
                  明细
                </Text>
              </View>
            </TouchableOpacity>
          </View>

          <View style={[CommonStyle.rightAbsoluteButtonView, {width: 90}]}>
            <TouchableOpacity
              onPress={() => {
                Alert.alert('确认', '您确定要导出报表文件吗？', [
                  {
                    text: '取消',
                    onPress: () => {
                      WToast.show({data: '点击了取消'});
                    },
                  },
                  {
                    text: '确定',
                    onPress: () => {
                      WToast.show({data: '点击了确定'});
                      this.exportReportPdfFile();
                    },
                  },
                ]);
              }}>
              <View style={[CommonStyle.rightAbsoluteButtonBoxView]}>
                <Image
                  style={[CommonStyle.rightAbsoluteButtonIconView]}
                  source={require('../../assets/icon/iconfont/output.png')}></Image>
                <Text style={[CommonStyle.rightAbsoluteButtonTextView]}>
                  报表
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        <View style={CommonStyle.contentViewStyle}>
          {/* <ScrollView style={[CommonStyle.contentViewStyle,{marginBottom:0}]}> */}
          {/* <View style={CommonStyle.lineListHeadRenderRowStyle}>
                        </View>  */}
          <FlatList
            data={this.state.dataSource}
            ItemSeparatorComponent={this.space}
            renderItem={({item, index}) => this.renderRowNew(item, index)}
            keyExtractor={(item, index) => item.encastageId}
            ListEmptyComponent={this.emptyComponent}
            // 自定义下拉刷新
            refreshControl={
              <RefreshControl
                tintColor="#FF0000"
                title="loading"
                colors={['#FF0000', '#00FF00', '#0000FF']}
                progressBackgroundColor="#FFFF00"
                refreshing={this.state.refreshing}
                onRefresh={() => {
                  this._loadFreshData();
                }}
              />
            }
            // 底部加载
            ListFooterComponent={() => this.flatListFooterComponent()}
            onEndReached={() => this._loadNextData()}
            onEndReachedThreshold={0.5}
          />
          {/* </ScrollView> */}
        </View>
        <BottomScrollSelect
          ref={'SelectGmtCreated'}
          callBackDateValue={this.callBackSelectGmtCreatedValue.bind(this)}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  // contentViewStyle:{
  //     height:screenHeight - 70,
  //     backgroundColor:'#FFFFFF'
  // },
  innerViewStyle: {
    marginTop: 10,
    // borderColor: "#F4F4F4",
    // borderWidth: 8
  },
  titleViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginRight: 10,
    marginBottom: 5,
    marginTop: 5,
  },
  titleTextStyle: {
    fontSize: 16,
  },
  itemContentStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 15,
    paddingTop: 5,
  },
  itemContentImageStyle: {
    width: 120,
    height: 120,
  },
  itemContentViewStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 25,
  },
  itemContentChildViewStyle: {
    flexDirection: 'row',
  },
  itemContentChildCol1ViewStyle: {
    marginLeft: 20,
    marginTop: 15,
  },
  itemContentChildCol2ViewStyle: {
    marginLeft: 40,
    marginTop: 15,
  },
  itemContentChildTextStyle: {
    fontSize: 15,
  },
  itemBottomBtnStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  itemBottomDeleteBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    borderWidth: 1,
    borderColor: '#A0A0A0',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    borderRadius: 4,
  },
  itemBottomEditBtnViewStyle: {
    fontSize: 16,
    width: 100,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
    backgroundColor: '#CB4139',
    borderRadius: 4,
  },
  itemBottomEditBtnTextStyle: {
    color: '#F0F0F0',
  },
  newrightTop50FloatingBlockView: {
    zIndex: 100,
    position: 'absolute',
    right: 15,
    backgroundColor: '#F2C16D',
    width: 80,
    height: 30,
    opacity: 0.9,
    alignItems: 'center',
    borderRadius: 50,
    justifyContent: 'center',
  },
});
module.exports = EncastageList;
