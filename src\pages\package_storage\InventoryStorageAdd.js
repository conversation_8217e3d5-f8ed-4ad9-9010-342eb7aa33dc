import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,KeyboardAvoidingView,
    TouchableOpacity,Dimensions,Image,Modal} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class InventoryStorageAdd extends Component {
    constructor(){
        super()
        this.state = {
            storageInId:'',
            // 原始数据
            seriesDataSource:[],
            // 过滤后的数据
            _seriesDataSource:[],
            brickTypeDataSource:[],
            storageLocationDataSource:[],
            storageLocationAreaDataSource:[],
            selSeriesId:"",
            selSeriesName:"",
            selBrickTypeId:"",
            selLocationId:"",
            selLocationAreaId:0,
            inAmount:"",
            actualSingleWeight:"",
            actualTotalWeight:"",
            actualSize:"",
            location:"",
            selPackageAmount:"",
            amountPerPackage:"",
            selectedStorageInDate: [],
            storageInTime: "",
            seriesModal:false,
            searchKeyWord:null
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        // 当前时间
        var currentDate = new Date();
        var currentDateMonth = ("0" + (currentDate.getMonth() + 1)).slice(-2);
        var currentDateDay = ("0" + currentDate.getDate()).slice(-2);
        this.setState({
            selectedStorageInDate: [currentDate.getFullYear(), currentDateMonth, currentDateDay],
            storageInTime: currentDate.getFullYear() + "-" + currentDateMonth + "-" + currentDateDay
        })
        this.loadInitData();
    }

    // 加载对应库区的库位信息
    loadStorageLocationByAreaId=(locationAreaId)=>{
        let url= "/biz/storage/location/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':1000,
            'locationAreaId':locationAreaId
        };
        httpPost(url, loadRequest, this.callBackLoadStorageLocationByAreaId);
    }

    // 库位信息回调
    callBackLoadStorageLocationByAreaId=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库位");
                WToast.show(toastOpts);
                this.setState({
                    storageLocationDataSource:[]
                })
                return;
            }
            this.setState({
                storageLocationDataSource:response.data.dataList,
            })
            if (this.state.storageInId && this.state.selLocationId != 0) {
                this.setState({
                    selLocationId:this.state.selLocationId,
                })
            }
            else if (response.data.dataList.length > 0) {
                this.setState({
                    selLocationId:response.data.dataList[0].locationId
                })
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadInitData=()=>{
        // 加载库区列表
        let url= "/biz/storage/location/area/list";
        let request={'currentPage':1,'pageSize':1000};
        httpPost(url, request, this.callBackLoadStorageLocationArea);

        // 加载排产状态的订单，显示砖型
        let loadTypeUrl= "/biz/brick/class/series/list";
        let loadRequest={
            'currentPage':1,
            'pageSize':100
        };
        httpPost(loadTypeUrl, loadRequest, this.seriesLoadCallBack);

        
    }

    // 砖的系列回调加载
    seriesLoadCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                WToast.show({data:"没有砖大类"});
                return;
            }
            this.setState({
                seriesDataSource:response.data.dataList,
                // selSeriesId:response.data.dataList[0] ? response.data.dataList[0].seriesId : "",
                inAmount:"",
            })
            let loadTypeUrl;
            let loadRequest;
            const { route, navigation } = this.props;
            if (route && route.params) {
                const { storageInId } = route.params;
                if (storageInId) {
                    console.log("========Edit==storageInId:", storageInId);
                    this.setState({
                        storageInId:storageInId
                    })
                    loadTypeUrl= "/biz/storage/in/get";
                    loadRequest={'storageInId':storageInId};
                    httpPost(loadTypeUrl, loadRequest, this.loadEditStorageInDataCallBack);
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadEditStorageInDataCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            console.log("=============loadEditStorageInDataCallBack:", response.data);
            var selectedStorageInDate = response.data.storageInTime.split("-");
            this.setState({
                storageInId:response.data.storageInId,
                selBrickTypeId:response.data.brickTypeId,
                selSeriesId:response.data.seriesId,
                selSeriesName:response.data.seriesName,
                inAmount:response.data.inAmount,
                selPackageAmount:response.data.packageAmount,
                amountPerPackage:response.data.inAmount/response.data.packageAmount,
                actualSingleWeight:response.data.actualSingleWeight,
                actualTotalWeight:(response.data.actualTotalWeight / 1000).toFixed(2),
                actualSize:response.data.actualSize,
                selLocationAreaId:response.data.locationAreaId,
                selLocationId:response.data.locationId,
                location:response.data.location,
                storageInTime:response.data.storageInTime,
                selectedStorageInDate:selectedStorageInDate,
            })
            // 调接口查询该系列下的具体砖型
            let loadTypeUrl;
            let loadRequest;
            loadTypeUrl= "/biz/brick/series/type/list";
            loadRequest={
                "seriesId": response.data.seriesId,
                "currentPage": 1,
                "pageSize": 100
            };
            httpPost(loadTypeUrl, loadRequest, this.brickTypeLoadCallBack);
            
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
        else {
            WToast.show({data:response.message});
        }
    }

    brickTypeLoadCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            this.setState({
                brickTypeDataSource:response.data.dataList,
            })
            console.log("=============this.state.brickTypeDataSource:", this.state.brickTypeDataSource);
        }
    }
    // 库区回调加载
    callBackLoadStorageLocationArea=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            if (response.data.dataList.length <= 0) {
                let toastOpts = getFailToastOpts("请联系管理员添加库区");
                WToast.show(toastOpts);
                return;
            }
            this.setState({
                storageLocationAreaDataSource:response.data.dataList,
            })
            const { route, navigation } = this.props;
            if (route && route.params) {
                const { storageInId,locationAreaId } = route.params;
                if(locationAreaId){
                    this.setState({
                        selLocationAreaId:locationAreaId,
                    })
                    this.loadStorageLocationByAreaId(locationAreaId)
                }
                else if (response.data.dataList.length > 0) {
                    this.setState({
                        selLocationAreaId:response.data.dataList[0].locationAreaId
                    })
                    this.loadStorageLocationByAreaId(response.data.dataList[0].locationAreaId);
                }
            }
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddLeftViewStyle }>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}  style={[ CommonStyle.btnAddLeftBtn ]}>
                    <Image  style={ CommonStyle.btnAddLeftBtnView } source={require('../../assets/icon/iconfont/back.png')}></Image>
                    <Text style={ CommonStyle.btnAddLeftBtnText }>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => { 
            //     this.props.navigation.navigate("StorageInList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>入库管理</Text>
            // </TouchableOpacity>
            <View style={ CommonStyle.viewAddRightViewStyle}>
                <TouchableOpacity onPress={() => {

                }}>
                    {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={ CommonStyle.btnAddRightBtnText }>入库管理</Text>
                </TouchableOpacity>
            </View>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveStorageIn =()=> {
        console.log("=======saveStorageIn");
        let toastOpts;
        if (!this.state.selBrickTypeId || this.state.selBrickTypeId === 0) {
            toastOpts = getFailToastOpts("请选择要入库的砖型");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selPackageAmount || this.state.selPackageAmount === "0") {
            WToast.show({data:"请输入件数"});
            return;
        }
        // if (!this.state.amountPerPackage || this.state.amountPerPackage === "0") {
        //     WToast.show({data:"请输入块/件"});
        //     return;
        // }
        if (!this.state.inAmount || this.state.inAmount === 0) {
            toastOpts = getFailToastOpts("请输入数量");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selLocationId || this.state.selLocationId === 0) {
            toastOpts = getFailToastOpts("请选择库位");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.actualTotalWeight) {
            toastOpts = getFailToastOpts("请输入实际总重");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/storage/in/add";
        if (this.state.storageInId) {
            console.log("=========Edit===storageInId", this.state.storageInId)
            url= "/biz/storage/in/modify";
        }
        let requestParams={
            "storageInId":this.state.storageInId,
            "brickTypeId":this.state.selBrickTypeId,
            "inAmount":this.state.inAmount,
            "packageAmount":this.state.selPackageAmount,
            "actualSingleWeight":this.state.actualSingleWeight,
            "actualTotalWeight":this.state.actualTotalWeight * 1000,
            "actualSize":this.state.actualSize,
            "locationId":this.state.selLocationId,
            "location":this.state.location,
            "operator":constants.loginUser.userName,
            "storageInTime":this.state.storageInTime,
        };
        httpPost(url, requestParams, this.saveStorageInCallBack);
    }
    
    // 保存回调函数
    saveStorageInCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    if (this.state.storageInId) {
                        return;
                    }
                    this.setState({
                        selSeriesId:item.seriesId,
                        inAmount:"",
                    })

                    // 调接口查询该系列下的具体砖型
                    let loadTypeUrl;
                    let loadRequest;
                    loadTypeUrl= "/biz/brick/series/type/list";
                    loadRequest={
                        "seriesId": item.seriesId,
                        "currentPage": 1,
                        "pageSize": 100
                    };
                    httpPost(loadTypeUrl, loadRequest, this.brickTypeLoadCallBack);
                }}>
                <View key={item.orderId} style={[item.seriesId===this.state.selSeriesId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle, this.state.storageInId ? CommonStyle.disableViewStyle : ''] }>
                    <Text style={item.seriesId===this.state.selSeriesId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.seriesName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 库区
    renderLocationAreaRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.storageInId) {
                    let toastOpts = getFailToastOpts('不能编辑');
                    WToast.show(toastOpts);
                    return;
                }
                this.setState({
                    selLocationAreaId:item.locationAreaId
                }) 
                this.loadStorageLocationByAreaId(item.locationAreaId);
            }}>
                <View key={item.locationAreaId} style={[this.state.storageInId ? CommonStyle.disableViewStyle : null,item.locationAreaId === this.state.selLocationAreaId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 4,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.locationAreaId === this.state.selLocationAreaId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.locationAreaName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    // 库位
    renderLocationRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => { 
                if (this.state.storageInId) {
                    let toastOpts = getFailToastOpts('不能编辑');
                    WToast.show(toastOpts);
                    return;
                }
                this.setState({
                    selLocationId:item.locationId
                })
            }}>
                <View key={item.locationId} style={[this.state.storageInId ? CommonStyle.disableViewStyle : null,item.locationId === this.state.selLocationId ?
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#F2F5FC'}
                    ,
                    {
                        marginRight: 8,
                        marginTop: 8,
                        marginBottom: 4,
                        borderRadius: 4,
                        justifyContent: 'center',
                        alignContent: 'center',
                        height: 36,
                        width: (screenWidth - 54)/3,
                        borderRadius: 4
                    }
                ]}>
                    <Text style={[item.locationId === this.state.selLocationId ?
                        {
                            color: '#1E6EFA'
                        }
                        :
                        {
                            color: '#404956'
                        }
                        ,
                    {
                        fontSize: 16, textAlign : 'center'
                    }
                    ]}>
                        {item.locationName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    openStorageInDate() {
        if (this.state.storageInId) {
            return;
        }
        this.refs.SelectStorageInDate.showDate(this.state.selectedStorageInDate)
    }

    callBackSelectStorageInDateValue(value) {
        console.log("==========提交时间选择结果：", value)
        if (!value) {
            return;
        }
        this.setState({
            selectedStorageInDate: value
        })
        if (value && value.length) {
            var storageInTime = "";
            var vartime;
            for (var index = 0; index < value.length; index++) {
                vartime = value[index];
                if (index === 0) {
                    storageInTime += vartime;
                }
                else {
                    storageInTime += "-" + vartime;
                }
            }
            this.setState({
                storageInTime: storageInTime
            })
        }
    }

    loadBrickSeries = () => {
        var _seriesDataSource = copyArr(this.state.seriesDataSource);
        if (this.state.searchKeyWord && this.state.searchKeyWord.length > 0) {
            _seriesDataSource = _seriesDataSource.filter(item => item.seriesName.indexOf(this.state.searchKeyWord) > -1);
        }
        this.setState({
            _seriesDataSource: _seriesDataSource,
        })
    }

    renderSeriesRow = (item) => {
        return (
            <TouchableOpacity onPress={()=>{
                this.setState({
                    selSeriesId:item.seriesId,
                    selSeriesName:item.seriesName,
                    selBrickTypeId:null,
                    inAmount:"",
                })
                WToast.show({data:'点击了' + item.seriesName});
                // 调接口查询该系列下的具体砖型
                let loadTypeUrl;
                let loadRequest;
                loadTypeUrl= "/biz/brick/series/type/list";
                loadRequest={
                    "seriesId": item.seriesId,
                    "currentPage": 1,
                    "pageSize": 100
                };
                httpPost(loadTypeUrl, loadRequest, this.brickTypeLoadCallBack);

            }}>
                <View key={item.seriesId} style={[item.seriesId===this.state.selSeriesId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.seriesId===this.state.selSeriesId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.seriesName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render(){
        return (
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title='库存品入库'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                    />
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: -2 }} />
                <ScrollView style={CommonStyle.formContentViewStyle}>
                <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle,{color:'#FFFFFF',}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>入库日期</Text>
                        </View>
                        <TouchableOpacity onPress={() => this.openStorageInDate()}>
                        <View style={[CommonStyle.inputTextStyleTextStyle, {  borderWidth: 0, width: screenWidth - (leftLabWidth + 50) }]}>
                                <Text style={{ color: '#A0A0A0', fontSize: 15 }}>
                                    {!this.state.storageInTime ? "请选择入库日期" : this.state.storageInTime}
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%',  marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabRedTextStyle}>*</Text>
                                    <Text style={styles.leftLabNameTextStyle}>产品分类</Text>
                                    {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                                </View>
                                <View style={[(!this.state.seriesDataSource || this.state.seriesDataSource.length === 0 || this.state.selSeriesId) ? CommonStyle.disableViewStyle : null]}>
                                    <TouchableOpacity onPress={() => {
                                        if (this.state.storageInId) {
                                            WToast.show({ data: "只能编辑件数、块/件、数量、单重、总重" })
                                            return;
                                        }
                                        if (this.state.seriesDataSource && this.state.seriesDataSource.length > 0) {
                                            this.setState({
                                                _seriesDataSource: copyArr(this.state.seriesDataSource),
                                            })
                                        }
                                        this.setState({
                                            seriesModal: true,
                                            searchKeyWord: ""
                                        })

                                        if (!this.state.selSeriesId && this.state.seriesDataSource && this.state.seriesDataSource.length > 0) {
                                            this.setState({
                                                selSeriesId: this.state.seriesDataSource[0].seriesId,
                                                selSeriesName: this.state.seriesDataSource[0].seriesName,
                                            })
                                        }
                                    }}>
                                        <View style={[this.state.selSeriesId ?
                                            {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                            :
                                            {backgroundColor: '#F2F5FC'}
                                            ,
                                            {
                                                marginRight: 8,
                                                marginTop: 4,
                                                marginBottom: 4,
                                                borderRadius: 4,
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                                height: 36,
                                                paddingLeft:6,
                                                paddingRight:6,
                                                // width: (screenWidth - 54)/2,
                                                borderRadius: 4,
                                            }
                                        ]}>
                                            <Text style={[this.state.selSeriesId ?
                                                { color: '#1E6EFA' }
                                                :
                                                { color: '#404956' }
                                                ,
                                            {
                                                fontSize: 16, textAlignVertical : 'center'
                                            }
                                            ]}>
                                                {this.state.selSeriesId ? ("" + this.state.selSeriesName) : "选择产品分类"}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginLeft:16}} />

                    

                    <Modal      
                        animationType={'slide'}
                        transparent={true}
                        onRequestClose={() => console.log('onRequestClose...')}
                        visible={this.state.seriesModal}>
                        <View style={CommonStyle.fullScreenKeepOut}>
                            <View style={CommonStyle.modalContentViewStyle}>
                                <View style={CommonStyle.rowLabView}>
                                    <TextInput
                                        style={[CommonStyle.modalSearchInputText]}
                                        placeholder={'请输入查询关键字'}
                                        onChangeText={(text) => this.setState({ searchKeyWord: text })}
                                    >
                                        {this.state.searchKeyWord}
                                    </TextInput>
                                    <TouchableOpacity onPress={() => {
                                        this.loadBrickSeries();
                                    }}>
                                        <View style={[CommonStyle.modalSearchBtnViewStyle]}>
                                            <Text style={CommonStyle.modalSearchBtnTextStyle}>查询</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <ScrollView style={{}}>
                                    <View style={{ flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                                        {
                                            (this.state._seriesDataSource && this.state._seriesDataSource.length > 0)
                                                ?
                                                this.state._seriesDataSource.map((item, index) => {
                                                    if (index < 1000) {
                                                        return this.renderSeriesRow(item)
                                                    }
                                                })
                                                : <EmptyRowViewComponent />
                                        }
                                    </View>
                                </ScrollView>
                                <View style={[CommonStyle.btnRowStyle, { justifyContent: 'center' }]}>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            seriesModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: screenWidth / 2 - 100, marginRight: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight:5 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                            <Text style={[CommonStyle.btnRowLeftCancelBtnText, { fontWeight: 'bold' }]}>取消</Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => {
                                        this.setState({
                                            seriesModal: false,
                                        })
                                    }}>
                                        <View style={[CommonStyle.btnRowRightSaveBtnView, { width: screenWidth / 2 - 100, marginLeft: 20 }]}>
                                            <Image style={{width:30, height:30,marginRight:5}} source={require('../../assets/icon/iconfont/ok.png')}></Image>
                                            <Text style={[CommonStyle.btnRowRightSaveBtnText, { fontWeight: 'bold' }]}>确定</Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <View style={styles.rowLabView}>
                        <Text style={styles.leftLabRedTextStyle}>*</Text>
                        <Text style={styles.leftLabNameTextStyle}>砖型</Text>
                        {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                    </View>
                    
                    <View style={[{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 16, marginRight: 15}]}>
                        {!this.state.brickTypeDataSource || this.state.brickTypeDataSource.length < 1 ? 
                        <View style={{width:screenWidth}}><EmptyRowViewComponent/></View> 
                        : <Text></Text>} 
                    {this.state.brickTypeDataSource.map((item, key)=>{
                        return(
                            <TouchableOpacity onPress={()=>{
                                if (this.state.storageInId) {
                                    WToast.show({ data: "只能编辑件数、块/件、数量、单重、总重" })
                                    return;
                                }
                                this.setState({
                                    selBrickTypeId:item.brickTypeId,
                                    actualSize:item.standardSize,
                                    actualSingleWeight:item.standardWeight,
                                    inAmount:"",
                                })
                                WToast.show({data:'点击了' + item.brickTypeName});
                            }}>
                                {/* <View style={[{margin:10, borderRadius:4, padding:10, height:40, backgroundColor:'#F5F5F5'}, (item.seriesId===this.state.selSeriesId) ? {backgroundColor:'red'} : ""]}>
                                    <Text style={[styles.titleTextStyle, (item.seriesId===this.state.selSeriesId) ? {color:'#FFFFFF'} : {color:'#000000'}]}>{item.seriesName}</Text>
                                </View> */}
                                <View key={item.brickTypeId} style={[this.state.storageInId ? CommonStyle.disableViewStyle : null,item.brickTypeId === this.state.selBrickTypeId ?
                                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                                    :
                                    {backgroundColor: '#F2F5FC'}
                                    ,
                                    {
                                        marginRight: 8,
                                        marginTop: 4,
                                        marginBottom: 4,
                                        borderRadius: 4,
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                        height: 36,
                                        width: (screenWidth - 54)/2,
                                        borderRadius: 4
                                    }
                                ]}>
                                    <Text style={[item.brickTypeId === this.state.selBrickTypeId ?
                                        {
                                            color: '#1E6EFA'
                                        }
                                        :
                                        {
                                            color: '#404956'
                                        }
                                        ,
                                    {
                                        fontSize: 16, textAlign : 'center'
                                    }
                                    ]}>
                                        {item.brickTypeName}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        )
                    })}
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%',  marginLeft:16}} />

                    {/* <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>实际尺寸</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入实际尺寸'}
                            onChangeText={(text) => this.setState({actualSize:text})}
                        >
                            {this.state.actualSize}
                        </TextInput>
                    </View> */}
                     <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>件数</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入'}
                            onChangeText={
                                (text) => {
                                    this.setState({selPackageAmount:text})
                                    if(this.state.amountPerPackage){
                                        this.setState({
                                            inAmount:text*this.state.amountPerPackage
                                        })
                                        if (this.state.actualSingleWeight) {
                                            this.setState({
                                                actualTotalWeight:(text*this.state.amountPerPackage*this.state.actualSingleWeight / 1000).toFixed(2)
                                            })
                                        }
                                    }
                                }
                            }
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                            {this.state.selPackageAmount}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%',  marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle, {color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>块/件</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            placeholder={'请输入'}
                            onChangeText={
                                (text) =>{
                                    this.setState({amountPerPackage:text})
                                    if(this.state.selPackageAmount){
                                        this.setState({
                                            inAmount:text*this.state.selPackageAmount
                                        })
                                        if (this.state.actualSingleWeight) {
                                            this.setState({
                                                actualTotalWeight:(text*this.state.selPackageAmount*this.state.actualSingleWeight / 1000).toFixed(2)
                                            })
                                        }
                                    }
                                    else{
                                        WToast.show({data:"请输入件数"});
                                    }
                                }
                                }
                                style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}>
                                {this.state.amountPerPackage}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%',  marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>数量</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}
                            placeholder={'请输入'}
                            onChangeText={(text) => {
                                if (this.state.actualSingleWeight) {
                                    this.setState({
                                        actualTotalWeight:(text*this.state.actualSingleWeight / 1000).toFixed(2)
                                    })
                                }
                                this.setState({inAmount:text})
                            }}
                        >
                            {this.state.inAmount}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%',  marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>库区</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                        {
                            (this.state.storageLocationAreaDataSource && this.state.storageLocationAreaDataSource.length > 0) 
                            ? 
                            this.state.storageLocationAreaDataSource.map((item, index)=>{
                                return this.renderLocationAreaRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%',  marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>库位</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                    </View>
                    <View style={{width: screenWidth -30, flexWrap: 'wrap', flexDirection: 'row', justifyContent: 'flex-start', marginLeft: 15, marginRight: 15}}>
                        {
                            (this.state.storageLocationDataSource && this.state.storageLocationDataSource.length > 0) 
                            ? 
                            this.state.storageLocationDataSource.map((item, index)=>{
                                return this.renderLocationRow(item)
                            })
                            : <EmptyRowViewComponent/> 
                        }
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%',  marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle, {color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>备注</Text>
                        </View>
                        <TextInput 
                        style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}
                        placeholder={'请输入'}
                            onChangeText={(text) => this.setState({location:text})}
                        >
                            {this.state.location}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%',  marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={[styles.leftLabRedTextStyle, {color:'#FFFFFF'}]}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>实际单重(Kg)</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}
                            placeholder={'请输入'}
                            onChangeText={(text) => {
                                if (this.state.inAmount) {
                                    this.setState({
                                        actualTotalWeight:(text*this.state.inAmount / 1000).toFixed(2)
                                    })
                                }
                                this.setState({actualSingleWeight:text})
                            }}
                        >
                            {this.state.actualSingleWeight}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%',  marginLeft:16}} />

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>实际总重(吨)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={[styles.inputRightText, { width: screenWidth - (leftLabWidth + 50) }]}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({actualTotalWeight:text})}
                        >
                            {this.state.actualTotalWeight}
                        </TextInput>
                    </View>
                    <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%',  marginLeft:16}} />

                    <View style={[CommonStyle.blockAddCancelSaveStyle]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveStorageIn.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image  style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                <BottomScrollSelect
                    ref={'SelectStorageInDate'}
                    callBackDateValue={this.callBackSelectStorageInDateValue.bind(this)}
                />
            </KeyboardAvoidingView>
        );
    }
}

let styles = StyleSheet.create({
    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:6,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        // borderRadius:5,
        // borderColor:'#F1F1F1',
        // borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})