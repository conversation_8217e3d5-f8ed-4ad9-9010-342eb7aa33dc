import React,{ Component } from 'react';
import {View, ScrollView, Text, TextInput, StyleSheet,Alert,FlatList,Image,TouchableOpacity,Dimensions,LinearLayout} from 'react-native';
const {ifIphoneXContentViewHeight, } = require('../../utils/ScreenUtil');

// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip'
import BottomScrollSelect from '../../component/BottomScrollSelect';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyRowViewComponent from '../../component/EmptyRowViewComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;

export default class PromotionPlanSelUser extends Component {
    constructor(){
        super()
        this.state = {
            trackId: "",
            contractId:"",
            trackRemark:"",
            userId:"",
            userSearchKeyWord: "",
            userDataSource: [],
            _userDataSource: [],
            selCheckInUserId: "",
            selCheckInUserName: "",
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { selCheckInUserId, selCheckInUserName } = route.params;
            console.log('selCheckInUserId==========', selCheckInUserId)
            this.setState({
                selCheckInUserId: selCheckInUserId,
                selCheckInUserName: selCheckInUserName
            })
        }
        this.loadCheckInUserList();
    }

    loadCheckInUserList = () => {
        let url = "/biz/portal/user/list";
        let loadRequest = {
            "currentPage": 1,
            "pageSize": 1000,
        };
        httpPost(url, loadRequest, this.loadCheckInUserListCallBack);
    }

    loadCheckInUserListCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataSource = response.data.dataList;
            // dataSource = dataSource.filter(item => item.userId != constants.loginUser.userId)
            this.setState({
                userDataSource: dataSource
            })
            if (this.state.userDataSource && this.state.userDataSource.length > 0) {
                this.setState({
                    _userDataSource: copyArr(this.state.userDataSource)
                })
            }
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    }
    
    loadUser = () => {
        var _userDataSource = copyArr(this.state.userDataSource);
        if (this.state.userSearchKeyWord && this.state.userSearchKeyWord.length > 0) {
            _userDataSource = _userDataSource.filter(item => item.userName.indexOf(this.state.userSearchKeyWord) > -1);
        }
        this.setState({
            _userDataSource: _userDataSource,
        })
    }

    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <View/>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    //员工显示组件
    renderUserRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    selCheckInUserId: item.userId,
                    selCheckInUserName: item.userName,
                })
            }}>
                <View key={item.userId} style={[item.userId === this.state.selCheckInUserId ? 
                    {backgroundColor: '#FFFFFF', borderColor: '#1E6EFA', borderWidth: 1}
                    :
                    {backgroundColor: '#FFFFFF'}
                    ,
                    {
                        height: 36,
                        marginRight: 8,
                        marginTop: 11,
                        marginBottom: 4,
                        paddingLeft: 16,
                        paddingTop: 8,
                        paddingRight: 16,
                        paddingBottom: 8,
                        borderRadius: 8,
                        justifyContent: 'center',
                        alignContent: 'center',
                    }
                ]}>
                    <Text style={[item.userId === this.state.selCheckInUserId ? 
                        {color: '#1E6EFA'}
                        :
                        {color: 'rgba(0, 10, 32, 0.85)'}
                        ,
                        {
                            fontSize: 16, textAlign : 'center'
                        }
                    ]}>
                        {item.userName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    topBlockLayout = (event) => {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })
    }

    render(){
        return (
            <View style={[CommonStyle.formContentViewStyle]}>
                <CommonHeadScreen title='选择人员'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
            
                <View style={[CommonStyle.headViewStyle, { borderLeftWidth: 0, borderRightWidth: 0 }]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={CommonStyle.singleSearchBox}>
                        <View style={[CommonStyle.searchBoxWithoutOthers,{justifyContent:'center'}]}>
                            <Image style={{ width: 16, height: 16, marginRight:6 }} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            <TextInput
                                style={{ color: 'rgba(0, 10, 32, 0.45)', fontSize: 14, paddingTop: 0, paddingBottom: 0, paddingRight: 0, paddingLeft: 0 }}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.loadUser();
                                }}
                                placeholder={'请输入姓名'}
                                onChangeText={(text) => this.setState({ userSearchKeyWord: text })}
                            >
                                {this.state.userSearchKeyWord}
                            </TextInput>
                        </View>
                    </View>
                </View>

                <ScrollView style={[CommonStyle.formContentViewStyle, {backgroundColor: 'rgba(242, 245, 252, 1)',marginBottom: 0}]}>
                    <View style={{ width: screenWidth -30, marginLeft: 15, marginRight: 15, flexDirection: 'row', flexWrap: 'wrap', overflow: 'scroll' }}>
                        {
                            (this.state._userDataSource && this.state._userDataSource.length > 0)
                                ?
                                this.state._userDataSource.map((item, index) => {
                                    if (index < 1000) {
                                        return this.renderUserRow(item)
                                    }
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>
                   
                </ScrollView>

                <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0,marginTop:0,marginBottom:0,backgroundColor:'rgba(255, 255, 255, 1)',height:62}]}>
                    <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                        <View style={[CommonStyle.btnRowLeftCancelBtnView, {marginLeft: 20,marginTop:10, width: (screenWidth - 56)/2}]} >
                            <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {
                        if (this.props.route.params.refresh) {
                            this.props.route.params.refresh(this.state.selCheckInUserId, this.state.selCheckInUserName);
                        }
                        this.props.navigation.goBack()
                    }}>
                        <View style={[CommonStyle.btnRowRightSaveBtnView, {marginRight: 20,marginTop:10, width: (screenWidth - 56)/2}]}>
                            <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        );
    }
}

let styles = StyleSheet.create({
    itemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:'#FFFFFF'
    },
    selectedItemViewStyle:{
        margin:10,  
        padding:15, 
        borderRadius:2,
        backgroundColor:"#CB4139"
    },
    itemTextStyle:{
        color:'#000000'
    },
    selectedItemTextStyle:{
        color:'#FFFFFF'
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 5),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10
    }
})