import React,{Component} from 'react';
import {
    <PERSON><PERSON>,Modal,
    View, 
    ScrollView, 
    Text, 
    TextInput, 
    StyleSheet, 
    FlatList ,
    TouchableOpacity,
    Dimensions,
    Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class HLDoctorAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            text: '初始状态', 
            refreshing: false,
            pageSize:15,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            operate:"",

            doctorId:"",
            doctorName:"",

            departmentId:"",
            departmentDataSource:[],
            departmentName:"",

            doctorCode:"",
            gender:"",
            jobNumber:"",
            contactTel:"",
            doctorSort:0,
            tenantId:"",

            hospitalId:"",
            hospitalName:"",
            hospitalDataSource:[],

            genderDataSource:[
                {
                    genderId:1,
                    genderType:"男",
                    gendedrName:"M"
                },
                {
                    genderId:2,
                    genderType:"女",
                    gendedrName:"L"
                }
            ],
            selGenderId:1
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { doctorId,departmentId,hospitalId } = route.params;
            if (doctorId) {
                console.log("=============doctorId" + doctorId + "");
                this.setState({
                    doctorId:doctorId,
                    operate:"编辑"
                })
                let loadTypeUrl= "/biz/hl/doctor/get";
                let loadRequest={'doctorId':doctorId};
                httpPost(loadTypeUrl, loadRequest, this.loadDoctorCallBack);
            }
            else {
                this.setState({
                    operate:"新增",  
                    gender:"M"
                })
            }

            if (departmentId) {
                console.log("========departmentId:", departmentId);
                this.setState({
                    departmentId:departmentId,
                })
            }

            if (hospitalId) {
                console.log("========hospitalId:", hospitalId);
                this.setState({
                    hospitalId:hospitalId,
                })
            }
        }
    }


    loadDoctorCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            if(response.data.gender == 'M'){
                this.setState({
                    selGenderId:1
                })
            }
            if(response.data.gender == 'L'){
                this.setState({
                    selGenderId:2
                })
            }
            this.setState({
                departmentId:response.data.departmentId,
                departmentName:response.data.departmentName,
                hospitalId:response.data.hospitalId,
                hospitalName:response.data.hospitalName,
                doctorName: response.data.doctorName,
                doctorCode: response.data.doctorCode,
                gender: response.data.gender,
                jobNumber: response.data.jobNumber,
                contactTel: response.data.contactTel,
                doctorSort: response.data.doctorSort,
            })
        }
    }

    
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                this.props.navigation.navigate("HLDoctorList", 
                {
                    // 传递回调函数
                    refresh: this.callBackFunction 
                })
            }}>
                <Text style={CommonStyle.headRightText}>医生设置</Text>
            </TouchableOpacity>
        )
    }


     //sex列表展示
     renderRow=(item)=>{
        return (
            <TouchableOpacity onPress={() => {
                    this.setState({
                        selGenderId:item.genderId,
                    })
                }}>
                <View key={item.genderId} style={[item.genderId===this.state.selGenderId ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle] }>
                    <Text style={item.genderId===this.state.selGenderId ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16 }>
                        {item.genderType}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    emptyComponent() {
        return <EmptyRowViewComponent/>
    }

    saveDoctor=()=>{
        console.log("=======save====");
        let toastOpts;
        if (!this.state.doctorName) {
            toastOpts = getFailToastOpts("请输入医生姓名");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.selGenderId) {
            toastOpts = getFailToastOpts("请选择性别");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.jobNumber) {
            toastOpts = getFailToastOpts("请输入工号");
            WToast.show(toastOpts)
            return;
        }
        if (!this.state.contactTel) {
            toastOpts = getFailToastOpts("请输入联系电话");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/hl/doctor/add";
        if (this.state.doctorId) {
            console.log("=========Edit===doctorId", this.state.doctorId)
            url= "/biz/hl/doctor/modify";
        }
        let requestParams={
            "doctorId":this.state.doctorId,
            "doctorName":this.state.doctorName,
            "doctorCode":this.state.doctorCode,
            "gender": this.state.selGenderId == 1 ?'M' : 'L',
            "departmentId":this.state.departmentId,
            "jobNumber":this.state.jobNumber,
            "contactTel":this.state.contactTel,
            "doctorSort":this.state.doctorSort,
            "hospitalId":this.state.hospitalId,
        };
        httpPost(url, requestParams, this.saveDoctorCallBack);
    }
    saveDoctorCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return(
            <View>
                <CommonHeadScreen title={this.state.operate +'医生'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.contentViewStyle}>
                    <ScrollView style={CommonStyle.contentViewStyle}> 

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                医生姓名
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                            style={[styles.inputRightText]}
                            placeholder={'请输入医生姓名'}
                                onChangeText={(text) => this.setState({doctorName:text})}
                            >
                                {this.state.doctorName}
                            </TextInput>
                        </View>

                        <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabNameTextStyle}>性别</Text>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                        </View>
                        
                        <View style={{width:screenWidth, flexWrap:'wrap', flexDirection:'row'}}>
                            {
                                (this.state.genderDataSource && this.state.genderDataSource.length > 0) 
                                ? 
                                this.state.genderDataSource.map((item, index)=>{
                                    return this.renderRow(item)
                                })
                                : <EmptyRowViewComponent/> 
                            }
                        </View>
                    </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                    工号
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                keyboardType='numeric'
                                style={[styles.inputRightText]}
                                placeholder={'请输入工号'}
                                onChangeText={(text) => this.setState({jobNumber:text})}
                            >
                                {this.state.jobNumber}
                            </TextInput>
                        </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                    联系电话
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                keyboardType='numeric'
                                style={[styles.inputRightText]}
                                placeholder={'请输入联系电话'}
                                onChangeText={(text) => this.setState({contactTel:text})}
                            >
                                {this.state.contactTel}
                            </TextInput>
                        </View>   

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                关联标识
                                </Text>
                            </View>
                            <TextInput 
                                style={[styles.inputRightText]}
                                placeholder={'请输入外系统关联标识'}
                                onChangeText={(text) => this.setState({doctorCode:text})}
                            >
                                {this.state.doctorCode}
                            </TextInput>
                        </View>

                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                                <Text style={styles.leftLabNameTextStyle}>
                                排序
                                </Text>
                                <Text style={styles.leftLabRedTextStyle}>*</Text>
                            </View>
                            <TextInput 
                                keyboardType='numeric'
                                style={[styles.inputRightText]}
                                placeholder={'请输入排序'}
                                onChangeText={(text) => this.setState({doctorSort:text})}
                            >
                                {this.state.doctorSort}
                            </TextInput>
                        </View>
                        <View style={CommonStyle.btnRowStyle}>
                            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                                <View style={[CommonStyle.btnRowLeftCancelBtnView]} >
                                <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                    <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                                </View>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={this.saveDoctor.bind(this)}>
                                <View style={[CommonStyle.btnRowRightSaveBtnView]}>
                                <Image  style={CommonStyle.image} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                    <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </ScrollView>
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    rowLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    inputRowStyle:{
        height:45,
        flexDirection:'row',
        marginTop:10,
        // flex: 1,
        // justifyContent: 'space-between',
         alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },
    leftLabView:{
        width:leftLabWidth,
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    inputRightText:{
        width:screenWidth - (leftLabWidth + 30),
        borderRadius:5,
        borderColor:'#F1F1F1',
        borderWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:15,
        paddingLeft:10,
        paddingRight:10,
        alignItems:'center'
    },
    leftLabNameTextStyle:{
        fontSize:18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle:{
        color:'red',
        marginLeft:5,
        marginRight:5
    },
});