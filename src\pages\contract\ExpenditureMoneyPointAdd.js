import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Image,Dimensions,TextInput, ScrollView,TouchableOpacity,Alert,
    FlatList,RefreshControl,KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
var screenHeight = Dimensions.get('window').height;
export default class ExpenditureMoneyPointAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate:"",
            pointId: "",
            pointName: "",
            pointSort: 0
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { pointId } = route.params;
            if (pointId) {
                console.log("=============pointId" + pointId + "");
                this.setState({
                    pointId:pointId,
                    operate:"编辑",
                })
                let loadTypeUrl = "/biz/contract/collect/money/point/get";
                let loadRequest = { 'pointId': pointId };
                httpPost(loadTypeUrl, loadRequest, this.loadEditContractExpenditureMoneyPointDataCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    loadEditContractExpenditureMoneyPointDataCallBack = (response) => {
        if (response.code == 200 && response.data) {

            this.setState({
                pointName: response.data.pointName,
                pointSort: response.data.pointSort
            })
        }
    }

    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //    <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={{ marginBottom: 1.5, flexDirection: 'row', alignItems: 'center'}}>
                    <Image style={{ width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')} />
                    <Text style={{ color: '#3C6CDE', marginLeft: 3, fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("ExpenditureMoneyPointList", 
            //     {
            //         // 传递回调函数
            //         refresh: this.callBackFunction 
            //     })
            // }}>
            //     <Text style={CommonStyle.headRightText}>支出节点</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
            <TouchableOpacity onPress={() => {

            }}>
            {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                <Text style={{color:'#FFFFFF'}}>支出节点</Text>
            {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
            </TouchableOpacity>
            </View>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent />
    }
    saveContractExpenditureMoneyPoint = () => {
        console.log("=======saveContractExpenditureMoneyPoint");
        let toastOpts;
        if (!this.state.pointName) {
            toastOpts = getFailToastOpts("请填写节点名称");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/contract/collect/money/point/add";
        if (this.state.pointId) {
            console.log("=========Edit===pointId", this.state.pointId)
            url = "/biz/contract/collect/money/point/modify";
        }
        let requestParams = {
            pointId: this.state.pointId,
            pointName: this.state.pointName,
            pointSort: this.state.pointSort,
            pointType:"E"
        };
        httpPost(url, requestParams, this.saveContractExpenditureMoneyPointCallBack);
    }

    // 保存回调函数
    saveContractExpenditureMoneyPointCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '节点'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={CommonStyle.lineHeadBorderStyle} />
                <View style={CommonStyle.contentViewStyle}>
                     <ScrollView style={[CommonStyle.formContentViewStyle]}>

                     <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>节点名称</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ pointName: text })}
                        >
                            {this.state.pointName}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'0'}
                            onChangeText={(text) => this.setState({ pointSort: text })}
                        >
                            {this.state.pointSort}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle} />
                    <View style={{height:ifIphoneXContentViewHeight()-90-105, backgroundColor:'#F2F5FC'}}>

                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{marginTop:0}]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={[CommonStyle.btnAddCancelBtnView]} >
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveContractExpenditureMoneyPoint.bind(this)}>
                            <View style={[CommonStyle.btnAddSaveBtnView]}>
                                {/* <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    </ScrollView>
                    
                </View>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({

    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        // borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }
});