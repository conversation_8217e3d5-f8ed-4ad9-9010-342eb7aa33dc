import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,ScrollView,Image
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewDynamicHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');
var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class HLDepartmentStoreKeeperRelAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataSource:[],
            text: '初始状态', 
            refreshing: false,
            pageSize:10,
            currentPage:1,
            totalPage:1,
            totalRecord:1,
            topBlockLayoutHeight:0,
            departmentId:null,
            departmentName:"",
            selStaffIdList:[],
            oldSelStaffIdList:[],
            searchKeyWord:null,
        }
    }

    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { departmentId ,departmentName} = route.params;
            if (departmentName) {
                console.log("=============departmentName" + departmentName + "");
                this.setState({
                    departmentName:departmentName,
                })
                
            }
            if (departmentId) {
                console.log("=============departmentId" + departmentId + "");
                this.setState({
                    departmentId:departmentId,
                })
                this.loadDepartmentStaffList(departmentId);
                this.loadDepartmentUserIdList(departmentId);                
            }
        }
    }


    loadDepartmentStaffList=(departmentId)=>{
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": this.state.currentPage,
            "pageSize": this.state.pageSize,
            "departmentId": departmentId ? departmentId : this.state.departmentId,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this.loadDepartmentStaffListCallBack);
    }

    loadDepartmentStaffListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            var dataOld = this.state.dataSource;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataOld, ...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    loadDepartmentUserIdList=(departmentId)=>{
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": 10000,
            "departmentId": departmentId ? departmentId : this.state.departmentId,
            "searchKeyWord": this.state.searchKeyWord,
        };
        httpPost(url, loadRequest, this.loadDepartmentUserIdListCallBack);
    }

    loadDepartmentUserIdListCallBack=(response)=>{
        if (response.code == 200 && response.data && response.data.dataList) {
            // this.setState({
            //     dataSource:response.data.dataList
            // })
            var staffDTO;
            var selStaffIdList = [];
            for(var index = 0; index < response.data.dataList.length; index ++) {
                staffDTO = response.data.dataList[index];
                if (staffDTO && staffDTO.departmentSelectedStaff === "Y") {
                    selStaffIdList = selStaffIdList.concat(staffDTO.userId)
                }
            }
            this.setState({
                selStaffIdList:selStaffIdList,
                oldSelStaffIdList:copyArr(selStaffIdList),
            })
            console.log("=========oldSelStaffIdList:", selStaffIdList);
        }
        else if (response.code == 401) {
            WToast.show({data:response.message});
            this.props.navigation.navigate("LoginView");
        }
    }

    // 上拉触底加载下一页
    _loadNextData = () => {
        if ((this.state.currentPage - 1) >= this.state.totalPage) {
            WToast.show({ data: "已经是最后一页了，我们也是有底线的" });
            return;
        }
        this.setState({
            refreshing: true
        })
        this.loadDepartmentStaffList();
    }

    searchByKeyWord=()=>{
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId":  this.state.departmentId,
            "searchKeyWord": this.state.searchKeyWord,
        }
        httpPost(url, loadRequest, this._loadFreshDataCallBack);

    }

    // 下拉触顶刷新到第一页
    _loadFreshData = () => {
        if (this.state.currentPage == 1 || this.state.totalRecord <= this.state.pageSize) {
            console.log("==========不刷新=====");
            return;
        }
        this.setState({
            currentPage: 1
        })
        let url= "/biz/job/user/tenant_staff";
        let loadRequest={
            "currentPage": 1,
            "pageSize": this.state.pageSize,
            "departmentId": this.state.departmentId,
            "searchKeyWord": this.state.searchKeyWord,
        }
        httpPost(url, loadRequest, this._loadFreshDataCallBack);
    }

    
    _loadFreshDataCallBack = (response) => {
        if (response.code == 200 && response.data && response.data.dataList) {
            var dataNew = response.data.dataList;
            // dataOld.unshift(dataNew);
            var dataAll = [...dataNew];
            this.setState({
                dataSource: dataAll,
                currentPage: response.data.currentPage + 1,
                totalPage: response.data.totalPage,
                totalRecord: response.data.totalRecord,
                refreshing: false
            })
        }
        else if (response.code == 401) {
            WToast.show({ data: response.message });
            this.props.navigation.navigate("LoginView");
        }
    
    }

    flatListFooterComponent = () => {
        return (
            <CustomListFooterComponent isloading={(this.state.currentPage - 1) < this.state.totalPage} />
        )
    }

    space(){
        return(<View style={{height: 1, backgroundColor: '#F0F0F0'}}/>)
    }
    emptyComponent() {
        return <EmptyListComponent/>
    }


    renderRow = (item, index) => {
        return (
            <View>
                <TouchableOpacity onPress={()=>{
                    var selStaffIdList = this.state.selStaffIdList;
                    if (item.departmentSelectedStaff && item.departmentSelectedStaff == "Y") {
                        item.departmentSelectedStaff = "N";
                        arrayRemoveItem(selStaffIdList, item.userId);
                    }
                    else {
                        item.departmentSelectedStaff = "Y";
                        selStaffIdList = selStaffIdList.concat(item.userId)
                    }
                    this.setState({
                        selStaffIdList:selStaffIdList,
                    })
                    WToast.show({data:'点击了' + item.userName});
                    console.log("======selStaffIdList:", selStaffIdList)
                }}>
                    <View key={item.userId} style={[styles.innerViewStyle,(item.departmentSelectedStaff && item.departmentSelectedStaff === 'Y') ? {backgroundColor:'rgba(255,0,0,0.4)',borderRadius:20,hight:80}:{} ]}>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>员工姓名：{item.userName}</Text>
                        </View>
                        <View style={styles.titleViewStyle}>
                            <Text style={styles.titleTextStyle}>联系电话：{item.userNbr ? item.userNbr : "无"}</Text>
                        </View>
                    </View>
                </TouchableOpacity>    
            </View>
        )
    }            
    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
                {/* <EvilIcons name='chevron-left' size={40} style={{color:'#FFFFFF'}}></EvilIcons> */}
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22}} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            <TouchableOpacity onPress={() => {
                let requestUrl= "/biz/hl/department/store/keeper/rel/add";
                let requestParams={
                    "departmentId":this.state.departmentId,
                    "selStaffIdList":this.state.selStaffIdList,
                    "oldSelStaffIdList":this.state.oldSelStaffIdList,
                };
                console.log("=======save==requestParams:", requestParams);
                httpPost(requestUrl, requestParams, (response)=>{
                    let toastOpts;
                    if (response && response.code === 200) {
                        if (this.props.route.params.refresh) {
                            this.props.route.params.refresh();
                        }
                        toastOpts = getSuccessToastOpts('保存完成');
                        WToast.show(toastOpts);
                        this.props.navigation.goBack()
                        // this.props.navigation.navigate("JobStaffMgrList", 
                        // {
                        //     // 传递回调函数
                        //     refresh: this.callBackFunction 
                        // })
                    }
                    else {
                        toastOpts = getFailToastOpts(response.message);
                        WToast.show({data:response.message})
                    }
                });
            }}>
                <Image  style={{width:28, height:28,marginRight:5}} source={require('../../assets/icon/iconfont/save.png')}></Image>
                {/* <Text style={CommonStyle.headRightText}>保存</Text> */}
            </TouchableOpacity>
        )
    }
    topBlockLayout=(event)=> {
        this.setState({
            topBlockLayoutHeight: event.nativeEvent.layout.height
        })

    }
    render(){
        return(
            <View>
                <CommonHeadScreen title='新增库管员'
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={[styles.innerViewStyle,{marginTop:0, index:1000}]} onLayout={this.topBlockLayout.bind(this)}>
                    <View style={styles.titleViewStyle}>
                        <Text style={[styles.titleTextStyle,{marginLeft:10, fontWeight:'bold', marginRight:100}]}>科室名称：{this.state.departmentName}</Text>
                    </View>
                    <View style={{marginTop:10}}>
                        <View style={styles.inputRowStyle}>
                            <View style={styles.leftLabView}>
                            <Image  style={{width:25, height:25}} source={require('../../assets/icon/iconfont/search.png')}></Image>
                            </View>
                            <TextInput 
                                style={[styles.searchInputText, {}]}
                                returnKeyType="search"
                                returnKeyLabel="搜索"
                                onSubmitEditing={e => {
                                    this.searchByKeyWord();
                                }}
                                placeholder={'姓名或电话'}
                                onChangeText={(text) => this.setState({searchKeyWord:text})}
                            >
                                {this.state.searchKeyWord}
                            </TextInput>
                            {/* <TouchableOpacity onPress={()=>{
                                this.loadJobStaffList();
                                }}>
                                <View style={[CommonStyle.itemBottomDeleteBtnViewStyle]}>
                                    <Text style={CommonStyle.itemBottomDeleteBtnTextStyle}>查询</Text>
                                </View>
                            </TouchableOpacity> */}
                        </View>
                        {/* <View style={{height:5, backgroundColor:'#FFFFFF'}}></View> */}
                    </View>
                </View>
                <View style={[CommonStyle.contentViewStyle, { height: ifIphoneXContentViewDynamicHeight(this.state.topBlockLayoutHeight) }]}>
                    <FlatList
                        data={this.state.dataSource}
                        renderItem={({ item, index }) => this.renderRow(item, index)}
                        ListEmptyComponent={this.emptyComponent}
                        // 自定义下拉刷新
                        refreshControl={
                            <RefreshControl
                                tintColor="#FF0000"
                                title="loading"
                                colors={['#FF0000', '#00FF00', '#0000FF']}
                                progressBackgroundColor="#FFFF00"
                                refreshing={this.state.refreshing}
                                onRefresh={() => {
                                    this._loadFreshData()
                                }}
                            />
                        }
                        // 底部加载
                        ListFooterComponent={() => this.flatListFooterComponent()}
                        onEndReached={() => this._loadNextData()}
                        onEndReachedThreshold={0.7}
                    />
                </View>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    inputRowStyle:{
        paddingLeft: 5,
        height: 40,
        flexDirection: 'row',
        borderWidth:1,
        borderColor:"#FFFFFF",
        backgroundColor:"#FFFFFF",
        borderRadius:5
    },

    leftLabView:{
        height:45,
        flexDirection:'row',
        alignItems:'center',
        paddingLeft:10,
        paddingBottom:5
    },
    leftLabNameTextStyle:{
        fontSize:18,
    },
    searchInputText:{
        width:screenWidth / 2,
        borderColor:'#000000',
        // borderBottomWidth:1,
        marginRight:5,
        color:'#A0A0A0',
        fontSize:16,
        marginLeft:10,
        paddingLeft:10,
        paddingRight:10,
        paddingBottom:0,
        paddingTop:0
    },
    innerViewStyle:{
        // marginTop:10,
        borderColor:"#F4F4F4",
        borderWidth:8,
        borderBottomWidth:5,
    },
    titleViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:10,
        marginRight:10,
        marginBottom:5,
        marginTop:5,
    },
    titleTextStyle:{
        fontSize:16
    },
    itemContentStyle:{
        flexDirection:'row',
        alignItems:'center'
    },
    itemContentImageStyle:{
        width:120,
        height:120
    },
    itemContentViewStyle:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginLeft:25
    },
    itemContentChildViewStyle:{
        flexDirection:'column'
    },
    itemContentChildTextStyle:{
        marginLeft:10,
        marginTop:15,
        fontSize:16
    },
});