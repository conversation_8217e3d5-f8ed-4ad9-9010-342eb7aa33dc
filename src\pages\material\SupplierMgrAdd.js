import React,{Component} from 'react';
import {
    View,Text,StyleSheet,Dimensions,TouchableOpacity,Alert,
    FlatList,RefreshControl,TextInput,ScrollView,Image,KeyboardAvoidingView
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {WToast} from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
import { ifIphoneXContentViewHeight } from '../../utils/ScreenUtil';
var CommonStyle = require('../../assets/css/CommonStyle');

var screenWidth = Dimensions.get('window').width;
var screenHeight = Dimensions.get('window').height;
const leftLabWidth = 130;
export default class SupplierMgrAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            supplierId: "",
            supplierName: "",
            supplierAbbreviation: "",
            supplierContact: "",
            supplierTel: "",
            address: "",
            taxpayerIdNumber: "",
            bankAccountRealName: "",
            openingAccountBank: "",
            bankAccount: "",
            supplierSort:0,
            operate:"",
        }
    }


    UNSAFE_componentWillMount(){
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { supplierId } = route.params;
            if (supplierId) {
                console.log("=============supplierId" + supplierId + "");
                this.setState({
                    supplierId:supplierId,
                    operate:"编辑"
                })
                let loadTypeUrl= "/biz/supplier/get";
                let loadRequest={'supplierId':supplierId};
                httpPost(loadTypeUrl, loadRequest, this.loadSupplierCallBack);
            }
            else {
                this.setState({
                    operate:"新增"
                })
            }
        }
    }
    loadSupplierCallBack=(response)=>{
        if (response.code == 200 && response.data) {
            this.setState({
                supplierName:response.data.supplierName,
                supplierAbbreviation:response.data.supplierAbbreviation,
                supplierContact:response.data.supplierContact,
                supplierTel:response.data.supplierTel,
                supplierSort:response.data.supplierSort,
                address:response.data.address,
                taxpayerIdNumber:response.data.taxpayerIdNumber,
                bankAccountRealName:response.data.bankAccountRealName,
                openingAccountBank:response.data.openingAccountBank,
                bankAccount:response.data.bankAccount,
            })
        }
    }
    // 头部左侧
    renderLeftItem() {
        return (
            // <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{marginBottom:1.5}]}>
            //     {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
            //     <Image  style={{width:22, height:22}} source={require('../../assets/icon/iconfont/back.png')}></Image>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={{ marginBottom: 1.5, flexDirection: 'row', alignItems: 'center'}}>
                    <Image style={{ width: 22, height: 22, marginVertical: 2, tintColor: '#3C6CDE'}} source={require('../../assets/icon/iconfont/back.png')} />
                    <Text style={{ color: '#3C6CDE', marginLeft: 3, fontWeight:'bold'}}>返回</Text>
                </TouchableOpacity>
            </View>
        )
    }
    // 头部右侧
    renderRightItem() {
        return (
            // <TouchableOpacity onPress={() => {
            //     this.props.navigation.navigate("SupplierMgrList")
            // }}>
            //     <Text style={CommonStyle.headRightText}>供应商</Text>
            // </TouchableOpacity>
            <View style={{ flexDirection: 'row', alignItems: 'center', width:70}}>
                <TouchableOpacity onPress={() => {

                }}>
                {/* <TouchableOpacity onPress={() => { this.props.navigation.navigate("CustomerList") }}> */}
                    <Text style={{color:'#FFFFFF'}}>供应商</Text>
                {/* <Text style={CommonStyle.headRightText}>客户管理</Text> */}
                </TouchableOpacity>
            </View>
        )
    }
    emptyComponent() {
        return <EmptyRowViewComponent/>
    }
    saveSupplier =()=> {
        console.log("=======saveSupplier");
        let toastOpts;
        if (!this.state.supplierName) {
            toastOpts = getFailToastOpts("请填写供应商名称");
            WToast.show(toastOpts)
            return;
        }
        let url= "/biz/supplier/add";
        if (this.state.supplierId) {
            console.log("=========Edit===supplierId", this.state.supplierId)
            url= "/biz/supplier/modify";
        }
        let requestParams={
            supplierId:this.state.supplierId,
            supplierName: this.state.supplierName,
            supplierAbbreviation: this.state.supplierAbbreviation,
            supplierContact:this.state.supplierContact,
            supplierTel:this.state.supplierTel,
            supplierSort:this.state.supplierSort,
            address:this.state.address,
            taxpayerIdNumber:this.state.taxpayerIdNumber,
            bankAccountRealName:this.state.bankAccountRealName,
            openingAccountBank:this.state.openingAccountBank,
            bankAccount:this.state.bankAccount,
        };
        httpPost(url, requestParams, this.saveSupplierCallBack);
    }

    // 保存回调函数
    saveSupplierCallBack=(response)=>{
        let toastOpts;
        switch (response.code) {
            case 200:
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh();
                }
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts);
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({data:response.message})
          }
    }

    render(){
        return(
            <KeyboardAvoidingView style={[CommonStyle.formContentViewStyle]} behavior="padding">
                <CommonHeadScreen title={this.state.operate + '供应商'}
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <View style={{ borderBottomWidth: 1, borderBottomColor: '#F1F1F1', width: '100%', marginTop: -2 }} />
                <ScrollView style={[CommonStyle.formContentViewStyle]}>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>供应商名称</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ supplierName: text })}
                        >
                            {this.state.supplierName}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>供应商简称</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ supplierAbbreviation: text })}
                        >
                            {this.state.supplierAbbreviation}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                     <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>联系人</Text>
                        </View>
                        <TextInput
                            //keyboardType='text'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ supplierContact: text })}
                        >
                            {this.state.supplierContact}
                        </TextInput>
                    </View> 
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>联系电话</Text>
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ supplierTel: text })}
                        >
                            {this.state.supplierTel}
                        </TextInput>
                    </View> 
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>地址</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}></Text> */}
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({address:text})}
                        >
                            {this.state.address}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>纳税人识别号</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({taxpayerIdNumber:text})}
                        >
                            {this.state.taxpayerIdNumber}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>开户名</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({bankAccountRealName:text})}
                        >
                            {this.state.bankAccountRealName}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>开户银行</Text>
                        </View>
                        <TextInput 
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({openingAccountBank:text})}
                        >
                            {this.state.openingAccountBank}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>

                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabWhiteTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>账号</Text>
                        </View>
                        <TextInput 
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({bankAccount:text})}
                        >
                            {this.state.bankAccount}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>
                    
                    <View style={styles.inputRowStyle}>
                        <View style={styles.leftLabView}>
                            <Text style={styles.leftLabRedTextStyle}>*</Text>
                            <Text style={styles.leftLabNameTextStyle}>排序(升序)</Text>
                            {/* <Text style={styles.leftLabRedTextStyle}>*</Text> */}
                        </View>
                        <TextInput
                            keyboardType='numeric'
                            style={styles.inputRightText}
                            placeholder={'请输入'}
                            onChangeText={(text) => this.setState({ supplierSort: text })}
                        >
                            {this.state.supplierSort}
                        </TextInput>
                    </View>
                    <View style={CommonStyle.lineBorderBottomStyle}></View>
                    <View style={{height:ifIphoneXContentViewHeight()-450-175, backgroundColor:'#F2F5FC'}}>
                    </View>
                    <View style={[CommonStyle.blockAddCancelSaveStyle,{ marginTop:0 }]}>
                        <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                            <View style={CommonStyle.btnAddCancelBtnView} >
                                {/* <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/revoke.png')}></Image> */}
                                <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity onPress={this.saveSupplier.bind(this)}>
                            <View style={CommonStyle.btnAddSaveBtnView}>
                                {/* <Image style={{width:25, height:25,marginRight:15}} source={require('../../assets/icon/iconfont/save.png')}></Image> */}
                                <Text style={CommonStyle.btnRowRightSaveBtnText}>确定</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        )
    }
}
const styles = StyleSheet.create({

    // contentViewStyle:{
    //     height:screenHeight - 140,
    //     backgroundColor:'#FFFFFF'
    // },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 4,
        marginBottom:4,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    leftLabWhiteTextStyle:{
        color:'#FFFFFF',
        marginLeft:5,
        marginRight:5,
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        // borderRadius: 5,
        // borderColor: '#F1F1F1',
        // borderWidth: 1,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    }

});