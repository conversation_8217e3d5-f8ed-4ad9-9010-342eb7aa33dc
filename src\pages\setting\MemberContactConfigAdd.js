import React, { Component } from 'react';
import {
    View, Text, StyleSheet, Dimensions, TouchableOpacity, Alert,
    FlatList, RefreshControl, Image, ScrollView, TextInput
} from 'react-native';
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { WToast } from 'react-native-smart-tip';
import CommonHeadScreen from '../../component/CommonHeadScreen';
import EmptyListComponent from '../../component/EmptyListComponent';
import CustomListFooterComponent from '../../component/CustomListFooterComponent';
var CommonStyle = require('../../assets/css/CommonStyle');
import BottomScrollSelect from '../../component/BottomScrollSelect';
import { uploadImageLibrary } from '../../utils/UploadImageUtils';

var screenHeight = Dimensions.get('window').height;
var screenWidth = Dimensions.get('window').width;
const leftLabWidth = 130;
export default class MemberContactConfigAdd extends Component {
    constructor(props) {
        super(props);
        this.state = {
            operate: "",
            paramCode: "",
            paramCodeName: "",
            selectedParamCode: [],
            paramCodeDataSource: [],
            configLink: "",
            advertisingType: "",
            advertisingState: "",
            paramName: "",
            paramValue: "",
            advertisingId: 0,
            valueTypeList: [
                {
                    typeId: 0,
                    typeCode: 'link',
                    typeName: "自定义链接"
                },
                {
                    typeId: 1,
                    typeCode: 'image-text',
                    typeName: "自定义图文"
                },
            ],
            valueType: "link",
            imageConfigList: ["PAY_QR_CODE", "ALI_PAY_QR_CODE", "WECHAT_PAY_QR_CODE", "APPLY_CHARGE_PERSON_WECHAT_QR_CODE", "WECHAT_SHARE_PAGE_BOTTOM_IMAGE"
                , "RESUME_SHARE_LOGO", "RESUME_SHARE_WEBSITE_BANNER", "ENTERPRISE_SHARE_LOGO", "ENTERPRISE_SHARE_WEBSITE_BANNER","MEMBER_SHARE_WEBSITE_BANNER","MEMBER_SHARE_WEBSITE_NOTIFICATION_PICTURE","MEMBER_SHARE_CARD_LOGO"],
            bannerConfigList:["RESUME_SHARE_WEBSITE_BANNER", "ENTERPRISE_SHARE_WEBSITE_BANNER", "MEMBER_SHARE_WEBSITE_BANNER"]

        }
    }


    UNSAFE_componentWillMount() {
        console.log('componentWillMount');
        const { route, navigation } = this.props;
        if (route && route.params) {
            const { paramName, advertisingId, advertisingType } = route.params;
            if (paramName) {
                this.setState({
                    paramName: paramName
                })
            }
            if (advertisingType) {
                this.setState({
                    advertisingType: advertisingType
                })
            }
            if (advertisingId) {
                console.log("=============advertisingId" + advertisingId);
                this.setState({
                    advertisingId: advertisingId,
                    operate: "编辑"
                })
                let url = "/biz/portal/advertising/get";
                let request = {
                    "advertisingId": advertisingId,
                };
                httpPost(url, request, this.loadEditContactConfig);
            }
            else {
                this.setState({
                    operate: "新增"
                })
            }
        }
    }

    loadEditContactConfig = (response) => {
        if (response.code == 200 && response.data) {
            this.setState({
                configLink: response.data.advertisingLink,
                advertisingType: response.data.advertisingType,
            })
        }
    }

    saveContactConfig = () => {
        console.log("=======saveContactConfig");
        let toastOpts;
        if (!this.state.configLink) {
            toastOpts = getFailToastOpts("请输入自定义链接");
            WToast.show(toastOpts)
            return;
        }
        let url = "/biz/portal/advertising/add";
        if (this.state.operate === '编辑') {
            url = "/biz/portal/advertising/modify";
        }
        let requestParams = {
            advertisingId: this.state.advertisingId,
            advertisingTitle: "无",
            advertisingLink: this.state.configLink,
            advertisingContent: "<p></p>",
            advertisingType: this.state.advertisingType
        };
        console.log("=======requestParams", requestParams);
        httpPost(url, requestParams, this.saveContactConfigCallBack);
    }

    saveContactConfigCallBack = (response) => {
        let toastOpts;
        switch (response.code) {
            case 200:
                toastOpts = getSuccessToastOpts('保存完成');
                WToast.show(toastOpts)
                // if (this.state.operate === '新增' || this.state.advertisingState == "0AA") {
                //     this.savePortalTenantParam()
                // }
                if (this.props.route.params.refresh) {
                    this.props.route.params.refresh()
                }
                this.props.navigation.goBack()
                break;
            default:
                toastOpts = getFailToastOpts(response.message);
                WToast.show({ data: response.message })
        }
    }


    // 头部左侧
    renderLeftItem() {
        return (
            <TouchableOpacity onPress={() => { this.props.navigation.goBack() }} style={[{ marginBottom: 1.5 }]}>
                {/* <Text style={CommonStyle.headLeftText}>返回</Text> */}
                <Image style={{ width: 22, height: 22 }} source={require('../../assets/icon/iconfont/backnew.png')}></Image>
            </TouchableOpacity>
        )
    }

    // 头部右侧
    renderRightItem() {
        return (
            <View></View>
        )
    }

    // 配置类型单项渲染
    renderValueTypeRow = (item) => {
        return (
            <TouchableOpacity onPress={() => {
                this.setState({
                    valueType: item.typeCode
                })
            }}>
                <View key={item.typeId} style={item.typeCode === this.state.valueType ? CommonStyle.selectedBlockItemViewStyle : CommonStyle.blockItemViewStyle}>
                    <Text style={item.typeCode === this.state.valueType ? CommonStyle.selectedBlockItemTextStyle16 : CommonStyle.blockItemTextStyle16}>
                        {item.typeName}
                    </Text>
                </View>
            </TouchableOpacity>
        )
    }

    render() {
        return (
            <View>
                <CommonHeadScreen title={
                    // this.state.operate + 
                    this.state.paramName
                }
                    leftItem={() => this.renderLeftItem()}
                    rightItem={() => this.renderRightItem()}
                />
                <ScrollView style={CommonStyle.contentViewStyle}>
                    <View style={styles.leftLabView}>
                        <Text style={styles.leftLabNameTextStyle}>配置类型</Text>
                    </View>
                    <View style={{ width: screenWidth, flexWrap: 'wrap', flexDirection: 'row' }}>
                        {
                            (this.state.valueTypeList && this.state.valueTypeList.length > 0)
                                ?
                                this.state.valueTypeList.map((item, index) => {
                                    return this.renderValueTypeRow(item)
                                })
                                : <EmptyRowViewComponent />
                        }
                    </View>
                    <View style={styles.inputLineViewStyle}/>

                    {
                        this.state.valueType === 'link' ?
                            <View>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>配置内容</Text>
                                </View>
                                <View style={[styles.inputRowStyle, { height: 200 }]}>
                                    <TextInput
                                        multiline={true}
                                        textAlignVertical="top"
                                        style={[CommonStyle.inputRowText, { height: 200 }]}
                                        placeholder={'请输入自定义链接'}
                                        onChangeText={(text) => this.setState({ configLink: text })}
                                    >
                                        {this.state.configLink}
                                    </TextInput>
                                </View>
                            </View>
                            :
                            <View />
                    }

                    {
                        this.state.valueType === 'image-text' ?
                            <View>
                                <View style={styles.leftLabView}>
                                    <Text style={styles.leftLabNameTextStyle}>配置内容</Text>
                                </View>
                                <View style={{marginTop: 10}}>
                                    <Text style={{ marginLeft: 10, fontSize: 16, color: "red" }} >
                                        请前往电脑端设置图文
                                    </Text>
                                </View>
                            </View>
                            :
                            <View />
                    }

                    {
                        this.state.valueType === 'link' ?
                            <View style={[CommonStyle.btnRowStyle, {width: screenWidth, marginLeft: 0, marginTop: 6}]}>
                                <TouchableOpacity onPress={() => { this.props.navigation.goBack() }}>
                                    <View style={[CommonStyle.btnRowLeftCancelBtnView, { width: (screenWidth - 80)/2, marginLeft: 20 }]} >
                                        <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/revoke.png')}></Image>
                                        <Text style={CommonStyle.btnRowLeftCancelBtnText}>取消</Text>
                                    </View>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={this.saveContactConfig.bind(this)}>
                                    <View style={[CommonStyle.btnRowRightSaveBtnView, { width: (screenWidth - 80)/2, marginRight: 20 }]}>
                                        <Image style={{ width: 25, height: 25, marginRight: 15 }} source={require('../../assets/icon/iconfont/save.png')}></Image>
                                        <Text style={CommonStyle.btnRowRightSaveBtnText}>保存</Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            :
                            <View></View>
                    }
                </ScrollView>
            </View>
        )
    }
}
const styles = StyleSheet.create({
    leftLabNameTextStyle: {
        fontSize: 18,
    },
    itemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: '#FFFFFF'
    },
    selectedItemViewStyle: {
        margin: 10,
        padding: 15,
        borderRadius: 2,
        backgroundColor: "#CB4139"
    },
    itemTextStyle: {
        color: '#000000'
    },
    selectedItemTextStyle: {
        color: '#FFFFFF'
    },
    inputRowStyle: {
        height: 45,
        flexDirection: 'row',
        marginTop: 10,
        // flex: 1,
        // justifyContent: 'space-between',
        // alignContent:'center'
        // backgroundColor:'#000FFF',
        // width:screenWidth,
        // alignContent:'space-between',
        // justifyContent:'center'
    },

    rowLabView: {
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        marginRight: 30
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabView: {
        width: leftLabWidth,
        height: 45,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 10,
        // alignContent:'flex-start',
        // justifyContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabNameTextStyle: {
        fontSize: 18,
        // color:'red',
        // borderColor:'#000',
        // borderWidth:1,
        // justifyContent:'center',
        // alignContent:'center',
        // backgroundColor:'yellow',
    },
    leftLabRedTextStyle: {
        color: 'red',
        marginLeft: 5,
        marginRight: 5
    },
    inputRightText: {
        width: screenWidth - (leftLabWidth + 5),
        borderRadius: 5,
        marginRight: 5,
        color: '#A0A0A0',
        fontSize: 15,
        paddingLeft: 10,
        paddingRight: 10
    },
    inputLineViewStyle: {
        height:1,
        marginLeft: 13,
        marginRight: 13,
        borderBottomWidth: 0.5,
        borderColor:'#E8E9EC'
    },

});